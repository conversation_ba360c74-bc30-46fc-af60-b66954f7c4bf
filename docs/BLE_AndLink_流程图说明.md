# BLE和AndLink系统流程图说明

## 1. 系统架构流程图

### 1.1 整体架构图
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   移动端APP     │    │   机器人系统    │    │   移动云平台    │
│                 │    │                 │    │                 │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │ 蓝牙连接    │ │◄──►│ │ BLE模块     │ │    │ │ AndLink平台 │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │ WiFi配置    │ │    │ │ AndLink模块 │ │◄──►│ │ 设备管理    │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │ 设备控制    │ │    │ │ 网络模块    │ │    │ │ 用户绑定    │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
│                 │    │        │        │    │ ┌─────────────┐ │
└─────────────────┘    │ ┌─────────────┐ │    │ │ 远程控制    │ │
                       │ │ ROS2通信    │ │    │ └─────────────┘ │
                       │ └─────────────┘ │    │                 │
                       └─────────────────┘    └─────────────────┘
```

### 1.2 模块间关系
- **移动端APP**: 用户交互界面，负责设备发现、配网和控制
- **BLE模块**: 蓝牙通信处理，实现设备发现和数据传输
- **AndLink模块**: 云平台接入，处理设备注册和远程控制
- **网络模块**: WiFi连接管理，提供网络服务
- **ROS2通信**: 模块间消息传递和服务调用

## 2. BLE配网流程图

### 2.1 详细配网流程
```
APP端                    BLE模块                 AndLink模块              网络模块
  │                        │                        │                      │
  │ 1.搜索设备              │                        │                      │
  ├────────────────────────►│                        │                      │
  │                        │ 2.开启广播              │                      │
  │                        ├────────────────────────┤                      │
  │ 3.发现设备              │                        │                      │
  │◄───────────────────────┤                        │                      │
  │ 4.连接设备              │                        │                      │
  ├────────────────────────►│                        │                      │
  │                        │ 5.建立GATT连接          │                      │
  │                        ├────────────────────────┤                      │
  │ 6.发送WiFi信息          │                        │                      │
  ├────────────────────────►│                        │                      │
  │                        │ 7.解析WiFi配置          │                      │
  │                        ├────────────────────────┤                      │
  │                        │ 8.转发WiFi配置          │                      │
  │                        ├────────────────────────►│                      │
  │                        │                        │ 9.调用网络服务        │
  │                        │                        ├─────────────────────►│
  │                        │                        │                      │ 10.连接WiFi
  │                        │                        │                      ├──────────┐
  │                        │                        │                      │          │
  │                        │                        │ 11.返回连接结果       │◄─────────┘
  │                        │                        │◄─────────────────────┤
  │                        │ 12.通知连接状态         │                      │
  │                        │◄───────────────────────┤                      │
  │ 13.返回配网结果         │                        │                      │
  │◄───────────────────────┤                        │                      │
  │                        │                        │ 14.注册到云平台       │
  │                        │                        ├─────────────────────►│ 云平台
  │                        │                        │ 15.获取设备Token      │
  │                        │                        │◄─────────────────────┤
  │ 16.发送绑定信息         │                        │                      │
  ├────────────────────────►│                        │                      │
  │                        │ 17.转发绑定请求         │                      │
  │                        ├────────────────────────►│                      │
  │                        │                        │ 18.执行用户绑定       │
  │                        │                        ├─────────────────────►│ 云平台
  │                        │                        │ 19.返回绑定结果       │
  │                        │                        │◄─────────────────────┤
  │                        │ 20.通知绑定状态         │                      │
  │                        │◄───────────────────────┤                      │
  │ 21.返回绑定结果         │                        │                      │
  │◄───────────────────────┤                        │                      │
  │                        │ 22.停止BLE广播          │                      │
  │                        ├────────────────────────┤                      │
```

### 2.2 关键步骤说明

#### 2.2.1 设备发现阶段(步骤1-5)
- APP扫描周围的BLE设备
- 设备开启BLE广播，包含设备标识信息
- APP根据设备名称过滤目标设备
- 建立BLE GATT连接

#### 2.2.2 WiFi配网阶段(步骤6-13)
- APP通过BLE发送WiFi SSID和密码
- BLE模块解析配置数据
- 调用ROS2网络服务连接WiFi
- 返回连接结果给APP

#### 2.2.3 云平台注册阶段(步骤14-15)
- WiFi连接成功后自动注册到AndLink平台
- 获取设备在云平台的唯一Token
- 建立与云平台的长连接

#### 2.2.4 用户绑定阶段(步骤16-22)
- APP发送用户绑定信息
- 执行用户与设备的绑定操作
- 绑定成功后停止BLE广播

## 3. ROS2消息通信流程图

### 3.1 Topic通信流程
```
BLE节点                  AndLink节点              网络节点               其他节点
   │                        │                      │                     │
   │ ble_byte_stream        │                      │                     │
   ├───────────────────────►│                      │                     │
   │                        │ 处理BLE数据           │                     │
   │                        ├─────────────────────┤                     │
   │                        │                      │                     │
   │ andlink_cmd            │                      │                     │
   ├───────────────────────►│                      │                     │
   │                        │ 处理AndLink命令       │                     │
   │                        ├─────────────────────┤                     │
   │                        │                      │                     │
   │                        │ ble_cmd              │                     │
   │◄───────────────────────┤                      │                     │
   │ 处理BLE命令             │                      │                     │
   ├─────────────────────┤  │                      │                     │
   │                        │                      │                     │
   │ andlink_network        │                      │                     │
   ├─────────────────────────────────────────────►│                     │
   │                        │                      │ 处理网络状态         │
   │                        │                      ├─────────────────────┤
   │                        │                      │                     │
   │ sigc_event_topic_APP   │                      │                     │
   ├─────────────────────────────────────────────────────────────────────►│
   │                        │                      │                     │ 处理APP事件
   │                        │                      │                     ├──────────────┤
```

### 3.2 Service调用流程
```
AndLink节点              网络节点                 语音节点
    │                      │                       │
    │ NetCtrl Service      │                       │
    ├─────────────────────►│                       │
    │                      │ 执行网络操作           │
    │                      ├──────────────────────┤
    │                      │                       │
    │ 返回操作结果          │                       │
    │◄─────────────────────┤                       │
    │                      │                       │
    │                      │ AssistantSpeechText   │
    │                      ├──────────────────────►│
    │                      │                       │ 执行语音播报
    │                      │                       ├─────────────────┤
    │                      │ 返回播报结果           │
    │                      │◄──────────────────────┤
```

## 4. 异常处理流程图

### 4.1 异常检测流程
```
系统监控                 异常管理器               恢复处理器
    │                      │                       │
    │ 1.定期健康检查        │                       │
    ├─────────────────────►│                       │
    │                      │ 2.分析系统状态         │
    │                      ├──────────────────────┤
    │                      │                       │
    │ 3.发现异常            │                       │
    ├─────────────────────►│                       │
    │                      │ 4.记录异常信息         │
    │                      ├──────────────────────┤
    │                      │                       │
    │                      │ 5.触发恢复策略         │
    │                      ├──────────────────────►│
    │                      │                       │ 6.执行恢复操作
    │                      │                       ├─────────────────┤
    │                      │ 7.返回恢复结果         │
    │                      │◄──────────────────────┤
    │                      │                       │
    │ 8.验证恢复效果        │                       │
    │◄─────────────────────┤                       │
    │                      │                       │
    │ 9.更新系统状态        │                       │
    ├─────────────────────►│                       │
```

### 4.2 具体异常处理流程

#### 4.2.1 BLE连接异常
```
检测到BLE异常 → 记录异常信息 → 重启蓝牙服务 → 重新初始化BLE管理器 → 验证连接状态
```

#### 4.2.2 WiFi连接异常
```
检测到WiFi异常 → 记录异常信息 → 重新连接WiFi → 检查网络状态 → 更新连接状态
```

#### 4.2.3 AndLink连接异常
```
检测到AndLink异常 → 记录异常信息 → 重新注册设备 → 恢复云平台连接 → 验证注册状态
```

## 5. 状态机流程图

### 5.1 设备状态机
```
[初始化] → [BLE广播] → [等待连接] → [已连接] → [配网中] → [已配网] → [云平台注册] → [用户绑定] → [正常运行]
    │           │           │           │          │          │            │            │            │
    │           │           │           │          │          │            │            │            │
    └───────────┴───────────┴───────────┴──────────┴──────────┴────────────┴────────────┴────────────┘
                                                    │                                                  │
                                                    │ 异常处理                                        │
                                                    ▼                                                  │
                                              [异常状态] ──────────────────────────────────────────────┘
                                                    │
                                                    │ 恢复成功
                                                    ▼
                                              [恢复中] ────────────────────────────────────────────────┘
```

### 5.2 状态转换条件
- **初始化 → BLE广播**: 系统启动完成
- **BLE广播 → 等待连接**: 广播开启成功
- **等待连接 → 已连接**: APP连接成功
- **已连接 → 配网中**: 收到WiFi配置
- **配网中 → 已配网**: WiFi连接成功
- **已配网 → 云平台注册**: 开始注册流程
- **云平台注册 → 用户绑定**: 注册成功
- **用户绑定 → 正常运行**: 绑定完成
- **任意状态 → 异常状态**: 检测到异常
- **异常状态 → 恢复中**: 开始恢复
- **恢复中 → 正常运行**: 恢复成功

## 6. 数据流图

### 6.1 配网数据流
```
APP输入 → BLE传输 → 数据解析 → WiFi连接 → 状态反馈 → APP显示
   │         │         │         │         │         │
   │         │         │         │         │         │
WiFi信息   蓝牙数据包  配置结构体  网络调用  连接结果  用户界面
```

### 6.2 控制数据流
```
云平台命令 → AndLink接收 → ROS2消息 → 设备执行 → 状态上报 → 云平台更新
     │           │           │         │         │         │
     │           │           │         │         │         │
  远程指令    SDK处理     Topic发布   功能调用   结果收集   状态同步
```

---

*文档版本: v1.0*  
*最后更新: 2025-01-08*
