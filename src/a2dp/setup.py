from setuptools import setup

package_name = 'a2dp'

setup(
    name=package_name,
    version='0.0.0',
    packages=[package_name],
    data_files=[ 
        ('share/ament_index/resource_index/packages',
            ['resource/' + package_name]),
        ('share/' + package_name, ['package.xml']),
    ],
    install_requires=['setuptools'],
    zip_safe=True,
    maintainer='yuwei',
    maintainer_email='<EMAIL>',
    description='A ROS2 package for A2DP audio streaming',
    license='TODO: License declaration',
    tests_require=['pytest'],
    entry_points={
        'console_scripts': [
            'a2dp_node = a2dp.a2dp_node:main',  # 添加入口点，执行 a2dp.py 中的 main() 函数
        ],
    },
    # 添加 ROS2 所需的依赖项
    extras_require={
        'dependencies': [
            'rclpy',  # ROS2 Python客户端库
            'homi_speech_interface',  # 你的自定义接口包
            'pexpect',  # 用于蓝牙操作
            'pyaudio',  # 用于音频捕获
        ]
    }
)
