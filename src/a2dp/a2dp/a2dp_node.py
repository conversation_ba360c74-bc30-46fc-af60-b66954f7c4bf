#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pexpect
import subprocess
import time
import sys
import os
import struct
import numpy as np
import rclpy
from homi_speech_interface.msg import PCMStream, Wakeup
import pyaudio
import dbus
import wave
import re
from .read import DeviceConfig

CONFIG_PATH = '/etc/cmcc_robot/andlinkSdk.conf'
FORMAT = pyaudio.paInt16
CHANNELS = 1
RATE = 16000
CHUNK = 5000  # 你可以改成自己想要的，比如 5000

def send_wakeup_event(publisher):
    msg = Wakeup()
    msg.ivw_word = "hello"
    msg.angle = 0
    msg.extra_info = "hello"
    publisher.publish(msg)
    print(f"[*] 已发布唤醒事件: ivw_word={msg.ivw_word}, angle={msg.angle}, extra_info={msg.extra_info}")

def start_pulseaudio(first_run=False):
    """
    first_run 表示是否是本脚本第一次调用。
    若 first_run=True，则无条件先执行 kill 再 start。
    若 first_run=False，则先检测 pulseaudio --start 是否已在运行，如果没有才进行 kill + start。
    """
    if first_run:
        print("[*] 第一次执行，强制重启 pulseaudio...")
        subprocess.run(["pulseaudio", "-k"], stderr=subprocess.DEVNULL)
        proc = subprocess.run(["pulseaudio", "--start"], capture_output=True)
        if proc.returncode != 0:
            print("[!] Pulseaudio 启动失败:", proc.stderr.decode("utf-8"))
        else:
            print("[*] Pulseaudio 已启动。")
        return

    # 后续循环时，先检查是否已经有 pulseaudio --start 存在
    check_proc = subprocess.run(["pgrep", "-fa", "pulseaudio --start"], capture_output=True, text=True)
    if check_proc.returncode == 0 and check_proc.stdout.strip():
        # 找到匹配项，说明 pulseaudio --start 已运行
        print("[*] Pulseaudio 已运行，不再重启。")
        return

    # 未找到匹配项，需要重新启动
    print("[*] 未检测到 pulseaudio --start 进程，重启 pulseaudio...")
    subprocess.run(["pulseaudio", "-k"], stderr=subprocess.DEVNULL)
    proc = subprocess.run(["pulseaudio", "--start"], capture_output=True)
    if proc.returncode != 0:
        print("[!] Pulseaudio 启动失败:", proc.stderr.decode("utf-8"))
    else:
        print("[*] Pulseaudio 已启动。")


def cleanup_bluetooth():
    print("[*] 执行蓝牙清理操作...")
    subprocess.run(["pkill", "-f", "blueman-applet"], stderr=subprocess.DEVNULL)
    '''
    try:
        child = pexpect.spawn("bluetoothctl")
        child.expect("#")
        child.sendline("agent off")
        child.expect("#")
        child.sendline("paired-devices")
        child.expect("#")
        output = child.before.decode("utf-8")
        lines = output.strip().splitlines()
        for line in lines:
            if "Device " in line:
                parts = line.split()
                mac = parts[1]
                print(f"[*] remove 已配对设备: {mac}")
                # child.sendline(f"remove {mac}")
                # child.expect("#")
        child.close()
    except Exception as e:
        print(f"[!] 清理时出现异常：{e}")
    '''
    print("[*] 尝试重启蓝牙服务...")
    #subprocess.run(["sudo", "systemctl", "restart", "bluetooth"])
    #time.sleep(4)
    print("[*] 蓝牙服务已重启，清理完成。")

def bluetooth_pair():
    child = pexpect.spawn('bluetoothctl', encoding='utf-8')
    child.sendline('power on')
    child.sendline('discoverable off')
    print("[!] discoverable off 执行成功")
    time.sleep(1)
    child.sendline('discoverable on')
    print("[!] discoverable on 执行成功")
    child.sendline('pairable on')

    while True:
        try:
            index = child.expect([
                r'Confirm passkey',
                r'Authorize service.*\(yes/no\):',
                r'Pairing confirmed',
                r'Failed to pair',
                pexpect.EOF,
                pexpect.TIMEOUT
            ], timeout=30)

            if index == 0:
                child.sendline('yes')
                print("[*] Passkey confirmed.")
            elif index == 1:
                text = child.after.strip()
                if "0000110d-0000-1000-8000-00805f9b34fb" in text:
                    child.sendline('yes')
                    print("[*] Authorized A2DP (0x110D).")
                    break
                else:
                    child.sendline('no')
                    print(f"[!] 非A2DP服务: {text}")
            elif index == 2:
                print("[*] Pairing confirmed.")
                continue
            elif index == 3:
                print("[!] Pairing failed.")
                break
            elif index == 4:
                print("[!] bluetoothctl EOF.")
                break
            elif index == 5:
                print("[!] 等待超时，尝试重启经典蓝牙广播...")
                child.sendline('discoverable off')
                print("[!] discoverable off 执行成功")
                time.sleep(1)
                child.sendline('discoverable on')
                print("[!] discoverable on 执行成功")
                continue
        except pexpect.exceptions.TIMEOUT:
            print("[!] pexpect 超时，继续等待。")
            continue

    child.sendline('exit')
    child.close()

def find_connected_device_path():
    """
    先找到已连接 (Connected=true) 的蓝牙设备(org.bluez.Device1)，
    然后检查是否存在对应的 '/fd\d+' 子路径，如果有，则返回该子路径。
    若找不到，返回 None。
    """
    bus = dbus.SystemBus()
    manager = dbus.Interface(
        bus.get_object('org.bluez', '/'),
        'org.freedesktop.DBus.ObjectManager'
    )
    objects = manager.GetManagedObjects()

    # 1) 收集所有“已连接的蓝牙设备”的基础路径，比如 /org/bluez/hci0/dev_FC_2A_46_6F_CA_1C
    connected_base_paths = []
    for path, interfaces in objects.items():
        if 'org.bluez.Device1' in interfaces:
            dev_props = interfaces['org.bluez.Device1']
            connected = dev_props.get('Connected', False)
            if connected:
                print(f"[调试] 找到已连接的基础设备路径: {path}")
                connected_base_paths.append(path)

    # 2) 再从所有对象里搜索是否有“基础路径 + /fdX” 的子路径
    for base_path in connected_base_paths:
        for path, interfaces in objects.items():
            # 判断 path 是否是 base_path + '/fdX'
            # 例如 base_path = /org/bluez/hci0/dev_FC_2A_46_6F_CA_1C
            #       path      = /org/bluez/hci0/dev_FC_2A_46_6F_CA_1C/fd0
            print(f"[调试] 所有的path路径: {path}")
            if path.startswith(base_path + "/fd") and re.search(r'/fd\d+$', path):
                print(f"[调试] 找到带 fd 的子路径: {path}")
                return path

    print("[调试] 未发现任何 connected + /fd\d+ 的路径")
    return None

def listen_for_bluetooth_events(device_path):
    """
    使用给定的 device_path 获取该设备的 MediaTransport1.State。
    如果 device_path 不存在或读取出错，返回 None。
    """
    if not device_path:
        return None

    bus = dbus.SystemBus()
    try:
        bluez_device = bus.get_object('org.bluez', device_path)
        bluez_iface = dbus.Interface(bluez_device, 'org.freedesktop.DBus.Properties')
        state_property = bluez_iface.Get('org.bluez.MediaTransport1', 'State')
        return state_property  # 'active' or 'idle' etc.
    except dbus.exceptions.DBusException as e:
        print(f"[!] 无法读取 {device_path} 的状态: {e}")
        return None

def capture_audio_on_button_press(
    pcm_publisher,
    wakeup_publisher,
    save_to_file=False,
    wav_filename="/tmp/output.wav",
    frames_per_buffer=CHUNK
):
    p = pyaudio.PyAudio()
    stream = None

    wf = None
    if save_to_file:
        wf = wave.open(wav_filename, 'wb')
        wf.setnchannels(CHANNELS)
        wf.setsampwidth(p.get_sample_size(FORMAT))
        wf.setframerate(RATE)

    # 调用部分添加重试逻辑
    max_attempts = 10  # 总尝试次数 = 1（立即执行） + 4（后续重试）
    device_path = None

    for attempt in range(max_attempts):
        device_path = find_connected_device_path()
        if device_path is not None:
            break
        if attempt < max_attempts - 1:  # 最后一次不 sleep
            print(f"第 {attempt+1} 次未找到，3秒后重试...")
            time.sleep(3)

    print(f"[*] 最终结果: {device_path}")

    if not device_path:
        print("[!] 未找到任何已连接的蓝牙媒体传输设备，退出采集并准备重试。")
        # 不再显式 p.terminate()，让 finally 去做
        # 同理 wf.close() 也不在这里做
        return False

    try:
        while True:
            transport_state = listen_for_bluetooth_events(device_path)
            if transport_state is None:
                print("[!] 媒体传输状态 None，退出采集并让外层去重试连接。")
                return False

            if transport_state == "active" and stream is None:
                send_wakeup_event(wakeup_publisher)
                print("[*] 开始采集音频...")
                stream = p.open(
                    format=FORMAT,
                    channels=CHANNELS,
                    rate=RATE,
                    input=True,
                    frames_per_buffer=frames_per_buffer
                )

            elif transport_state == "idle" and stream is not None:
                print("[*] 停止采集音频...")
                stream.stop_stream()
                stream.close()
                stream = None
                if wf:
                    wf.close()
                    print(f"[*] wf cloese, 音频已保存到 {wav_filename}")

            if stream is not None:
                frames_available = stream.get_read_available()
                if frames_available > 0:
                    frames_to_read = min(frames_available, frames_per_buffer)
                    audio_data = stream.read(frames_to_read, exception_on_overflow=False)

                    # 发送 ROS 消息
                    msg = PCMStream()
                    msg.ts = int(time.time() * 1000)
                    msg.data = list(audio_data)
                    pcm_publisher.publish(msg)
                    print("PCM字节流(hex):", audio_data.hex()[:80], "...")

                    if save_to_file and wf is not None:
                        wf.writeframes(audio_data)

            time.sleep(0.1)

    except KeyboardInterrupt:
        print("捕获到 Ctrl+C，停止采集。")
    finally:
        # 在 finally 中统一处理所有资源清理
        if stream:
            stream.stop_stream()
            stream.close()
        if wf:
            wf.close()
            print(f"[*] 音频已保存到 {wav_filename}")
        p.terminate()

    return True

def is_user_bound(config_path=CONFIG_PATH):
    """
    从给定的配置文件中获取 userBind 和 usrkey 的值。
    若 userBind 为 '1' 且 usrkey 不为 None，表示已绑定，返回 True；
    否则返回 False。
    """
    device_config = DeviceConfig(config_path)
    user_bind = device_config.get_config_value('userBind')
    usrkey = device_config.get_config_value('usrkey')

    return (user_bind is not None and user_bind == '1' and usrkey is not None)

def main():
    rclpy.init()
    node = rclpy.create_node("audio_publisher_node")

    pcm_publisher = node.create_publisher(PCMStream, "/audio_recorder/pcm_stream", 10)
    wakeup_publisher = node.create_publisher(Wakeup, "/audio_recorder/wakeup_event", 10)

    first_run = True
    try:
        while True:
            if is_user_bound():
                print("[*] 开始执行蓝牙配对和音频采集流程...")
                start_pulseaudio(first_run=first_run)
                first_run = False
                cleanup_bluetooth()
                bluetooth_pair()

                capture_audio_on_button_press(
                    pcm_publisher=pcm_publisher,
                    wakeup_publisher=wakeup_publisher,
                    save_to_file=False
                )
            else:
                print("[!] 设备未绑定，不执行经典蓝牙配对。")

            time.sleep(2)  # 防止循环过于紧凑
    except KeyboardInterrupt:
        print("[!] 检测到 Ctrl+C，退出循环。")

    print("[*] 脚本执行完成。")
    rclpy.shutdown()

if __name__ == "__main__":
    main()


