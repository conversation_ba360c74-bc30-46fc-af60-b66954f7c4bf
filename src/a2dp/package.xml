<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>a2dp</name>
  <version>0.0.0</version>
  <description>A ROS2 package for A2DP audio streaming</description>
  <maintainer email="<EMAIL>">yuwei</maintainer>
  <license>TODO: License declaration</license>

  <!-- Build dependencies -->
  <buildtool_depend>ament_python</buildtool_depend>

  <depend>rclpy</depend>  <!-- ROS2 Python客户端库 -->
  <depend>homi_speech_interface</depend>  <!-- 自定义的 ROS2 消息接口包 -->
  <depend>pexpect</depend>  <!-- 用于蓝牙操作 -->
  <depend>pyaudio</depend>  <!-- 用于音频捕获 -->

  <!-- Test dependencies -->
  <test_depend>ament_copyright</test_depend>
  <test_depend>ament_flake8</test_depend>
  <test_depend>ament_pep257</test_depend>
  <test_depend>python3-pytest</test_depend>

  <!-- Exporting build type -->
  <export>
    <build_type>ament_python</build_type>
  </export>
</package>

