#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
BLE节点异常处理器
专门处理BLE相关的异常情况和恢复策略
"""

import time
import subprocess
import threading
from typing import Dict, Any, Optional
from .exception_manager import ExceptionManager, ExceptionType, ExceptionSeverity, ExceptionRecord


class BLEExceptionHandler:
    """BLE异常处理器"""
    
    def __init__(self, ble_node, exception_manager: ExceptionManager):
        self.ble_node = ble_node
        self.exception_manager = exception_manager
        self.logger = ble_node.get_logger()
        
        # BLE状态监控
        self.ble_health_check_interval = 30  # 30秒检查一次
        self.last_successful_send = time.time()
        self.last_successful_receive = time.time()
        self.send_timeout_threshold = 120  # 2分钟没有成功发送视为异常
        self.receive_timeout_threshold = 300  # 5分钟没有接收到数据视为异常
        
        # 连接重试配置
        self.max_reconnect_attempts = 3
        self.reconnect_delay = 5  # 重连延迟（秒）
        
        # 注册异常处理器
        self._register_handlers()
        self._register_recovery_strategies()
        
        # 启动健康检查
        self.health_check_thread = threading.Thread(target=self._health_check_loop, daemon=True)
        self.health_check_thread.start()
        
        self.logger.info("BLE异常处理器已初始化")

    def _register_handlers(self):
        """注册异常处理器"""
        self.exception_manager.register_handler(
            ExceptionType.BLE_SEND_ERROR, 
            self._handle_send_error
        )
        self.exception_manager.register_handler(
            ExceptionType.BLE_RECEIVE_ERROR, 
            self._handle_receive_error
        )
        self.exception_manager.register_handler(
            ExceptionType.BLE_CONNECTION_ERROR, 
            self._handle_connection_error
        )
        self.exception_manager.register_handler(
            ExceptionType.BLE_ADVERTISEMENT_ERROR, 
            self._handle_advertisement_error
        )
        self.exception_manager.register_handler(
            ExceptionType.BLE_DEVICE_ERROR, 
            self._handle_device_error
        )

    def _register_recovery_strategies(self):
        """注册恢复策略"""
        self.exception_manager.register_recovery_strategy(
            ExceptionType.BLE_SEND_ERROR, 
            self._recover_send_error
        )
        self.exception_manager.register_recovery_strategy(
            ExceptionType.BLE_RECEIVE_ERROR, 
            self._recover_receive_error
        )
        self.exception_manager.register_recovery_strategy(
            ExceptionType.BLE_CONNECTION_ERROR, 
            self._recover_connection_error
        )
        self.exception_manager.register_recovery_strategy(
            ExceptionType.BLE_ADVERTISEMENT_ERROR, 
            self._recover_advertisement_error
        )
        self.exception_manager.register_recovery_strategy(
            ExceptionType.BLE_DEVICE_ERROR, 
            self._recover_device_error
        )

    def report_send_error(self, error_message: str, context: Optional[Dict[str, Any]] = None):
        """报告发送错误"""
        self.exception_manager.report_exception(
            exception_type=ExceptionType.BLE_SEND_ERROR,
            message=f"BLE发送失败: {error_message}",
            severity=ExceptionSeverity.MEDIUM,
            context=context
        )

    def report_receive_error(self, error_message: str, context: Optional[Dict[str, Any]] = None):
        """报告接收错误"""
        self.exception_manager.report_exception(
            exception_type=ExceptionType.BLE_RECEIVE_ERROR,
            message=f"BLE接收失败: {error_message}",
            severity=ExceptionSeverity.MEDIUM,
            context=context
        )

    def report_connection_error(self, error_message: str, context: Optional[Dict[str, Any]] = None):
        """报告连接错误"""
        self.exception_manager.report_exception(
            exception_type=ExceptionType.BLE_CONNECTION_ERROR,
            message=f"BLE连接异常: {error_message}",
            severity=ExceptionSeverity.HIGH,
            context=context
        )

    def report_successful_send(self):
        """报告成功发送"""
        self.last_successful_send = time.time()

    def report_successful_receive(self):
        """报告成功接收"""
        self.last_successful_receive = time.time()

    def _handle_send_error(self, record: ExceptionRecord):
        """处理发送错误"""
        self.logger.warning(f"处理BLE发送错误: {record.message}")
        
        # 检查BLE管理器状态
        if hasattr(self.ble_node, 'ble_manager') and self.ble_node.ble_manager:
            try:
                # 尝试重新初始化发送缓冲区
                if hasattr(self.ble_node.ble_manager, 'reset_send_buffer'):
                    self.ble_node.ble_manager.reset_send_buffer()
            except Exception as e:
                self.logger.error(f"重置发送缓冲区失败: {e}")

    def _handle_receive_error(self, record: ExceptionRecord):
        """处理接收错误"""
        self.logger.warning(f"处理BLE接收错误: {record.message}")
        
        # 检查接收缓冲区
        if hasattr(self.ble_node, 'ble_manager') and self.ble_node.ble_manager:
            try:
                if hasattr(self.ble_node.ble_manager, 'reset_receive_buffer'):
                    self.ble_node.ble_manager.reset_receive_buffer()
            except Exception as e:
                self.logger.error(f"重置接收缓冲区失败: {e}")

    def _handle_connection_error(self, record: ExceptionRecord):
        """处理连接错误"""
        self.logger.error(f"处理BLE连接错误: {record.message}")
        
        # 标记需要重新连接
        if hasattr(self.ble_node, 'ble_manager'):
            self.ble_node.ble_manager = None

    def _handle_advertisement_error(self, record: ExceptionRecord):
        """处理广播错误"""
        self.logger.warning(f"处理BLE广播错误: {record.message}")

    def _handle_device_error(self, record: ExceptionRecord):
        """处理设备错误"""
        self.logger.error(f"处理BLE设备错误: {record.message}")

    def _recover_send_error(self, record: ExceptionRecord) -> bool:
        """恢复发送错误"""
        try:
            self.logger.info("尝试恢复BLE发送功能...")
            
            # 步骤1: 重新初始化BLE管理器
            if self._reinitialize_ble_manager():
                # 步骤2: 测试发送功能
                if self._test_send_function():
                    self.logger.info("BLE发送功能恢复成功")
                    return True
            
            self.logger.warning("BLE发送功能恢复失败")
            return False
            
        except Exception as e:
            self.logger.error(f"恢复发送错误时发生异常: {e}")
            return False

    def _recover_receive_error(self, record: ExceptionRecord) -> bool:
        """恢复接收错误"""
        try:
            self.logger.info("尝试恢复BLE接收功能...")
            
            # 重新初始化BLE管理器
            if self._reinitialize_ble_manager():
                self.logger.info("BLE接收功能恢复成功")
                return True
            
            self.logger.warning("BLE接收功能恢复失败")
            return False
            
        except Exception as e:
            self.logger.error(f"恢复接收错误时发生异常: {e}")
            return False

    def _recover_connection_error(self, record: ExceptionRecord) -> bool:
        """恢复连接错误"""
        try:
            self.logger.info("尝试恢复BLE连接...")
            
            for attempt in range(self.max_reconnect_attempts):
                self.logger.info(f"BLE重连尝试 {attempt + 1}/{self.max_reconnect_attempts}")
                
                # 重启蓝牙服务
                if self._restart_bluetooth_service():
                    time.sleep(self.reconnect_delay)
                    
                    # 重新初始化BLE管理器
                    if self._reinitialize_ble_manager():
                        self.logger.info("BLE连接恢复成功")
                        return True
                
                time.sleep(self.reconnect_delay)
            
            self.logger.error("BLE连接恢复失败，已达到最大重试次数")
            return False
            
        except Exception as e:
            self.logger.error(f"恢复连接错误时发生异常: {e}")
            return False

    def _recover_advertisement_error(self, record: ExceptionRecord) -> bool:
        """恢复广播错误"""
        try:
            self.logger.info("尝试恢复BLE广播...")
            
            if hasattr(self.ble_node, 'ble_manager') and self.ble_node.ble_manager:
                # 重新启动广播
                if hasattr(self.ble_node.ble_manager, 'update_advertisement'):
                    self.ble_node.ble_manager.update_advertisement(True)
                    self.logger.info("BLE广播恢复成功")
                    return True
            
            # 如果BLE管理器不存在，尝试重新初始化
            if self._reinitialize_ble_manager():
                self.logger.info("BLE广播恢复成功")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"恢复广播错误时发生异常: {e}")
            return False

    def _recover_device_error(self, record: ExceptionRecord) -> bool:
        """恢复设备错误"""
        try:
            self.logger.info("尝试恢复BLE设备...")
            
            # 重启蓝牙服务
            if self._restart_bluetooth_service():
                time.sleep(5)  # 等待服务完全启动
                
                # 重新初始化
                if self._reinitialize_ble_manager():
                    self.logger.info("BLE设备恢复成功")
                    return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"恢复设备错误时发生异常: {e}")
            return False

    def _reinitialize_ble_manager(self) -> bool:
        """重新初始化BLE管理器"""
        try:
            self.logger.info("重新初始化BLE管理器...")
            
            # 清理旧的BLE管理器
            if hasattr(self.ble_node, 'ble_manager') and self.ble_node.ble_manager:
                try:
                    self.ble_node.ble_manager.cleanup()
                except:
                    pass
                self.ble_node.ble_manager = None
            
            # 重新创建BLE管理器
            from .ble_manager import Ble_manager
            self.ble_node.ble_manager = Ble_manager(self.ble_node)
            
            self.logger.info("BLE管理器重新初始化成功")
            return True
            
        except Exception as e:
            self.logger.error(f"重新初始化BLE管理器失败: {e}")
            return False

    def _restart_bluetooth_service(self) -> bool:
        """重启蓝牙服务"""
        try:
            self.logger.info("重启蓝牙服务...")
            
            # 停止蓝牙服务
            subprocess.run(['sudo', 'systemctl', 'stop', 'bluetooth'], 
                         check=False, capture_output=True)
            time.sleep(2)
            
            # 启动蓝牙服务
            result = subprocess.run(['sudo', 'systemctl', 'start', 'bluetooth'], 
                                  check=False, capture_output=True)
            
            if result.returncode == 0:
                self.logger.info("蓝牙服务重启成功")
                return True
            else:
                self.logger.error(f"蓝牙服务重启失败: {result.stderr.decode()}")
                return False
                
        except Exception as e:
            self.logger.error(f"重启蓝牙服务时发生异常: {e}")
            return False

    def _test_send_function(self) -> bool:
        """测试发送功能"""
        try:
            # 这里可以发送一个测试消息来验证发送功能
            # 具体实现依赖于BLE管理器的接口
            return True
        except Exception as e:
            self.logger.error(f"测试发送功能失败: {e}")
            return False

    def _health_check_loop(self):
        """健康检查循环"""
        while True:
            try:
                time.sleep(self.ble_health_check_interval)
                self._perform_health_check()
            except Exception as e:
                self.logger.error(f"健康检查时发生错误: {e}")

    def _perform_health_check(self):
        """执行健康检查"""
        current_time = time.time()
        
        # 检查发送超时
        if current_time - self.last_successful_send > self.send_timeout_threshold:
            self.exception_manager.report_exception(
                exception_type=ExceptionType.BLE_SEND_ERROR,
                message="发送功能超时：长时间未成功发送数据",
                severity=ExceptionSeverity.HIGH,
                context={
                    "last_send_time": self.last_successful_send,
                    "timeout_threshold": self.send_timeout_threshold
                }
            )
        
        # 检查接收超时
        if current_time - self.last_successful_receive > self.receive_timeout_threshold:
            self.exception_manager.report_exception(
                exception_type=ExceptionType.BLE_RECEIVE_ERROR,
                message="接收功能超时：长时间未接收到数据",
                severity=ExceptionSeverity.MEDIUM,
                context={
                    "last_receive_time": self.last_successful_receive,
                    "timeout_threshold": self.receive_timeout_threshold
                }
            )
        
        # 检查BLE管理器状态
        if not hasattr(self.ble_node, 'ble_manager') or self.ble_node.ble_manager is None:
            self.exception_manager.report_exception(
                exception_type=ExceptionType.BLE_CONNECTION_ERROR,
                message="BLE管理器未初始化或已损坏",
                severity=ExceptionSeverity.CRITICAL
            )

    def get_health_status(self) -> Dict[str, Any]:
        """获取健康状态"""
        current_time = time.time()
        return {
            "send_health": {
                "last_successful": self.last_successful_send,
                "seconds_since_last": current_time - self.last_successful_send,
                "is_healthy": current_time - self.last_successful_send < self.send_timeout_threshold
            },
            "receive_health": {
                "last_successful": self.last_successful_receive,
                "seconds_since_last": current_time - self.last_successful_receive,
                "is_healthy": current_time - self.last_successful_receive < self.receive_timeout_threshold
            },
            "manager_health": {
                "is_initialized": hasattr(self.ble_node, 'ble_manager') and self.ble_node.ble_manager is not None
            }
        } 