#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
异常管理器模块
用于BLE和Network节点的异常处理、监控和恢复机制
"""

import time
import threading
import traceback
from enum import Enum
from typing import Dict, Optional, Callable, Any
from dataclasses import dataclass
from datetime import datetime, timedelta
import json


class ExceptionType(Enum):
    """异常类型枚举"""
    # BLE相关异常
    BLE_SEND_ERROR = "ble_send_error"
    BLE_RECEIVE_ERROR = "ble_receive_error"
    BLE_CONNECTION_ERROR = "ble_connection_error"
    BLE_ADVERTISEMENT_ERROR = "ble_advertisement_error"
    BLE_DEVICE_ERROR = "ble_device_error"
    
    # WiFi相关异常
    WIFI_CONNECTION_ERROR = "wifi_connection_error"
    WIFI_SCAN_ERROR = "wifi_scan_error"
    WIFI_AUTHENTICATION_ERROR = "wifi_authentication_error"
    WIFI_INTERFACE_ERROR = "wifi_interface_error"
    
    # 网络相关异常
    NETWORK_INTERFACE_ERROR = "network_interface_error"
    NETWORK_CONNECTIVITY_ERROR = "network_connectivity_error"
    DNS_RESOLUTION_ERROR = "dns_resolution_error"
    
    # 系统相关异常
    SYSTEM_RESOURCE_ERROR = "system_resource_error"
    CONFIG_FILE_ERROR = "config_file_error"
    SERVICE_ERROR = "service_error"


class ExceptionSeverity(Enum):
    """异常严重程度"""
    LOW = 1      # 低：不影响主要功能
    MEDIUM = 2   # 中：影响部分功能
    HIGH = 3     # 高：影响主要功能
    CRITICAL = 4 # 严重：系统无法正常工作


@dataclass
class ExceptionRecord:
    """异常记录"""
    exception_type: ExceptionType
    severity: ExceptionSeverity
    message: str
    timestamp: datetime
    stack_trace: Optional[str] = None
    context: Optional[Dict[str, Any]] = None
    recovery_attempted: bool = False
    recovery_successful: bool = False
    count: int = 1


class ExceptionManager:
    """异常管理器"""
    
    def __init__(self, logger, node_name: str = "unknown"):
        self.logger = logger
        self.node_name = node_name
        self.exceptions: Dict[str, ExceptionRecord] = {}
        self.exception_handlers: Dict[ExceptionType, Callable] = {}
        self.recovery_strategies: Dict[ExceptionType, Callable] = {}
        
        # 异常统计
        self.stats = {
            "total_exceptions": 0,
            "recoveries_attempted": 0,
            "recoveries_successful": 0,
            "critical_exceptions": 0
        }
        
        # 异常阈值配置
        self.thresholds = {
            ExceptionType.BLE_SEND_ERROR: 5,           # 5次发送错误
            ExceptionType.BLE_RECEIVE_ERROR: 10,       # 10次接收错误
            ExceptionType.WIFI_CONNECTION_ERROR: 3,    # 3次连接错误
            ExceptionType.NETWORK_CONNECTIVITY_ERROR: 5 # 5次网络连接错误
        }
        
        # 时间窗口配置（秒）
        self.time_windows = {
            ExceptionType.BLE_SEND_ERROR: 300,         # 5分钟
            ExceptionType.BLE_RECEIVE_ERROR: 600,      # 10分钟
            ExceptionType.WIFI_CONNECTION_ERROR: 1800, # 30分钟
            ExceptionType.NETWORK_CONNECTIVITY_ERROR: 900 # 15分钟
        }
        
        # 异常清理线程
        self.cleanup_thread = threading.Thread(target=self._cleanup_old_exceptions, daemon=True)
        self.cleanup_thread.start()
        
        self.logger.info(f"异常管理器已初始化 - 节点: {node_name}")

    def register_handler(self, exception_type: ExceptionType, handler: Callable):
        """注册异常处理器"""
        self.exception_handlers[exception_type] = handler
        self.logger.debug(f"已注册异常处理器: {exception_type.value}")

    def register_recovery_strategy(self, exception_type: ExceptionType, strategy: Callable):
        """注册恢复策略"""
        self.recovery_strategies[exception_type] = strategy
        self.logger.debug(f"已注册恢复策略: {exception_type.value}")

    def report_exception(self, 
                        exception_type: ExceptionType, 
                        message: str,
                        severity: ExceptionSeverity = ExceptionSeverity.MEDIUM,
                        context: Optional[Dict[str, Any]] = None,
                        stack_trace: Optional[str] = None) -> bool:
        """
        报告异常
        
        Returns:
            bool: 是否需要进行恢复操作
        """
        try:
            current_time = datetime.now()
            exception_key = f"{exception_type.value}_{current_time.strftime('%Y%m%d_%H')}"
            
            # 更新或创建异常记录
            if exception_key in self.exceptions:
                record = self.exceptions[exception_key]
                record.count += 1
                record.timestamp = current_time
                record.message = message  # 更新为最新消息
                if context:
                    record.context = context
            else:
                record = ExceptionRecord(
                    exception_type=exception_type,
                    severity=severity,
                    message=message,
                    timestamp=current_time,
                    stack_trace=stack_trace,
                    context=context
                )
                self.exceptions[exception_key] = record
            
            # 更新统计
            self.stats["total_exceptions"] += 1
            if severity == ExceptionSeverity.CRITICAL:
                self.stats["critical_exceptions"] += 1
            
            # 记录日志
            self._log_exception(record)
            
            # 调用异常处理器
            if exception_type in self.exception_handlers:
                try:
                    self.exception_handlers[exception_type](record)
                except Exception as e:
                    self.logger.error(f"异常处理器执行失败: {e}")
            
            # 检查是否需要恢复
            need_recovery = self._should_attempt_recovery(exception_type, record)
            
            if need_recovery:
                self._attempt_recovery(exception_type, record)
            
            return need_recovery
            
        except Exception as e:
            self.logger.error(f"报告异常时发生错误: {e}")
            return False

    def _log_exception(self, record: ExceptionRecord):
        """记录异常日志"""
        severity_map = {
            ExceptionSeverity.LOW: "DEBUG",
            ExceptionSeverity.MEDIUM: "WARN",
            ExceptionSeverity.HIGH: "ERROR",
            ExceptionSeverity.CRITICAL: "ERROR"
        }
        
        log_level = severity_map.get(record.severity, "ERROR")
        log_message = (
            f"🚨 [{record.exception_type.value}] {record.message} "
            f"(计数: {record.count}, 严重程度: {record.severity.name})"
        )
        
        if record.context:
            log_message += f" 上下文: {json.dumps(record.context, ensure_ascii=False)}"
        
        if log_level == "DEBUG":
            self.logger.debug(log_message)
        elif log_level == "WARN":
            self.logger.warning(log_message)
        else:
            self.logger.error(log_message)
            
        if record.stack_trace and record.severity in [ExceptionSeverity.HIGH, ExceptionSeverity.CRITICAL]:
            self.logger.error(f"堆栈跟踪: {record.stack_trace}")

    def _should_attempt_recovery(self, exception_type: ExceptionType, record: ExceptionRecord) -> bool:
        """判断是否应该尝试恢复"""
        # 严重异常立即尝试恢复
        if record.severity == ExceptionSeverity.CRITICAL:
            return True
        
        # 检查异常计数是否超过阈值
        threshold = self.thresholds.get(exception_type, 10)
        if record.count >= threshold:
            return True
        
        # 检查时间窗口内的异常频率
        time_window = self.time_windows.get(exception_type, 3600)  # 默认1小时
        cutoff_time = datetime.now() - timedelta(seconds=time_window)
        
        recent_count = sum(
            1 for r in self.exceptions.values()
            if r.exception_type == exception_type and r.timestamp > cutoff_time
        )
        
        return recent_count >= threshold

    def _attempt_recovery(self, exception_type: ExceptionType, record: ExceptionRecord):
        """尝试恢复"""
        if record.recovery_attempted:
            self.logger.debug(f"异常 {exception_type.value} 已尝试过恢复，跳过")
            return
        
        record.recovery_attempted = True
        self.stats["recoveries_attempted"] += 1
        
        if exception_type in self.recovery_strategies:
            try:
                self.logger.info(f"🔧 尝试恢复异常: {exception_type.value}")
                success = self.recovery_strategies[exception_type](record)
                
                if success:
                    record.recovery_successful = True
                    self.stats["recoveries_successful"] += 1
                    self.logger.info(f"✅ 异常恢复成功: {exception_type.value}")
                else:
                    self.logger.warning(f"❌ 异常恢复失败: {exception_type.value}")
                    
            except Exception as e:
                self.logger.error(f"恢复策略执行失败: {e}")
                self.logger.error(f"恢复策略堆栈: {traceback.format_exc()}")
        else:
            self.logger.warning(f"未找到恢复策略: {exception_type.value}")

    def get_exception_stats(self) -> Dict[str, Any]:
        """获取异常统计信息"""
        recent_exceptions = {}
        cutoff_time = datetime.now() - timedelta(hours=1)
        
        for record in self.exceptions.values():
            if record.timestamp > cutoff_time:
                exception_type = record.exception_type.value
                if exception_type not in recent_exceptions:
                    recent_exceptions[exception_type] = 0
                recent_exceptions[exception_type] += record.count
        
        return {
            "node_name": self.node_name,
            "stats": self.stats.copy(),
            "recent_exceptions": recent_exceptions,
            "active_exceptions": len(self.exceptions),
            "recovery_rate": (
                self.stats["recoveries_successful"] / max(1, self.stats["recoveries_attempted"])
            ) * 100
        }

    def clear_exceptions(self, exception_type: Optional[ExceptionType] = None):
        """清除异常记录"""
        if exception_type:
            keys_to_remove = [
                key for key, record in self.exceptions.items()
                if record.exception_type == exception_type
            ]
            for key in keys_to_remove:
                del self.exceptions[key]
            self.logger.info(f"已清除异常类型: {exception_type.value}")
        else:
            self.exceptions.clear()
            self.logger.info("已清除所有异常记录")

    def _cleanup_old_exceptions(self):
        """清理旧的异常记录"""
        while True:
            try:
                time.sleep(3600)  # 每小时清理一次
                cutoff_time = datetime.now() - timedelta(hours=24)  # 保留24小时内的记录
                
                keys_to_remove = [
                    key for key, record in self.exceptions.items()
                    if record.timestamp < cutoff_time
                ]
                
                for key in keys_to_remove:
                    del self.exceptions[key]
                
                if keys_to_remove:
                    self.logger.debug(f"清理了 {len(keys_to_remove)} 条过期异常记录")
                    
            except Exception as e:
                self.logger.error(f"清理异常记录时发生错误: {e}")


class ExceptionDecorator:
    """异常装饰器，用于自动捕获和报告异常"""
    
    def __init__(self, exception_manager: ExceptionManager, 
                 exception_type: ExceptionType,
                 severity: ExceptionSeverity = ExceptionSeverity.MEDIUM):
        self.exception_manager = exception_manager
        self.exception_type = exception_type
        self.severity = severity

    def __call__(self, func):
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                self.exception_manager.report_exception(
                    exception_type=self.exception_type,
                    message=f"函数 {func.__name__} 执行失败: {str(e)}",
                    severity=self.severity,
                    stack_trace=traceback.format_exc(),
                    context={
                        "function": func.__name__,
                        "args": str(args)[:200],  # 限制长度
                        "kwargs": str(kwargs)[:200]
                    }
                )
                raise
        return wrapper 