import rclpy
import json
from std_msgs.msg import String
import sys
# from .SockClass import TcpServer
import math
import dbus
from homi_speech_interface.srv import SIGCData
import binascii
import zlib 
import time
import struct
from rclpy.node import Node
import datetime
from homi_speech_interface.msg  import SIGCEvent
import threading
import subprocess
# from .custom_logger import CustomLogger
# from .read import DeviceConfig

def list_to_hex_string(list_data):
    list_str = '[ '
    for x in list_data:
        list_str += '0x{:02X},'.format(x)
    list_str += ' ]'
    return list_str

def get_timestamp_bytes():
    # 获取当前的时间戳
    current_timestamp = int(time.time())
    
    # 将整数时间戳转换为大端字节流
    bytes_stream = struct.pack('>I', current_timestamp)
    
    # 将字节流转换为十六进制列表
    return list(bytes_stream)


class DataProcessor:
    def __init__(self,Node,ble_adv_localname,manuf_code,manuf_data,deviceId,user_bind, ble_manager=None):

        self.tcp_socket = None
        self.client_socket = None
        self.client_address = None
        self.deviceId = deviceId
        self.device_login_status = 0
        self.bind_data_frame_num = -1
        self.control_data_frame_num = -1
        self.control_data_mult_frame = 0
        self.control_first_frame_data = None
        self.control_data_frame_data = []

        self.notify_characteristic_data = None
        self.ble_adv_localname = ble_adv_localname
        self.manuf_code = manuf_code
        self.manuf_data = manuf_data

        self.andlink_plugin_connection = 0

        self.ble_manager = ble_manager

        self.user_bind = user_bind

        self.Node = Node
        self.logger = self.Node.get_logger()

    def set_ble_manager(self,ble_manager):
        self.ble_manager = ble_manager

    def set_ble_adv_localname(self,localname):
        self.ble_adv_localname = localname

    def set_ble_adv_manuf_data(self,manuf_code,manuf_data):
        self.manuf_code = manuf_code
        self.manuf_data = manuf_data

    def set_ble_json_deviceId(self,deviceId):
        self.deviceId = deviceId

    def set_notify_characteristic_data(self, characteristic_data): 
        self.notify_characteristic_data = characteristic_data

    def ble_write_characteristic_data_process(self, data):
        self.logger.info(f"Processing BLE write characteristic data: {list_to_hex_string(data)}")

        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
        self.logger.info(f"log info: {current_time} - receive a frame: {list_to_hex_string(data)}")

        if data[0] == 0 and data[1] == 0:   #配网相关命令
            self.Node.start_ble_bind_timer()
            self.bind_data_frame_num = int(data[4])+1
        else:
            if data[0] == 0 and data[2] == 0xaa and data[3] == 0x02:
                self.bind_data_frame_num = 0
                self.control_first_frame_data = data
                self.control_data_frame_num = data[1]
                self.control_data_frame_data = data[9:]
            elif data[0] == 0 and data[2] == 0xc1 and data[3] == 0x02:     #时间同步更新
                self.bind_data_frame_num = 0
                self.control_first_frame_data = data
                self.control_data_frame_num = data[1]
                self.control_data_frame_data = data[9:]
            elif data[0] == 0 and data[2] == 0xc1 and data[3] == 0x06:     #控制命令更新
                self.bind_data_frame_num = 0
                self.control_first_frame_data = data
                self.control_data_frame_num = data[1]
                self.control_data_frame_data = data[9:]
            elif data[0] == 0 and data[2] == 0xc2 and data[3] == 0x05:     #状态获取命令更新
                self.bind_data_frame_num = 0
                self.control_first_frame_data = data
                self.control_data_frame_num = data[1]
                self.control_data_frame_data = data[9:]
            else:
                self.control_data_frame_data.extend(data[1:])

        if self.bind_data_frame_num > 0:
            self.logger.info(f"Handling bind data frame. Remaining frames: {self.bind_data_frame_num}")
            byte_values = [byte for byte in data]
            byte_array = bytes(byte_values)
            self.logger.info(f'Published byte stream: {list(byte_array)}')
            self.Node.publish_byte_stream(self.Node.ble_stream_publisher_,byte_array)

            if self.bind_data_frame_num == 0:
                self.ble_notify_characteristic_data_process(b'00')
            self.bind_data_frame_num -= 1

        if self.control_data_frame_num > 0:
            self.logger.info(f"Handling control data frame. Remaining frames: {self.control_data_frame_num}")
            self.control_data_frame_num -= 1
            if self.control_data_frame_num == 0:
                if self.control_first_frame_data[2] == 0xaa and self.control_first_frame_data[3] == 0x02:
                    self.ble_notify_characteristic_data_login_status_response()  #登陆状态上传
                else:
                    if self.control_first_frame_data[2] == 0xc1:
                        if self.control_first_frame_data[3] == 0x02:      #时间同步
                            self.ble_notify_characteristic_data_settime_response()
                        elif self.control_first_frame_data[3] == 0x06:    #控制命令
                            self.ble_publish_data_process()
                            self.ble_notify_characteristic_data_control_response()
                    elif self.control_first_frame_data[2] == 0xc2:    #设备查询
                        if self.control_first_frame_data[3] == 0x05:
                            self.ble_publish_data_process()

    def ble_notify_characteristic_data_login_status_response(self):
        self.logger.info("Preparing login status response.")
        if len(self.control_first_frame_data) > 9:
            response_all_list = self.control_first_frame_data[:9]
            response_all_list[1] = 0x01
            response_all_list.append(0x01)
            response_all_list[5:9] = get_timestamp_bytes()
            self.ble_notify_characteristic_data_process(response_all_list)
            self.device_login_status = 1
        else:
            self.logger.warning("receice frame format is error")

    def ble_notify_characteristic_data_status_response(self, response_all_data_string):
        self.logger.info("Preparing status response.")
        if len(self.control_first_frame_data) > 9:
            response_all_data_bytes=response_all_data_string.encode("utf-8")
            response_all_list = self.control_first_frame_data[:9]
            response_all_data_bytes_len = len(response_all_data_bytes)
            frame_num = (response_all_data_bytes_len-9)/19
            frame_num = math.ceil(frame_num)
            response_all_list[1]= frame_num + 1
            response_all_list[5:9] = get_timestamp_bytes()
            response_all_list[9:11] = list(response_all_data_bytes_len.to_bytes(2, byteorder='little'))
            response_all_list[11:20] = response_all_data_bytes[0:9]
            self.ble_notify_characteristic_data_process(response_all_list)
            i = 1
            while frame_num > 0:
                frame_num -= 1
                response_all_list=[i]
                response_all_list.extend(response_all_data_bytes[9+19*(i-1):9+19*(i-1)+19])
                i += 1
                self.ble_notify_characteristic_data_process(response_all_list)
        else:
            self.logger.warning("receice frame format is error")
    
    def ble_notify_characteristic_data_settime_response(self):
        self.logger.info("Preparing set time response.")
        if len(self.control_first_frame_data) > 9:
            response_all_list = self.control_first_frame_data[:9]
            response_result_list = [0x01]
            response_all_list.extend(response_result_list)
            response_all_list[5:9] = get_timestamp_bytes()
            self.ble_notify_characteristic_data_process(response_all_list)
        else:
            self.logger.warning("receice frame format is error")

    def ble_notify_characteristic_data_control_response(self):
        self.logger.info("Preparing control response.")
        if len(self.control_first_frame_data) > 9:
            response_all_list = self.control_first_frame_data[:9]
            response_result_list = [0x01]
            response_all_list.extend(response_result_list)
            response_all_list[1] = 0x01
            response_all_list[5:9] = get_timestamp_bytes()
            self.ble_notify_characteristic_data_process(response_all_list)
        else:
            self.logger.warning("receice frame format is error")

    def ble_notify_characteristic_data_control_error_nologin_response(self):
        self.logger.debug(sys._getframe().f_code.co_name)
        if len(self.control_first_frame_data) > 9:
            response_all_list = [0x00,0x01,0x00,0x02]
            response_all_list[5:9] = get_timestamp_bytes()
            self.ble_notify_characteristic_data_process(response_all_list)
        else:
            self.logger.warning("receice frame format is error")

    def ble_notify_characteristic_data_control_ack_response(self):
        self.logger.debug(sys._getframe().f_code.co_name)
        if len(self.control_first_frame_data) > 9:
            response_all_list = [0x00,0x01,0x00,0x00]
            response_all_list[5:9] = get_timestamp_bytes()
            self.ble_notify_characteristic_data_process(response_all_list)
        else:
            self.logger.warning("receice frame format is error")

    def ble_notify_characteristic_data_control_nack_response(self):
        self.logger.debug(sys._getframe().f_code.co_name)
        if len(self.control_first_frame_data) > 9:
            response_all_list = [0x00,0x01,0x00,0x01]
            response_all_list[5:9] = get_timestamp_bytes()
            self.ble_notify_characteristic_data_process(response_all_list)
        else:
            self.logger.warning("receice frame format is error")

    def ble_notify_characteristic_data_process(self, data):
        self.logger.info(f"Notifying characteristic data: {list_to_hex_string(data)}")
        self.notify_characteristic_data.notify_update(data)
        json_string_from_bytes = bytes(self.control_data_frame_data).decode('utf-8')
        self.logger.debug(f"receive json from app:{json_string_from_bytes}")
        self.logger.debug(f'response a frame:{list_to_hex_string(data)}')

    def mac_to_bytes(self,mac):
        if len(mac) != 12:
            raise ValueError("MAC address should be exactly 12 characters long")
        return bytes(int(mac[i:i+2], 16) for i in range(0, len(mac), 2))

    def ble_publish_data_process(self):    
        try:
            self.logger.info("Processing BLE publish data.")
            json_string_from_bytes = bytes(self.control_data_frame_data).decode('utf-8')
            json_dict = json.loads(json_string_from_bytes)
            json_dict["deviceId"] = self.deviceId
            json_string_from_bytes = json.dumps(json_dict)
            self.logger.debug(f"publish json: {json_string_from_bytes}")
            self.Node.publish_app_cmd(json_string_from_bytes)
        except Exception as e:
            self.logger.error(f"Error during BLE publish data processing: {e}")
