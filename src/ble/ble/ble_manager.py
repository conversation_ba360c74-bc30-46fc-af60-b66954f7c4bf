#!/usr/bin/env python3
# SPDX-License-Identifier: LGPL-2.1-or-later

from __future__ import print_function
import time
import argparse
from random import randint

import threading

import dbus
import dbus.exceptions
import dbus.service
from gi.repository import GLib
import dbus.mainloop.glib

import array

import subprocess

try:
    from gi.repository import GObject  # python3
except ImportError:
    import gobject as GObject  # python2


# from .custom_logger import CustomLogger
from .ble_data_process import DataProcessor

mainloop = None

# Interfaces and Constants
BLUEZ_SERVICE_NAME = 'org.bluez'
LE_ADVERTISING_MANAGER_IFACE = 'org.bluez.LEAdvertisingManager1'
DBUS_OM_IFACE = 'org.freedesktop.DBus.ObjectManager'
DBUS_PROP_IFACE = 'org.freedesktop.DBus.Properties'

LE_ADVERTISEMENT_IFACE = 'org.bluez.LEAdvertisement1'

GATT_SERVICE_IFACE = 'org.bluez.GattService1'
GATT_CHRC_IFACE = 'org.bluez.GattCharacteristic1'
GATT_DESC_IFACE = 'org.bluez.GattDescriptor1'

GATT_MANAGER_IFACE = 'org.bluez.GattManager1'
DEVICE_INTERFACE = 'org.bluez.Device1'

BLUEZ_SERVICE_NAME = "org.bluez"
ADAPTER_INTERFACE = "org.bluez.Adapter1"
DBUS_PROP_IFACE = "org.freedesktop.DBus.Properties"


# D-bus Exception Deal
class InvalidArgsException(dbus.exceptions.DBusException):
    _dbus_error_name = 'org.freedesktop.DBus.Error.InvalidArgs'


class NotSupportedException(dbus.exceptions.DBusException):
    _dbus_error_name = 'org.bluez.Error.NotSupported'


class NotPermittedException(dbus.exceptions.DBusException):
    _dbus_error_name = 'org.bluez.Error.NotPermitted'


class InvalidValueLengthException(dbus.exceptions.DBusException):
    _dbus_error_name = 'org.bluez.Error.InvalidValueLength'


class FailedException(dbus.exceptions.DBusException):
    _dbus_error_name = 'org.bluez.Error.Failed'

## 广播包设置
class Advertisement(dbus.service.Object):
    PATH_BASE = '/org/bluez/example/advertisement'

    def __init__(self, bus, index, advertising_type):
        self.path = self.PATH_BASE + str(index)
        self.bus = bus
        self.ad_type = advertising_type
        self.service_uuids = None
        self.manufacturer_data = None
        self.solicit_uuids = None
        self.service_data = None
        self.local_name = None
        self.include_tx_power = False
        self.data = None
        self.min_interval = 0x0100  # 默认最小间隔
        self.max_interval = 0x0100  # 默认最大间隔
        dbus.service.Object.__init__(self, bus, self.path)

    def set_advertising_interval(self, min_interval, max_interval):
        self.min_interval = min_interval
        self.max_interval = max_interval

    def update_advertisement(self, service_uuids=None, manufacturer_data=None,
                            solicit_uuids=None, service_data=None,
                            local_name=None, include_tx_power=None, data=None):
        if service_uuids is not None:
            self.service_uuids = service_uuids
        if manufacturer_data is not None:
            for manuf_code, manuf_data in manufacturer_data.items():
                self.manufacturer_data[manuf_code] = dbus.Array(manuf_data, signature='y')
        if solicit_uuids is not None:
            self.solicit_uuids = solicit_uuids
        if service_data is not None:
            self.service_data = service_data
        if local_name is not None:
            self.local_name = local_name
        if include_tx_power is not None:
            self.include_tx_power = include_tx_power
        if data is not None:
            self.data = data
        # 通知 D-Bus 属性已更改
        self.PropertiesChanged(LE_ADVERTISEMENT_IFACE, self.get_properties()[LE_ADVERTISEMENT_IFACE], [])

    @dbus.service.signal(DBUS_PROP_IFACE, signature='sa{sv}as')
    def PropertiesChanged(self, interface, changed_properties, invalidated_properties):
        pass

    def get_properties(self):
        properties = dict()
        properties['Type'] = self.ad_type
        if self.service_uuids is not None:
            properties['ServiceUUIDs'] = dbus.Array(self.service_uuids,
                                                    signature='s')
        if self.solicit_uuids is not None:
            properties['SolicitUUIDs'] = dbus.Array(self.solicit_uuids,
                                                    signature='s')
        if self.manufacturer_data is not None:
            properties['ManufacturerData'] = dbus.Dictionary(
                self.manufacturer_data, signature='qv')
        if self.service_data is not None:
            properties['ServiceData'] = dbus.Dictionary(self.service_data,
                                                        signature='sv')
        if self.local_name is not None:
            properties['LocalName'] = dbus.String(self.local_name)
        if self.include_tx_power:
            properties['Includes'] = dbus.Array(["tx-power"], signature='s')

        if self.data is not None:
            properties['Data'] = dbus.Dictionary(
                self.data, signature='yv')
        return {LE_ADVERTISEMENT_IFACE: properties}

    def get_path(self):
        return dbus.ObjectPath(self.path)

    def add_service_uuid(self, uuid):
        if not self.service_uuids:
            self.service_uuids = []
        self.service_uuids.append(uuid)

    def add_solicit_uuid(self, uuid):
        if not self.solicit_uuids:
            self.solicit_uuids = []
        self.solicit_uuids.append(uuid)

    def add_manufacturer_data(self, manuf_code, data):
        if not self.manufacturer_data:
            self.manufacturer_data = dbus.Dictionary({}, signature='qv')
        self.manufacturer_data[manuf_code] = dbus.Array(data, signature='y')

    def add_service_data(self, uuid, data):
        if not self.service_data:
            self.service_data = dbus.Dictionary({}, signature='sv')
        self.service_data[uuid] = dbus.Array(data, signature='y')

    def add_local_name(self, name):
        if not self.local_name:
            self.local_name = ""
        self.local_name = dbus.String(name)

    def add_data(self, ad_type, data):
        if not self.data:
            self.data = dbus.Dictionary({}, signature='yv')
        self.data[ad_type] = dbus.Array(data, signature='y')

    @dbus.service.method(DBUS_PROP_IFACE,
                         in_signature='s',
                         out_signature='a{sv}')
    def GetAll(self, interface):
        #print('GetAll')
        if interface != LE_ADVERTISEMENT_IFACE:
            raise InvalidArgsException()
        #print('returning props')
        return self.get_properties()[LE_ADVERTISEMENT_IFACE]

    @dbus.service.method(LE_ADVERTISEMENT_IFACE,
                         in_signature='',
                         out_signature='')
    def Release(self):
        print('%s: Released!' % self.path)


class Service(dbus.service.Object):
    """
    org.bluez.GattService1 interface implementation
    """
    PATH_BASE = '/org/bluez/example/service'

    def __init__(self, bus, index, uuid, primary):
        self.path = self.PATH_BASE + str(index)
        self.bus = bus
        self.uuid = uuid
        self.primary = primary
        self.characteristics = []
        dbus.service.Object.__init__(self, bus, self.path)

    def get_properties(self):
        return {
                GATT_SERVICE_IFACE: {
                        'UUID': self.uuid,
                        'Primary': self.primary,
                        'Characteristics': dbus.Array(
                                self.get_characteristic_paths(),
                                signature='o')
                }
        }

    def get_path(self):
        return dbus.ObjectPath(self.path)

    def add_characteristic(self, characteristic):
        self.characteristics.append(characteristic)

    def get_characteristic_paths(self):
        result = []
        for chrc in self.characteristics:
            result.append(chrc.get_path())
        return result

    def get_characteristics(self):
        return self.characteristics

    @dbus.service.method(DBUS_PROP_IFACE,
                         in_signature='s',
                         out_signature='a{sv}')
    def GetAll(self, interface):
        if interface != GATT_SERVICE_IFACE:
            raise InvalidArgsException()

        return self.get_properties()[GATT_SERVICE_IFACE]


class Characteristic(dbus.service.Object):
    """
    org.bluez.GattCharacteristic1 interface implementation
    """
    def __init__(self, bus, index, uuid, flags, service):
        self.path = service.path + '/char' + str(index)
        self.bus = bus
        self.uuid = uuid
        self.service = service
        self.flags = flags
        self.descriptors = []
        dbus.service.Object.__init__(self, bus, self.path)

    def get_properties(self):
        return {
                GATT_CHRC_IFACE: {
                        'Service': self.service.get_path(),
                        'UUID': self.uuid,
                        'Flags': self.flags,
                        'Descriptors': dbus.Array(
                                self.get_descriptor_paths(),
                                signature='o')
                }
        }

    def get_path(self):
        return dbus.ObjectPath(self.path)

    def add_descriptor(self, descriptor):
        self.descriptors.append(descriptor)

    def get_descriptor_paths(self):
        result = []
        for desc in self.descriptors:
            result.append(desc.get_path())
        return result

    def get_descriptors(self):
        return self.descriptors

    @dbus.service.method(DBUS_PROP_IFACE,
                         in_signature='s',
                         out_signature='a{sv}')
    def GetAll(self, interface):
        if interface != GATT_CHRC_IFACE:
            raise InvalidArgsException()

        return self.get_properties()[GATT_CHRC_IFACE]

    @dbus.service.method(GATT_CHRC_IFACE,
                        in_signature='a{sv}',
                        out_signature='ay')
    def ReadValue(self, options):
        print('Default ReadValue called, returning error')
        raise NotSupportedException()

    @dbus.service.method(GATT_CHRC_IFACE, in_signature='aya{sv}')
    def WriteValue(self, value, options):
        print('Default WriteValue called, returning error')
        raise NotSupportedException()

    @dbus.service.method(GATT_CHRC_IFACE)
    def StartNotify(self):
        print('Default StartNotify called, returning error')
        raise NotSupportedException()

    @dbus.service.method(GATT_CHRC_IFACE)
    def StopNotify(self):
        print('Default StopNotify called, returning error')
        raise NotSupportedException()

    @dbus.service.signal(DBUS_PROP_IFACE,
                         signature='sa{sv}as')
    def PropertiesChanged(self, interface, changed, invalidated):
        pass


class Descriptor(dbus.service.Object):
    """
    org.bluez.GattDescriptor1 interface implementation
    """
    def __init__(self, bus, index, uuid, flags, characteristic):
        self.path = characteristic.path + '/desc' + str(index)
        self.bus = bus
        self.uuid = uuid
        self.flags = flags
        self.chrc = characteristic
        dbus.service.Object.__init__(self, bus, self.path)

    def get_properties(self):
        return {
                GATT_DESC_IFACE: {
                        'Characteristic': self.chrc.get_path(),
                        'UUID': self.uuid,
                        'Flags': self.flags,
                }
        }

    def get_path(self):
        return dbus.ObjectPath(self.path)

    @dbus.service.method(DBUS_PROP_IFACE,
                         in_signature='s',
                         out_signature='a{sv}')
    def GetAll(self, interface):
        if interface != GATT_DESC_IFACE:
            raise InvalidArgsException()

        return self.get_properties()[GATT_DESC_IFACE]

    @dbus.service.method(GATT_DESC_IFACE,
                        in_signature='a{sv}',
                        out_signature='ay')
    def ReadValue(self, options):
        print ('Default ReadValue called, returning error')
        raise NotSupportedException()

    @dbus.service.method(GATT_DESC_IFACE, in_signature='aya{sv}')
    def WriteValue(self, value, options):
        print('Default WriteValue called, returning error')
        raise NotSupportedException()


class Application(dbus.service.Object):
    """
    org.bluez.GattApplication1 interface implementation
    """
    def __init__(self, bus,data_process):
        self.path = '/'
        self.services = []
        self.data_process = data_process
        dbus.service.Object.__init__(self, bus, self.path)
        self.add_service(TestService(bus, 0,self.data_process))

    def get_path(self):
        return dbus.ObjectPath(self.path)

    def add_service(self, service):
        self.services.append(service)

    @dbus.service.method(DBUS_OM_IFACE, out_signature='a{oa{sa{sv}}}')
    def GetManagedObjects(self):
        response = {}
        for service in self.services:
            response[service.get_path()] = service.get_properties()
            chrcs = service.get_characteristics()
            for chrc in chrcs:
                response[chrc.get_path()] = chrc.get_properties()
                descs = chrc.get_descriptors()
                for desc in descs:
                    response[desc.get_path()] = desc.get_properties()
        return response


class TestAdvertisement(Advertisement):

    def __init__(self, bus, index,data_process):
        Advertisement.__init__(self, bus, index, 'peripheral')
        # self.add_service_uuid('180D')
        # self.add_service_uuid('180F')
        self.add_manufacturer_data(data_process.manuf_code, data_process.manuf_data)
#        self.add_service_data('9999', [0x00, 0x01, 0x02])
        self.add_local_name(data_process.ble_adv_localname)
        # self.include_tx_power = True
        # self.add_data(0x26, [0x01, 0x01, 0x00])
        # self.set_advertising_interval(0x0100, 0x0100)  # 设置广播间隔
        

    def set_manufacturer_data(self, manuf_code,data):
        self.add_manufacturer_data(manuf_code,data)
    def set_local_name(self, name):
        self.add_local_name(name)

class TestService(Service):
    """
    Dummy test service that provides characteristics and descriptors that
    exercise various API functionality.

    """
    TEST_SVC_UUID = '0000D459-0000-1000-8000-00805f9b34fb'

    def __init__(self, bus, index,data_process):
        Service.__init__(self, bus, index, self.TEST_SVC_UUID, True)
        self.data_process = data_process
        self.add_characteristic(WriteCharacteristic(bus, 0, self,self.data_process))
        self.add_characteristic(NotifyCharacteristic(bus, 1, self,self.data_process))
        self.add_characteristic(IndicateCharacteristic(bus, 2, self,self.data_process))
        # self.add_characteristic(TestEncryptCharacteristic(bus, 1, self))
        # self.add_characteristic(TestSecureCharacteristic(bus, 2, self))

class WriteCharacteristic(Characteristic):
    """
    Dummy test characteristic. Allows writing arbitrary bytes to its value, and
    contains "extended properties", as well as a test descriptor.

    """
    TEST_CHRC_UUID = '00000013-0000-1000-8000-00805f9b34fb'

    def __init__(self, bus, index, service,data_process):
        Characteristic.__init__(
                self, bus, index,
                self.TEST_CHRC_UUID,
                ['write'],
                service)
        self.data_process = data_process
        self.value = []
        self.add_descriptor(WriteDescriptor(bus, 0, self))
        self.add_descriptor(
                CharacteristicUserDescriptionDescriptor(bus, 1, self))

    def ReadValue(self, options):
        print('TestCharacteristic Read: ' + repr(self.value))
        return self.value

    def WriteValue(self, value, options):
        self.data_process.ble_write_characteristic_data_process(value)
        # print('TestCharacteristic Write: ' + repr(value))
        # self.value = value

class WriteDescriptor(Descriptor):
    """
    Dummy test descriptor. Returns a static value.

    """
    TEST_DESC_UUID = '12345678-1234-5678-1234-56789abcdef2'

    def __init__(self, bus, index, characteristic):
        Descriptor.__init__(
                self, bus, index,
                self.TEST_DESC_UUID,
                ['read', 'write'],
                characteristic)

    def ReadValue(self, options):
        return [
                dbus.Byte('T'), dbus.Byte('e'), dbus.Byte('s'), dbus.Byte('t')
        ]

class NotifyCharacteristic(Characteristic):
    """
    Dummy test characteristic. Allows writing arbitrary bytes to its value, and
    contains "extended properties", as well as a test descriptor.

    """
    TEST_CHRC_UUID = '00000014-0000-1000-8000-00805f9b34fb'

    def __init__(self, bus, index, service,data_process):
        Characteristic.__init__(
                self, bus, index,
                self.TEST_CHRC_UUID,
                ['read', 'notify'],
                service)
        self.data_process = data_process
        self.data_process.set_notify_characteristic_data(self)
        self.value = []
        self.battery_lvl = 100
        self.notifying = False
        # GLib.timeout_add(5000, self.drain_battery)
        self.add_descriptor(NotifyDescriptor(bus, 0, self))
        self.add_descriptor(
                CharacteristicUserDescriptionDescriptor(bus, 1, self))
    def notify_battery_level(self):
        if not self.notifying:
            return
        self.PropertiesChanged(
                GATT_CHRC_IFACE,
                { 'Value': dbus.ByteArray(b'11223344')}, [])

    def drain_battery(self):
        if not self.notifying:
            return True
        if self.battery_lvl > 0:
            self.battery_lvl -= 2
            if self.battery_lvl < 0:
                self.battery_lvl = 0
        print('Battery Level drained: ' + repr(self.battery_lvl))
        self.notify_battery_level()
        return True

    def ReadValue(self, options):
        print('TestCharacteristic Read: ' + repr(self.value))
        return self.value

    def WriteValue(self, value, options):
        print('TestCharacteristic Write: ' + repr(value))
        self.value = value

    def StartNotify(self):
        if self.notifying:
            print('Already notifying, nothing to do')
            return

        self.notifying = True
        print('Notifying')
        # self.notify_battery_level()

    def StopNotify(self):
        if not self.notifying:
            print('Not notifying, nothing to do')
            return
        print('stop notification')
        self.notifying = False
    def notify_update(self, value):
        if not self.notifying:
            return
        self.PropertiesChanged(
                GATT_CHRC_IFACE,
                { 'Value': dbus.ByteArray(value) }, [])

class NotifyDescriptor(Descriptor):
    """
    Dummy test descriptor. Returns a static value.

    """
    TEST_DESC_UUID = '12345678-1234-5678-1234-56789abcdef2'

    def __init__(self, bus, index, characteristic):
        Descriptor.__init__(
                self, bus, index,
                self.TEST_DESC_UUID,
                ['read', 'write'],
                characteristic)

    def ReadValue(self, options):
        return [
                dbus.Byte('T'), dbus.Byte('e'), dbus.Byte('s'), dbus.Byte('t')
        ]

class IndicateCharacteristic(Characteristic):
    """
    Dummy test characteristic. Allows writing arbitrary bytes to its value, and
    contains "extended properties", as well as a test descriptor.

    """
    TEST_CHRC_UUID = '00000015-0000-1000-8000-00805f9b34fb'

    def __init__(self, bus, index, service,data_process):
        Characteristic.__init__(
                self, bus, index,
                self.TEST_CHRC_UUID,
                ['indicate'],
                service)
        self.data_process = data_process
        self.value = []
        self.add_descriptor(IndicateDescriptor(bus, 0, self))
        self.add_descriptor(
                CharacteristicUserDescriptionDescriptor(bus, 1, self))

    def ReadValue(self, options):
        print('TestCharacteristic Read: ' + repr(self.value))
        return self.value

    def WriteValue(self, value, options):
        print('TestCharacteristic Write: ' + repr(value))
        self.value = value

class IndicateDescriptor(Descriptor):
    """
    Dummy test descriptor. Returns a static value.

    """
    TEST_DESC_UUID = '12345678-1234-5678-1234-56789abcdef2'

    def __init__(self, bus, index, characteristic):
        Descriptor.__init__(
                self, bus, index,
                self.TEST_DESC_UUID,
                ['read', 'write'],
                characteristic)

    def ReadValue(self, options):
        return [
                dbus.Byte('T'), dbus.Byte('e'), dbus.Byte('s'), dbus.Byte('t')
        ]

class CharacteristicUserDescriptionDescriptor(Descriptor):
    """
    Writable CUD descriptor.

    """
    CUD_UUID = '2901'

    def __init__(self, bus, index, characteristic):
        self.writable = 'writable-auxiliaries' in characteristic.flags
        self.value = array.array('B', b'This is a characteristic for testing')
        self.value = self.value.tolist()
        Descriptor.__init__(
                self, bus, index,
                self.CUD_UUID,
                ['read', 'write'],
                characteristic)

    def ReadValue(self, options):
        return self.value

    def WriteValue(self, value, options):
        if not self.writable:
            raise NotPermittedException()
        self.value = value

class Ble_manager:
    def __init__(self, Node, data_processor_t, is_custom=False):
        self.Node = Node
        self.log_t = Node.get_logger()  # 确保 log_t 在使用前初始化
        self.log_t.info("Initializing BLE Manager...")
        dbus.mainloop.glib.DBusGMainLoop(set_as_default=True)

        self.Node = Node
        self.log_t = Node.get_logger()
        self.bus = dbus.SystemBus()
        self.adapter = self.find_adapter(self.bus)
        if not self.adapter:
            self.log_t.warning('LEAdvertisingManager1 interface not found')
            raise 'LEAdvertisingManager1 interface not found'

        self.adapter2 = self.find_adapter2(self.bus)
        if not self.adapter:
            self.log_t.warning('GATT_MANAGER_IFACE interface not found')
            raise 'LEAdvertisingManager1 interface not found'
        
        # adapter_props = dbus.Interface(self.bus.get_object(BLUEZ_SERVICE_NAME, self.adapter),
        #                            "org.freedesktop.DBus.Properties")

        # adapter_props.Set("org.bluez.Adapter1", "Powered", dbus.Boolean(0))
        # time.sleep(0.2)
        # adapter_props.Set("org.bluez.Adapter1", "Powered", dbus.Boolean(1))

        self.ad_manager = None
        self.app = None
        self.data_processor = data_processor_t
        
        self.test_advertisement = None
        self.log_t.info("BLE Manager initialized successfully.")

    def set_discoverable(self, value, retries=5, delay=3):
        self.log_t.info(f"Setting discoverable to {value}. Retries left: {retries}")
        try:
            # 获取适配器的 D-Bus 对象和属性接口
            adapter_obj = self.bus.get_object(BLUEZ_SERVICE_NAME, self.adapter)
            adapter_props = dbus.Interface(adapter_obj, DBUS_PROP_IFACE)

            # 如果要使适配器可发现，一般也要确保它是 Powered 状态
            adapter_props.Set(ADAPTER_INTERFACE, "Powered", dbus.Boolean(True))

            # 设置 DiscoverableTimeout = 0 表示不限时可发现
            adapter_props.Set(ADAPTER_INTERFACE, "DiscoverableTimeout", dbus.UInt32(0))

            # 设置 Discoverable 属性
            adapter_props.Set(ADAPTER_INTERFACE, "Discoverable", dbus.Boolean(value))

            print(f"Bluetooth discoverable set to {value}")

        except Exception as e:
            self.log_t.error(f"Failed to set discoverable state due to: {e}")
            if retries > 0:
                self.log_t.info(f"Retrying in {delay} seconds...")
                time.sleep(delay)
                self.set_discoverable(value, retries - 1, delay)
            else:
                self.log_t.error("Failed to set discoverable state after retries")
        self.log_t.info(f"Discoverable set to {value}.")

    def register_ad_cb(self):
        self.log_t.info('Advertisement registered')

    def register_ad_error_cb(self,error):
        self.log_t.error(f'Failed to register advertisement: {error}')
        mainloop.quit()

    
    def register_app_cb(self):
        self.log_t.info('GATT application registered')


    def register_app_error_cb(self,error):
        self.log_t.error(f'Failed to register application: {error}')
        self.mainloop.quit()

    def find_adapter(self,bus):
        remote_om = dbus.Interface(bus.get_object(BLUEZ_SERVICE_NAME, '/'),
                                DBUS_OM_IFACE)
        objects = remote_om.GetManagedObjects()

        for o, props in objects.items():
            if LE_ADVERTISING_MANAGER_IFACE in props:
                return o
        return None
    
    def find_adapter2(self,bus):
        remote_om = dbus.Interface(bus.get_object(BLUEZ_SERVICE_NAME, '/'),
                               DBUS_OM_IFACE)
        objects = remote_om.GetManagedObjects()

        for o, props in objects.items():
            if GATT_MANAGER_IFACE in props.keys():
                return o
        return None
    

    def shutdown(self,timeout):
        self.log_t.info('Advertising for {} seconds...'.format(timeout))
        time.sleep(timeout)
        self.mainloop.quit()
    

    def ble_manager_start(self):
        self.mainloop = GLib.MainLoop()
        self.mainloop.run()

    def set_adapter_power(self, value, retries=5, delay=3):
        try:
            adapter_props = dbus.Interface(self.bus.get_object(BLUEZ_SERVICE_NAME, self.adapter),
                                        "org.freedesktop.DBus.Properties")
            adapter_props.Set("org.bluez.Adapter1", "Powered", dbus.Boolean(value))
            self.log_t.info(f"Adapter power set to:{value}")
        except dbus.exceptions.DBusException as e:
            self.log_t.warning(f"Failed to set adapter power due to: {e.get_dbus_message()} at {e.get_dbus_name()}")
            if retries > 0:
                self.log_t.info("Retrying in{delay}seconds...")
                time.sleep(delay)
                self.set_adapter_power(value, retries-1, delay)
            else:
                self.log_t.error(f"Failed to set adapter power after retries:{e}")

    def custom_advertisement_register(self, index=0, adv_type='peripheral',
                                      localName='TestAdvertisement',
                                      manuf_code=0xffff, manuf_data=[0x00, 0x01, 0x02, 0x03],
                                      serviceUUID='180D', is_include_tx_power=True):
        self.log_t.info("Registering custom advertisement...")
        self.set_adapter_power(False)
        time.sleep(2)
        self.set_adapter_power(True)
        time.sleep(2)

        # 注销之前的广告，防止崩溃
        try:
            if self.test_advertisement is not None:
                self.unregister_adv(self.test_advertisement)
        except dbus.exceptions.DBusException as e:
            self.log_t.error(f"unregister_adv 发生 D-Bus 异常: {e}")
        except Exception as e:
            self.log_t.error(f"unregister_adv 发生未知的异常: {e}")

        self.set_discoverable(True)
        time.sleep(1)
        # 注册新的蓝牙广告
        try:
            self.test_advertisement = TestAdvertisement(self.bus, 0, self.data_processor)
            self.ad_manager = dbus.Interface(self.bus.get_object(BLUEZ_SERVICE_NAME, self.adapter),
                                            LE_ADVERTISING_MANAGER_IFACE)
            self.ad_manager.RegisterAdvertisement(self.test_advertisement.get_path(), {},
                                                reply_handler=self.register_ad_cb,
                                                error_handler=self.register_ad_error_cb)
            time.sleep(2)
        except dbus.exceptions.DBusException as e:
            self.log_t.error(f"RegisterAdvertisement 发生 D-Bus 异常: {e}")
        except Exception as e:
            self.log_t.error(f"RegisterAdvertisement 发生未知的异常: {e}")
        self.log_t.info("Custom advertisement registered successfully.")

    def custom_app_register(self):    
        self.log_t.info("Registering custom GATT application.")
        self.app = Application(self.bus,self.data_processor)
        service_manager = dbus.Interface(self.bus.get_object(BLUEZ_SERVICE_NAME, self.adapter2),
                                GATT_MANAGER_IFACE)
        service_manager.RegisterApplication(self.app.get_path(), {},
                                        reply_handler=self.register_app_cb,
                                        error_handler=self.register_app_error_cb) 

    def custom_app_addsrv(self,service):
        self.app.add_service(service)


    def create_service(self,index = 0, UUID = '0000D459-0000-1000-8000-00805f9b34fb',primary = True):
        return  Service(self.bus, index, UUID, primary)

    def add_charct(self,service,charact):
        service.add_characteristic(charact)


    def unregister_adv(self,adv):
        self.ad_manager.UnregisterAdvertisement(adv)
        self.log_t.info('Advertisement unregistered')
        dbus.service.Object.remove_from_connection(adv)

    def update_advertisement(self, status):
        self.log_t.info(f"Updating advertisement. Status: {'Enabled' if status else 'Disabled'}")
        manuf_code = self.data_processor.manuf_code
        manuf_data = self.data_processor.manuf_data
        if status == True:
            if self.Node.andlink_reset == True:
                self.log_t.info('广播01, 收到Anlink的复位信号')
                manuf_data[-1] = 0x01
            else:
                self.log_t.info('广播03, 请求广播01但还未收到Anlink的复位信号')
                manuf_data[-1] = 0x03
        else:
            self.log_t.info('广播02')
            manuf_data[-1] = 0x02
        self.test_advertisement.update_advertisement(
            local_name=self.data_processor.ble_adv_localname,
            manufacturer_data={manuf_code: manuf_data},
        )
        self.log_t.info('Advertisement updated successfully.')

    def property_changed(self, interface, changed, invalidated, path):
        if interface == "org.bluez.Device1":
            for prop in ("Connected", "ServicesResolved"):
                if prop in changed:
                    status = changed[prop]
                    self.log_t.info(f"Bluez状态 at {path} connected: {status}")
                    if not status:  # 修复为布尔值判断
                        current_user_bind = self.Node.get_user_bind()
                        if current_user_bind == '1':
                            self.log_t.info(f"[蓝牙断开]绑定状态,user_bind=1, 关闭定时器,广播02")
                            self.Node.stop_ble_bind_timer()
                            self.update_advertisement(False)
                        else:
                            self.log_t.info(f"[蓝牙断开]未绑定状态,user_bind不为1, 让定时器判断绑定结果,广播01")
                            self.update_advertisement(True)
                    else:
                        self.save_connected_device_info(path)

    def start_bluetooth_listener(self):
        self.log_t.info("Starting Bluetooth listener...")
        bus = dbus.SystemBus()
        dbus.mainloop.glib.DBusGMainLoop(set_as_default=True)

        bus.add_signal_receiver(
            self.property_changed,
            bus_name="org.bluez",
            dbus_interface="org.freedesktop.DBus.Properties",
            signal_name="PropertiesChanged",
            path_keyword="path"
        )

        loop = GLib.MainLoop()
        self.log_t.info("Listening for Bluetooth connection changes...")
        loop.run()
        self.log_t.info("Bluetooth listener started successfully.")

    def execute_shell_command(self,command):
        try:
            result = subprocess.run(command, capture_output=True, text=True, check=True)
            return result.stdout
        except subprocess.CalledProcessError as e:
            return f"Error: {e.stderr}"

    def disconnect_wifi(self, interface="wlan0"):
        command = ['nmcli', 'd', 'disconnect', interface]
        result = self.execute_shell_command(command)
        self.log_t.info(f"Disconnected WiFi on interface: {interface}")
        return result

    def enable_network_interface(self, interface="wlan0"):
        command = ['ifconfig',interface,'up']
        result = self.execute_shell_command(command)
        self.log_t.info(f"Enabled network interface: {interface}")
        return result
    
    def disable_network_interface(self, interface="wlan0"):
        command = ['ifconfig',interface,'down']
        result = self.execute_shell_command(command)
        self.log_t.info(f"Disabled network interface: {interface}")
        return result

    def remove_andlinkSdkConfig_file(self, action: str = "remove_new"):
        if action == "remove_new":
            command = ['rm', '/etc/cmcc_robot/andlinkSdk.conf']
            self.execute_shell_command(command)
            self.log_t.info("remove andlinkSdk.conf")
        elif action == "remove_old":
            command = ['rm', '/etc/cmcc_robot/andlinkSdk_old.conf']
            self.execute_shell_command(command)
            self.log_t.info("remove andlinkSdk_old.conf")
        elif action == "remove_all":
            command = ['rm', '/etc/cmcc_robot/andlinkSdk.conf']
            self.execute_shell_command(command)
            self.log_t.info("remove andlinkSdk.conf")
            command = ['rm', '/etc/cmcc_robot/andlinkSdk_old.conf']
            self.execute_shell_command(command)
            self.log_t.info("remove andlinkSdk_old.conf")
        elif action == "move":
            source = '/etc/cmcc_robot/andlinkSdk.conf'
            destination = '/etc/cmcc_robot/andlinkSdk_old.conf'
            command = ['mv', source, destination]
            self.execute_shell_command(command)
            self.log_t.info(f"Renamed {source} to {destination}")
        else:
            raise ValueError(f"Invalid action: {action}. Supported actions are 'remove' or 'move'")

    def disconnect_bluetooth_device(self, device_path):
        """
        主动断开蓝牙设备连接
        :param device_path: 蓝牙设备的 D-Bus 路径
        """
        bus = dbus.SystemBus()
        device_proxy = bus.get_object(BLUEZ_SERVICE_NAME, device_path)
        device = dbus.Interface(device_proxy, DEVICE_INTERFACE)

        try:
            device.Disconnect()
            self.log_t.info(f"Device at {device_path} disconnected successfully.")
        except dbus.exceptions.DBusException as e:
            self.log_t.error(f"Failed to disconnect device at {device_path}: {e}")

    def save_connected_device_info(self, device_path, file_path='/etc/cmcc_robot/connected_device_info.txt'):
        self.log_t.info(f"Saving connected device info for path: {device_path}")
        """
        保存当前连接的设备信息到文件
        :param device_path: 蓝牙设备的 D-Bus 路径
        :param file_path: 保存设备信息的文件路径
        """
        try:
            bus = dbus.SystemBus()
            device_proxy = bus.get_object(BLUEZ_SERVICE_NAME, device_path)
            device_props = dbus.Interface(device_proxy, DBUS_PROP_IFACE)
            
            device_info = {
                "Address": device_props.Get(DEVICE_INTERFACE, "Address"),
                "Name": device_props.Get(DEVICE_INTERFACE, "Name"),
                # "RSSI": device_props.Get(DEVICE_INTERFACE, "RSSI"),
            }

            with open(file_path, 'w') as file:
                for key, value in device_info.items():
                    file.write(f"{key}: {value}\n")

            self.log_t.info(f"Device info saved to {file_path}: {device_info}")
        except Exception as e:
            self.log_t.error(f"Failed to save device info: {e}")
        self.log_t.info("Connected device info saved successfully.")

    def disconnect_all_devices(self):
        self.log_t.info("Attempting to disconnect all devices...")
        try:
            bus = dbus.SystemBus()
            remote_om = dbus.Interface(bus.get_object(BLUEZ_SERVICE_NAME, '/'), DBUS_OM_IFACE)
            objects = remote_om.GetManagedObjects()

            # 判断 objects 是否为空
            if not objects:
                self.log_t.info("No devices to disconnect.")
                return

            for path, interfaces in objects.items():
                if DEVICE_INTERFACE in interfaces:
                    device_proxy = bus.get_object(BLUEZ_SERVICE_NAME, path)
                    device = dbus.Interface(device_proxy, DEVICE_INTERFACE)
                    connected = interfaces[DEVICE_INTERFACE].get("Connected", False)
                    if connected:
                        try:
                            device.Disconnect()
                            self.log_t.info(f"Device at {path} disconnected successfully.")
                            # self.Node.stop_ble_bind_timer()
                        except dbus.exceptions.DBusException as e:
                            self.log_t.error(f"Failed to disconnect device at {path}: {e}")
        except Exception as e:
            self.log_t.error(f"Failed to disconnect all devices: {e}")
        self.log_t.info("All devices disconnected successfully.")
