#!/usr/bin/env python3
# coding: utf-8

import os
import fcntl
import subprocess
import re
import json
import uuid
import time
import sys
import argparse
import logging
import psutil
from logging import getLogger, basicConfig, DEBUG

# ==================== 配置参数 ====================
LOCK_PATH = "/etc/cmcc_robot/wifi_scan.lock"
JSON_PATH = "/etc/cmcc_robot/wifi_scan_result.json"
ANDLINK_CONF_PATH = "/etc/cmcc_robot/andlinkSdk.conf"
WPA_CONF_PATH = "/etc/wpa_supplicant/wpa_supplicant-wlan0.conf"
SCAN_INTERFACE = "wlan0"
SCAN_TIMEOUT = 60

# ==================== 日志配置 ====================
basicConfig(
    level=DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = getLogger(__name__)

# ==================== 命令行参数解析 ====================
def parse_arguments():
    parser = argparse.ArgumentParser(description='WiFi扫描和设置工具')
    subparsers = parser.add_subparsers(dest='command', help='可用命令', required=True)
    
    # scan命令：执行WiFi扫描
    scan_parser = subparsers.add_parser('scan', help='扫描WiFi热点')
    
    # set命令：设置WiFi名称和密码
    set_parser = subparsers.add_parser('set', help='设置WiFi名称和密码')
    set_parser.add_argument('ssid', type=str, help='WiFi名称')
    set_parser.add_argument('password', type=str, help='WiFi密码')
    
    return parser.parse_args()

# ==================== 锁文件管理 ====================
def acquire_lock() -> bool:
    try:
        lock_file = open(LOCK_PATH, 'a+')
        fcntl.flock(lock_file.fileno(), fcntl.LOCK_EX)
        logger.info(f"✅ 已获取锁文件：{LOCK_PATH}")
        return True
    except Exception as e:
        logger.error(f"❌ 获取锁文件失败：{str(e)}")
        return False

def release_lock():
    try:
        lock_file = open(LOCK_PATH, 'a+')
        fcntl.flock(lock_file.fileno(), fcntl.LOCK_UN)
        logger.info(f"✅ 已释放锁文件：{LOCK_PATH}")
    except Exception as e:
        logger.error(f"❌ 释放锁文件失败：{str(e)}")

# ==================== 无线接口检查 ====================
def check_interface(interface: str) -> bool:
    if not os.path.exists(f"/sys/class/net/{interface}"):
        logger.error(f"❌ 接口{interface}不存在")
        return False
    
    try:
        with open(f"/sys/class/net/{interface}/operstate", 'r') as f:
            state = f.read().strip()
            if state != "up":
                logger.error(f"❌ 接口{interface}处于关闭状态")
                return False
    except Exception as e:
        logger.error(f"❌ 检查接口状态失败：{str(e)}")
        return False
    
    logger.info(f"✅ 接口{interface}状态正常（开启）")
    return True

# ==================== WiFi扫描 ====================
def scan_wifi(interface: str) -> str:
    try:
        logger.info(f"🔍 开始扫描WiFi（接口：{interface}，命令：iwlist {interface} scan）...")
        output = subprocess.check_output(
            ["iwlist", interface, "scan"],
            stderr=subprocess.STDOUT,
            timeout=SCAN_TIMEOUT
        ).decode(errors='ignore')
        logger.info("✅ WiFi扫描完成")
        return output
    except subprocess.TimeoutExpired:
        logger.error(f"❌ 扫描超时（超过{SCAN_TIMEOUT}秒）")
        return ""
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ 扫描失败（返回码：{e.returncode}）：{e.output.decode(errors='ignore')}")
        return ""
    except Exception as e:
        logger.error(f"❌ 扫描失败：{str(e)}")
        return ""

# ==================== 热点解析 ====================
def parse_scan_result(scan_output: str) -> list:
    wifi_list = []
    cell_entries = re.split(r'\n\s*(?=Cell \d+ - Address:)', scan_output)
    logger.debug(f"找到 {len(cell_entries)} 个热点条目")
    
    for index, entry in enumerate(cell_entries):
        if not entry.strip():
            continue
            
        entry_header = entry.replace('\n', ' ')[:100]
        logger.debug(f"\n===== 解析热点 #{index+1} =====\n{entry_header}...")
        
        # 1. 提取SSID
        ssid = None
        ssid_match = re.search(r'ESSID:\s*"([^"]*)"', entry)
        if ssid_match:
            ssid = ssid_match.group(1).strip()
        else:
            ssid_match = re.search(r'ESSID:\s*([^\n]+?)\s*\n', entry)
            if ssid_match:
                ssid = ssid_match.group(1).strip()
        
        # 2. 过滤无效SSID
        if not ssid or ssid.strip() == "" or ssid.lower() == "off/any":
            logger.debug(f"❌ 无效SSID（值：{repr(ssid)}），跳过")
            continue
        logger.debug(f"✅ 提取到有效SSID：{ssid}")
        
        # 3. 提取信号强度
        signal_strength = None
        signal_match = re.search(r'Signal level\s*[=:]\s*(-?\d+)\s*dBm', entry)
        if signal_match:
            try:
                signal_strength = int(signal_match.group(1))
                logger.debug(f"信号强度：{signal_strength} dBm")
            except ValueError:
                logger.debug(f"无效信号强度（值：{signal_match.group(1)}），跳过")
                continue
        else:
            logger.debug("未找到信号强度，跳过")
            continue
        
        # 4. 检查加密状态
        encryption_match = re.search(r'Encryption key:\s*(\w+)', entry)
        if not (encryption_match and encryption_match.group(1).lower() == "on"):
            logger.debug("加密未开启，跳过")
            continue
        
        # 5. 检查WPA2+CCMP加密
        wpa2_match = re.search(r'(IEEE 802\.11i/WPA2|WPA2 Version \d)', entry, re.IGNORECASE)
        ccmp_match = re.search(r'CCMP', entry, re.IGNORECASE)
        if not (wpa2_match and ccmp_match):
            logger.debug("非WPA2+CCMP加密，跳过")
            continue
        
        # 6. 添加符合条件的热点
        wifi_list.append({
            "wifiName": ssid,
            "signalStrength": signal_strength,
            "encryptState": 1
        })
        logger.info(f"✅ 成功识别热点：{ssid}（信号：{signal_strength} dBm）")
    
    logger.info(f"📊 解析完成：找到{len(wifi_list)}个符合条件的WPA2+CCMP加密热点")
    return wifi_list

# ==================== JSON生成与保存 ====================
def get_device_id() -> str:
    return "unknown_device"

def generate_json(wifi_list: list) -> dict:
    return {
        "deviceId": get_device_id(),
        "domain": "DEVICE_PROPERTIES",
        "event": "wifi_list_query",
        "eventId": str(uuid.uuid4()),
        "seq": str(int(time.time())),
        "body": {
            "queryStatus": 1,
            "wifiList": wifi_list
        }
    }

def save_json(json_data: dict):
    try:
        os.makedirs(os.path.dirname(JSON_PATH), exist_ok=True)
        with open(JSON_PATH, 'w') as f:
            json.dump(json_data, f, indent=4, ensure_ascii=False)
        logger.info(f"💾 JSON文件已保存：{JSON_PATH}")
    except Exception as e:
        logger.error(f"❌ 保存JSON文件失败：{str(e)}")

# ==================== WiFi设置功能 ====================
def set_wifi_credentials(ssid: str, password: str):
    """更新andlinkSdk.conf中的WiFi名称和密码"""
    try:
        # 确保配置文件目录存在
        os.makedirs(os.path.dirname(ANDLINK_CONF_PATH), exist_ok=True)
        
        # 读取现有配置文件内容
        if os.path.exists(ANDLINK_CONF_PATH):
            with open(ANDLINK_CONF_PATH, 'r') as f:
                lines = f.readlines()
        else:
            logger.warning(f"⚠️ 配置文件不存在，将创建新文件：{ANDLINK_CONF_PATH}")
            lines = []
        
        # 更新或添加ssid和password行
        updated_lines = []
        ssid_found = False
        password_found = False
        
        for line in lines:
            stripped_line = line.strip()
            if stripped_line.startswith("ssid="):
                updated_lines.append(f"ssid={ssid}\n")
                ssid_found = True
            elif stripped_line.startswith("password="):
                updated_lines.append(f"password={password}\n")
                password_found = True
            else:
                updated_lines.append(line)
        
        # 如果文件中没有找到相关行，则添加
        if not ssid_found:
            updated_lines.append(f"ssid={ssid}\n")
        if not password_found:
            updated_lines.append(f"password={password}\n")
        
        # 保存更新后的文件
        with open(ANDLINK_CONF_PATH, 'w') as f:
            f.writelines(updated_lines)
        
        logger.info(f"✅ 成功更新WiFi设置：SSID={ssid}, Password={password}")
        logger.info(f"💾 配置文件已更新：{ANDLINK_CONF_PATH}")
        return True
        
    except Exception as e:
        logger.error(f"❌ 更新WiFi设置失败：{str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False

# ==================== WiFi连接辅助功能 ====================
def is_connected_to_ssid(interface: str, target_ssid: str) -> bool:
    """检查无线接口是否已连接到指定SSID"""
    try:
        # 执行iw命令获取接口状态
        cmd = f"iw dev {interface} link"
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)
        
        if result.returncode != 0:
            logger.error(f"❌ 检查连接状态失败：{result.stderr}")
            return False
        
        output = result.stdout.lower()
        
        # 检查是否连接
        if "not connected" in output:
            logger.info(f"📡 接口 {interface} 未连接任何网络")
            return False
        
        # 查找SSID信息
        ssid_match = re.search(r'ssid:\s*(.+)', output, re.IGNORECASE)
        if ssid_match:
            current_ssid = ssid_match.group(1).strip()
            logger.info(f"📡 接口 {interface} 当前连接到的SSID: {current_ssid}")
            return current_ssid.lower() == target_ssid.lower()
        
        logger.warning(f"⚠️ 接口 {interface} 状态正常但未找到SSID信息")
        return False
        
    except Exception as e:
        logger.error(f"❌ 检查连接状态时出错：{str(e)}")
        return False

def kill_wpa_supplicant():
    """杀死所有wpa_supplicant进程"""
    try:
        # 查找所有匹配的进程
        killed = 0
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                # 检查是否为wpa_supplicant进程
                if proc.info['name'] == 'wpa_supplicant' or 'wpa_supplicant' in ' '.join(proc.info['cmdline']):
                    # 检查是否针对wlan0接口
                    if 'wlan0' in ' '.join(proc.info['cmdline']) and WPA_CONF_PATH in ' '.join(proc.info['cmdline']):
                        logger.info(f"🚫 杀死wpa_supplicant进程 (PID: {proc.info['pid']})")
                        psutil.Process(proc.info['pid']).terminate()
                        killed += 1
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                continue
        
        if killed > 0:
            logger.info(f"✅ 成功杀死 {killed} 个wpa_supplicant进程")
            # 等待进程完全终止
            time.sleep(2)
        else:
            logger.info("ℹ️ 未找到运行的wpa_supplicant进程")
            
        return True
    except Exception as e:
        logger.error(f"❌ 杀死wpa_supplicant进程失败：{str(e)}")
        return False

def write_wpa_conf_file(ssid: str, password: str) -> bool:
    """生成wpa_supplicant配置文件"""
    try:
        # 确保配置文件目录存在
        os.makedirs(os.path.dirname(WPA_CONF_PATH), exist_ok=True)
        
        # 创建配置文件内容
        conf_content = f"""# Auto-generated wpa_supplicant configuration
ctrl_interface=/var/run/wpa_supplicant
ap_scan=1
update_config=1
country=CN

network={{
    ssid="{ssid}"
    psk="{password}"
    key_mgmt=WPA-PSK
    scan_ssid=1
}}
"""
        # 写入文件
        with open(WPA_CONF_PATH, 'w') as f:
            f.write(conf_content)
        
        logger.info(f"✅ 成功生成wpa_supplicant配置文件")
        logger.debug(f"配置文件内容:\n{conf_content}")
        return True
        
    except Exception as e:
        logger.error(f"❌ 生成wpa_supplicant配置文件失败：{str(e)}")
        return False

def start_wpa_supplicant():
    """启动wpa_supplicant服务"""
    try:
        cmd = f"wpa_supplicant -i {SCAN_INTERFACE} -B -c {WPA_CONF_PATH}"
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        
        if result.returncode == 0:
            logger.info("✅ wpa_supplicant启动成功")
            return True
        else:
            logger.error(f"❌ wpa_supplicant启动失败：{result.stderr}")
            return False
    except Exception as e:
        logger.error(f"❌ 启动wpa_supplicant时出错：{str(e)}")
        return False

def reconnect_wifi():
    """触发WiFi重新连接"""
    try:
        # 先尝试reconfigure命令
        cmd = f"wpa_cli -i {SCAN_INTERFACE} reconfigure"
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        
        if result.returncode != 0:
            logger.warning(f"⚠️ wpa_cli reconfigure失败：{result.stderr}")
        
        # 再发送reconnect命令
        time.sleep(1)
        cmd = f"wpa_cli -i {SCAN_INTERFACE} reconnect"
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        
        if result.returncode == 0:
            logger.info("✅ 已触发WiFi重新连接")
            return True
        else:
            logger.error(f"❌ WiFi重新连接失败：{result.stderr}")
            return False
    except Exception as e:
        logger.error(f"❌ 重新连接WiFi时出错：{str(e)}")
        return False

def connect_to_wifi(ssid: str, password: str):
    """连接到指定WiFi网络"""
    try:
        # 1. 检查是否已经连接到目标WiFi
        logger.info(f"🔍 检查当前WiFi连接状态...")
        if is_connected_to_ssid(SCAN_INTERFACE, ssid):
            logger.info(f"✅ 已连接到目标WiFi: {ssid}")
            return True
        
        # 2. 杀死现有的wpa_supplicant进程
        logger.info("🚫 停止现有wpa_supplicant进程...")
        if not kill_wpa_supplicant():
            logger.warning("⚠️ 停止现有wpa_supplicant失败，尝试继续")
        
        # 3. 生成新的wpa_supplicant配置文件
        logger.info(f"📝 生成新的wpa_supplicant配置文件...")
        if not write_wpa_conf_file(ssid, password):
            logger.error("❌ 生成配置文件失败，无法连接WiFi")
            return False
        
        # 4. 启动新的wpa_supplicant实例
        logger.info("🚀 启动新的wpa_supplicant进程...")
        if not start_wpa_supplicant():
            logger.error("❌ 启动wpa_supplicant失败，无法连接WiFi")
            return False
        
        # 5. 触发重新连接
        time.sleep(2)  # 等待服务初始化
        logger.info("🔁 触发WiFi重新连接...")
        if not reconnect_wifi():
            logger.warning("⚠️ 重新连接命令执行失败，但wpa_supplicant已启动")
        
        logger.info(f"🔄 正在尝试连接到WiFi: {ssid}...")
        logger.info("ℹ️ 连接过程可能需要10-30秒，请稍后检查连接状态")
        return True
        
    except Exception as e:
        logger.error(f"❌ 连接WiFi时出错：{str(e)}")
        return False

# ==================== 主函数 ====================
def main():
    args = parse_arguments()
    
    if args.command == "scan":
        # 扫描模式
        if not acquire_lock():
            sys.exit(1)
        
        try:
            if not check_interface(SCAN_INTERFACE):
                sys.exit(1)
            
            scan_output = scan_wifi(SCAN_INTERFACE)
            if not scan_output:
                logger.error("❌ 扫描结果为空，保存空JSON")
                save_json(generate_json([]))
                sys.exit(1)
            
            # 保存扫描输出到临时文件（便于调试）
            debug_path = "/tmp/wifi_scan_output.txt"
            with open(debug_path, "w") as f:
                f.write(scan_output)
            logger.debug(f"扫描输出已保存到：{debug_path}")
            
            # 解析扫描结果
            wifi_list = parse_scan_result(scan_output)
            # 生成并保存JSON
            json_data = generate_json(wifi_list)
            save_json(json_data)
            
        except Exception as e:
            logger.error(f"❌ 主函数执行异常：{str(e)}")
            import traceback
            logger.error(traceback.format_exc())
        finally:
            release_lock()
    
    elif args.command == "set":
        # 设置模式
        logger.info(f"⚙️ 开始设置WiFi：SSID={args.ssid}, Password={args.password}")
        
        # 1. 更新andlinkSdk.conf文件
        if set_wifi_credentials(args.ssid, args.password):
            # 2. 连接到新的WiFi
            connect_to_wifi(args.ssid, args.password)
        
        logger.info("✅ WiFi设置和连接操作完成")

if __name__ == "__main__":
    main()
