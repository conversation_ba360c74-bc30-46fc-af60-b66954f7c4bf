import configparser
import os

class DeviceConfig:
    def __init__(self, filepath):
        self.filepath = filepath

        if os.path.exists(filepath):
            self.config = configparser.ConfigParser()
            self._load_config()

    def _load_config(self):
        """Private method to read and parse the config file."""
        clean_lines = []
        with open(self.filepath, 'r') as file:
            for line in file:
                # Strip comments and whitespace
                line = line.split('#')[0].strip()
                if line:
                    clean_lines.append(line)

        # Create a fake section header for configparser
        config_string = '[DEFAULT]\n' + '\n'.join(clean_lines)
        self.config.read_string(config_string)

    def get_config_value(self, key):
        if not os.path.exists(self.filepath) :
            return None
        """Generic method to get a configuration value by key."""
        return self.config.get('DEFAULT', key, fallback=None)

# Example usage
if __name__ == "__main__":
    config_path = '/etc/cmcc_robot/andlinkSdk.conf'
    device_config = DeviceConfig(config_path)
    print("userBind:", device_config.get_config_value('userBind'))
    print("deviceId:", device_config.get_config_value('deviceId'))


