import os
import sys
import subprocess
import argparse
import signal
import time
from typing import Optional

class MPVController:
    """通过信号控制mpv进程的控制器（支持播放/暂停/继续/退出）"""
    def __init__(self, pid_file: str = "/tmp/mpv_audio.pid"):
        # 初始化PID文件（绝对路径，确保跨目录执行时正确）
        self.pid_file = os.path.abspath(pid_file)
        # 创建PID文件所在目录（若不存在）
        os.makedirs(os.path.dirname(self.pid_file), exist_ok=True)

    def _get_pid(self) -> Optional[int]:
        """从PID文件读取当前播放的mpv进程PID"""
        if not os.path.exists(self.pid_file):
            return None
        try:
            with open(self.pid_file, "r") as f:
                return int(f.read().strip())
        except (ValueError, IOError) as e:
            print(f"\033[93m警告：PID文件读取失败（{e}），将重新初始化\033[0m")
            self._cleanup_pid_file()
            return None

    def _is_process_running(self, pid: int) -> bool:
        """检查PID对应的进程是否存在（不影响进程运行）"""
        try:
            os.kill(pid, 0)  # 发送0号信号（无实际作用，仅检查进程存在性）
            return True
        except ProcessLookupError:
            return False  # 进程不存在
        except PermissionError:
            return False  # 无权限（视为不存在）

    def _cleanup_pid_file(self):
        """清理残留的PID文件"""
        if os.path.exists(self.pid_file):
            os.remove(self.pid_file)
            print(f"\033[93m警告：已清理残留的PID文件（{self.pid_file}）\033[0m")

    def play(self, url: str):
        """启动mpv播放指定URL（后台运行，记录PID）"""
        # 新增：终止之前的mpv进程（如果存在）
        current_pid = self._get_pid()
        if current_pid and self._is_process_running(current_pid):
            print(f"\033[93m提示：正在终止之前运行的mpv进程（PID: {current_pid}）\033[0m")
            self.quit()  # 调用quit方法终止进程并清理PID文件

        # 启动新的mpv进程（原来的代码）
        try:
            proc = subprocess.Popen(
                ["mpv", "--no-video", "--quiet", url],  # --no-video：仅音频；--quiet：无输出
                stdout=subprocess.DEVNULL,  # 重定向stdout到/dev/null（不打印日志）
                stderr=subprocess.DEVNULL,  # 重定向stderr到/dev/null（不打印错误）
                stdin=subprocess.DEVNULL,   # 重定向stdin到/dev/null（后台运行）
                start_new_session=True      # 创建新会话（避免父进程退出时终止子进程）
            )
            actual_pid = proc.pid  # 获取实际mpv进程的PID
        except Exception as e:
            print(f"\033[91m错误：启动mpv失败：{e}\033[0m")
            sys.exit(1)

        # 记录PID到文件（用于后续控制）
        try:
            with open(self.pid_file, "w") as f:
                f.write(str(actual_pid))
            print(f"\033[92m成功：mpv进程启动（PID: {actual_pid}）\033[0m")
            print(f"\033[92m正在播放：{url}\033[0m")
            print(f"\033[92mPID记录文件：{self.pid_file}\033[0m")
        except IOError as e:
            print(f"\033[91m错误：无法写入PID文件：{e}\033[0m")
            proc.kill()  # 启动失败，终止mpv进程
            sys.exit(1)

    def pause(self):
        """暂停当前mpv播放（发送SIGSTOP信号，对应kill -19）"""
        pid = self._get_pid()
        if not pid:
            print(f"\033[91m错误：无运行中的mpv进程（未找到PID文件：{self.pid_file}）\033[0m")
            sys.exit(1)
        if not self._is_process_running(pid):
            print(f"\033[91m错误：PID {pid} 对应的进程不存在（已清理PID文件）\033[0m")
            self._cleanup_pid_file()
            sys.exit(1)

        try:
            os.kill(pid, signal.SIGSTOP)
            print(f"\033[92m成功：mpv进程暂停（PID: {pid}）\033[0m")
        except Exception as e:
            print(f"\033[91m错误：暂停失败：{e}\033[0m")
            sys.exit(1)

    def resume(self):
        """继续当前mpv播放（发送SIGCONT信号，对应kill -18）"""
        pid = self._get_pid()
        if not pid:
            print(f"\033[91m错误：无运行中的mpv进程（未找到PID文件：{self.pid_file}）\033[0m")
            sys.exit(1)
        if not self._is_process_running(pid):
            print(f"\033[91m错误：PID {pid} 对应的进程不存在（已清理PID文件）\033[0m")
            self._cleanup_pid_file()
            sys.exit(1)

        try:
            os.kill(pid, signal.SIGCONT)
            print(f"\033[92m成功：mpv进程继续播放（PID: {pid}）\033[0m")
        except Exception as e:
            print(f"\033[91m错误：继续播放失败：{e}\033[0m")
            sys.exit(1)

    def quit(self):
        """退出当前mpv播放（发送SIGKILL信号，对应kill -9）"""
        pid = self._get_pid()
        if not pid:
            print(f"\033[91m错误：无运行中的mpv进程（未找到PID文件：{self.pid_file}）\033[0m")
            sys.exit(1)
        if not self._is_process_running(pid):
            print(f"\033[91m错误：PID {pid} 对应的进程不存在（已清理PID文件）\033[0m")
            self._cleanup_pid_file()
            sys.exit(1)

        try:
            os.kill(pid, signal.SIGKILL)  # 强制终止进程（无法被捕获）
            # 等待进程退出（最长3秒）
            for _ in range(30):
                if not self._is_process_running(pid):
                    break
                time.sleep(0.1)
            # 清理PID文件
            self._cleanup_pid_file()
            print(f"\033[92m成功：mpv进程终止（PID: {pid}）\033[0m")
        except Exception as e:
            print(f"\033[91m错误：退出失败：{e}\033[0m")
            sys.exit(1)


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="\033[94mmpv音频控制脚本\033[0m - 支持播放/暂停/继续/退出操作",
        epilog="\033[93m示例用法：\033[0m\n"
               "  1. 播放音频（自动终止旧进程）：\n"
               "     python3 mpv_control.py play http://example.com/audio.mp3\n"
               "  2. 暂停播放：\n"
               "     python3 mpv_control.py pause\n"
               "  3. 继续播放：\n"
               "     python3 mpv_control.py resume\n"
               "  4. 退出播放：\n"
               "     python3 mpv_control.py quit",
        formatter_class=argparse.RawTextHelpFormatter
    )
    parser.add_argument("action", choices=["play", "pause", "resume", "quit"], help="操作类型")
    parser.add_argument("url", nargs="?", help="播放的URL（仅`play`操作需要）")
    return parser.parse_args()


def main():
    args = parse_args()
    controller = MPVController()

    # 根据操作类型执行对应方法
    if args.action == "play":
        if not args.url:
            print(f"\033[91m错误：`play`操作必须提供URL参数（示例：python3 {sys.argv[0]} play http://example.com/audio.mp3）\033[0m")
            sys.exit(1)
        controller.play(args.url)
    elif args.action == "pause":
        controller.pause()
    elif args.action == "resume":
        controller.resume()
    elif args.action == "quit":
        controller.quit()
    else:
        print(f"\033[91m错误：无效的操作类型（请使用`--help`查看帮助）\033[0m")
        sys.exit(1)


if __name__ == "__main__":
    main()