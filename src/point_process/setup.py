from setuptools import setup
from setuptools import find_packages

package_name = 'point_process'

setup(
    name=package_name,
    version='0.0.0',
    packages=find_packages(),
    data_files=[
        ('share/ament_index/resource_index/packages',
            ['resource/' + package_name]),
        ('share/' + package_name, ['package.xml']),
    ],
    install_requires=['setuptools'],
    zip_safe=True,
    maintainer='qiming',
    maintainer_email='<EMAIL>',
    description='TODO: Package description',
    license='TODO: License declaration',
    tests_require=['pytest'],
    entry_points={
    'console_scripts': [
        'map_uploader_node = point_process.map_uploader:main',
        'report_point_node = point_process.report_point:main',
        'transform_point_node = point_process.transform_point:main',
    ],
    },
)