#!/usr/bin/env python3
import rclpy
from rclpy.node import Node
from std_msgs.msg import String
import json
import math
import os


def parse_map_display_txt(file_path):
    # 初始化解析结果
    pgm_w = 150
    pgm_h = 150
    resolution = 0.1
    list_origin = [-7.0, -7.0]

    # 打开文件并逐行读取
    with open(file_path, "r") as file:
        lines = file.readlines()

        # 解析 pgm_w 和 pgm_h
        if len(lines) > 0:
            # 第一个行包含 pgm_w 和 pgm_h
            dimensions = lines[0].strip().split()
            if len(dimensions) >= 2:
                pgm_w = int(dimensions[0])
                pgm_h = int(dimensions[1])

        # 解析 resolution
        if len(lines) > 1:
            resolution = float(lines[1].strip())

        # 解析 list_origin
        if len(lines) > 2:
            origin = lines[2].strip().split()
            if len(origin) >= 2:
                list_origin = [float(origin[0]), float(origin[1])]

        print("pgm_h: ", pgm_h)

    # 返回解析结果
    return {
        "pgm_w": pgm_w,
        "pgm_h": pgm_h,
        "resolution": resolution,
        "list_origin": list_origin,
    }


class PointTransformNode(Node):
    def __init__(self):
        super().__init__("point_transform_node")
        # 创建订阅者，订阅/point_transform话题
        self.subscription = self.create_subscription(
            String, "point_transform", self.listener_callback, 10
        )
        self.subscription  # 防止未使用变量警告

        # 创建发布者，发布转换后的坐标
        self.publisher_ = self.create_publisher(String, "point_transform_result", 10)

        self.config_file = "/etc/cmcc_robot/map/unitree_files/map_display.txt"
        os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
        # 如果文件不存在，则创建文件
        if not os.path.exists(self.config_file):
            with open(self.config_file, 'w') as f:
                pass  # 创建一个空文件
        self.update_config()

        self.get_logger().info("点位转换节点已启动")

    def update_config(self):
        config = parse_map_display_txt(self.config_file)
        self.list_origin = [float(x) for x in config["list_origin"][:2]]
        self.resolution = float(config["resolution"])
        self.pgm_h = config["pgm_h"]

        # 定义转换参数
        self.origin_x = self.list_origin[0]
        self.origin_y = self.list_origin[1]

        self.get_logger().info(
            f"读取配置: self.origin_x: {self.origin_x}, self.origin_y: {self.origin_y}, self.pgm_h: {self.pgm_h}, self.resolution: {self.resolution}"
        )

    def listener_callback(self, msg):
        try:
            self.update_config()

            # 解析接收到的JSON字符串
            input_data = json.loads(msg.data)

            # 提取像素坐标和角度
            pix_x = float(input_data.get("pix_x", 0.0))
            pix_y = float(input_data.get("pix_y", 0.0))
            angle_ori = float(input_data.get("angle", 0.0))
            # 打印输入的点位信息
            self.get_logger().info(
                f"收到原始数据: pix_x: {pix_x}, pix_y: {pix_y}, angle: {angle_ori}"
            )

            angle = -angle_ori
            if angle < -180:
                angle += 360

            # 进行坐标转换
            x = pix_x * self.resolution + self.origin_x
            y = (self.pgm_h - 1 - pix_y) * self.resolution + self.origin_y
            angle_rad = math.radians(angle)

            # 构建输出JSON
            output_data = {"x": x, "y": y, "angle": angle_rad}

            # 转换为JSON字符串并发布
            output_msg = String()
            output_msg.data = json.dumps(output_data)
            self.publisher_.publish(output_msg)

            self.get_logger().info(f"转换完成: {output_msg.data}")

        except json.JSONDecodeError:
            self.get_logger().error("接收到的消息不是有效的JSON格式")
        except Exception as e:
            self.get_logger().error(f"处理消息时出错: {str(e)}")


def main(args=None):
    rclpy.init(args=args)
    point_transform_node = PointTransformNode()
    rclpy.spin(point_transform_node)
    point_transform_node.destroy_node()
    rclpy.shutdown()


if __name__ == "__main__":
    main()
