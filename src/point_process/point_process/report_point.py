#!/usr/bin/env python3

# apt install -y ros-foxy-sensor-msgs-py

import rclpy
from rclpy.node import Node
from nav_msgs.msg import Path, Odometry
from sensor_msgs.msg import PointCloud2  # 添加PointCloud2消息类型
from geometry_msgs.msg import (
    PoseStamped,
    PoseWithCovarianceStamped,
    Point32,
)  # 添加Point32消息类型
from sensor_msgs_py import point_cloud2  # 添加point_cloud2处理库
from std_msgs.msg import String
import json
import numpy as np
from tf2_ros import TransformException
from tf2_ros.buffer import Buffer
from tf2_ros.transform_listener import TransformListener
import yaml
import os
import math


def load_pgm(file_path):
    """
    加载 PGM 文件并返回 NumPy 矩阵
    """
    with open(file_path, "r") as file:
        # 读取文件内容
        lines = file.readlines()

        # 跳过注释行（以#开头的行）
        lines = [line.strip() for line in lines if not line.startswith("#")]

        # 检查文件格式
        if lines[0] != "P2":
            raise ValueError("不支持的 PGM 文件格式！")

        # 解析宽度和高度
        width, height = map(int, lines[1].split())

        # 解析最大灰度值
        max_gray = int(lines[2])

        # 解析像素数据
        pixel_data = list(map(int, " ".join(lines[3:]).split()))

        # 将像素数据转换为 NumPy 矩阵
        image_matrix = np.array(pixel_data, dtype=np.uint8).reshape((height, width))

        return image_matrix


def parse_map_display_txt(file_path):
    # 初始化解析结果
    pgm_w = 150
    pgm_h = 150
    resolution = 0.1
    list_origin = [-7.0, -8.0]

    # 打开文件并逐行读取
    with open(file_path, "r") as file:
        lines = file.readlines()

        # 解析 pgm_w 和 pgm_h
        if len(lines) > 0:
            # 第一个行包含 pgm_w 和 pgm_h
            dimensions = lines[0].strip().split()
            if len(dimensions) >= 2:
                pgm_w = int(dimensions[0])
                pgm_h = int(dimensions[1])

        # 解析 resolution
        if len(lines) > 1:
            resolution = float(lines[1].strip())

        # 解析 list_origin
        if len(lines) > 2:
            origin = lines[2].strip().split()
            if len(origin) >= 2:
                list_origin = [float(origin[0]), float(origin[1])]

    # 返回解析结果
    return {
        "pgm_w": pgm_w,
        "pgm_h": pgm_h,
        "resolution": resolution,
        "list_origin": list_origin,
    }


class PointReportNode(Node):
    """点位上报节点，负责从指定话题获取数据并转换后上报"""

    def __init__(self):
        super().__init__("point_report")  # 设置节点名称为"point_report"

        # 从YAML文件加载配置
        self.config_file = "/etc/cmcc_robot/map/unitree_files/map_display.txt"
        self.pgm_file = "/etc/cmcc_robot/map/unitree_files/map_display.pgm"
        self.update_config()

        # 创建TF监听器用于坐标转换
        self.tf_buffer = Buffer()
        self.tf_listener = TransformListener(self.tf_buffer, self)

        # 创建发布者和订阅者
        self.point_pub = self.create_publisher(String, "/navigation_position", 10)
        self.path_pub = self.create_publisher(String, "/navigation_path", 10)

        self.path_sub = self.create_subscription(
            PointCloud2, "/uslam/navigation/global_path", self.path_callback, 10
        )

        self.odom_sub = self.create_subscription(
            Odometry, "/uslam/localization/odom", self.odom_callback_odometry, 10
        )
        self.get_logger().info("已订阅Odometry类型")

        self.last_process_time = self.get_clock().now()
        self.min_interval = 0.5  # 10Hz = 0.1秒间隔
        self.get_logger().info("self.min_interval = 0.5 ; 10Hz = 0.1秒间隔")

        self.get_logger().info("点位上报节点初始化完成")

    def update_config(self):
        try:
            config = parse_map_display_txt(self.config_file)
            self.list_origin = [float(x) for x in config["list_origin"][:2]]
            self.resolution = float(config["resolution"])
            self.pgm_h = config["pgm_h"]
            self.get_logger().info(
                f"配置加载成功 {self.config_file}: origin={self.list_origin}, resolution={self.resolution}, pgm_h={self.pgm_h}"
            )
        except Exception as e:
            self.get_logger().error(f"加载配置失败: {str(e)}")
            # 设置默认值
            self.list_origin = [-7.0, -8.0]
            self.resolution = 0.1
            self.pgm_h = 150

    def load_yaml(self, file_path):
        """加载YAML配置文件"""
        try:
            with open(file_path, "r") as f:
                return yaml.safe_load(f)
        except Exception as e:
            self.get_logger().error(f"读取YAML文件失败: {str(e)}")
            raise

    def path_callback(self, cloud_msg):
        """处理PointCloud2类型的全局路径消息"""
        try:
            # 解析PointCloud2消息获取路径点
            path_points = []
            # 将PointCloud2消息转换为点列表
            points = list(
                point_cloud2.read_points(cloud_msg, field_names=("x", "y", "z"))
            )

            for point in points:
                # 创建虚拟Pose对象用于坐标转换
                dummy_pose = PoseStamped().pose
                dummy_pose.position.x = point[0]
                dummy_pose.position.y = point[1]
                dummy_pose.position.z = point[2]

                # 假设朝向为0 (如果PointCloud2中不包含朝向信息)
                dummy_pose.orientation.x = 0.0
                dummy_pose.orientation.y = 0.0
                dummy_pose.orientation.z = 0.0
                dummy_pose.orientation.w = 1.0

                pix_x, pix_y, angle = self.transform_pose(dummy_pose)
                path_points.append(
                    {"path_x": pix_x, "path_y": pix_y, "path_angle": angle}
                )

            # 构造路径上报消息
            report_msg = {"path": path_points}

            # 创建String消息并发布
            msg = String()
            msg.data = json.dumps(report_msg)
            self.path_pub.publish(msg)

            self.get_logger().info(f"发布路径: {len(path_points)} 个点")
        except Exception as e:
            self.get_logger().error(f"处理路径消息时出错: {str(e)}")

    def odom_callback_odometry(self, odom_msg):
        """处理Odometry类型的定位消息"""
        # self.get_logger().debug("收到Odometry类型消息")

        current_time = self.get_clock().now()
        elapsed = (current_time - self.last_process_time).nanoseconds * 1e-9

        if elapsed >= self.min_interval:
            # 从Odometry消息中提取pose
            pose = odom_msg.pose.pose
            pix_x, pix_y, angle = self.transform_pose(pose)

            pix_x_int = int(pix_x)
            pix_y_int = int(pix_y)
            image = load_pgm(self.pgm_file)
            height, width = image.shape  # 获取图像尺寸

            pointStatus = 0

            # 检查坐标是否在图像范围内
            if 0 <= pix_x_int < width and 0 <= pix_y_int < height:
                if (
                    image[pix_y_int, pix_x_int] < 127
                ):  # 注意：numpy数组索引是[行,列]，即[y,x]
                    # 障碍物上，暂时不设置(-1, -1)
                    # pix_x = -1
                    # pix_y = -1
                    pointStatus = 2  # 在地图上的障碍物上，设置为 = 2
            else:
                # 地图外，暂时不设置(-1, -1)
                # pix_x = -1
                # pix_y = -1
                pointStatus = 1  # 在地图外，设置为未知区域 = 1
                self.get_logger().error(
                    f"坐标超出图像范围: ({pix_x_int}, {pix_y_int}), 图像尺寸: ({width}, {height})"
                )

            # 构造点位上报消息
            report_msg = {
                "x": pix_x,
                "y": pix_y,
                "angle": angle,
                "pointStatus": pointStatus,
            }

            # 创建String消息并发布
            msg = String()
            msg.data = json.dumps(report_msg)
            self.point_pub.publish(msg)

            self.get_logger().info(
                f"发布当前位置: ({pix_x}, {pix_y}, {angle}, {pointStatus})"
            )
            self.last_process_time = current_time

    def transform_pose(self, pose):
        """将ROS坐标系统中的pose转换为像素坐标"""

        self.update_config()

        # 提取位置信息
        env_x_ori = pose.position.x
        env_y_ori = pose.position.y

        # 应用原点偏移
        env_x = env_x_ori - self.list_origin[0]
        env_y = env_y_ori - self.list_origin[1]

        # 转换为像素坐标
        pix_x = env_x / self.resolution
        pix_y = env_y / self.resolution

        # y轴翻转(ROS的坐标系原点在左下角，图像坐标系原点在左上角)
        pix_y_flip = self.pgm_h - pix_y - 1

        # 转换朝向四元数为欧拉角
        quaternion = (
            pose.orientation.x,
            pose.orientation.y,
            pose.orientation.z,
            pose.orientation.w,
        )

        # 使用numpy实现四元数转欧拉角
        yaw = self.quaternion_to_yaw(quaternion)

        # 原公式：APP图片到宇树狗子如下；
        #             angle = -angle_ori
        #             if angle < -180:
        #                 angle += 360
        #             angle_rad = math.radians(angle)
        # 反向的公式
        # 从宇树的角度(-pi, pi)；转换为APP图片上的角度(0, 360)
        """
        APP: 90度   宇树：-90度
        APP: 200度  宇树: 160度
        APP: 0度    宇树: 0度
        """
        angle_deg = math.degrees(yaw)
        if angle_deg > 0:
            angle_deg -= 360
        angle = -angle_deg

        self.get_logger().info(
            f"pose.position.x: {pose.position.x}, pose.position.y: {pose.position.y}, yaw: {yaw}"
        )

        return pix_x, pix_y_flip, angle

    def quaternion_to_yaw(self, quaternion):
        """将四元数转换为yaw角(绕z轴旋转)"""
        x, y, z, w = quaternion

        # 计算yaw角
        siny_cosp = 2 * (w * z + x * y)
        cosy_cosp = 1 - 2 * (y * y + z * z)
        yaw = np.arctan2(siny_cosp, cosy_cosp)

        return yaw


def main(args=None):
    rclpy.init(args=args)
    node = PointReportNode()

    try:
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    finally:
        node.destroy_node()
        rclpy.shutdown()


if __name__ == "__main__":
    main()
