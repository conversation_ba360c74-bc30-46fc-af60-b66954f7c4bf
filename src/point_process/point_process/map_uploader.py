#!/usr/bin/env python3

import json
import os
import shutil
import subprocess
import threading
import time
import zipfile
import requests
import hashlib

import rclpy
from rclpy.node import Node
from std_msgs.msg import String
from sensor_msgs.msg import PointCloud2
from nav_msgs.msg import OccupancyGrid
from tf_transformations import euler_from_quaternion

map_uploader_version = 11.0

class MapSaverAndUploader(Node):
    def __init__(self):
        super().__init__("map_saver_uploader")

        # 初始化参数
        self.declare_parameter("map_save_root", "/etc/cmcc_robot/map/grid_map_2d/")
        self.declare_parameter("upload_interval", 0.5)  # 上传间隔默认0.5秒
        self.declare_parameter(
            "unitree_files_root", "/etc/cmcc_robot/map/unitree_files/"
        )
        self.declare_parameter("grid_map_2d_root", "/etc/cmcc_robot/map/grid_map_2d/")

        self.map_save_root = self.get_parameter("map_save_root").value
        self.upload_interval = self.get_parameter("upload_interval").value
        self.unitree_files_root = os.path.expanduser(
            self.get_parameter("unitree_files_root").value
        )
        self.grid_map_2d_root = self.get_parameter("grid_map_2d_root").value

        # 添加创建路径
        if not os.path.exists(self.unitree_files_root):
            os.makedirs(os.path.dirname(self.unitree_files_root), exist_ok=True)

        # 用于存储下载的地图文件
        self.map_file_sub = None  # 订阅者对象，用于接收宇树发布的文件
        self.command_pub = None  # 发布者对象，用于发送命令

        # 用于切换地图
        self.map_file_switch_pub = self.create_publisher(
            PointCloud2, "/uslam/map_file_sub", 100
        )

        # 状态变量
        self.current_map_id = ""
        self.upload_url = ""
        self.is_uploading = False

        # 订阅建图控制话题
        self.mapping_control_sub = self.create_subscription(
            String, "/mapping_control", self.mapping_control_callback, 10
        )

        self.map_file_sub = self.create_subscription(
            PointCloud2,
            "/uslam/map_file_pub",
            self.map_file_callback,
            100,
        )

        # 创建发布者，用于向/uslam/client_command发送指令
        self.command_pub = self.create_publisher(String, "/uslam/client_command", 10)

        # 2D栅格地图保存
        self.map_subscription = self.create_subscription(
            OccupancyGrid, "/uslam/grid_map", self.map_callback, 10
        )

        # 创建定时器替代线程
        self.timer = self.create_timer(self.upload_interval, self.upload_callback)

        self.get_logger().info("Map Saver and Uploader initialized")

    def convert_to_pgm_p5(self, input_path, output_path):
        """将PGM文件转换为P5格式（二进制）"""
        with open(input_path, "rb") as f:
            # 读取文件头
            magic_number = f.readline().decode("ascii").strip()
            if magic_number not in ("P2", "P5"):
                raise ValueError(f"不支持的魔数: {magic_number}")

            # 跳过注释行
            line = f.readline().decode("ascii").strip()
            while line.startswith("#"):
                line = f.readline().decode("ascii").strip()

            # 读取宽高和最大灰度值
            width, height = map(int, line.split())
            max_val = int(f.readline().decode("ascii").strip())

            # 读取图像数据
            if magic_number == "P5":
                # 已经是P5格式，直接读取二进制数据
                pixels = f.read()
            else:
                # P2格式，读取文本数据并转换为整数
                data = f.read().decode("ascii")
                pixels = list(map(int, data.split()))

        # 写入P5格式文件
        with open(output_path, "wb") as f:
            f.write(b"P5\n")
            # f.write(f"#{input_path}\n".encode("ascii"))
            f.write(f"{width} {height}\n".encode("ascii"))
            f.write(f"{max_val}\n".encode("ascii"))

            # 写入二进制数据
            if max_val <= 255:
                # 8位灰度
                f.write(bytes(pixels))
            else:
                # 16位灰度，每个像素占两个字节，高字节在前
                for pixel in pixels:
                    f.write(bytes([(pixel >> 8) & 0xFF, pixel & 0xFF]))

    def process_pgm(self, input_path, output_path):
        """处理PGM文件，根据阈值修改灰度值"""
        output_path_tmp = output_path + ".tmp"
        try:
            with open(input_path, "r") as infile, open(output_path_tmp, "w") as outfile:
                # 复制文件头三行
                for _ in range(3):
                    header_line = infile.readline()
                    outfile.write(header_line)

                # 处理数据部分
                for line in infile:
                    processed_values = []
                    for value in line.strip().split():
                        try:
                            num = int(value)
                            # 根据阈值处理每个像素值
                            if num < 127:
                                processed_values.append("0")
                            elif num > 127:
                                processed_values.append("254")
                            else:
                                processed_values.append("205")
                        except ValueError:
                            # 非整数字符保持不变
                            processed_values.append(value)

                    # 将处理后的值写回文件
                    outfile.write(" ".join(processed_values) + "\n")

            print(f"process pgm tmp output: {output_path_tmp}")
        except FileNotFoundError:
            print(f"process pgm fail: not found {input_path}")
        except Exception as e:
            print(f"process pgm error: {e}")

        self.convert_to_pgm_p5(output_path_tmp, output_path)

    def map_callback(self, msg):
        """地图消息回调函数，处理并保存地图"""
        try:
            self.get_logger().info(
                f"Received map: {msg.info.width}x{msg.info.height} at {msg.info.resolution:.6f}m/pix"
            )

            # 确保保存目录存在
            os.makedirs(os.path.dirname(self.grid_map_2d_root), exist_ok=True)
            mapname = os.path.join(self.grid_map_2d_root, "grid_map")

            # 保存PGM文件
            pgm_path = f"{mapname}.pgm"
            self.save_pgm_map(msg, pgm_path)

            # 保存YAML元数据
            yaml_path = f"{mapname}.yaml"
            self.save_yaml_metadata(msg, yaml_path, pgm_path)

            self.get_logger().info(f"Grid Map Saved: {mapname}")
            self.saved_map = True

        except Exception as e:
            self.get_logger().error(f"Error saving map: {str(e)}")

    def save_pgm_map(self, map_msg, pgm_path):
        """将OccupancyGrid保存为PGM格式，使用二进制写入模式"""
        width = map_msg.info.width
        height = map_msg.info.height

        # 打开文件以二进制写入模式
        with open(pgm_path, "wb") as out:
            # 写入PGM文件头（以二进制形式）
            header = (
                f"P5\n"
                f"# CREATOR: map_saver.py {map_msg.info.resolution:.3f} m/pix\n"
                f"{width} {height}\n"
                f"255\n"
            ).encode("ascii")
            out.write(header)

            # 准备像素数据缓冲区
            pixel_data = bytearray()

            # 填充像素数据
            for y in range(height):
                for x in range(width):
                    # 计算线性索引（注意坐标系转换，与C++代码保持一致）
                    linear_index = x + (height - y - 1) * width

                    # 确保索引不越界
                    if linear_index >= len(map_msg.data):
                        pixel_value = 205  # 未知区域
                    else:
                        cell_value_origin = map_msg.data[linear_index]

                        # 将int8转换为uint8
                        cell_value = cell_value_origin & 0xFF

                        # 根据阈值设置像素值
                        if cell_value > 127:
                            pixel_value = 254  # 占据
                        elif cell_value < 127:
                            pixel_value = 0  # 空闲
                        else:
                            pixel_value = 205  # 未知

                    pixel_data.append(pixel_value)

            # 一次性写入所有像素数据
            out.write(pixel_data)

    def save_yaml_metadata(self, map_msg, yaml_path, pgm_path):
        """保存地图YAML元数据"""

        # 提取方向信息并转换为欧拉角
        orientation = map_msg.info.origin.orientation
        orientation_list = [orientation.x, orientation.y, orientation.z, orientation.w]
        _, _, yaw = euler_from_quaternion(orientation_list)

        # 写入YAML文件
        with open(yaml_path, "w") as f:
            f.write(f"image: {pgm_path}\n")
            f.write(f"resolution: {map_msg.info.resolution:.6f}\n")
            f.write(
                f"origin: [{map_msg.info.origin.position.x}, {map_msg.info.origin.position.y}, 0.00]\n"
            )
            f.write("negate: 0\n")
            f.write("occupied_thresh: 0.65\n")
            f.write("free_thresh: 0.196\n")

    def mapping_control_callback(self, msg):
        """处理建图控制指令的回调函数"""
        try:
            msg_json = json.loads(msg.data)
            map_id = msg_json.get("mapId", "")
            action = msg_json.get("action", None)
            url = msg_json.get("url", "")

            self.get_logger().info(
                f"Received mapping control: action={action}, mapId={map_id}, url={url}"
            )

            if action is None:
                self.get_logger().warn(
                    f"Invalid mapping control message, action missing: action={action}, mapId={map_id}, url={url}"
                )
                return

            # 处理不同的建图操作
            if action == 0:
                # 开始建图
                self.start_mapping(map_id, url)
            elif action == 1:
                # 完成建图
                self.complete_mapping(map_id)
            elif action == 2:
                # 取消建图
                self.cancel_mapping(map_id)
            elif action == 3:
                # 删除地图
                self.delete_map(map_id)
            elif action == 4:
                # 切换地图
                self.switch_map(map_id)
            else:
                self.get_logger().warn(
                    f"Unknown action: action={action}, mapId={map_id}, url={url}"
                )
        except Exception as e:
            self.get_logger().error(f"Error processing mapping control: {str(e)}")

    def start_mapping(self, map_id, url):
        """开始建图过程"""
        if self.is_uploading:
            self.get_logger().info(
                f"Map already in uploading progress for mapId: {self.current_map_id}"
            )
            self.get_logger().info(f"Now uploading for mapId: {map_id}")

        # 初始化地图保存路径
        self.current_map_id = map_id
        self.upload_url = url
        self.map_save_dir = os.path.join(self.map_save_root, map_id)
        self.map_save_path = os.path.join(self.map_save_dir, map_id)

        # 创建保存目录
        self.create_directory(self.map_save_dir)

        # 上传开始
        self.is_uploading = True

        self.get_logger().info(f"Started uploading for mapId: {map_id}")

    def complete_mapping(self, map_id):
        """完成建图过程"""
        self.get_logger().warn(f"complete mapping for mapId: {map_id}")

        # 停止上传
        self.is_uploading = False
        self.send_set_map_id_command(map_id)

        # 共发送3次get_map_file，防止文件接收不到的情况
        self.send_get_map_file_command()
        time.sleep(self.upload_interval)
        self.send_get_map_file_command()
        time.sleep(self.upload_interval)
        self.send_get_map_file_command()

        self.get_logger().warn(f"finish get_map_file command for mapId: {map_id}")

        if self.current_map_id != map_id:
            self.get_logger().warn(f"complete mapping, not mapping for mapId: {map_id}")

        self.get_logger().info(f"Completed mapping for mapId: {map_id}")

    def cancel_mapping(self, map_id):
        """完成建图过程"""
        self.get_logger().warn(f"Cancel mapping for mapId: {map_id}")

        # 停止上传
        self.is_uploading = False
        self.current_map_downloading = False  # 停止下载
        self.get_logger().info(f"Cancel mapping for mapId: {map_id}")

        # 删除对应的map_id的文件夹以及其中的文件，如果文件夹不存在则不需要执行其他操作
        map_path = os.path.join(self.map_save_root, map_id)

        # 检查地图路径是否存在
        if not os.path.exists(map_path):
            self.get_logger().warn(f"Map directory does not exist: {map_path}")
            return

        try:
            # 遍历目录下的所有文件和子目录
            for root, dirs, files in os.walk(map_path, topdown=False):
                # 先删除所有文件
                for file in files:
                    file_path = os.path.join(root, file)
                    self.get_logger().warn(f"Deleting file: {file_path}")
                    os.remove(file_path)

                # 再删除所有子目录
                for dir in dirs:
                    dir_path = os.path.join(root, dir)
                    self.get_logger().warn(f"Deleting directory: {dir_path}")
                    os.rmdir(dir_path)

            # 最后删除主目录
            self.get_logger().warn(f"Deleting main map_id directory: {map_path}")
            os.rmdir(map_path)
            self.get_logger().warn(f"Successfully deleted map directory: {map_id}")

        except Exception as e:
            self.get_logger().error(f"Error deleting map directory {map_id}: {str(e)}")

    def delete_map(self, map_id):
        """删除地图"""
        self.get_logger().info(f"Delete map for mapId: {map_id}")
        # 删除这个目录下所有的文件和子目录，os.path.join(self.map_save_root, map_id)
        # 删除过程中，使用self.get_logger().warn打印删除的文件名称

        if map_id == "-1":
            # 删除self.map_save_root下的所有子目录
            self.get_logger().warn("Now delete all map_id.")
            try:
                # 获取所有子目录
                subdirs = [
                    d
                    for d in os.listdir(self.map_save_root)
                    if os.path.isdir(os.path.join(self.map_save_root, d))
                ]

                if not subdirs:
                    self.get_logger().warn("No subdirectories found to delete.")
                    return

                # 打印并删除子目录
                for subdir in subdirs:
                    subdir_path = os.path.join(self.map_save_root, subdir)
                    self.get_logger().warn(f"Deleting subdirectory: {subdir_path}")

                    # 递归删除子目录内容
                    for root, dirs, files in os.walk(subdir_path, topdown=False):
                        for file in files:
                            file_path = os.path.join(root, file)
                            self.get_logger().warn(f"Deleting file: {file_path}")
                            os.remove(file_path)

                        for dir in dirs:
                            dir_path = os.path.join(root, dir)
                            self.get_logger().warn(f"Deleting directory: {dir_path}")
                            os.rmdir(dir_path)

                    # 删除空的子目录
                    os.rmdir(subdir_path)
                    self.get_logger().warn(
                        f"Successfully deleted subdirectory: {subdir}"
                    )

                self.get_logger().info("Successfully deleted all subdirectories.")

            except Exception as e:
                self.get_logger().error(f"Error deleting all subdirectories: {str(e)}")
            return

        map_path = os.path.join(self.map_save_root, map_id)

        # 检查地图路径是否存在
        if not os.path.exists(map_path):
            self.get_logger().warn(f"Map directory does not exist: {map_path}")
            return

        try:
            # 遍历目录下的所有文件和子目录
            for root, dirs, files in os.walk(map_path, topdown=False):
                # 先删除所有文件
                for file in files:
                    file_path = os.path.join(root, file)
                    self.get_logger().warn(f"Deleting file: {file_path}")
                    os.remove(file_path)

                # 再删除所有子目录
                for dir in dirs:
                    dir_path = os.path.join(root, dir)
                    self.get_logger().warn(f"Deleting directory: {dir_path}")
                    os.rmdir(dir_path)

            # 最后删除主目录
            self.get_logger().warn(f"Deleting main map_id directory: {map_path}")
            os.rmdir(map_path)
            self.get_logger().warn(f"Successfully deleted map: {map_id}")

        except Exception as e:
            self.get_logger().error(f"Error deleting map {map_id}: {str(e)}")

    def switch_map(self, map_id):
        """
        切换地图功能实现
        - 检查目标地图目录是否存在
        - 遍历指定文件列表并发送到 ROS 话题
        - 处理文件不存在的情况并给出警告
        """
        if map_id == "":
            self.get_logger().warn("地图目录为空: switch map map_id='' ")
            return
        self.get_logger().warn(f"switch map map_id={map_id} ")

        self.is_uploading = False
        self.get_logger().info(f"Completed mapping for mapId: {self.current_map_id}")

        # 初始化切换地图的相关路径
        self.switch_map_dir = os.path.join(self.map_save_root, map_id)

        # 检查目标地图目录是否存在
        if not os.path.exists(self.switch_map_dir):
            self.get_logger().error(f"地图目录不存在: {self.switch_map_dir}")
            return

        # 定义需要发送的文件列表
        file_list = [
            "-1_-1.pcd",
            "map_display.pgm",
            "map_display.txt",
            "map_id.txt",
            "map_index.txt",
            "map_original.pcd",
            "map.pcd",
            "map.pgm",
            "map.txt",
        ]

        self.get_logger().info(
            f"开始切换地图: {map_id}, 准备发送 {len(file_list)} 个文件"
        )

        # 遍历文件列表并发送每个文件到指定话题
        for file_name in file_list:
            file_path = os.path.join(self.switch_map_dir, file_name)

            # 检查文件是否存在
            if not os.path.exists(file_path):
                self.get_logger().warning(f"文件不存在，跳过发送: {file_path}")
                continue

            try:
                # 创建 PointCloud2 消息
                msg = PointCloud2()
                msg.header.frame_id = file_name  # 设置文件名作为 frame_id

                # 以二进制方式读取文件内容
                with open(file_path, "rb") as f:
                    file_data = f.read()
                    msg.data = list(file_data)  # 转换为列表形式

                # 发送消息到话题
                self.map_file_switch_pub.publish(msg)
                self.get_logger().info(f"成功发送文件到话题: {file_name}")

            except Exception as e:
                self.get_logger().error(f"发送文件 {file_name} 时出错: {str(e)}")

        # 然后，更新本地unitree_root下的文件。
        # (改为拷贝到本地文件夹)

        # 确保目标目录存在
        if not os.path.exists(self.unitree_files_root):
            os.makedirs(self.unitree_files_root, exist_ok=True)
            self.get_logger().info(
                f"创建目标目录(switch_map): {self.unitree_files_root}"
            )

        # 遍历切换地图目录下的所有文件
        for item in os.listdir(self.switch_map_dir):
            # 只拷贝file_list中存在的文件
            if item not in file_list:
                continue
            item_path = os.path.join(self.switch_map_dir, item)

            # 只处理文件，跳过子目录
            if os.path.isfile(item_path):
                target_path = os.path.join(self.unitree_files_root, item)

                # 拷贝文件（覆盖同名文件）
                try:
                    shutil.copy2(item_path, target_path)
                    self.get_logger().info(
                        f"成功拷贝文件: {item} -> {self.unitree_files_root}"
                    )
                except Exception as e:
                    self.get_logger().error(f"拷贝文件失败 {item}: {str(e)}")

        self.get_logger().info(f"完成地图文件拷贝到 {self.unitree_files_root}")
        self.get_logger().info(f"地图切换完成: {map_id}")

    def upload_callback(self):
        """使用ROS定时器触发的上传回调函数"""
        if self.is_uploading:
            try:
                # 保存地图
                self.save_map()

                # 打包并上传
                zip_path = f"{self.map_save_path}.zip"
                pgm_path = f"{self.map_save_path}.pgm"
                yaml_path = f"{self.map_save_path}.txt"

                if os.path.exists(pgm_path) and os.path.exists(yaml_path):
                    success, message = self.upload_map_to_oss(
                        zip_path, [pgm_path, yaml_path]
                    )
                    if success:
                        self.get_logger().info("Map uploaded successfully")
                    else:
                        self.get_logger().error(f"Map upload failed: {message}")
                else:
                    self.get_logger().warn("Map files not found, skipping upload")
            except Exception as e:
                self.get_logger().error(f"Error in upload callback: {str(e)}")

    def save_map(self):
        """使用map_server保存地图"""
        try:
            self.get_logger().info(f"Saving map to: {self.map_save_path}")

            # 复制下载的文件到目标路径
            pgm_path = f"{self.map_save_path}.pgm"
            yaml_path = f"{self.map_save_path}.txt"

            # 检查是否存在display_map.pgm和display_map.txt
            display_pgm = self.grid_map_2d_root + "grid_map.pgm"
            display_yaml = self.grid_map_2d_root + "grid_map.yaml"

            if os.path.exists(display_pgm):
                shutil.copy2(display_pgm, pgm_path)

            if os.path.exists(display_yaml):
                shutil.copy2(display_yaml, yaml_path)

            if not os.path.exists(self.map_save_dir):
                os.makedirs(self.map_save_dir)

            self.get_logger().info(f"Map saved successfully to: {self.map_save_path}")
        except subprocess.CalledProcessError as e:
            self.get_logger().error(f"Failed to save map: {e.stderr}")
            raise
        except Exception as e:
            self.get_logger().error(f"Error saving map: {str(e)}")
            raise

    def send_set_map_id_command(self, map_id):
        cmd_msg = String()
        cmd_msg.data = f"common/set_map_id/{map_id}"
        self.command_pub.publish(cmd_msg)
        self.get_logger().info(
            f"Sent set_map_id command to /uslam/client_command with map_id: {map_id}"
        )
        time.sleep(0.5)

    def send_get_map_file_command(self):
        """向/uslam/client_command发送获取地图文件的指令"""
        if self.command_pub:
            cmd_msg = String()
            cmd_msg.data = "common/get_map_file"
            self.command_pub.publish(cmd_msg)
            self.get_logger().info("Sent get_map_file command to /uslam/client_command")
        else:
            self.get_logger().error("Command publisher not initialized")

    def map_file_callback(self, msg):
        """处理接收到的地图文件"""
        self.get_logger().info("downloading map_file_callback")

        try:
            # 从header.frame_id获取文件名
            file_name = msg.header.frame_id
            if not file_name:
                self.get_logger().warn("Received map file with empty frame_id")
                return

            # 构建保存路径到unitree_files_root
            file_path = os.path.join(self.unitree_files_root, file_name)

            # 确保目录存在
            os.makedirs(os.path.dirname(file_path), exist_ok=True)

            # 保存文件
            with open(file_path, "wb") as f:
                f.write(bytes(msg.data))

            # 计算文件的MD5哈希值
            md5_hash = hashlib.md5()
            with open(file_path, "rb") as f:
                # 分块读取以处理大文件
                for chunk in iter(lambda: f.read(4096), b""):
                    md5_hash.update(chunk)
            md5sum = md5_hash.hexdigest()

            # 使用warn级别日志记录文件名和MD5哈希值
            self.get_logger().warn(f"Saved map file to: {file_path}, MD5: {md5sum}")

            if file_name == "map_display.pgm" or file_name == "map_display.txt":
                # 打包并上传
                zip_path = f"{self.map_save_path}.zip"
                pgm_path = f"{self.map_save_path}.pgm"
                yaml_path = f"{self.map_save_path}.txt"

                zip_path = f"{self.map_save_path}.zip"
                unitree_final_pgm = f"{self.unitree_files_root}map_display.pgm"
                unitree_final_yaml = f"{self.unitree_files_root}map_display.txt"

                pgm_path_final = f"{self.map_save_path}.pgm.final"
                yaml_path_final = f"{self.map_save_path}.txt.final"

                if file_name == "map_display.pgm":
                    self.process_pgm(unitree_final_pgm, pgm_path)
                    # 创建文件pgm_path_final（空文件，作为标记）
                    with open(pgm_path_final, "w") as f1:
                        f1.write("Processed")

                    self.get_logger().warn(
                        "map_file_callback: Finally map_display.pgm saved"
                    )

                if file_name == "map_display.txt":
                    shutil.copy2(unitree_final_yaml, yaml_path)
                    # 创建文件yaml_path_final（空文件，作为标记）
                    with open(yaml_path_final, "w") as f2:
                        f2.write("Processed")
                    self.get_logger().warn(
                        "map_file_callback: Finally map_display.txt saved"
                    )

                # 添加判断pgm_path_final和yaml_path_final是否存在
                if (
                    os.path.exists(pgm_path_final)
                    and os.path.exists(yaml_path_final)
                    and os.path.exists(pgm_path)
                    and os.path.exists(yaml_path)
                ):
                    success, message = self.upload_map_to_oss(
                        zip_path, [pgm_path, yaml_path]
                    )
                    if success:
                        self.get_logger().warn(
                            "map_file_callback: Map uploaded successfully"
                        )
                    else:
                        self.get_logger().error(
                            f"map_file_callback: Map upload failed: {message}"
                        )

                if file_name == "map_display.pgm":
                    self.get_logger().warn("map_file_callback OK: map_display.pgm")
                if file_name == "map_display.txt":
                    self.get_logger().warn("map_file_callback OK: map_display.txt")
            else:
                # 同时，拷贝到id的文件夹下
                if os.path.exists(self.map_save_dir):
                    source_path = os.path.join(self.unitree_files_root, file_name)
                    target_path = os.path.join(self.map_save_dir, file_name)
                    # 只处理文件，忽略子文件夹
                    if os.path.isfile(source_path):
                        shutil.copy2(source_path, target_path)
                        self.get_logger().info(
                            f"mapfile_callback copied file: {file_name} to {self.map_save_dir}"
                        )
                else:
                    self.get_logger().error(
                        "mapfile_callback error: not os.path.exists(self.map_save_dir)"
                    )

        except Exception as e:
            self.get_logger().error(f"Error processing map file: {str(e)}")

    def upload_map_to_oss(self, zip_path, file_paths):
        """将地图文件打包并上传到OSS"""
        try:
            # 打包文件
            self.get_logger().info("Compressing map files to zip...")
            with zipfile.ZipFile(
                zip_path, "w", compression=zipfile.ZIP_DEFLATED
            ) as zipf:
                for file_path in file_paths:
                    if os.path.exists(file_path):
                        arcname = os.path.basename(file_path)
                        zipf.write(file_path, arcname=arcname)
                    else:
                        self.get_logger().warn(f"File not found: {file_path}")
                        return False, f"File not found: {file_path}"

            # 上传到OSS
            self.get_logger().info("Uploading map to OSS...")
            if not self.upload_url:
                return False, "Upload URL is not specified"

            # 确保URL以http或https开头
            if not self.upload_url.startswith(("http://", "https://")):
                self.upload_url = "http://" + self.upload_url

            # 发送PUT请求上传文件
            with open(zip_path, "rb") as f:
                response = requests.put(self.upload_url, data=f, timeout=30.0)

            if response.status_code == 200 or response.status_code == 201:
                self.get_logger().info("Map uploaded successfully to OSS")
                return True, "Success"
            else:
                self.get_logger().error(
                    f"OSS upload failed with status code: {response.status_code}"
                )
                return False, f"OSS upload failed: {response.status_code}"

        except zipfile.BadZipFile as e:
            self.get_logger().error(f"Error creating zip file: {str(e)}")
            return False, f"Zip file error: {str(e)}"
        except requests.exceptions.RequestException as e:
            self.get_logger().error(f"Error uploading to OSS: {str(e)}")
            return False, f"Upload error: {str(e)}"
        except Exception as e:
            self.get_logger().error(f"Unexpected error during upload: {str(e)}")
            return False, f"Unexpected error: {str(e)}"

    def create_directory(self, directory):
        """创建目录，确保权限正确"""
        try:
            if not os.path.exists(directory):
                os.makedirs(directory, mode=0o777, exist_ok=True)
                self.get_logger().info(f"Created directory: {directory}")
        except Exception as e:
            self.get_logger().error(f"Failed to create directory: {str(e)}")
            raise


def main(args=None):
    rclpy.init(args=args)
    node = MapSaverAndUploader()
    try:
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    finally:
        # 确保资源正确释放
        node.destroy_node()
        rclpy.shutdown()


if __name__ == "__main__":
    main()
