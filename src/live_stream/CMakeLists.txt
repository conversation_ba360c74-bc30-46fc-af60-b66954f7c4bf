cmake_minimum_required(VERSION 3.5)
project(live_stream)

# 安防特殊，预留自测时切换测试/线上环境入口。库上代码必须使用 pro
set(CMAKE_CUSTOM_ENV_TYPE "pro")
if("${CMAKE_BUILD_TYPE}" STREQUAL "Release")
  set(CMAKE_C_FLAGS_RELEASE "${CMAKE_C_FLAGS_RELEASE} -s")
  set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} -s")
endif()

# Default to C99
if(NOT CMAKE_C_STANDARD)
  set(CMAKE_C_STANDARD 99)
endif()

# Default to C++14
if(NOT CMAKE_CXX_STANDARD)
  set(CMAKE_CXX_STANDARD 14)
endif()

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# add_compile_options(-g)

# find dependencies
find_package(ament_cmake REQUIRED)
find_package(homi_speech_interface REQUIRED)
find_package(rclcpp REQUIRED)
find_package(std_msgs REQUIRED)
find_package(geometry_msgs REQUIRED)
find_package(PkgConfig)
find_package(ament_index_cpp)
pkg_search_module(GSTREAMER REQUIRED gstreamer-1.0)

get_filename_component(SRC_DIR ${CMAKE_CURRENT_SOURCE_DIR} DIRECTORY)
get_filename_component(MAIN_DIR ${SRC_DIR} DIRECTORY)
message(${MAIN_DIR})

include_directories(
 ${CMAKE_CURRENT_SOURCE_DIR}/src/include/
 ${CMAKE_CURRENT_SOURCE_DIR}/src/include/cjson/
 ${CMAKE_CURRENT_SOURCE_DIR}/src/include/mbedtls/ 
 ${MAIN_DIR}/include
 ${homi_speech_interface_INCLUDE_DIRS}
 ${GSTREAMER_INCLUDE_DIRS}
)

if("${CMAKE_CUSTOM_ENV_TYPE}" STREQUAL "dev")
link_directories(
  ${CMAKE_CURRENT_SOURCE_DIR}/src/lib/
)
endif()


if("${CMAKE_CUSTOM_ENV_TYPE}" STREQUAL "pro")
link_directories(
  ${CMAKE_CURRENT_SOURCE_DIR}/src/formallib/
)
endif()

add_executable(live_stream_node
  src/live_stream.cpp
  src/live_stream_node.cpp
  src/cmcc_ovd.cpp
  src/ini_file_manager.cpp
  src/live_stream_task_manager.cpp
  src/pcm_to_aac_encoder.cpp
)

#ament_target_dependencies(live_stream_node rclcpp std_msgs geometry_msgs)
ament_target_dependencies(${PROJECT_NAME}_node rclcpp ament_index_cpp homi_speech_interface std_msgs geometry_msgs)

target_compile_features(live_stream_node PUBLIC c_std_99 cxx_std_17) 

target_link_libraries(live_stream_node 
  ovdsdk
  faac
  ${GSTREAMER_LIBRARIES}
  mbedtls
  mbedcrypto
  mbedx509
  cjson
  zmq
  jsoncpp
  pthread
  stdc++
  m
  rt)
install(TARGETS live_stream_node DESTINATION lib/${PROJECT_NAME})

if("${CMAKE_CUSTOM_ENV_TYPE}" STREQUAL "dev")
install(PROGRAMS
  ${CMAKE_CURRENT_SOURCE_DIR}/src/DeviceConf.ini
  DESTINATION share/${PROJECT_NAME}/launch
)
endif()

if("${CMAKE_CUSTOM_ENV_TYPE}" STREQUAL "pro")
install(PROGRAMS
  ${CMAKE_CURRENT_SOURCE_DIR}/src/formallib/DeviceConf.ini
  DESTINATION share/${PROJECT_NAME}/launch
)
endif()

install(PROGRAMS
  ${CMAKE_CURRENT_SOURCE_DIR}/src/device.cfg
  DESTINATION share/${PROJECT_NAME}/launch
)

if(NOT CMAKE_CUSTOM_LIB_INSTALL)
install(PROGRAMS
  ${CMAKE_CURRENT_SOURCE_DIR}/src/lib/libcmcc_stream_get.so
  DESTINATION lib/${PROJECT_NAME}
)

install(PROGRAMS
  ${CMAKE_CURRENT_SOURCE_DIR}/src/lib/libaikit.so
  DESTINATION lib/${PROJECT_NAME}
)

install(PROGRAMS
  ${CMAKE_CURRENT_SOURCE_DIR}/src/lib/librknnrt.so
  DESTINATION lib/${PROJECT_NAME}
)

install(PROGRAMS
  ${CMAKE_CURRENT_SOURCE_DIR}/src/lib/librockiva.so
  DESTINATION lib/${PROJECT_NAME}
)

install(PROGRAMS
  ${CMAKE_CURRENT_SOURCE_DIR}/src/lib/libzmq.so
  DESTINATION lib/${PROJECT_NAME}
)

install(PROGRAMS
  ${CMAKE_CURRENT_SOURCE_DIR}/src/lib/libzmq.so.5
  DESTINATION lib/${PROJECT_NAME}
)


install(PROGRAMS
  ${CMAKE_CURRENT_SOURCE_DIR}/src/lib/libzmq.so.5.2.4
  DESTINATION lib/${PROJECT_NAME}
)

install(PROGRAMS
  ${CMAKE_CURRENT_SOURCE_DIR}/src/lib/libovdsdk.so
  DESTINATION lib/${PROJECT_NAME}
)
else()
install(PROGRAMS
  ${CMAKE_CURRENT_SOURCE_DIR}/src/formallib/libcmcc_stream_get.so
  ${CMAKE_CURRENT_SOURCE_DIR}/src/formallib/libaikit.so
  ${CMAKE_CURRENT_SOURCE_DIR}/src/formallib/librknnrt.so
  ${CMAKE_CURRENT_SOURCE_DIR}/src/formallib/librockiva.so
  ${CMAKE_CURRENT_SOURCE_DIR}/src/formallib/libzmq.so
  ${CMAKE_CURRENT_SOURCE_DIR}/src/formallib/libzmq.so.5
  ${CMAKE_CURRENT_SOURCE_DIR}/src/formallib/libzmq.so.5.2.4
  DESTINATION ${CMAKE_CUSTOM_LIB_INSTALL}
)
install(PROGRAMS
  ${CMAKE_CURRENT_SOURCE_DIR}/src/lib/libovdsdk.so
  DESTINATION ${CMAKE_CUSTOM_LIB_INSTALL}/dev
)
install(PROGRAMS
  ${CMAKE_CURRENT_SOURCE_DIR}/src/formallib/libovdsdk.so
  DESTINATION ${CMAKE_CUSTOM_LIB_INSTALL}/pro
)
endif()


if(BUILD_TESTING)
  find_package(ament_lint_auto REQUIRED)
  # the following line skips the linter which checks for copyrights
  # uncomment the line when a copyright and license is not present in all source files
  #set(ament_cmake_copyright_FOUND TRUE)
  # the following line skips cpplint (only works in a git repo)
  # uncomment the line when this package is not in a git repo
  #set(ament_cmake_cpplint_FOUND TRUE)
  ament_lint_auto_find_test_dependencies()
endif()
ament_package()