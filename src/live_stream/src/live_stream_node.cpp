#include <live_stream_node.h>
#include <ament_index_cpp/get_package_share_directory.hpp>
#include <jsoncpp/json/json.h>

//#define LIVE_STREAM_DEMO_MODE 1

LiveSteamNode::LiveSteamNode(): Node("live_stream_node") {
    // RCLCPP_INFO(this->get_logger(), "节点已启动：%s.", name.c_str());
    videoStreamClient_ = this->create_client<homi_speech_interface::srv::GetVideoStream>("/video_gst/get_video_service");
    videoStreamEndClient_ = this->create_client<homi_speech_interface::srv::EndVideoStream>("/video_gst/end_video_service");
    // m_pLiveStreamTaskManager = new LiveStreamTaskManager("/home/<USER>/robot-application/xiaoli_application_ros2/config/task.json");

    audioSuber_ = this->create_subscription<homi_speech_interface::msg::PCMStream>("/audio_recorder/pcm_origin", 10,
    std::bind(&LiveSteamNode::pcmCallback, this, std::placeholders::_1));

    taskSuber_ = this->create_subscription<homi_speech_interface::msg::LiveStreamTask>("/live_stream/live_stream_task", 10,
        std::bind(&LiveSteamNode::taskCallback, this, std::placeholders::_1));
    m_audioBuffer = new unsigned char[3200];   
    pubPlatMsgMonitor = this->create_publisher<homi_speech_interface::msg::SIGCEvent>("/homi_speech/sigc_event_topic", 10);

    // internetConnectStatusSuber_ = this->create_subscription<std_msgs::msg::String>(
    //     "internet_connect_status", 10,
    //     std::bind(&LiveSteamNode::updateInternetConnectStatus, this, std::placeholders::_1));
}

LiveSteamNode::~LiveSteamNode() {
    if(nullptr != m_pPcmToAacEncoder) {
        delete m_pPcmToAacEncoder;
        m_pPcmToAacEncoder = nullptr;
    }

    if(nullptr != m_pOvdController) {
        delete m_pOvdController;
        m_pOvdController = nullptr;
    }

    if(nullptr != m_audioBuffer){
        delete[] m_audioBuffer;
        m_audioBuffer = nullptr;
    }

}

void LiveSteamNode::init() {   
    static const std::string packageShareDir = ament_index_cpp::get_package_share_directory("live_stream");


    RCLCPP_INFO(this->get_logger(), "ini file is %s", packageShareDir.c_str());
    
    sleep(5);
    
    //等待网络连接
    while (true) {
        int ret = system("ping -c 1 -W 2 www.baidu.com > /dev/null 2>&1");
        if(0 == ret) {
            break;
        }
        RCLCPP_ERROR(this->get_logger(), "LiveSteamNode::liveStreaming is waiting network connect");
        sleep(2); // 每2秒检查一次
    }  
    
    RCLCPP_INFO(this->get_logger(), "ovd lib init start");
    m_pOvdController = new CmccOvd(packageShareDir);
    m_pOvdController->init();
    RCLCPP_INFO(this->get_logger(), "ovd lib init success");

    m_pPcmToAacEncoder = new PcmToAacEncoder();
    m_pPcmToAacEncoder->init();
    // pthread_t monitorThread;
    // pthread_create(&monitorThread, NULL, LiveSteamNode::monitorTaskStateWrapper, this);
}

void LiveSteamNode::unInit(){
    m_pPcmToAacEncoder->unInit();    
} 

void* LiveSteamNode::monitorTaskStateWrapper(void* arg) {
    LiveSteamNode* node = static_cast<LiveSteamNode*>(arg);
    return node->monitorTaskState(arg);
}


// 线程函数，每秒监视task状态
// void* LiveSteamNode::monitorTaskState(void* arg) {
//     bool currentTaskState = false;
  
//     while (true) {
//         if(nullptr != m_pLiveStreamTaskManager){
//             currentTaskState = m_pLiveStreamTaskManager->queryCurrentState();
//         }
        
//         if (currentTaskState != m_taskState) {
//             // printf("任务状态发生变化: %d -> %d\n", currentTaskState, m_taskState);
//             int ret = m_pOvdController->setStreamState(currentTaskState);
//             if(0 == ret){
//                 m_taskState = currentTaskState;
//             }
//         }
//         sleep(2); // 每2秒检查一次
//     }
//     return NULL;
// }

void* LiveSteamNode::monitorTaskState(void* arg) {
    bool taskState = true;
    int startSecond = m_iTaskStartSecond - 30;
    int endSecond = m_iTaskEndSecond;
  
    while (taskState) {
        time_t now = std::time(nullptr);
        tm *localTime = std::localtime(&now);
        int currentSecond = localTime->tm_hour * 3600 + localTime->tm_min * 60 + localTime->tm_sec;
        if(currentSecond >= endSecond) {
            taskState = false;
            m_bTaskMode = false;
            //关闭1
            if((!m_bPageMode) && (!m_bTaskMode)) {
                //stopHeadSensorData();
                m_pOvdController->stopPushHeadSensorData();
                m_iSensorState = 0;
            }
            // send task stop message to robdog_control
            sendMonitorTaskFinish();
        }
        RCLCPP_ERROR(this->get_logger(), "LiveSteamNode task running.");
        sleep(2); // 每2秒检查一次
    }

    return NULL;
}


void LiveSteamNode::liveStreaming() {
    RCLCPP_INFO(this->get_logger(), "stream function start");
    //1.等待服务端上线
    while (!videoStreamClient_->wait_for_service(std::chrono::seconds(1))) {
      //等待时检测rclcpp的状态
      if (!rclcpp::ok()) {
        RCLCPP_ERROR(this->get_logger(), "等待服务的过程中被打断...");
        return;
      }
      RCLCPP_INFO(this->get_logger(), "等待服务端上线中");
    }

    // 2.构造请求
    // auto requestHeadStream =
    //   std::make_shared<homi_speech_interface::srv::GetVideoStream_Request>();
    // requestHeadStream->resolution = "fhd";

    // 3.发送异步请求，然后等待返回，返回时调用回调函数(开始推流)
    // videoStreamClient_->async_send_request(
    //     requestHeadStream, std::bind(&LiveSteamNode::headStreamCallback, this,
    //                     std::placeholders::_1));

    RCLCPP_INFO(this->get_logger(), "video stream ready");
    
    pushHeadSensorData();
    
    RCLCPP_INFO(this->get_logger(), "push head sensor ready");
    
#ifdef LIVE_STREAM_DEMO_MODE
    m_pOvdController->pushHeadSensorData();    
#endif    

    RCLCPP_INFO(this->get_logger(), "LiveSteamNode::liveStreaming 1");   
    //////////////////////////////////////////////////////
    // auto requestJpegStream = 
    //     std::make_shared<homi_speech_interface::srv::GetVideoStream_Request>();    
    // requestJpegStream->resolution = "jpeg";

    // videoStreamClient_->async_send_request(
    //     requestJpegStream, std::bind(&LiveSteamNode::jpegStreamCallback, this,
    //                     std::placeholders::_1));



    const char* homeDir = getenv("HOME");
    char ovdLibPath[128] = {0};
    char aiAppPath[128] = {0};
    if (homeDir) {
        snprintf(ovdLibPath, sizeof(ovdLibPath), "%s/.ovd/lib", homeDir);
        snprintf(aiAppPath, sizeof(aiAppPath), "%s/.ovd/ai/ai_app/CMCC-RK3588S-BASE-0002/bin/cmcc_base", homeDir);
        RCLCPP_INFO(this->get_logger(),"ovd lib path: %s", ovdLibPath);
        RCLCPP_INFO(this->get_logger(),"ai app path: %s", aiAppPath);
    } else {
        RCLCPP_INFO(this->get_logger(),"can not get HOME path");
    }

    if (access(aiAppPath, F_OK) == 0) {
        RCLCPP_INFO(this->get_logger(), "vidoe algo start");
        auto requestJpegStream = 
            std::make_shared<homi_speech_interface::srv::GetVideoStream_Request>();    
        requestJpegStream->resolution = "jpeg";

        videoStreamClient_->async_send_request(requestJpegStream);
        usleep(250000);

        setenv("LD_LIBRARY_PATH", ovdLibPath, 1);
        int result = system(aiAppPath);

        if (result == -1) {
            RCLCPP_ERROR(this->get_logger(), "cmcc_base Failed to execute command");
            printf("cmcc_base Failed to execute command\n");
        } else {
            RCLCPP_INFO(this->get_logger(), "cmcc_base Command executed successfully");
            printf("cmcc_base Command executed successfully\n");
        }
    } else {
        printf("cmcc_base is not exist\n");
    }

    RCLCPP_INFO(this->get_logger(), "LiveSteamNode::liveStreaming 2");
}

void LiveSteamNode::stopStreaming() {
}

// void LiveSteamNode::result_callback_() {
//     m_pOvdController->pushData();
//     // rclcpp::Client<homi_speech_interface::srv::GetVideoStream>::SharedFuture
//     //   result_future) {
//     // auto response = result_future.get();
//     // String socket_path = response->socket_path;
//     // m_pOvdController->setSourcePath(socket_path);
//     // RCLCPP_INFO(this->get_logger(), "计算结果：%ld", response->sum);
// }

void LiveSteamNode::headStreamCallback(rclcpp::Client<homi_speech_interface::srv::GetVideoStream>::SharedFuture resultFuture) {
    
    RCLCPP_INFO(this->get_logger(), "headStreamCallback step1");
    
    auto response = resultFuture.get();
    std::string socketPath = response->socket_path;

    RCLCPP_INFO(this->get_logger(), "response->socket_path: %s", response->socket_path.c_str());
    //m_pOvdController->setSourcePath(socket_path);
    if(nullptr != m_pOvdController) {
        m_pOvdController->pushHeadSensorData();
    } else {
        RCLCPP_INFO(this->get_logger(), "object ovdController is null");
    }
}

void LiveSteamNode::jpegStreamCallback(rclcpp::Client<homi_speech_interface::srv::GetVideoStream>::SharedFuture resultFuture) {
    RCLCPP_INFO(this->get_logger(), "jpegStreamCallback step1");

    auto response = resultFuture.get();
    std::string socketPath = response->socket_path;

    // int result = system("/home/<USER>/test");

    //拉起算法
}


//task mode
//page mode
void LiveSteamNode::taskCallback(const homi_speech_interface::msg::LiveStreamTask::SharedPtr msg) {
    RCLCPP_ERROR(this->get_logger(), "receive task：%s", msg->task.c_str());
    unsigned int iSwitchCode = 0;

    std::string task = msg->task;
    if(strcmp(task.c_str(), "start") == 0) {
        RCLCPP_ERROR(this->get_logger(), "push stream start.");
        iSwitchCode = 1;
        m_bPageMode = true;        
    }

    if(strcmp(task.c_str(), "stop") == 0) {
        RCLCPP_ERROR(this->get_logger(), "push stream stop.");
        iSwitchCode = 2;
        m_bPageMode = false;  
    }

    if(strcmp(task.c_str(), "task") == 0) {
        RCLCPP_ERROR(this->get_logger(), "task start.");

        m_iTaskStartSecond = msg->start_second;
        m_iTaskEndSecond = msg->end_second;
        
        time_t now = std::time(nullptr);
        tm *localTime = std::localtime(&now);
        int currentSecond = localTime->tm_hour * 3600 + localTime->tm_min * 60 + localTime->tm_sec;

        if((currentSecond > msg->start_second -30) && (currentSecond < msg->end_second)) {
            iSwitchCode = 1;
            m_bTaskMode = true;
            pthread_t monitorThread;
            pthread_create(&monitorThread, NULL, LiveSteamNode::monitorTaskStateWrapper, this); 
        }      
    }


    if(strcmp(task.c_str(), "tripStart") == 0) {
        RCLCPP_ERROR(this->get_logger(), "tripstart");

    }


    if(strcmp(task.c_str(), "tripstop") == 0) {
        RCLCPP_ERROR(this->get_logger(), "tripstart");

    }

    //打开1
    if(1 == iSwitchCode) {
        if(0 == m_iSensorState) {
            //打开1
#ifndef LIVE_STREAM_DEMO_MODE
            //pushHeadSensorData();
            m_pOvdController->pushHeadSensorData();
#endif
            m_iSensorState = 1;
        } else if(1 == m_iSensorState) {
            //
        } else if(2 == m_iSensorState) {
            RCLCPP_ERROR(this->get_logger(), "Request to open camera 1, but camera 2 is already open.");
        }
    }

    //关闭1
    if(2 == iSwitchCode) {
        if(1 == m_iSensorState) {
            //关闭1
            if((!m_bPageMode) && (!m_bTaskMode)) {
#ifndef LIVE_STREAM_DEMO_MODE
                //stopHeadSensorData();
                m_pOvdController->stopPushHeadSensorData();
#endif
                m_iSensorState = 0;
            }
        }else if(0 == m_iSensorState) {
            //
        }else if(2 == m_iSensorState) {
            RCLCPP_ERROR(this->get_logger(), "Request to close camera 1, but camera 2 is already open.");
        }
    }

    //打开2
    if(3 == iSwitchCode) {
        if(0 == m_iSensorState) {
            //打开2
            m_iSensorState = 2;
        } else if(2 == m_iSensorState) {
            //
        } else if(1 == m_iSensorState) {
            RCLCPP_ERROR(this->get_logger(), "Request to open camera 2, but camera 1 is already open.");
        }
    }

    //关闭2
    if(4 == iSwitchCode) {
        if(2 == m_iSensorState) {
            //打开2
            m_iSensorState = 0;
        } else if(0 == m_iSensorState) {
            //
        } else if(1 == m_iSensorState) {
            RCLCPP_ERROR(this->get_logger(), "Request to close camera 2, but camera 1 is already open.");
        }
    }
}

void LiveSteamNode::pushHeadSensorData() {
    auto requestHeadStream =
      std::make_shared<homi_speech_interface::srv::GetVideoStream_Request>();
    requestHeadStream->resolution = "fhd";

    ///////////////////////////////////////logic1
    //发送异步请求，然后等待返回，返回时调用回调函数(开始推流)
    // videoStreamClient_->async_send_request(
    //     requestHeadStream, std::bind(&LiveSteamNode::headStreamCallback, this,
    //                     std::placeholders::_1));

    //////////////////////////////////////logic2
    videoStreamClient_->async_send_request(requestHeadStream);

    //usleep(250000);
    
    if(nullptr != m_pOvdController) {
        m_pOvdController->pushHeadSensorData();
    } else {
        RCLCPP_INFO(this->get_logger(), "object ovdController is null");
    }
}

void LiveSteamNode::stopHeadSensorData() {
    m_pOvdController->stopPushHeadSensorData();
    
    RCLCPP_INFO(this->get_logger(), "stopHeadSensor pipeline success");
    //end
    auto endHeadStream = std::make_shared<homi_speech_interface::srv::EndVideoStream_Request>();
    endHeadStream->resolution = "fhd";

    //发送异步请求，然后等待返回，返回时调用回调函数(开始推流)
    videoStreamEndClient_->async_send_request(endHeadStream);
}    

void LiveSteamNode::pushNeckSensorData() {

}

void LiveSteamNode::stopNeckSensorData() {

}


void LiveSteamNode::pcmCallback(const homi_speech_interface::msg::PCMStream::SharedPtr msg) {
    unsigned char* pAacData = nullptr;
    int ret = 0;
    int dataSize = msg->data.size();
    
    //printf("dataSize = %d, timestamp = %lu\n", dataSize, msg->ts);

    memcpy((void*)&(m_audioBuffer[audioBufferPointer]), (void*)&(msg->data[0]), dataSize);
    audioBufferPointer = audioBufferPointer + dataSize;
    if(audioBufferPointer >= 2048) {
        ret = m_pPcmToAacEncoder->encode((uint8_t*)m_audioBuffer, 2048, &pAacData);
// #ifndef LIVE_STREAM_DEMO_MODE    
//         if(0 != m_iSensorState) {
// #endif
        if(ret > 0){
            m_pOvdController->pushAudioData(pAacData, ret);
        }
// #ifndef LIVE_STREAM_DEMO_MODE 
//         }
// #endif      
        audioBufferPointer = audioBufferPointer - 2048;
        memcpy((void*)m_audioBuffer, (void*)&(m_audioBuffer[2048]), audioBufferPointer);
    }  
} 

/*
  generate a message to robdog controller
*/
void LiveSteamNode::sendMonitorTaskFinish()
{
/* json message generate and cover to string then publish:
{"deviceId":"1830004229212345670000041","domain":"DEVICE_ABILITY","event":"robot_move","eventId":"speech_1700601037149","requestId":"dec540419f9342908e637aa637c68627","seq":1744870387582,"response":false,"body":{"direction":{"x":1},"actionType":2}}
The seq is current timestamp,deviceId always robdogCenter,eventId always guard_done,requestId always guard_done
*/
    Json::Value root;
    root["deviceId"] = "robdogCenter";
    root["domain"] = "DEVICE_ABILITY";
    root["event"] = "guard_done";
    root["eventId"] = "guard_done";
    root["requestId"] = "guard_done";
    root["seq"] = 1;
    root["response"] = false;
    root["body"]["direction"]["x"] = 1;
    root["body"]["actionType"] = 2;

    Json::StreamWriterBuilder writer;
    writer["commentStyle"] = "None";
    writer["indentation"] = "";
    std::string jsonString = Json::writeString(writer, root);

    homi_speech_interface::msg::SIGCEvent plat_msg;
    plat_msg.event = jsonString;

    RCLCPP_INFO(this->get_logger(), "sendMonitorTaskFinish guard_done");
    pubPlatMsgMonitor->publish(plat_msg);
    return;
}

void LiveSteamNode::updateInternetConnectStatus(const std_msgs::msg::String::SharedPtr msg) {
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Received message: '%s'", msg->data.c_str());
    std::string strMsg = msg->data; // JSON字段
    Json::Value value;
    Json::Reader reader;
    if (!reader.parse(strMsg, value)) return; // 解析 JSON

    if (value.isMember("isInternetConnect"))
    {
        std::string isInternetConnect = value["isInternetConnect"].asString();
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "isInternetConnect: %s", isInternetConnect.c_str());

        bool new_status = (isInternetConnect == "true");
        if(m_bNetStatus != new_status) {
            m_bNetStatus = new_status;
        }
    }    
}