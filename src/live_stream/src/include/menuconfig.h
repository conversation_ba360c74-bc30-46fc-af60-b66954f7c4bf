/*
 * Automatically generated C config: don't edit
 */
/*
 * Version Number
 */
#ifndef OVDCFG_MODULE_SWITCH
#define OVDCFG_MODULE_SWITCH
#define CONFIG_SDK_VERSION "V1.54.0-beta.1"
#define CONFIG_SDK_SERIES_GENERAL 1
#define CONFIG_SDK_SERIES "general"

/*
 * Crypto options
 */
#define CONFIG_MODULE_mbedtls 1
#define CONFIG_MBEDTLS_VERSION_2_28_1 1
#define CONFIG_MBEDTLS_VERSION "2.28.1"

/*
 * Utils options
 */
#define CONFIG_MODULE_JSON_Schema 1
#define CONFIG_MODULE_timer 1

/*
 * Media options
 */
#define CONFIG_MAX_CHANNEL_COUNT 2
#define CONFIG_MODULE_srt 1
#define CONFIG_MODULE_srtclient 1
#define CONFIG_MODULE_ts 1
#define CONFIG_MODULE_cseg 1
#define CONFIG_MODULE_CardPlayback 1
#define CONFIG_MODULE_picupload 1
#define CONFIG_SDK_TALK_PROTOCOL_CCRTC 1
#define CONFIG_SDK_TALK_PROTOCOL "CCRTC"
#define CONFIG_SDK_REPLAY_PROTOCOL_SRT 1
#define CONFIG_SDK_REPLAY_PROTOCOL "SRT"
#define CONFIG_ENABLE_TIMESTAMP_CHECK 1

/*
 * Device manager options
 */
#define CONFIG_MODULE_netprobe 1
#define CONFIG_MODULE_speedtest 1
#define CONFIG_MODULE_softprobe 1
#define CONFIG_MODULE_log 1
#define CONFIG_LOG_SUPPORT_CONSOLE_OUT 1
#define CONFIG_MODULE_logBuf 1
#define CONFIG_MODULE_local_log 1
#define CONFIG_LOGFILE_SIZE_1MB 1
#define CONFIG_LOGFILE_SIZE 1048576
#define CONFIG_MODULE_softwdt 1

/*
 * Net Options
 */
#define CONFIG_MODULE_ble 1
#define CONFIG_MODULE_QRstringanalyse 1
#define CONFIG_MODULE_HTTPCLIENT 1
#define CONFIG_SDK_IPV6_MODE 1
#define CONFIG_ENCRYPTION_MODE 1

/*
 * Base options
 */
#define CONFIG_MODULE_serviceschedule 1
#define CONFIG_MODULE_libwebsocket 1
#define CONFIG_MODULE_DME 1
#define CONFIG_MODULE_cJSON 1
#define CONFIG_MODULE_OPENAPI 1
#define CONFIG_M64_MODE 1
#define CONFIG_USING_VIDEO_PROCESS 1

/*
 * Debug options
 */
#define CONFIG_BUILD_SHARDLIB 1
#define CONFIG_TARGET_SYSTEM_LINUX 1
#define CONFIG_TARGET_SYSTEM "linux"

/*
 * Development
 */
#define CONFIG_CUSTOM_NAME "general"
#define CONFIG_PLATFORM_NAME "x86"

#endif
