/**
 * 说明：ini配置文件读写
 * 1、支持;和#注释符号，支持行尾注释。
 * 2、支持带引号'或"成对匹配的字符串，提取时自动去引号。引号中可带其它引号或;#注释符。
 * 3、支持无section或空section(名称为空)。
 * 4、支持10、16、8进制数，0x开头为16进制数，0开头为8进制。
 * 5、支持section、key或:号前后带空格。
 * 6、支持\n、\r、\r\n或\n\r换行格式。
 * 7、不区分section、key大小写，但写入时以新串为准，并保持其大小写。
 * 8、新增数据时，若section存在则在该节最后一个有效数据后添加，否则在文件尾部添加。
 * 9、支持指定key所在整行删除，即删除该键值，包括注释。
 * 10、可自动跳过格式错误行，修改时仍然保留。
 * 11、修改时保留原注释：包括整行注释、行尾注释(包括前面空格)。
 * 12、修改时保留原空行。以上三点主要是尽量保留原格式。
 */
#include <ctype.h>
#include <stdlib.h>
#include <stdio.h>
#include <string.h>


#define SIZE_LINE		1024	//每行最大长度
#define SIZE_FILENAME	256		//文件名最大长度

#define MIN(x, y)		(x <= y) ? x : y

typedef enum _ELineType_ {
    LINE_IDLE,		//未处理行
	LINE_ERROR,		//错误行
	LINE_EMPTY,		//空白行或注释行
	LINE_SECTION,	//节定义行
	LINE_VALUE		//值定义行
} ELineType ;

class IniFileManager {
public:
	IniFileManager(const char* filename);
	~IniFileManager();

    //加载ini文件至内存
    int iniFileLoad(const char *filename);
    //释放ini文件所占资源
    void iniFileFree();
    //获取字符串，不带引号
    int iniGetString(const char *section, const char *key, char *value, int size, const char *defvalue);
    //获取整数值
    int iniGetInt(const char *section, const char *key, int defvalue);
    //获取浮点数
    double iniGetDouble(const char *section, const char *key, double defvalue);
    //设置字符串：若value为NULL，则删除该key所在行，包括注释
    int iniSetString(const char *section, const char *key, const char *value);
    //设置整数值：base取值10、16、8，分别表示10、16、8进制，缺省为10进制
    int iniSetInt(const char *section, const char *key, int value, int base);
    
private:
    //去除串首尾空格，原串被改写
    char *StrStrip(char *s);
    //不区分大小写比较字符串
    int StriCmp(const char *s1, const char *s2);
    //取一行
    //输入：数据区(指针及长度)
    //输出：行类型、有效内容串(去首尾空格)、注释首、注释尾、下一行首(行尾与下一行首间为换行符)
    //      有效内容位置为[buf, rem1)
    int GetLine(char *buf, int buflen, char *content, char **rem1, char **rem2, char **nextline);
    //取一节section
    //输入：节名称
    //输出：成功与否、节名称首、节名称尾、节内容首、节内容尾(含换行)、下一节首(节尾与下一节首间为空行或注释行)
    int FindSection(const char *section, char **sect1, char **sect2, char **cont1, char **cont2, char **nextsect);
    //从一行取键、值
    //输入：内容串(将被改写)
    //输出：键串、值串
    void GetKeyValue(char *content, char **key, char **value);
    //读取值原始串
    int iniGetValue(const char *section, const char *key, char *value, int maxlen, const char *defvalue);    
  
    
private:
    char m_fileName[SIZE_FILENAME] = {0};
    char* m_gBuffer = NULL;
    int m_gBufferLen = 0;    
};
