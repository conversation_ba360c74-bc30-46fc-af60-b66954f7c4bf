#include "rclcpp/rclcpp.hpp"
#include "homi_speech_interface/srv/get_video_stream.hpp"
#include "homi_speech_interface/srv/end_video_stream.hpp"
#include "homi_speech_interface/msg/live_stream_task.hpp"
#include "homi_speech_interface/msg/pcm_stream.hpp"
#include "homi_speech_interface/msg/sigc_event.hpp"
#include <std_msgs/msg/string.hpp>
#include <cstring>

#include <iostream>
#include <fstream>
#include "cmcc_ovd.h"
#include "live_stream_task_manager.h"
#include "pcm_to_aac_encoder.h"

#include <stdlib.h>
   
class LiveSteamNode : public rclcpp::Node {
public:
    LiveSteamNode();
    ~LiveSteamNode();

    //初始化，平台注册
    void init();
    void unInit();
    //推流
    void liveStreaming();
    void stopStreaming();
    void result_callback_();
    void taskCallback(const homi_speech_interface::msg::LiveStreamTask::SharedPtr msg);
    static void* monitorTaskStateWrapper(void* arg);
    void* monitorTaskState(void* arg);
    void pcmCallback(const homi_speech_interface::msg::PCMStream::SharedPtr msg);
    void headStreamCallback(rclcpp::Client<homi_speech_interface::srv::GetVideoStream>::SharedFuture resultFuture);
    void jpegStreamCallback(rclcpp::Client<homi_speech_interface::srv::GetVideoStream>::SharedFuture resultFuture);
    void updateInternetConnectStatus(const std_msgs::msg::String::SharedPtr msg);
    
    void pushHeadSensorData();   
    void stopHeadSensorData();   
    void pushNeckSensorData();   
    void stopNeckSensorData();

private:
    CmccOvd* m_pOvdController = nullptr;
    PcmToAacEncoder* m_pPcmToAacEncoder = nullptr;
    bool m_taskState = false;
    LiveStreamTaskManager* m_pLiveStreamTaskManager = nullptr;
    unsigned char* m_audioBuffer = nullptr;
    bool m_audioBufferFlag = false;

    int audioBufferPointer = 0;

    // 声明客户端
    rclcpp::Client<homi_speech_interface::srv::GetVideoStream>::SharedPtr videoStreamClient_;
    rclcpp::Client<homi_speech_interface::srv::EndVideoStream>::SharedPtr videoStreamEndClient_;
    rclcpp::Subscription<homi_speech_interface::msg::PCMStream>::SharedPtr audioSuber_;
    rclcpp::Subscription<homi_speech_interface::msg::LiveStreamTask>::SharedPtr taskSuber_;
    rclcpp::Publisher<homi_speech_interface::msg::SIGCEvent>::SharedPtr pubPlatMsgMonitor = nullptr;


    rclcpp::Subscription<std_msgs::msg::String>::SharedPtr internetConnectStatusSuber_;

    
	FILE* m_file = nullptr;
    int m_count = 0;

    unsigned int m_iSensorState = 0;
    unsigned int m_iSwitchCode = 0;

    bool m_bTaskMode = false;
    bool m_bPageMode = false;
    bool m_bNetStatus = false;
    int m_iTaskStartSecond = 0;
    int m_iTaskEndSecond = 0;

    void sendMonitorTaskFinish();
};
