#include <stdio.h>
#include <stdlib.h>
#include <string>
#include <string.h>
#include <sys/stat.h>
#include <cJSON.h>

#define MAX_TASK_COUNT 50

typedef enum {
	TASK_TYPE_EVERY_WEEK = 1,
	TASK_TYPE_ONCE,
	TASK_TYPE_MONTHLY,
	TASK_TYPE_MAX
} LiveStreamTaskType;

typedef struct {
	long long taskId;
	LiveStreamTaskType taskType; // 0:每周，1：单次，2：每月
	int year; // 年
	int month; // 月
	bool weekdays[7]; // 星期几，0：周一至周日
	int dayOfMonth; // 一个月中的第几天
	int startSecond; // 开始时间，秒
	int endSecond; // 结束时间，秒
} LiveStreamTaskInfo;


class LiveStreamTaskManager {
public:
	LiveStreamTaskManager(const char* localTaskFilepath);
	~LiveStreamTaskManager();
	bool queryCurrentState();
	void parseTask(const char* taskStr);
	
private:
	int parseLocalFile(const char* fileData, int dataSize, LiveStreamTaskInfo* taskInfoList);
	void addTask(LiveStreamTaskInfo* taskInfo);
	void updateTask(LiveStreamTaskInfo* taskInfo);
	void deleteTask(int taskId);
	int getFileSize(const char *filepath);
	int saveJsonToFile(cJSON* localTaskJson);	
	
private:
	char m_localTaskFilename[256];
	LiveStreamTaskInfo m_taskListMem[MAX_TASK_COUNT];
	cJSON* m_taskJson = nullptr;
};