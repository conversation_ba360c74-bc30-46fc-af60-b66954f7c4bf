#include <stdio.h>
#include <math.h>
extern "C"
{
#include <faac.h>
}

class PcmToAacEncoder {

public:
    int init();    
    int encode(const uint8_t* pcmData, int dataSize, unsigned char** ppAacData);
    void unInit();
    
private:
    faacEncHandle m_hEncoder;
    faacEncConfigurationPtr pConfiguration = nullptr;

    uint32_t m_nPCMBitSize = 16 /*= 16*/;
    unsigned long m_nInputSamples = 0 /*= 0*/;
    unsigned long m_nMaxOutputBytes = 0 /*= 0*/;
    unsigned long m_nMaxInputBytes = 0 /*= 0*/;
    unsigned char* m_AacBuffer = nullptr;
};