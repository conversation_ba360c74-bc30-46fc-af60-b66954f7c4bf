#include <iostream>
#include <semaphore.h>
#include <sys/time.h>
#include <sys/prctl.h>
#include <string.h>
#include <malloc.h>
#include "cJSON.h"
#include "OVD_Demo.h"
#include "OVD_OpenAPI.h"
#include "OVD_define.h"
#include "OVD_openapi_lock.h"
#include "menuconfig.h"
#include "ini_file_manager.h"
// #include "functional"
#include <gst/gst.h>
#include <sys/types.h>
#include <sys/stat.h>

#define OVD_MAX_JSON_REND_SIZE  (0x4000)
#define CONFIG_MODULE_CardPlayback 1

class CmccOvd {
public:
    CmccOvd(std::string cfgFileDir);
    ~CmccOvd();    
    //初始化，平台注册
    void init();
    //推流
    int pushHeadSensorData();
    int stopPushHeadSensorData();
    int pushNeckSensorData();
    int stopPushNeckSensorData();    
    //推音频数据
    int pushAudioData(unsigned char* aacData, int dataSize);

    int setStreamState(bool bStreamState);

private:
    int ovdInitDevCapV2();
    int ovdInitCapbility(OVDCapInfoV2_t *demo_cap);
    int ovdGetRebootInfo(ovd_reboot_param_t *rebootinfo);
    void *pthread_OVDDemoLogOutput(void *args);
    void ovdLogOutCallBack(const char* buff);
    int ovdInitLogConfInfo(); 
    void setCallBackFunList(OVD_CallBackFunList *pcallBackFunList);
    void prepareJsonParam(char *jsonParam);
    int ovdInitDeviceInfo();
    int ovdShowDeviceInfo();
    int ovdInitClientInfo();
    int ovdInitDeviceConfInfo();
    int ovdShowClientInfo();
    int ovdDeviceInit();

    static int OVD_GetOVDDeviceInfo(OVDDeviceInfo *deviceInfo);
    static int OVD_GetOVDConfigureInfo(char** output_ovdconfig, int *len);
    static int OVD_SetOVDConfigureInfo(char* in_ovdconfig,char* err_msg_info,int str_len);
    static int OVD_GetOVDChannelConfigureInfo(int channel, char** output_ovdconfig, int *len);
    static void OVD_OVCConnectStatus(int connectStatus);
    static int OVD_ReBootChannel(int channel);
    static int OVD_ReBootDevice(ovd_reboot_reason_e reason);
    static int OVD_GetGpsInfo(ovd_gps_info_t *gpsinfo);
    static OVD_int32 OVD_FirmwareBinUpgrade(OVD_int32 channel_id, OVD_Upgrade_cmd_e cmd, void* data, size_t size, void* user_data);
    static OVD_int32 OVD_FirmwareUpgradeStatusCb(OVD_int32 channel_id, OVD_UpgradeConfirmInfo_t *info);
    static int OVD_SetSDCardFormat();
    static ovd_ptz_cmd_code OVD_PTZCmd(int channel,OVCPTZControlCmd ptzcmd,int speed);
    static int OVD_GetPresetList(int channel,int *presetList,int array_num, int *count);
    static int OVD_ForceIFrame(int channel, OVDCodeStream code_stream);
    static int OVD_Snapshot(int channel,OVDImageInfo *imageInfo, int maxImageSize);
    //告警状态通知回调
    static int OVD_AlarmNotifyCalback(OVD_AlarmNotifyType_e type, const void* buf, const unsigned int len);
    static int OVD_SetMp3Url(int channel,const char *url,int repeat, int volume);
    static int OVD_KeepAwakenUtilExpired(int channel, int notAllowHibernate, int expired, OVDHibernateReason reason);
    static int OVD_ResetConfig();
    static OVD_int32 OVD_gettime(OVD_uint64 *out_time);
    /*
    由厂商实现设置系统时间戳函数，精确度为毫秒
    in: input_time  设置的时间戳， 单位为毫秒
    in: tolerance_value 设置的容忍值，单位为秒,即当前系统时间戳和预设置的时间相差小于容忍值，则不需要设置系统时间
    */
    static OVD_int32 OVD_settime(OVD_uint64 input_time, OVD_int32 tolerance_value);
    static int OVD_getsimpleovdinfo(SimpleOVDinfo *outinfo);
    static int OVD_GetDiskInfo(int *out_state,int *out_total, int *out_free);
    static int OVD_LogUploadAsync(char *trans_id, char *start, char* end,char* url);
    static OVD_int32 OVD_DMEAPI_callback_RecordSearch2(OVD_int32 channel, OVD_uint64 starttime, OVD_uint64 endtime, OVD_int32 page,
                OVD_int32 numInPage, OVD_DMERecordFileListPerPage *fileinpage);
    static void* OVD_DMEAPI_callback_RecordOpen(int channel);
    static int OVD_DMEAPI_callback_RecordSeek(void* ctx, long long timestamp);
    static int OVD_DMEAPI_callback_RecordReadFrame(void* ctx, OVD_FrameInfo *pframe_info);
    static int OVD_DMEAPI_callback_RecordSpeedReadFrame(void* ctx, ovd_playback_speed_times_e speed, OVD_FrameInfo *pframe_info);
    static int OVD_DMEAPI_callback_RecordClose(void *ctx);
    static int OVD_StopAlarm(OVDAlarmType alarmtype);
    static int OVD_EBOCmd(OVCEBOControlCmd cmd,void* data);
    static int OVD_GetDevRunningInfo(ovd_probe_devrunning_info_e info_e, void* data);
    static int OVD_ROBOTCmd(ovd_robot_content_t* cmd,void* data);
    static int OVD_LogNotifyCb(const OVD_char *log_path);

    static void OVD_PrintDeviceInfo();
    static int OVD_OVCGetRepeat(cJSON* switchentry,int repeat);
    static cJSON* OVD_GetOVDChannelConfigureInfoJson(int channel);
    static cJSON* GetAISentry(cJSON *channelentry,int i);
    static cJSON* GetAlarmSentry(cJSON *channelentry,int i);
    static int OVD_SetRebootInfo(ovd_reboot_param_t *rebootinfo);

    static long long  OVD_getutcMicroSec();
    static GstFlowReturn cb_call_sink_new_sample(GstElement *appsink, gpointer user_data);
    
    
private:
    static OVD_DEMO_Capinfo_t m_capInfoV2;
    // char* m_cfgFilePath = CFG_FILEPATH;
    // char* m_iniFilePath = INI_FILEPATH;
    IniFileManager* m_pIniFileManager = nullptr;
    IniFileManager* m_pCfgFileManager = nullptr;
    
    static OVDDeviceInfo m_deviceInfo;
    OVDLogParam m_logParam;
    OVDClientParam m_clientParam;
    static OVDConfigrationInfo m_configureInfo;
    OVD_CallBackFunList m_callBackFunList;

    char m_jsonParam[200];
    char m_bindId[17];
    int m_iSDKStarted;
    int m_iLogThreadStarted;    

    pthread_t m_logOutputPid;
    pthread_mutex_t m_logOutputMutex;    
    sem_t full;
    sem_t empty;

    static int m_iStatus;
    static int m_presetlist[256];
    static int m_keepAwakeTime;

    int Device_IMAGE_BUFF_SIZE;       //截图图片初始大小
    int Device_AV_BUFF_DURATION;	  //设备上电后，音视频内容最长缓存时间
    int Device_DEBUG_TS;              //是否将每一帧的音视频时间戳输出
    int Device_CSEG_DURATION;         //单个云存切片时长
    int Device_CSEG_MAXCOUNT;         //云存模块最多切片
    int Device_SNDDROPDELAY;          //底层流媒体srt连接缓存最大时延
    int run_daemon = 0;
    int videoFPS = 0;
    static int dutMTU;

    GstElement* m_HeadPipeline = nullptr;
    GstElement* m_NeckPipeline = nullptr;
    bool m_pushStartFlag = false;

    static unsigned long long m_FakeVideoTimeStamp;
    static unsigned long long m_FakeAudioTimeStamp;
    static bool m_bEndPushFlag;
};