#include "cmcc_ovd.h"

CmccOvd::CmccOvd(std::string cfgFileDir){
    
    std::string cfgFilePath = cfgFileDir + "/launch/device.cfg";
    std::string iniFilePath = cfgFileDir + "/launch/DeviceConf.ini";
    
    m_pCfgFileManager = new IniFileManager(cfgFilePath.c_str());
    if(NULL == m_pCfgFileManager) {
        OVD_Printf(OVD_LOGLEVEL_ERROR,"cfg file manager launch fail !\n");     
    }

    m_pIniFileManager = new IniFileManager(iniFilePath.c_str());
    if(NULL == m_pIniFileManager) {
        OVD_Printf(OVD_LOGLEVEL_ERROR,"ini file manager launch fail !\n");        
    }
}

CmccOvd::~CmccOvd(){
    //释放IniFileManager
    //m_pCfgFileManager       
} 

int CmccOvd::ovdInitDevCapV2() {
    char buffer[512];
	memset(&m_capInfoV2, 0, sizeof(OVD_DEMO_Capinfo_t));
    
    // if(NULL == m_iniFilePath) {
        //     OVD_Printf(OVD_LOGLEVEL_ERROR,"ini file is null!\n");
        //     return -1;
    // }
    
    m_capInfoV2.device_type = (ovd_device_type_e)m_pIniFileManager->iniGetInt("devCap", "device_type", 0);
    m_capInfoV2.base.stream_encryption_mode = (ovd_media_encrypt_type_e)m_pIniFileManager->iniGetInt("devCap","stream_encryption_mode",0);

    // 低功耗
    m_capInfoV2.lowpower.support_lowpower_mode = m_pIniFileManager->iniGetInt("devCap","enable_low",0);
    m_capInfoV2.lowpower.support_awaked = m_pIniFileManager->iniGetInt("devCap","support_awaked",0);
    m_capInfoV2.lowpower.support_awaked_switch = m_pIniFileManager->iniGetInt("devCap","support_awaked_switch",0);
    m_capInfoV2.lowpower.support_smart_mode = m_pIniFileManager->iniGetInt("devCap","smart_mode",0);

    // 视频能力集
    int VideoCap = m_pIniFileManager->iniGetInt("devCap","have_video_out",1);
    m_capInfoV2.video.support_multi_stream = m_pIniFileManager->iniGetInt("devCap","multi_stream",0);
    m_capInfoV2.video.support_static_scence_ABR = m_pIniFileManager->iniGetInt("devCap","support_static_scence_ABR",0);
    m_pIniFileManager->iniGetString("devCap","video_quality", buffer,sizeof(buffer),"sd,hd");
    if (strstr(buffer, "ld")) {
        m_capInfoV2.video.video_quality |= OVD_VIDEO_QUALITY_LD;
    }
    
    if (strstr(buffer, "sd")) {
        m_capInfoV2.video.video_quality |= OVD_VIDEO_QUALITY_SD;
    }
    
    if (strstr(buffer, "hd")) {
        m_capInfoV2.video.video_quality |= OVD_VIDEO_QUALITY_HD;
    }
    
    if (strstr(buffer, "fhd")) {
        m_capInfoV2.video.video_quality |= OVD_VIDEO_QUALITY_FHD;
    }

    m_pIniFileManager->iniGetString("devCap","video_formats_supportlists", buffer,sizeof(buffer),"h264");
    if (strstr(buffer, "h264")) {
        m_capInfoV2.video.video_codec |= OVD_VIDEO_CODEC_H264;
    }
    
    if (strstr(buffer, "h265")) {
        m_capInfoV2.video.video_codec |= OVD_VIDEO_CODEC_H265;
    }
    
    m_capInfoV2.video.support_set_video_formats = m_pIniFileManager->iniGetInt("devCap","support_set_video_formats",1);
    m_capInfoV2.video.support_set_bitrate = m_pIniFileManager->iniGetInt("devCap","support_set_bitrate",0);
    m_capInfoV2.video.support_set_framerate = m_pIniFileManager->iniGetInt("devCap","support_set_framerate",0);
    m_capInfoV2.video.support_set_gop = m_pIniFileManager->iniGetInt("devCap","support_set_gop",0);
    m_capInfoV2.video.support_set_AI_rules_mode = m_pIniFileManager->iniGetInt("devCap","support_set_AI_rules_mode",0);
    m_capInfoV2.video.support_osd_text = m_pIniFileManager->iniGetInt("devCap","osd_text",1);
    m_capInfoV2.video.support_osd_logo = m_pIniFileManager->iniGetInt("devCap","osd_logo",1);
    m_capInfoV2.video.support_aov = m_pIniFileManager->iniGetInt("devCap","support_aov",0);

    // 音频能力集
    m_capInfoV2.audio.support_audio_out = m_pIniFileManager->iniGetInt("devCap","have_audio_out",1);
    if(1==m_capInfoV2.audio.support_audio_out) {
        int AudioCap = 1;
    }
    
    m_capInfoV2.audio.support_voice_out = m_pIniFileManager->iniGetInt("devCap","have_voice_out",1);
    m_capInfoV2.audio.audiosamplerate = m_pIniFileManager->iniGetInt("devCap","audiosamplerate",1);
    m_capInfoV2.audio.support_voiceout_volume = m_pIniFileManager->iniGetInt("devCap", "have_voiceout_volume", 1);

    // 摄像机能力
    m_capInfoV2.camera.card_playback_speed= m_pIniFileManager->iniGetInt("devCap","card_playback_speed", OVD_CARD_PLAYBACK_SPEED_MASK_1TIMES);
    m_capInfoV2.camera.AIGC_cloud_record = m_pIniFileManager->iniGetInt("devCap", "AIGC_cloud_record", 0);
    m_capInfoV2.camera.support_DN_mode = m_pIniFileManager->iniGetInt("devCap", "support_DN_mode", 0);
    m_capInfoV2.camera.support_led = m_pIniFileManager->iniGetInt("devCap", "have_led", 1);
    m_capInfoV2.camera.support_sd = m_pIniFileManager->iniGetInt("devCap", "have_sd", 1);
    m_capInfoV2.camera.support_switch = m_pIniFileManager->iniGetInt("devCap", "have_switch", 1);
    m_capInfoV2.camera.support_sdcapacity = m_pIniFileManager->iniGetInt("devCap","have_sdcapacity",0);

    int normal_nightvision = m_pIniFileManager->iniGetInt("devCap","support_set_normal_nightvision_mode",1);
    int color_nightvision = m_pIniFileManager->iniGetInt("devCap", "support_set_color_nightvision_mode", 1);
    if (color_nightvision) {
        m_capInfoV2.camera.nightvision_mode = OVD_NIGHTVISION_MODE_COLOR;
    } else if (normal_nightvision) {
        m_capInfoV2.camera.nightvision_mode = OVD_NIGHTVISION_MODE_NORMAL;
    } else {
        m_capInfoV2.camera.nightvision_mode = OVD_NIGHTVISION_MODE_NONE;
    }
    m_capInfoV2.camera.ai_nightvision_mask = m_pIniFileManager->iniGetInt("devCap", "support_detect_nightvision_mask", OVD_NIGHTVISION_DETECT_MOTION | OVD_NIGHTVISION_DETECT_HUMAN);
    
    m_capInfoV2.cloudai.support_CloudAI = true;
    
    return 0;
}

int CmccOvd::ovdInitCapbility(OVDCapInfoV2_t *demo_cap) {
    int i;
    OVD_cap_params_t  cap_param = {0};
    char buffer[256];

    cap_param.max_channel = m_configureInfo.channelsInfoCount;
    cap_param.product_function = OVD_PRODUCT_FUNCTION_LOWPOWER;
    cap_param.ai_function = 0;

    OVD_CapGetV2(m_capInfoV2.device_type, &cap_param, demo_cap);

    for (i = 0; i<demo_cap->count; i++)
    {
        OVD_Printf(OVD_LOGLEVEL_ERROR, "cap setting, channel %d, type %d, size %d\n", demo_cap->cap[i]->channel, demo_cap->cap[i]->cap_type, demo_cap->cap[i]->size);
        // demo 中，各个视频通道使用相同的能力集
        if (demo_cap->cap[i]->cap_type == OVD_CAP_TYPE_BASE)
        {
            memcpy(demo_cap->cap[i]->cap_data, &m_capInfoV2.base, sizeof(OVD_CapInfoBase));
        }
        else if (demo_cap->cap[i]->cap_type == OVD_CAP_TYPE_LOW_POWER)
        {
            memcpy(demo_cap->cap[i]->cap_data, &m_capInfoV2.lowpower, sizeof(OVD_CapInfoLowPower));
        }
        else if (demo_cap->cap[i]->cap_type == OVD_CAP_TYPE_CAMERA)
        {
            memcpy(demo_cap->cap[i]->cap_data, &m_capInfoV2.camera, sizeof(OVD_CapInfoCamera));
        }
        else
        {
            OVD_Printf(OVD_LOGLEVEL_WARN, "ignore cap, channel %d, type %d\n", demo_cap->cap[i]->channel, demo_cap->cap[i]->cap_type);
            continue;
        }
    }

    return 0;
}

int CmccOvd::ovdGetRebootInfo(ovd_reboot_param_t *rebootinfo) {
    rebootinfo->ts = m_pCfgFileManager->iniGetInt("rebootinfo", "ts", 0);
    if(0==rebootinfo->ts)
    {

        struct timeval tv = {0};

        gettimeofday(&tv, NULL);
        rebootinfo->ts= tv.tv_sec;
    }
    rebootinfo->reason = (ovd_reboot_reason_e)m_pCfgFileManager->iniGetInt("rebootinfo","reason",OVD_REBOOT_REASON_DUT_COMMON);
    m_pCfgFileManager->iniGetString("rebootinfo", "message", rebootinfo->message, sizeof(rebootinfo->message), NULL);
    OVD_Printf(OVD_LOGLEVEL_DEBUG,"==wjj==reason:%d, ts:%lld, message:%s====\n",rebootinfo->reason,rebootinfo->ts,rebootinfo->message);
    // m_pIniFileManager->iniFileFree();
    return 0;
}

void *CmccOvd::pthread_OVDDemoLogOutput(void *args) {
    // OVD_Printf(OVD_LOGLEVEL_WARN,"pthread_OVDDemoLogOutput started\n");
    // prctl(PR_SET_NAME,"demo_logout");
    // FILE *fp = NULL;
    // fp = fopen(uploadlogFILEPATH, "w");
    // if(NULL == fp){
    //     OVD_Printf(OVD_LOGLEVEL_ERROR,"打开失败OVDSdkDemo.log\n");
    //     return NULL;
    // }

    // OVD_Printf(OVD_LOGLEVEL_DEBUG,"pthread_OVDDemoLogOutput file open succ, SDKDemoStarted %d\n",SDKDemoStarted);

    // while(1 == SDKDemoStarted){
    //     m_iLogThreadStarted = 1;
    //     sem_wait(&full);
    //     pthread_mutex_lock(&m_logOutputMutex);
    //     //OVD_Printf(OVD_LOGLEVEL_INFO,"get buff: %s\n", ovdLogOutputBuff);
    //     fprintf(fp, "OVD SDK: %s", ovdLogOutputBuff);
    //     pthread_mutex_unlock(&m_logOutputMutex);
    //     //sem_post(&empty);
    // }

    // fclose(fp);
    // OVD_Printf(OVD_LOGLEVEL_WARN,"pthread_OVDDemoLogOutput quit\n");
    return NULL;
}

void CmccOvd::ovdLogOutCallBack(const char* buff) {
    // if(0 == m_iLogThreadStarted){
    //     return;
    // }
    // pthread_mutex_lock(&m_logOutputMutex);
    // snprintf(ovdLogOutputBuff, 2048, "%s", buff);
    // pthread_mutex_unlock(&m_logOutputMutex);

    // sem_post(&full);
}

int CmccOvd::ovdInitLogConfInfo() {
    // OVD_Printf(OVD_LOGLEVEL_WARN,"读取logConfiguration\n");
    // int subStrLenth = 0;
    // m_iLogThreadStarted = 0;

    // //先把值都清空
    // memset(&m_logParam, 0, sizeof(OVDLogParam));
    // /*
    // typedef struct
    // {
    //     OVDLogLevel logLevel;           //日志输出级别，详细见枚举值LogLevel，可选
    //     OVDLogSTD   logSTD;             //日志输出位置，可选，详细见枚举值LogSTD，可选
    //     void (*pOVDLogOutCallBack)(const char* buff);  //设备提供的日志输出回调，SDK的输出日志可以保存到device的存储文件中，可选，若未空为不支持
    // }OVDLogParam;
    // */

    // m_logParam.logSTD = (OVDLogSTD)m_pIniFileManager->iniGetInt("logConfiguration","logSTD",1);
    // m_logParam.logLevel = (OVDLogLevel)m_pIniFileManager->iniGetInt("logConfiguration","logLevel",1);
    // m_logParam.max_size = m_pIniFileManager->iniGetInt("logConfiguration","max_size",1);

    // char value[2];
    // memset(value,0,2);
    // m_pIniFileManager->iniGetString("logCOnfiguration","callback",value,sizeof(value),NULL);
    // sem_init(&full, 0, 0);
    // if(0 == strcmp(value, "y"))
    // {
    //     OVD_Printf(OVD_LOGLEVEL_WARN,"创建线程 设置回调\n");
    //     pthread_mutex_init(&m_logOutputMutex, NULL);
	// 	pthread_create(&m_logOutputPid, NULL, pthread_OVDDemoLogOutput, NULL);
    //     m_logParam.pOVDLogOutCallBack = ovdLogOutCallBack;
    //  }
    // OVD_Printf(OVD_LOGLEVEL_DEBUG,"logParam std %d, level %d\n", m_logParam.logSTD, m_logParam.logLevel);

    return 0;
}
    
void CmccOvd::setCallBackFunList(OVD_CallBackFunList *pcallBackFunList) {
    // std::function<OVD_int32(OVDDeviceInfo *)> testCallback = std::bind(&CmccOvd::OVD_GetOVDDeviceInfo, this, std::placeholders::_1);
    pcallBackFunList->OVD_GetOVDDeviceInfo = OVD_GetOVDDeviceInfo;
    pcallBackFunList->OVD_GetOVDConfigureInfo = OVD_GetOVDConfigureInfo;
    pcallBackFunList->OVC_SetOVDConfigureInfo = OVD_SetOVDConfigureInfo;
    pcallBackFunList->OVD_GetOVDChannelConfigureInfo = OVD_GetOVDChannelConfigureInfo;

	pcallBackFunList->OVD_OVCConnectStatus = OVD_OVCConnectStatus;
    pcallBackFunList->OVD_ReBootChannel = OVD_ReBootChannel;
    pcallBackFunList->OVD_ReBootDevice = OVD_ReBootDevice;
    pcallBackFunList->OVD_GetGpsInfo = OVD_GetGpsInfo;
    pcallBackFunList->OVD_FirmwareBinUpgrade = OVD_FirmwareBinUpgrade;
    pcallBackFunList->OVD_FirmwareUpgradeConfirmCb = OVD_FirmwareUpgradeStatusCb;
    pcallBackFunList->OVD_SetSDCardFormat = OVD_SetSDCardFormat;
    pcallBackFunList->OVD_PTZCmd = OVD_PTZCmd;
    pcallBackFunList->OVD_GetPresetList = OVD_GetPresetList;
    pcallBackFunList->OVD_ForceIFrame = OVD_ForceIFrame;
    pcallBackFunList->OVD_Snapshot = OVD_Snapshot;
    pcallBackFunList->OVD_AlarmNotifyCallback_t=OVD_AlarmNotifyCalback;
    pcallBackFunList->OVD_SetAudioOutPlay = OVD_SetMp3Url;

    pcallBackFunList->OVD_KeepAwakenUtilExpired = OVD_KeepAwakenUtilExpired;
	pcallBackFunList->OVD_ResetConfiguration = OVD_ResetConfig;
    pcallBackFunList->OVD_settime = OVD_settime;
    pcallBackFunList->OVD_gettime = OVD_gettime;
    pcallBackFunList->OVD_getsimpleovdinfo = OVD_getsimpleovdinfo;
    pcallBackFunList->OVD_GetDiskInfo = OVD_GetDiskInfo;
    pcallBackFunList->OVD_LogUploadAsync = OVD_LogUploadAsync;
    //srt 第二期卡录像方案
    pcallBackFunList->OVD_DMEAPI_callback_RecordSearch = OVD_DMEAPI_callback_RecordSearch2;
    pcallBackFunList->OVD_DMEAPI_callback_RecordOpen = OVD_DMEAPI_callback_RecordOpen;
    pcallBackFunList->OVD_DMEAPI_callback_RecordSeek = OVD_DMEAPI_callback_RecordSeek;
    pcallBackFunList->OVD_DMEAPI_callback_RecordReadFrame = OVD_DMEAPI_callback_RecordReadFrame;
    pcallBackFunList->OVD_DMEAPI_callback_RecordSpeedReadFrame = OVD_DMEAPI_callback_RecordSpeedReadFrame;
    pcallBackFunList->OVD_DMEAPI_callback_RecordClose = OVD_DMEAPI_callback_RecordClose;

    OVD_Printf(OVD_LOGLEVEL_WARN,"RecordOpen func addr = %p \n",pcallBackFunList->OVD_DMEAPI_callback_RecordOpen);
    pcallBackFunList->OVD_StopAlarm = OVD_StopAlarm;
    pcallBackFunList->OVD_EBOCmd = OVD_EBOCmd;
    pcallBackFunList->OVD_GetDevRunningInfo = OVD_GetDevRunningInfo;
    pcallBackFunList->OVD_ROBOTCmd = OVD_ROBOTCmd;
    pcallBackFunList->OVD_LogNotifyCb = OVD_LogNotifyCb;
}

void CmccOvd::prepareJsonParam(char *jsonParam) {
    char *data=NULL;
    cJSON *root= cJSON_CreateObject();
	cJSON_AddNumberToObject(root,"snapshotSize",Device_IMAGE_BUFF_SIZE);
	cJSON_AddNumberToObject(root,"buffDuration",Device_AV_BUFF_DURATION);
    if(Device_DEBUG_TS==1)
    {
        cJSON_AddBoolToObject(root,"debug_ts",1);
    }
    else
    {
        cJSON_AddBoolToObject(root,"debug_ts",0);
    }
    cJSON_AddNumberToObject(root,"cseg_duration",Device_CSEG_DURATION);
    cJSON_AddNumberToObject(root,"cseg_maxcount",Device_CSEG_MAXCOUNT);
    cJSON_AddNumberToObject(root,"snddropdelay",Device_SNDDROPDELAY);
	data=(char *)cJSON_PrintUnformatted(root);
	strncpy(jsonParam, data, strlen(data));
    cJSON_free(data);
    cJSON_Delete(root);
}

int CmccOvd::ovdInitDeviceInfo() {
    OVD_Printf(OVD_LOGLEVEL_WARN,"读取deviceConfiguration\n");
    char buff[255]={0};
    int subStrLenth = 0;
    char keys[255]={0};

    //先把值都清空
    memset(&m_deviceInfo, 0, sizeof(OVDDeviceInfo));
    char *sect="deviceConfiguration";

    if(nullptr == m_pIniFileManager) {
        printf("m_pIniFileManager is nullptr\n");        
    }

    run_daemon = m_pIniFileManager->iniGetInt(sect,"run_deamo" , 0);
    //设备id
    m_pIniFileManager->iniGetString(sect,"devId",m_deviceInfo.OVDDeviceID,sizeof(m_deviceInfo.OVDDeviceID),"");
  	//OVD_Printf(OVD_LOGLEVEL_ERROR,"error:OVDDeviceID = %s\n", m_deviceInfo.OVDDeviceID);
    //芯片型号
    m_pIniFileManager->iniGetString(sect,"modelId",m_deviceInfo.OVDChipModel,sizeof(m_deviceInfo.OVDChipModel),"chip");
    //硬件型号
    m_pIniFileManager->iniGetString(sect,"hardwareModel",m_deviceInfo.OVDHardWareModel,sizeof(m_deviceInfo.OVDHardWareModel),"hardware");
    //硬件版本号
    m_pIniFileManager->iniGetString(sect,"firmware_model",m_deviceInfo.OVDSystemVersion,sizeof(m_deviceInfo.OVDSystemVersion),"V1.0.0");
    //网络连接类型
    m_deviceInfo.networkType = m_pIniFileManager->iniGetInt(sect,"network_type",1);
    //配网类型，根据设备真实配网方式上报
    m_deviceInfo.DN_mode = (OVD_DNM_e)m_pIniFileManager->iniGetInt(sect,"DN_mode",1);
    //wifissid
    m_pIniFileManager->iniGetString(sect,"wifi_ssid",m_deviceInfo.wifi_ssid,sizeof(m_deviceInfo.wifi_ssid),NULL);
    //wifi强度
    m_deviceInfo.wifi_signal = m_pIniFileManager->iniGetInt(sect,"wifi_signal",0);
    //上行带宽
    m_deviceInfo.upBandwidth = m_pIniFileManager->iniGetInt(sect,"upBandwidth",0);
    //下行带宽
    m_deviceInfo.downBandwidth = m_pIniFileManager->iniGetInt(sect,"downBandwith",0);
    //ipaddr
    m_pIniFileManager->iniGetString(sect,"ipAddr",m_deviceInfo.ipAddr,sizeof(m_deviceInfo.ipAddr),NULL);
    //macaddr
    m_pIniFileManager->iniGetString(sect,"macAddr",m_deviceInfo.macAddr,sizeof(m_deviceInfo.macAddr),NULL);
    //电量
    m_deviceInfo.battery = m_pIniFileManager->iniGetInt(sect, "battery", 0);
	//CPU占用率
    m_deviceInfo.cpuLoad = m_pIniFileManager->iniGetInt("deviceSoftProbe", "cpuLoad", 0);
	//全部内存 单位KB
	m_deviceInfo.memoryTotal = m_pIniFileManager->iniGetInt("deviceSoftProbe", "memoryTotal", 0);
	//剩余内存 单位KB
	m_deviceInfo.memoryAvailable = m_pIniFileManager->iniGetInt("deviceSoftProbe", "memoryAvailable", 0);

    return 0;
}

int CmccOvd::ovdShowDeviceInfo() {
    OVD_Printf(OVD_LOGLEVEL_INFO,"===[deviceInfo]====\n");
    OVD_Printf(OVD_LOGLEVEL_INFO,"\t OVDDeviceID:%s \n",m_deviceInfo.OVDDeviceID);
    OVD_Printf(OVD_LOGLEVEL_INFO,"\t OVDHardWareModel: %s\n", m_deviceInfo.OVDHardWareModel);
    OVD_Printf(OVD_LOGLEVEL_INFO,"\t OVDSystemVersion: %s\n", m_deviceInfo.OVDSystemVersion);
    OVD_Printf(OVD_LOGLEVEL_INFO,"\t networkType: %d\n", m_deviceInfo.networkType);
    OVD_Printf(OVD_LOGLEVEL_INFO,"\t wifi_ssid: %s\n", m_deviceInfo.wifi_ssid);
    OVD_Printf(OVD_LOGLEVEL_INFO,"\t wifi_signal: %d\n",m_deviceInfo.wifi_signal);
    OVD_Printf(OVD_LOGLEVEL_INFO,"\t upBandwidth: %d\n", m_deviceInfo.upBandwidth);
    OVD_Printf(OVD_LOGLEVEL_INFO,"\t downBandwidth: %d\n", m_deviceInfo.downBandwidth);
    OVD_Printf(OVD_LOGLEVEL_INFO,"\t ipAddr: %s\n", m_deviceInfo.ipAddr);
    OVD_Printf(OVD_LOGLEVEL_INFO,"\t macAddr: %s\n", m_deviceInfo.macAddr);
    OVD_Printf(OVD_LOGLEVEL_INFO,"\t battery: %d \n", m_deviceInfo.battery);
    
    return 0;
}

int CmccOvd::ovdInitClientInfo() {
    memset(&m_clientParam, 0, sizeof(OVDClientParam));

    m_pIniFileManager->iniGetString("deviceConfiguration", "devId", m_clientParam.OVDDeviceID, sizeof(m_clientParam.OVDDeviceID), NULL);
    m_pIniFileManager->iniGetString("deviceConfiguration", "devCmei", m_clientParam.OVDDeviceCMEI, sizeof(m_clientParam.OVDDeviceCMEI), NULL);
    m_pIniFileManager->iniGetString("deviceClientInfo","OVDLoginPassword",m_clientParam.OVDLoginPassword,sizeof(m_clientParam.OVDLoginPassword), NULL);
	m_pIniFileManager->iniGetString("deviceClientInfo","OVDMediaEncPassword",m_clientParam.OVDMediaEncPassword,sizeof(m_clientParam.OVDMediaEncPassword), NULL);
	if(strlen(m_clientParam.OVDLoginPassword) < 6  )
	{
		OVD_Printf(OVD_LOGLEVEL_ERROR,"error:OVDLoginPassword输入不符合规范 \n");
		return -1;
	}
    m_pIniFileManager->iniGetString("deviceConfiguration","hardwareModel",m_clientParam.OVDHardWareModel,sizeof(m_clientParam.OVDHardWareModel),"hardware");
    //软件版本号
    m_pIniFileManager->iniGetString("deviceConfiguration","firmware_model",m_clientParam.OVDSystemVersion,sizeof(m_clientParam.OVDSystemVersion),"V1.0.0");
    m_pIniFileManager->iniGetString("deviceConfiguration","modelId",m_clientParam.OVDModelId,sizeof(m_clientParam.OVDModelId),"");
    m_pIniFileManager->iniGetString("deviceConfiguration","macAddr",m_clientParam.OVDmacaddress,sizeof(m_clientParam.OVDmacaddress),"");

    memset(m_bindId,0,sizeof(m_bindId));
    m_pIniFileManager->iniGetString("deviceClientInfo","bindId",m_bindId,sizeof(m_bindId),NULL);
    m_pIniFileManager->iniGetString("deviceClientInfo","servicescheduleurl",m_clientParam.servicescheduleurl,sizeof(m_clientParam.servicescheduleurl),NULL);
    m_pIniFileManager->iniGetString("deviceConfigInfo","ovd_log_path",m_clientParam.ovd_log_path,sizeof(m_clientParam.ovd_log_path),"/home/<USER>/robot-application/xiaoli_application_ros2/src/live_stream/src/log/");
    m_pIniFileManager->iniGetString("deviceConfigInfo","storage_path",m_clientParam.local_storage_path,sizeof(m_clientParam.local_storage_path), "/home/<USER>/robot-application/xiaoli_application_ros2/src/live_stream/src/");
    m_pIniFileManager->iniGetString("deviceConfigInfo","SDK_partition_mounting_path", m_clientParam.ovd_data_path,sizeof(m_clientParam.ovd_data_path), "/home/<USER>/robot-application/xiaoli_application_ros2/src/live_stream/src/");

    m_pIniFileManager->iniGetString("deviceConfigInfo","ovd_ai_path", m_clientParam.ovd_ai_path, sizeof(m_clientParam.ovd_ai_path), "/home/<USER>/robot-application/xiaoli_application_ros2/ai/");

    return 0;
}

int CmccOvd::ovdInitDeviceConfInfo() {
    OVDChannelsInfo *chn;
    OVD_Printf(OVD_LOGLEVEL_INFO,"读取deviceConfigInfo\n");
    int i=0;
    
    memset(&m_configureInfo, 0, sizeof(OVDConfigrationInfo));
    m_configureInfo.channelsInfoCount = m_pIniFileManager->iniGetInt("deviceConfigInfo", "channelsInfoCount", 1);
    m_configureInfo.tz = m_pIniFileManager->iniGetInt("deviceConfigInfo","tz",8);
    m_pIniFileManager->iniGetString("deviceConfigInfo","autoreboot_last_reboot",m_configureInfo.autorebootinfo.last_reboot,sizeof(m_configureInfo.autorebootinfo.last_reboot),NULL);

    m_configureInfo.channelsInfo = (OVDChannelsInfo *)malloc(m_configureInfo.channelsInfoCount * sizeof(OVDChannelsInfo));
    for(i=0;i<m_configureInfo.channelsInfoCount;i++) {
        char sect[256];
        memset(sect,0,256);
        snprintf(sect,256,"channel%d",i);
        chn = &m_configureInfo.channelsInfo[i];
        chn->channel=i;
        if (i < m_configureInfo.channelsInfoCount) {
            chn->videoinfo.codec = (OVDAVCodec)m_pIniFileManager->iniGetInt(sect,"video_codec",1);
            chn->videoinfo.bitrate = m_pIniFileManager->iniGetInt(sect,"video_bitrate",0);
            chn->videoinfo.width = (short)m_pIniFileManager->iniGetInt(sect,"video_width",1280);
            chn->videoinfo.height = (short)m_pIniFileManager->iniGetInt(sect,"video_height",720);
            chn->videoinfo.framerate = m_pIniFileManager->iniGetInt(sect,"video_framerate",30);
            chn->videoinfo.framerate_aov=1;
            chn->videoinfo.colorDepth = m_pIniFileManager->iniGetInt(sect,"video_colorDepth",8);
            chn->videoinfo.frameInterval = m_pIniFileManager->iniGetInt(sect,"video_frameInterval",60);

            chn->audioinfo.codec = (OVDAVCodec)m_pIniFileManager->iniGetInt(sect,"audio_codec",16);
            chn->audioinfo.samplesRate = m_pIniFileManager->iniGetInt(sect,"audio_samplesRate",16000);
            chn->audioinfo.bitrate = m_pIniFileManager->iniGetInt(sect,"audio_bitrate",0);
            chn->audioinfo.waveFormat = m_pIniFileManager->iniGetInt(sect,"audio_waveFormat",0);
            chn->audioinfo.channelNumber = m_pIniFileManager->iniGetInt(sect,"audio_channelNumber",1);
            chn->audioinfo.blockAlign = m_pIniFileManager->iniGetInt(sect,"audio_blockAlign",0);
            chn->audioinfo.bitsPerSample = m_pIniFileManager->iniGetInt(sect,"audio_bitsPerSample",0);
            chn->audioinfo.sampleperframe = m_pIniFileManager->iniGetInt(sect,"audio_samplePerframe",0);
            chn->audioinfo.frameInterval = m_pIniFileManager->iniGetInt(sect,"audio_frameInterval",0);
        }
    }

    return 0;
}

int CmccOvd::ovdShowClientInfo() {
    OVD_Printf(OVD_LOGLEVEL_INFO,"===[clientParam]====\n");
    OVD_Printf(OVD_LOGLEVEL_INFO,"\t OVDDeviceID:%s\n",m_clientParam.OVDDeviceID);
    OVD_Printf(OVD_LOGLEVEL_INFO,"\t OVDLoginPassword:%s\n",m_clientParam.OVDLoginPassword);
	OVD_Printf(OVD_LOGLEVEL_INFO,"\t OVDMediaEncPassword:%s\n",m_clientParam.OVDMediaEncPassword);
    OVD_Printf(OVD_LOGLEVEL_INFO,"\t OVDHardWareModel:%s\n", m_clientParam.OVDHardWareModel);
    OVD_Printf(OVD_LOGLEVEL_INFO,"\t OVDSystemVersion:%s\n", m_clientParam.OVDSystemVersion);
    OVD_Printf(OVD_LOGLEVEL_INFO,"\t OVDModelId:%s\n", m_clientParam.OVDModelId);
    
    return 0;
}

int CmccOvd::ovdDeviceInit() {
    int result = 0;
    videoFPS = 0;
    m_bindId[0] = '\0';
    
    result = ovdInitDeviceInfo();
    if(0 != result){
        OVD_Printf(OVD_LOGLEVEL_ERROR,"----->初始化DeviceInfo失败<-----\n");
        return -1;
    }
    ovdShowDeviceInfo();

    result=ovdInitClientInfo();
    if(0 != result){
        OVD_Printf(OVD_LOGLEVEL_ERROR,"----->初始化ClientInfo失败<-----\n");
        return -1;
    }

    ovdShowClientInfo();
    ovdInitDevCapV2();
    ovdInitDeviceConfInfo();
    ovdInitLogConfInfo();
    
    return 0;
}

int CmccOvd::OVD_GetOVDConfigureInfo(char** output_ovdconfig, int *len){

    cJSON *root= cJSON_CreateObject();
    cJSON *channelsarray=NULL;
    cJSON *channelentry=NULL;
    cJSON *screenentry=NULL;
    //cJSON *resp=NULL ;

    cJSON *auto_rebootentry=NULL;
    //cJSON *yard_light_config_entry=NULL;
    //cJSON *EBO_config_entry=NULL;
    //cJSON *rebot_ctrl_entry=NULL;
    //cJSON *OWL_config_cruise_entry=NULL;
    //cJSON *EBO_config_cruise_entry=NULL;
    //cJSON *dashcam_entry=NULL;
    //cJSON *timed_schedule_entry=NULL;
	cJSON *support_softprobentry=NULL;
    cJSON *id=NULL;
    char *data=NULL;
    char *json_buffer = NULL;
    int ret;
    int i=0;
	//OVD_DEMOE_InitSetableKeys();
    cJSON_AddItemToObject(root,"channels",channelsarray=cJSON_CreateObject());
	cJSON_AddBoolToObject(root,"led",m_configureInfo.led);
    cJSON_AddItemToObject(root,"screen",screenentry=cJSON_CreateObject());
    cJSON_AddBoolToObject(screenentry,"on",m_configureInfo.screen);

    cJSON_AddBoolToObject(root,"light_supplement_lamp_on",m_configureInfo.light_supplement_lamp_on);
    cJSON_AddNumberToObject(root,"dutMTU",dutMTU);

    cJSON_AddBoolToObject(root,"smart_mode_switch",m_configureInfo.smart_mode_switch_on);
	cJSON_AddItemToObject(root,"auto_reboot",auto_rebootentry=cJSON_CreateObject());
    cJSON_AddBoolToObject(auto_rebootentry,"on",m_configureInfo.autorebootinfo.on);
    cJSON_AddNumberToObject(auto_rebootentry,"cycle",m_configureInfo.autorebootinfo.cycle);
    cJSON_AddStringToObject(auto_rebootentry,"start",m_configureInfo.autorebootinfo.start);
    cJSON_AddStringToObject(auto_rebootentry,"end",m_configureInfo.autorebootinfo.end);
	cJSON_AddStringToObject(auto_rebootentry,"last_reboot",m_configureInfo.autorebootinfo.last_reboot);
    //庭院灯
    // cJSON_AddItemToObject(root,"yard_light_config",yard_light_config_entry=cJSON_CreateObject());
    // cJSON_AddNumberToObject(yard_light_config_entry,"light_brightness",OVD_DEMO_ConfigureInfo.yardlightconfig.light_brightness);
    // cJSON_AddNumberToObject(yard_light_config_entry,"control_mode",OVD_DEMO_ConfigureInfo.yardlightconfig.control_mode);
    // cJSON_AddBoolToObject(yard_light_config_entry,"light_on",OVD_DEMO_ConfigureInfo.yardlightconfig.light_on);
    // cJSON_AddItemToObject(yard_light_config_entry,"timed_schedule",timed_schedule_entry=cJSON_CreateArray());
    // int timed_schedule_num = 0;
    // int timed_schedule_size = 0;
    // cJSON *timed_schedule_tmp_entry = NULL;
    // if(OVD_DEMO_ConfigureInfo.yardlightconfig.timed_schedule_size){
    //     timed_schedule_size=OVD_DEMO_ConfigureInfo.yardlightconfig.timed_schedule_size;
    // }else{
    //     timed_schedule_size=2;
    // }
    // for(timed_schedule_num=0;timed_schedule_num < timed_schedule_size; timed_schedule_num++){
    //     timed_schedule_tmp_entry = cJSON_CreateObject();
    //     cJSON_AddStringToObject(timed_schedule_tmp_entry,"start_time",OVD_DEMO_ConfigureInfo.yardlightconfig.timed_schedule[timed_schedule_num].start_time);
    //     cJSON_AddStringToObject(timed_schedule_tmp_entry,"end_time",OVD_DEMO_ConfigureInfo.yardlightconfig.timed_schedule[timed_schedule_num].end_time);
    //     cJSON_AddItemToArray(timed_schedule_entry, timed_schedule_tmp_entry);
    // }
    // cJSON_AddItemToObject(root,"EBO",EBO_config_entry=cJSON_CreateObject());
    // cJSON_AddBoolToObject(EBO_config_entry,"EBO_nofall_on",OVD_DEMO_ConfigureInfo.eboconfig.EBO_nofall_on);
    // cJSON_AddBoolToObject(EBO_config_entry,"EBO_laser_on",OVD_DEMO_ConfigureInfo.eboconfig.EBO_laser_on);
    // cJSON_AddBoolToObject(EBO_config_entry,"EBO_track_on",OVD_DEMO_ConfigureInfo.eboconfig.EBO_track_on);
    // cJSON_AddNumberToObject(EBO_config_entry,"EBO_track_type",OVD_DEMO_ConfigureInfo.eboconfig.EBO_track_type);
    // cJSON_AddBoolToObject(EBO_config_entry,"EBO_collision_on",OVD_DEMO_ConfigureInfo.eboconfig.EBO_collision_on);
    // cJSON_AddBoolToObject(EBO_config_entry,"EBO_reminder_on",OVD_DEMO_ConfigureInfo.eboconfig.EBO_reminder_on);
    // cJSON_AddNumberToObject(EBO_config_entry,"EBO_speed_adjust",OVD_DEMO_ConfigureInfo.eboconfig.EBO_speed_adjust);
    // cJSON_AddItemToObject(EBO_config_entry,"EBO_cruise_on",EBO_config_cruise_entry=cJSON_CreateObject());
    // cJSON_AddBoolToObject(EBO_config_cruise_entry,"on",OVD_DEMO_ConfigureInfo.eboconfig.EBO_cruise_on);
    // cJSON_AddStringToObject(EBO_config_cruise_entry,"start_time",OVD_DEMO_ConfigureInfo.eboconfig.start_time);
    // cJSON_AddNumberToObject(EBO_config_cruise_entry,"time_long",OVD_DEMO_ConfigureInfo.eboconfig.time_long);
	// OVD_DEMO_OVCGetRepeat(EBO_config_cruise_entry,OVD_DEMO_ConfigureInfo.eboconfig.repeat);

    // cJSON_AddItemToObject(root,"OWL",rebot_ctrl_entry=cJSON_CreateObject());
    // cJSON_AddBoolToObject(rebot_ctrl_entry,"OWL_food_on",OVD_DEMO_ConfigureInfo.robot_config.OWL_food_on);
    // cJSON_AddBoolToObject(rebot_ctrl_entry,"OWL_track_mode",OVD_DEMO_ConfigureInfo.robot_config.OWL_track_mode);
    // cJSON_AddBoolToObject(rebot_ctrl_entry,"OWL_touch_mode",OVD_DEMO_ConfigureInfo.robot_config.OWL_touch_mode);
    // cJSON_AddBoolToObject(rebot_ctrl_entry,"OWL_sound_on",OVD_DEMO_ConfigureInfo.robot_config.OWL_sound_on);
    // cJSON_AddNumberToObject(rebot_ctrl_entry,"OWL_speed_switch",OVD_DEMO_ConfigureInfo.robot_config.OWL_speed_switch);
    // cJSON_AddItemToObject(rebot_ctrl_entry,"OWL_cruise_on",OWL_config_cruise_entry=cJSON_CreateObject());
    // cJSON_AddStringToObject(OWL_config_cruise_entry,"start_time",OVD_DEMO_ConfigureInfo.robot_config.start_time);
	// OVD_DEMO_OVCGetRepeat(OWL_config_cruise_entry,OVD_DEMO_ConfigureInfo.robot_config.repeat);

    // //1.45 行车记录仪
    // cJSON_AddItemToObject(root,"dashcam",dashcam_entry=cJSON_CreateObject());
    // cJSON_AddBoolToObject(dashcam_entry,"dashcam_findcar_on",OVD_DEMO_ConfigureInfo.dashcam_config.dashcam_findcar_on);
    // cJSON_AddBoolToObject(dashcam_entry,"dashcam_rescue_on",OVD_DEMO_ConfigureInfo.dashcam_config.dashcam_rescue_on);


    // cJSON *feeder_config_entry=NULL;
    // cJSON *feeder_immed_config_entry=NULL;
    // cJSON_AddItemToObject(root,"petfeeder",feeder_config_entry=cJSON_CreateObject());
    // cJSON_AddItemToObject(feeder_config_entry,"feeder_immed",feeder_immed_config_entry=cJSON_CreateObject());
    // cJSON_AddNumberToObject(feeder_immed_config_entry,"feeder_quantity",OVD_DEMO_ConfigureInfo.feeder_immed_quantity);
    // cJSON *feeder_plan_config_entry=NULL;
    // cJSON_AddItemToObject(feeder_config_entry,"feeder_plan",feeder_plan_config_entry=cJSON_CreateArray());
    // cJSON *pointentry=NULL;
    // int feeder_num = 0;
    // int feeder_count = OVD_DEMO_ConfigureInfo.feedercount;
    // for(feeder_num=0;feeder_num<feeder_count;feeder_num++){

    //     cJSON_AddItemToArray(feeder_plan_config_entry,pointentry=cJSON_CreateObject());
    //     cJSON_AddBoolToObject(pointentry,"on",OVD_DEMO_ConfigureInfo.feeder_plan_config[feeder_num].on);
    //     cJSON_AddNumberToObject(pointentry,"feeder_quantity",OVD_DEMO_ConfigureInfo.feeder_plan_config[feeder_num].quantity);
    //     cJSON_AddStringToObject(pointentry,"feeder_name",OVD_DEMO_ConfigureInfo.feeder_plan_config[feeder_num].name);
    //     cJSON_AddStringToObject(pointentry,"feeder_time",OVD_DEMO_ConfigureInfo.feeder_plan_config[feeder_num].time);
    //     OVD_DEMO_OVCGetRepeat(pointentry,OVD_DEMO_ConfigureInfo.feeder_plan_config[feeder_num].repeat);
    // }
    //ovd_config
    for(i=0;i< m_configureInfo.channelsInfoCount;i++)
    {
        cJSON *channelentry = OVD_GetOVDChannelConfigureInfoJson(m_configureInfo.channelsInfo[i].channel);
        if (channelentry != NULL)
        {
            char channel_str[4];
            sprintf(channel_str,"%d", m_configureInfo.channelsInfo[i].channel);
            cJSON_AddItemToObject(channelsarray, channel_str, channelentry);
        }

	}

    cJSON_AddNumberToObject(root,"tz",m_configureInfo.tz);
    int buf_size = OVD_MAX_JSON_REND_SIZE * m_configureInfo.channelsInfoCount;
    json_buffer = (char *)malloc(buf_size);
    ret = cJSON_PrintPreallocated(root, json_buffer, buf_size, 0);
    if (ret != 1)
    {
        OVD_Printf(OVD_LOGLEVEL_ERROR,"Rend json object failed, maybe buffer is limited, current buffer size %d\n", buf_size);
        free(json_buffer);
        cJSON_Delete(root);
        *output_ovdconfig = NULL;
        return -1;
    }
    OVD_Printf(OVD_LOGLEVEL_INFO,"output_ovdconfig:%s\n" ,json_buffer);
    cJSON_Delete(root);
    *len = strlen(json_buffer) + sizeof("");
    *output_ovdconfig = json_buffer;

    return 0;
} 

void CmccOvd::OVD_PrintDeviceInfo(){
    OVD_Printf(OVD_LOGLEVEL_INFO,"\nprint device info begin\n");
    OVD_Printf(OVD_LOGLEVEL_INFO,"devId %s\n", m_deviceInfo.OVDDeviceID);
    OVD_Printf(OVD_LOGLEVEL_INFO,"hardwareModel %s\n", m_deviceInfo.OVDHardWareModel);
    OVD_Printf(OVD_LOGLEVEL_INFO,"firmware_model %s\n", m_deviceInfo.OVDSystemVersion);
    OVD_Printf(OVD_LOGLEVEL_INFO,"network_type %d\n", m_deviceInfo.networkType);
    OVD_Printf(OVD_LOGLEVEL_INFO,"DN_mode %d\n", m_deviceInfo.DN_mode);
    OVD_Printf(OVD_LOGLEVEL_INFO,"wifi_ssid %s\n", m_deviceInfo.wifi_ssid);
    OVD_Printf(OVD_LOGLEVEL_INFO,"wifi_signal %d\n", m_deviceInfo.wifi_signal);
    OVD_Printf(OVD_LOGLEVEL_INFO,"upBandwidth %d\n", m_deviceInfo.upBandwidth);
    OVD_Printf(OVD_LOGLEVEL_INFO,"downBandwidth %d\n", m_deviceInfo.downBandwidth);
    OVD_Printf(OVD_LOGLEVEL_INFO,"ipAddr %s\n", m_deviceInfo.ipAddr);
    OVD_Printf(OVD_LOGLEVEL_INFO,"macAddr %s\n", m_deviceInfo.macAddr);
    OVD_Printf(OVD_LOGLEVEL_INFO,"print device info over\n\n");
}

int CmccOvd::OVD_OVCGetRepeat(cJSON* switchentry,int repeat){
	cJSON *resolutions = NULL;
	char *days[7] = {"Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"};
    int temp = 0;
    int j = 0;

	resolutions =cJSON_AddArrayToObject(switchentry,"repeat");
	//10进制转2进制 & 111 1111（7f） ->得出所有repeat日 ==7f则代表每天都repeat
    temp = repeat;
    while(temp){
        int i = temp%2;
        temp = temp/2;
        if (i==1){
			cJSON_AddItemToArray(resolutions, cJSON_CreateString(days[j]));
            //OVD_Printf(OVD_LOGLEVEL_INFO,"%d %s\n", i,days[j]);
        }
        j++;
    }
    return 0;
}

 int CmccOvd::OVD_GetOVDDeviceInfo(OVDDeviceInfo *deviceInfo) {
    OVD_PrintDeviceInfo();
    memcpy(deviceInfo, &m_deviceInfo, sizeof(OVDDeviceInfo));
    return 0;
} 

cJSON* CmccOvd::GetAISentry(cJSON *channelentry,int i) {
	//AI
	cJSON *AI=NULL;
	cJSON_AddItemToObject(channelentry,"AI",AI=cJSON_CreateObject());
	//AI-face
	cJSON *AI_face=NULL;
	cJSON_AddItemToObject(AI,"face",AI_face=cJSON_CreateObject());
	cJSON_AddBoolToObject(AI_face,"on", m_configureInfo.channelsInfo[i].aiset.AIface.on);
	cJSON_AddBoolToObject(AI_face,"exposure_on", m_configureInfo.channelsInfo[i].aiset.AIface.exposure_on);
	cJSON_AddNumberToObject(AI_face,"exposure_adjust",m_configureInfo.channelsInfo[i].aiset.AIface.exposure_adjust);
	cJSON_AddStringToObject(AI_face,"mode",m_configureInfo.channelsInfo[i].aiset.AIface.mode);
	cJSON_AddNumberToObject(AI_face,"quality_value",m_configureInfo.channelsInfo[i].aiset.AIface.quality_value);
	cJSON_AddNumberToObject(AI_face,"alarm_sensitivity",m_configureInfo.channelsInfo[i].aiset.AIface.sensitivity);
	cJSON *facezone=NULL;
	facezone = cJSON_AddArrayToObject(AI_face,"alarm_zone");
	cJSON * face_area =NULL;
	cJSON_AddItemToArray(facezone,face_area = cJSON_CreateObject());
	cJSON_AddNumberToObject(face_area,"areaId",0);
	cJSON *face_pointarray=NULL;
	cJSON_AddItemToObject(face_area,"point",face_pointarray=cJSON_CreateArray());
	cJSON *face_pointentry=NULL;

	cJSON_AddItemToArray(face_pointarray,face_pointentry=cJSON_CreateObject());
	cJSON_AddNumberToObject(face_pointentry,"pointId",0);
	cJSON_AddNumberToObject(face_pointentry,"x",m_configureInfo.channelsInfo[i].aiset.AIface.zoneInfo.left_bottom_x);
	cJSON_AddNumberToObject(face_pointentry,"y",m_configureInfo.channelsInfo[i].aiset.AIface.zoneInfo.left_bottom_y);

	cJSON_AddItemToArray(face_pointarray,face_pointentry=cJSON_CreateObject());
	cJSON_AddNumberToObject(face_pointentry,"pointId",1);
	cJSON_AddNumberToObject(face_pointentry,"x",m_configureInfo.channelsInfo[i].aiset.AIface.zoneInfo.right_bottom_x);
	cJSON_AddNumberToObject(face_pointentry,"y",m_configureInfo.channelsInfo[i].aiset.AIface.zoneInfo.right_bottom_y);

	cJSON_AddItemToArray(face_pointarray,face_pointentry=cJSON_CreateObject());
	cJSON_AddNumberToObject(face_pointentry,"pointId",2);
	cJSON_AddNumberToObject(face_pointentry,"x",m_configureInfo.channelsInfo[i].aiset.AIface.zoneInfo.right_up_x);
	cJSON_AddNumberToObject(face_pointentry,"y",m_configureInfo.channelsInfo[i].aiset.AIface.zoneInfo.right_up_y);

    cJSON_AddItemToArray(face_pointarray,face_pointentry=cJSON_CreateObject());
    cJSON_AddNumberToObject(face_pointentry,"pointId",3);
	cJSON_AddNumberToObject(face_pointentry,"x",m_configureInfo.channelsInfo[i].aiset.AIface.zoneInfo.left_up_x);
	cJSON_AddNumberToObject(face_pointentry,"y",m_configureInfo.channelsInfo[i].aiset.AIface.zoneInfo.left_up_y);

	//AI-face-mask_detection
    cJSON *mask_detection=NULL;
	cJSON_AddItemToObject(AI_face,"mask_detection",mask_detection=cJSON_CreateObject());
	cJSON_AddBoolToObject(mask_detection,"on", m_configureInfo.channelsInfo[i].aiset.AIface.maskDeteInfo.on);

	cJSON *maskDetetime=NULL;
	cJSON_AddItemToObject(mask_detection,"alerttime",maskDetetime=cJSON_CreateObject());
	cJSON_AddBoolToObject(maskDetetime,"on",m_configureInfo.channelsInfo[i].aiset.AIface.maskDeteInfo.maskSchedule.on);
	cJSON_AddStringToObject(maskDetetime,"starttime",m_configureInfo.channelsInfo[i].aiset.AIface.maskDeteInfo.maskSchedule.starttime);
	cJSON_AddStringToObject(maskDetetime,"endtime",m_configureInfo.channelsInfo[i].aiset.AIface.maskDeteInfo.maskSchedule.endtime);
	int area_repeat =m_configureInfo.channelsInfo[i].aiset.AIface.maskDeteInfo.maskSchedule.repeat;
	OVD_OVCGetRepeat(maskDetetime,area_repeat);

	cJSON *maskDetelinkage=NULL;
	cJSON_AddItemToObject(mask_detection,"linkage_strategy",maskDetelinkage=cJSON_CreateObject());
	cJSON *maskDetespeech=NULL;
	cJSON_AddItemToObject(maskDetelinkage,"speech",maskDetespeech=cJSON_CreateObject());
	cJSON_AddBoolToObject(maskDetespeech,"on",m_configureInfo.channelsInfo[i].aiset.AIface.maskDeteInfo.masklinkage.speech_on);
	cJSON_AddNumberToObject(maskDetespeech,"vol",m_configureInfo.channelsInfo[i].aiset.AIface.maskDeteInfo.masklinkage.speech_vol);
	cJSON_AddNumberToObject(maskDetespeech,"repeat",m_configureInfo.channelsInfo[i].aiset.AIface.maskDeteInfo.masklinkage.speech_repeat);
	cJSON_AddStringToObject(maskDetespeech,"url",m_configureInfo.channelsInfo[i].aiset.AIface.maskDeteInfo.masklinkage.speech_url);
	cJSON *maskDetelight=NULL;
	cJSON_AddItemToObject(maskDetelinkage,"light",maskDetelight=cJSON_CreateObject());
	cJSON_AddBoolToObject(maskDetelight,"on",m_configureInfo.channelsInfo[i].aiset.AIface.maskDeteInfo.masklinkage.light_on);
	cJSON_AddNumberToObject(maskDetelight,"mode",m_configureInfo.channelsInfo[i].aiset.AIface.maskDeteInfo.masklinkage.light_mode);
	cJSON_AddNumberToObject(maskDetelight,"dur",m_configureInfo.channelsInfo[i].aiset.AIface.maskDeteInfo.masklinkage.light_dur);

	cJSON *maskDetebuz=NULL;
	cJSON_AddItemToObject(maskDetelinkage,"buz",maskDetebuz=cJSON_CreateObject());
	cJSON_AddBoolToObject(maskDetebuz,"on",m_configureInfo.channelsInfo[i].aiset.AIface.maskDeteInfo.masklinkage.buz_on);
	cJSON_AddNumberToObject(maskDetebuz,"dur",m_configureInfo.channelsInfo[i].aiset.AIface.maskDeteInfo.masklinkage.buz_dur);


	//AI-kitchen_detection
	cJSON *AI_kitchen=NULL;
	cJSON_AddItemToObject(AI,"kitchen_detection",AI_kitchen=cJSON_CreateObject());
	cJSON_AddBoolToObject(AI_kitchen,"on", m_configureInfo.channelsInfo[i].aiset.AIkitchen.on);
	cJSON_AddBoolToObject(AI_kitchen,"mask_on", m_configureInfo.channelsInfo[i].aiset.AIkitchen.mask_on);
	cJSON_AddBoolToObject(AI_kitchen,"cap_on", m_configureInfo.channelsInfo[i].aiset.AIkitchen.cap_on);
	cJSON_AddBoolToObject(AI_kitchen,"clothes_on", m_configureInfo.channelsInfo[i].aiset.AIkitchen.clothes_on);
	cJSON_AddStringToObject(AI_kitchen,"clothes_color",m_configureInfo.channelsInfo[i].aiset.AIkitchen.clothes_color);
	cJSON_AddNumberToObject(AI_kitchen,"alarm_sensitivity",m_configureInfo.channelsInfo[i].aiset.AIkitchen.sensitivity);
	cJSON *kitchenzone=NULL;
	kitchenzone = cJSON_AddArrayToObject(AI_kitchen,"alarm_zone");
	cJSON * kitchen_area =NULL;
	cJSON_AddItemToArray(kitchenzone,kitchen_area = cJSON_CreateObject());
	cJSON_AddNumberToObject(kitchen_area,"areaId",0);
	cJSON *kitchen_pointarray=NULL;
	cJSON_AddItemToObject(kitchen_area,"point",kitchen_pointarray=cJSON_CreateArray());
	cJSON *kitchen_pointentry=NULL;

	cJSON_AddItemToArray(kitchen_pointarray,kitchen_pointentry=cJSON_CreateObject());
	cJSON_AddNumberToObject(kitchen_pointentry,"pointId",0);
	cJSON_AddNumberToObject(kitchen_pointentry,"x",m_configureInfo.channelsInfo[i].aiset.AIkitchen.zoneInfo.left_bottom_x);
	cJSON_AddNumberToObject(kitchen_pointentry,"y",m_configureInfo.channelsInfo[i].aiset.AIkitchen.zoneInfo.left_bottom_y);

	cJSON_AddItemToArray(kitchen_pointarray,kitchen_pointentry=cJSON_CreateObject());
	cJSON_AddNumberToObject(kitchen_pointentry,"pointId",1);
	cJSON_AddNumberToObject(kitchen_pointentry,"x",m_configureInfo.channelsInfo[i].aiset.AIkitchen.zoneInfo.right_bottom_x);
	cJSON_AddNumberToObject(kitchen_pointentry,"y",m_configureInfo.channelsInfo[i].aiset.AIkitchen.zoneInfo.right_bottom_y);

	cJSON_AddItemToArray(kitchen_pointarray,kitchen_pointentry=cJSON_CreateObject());
	cJSON_AddNumberToObject(kitchen_pointentry,"pointId",2);
	cJSON_AddNumberToObject(kitchen_pointentry,"x",m_configureInfo.channelsInfo[i].aiset.AIkitchen.zoneInfo.right_up_x);
	cJSON_AddNumberToObject(kitchen_pointentry,"y",m_configureInfo.channelsInfo[i].aiset.AIkitchen.zoneInfo.right_up_y);

    cJSON_AddItemToArray(kitchen_pointarray,kitchen_pointentry=cJSON_CreateObject());
	cJSON_AddNumberToObject(kitchen_pointentry,"pointId",3);
	cJSON_AddNumberToObject(kitchen_pointentry,"x",m_configureInfo.channelsInfo[i].aiset.AIkitchen.zoneInfo.left_up_x);
	cJSON_AddNumberToObject(kitchen_pointentry,"y",m_configureInfo.channelsInfo[i].aiset.AIkitchen.zoneInfo.left_up_y);

	//AI-vehicle_detection
	cJSON *AI_vehicle=NULL;
	cJSON_AddItemToObject(AI,"vehicle_detection",AI_vehicle=cJSON_CreateObject());
	cJSON_AddBoolToObject(AI_vehicle,"on", m_configureInfo.channelsInfo[i].aiset.AIvehicle.on);
    cJSON_AddNumberToObject(AI_vehicle,"alarm_sensitivity",m_configureInfo.channelsInfo[i].aiset.AIvehicle.sensitivity);
	cJSON_AddNumberToObject(AI_vehicle,"quality", m_configureInfo.channelsInfo[i].aiset.AIvehicle.quality);
	cJSON_AddNumberToObject(AI_vehicle,"capture_mode", m_configureInfo.channelsInfo[i].aiset.AIvehicle.capture_mode);
	cJSON_AddNumberToObject(AI_vehicle,"scheduled_capture_time",m_configureInfo.channelsInfo[i].aiset.AIvehicle.scheduled_capture_time);
	cJSON_AddNumberToObject(AI_vehicle,"detect_site",m_configureInfo.channelsInfo[i].aiset.AIvehicle.detect_site);
	cJSON_AddNumberToObject(AI_vehicle,"exit_mode",m_configureInfo.channelsInfo[i].aiset.AIvehicle.exit_mode);


	cJSON *vehlinkage=NULL;
	cJSON_AddItemToObject(AI_vehicle,"linkage_strategy",vehlinkage=cJSON_CreateObject());
	cJSON *vehspeech=NULL;
	cJSON_AddItemToObject(vehlinkage,"speech",vehspeech=cJSON_CreateObject());
	cJSON_AddBoolToObject(vehspeech,"on",m_configureInfo.channelsInfo[i].aiset.AIvehicle.vehlinkage.speech_on);
	cJSON_AddNumberToObject(vehspeech,"vol",m_configureInfo.channelsInfo[i].aiset.AIvehicle.vehlinkage.speech_vol);
	cJSON_AddNumberToObject(vehspeech,"repeat",m_configureInfo.channelsInfo[i].aiset.AIvehicle.vehlinkage.speech_repeat);
	cJSON_AddStringToObject(vehspeech,"url",m_configureInfo.channelsInfo[i].aiset.AIvehicle.vehlinkage.speech_url);
	cJSON *vehlight=NULL;
	cJSON_AddItemToObject(vehlinkage,"light",vehlight=cJSON_CreateObject());
	cJSON_AddBoolToObject(vehlight,"on",m_configureInfo.channelsInfo[i].aiset.AIvehicle.vehlinkage.light_on);
	cJSON_AddNumberToObject(vehlight,"mode",m_configureInfo.channelsInfo[i].aiset.AIvehicle.vehlinkage.light_mode);
	cJSON_AddNumberToObject(vehlight,"dur",m_configureInfo.channelsInfo[i].aiset.AIvehicle.vehlinkage.light_dur);

	cJSON *vehbuz=NULL;
	cJSON_AddItemToObject(vehlinkage,"buz",vehbuz=cJSON_CreateObject());
	cJSON_AddBoolToObject(vehbuz,"on",m_configureInfo.channelsInfo[i].aiset.AIvehicle.vehlinkage.buz_on);
	cJSON_AddNumberToObject(vehbuz,"dur",m_configureInfo.channelsInfo[i].aiset.AIvehicle.vehlinkage.buz_dur);

	cJSON *vehiclezone=NULL;
	vehiclezone = cJSON_AddArrayToObject(AI_vehicle,"alarm_zone");
	cJSON * vehicle_area =NULL;
	cJSON_AddItemToArray(vehiclezone,vehicle_area = cJSON_CreateObject());
	cJSON_AddNumberToObject(vehicle_area,"areaId",0);
	cJSON *vehicle_pointarray=NULL;
	cJSON_AddItemToObject(vehicle_area,"point",vehicle_pointarray=cJSON_CreateArray());
	cJSON *vehicle_pointentry=NULL;

	cJSON_AddItemToArray(vehicle_pointarray,vehicle_pointentry=cJSON_CreateObject());
	cJSON_AddNumberToObject(vehicle_pointentry,"pointId",0);
	cJSON_AddNumberToObject(vehicle_pointentry,"x",m_configureInfo.channelsInfo[i].aiset.AIvehicle.zoneInfo.left_bottom_x);
	cJSON_AddNumberToObject(vehicle_pointentry,"y",m_configureInfo.channelsInfo[i].aiset.AIvehicle.zoneInfo.left_bottom_y);

	cJSON_AddItemToArray(vehicle_pointarray,vehicle_pointentry=cJSON_CreateObject());
	cJSON_AddNumberToObject(vehicle_pointentry,"pointId",1);
	cJSON_AddNumberToObject(vehicle_pointentry,"x",m_configureInfo.channelsInfo[i].aiset.AIvehicle.zoneInfo.right_bottom_x);
	cJSON_AddNumberToObject(vehicle_pointentry,"y",m_configureInfo.channelsInfo[i].aiset.AIvehicle.zoneInfo.right_bottom_y);

	cJSON_AddItemToArray(vehicle_pointarray,vehicle_pointentry=cJSON_CreateObject());
	cJSON_AddNumberToObject(vehicle_pointentry,"pointId",2);
	cJSON_AddNumberToObject(vehicle_pointentry,"x",m_configureInfo.channelsInfo[i].aiset.AIvehicle.zoneInfo.right_up_x);
	cJSON_AddNumberToObject(vehicle_pointentry,"y",m_configureInfo.channelsInfo[i].aiset.AIvehicle.zoneInfo.right_up_y);

    cJSON_AddItemToArray(vehicle_pointarray,vehicle_pointentry=cJSON_CreateObject());
	cJSON_AddNumberToObject(vehicle_pointentry,"pointId",3);
	cJSON_AddNumberToObject(vehicle_pointentry,"x",m_configureInfo.channelsInfo[i].aiset.AIvehicle.zoneInfo.left_up_x);
	cJSON_AddNumberToObject(vehicle_pointentry,"y",m_configureInfo.channelsInfo[i].aiset.AIvehicle.zoneInfo.left_up_y);

    //AI-nonmotorvehicle_detection(梯控)
    cJSON *AI_nonmotorvehicle=NULL;
	cJSON_AddItemToObject(AI,"nonmotorvehicle_detection",AI_nonmotorvehicle=cJSON_CreateObject());
    OVD_Printf(OVD_LOGLEVEL_INFO,"nonmotorvehicle_detection_on: %d\n",m_configureInfo.channelsInfo[i].aiset.AInonmotorvehicle.on);
    cJSON_AddBoolToObject(AI_nonmotorvehicle,"on", m_configureInfo.channelsInfo[i].aiset.AInonmotorvehicle.on);
	cJSON_AddStringToObject(AI_nonmotorvehicle,"detect_mode_list",m_configureInfo.channelsInfo[i].aiset.AInonmotorvehicle.detect_mode_list);
    cJSON_AddNumberToObject(AI_nonmotorvehicle,"alarm_sensitivity",m_configureInfo.channelsInfo[i].aiset.AInonmotorvehicle.sensitivity);
    cJSON_AddNumberToObject(AI_nonmotorvehicle,"quality",m_configureInfo.channelsInfo[i].aiset.AInonmotorvehicle.quality);
    cJSON_AddNumberToObject(AI_nonmotorvehicle,"capture_mode",m_configureInfo.channelsInfo[i].aiset.AInonmotorvehicle.capture_mode);
    cJSON_AddNumberToObject(AI_nonmotorvehicle,"scheduled_capture_time",m_configureInfo.channelsInfo[i].aiset.AInonmotorvehicle.scheduled_capture_time);


	cJSON *nonvehlinkage=NULL;
	cJSON_AddItemToObject(AI_nonmotorvehicle,"linkage_strategy",nonvehlinkage=cJSON_CreateObject());
	cJSON *nonvehspeech=NULL;
	cJSON_AddItemToObject(nonvehlinkage,"speech",nonvehspeech=cJSON_CreateObject());
	cJSON_AddBoolToObject(nonvehspeech,"on",m_configureInfo.channelsInfo[i].aiset.AInonmotorvehicle.nonvehlinkage.speech_on);
	cJSON_AddNumberToObject(nonvehspeech,"vol",m_configureInfo.channelsInfo[i].aiset.AInonmotorvehicle.nonvehlinkage.speech_vol);
	cJSON_AddNumberToObject(nonvehspeech,"repeat",m_configureInfo.channelsInfo[i].aiset.AInonmotorvehicle.nonvehlinkage.speech_repeat);
	cJSON_AddStringToObject(nonvehspeech,"url",m_configureInfo.channelsInfo[i].aiset.AInonmotorvehicle.nonvehlinkage.speech_url);
	cJSON *nonvehlight=NULL;
	cJSON_AddItemToObject(nonvehlinkage,"light",nonvehlight=cJSON_CreateObject());
	cJSON_AddBoolToObject(nonvehlight,"on",m_configureInfo.channelsInfo[i].aiset.AInonmotorvehicle.nonvehlinkage.light_on);
	cJSON_AddNumberToObject(nonvehlight,"mode",m_configureInfo.channelsInfo[i].aiset.AInonmotorvehicle.nonvehlinkage.light_mode);
	cJSON_AddNumberToObject(nonvehlight,"dur",m_configureInfo.channelsInfo[i].aiset.AInonmotorvehicle.nonvehlinkage.light_dur);

	cJSON *nonvehbuz=NULL;
	cJSON_AddItemToObject(nonvehlinkage,"buz",nonvehbuz=cJSON_CreateObject());
	cJSON_AddBoolToObject(nonvehbuz,"on",m_configureInfo.channelsInfo[i].aiset.AInonmotorvehicle.nonvehlinkage.buz_on);
	cJSON_AddNumberToObject(nonvehbuz,"dur",m_configureInfo.channelsInfo[i].aiset.AInonmotorvehicle.nonvehlinkage.buz_dur);

	cJSON *nonvehiclezone=NULL;
	nonvehiclezone = cJSON_AddArrayToObject(AI_nonmotorvehicle,"alarm_zone");
	cJSON * nonvehicle_area =NULL;
	cJSON_AddItemToArray(nonvehiclezone,nonvehicle_area = cJSON_CreateObject());
	cJSON_AddNumberToObject(nonvehicle_area,"areaId",0);
	cJSON *nonvehicle_pointarray=NULL;
	cJSON_AddItemToObject(nonvehicle_area,"point",nonvehicle_pointarray=cJSON_CreateArray());
	cJSON *nonvehicle_pointentry=NULL;

	cJSON_AddItemToArray(nonvehicle_pointarray,nonvehicle_pointentry=cJSON_CreateObject());
	cJSON_AddNumberToObject(nonvehicle_pointentry,"pointId",0);
	cJSON_AddNumberToObject(nonvehicle_pointentry,"x",m_configureInfo.channelsInfo[i].aiset.AInonmotorvehicle.zoneInfo.left_bottom_x);
	cJSON_AddNumberToObject(nonvehicle_pointentry,"y",m_configureInfo.channelsInfo[i].aiset.AInonmotorvehicle.zoneInfo.left_bottom_y);

	cJSON_AddItemToArray(nonvehicle_pointarray,nonvehicle_pointentry=cJSON_CreateObject());
	cJSON_AddNumberToObject(nonvehicle_pointentry,"pointId",1);
	cJSON_AddNumberToObject(nonvehicle_pointentry,"x",m_configureInfo.channelsInfo[i].aiset.AInonmotorvehicle.zoneInfo.right_bottom_x);
	cJSON_AddNumberToObject(nonvehicle_pointentry,"y",m_configureInfo.channelsInfo[i].aiset.AInonmotorvehicle.zoneInfo.right_bottom_y);

	cJSON_AddItemToArray(nonvehicle_pointarray,nonvehicle_pointentry=cJSON_CreateObject());
	cJSON_AddNumberToObject(nonvehicle_pointentry,"pointId",2);
	cJSON_AddNumberToObject(nonvehicle_pointentry,"x",m_configureInfo.channelsInfo[i].aiset.AInonmotorvehicle.zoneInfo.right_up_x);
	cJSON_AddNumberToObject(nonvehicle_pointentry,"y",m_configureInfo.channelsInfo[i].aiset.AInonmotorvehicle.zoneInfo.right_up_y);

	cJSON_AddItemToArray(nonvehicle_pointarray,nonvehicle_pointentry=cJSON_CreateObject());
	cJSON_AddNumberToObject(nonvehicle_pointentry,"pointId",3);
	cJSON_AddNumberToObject(nonvehicle_pointentry,"x",m_configureInfo.channelsInfo[i].aiset.AInonmotorvehicle.zoneInfo.left_up_x);
	cJSON_AddNumberToObject(nonvehicle_pointentry,"y",m_configureInfo.channelsInfo[i].aiset.AInonmotorvehicle.zoneInfo.left_up_y);


    //客流统计
    cJSON *AI_passenger=NULL;
    cJSON_AddItemToObject(AI,"passenger_flow_statistics",AI_passenger=cJSON_CreateObject());
    cJSON_AddBoolToObject(AI_passenger,"on", m_configureInfo.channelsInfo[i].aiset.AIPassenger.on);
    cJSON_AddBoolToObject(AI_passenger,"exposure_on", m_configureInfo.channelsInfo[i].aiset.AIPassenger.exposure_on);
    cJSON_AddBoolToObject(AI_passenger,"osd_status",m_configureInfo.channelsInfo[i].aiset.AIPassenger.osd_status);
    cJSON_AddNumberToObject(AI_passenger,"exposure_adjust",m_configureInfo.channelsInfo[i].aiset.AIPassenger.exposure_adjust);
    cJSON_AddStringToObject(AI_passenger,"mode",m_configureInfo.channelsInfo[i].aiset.AIPassenger.mode);
    cJSON_AddNumberToObject(AI_passenger,"quality_value",m_configureInfo.channelsInfo[i].aiset.AIPassenger.quality_value);
    cJSON_AddNumberToObject(AI_passenger,"alarm_sensitivity",m_configureInfo.channelsInfo[i].aiset.AIPassenger.sensitivity);
    cJSON_AddNumberToObject(AI_passenger,"statistics_mode",m_configureInfo.channelsInfo[i].aiset.AIPassenger.statistics_mode);

    cJSON *AI_welcome=NULL;
    cJSON_AddItemToObject(AI_passenger,"welcome_promotion",AI_welcome=cJSON_CreateObject());
    cJSON *welcomespeech=NULL;
    cJSON_AddItemToObject(AI_welcome,"speech",welcomespeech=cJSON_CreateObject());
	cJSON_AddBoolToObject(welcomespeech,"on",m_configureInfo.channelsInfo[i].aiset.AIPassenger.speech.speech_on);
	cJSON_AddNumberToObject(welcomespeech,"vol",m_configureInfo.channelsInfo[i].aiset.AIPassenger.speech.speech_vol);
	cJSON_AddNumberToObject(welcomespeech,"repeat",m_configureInfo.channelsInfo[i].aiset.AIPassenger.speech.speech_repeat);
	cJSON_AddStringToObject(welcomespeech,"url_enter",m_configureInfo.channelsInfo[i].aiset.AIPassenger.speech.speech_url_enter);
    cJSON_AddStringToObject(welcomespeech,"url_leave",m_configureInfo.channelsInfo[i].aiset.AIPassenger.speech.speech_url_leave);
    cJSON *welcomealerttime=NULL;
    cJSON_AddItemToObject(AI_welcome,"alerttime",welcomealerttime=cJSON_CreateObject());
    cJSON_AddStringToObject(welcomealerttime,"starttime",m_configureInfo.channelsInfo[i].aiset.AIPassenger.switchschedule.start_time);
    cJSON_AddStringToObject(welcomealerttime,"endtime",m_configureInfo.channelsInfo[i].aiset.AIPassenger.switchschedule.shutdown_time);
    int passenger_repeat =m_configureInfo.channelsInfo[i].aiset.AIPassenger.switchschedule.repeat;
    OVD_OVCGetRepeat(welcomealerttime,passenger_repeat);

    cJSON *statistcs_line=NULL;
    cJSON_AddItemToObject(AI_passenger,"statistics_line",statistcs_line=cJSON_CreateObject());
    cJSON_AddNumberToObject(statistcs_line,"A_x",m_configureInfo.channelsInfo[i].aiset.AIPassenger.lineInfo.point_a_x);
    cJSON_AddNumberToObject(statistcs_line,"A_y",m_configureInfo.channelsInfo[i].aiset.AIPassenger.lineInfo.point_a_y);
    cJSON_AddNumberToObject(statistcs_line,"B_x",m_configureInfo.channelsInfo[i].aiset.AIPassenger.lineInfo.point_b_x);
    cJSON_AddNumberToObject(statistcs_line,"B_y",m_configureInfo.channelsInfo[i].aiset.AIPassenger.lineInfo.point_b_y);

    cJSON *passengerzone=NULL;
    passengerzone = cJSON_AddArrayToObject(AI_passenger,"alarm_zone");
    cJSON * passenger_area =NULL;
    cJSON_AddItemToArray(passengerzone,passenger_area = cJSON_CreateObject());
    cJSON_AddNumberToObject(passenger_area,"areaId",0);
    cJSON *passenger_pointarray=NULL;
    cJSON_AddItemToObject(passenger_area,"point",passenger_pointarray=cJSON_CreateArray());
    cJSON *passenger_pointentry=NULL;

    cJSON_AddItemToArray(passenger_pointarray,passenger_pointentry=cJSON_CreateObject());

    cJSON_AddNumberToObject(passenger_pointentry,"pointId",0);
    cJSON_AddNumberToObject(passenger_pointentry,"x",m_configureInfo.channelsInfo[i].aiset.AIPassenger.zoneInfo.left_bottom_x);
    cJSON_AddNumberToObject(passenger_pointentry,"y",m_configureInfo.channelsInfo[i].aiset.AIPassenger.zoneInfo.left_bottom_y);

    cJSON_AddItemToArray(passenger_pointarray,passenger_pointentry=cJSON_CreateObject());
    cJSON_AddNumberToObject(passenger_pointentry,"pointId",1);
    cJSON_AddNumberToObject(passenger_pointentry,"x",m_configureInfo.channelsInfo[i].aiset.AIPassenger.zoneInfo.right_bottom_x);
    cJSON_AddNumberToObject(passenger_pointentry,"y",m_configureInfo.channelsInfo[i].aiset.AIPassenger.zoneInfo.right_bottom_y);

    cJSON_AddItemToArray(passenger_pointarray,passenger_pointentry=cJSON_CreateObject());
    cJSON_AddNumberToObject(passenger_pointentry,"pointId",2);
    cJSON_AddNumberToObject(passenger_pointentry,"x",m_configureInfo.channelsInfo[i].aiset.AIPassenger.zoneInfo.right_up_x);
    cJSON_AddNumberToObject(passenger_pointentry,"y",m_configureInfo.channelsInfo[i].aiset.AIPassenger.zoneInfo.right_up_y);

    cJSON_AddItemToArray(passenger_pointarray,passenger_pointentry=cJSON_CreateObject());
    cJSON_AddNumberToObject(passenger_pointentry,"pointId",3);
    cJSON_AddNumberToObject(passenger_pointentry,"x",m_configureInfo.channelsInfo[i].aiset.AIPassenger.zoneInfo.left_up_x);
    cJSON_AddNumberToObject(passenger_pointentry,"y",m_configureInfo.channelsInfo[i].aiset.AIPassenger.zoneInfo.left_up_y);

    // //高空抛物
    // GetAIParabolicAerialEntry(AI, 0);

    // //区域人数统计
    // GetAIRegionalPeopleStatEntry(AI, 0);

    // //车道线
    // GetAILaneLineEntry(AI, 0);

    // //off duty
    // GetAIOffDutyEntry(AI, 0);

    // //头盔检测
    // GetAIHelmetEntry(AI, 0);

    // //手势识别
    // GetAIGestureEntry(AI, 0);

    return NULL;

}

cJSON* CmccOvd::GetAlarmSentry(cJSON *channelentry,int i) {
    cJSON *alarmsentry=NULL;
    cJSON *ioentry=NULL;
    cJSON *voiceentry=NULL;
    cJSON *cryentry=NULL;
    cJSON *cryingpacifyentry=NULL;
    cJSON *motionentry=NULL;
    cJSON *alertareaentry=NULL;
    cJSON *crossentry=NULL;
    cJSON *bodyentry=NULL;
    cJSON *pirentry=NULL;
    //alarms
    cJSON_AddItemToObject(channelentry,"alarms",alarmsentry=cJSON_CreateObject());
    //io
    cJSON_AddItemToObject(alarmsentry,"io",ioentry=cJSON_CreateObject());
    cJSON_AddBoolToObject(ioentry,"on",m_configureInfo.channelsInfo[i].alarms.ioAlarm.on);
    cJSON_AddNumberToObject(ioentry,"sensitivity",m_configureInfo.channelsInfo[i].alarms.ioAlarm.sensitivity);
    //cry
    cJSON_AddItemToObject(alarmsentry,"cry",cryentry=cJSON_CreateObject());
    cJSON_AddBoolToObject(cryentry,"on",m_configureInfo.channelsInfo[i].alarms.cryAlarm.on);
    cJSON_AddNumberToObject(cryentry,"sensitivity",m_configureInfo.channelsInfo[i].alarms.cryAlarm.sensitivity);
    cJSON_AddItemToObject(cryentry,"crying_pacify",cryingpacifyentry=cJSON_CreateObject());
    cJSON_AddBoolToObject(cryingpacifyentry,"on",m_configureInfo.channelsInfo[i].alarms.cryAlarm.crying_pacify_on);
    cJSON_AddNumberToObject(cryingpacifyentry,"audio_playing_count",m_configureInfo.channelsInfo[i].alarms.cryAlarm.crying_pacify_audio_playing_count);
    cJSON_AddNumberToObject(cryingpacifyentry,"audio_playing_volumn",m_configureInfo.channelsInfo[i].alarms.cryAlarm.crying_pacify_audio_playing_volumn);
    cJSON_AddStringToObject(cryingpacifyentry,"audio_playing_url",m_configureInfo.channelsInfo[i].alarms.cryAlarm.crying_pacify_audio_playing_url);


    //voice
    cJSON_AddItemToObject(alarmsentry,"voice",voiceentry=cJSON_CreateObject());
    cJSON_AddBoolToObject(voiceentry,"on",m_configureInfo.channelsInfo[i].alarms.voiceAlarm.on);
    cJSON_AddNumberToObject(voiceentry,"sensitivity",m_configureInfo.channelsInfo[i].alarms.voiceAlarm.sensitivity);
    //motion
    cJSON_AddItemToObject(alarmsentry,"motion",motionentry=cJSON_CreateObject());
    cJSON_AddBoolToObject(motionentry,"on",m_configureInfo.channelsInfo[i].alarms.motionAlarm.on);
    printf("furao2 %d\n",m_configureInfo.channelsInfo[i].alarms.motionAlarm.on);
    cJSON_AddNumberToObject(motionentry,"sensitivity",m_configureInfo.channelsInfo[i].alarms.motionAlarm.sensitivity);

    //alertarea
    cJSON_AddItemToObject(alarmsentry,"alertarea",alertareaentry=cJSON_CreateObject());
    cJSON_AddBoolToObject(alertareaentry,"on",m_configureInfo.channelsInfo[i].alarms.alertareaAlarm.on);
    cJSON_AddBoolToObject(alertareaentry,"expel",m_configureInfo.channelsInfo[i].alarms.alertareaAlarm.expel);
    cJSON_AddNumberToObject(alertareaentry,"sensitivity",m_configureInfo.channelsInfo[i].alarms.alertareaAlarm.sensitivity);
    cJSON *alertareatime=NULL;
    cJSON_AddItemToObject(alertareaentry,"alerttime",alertareatime=cJSON_CreateObject());
    cJSON_AddBoolToObject(alertareatime,"on",m_configureInfo.channelsInfo[i].alarms.alertareaAlarm.areaschedule.on);
    cJSON_AddStringToObject(alertareatime,"starttime",m_configureInfo.channelsInfo[i].alarms.alertareaAlarm.areaschedule.starttime);
    cJSON_AddStringToObject(alertareatime,"endtime",m_configureInfo.channelsInfo[i].alarms.alertareaAlarm.areaschedule.endtime);
    int area_repeat =m_configureInfo.channelsInfo[i].alarms.alertareaAlarm.areaschedule.repeat;
    OVD_OVCGetRepeat(alertareatime,area_repeat);
    cJSON *areazone = NULL;
    areazone = cJSON_AddArrayToObject(alertareaentry,"zone");
    cJSON * alarmarea = NULL;
    int area_count = 0;
    int zone_size = m_capInfoV2.alarm.alertarea_params.alertarea_num;
    for (area_count = 0; area_count < zone_size; area_count++)
    {
        cJSON_AddItemToArray(areazone,alarmarea = cJSON_CreateObject());
        cJSON_AddStringToObject(alarmarea,"on",m_configureInfo.channelsInfo[i].alarms.alertareaAlarm.zoneInfo[area_count].on);  // 1.41
        cJSON_AddNumberToObject(alarmarea,"areaId",area_count);

        cJSON *area_pointarray=NULL;
        cJSON_AddItemToObject(alarmarea,"point",area_pointarray=cJSON_CreateArray());

        cJSON *area_pointentry=NULL;
        cJSON_AddItemToArray(area_pointarray,area_pointentry=cJSON_CreateObject());
        cJSON_AddNumberToObject(area_pointentry,"pointId",0);
        cJSON_AddNumberToObject(area_pointentry,"x",m_configureInfo.channelsInfo[i].alarms.alertareaAlarm.zoneInfo[area_count].left_bottom_x);
        cJSON_AddNumberToObject(area_pointentry,"y",m_configureInfo.channelsInfo[i].alarms.alertareaAlarm.zoneInfo[area_count].left_bottom_y);

        cJSON_AddItemToArray(area_pointarray,area_pointentry=cJSON_CreateObject());
        cJSON_AddNumberToObject(area_pointentry,"pointId",1);
        cJSON_AddNumberToObject(area_pointentry,"x",m_configureInfo.channelsInfo[i].alarms.alertareaAlarm.zoneInfo[area_count].right_bottom_x);
        cJSON_AddNumberToObject(area_pointentry,"y",m_configureInfo.channelsInfo[i].alarms.alertareaAlarm.zoneInfo[area_count].right_bottom_y);

        cJSON_AddItemToArray(area_pointarray,area_pointentry=cJSON_CreateObject());
        cJSON_AddNumberToObject(area_pointentry,"pointId",2);
        cJSON_AddNumberToObject(area_pointentry,"x",m_configureInfo.channelsInfo[i].alarms.alertareaAlarm.zoneInfo[area_count].right_up_x);
        cJSON_AddNumberToObject(area_pointentry,"y",m_configureInfo.channelsInfo[i].alarms.alertareaAlarm.zoneInfo[area_count].right_up_y);

        cJSON_AddItemToArray(area_pointarray,area_pointentry=cJSON_CreateObject());
        cJSON_AddNumberToObject(area_pointentry,"pointId",3);
        cJSON_AddNumberToObject(area_pointentry,"x",m_configureInfo.channelsInfo[i].alarms.alertareaAlarm.zoneInfo[area_count].left_up_x);
        cJSON_AddNumberToObject(area_pointentry,"y",m_configureInfo.channelsInfo[i].alarms.alertareaAlarm.zoneInfo[area_count].left_up_y);
    }

    cJSON *arealinkage=NULL;
    cJSON_AddItemToObject(alertareaentry,"linkage_strategy",arealinkage=cJSON_CreateObject());
    cJSON *areaspeech=NULL;
    cJSON_AddItemToObject(arealinkage,"speech",areaspeech=cJSON_CreateObject());
    cJSON_AddBoolToObject(areaspeech,"on",m_configureInfo.channelsInfo[i].alarms.alertareaAlarm.arealinkage.speech_on);
    cJSON_AddNumberToObject(areaspeech,"vol",m_configureInfo.channelsInfo[i].alarms.alertareaAlarm.arealinkage.speech_vol);
    cJSON_AddNumberToObject(areaspeech,"repeat",m_configureInfo.channelsInfo[i].alarms.alertareaAlarm.arealinkage.speech_repeat);
    cJSON_AddStringToObject(areaspeech,"url",m_configureInfo.channelsInfo[i].alarms.alertareaAlarm.arealinkage.speech_url);
    cJSON *arealight=NULL;
    cJSON_AddItemToObject(arealinkage,"light",arealight=cJSON_CreateObject());
    cJSON_AddBoolToObject(arealight,"on",m_configureInfo.channelsInfo[i].alarms.alertareaAlarm.arealinkage.light_on);
    cJSON_AddNumberToObject(arealight,"mode",m_configureInfo.channelsInfo[i].alarms.alertareaAlarm.arealinkage.light_mode);
    cJSON_AddNumberToObject(arealight,"dur",m_configureInfo.channelsInfo[i].alarms.alertareaAlarm.arealinkage.light_dur);

    cJSON *areabuz=NULL;
    cJSON_AddItemToObject(arealinkage,"buz",areabuz=cJSON_CreateObject());
    cJSON_AddBoolToObject(areabuz,"on",m_configureInfo.channelsInfo[i].alarms.alertareaAlarm.arealinkage.buz_on);
    cJSON_AddNumberToObject(areabuz,"dur",m_configureInfo.channelsInfo[i].alarms.alertareaAlarm.arealinkage.buz_dur);

    cJSON_AddNumberToObject(alertareaentry,"targetType",m_configureInfo.channelsInfo[i].alarms.alertareaAlarm.targetType);
    cJSON_AddNumberToObject(alertareaentry,"staymode_time",m_configureInfo.channelsInfo[i].alarms.alertareaAlarm.staymode_time);

    //越界侦测
    cJSON *transgressionentry=NULL;
    cJSON_AddItemToObject(alarmsentry,"transgression",transgressionentry=cJSON_CreateObject());
    cJSON_AddBoolToObject(transgressionentry,"on",m_configureInfo.channelsInfo[i].alarms.transgressionAlarm.on);
    cJSON_AddNumberToObject(transgressionentry,"targetType",m_configureInfo.channelsInfo[i].alarms.transgressionAlarm.targetType);
    cJSON *transgressiontime=NULL;
    cJSON_AddItemToObject(transgressionentry,"alerttime",transgressiontime=cJSON_CreateObject());
    cJSON_AddBoolToObject(transgressiontime,"on",m_configureInfo.channelsInfo[i].alarms.transgressionAlarm.areaschedule.on);
    cJSON_AddStringToObject(transgressiontime,"starttime",m_configureInfo.channelsInfo[i].alarms.transgressionAlarm.areaschedule.starttime);
    cJSON_AddStringToObject(transgressiontime,"endtime",m_configureInfo.channelsInfo[i].alarms.transgressionAlarm.areaschedule.endtime);
    int transgression_repeat =m_configureInfo.channelsInfo[i].alarms.transgressionAlarm.areaschedule.repeat;
    OVD_OVCGetRepeat(transgressiontime,transgression_repeat);
    cJSON *statistics_line_entry=NULL;
    cJSON_AddItemToObject(transgressionentry,"statistics_line",statistics_line_entry=cJSON_CreateObject());
    cJSON_AddNumberToObject(statistics_line_entry,"A_x",m_configureInfo.channelsInfo[i].alarms.transgressionAlarm.statistics_line_A_x);
    cJSON_AddNumberToObject(statistics_line_entry,"A_y",m_configureInfo.channelsInfo[i].alarms.transgressionAlarm.statistics_line_A_y);
    cJSON_AddNumberToObject(statistics_line_entry,"B_x",m_configureInfo.channelsInfo[i].alarms.transgressionAlarm.statistics_line_B_x);
    cJSON_AddNumberToObject(statistics_line_entry,"B_y",m_configureInfo.channelsInfo[i].alarms.transgressionAlarm.statistics_line_B_y);
    cJSON_AddNumberToObject(transgressionentry,"statistics_mode_style",m_configureInfo.channelsInfo[i].alarms.transgressionAlarm.statistics_mode_style);
    cJSON_AddNumberToObject(transgressionentry,"statistics_mode_direction",m_configureInfo.channelsInfo[i].alarms.transgressionAlarm.statistics_mode_direction);
    cJSON *transgreslinkage=NULL;
    cJSON_AddItemToObject(transgressionentry,"linkage_strategy",transgreslinkage=cJSON_CreateObject());
    cJSON *transgresspeech=NULL;
    cJSON_AddItemToObject(transgreslinkage,"speech",transgresspeech=cJSON_CreateObject());
    cJSON_AddBoolToObject(transgresspeech,"on",m_configureInfo.channelsInfo[i].alarms.transgressionAlarm.arealinkage.speech_on);
    cJSON_AddNumberToObject(transgresspeech,"vol",m_configureInfo.channelsInfo[i].alarms.transgressionAlarm.arealinkage.speech_vol);
    cJSON_AddNumberToObject(transgresspeech,"repeat",m_configureInfo.channelsInfo[i].alarms.transgressionAlarm.arealinkage.speech_repeat);
    cJSON_AddStringToObject(transgresspeech,"url",m_configureInfo.channelsInfo[i].alarms.transgressionAlarm.arealinkage.speech_url);
    cJSON *transgreslight=NULL;
    cJSON_AddItemToObject(transgreslinkage,"light",transgreslight=cJSON_CreateObject());
    cJSON_AddBoolToObject(transgreslight,"on",m_configureInfo.channelsInfo[i].alarms.transgressionAlarm.arealinkage.light_on);
    cJSON_AddNumberToObject(transgreslight,"mode",m_configureInfo.channelsInfo[i].alarms.transgressionAlarm.arealinkage.light_mode);
    cJSON_AddNumberToObject(transgreslight,"dur",m_configureInfo.channelsInfo[i].alarms.transgressionAlarm.arealinkage.light_dur);

    cJSON *transgresbuz=NULL;
    cJSON_AddItemToObject(transgreslinkage,"buz",transgresbuz=cJSON_CreateObject());
    cJSON_AddBoolToObject(transgresbuz,"on",m_configureInfo.channelsInfo[i].alarms.transgressionAlarm.arealinkage.buz_on);
    cJSON_AddNumberToObject(transgresbuz,"dur",m_configureInfo.channelsInfo[i].alarms.transgressionAlarm.arealinkage.buz_dur);


    //cross
    cJSON_AddItemToObject(alarmsentry,"cross",crossentry=cJSON_CreateObject());
    cJSON_AddBoolToObject(crossentry,"on",m_configureInfo.channelsInfo[i].alarms.crossAlarm.on);
    cJSON_AddNumberToObject(crossentry,"sensitivity",m_configureInfo.channelsInfo[i].alarms.crossAlarm.sensitivity);

    //body
    cJSON_AddItemToObject(alarmsentry,"body",bodyentry=cJSON_CreateObject());
    cJSON_AddBoolToObject(bodyentry,"on",m_configureInfo.channelsInfo[i].alarms.bodyAlarm.on);
    cJSON_AddNumberToObject(bodyentry,"sensitivity",m_configureInfo.channelsInfo[i].alarms.bodyAlarm.sensitivity);

    cJSON *bodyzone=NULL;
    bodyzone= cJSON_AddArrayToObject(bodyentry,"zone");
    int body_count=0;
    cJSON * bodyarea =NULL;
    cJSON_AddItemToArray(bodyzone,bodyarea=cJSON_CreateObject());
    cJSON_AddNumberToObject(bodyarea,"areaId",body_count);
    cJSON *body_pointarray=NULL;
    cJSON_AddItemToObject(bodyarea,"point",body_pointarray=cJSON_CreateArray());
    cJSON *body_pointentry = NULL;
    cJSON_AddItemToArray(body_pointarray,body_pointentry=cJSON_CreateObject());
    cJSON_AddNumberToObject(body_pointentry,"pointId",0);
    cJSON_AddNumberToObject(body_pointentry,"x",m_configureInfo.channelsInfo[i].alarms.bodyAlarm.zoneInfo.left_bottom_x);
    cJSON_AddNumberToObject(body_pointentry,"y",m_configureInfo.channelsInfo[i].alarms.bodyAlarm.zoneInfo.left_bottom_y);


    cJSON_AddItemToArray(body_pointarray,body_pointentry=cJSON_CreateObject());
    cJSON_AddNumberToObject(body_pointentry,"pointId",1);
    cJSON_AddNumberToObject(body_pointentry,"x",m_configureInfo.channelsInfo[i].alarms.bodyAlarm.zoneInfo.right_bottom_x);
    cJSON_AddNumberToObject(body_pointentry,"y",m_configureInfo.channelsInfo[i].alarms.bodyAlarm.zoneInfo.right_bottom_y);

    cJSON_AddItemToArray(body_pointarray,body_pointentry=cJSON_CreateObject());
    cJSON_AddNumberToObject(body_pointentry,"pointId",2);
    cJSON_AddNumberToObject(body_pointentry,"x",m_configureInfo.channelsInfo[i].alarms.bodyAlarm.zoneInfo.right_up_x);
    cJSON_AddNumberToObject(body_pointentry,"y",m_configureInfo.channelsInfo[i].alarms.bodyAlarm.zoneInfo.right_up_y);

    cJSON_AddItemToArray(body_pointarray,body_pointentry=cJSON_CreateObject());
    cJSON_AddNumberToObject(body_pointentry,"pointId",3);
    cJSON_AddNumberToObject(body_pointentry,"x",m_configureInfo.channelsInfo[i].alarms.bodyAlarm.zoneInfo.left_up_x);
    cJSON_AddNumberToObject(body_pointentry,"y",m_configureInfo.channelsInfo[i].alarms.bodyAlarm.zoneInfo.left_up_y);

    //pir
    cJSON_AddItemToObject(alarmsentry,"pir", pirentry=cJSON_CreateObject());
    cJSON_AddBoolToObject(pirentry,"on",m_configureInfo.channelsInfo[i].alarms.pirAlarm.on);
    cJSON_AddNumberToObject(pirentry,"sensitivity",m_configureInfo.channelsInfo[i].alarms.pirAlarm.sensitivity);
    //losslock
    cJSON_AddItemToObject(alarmsentry,"losslock", pirentry=cJSON_CreateObject());
    cJSON_AddBoolToObject(pirentry,"on",m_configureInfo.channelsInfo[i].alarms.lossLockAlarm.on);

    cJSON_AddNumberToObject(channelentry,"audio_out_volume",m_configureInfo.channelsInfo[i].audioOutValume);
    cJSON_AddNumberToObject(channelentry,"doorbell_volume",m_configureInfo.channelsInfo[i].doorbell_volume);
    cJSON_AddBoolToObject(channelentry,"trace",m_configureInfo.channelsInfo[i].traceAbility);
    return NULL;
}

cJSON* CmccOvd::OVD_GetOVDChannelConfigureInfoJson(int channel) {

    cJSON *video_encodingentry=NULL;
    cJSON *audio_encodingentry=NULL;
    cJSON *switchentry=NULL;
	cJSON *imageentry=NULL;

	cJSON *osdentry=NULL;
	cJSON *osdposentry=NULL;
	cJSON *osdcolorentry=NULL;
	cJSON *osdlogoentry=NULL;

    cJSON *dashcam_info_entry = NULL;
    cJSON *alarm_duration_entry = NULL;
    cJSON *driving_collision_entry = NULL;
    cJSON *voice_capture_entry = NULL;
    cJSON *osdtemp=NULL;

    cJSON *channelentry = cJSON_CreateObject();

    int i = channel;
    //cJSON_AddNumberToObject(channelentry,"channel",m_configureInfo.channelsInfo[i].channel);

    cJSON_AddBoolToObject(channelentry,"on",m_configureInfo.channelsInfo[i].on);


    cJSON_AddItemToObject(channelentry,"video_encoding",video_encodingentry=cJSON_CreateObject());

    //cJSON_AddStringToObject(video_encodingentry,"encoder",m_configureInfo->channelsInfo[i].videoinfo.codec);
    //当前只支持h264
    //cJSON_AddStringToObject(video_encodingentry,"encoder","h264");

    if (source_STREAM_CODEC_H265 == m_configureInfo.channelsInfo[i].videoinfo.codec)
    {
        cJSON_AddStringToObject(video_encodingentry,"encoder","h265");
    }else
    {
        cJSON_AddStringToObject(video_encodingentry,"encoder","h264");
    }
    cJSON_AddBoolToObject(video_encodingentry,"static_scence_ABR_on",m_configureInfo.channelsInfo[i].static_scence_ABR_on);



    if(m_configureInfo.channelsInfo[i].videoinfo.quality == OVD_1DMODE)
        cJSON_AddStringToObject(video_encodingentry,"quality","ld");
    else if(m_configureInfo.channelsInfo[i].videoinfo.quality == OVD_SDMODE)
        cJSON_AddStringToObject(video_encodingentry,"quality","sd");
    else if(m_configureInfo.channelsInfo[i].videoinfo.quality == OVD_HDMODE )
        cJSON_AddStringToObject(video_encodingentry,"quality","hd");
    else if(m_configureInfo.channelsInfo[i].videoinfo.quality == OVD_FHDMODE)
        cJSON_AddStringToObject(video_encodingentry,"quality","fhd");

    cJSON_AddNumberToObject(video_encodingentry,"fps",m_configureInfo.channelsInfo[i].videoinfo.framerate);
    cJSON_AddNumberToObject(video_encodingentry,"bitrate",m_configureInfo.channelsInfo[i].videoinfo.bitrate);
    cJSON_AddNumberToObject(video_encodingentry,"width",m_configureInfo.channelsInfo[i].videoinfo.width);
    cJSON_AddNumberToObject(video_encodingentry,"height",m_configureInfo.channelsInfo[i].videoinfo.height);
    cJSON_AddNumberToObject(video_encodingentry,"gop",m_configureInfo.channelsInfo[i].videoinfo.frameInterval);

    //1.44 帧率、码率设置
    cJSON_AddNumberToObject(video_encodingentry,"bitrate_mode",m_configureInfo.channelsInfo[i].bitrate_mode);
    cJSON_AddNumberToObject(video_encodingentry,"bitrate_average",m_configureInfo.channelsInfo[i].bitrate_average);
    cJSON_AddNumberToObject(video_encodingentry,"bitrate_peak",m_configureInfo.channelsInfo[i].bitrate_peak);
    cJSON_AddNumberToObject(video_encodingentry,"bitrate_statically",m_configureInfo.channelsInfo[i].bitrate_statically);
    cJSON_AddNumberToObject(video_encodingentry,"framerate",m_configureInfo.channelsInfo[i].framerate);

    //cJSON_AddBoolToObject(video_encodingentry)

    //audio_encoding
    cJSON_AddItemToObject(channelentry,"audio_encoding",audio_encodingentry=cJSON_CreateObject());
    cJSON_AddStringToObject(audio_encodingentry,"encoder","aac");
    cJSON_AddNumberToObject(audio_encodingentry,"sample_rate",m_configureInfo.channelsInfo[i].audioinfo.samplesRate);
    cJSON_AddNumberToObject(audio_encodingentry,"bitrate",m_configureInfo.channelsInfo[i].audioinfo.bitrate);
    cJSON_AddNumberToObject(audio_encodingentry,"bits_per_sample",m_configureInfo.channelsInfo[i].audioinfo.bitsPerSample);
    cJSON_AddNumberToObject(audio_encodingentry,"sample_per_frame",m_configureInfo.channelsInfo[i].audioinfo.sampleperframe);
    cJSON_AddNumberToObject(audio_encodingentry,"channel",m_configureInfo.channelsInfo[i].audioinfo.channelNumber);

    // 是否有音柱设备
    cJSON_AddNumberToObject(channelentry, "Speeker", m_configureInfo.channelsInfo[i].has_speek);

    //image
    cJSON_AddItemToObject(channelentry,"image",imageentry=cJSON_CreateObject());
    cJSON_AddBoolToObject(imageentry,"horflip",m_configureInfo.channelsInfo[i].flipInfo.horflip);
    cJSON_AddBoolToObject(imageentry,"verflip",m_configureInfo.channelsInfo[i].flipInfo.verflip);

    GetAISentry(channelentry,i);
    GetAlarmSentry(channelentry,i);

    //switch_schedule
    cJSON_AddItemToObject(channelentry,"switch_schedule",switchentry=cJSON_CreateObject());
    cJSON_AddBoolToObject(switchentry,"on",m_configureInfo.channelsInfo[i].switchschedule.on);
    cJSON_AddStringToObject(switchentry,"start_time",m_configureInfo.channelsInfo[i].switchschedule.start_time);
    cJSON_AddStringToObject(switchentry,"shutdown_time",m_configureInfo.channelsInfo[i].switchschedule.shutdown_time);
    int repeat =m_configureInfo.channelsInfo[i].switchschedule.repeat;
    OVD_OVCGetRepeat(switchentry,repeat);
    //行车记录仪
    cJSON_AddItemToObject(channelentry,"driving_recorder",dashcam_info_entry=cJSON_CreateObject());
    cJSON_AddNumberToObject(dashcam_info_entry,"collision_alarm_sensitivity",m_configureInfo.channelsInfo[i].dashcam_info.collision_alarm_sensitivity);
    cJSON_AddItemToObject(dashcam_info_entry,"alarm_duration",alarm_duration_entry=cJSON_CreateObject());
    cJSON_AddNumberToObject(alarm_duration_entry,"parking_notice",m_configureInfo.channelsInfo[i].dashcam_info.alarm_duration.parking_notice);
    cJSON_AddNumberToObject(alarm_duration_entry,"parking_collision",m_configureInfo.channelsInfo[i].dashcam_info.alarm_duration.parking_collision);
    cJSON_AddItemToObject(alarm_duration_entry,"driving_collision",driving_collision_entry=cJSON_CreateObject());
    cJSON_AddNumberToObject(driving_collision_entry,"before",m_configureInfo.channelsInfo[i].dashcam_info.alarm_duration.driving_collision.before);
    cJSON_AddNumberToObject(driving_collision_entry,"after",m_configureInfo.channelsInfo[i].dashcam_info.alarm_duration.driving_collision.after);
    cJSON_AddItemToObject(alarm_duration_entry,"voice_capture",voice_capture_entry=cJSON_CreateObject());
    cJSON_AddNumberToObject(voice_capture_entry,"before",m_configureInfo.channelsInfo[i].dashcam_info.alarm_duration.voice_capture.before);
    cJSON_AddNumberToObject(voice_capture_entry,"after",m_configureInfo.channelsInfo[i].dashcam_info.alarm_duration.voice_capture.after);
    //osd
    cJSON_AddItemToObject(channelentry,"osd_text",osdtemp=cJSON_CreateArray());
    int osd_num=0;
    int osd_count=m_configureInfo.channelsInfo[i].osdcount;
    for (osd_num=0;osd_num<osd_count;osd_num++) {
        osdentry = cJSON_CreateObject();
        cJSON_AddBoolToObject(osdentry,"on", m_configureInfo.channelsInfo[i].osdinfo[osd_num].on);
        cJSON_AddStringToObject(osdentry,"text",m_configureInfo.channelsInfo[i].osdinfo[osd_num].text);
        cJSON_AddNumberToObject(osdentry,"fontsize",m_configureInfo.channelsInfo[i].osdinfo[osd_num].osdfont);
        cJSON_AddNumberToObject(osdentry,"mode",m_configureInfo.channelsInfo[i].osdinfo[osd_num].mode);
        cJSON_AddNumberToObject(osdentry,"id",m_configureInfo.channelsInfo[i].osdinfo[osd_num].id);

        cJSON_AddItemToObject(osdentry,"pos",osdposentry=cJSON_CreateObject());
        cJSON_AddNumberToObject(osdposentry,"x",m_configureInfo.channelsInfo[i].osdinfo[osd_num].osdpos.x);
        cJSON_AddNumberToObject(osdposentry,"y",m_configureInfo.channelsInfo[i].osdinfo[osd_num].osdpos.y);

        cJSON_AddItemToObject(osdentry,"color",osdcolorentry=cJSON_CreateObject());
        cJSON_AddNumberToObject(osdcolorentry,"Red",m_configureInfo.channelsInfo[i].osdinfo[osd_num].color.red);
        cJSON_AddNumberToObject(osdcolorentry,"Green",m_configureInfo.channelsInfo[i].osdinfo[osd_num].color.green);
        cJSON_AddNumberToObject(osdcolorentry,"Blue",m_configureInfo.channelsInfo[i].osdinfo[osd_num].color.blue);
        cJSON_AddItemToArray(osdtemp, osdentry);
    }
    cJSON_AddNumberToObject(channelentry,"normal_nightvision_mode",m_configureInfo.normal_nightvision_mode);
    cJSON_AddNumberToObject(channelentry,"color_nightvision_mode",m_configureInfo.color_nightvision_mode);
    cJSON_AddNumberToObject(channelentry,"nightvision_detect_mode",m_configureInfo.nightvision_detect_mode);

    cJSON_AddBoolToObject(channelentry,"AI_rules_mode_on",m_configureInfo.AI_rules_mode_on);

    cJSON_AddItemToObject(channelentry,"osd_logo",osdlogoentry=cJSON_CreateObject());
    cJSON_AddBoolToObject(osdlogoentry,"on", m_configureInfo.channelsInfo[i].osd_logo);

    cJSON *channel_rebot=NULL;
    cJSON_AddItemToObject(channelentry,"robotKeeper",channel_rebot=cJSON_CreateObject());
    cJSON *channel_extra=NULL;
    cJSON_AddItemToObject(channel_rebot,"extraInfo",channel_extra=cJSON_CreateObject());
    cJSON *channel_remind=NULL;
    cJSON_AddItemToObject(channel_extra,"remindConfig",channel_remind=cJSON_CreateArray());
    cJSON *pointentry=NULL;
    int rebot_num = 0;
    for(rebot_num=0;rebot_num<m_configureInfo.robotcount;rebot_num++){

        cJSON_AddItemToArray(channel_remind,pointentry=cJSON_CreateObject());
        cJSON_AddNumberToObject(pointentry,"bizType",m_configureInfo.robot_home_config[rebot_num].bizType);
        cJSON_AddStringToObject(pointentry,"startTime",m_configureInfo.robot_home_config[rebot_num].start_time);
        cJSON_AddStringToObject(pointentry,"endTime",m_configureInfo.robot_home_config[rebot_num].end_time);
    }

    return channelentry;
}

int CmccOvd::OVD_SetOVDConfigureInfo(char* in_ovdconfig,char* err_msg_info,int str_len){
    OVD_Printf(OVD_LOGLEVEL_INFO,"OVD_SetOVDConfigureInfo,str_len %d \n",str_len);
    return 0;
}  

int CmccOvd::OVD_GetOVDChannelConfigureInfo(int channel, char** output_ovdconfig, int *len){
    int ret;
    cJSON *channelentry = OVD_GetOVDChannelConfigureInfoJson(channel);
    if (channelentry == NULL)
    {
        return -1;
    }

    int buf_size = OVD_MAX_JSON_REND_SIZE;
    char *json_buffer = (char *)malloc(buf_size);
    ret = cJSON_PrintPreallocated(channelentry, json_buffer, buf_size, 0);
    cJSON_Delete(channelentry);
    if (ret == 1)
    {
        *output_ovdconfig = json_buffer;
        *len = strlen(json_buffer) + sizeof("");
        return 0;
    }
    else
    {
        free(json_buffer);
        return -1;
    }
}

void CmccOvd::OVD_OVCConnectStatus(int connectStatus) {
    OVD_Printf(OVD_LOGLEVEL_INFO,"当前与ovc的连接状态是 %d\n",connectStatus);    
    return;
}
int CmccOvd::OVD_ReBootChannel(int channel) {
    OVD_Printf(OVD_LOGLEVEL_INFO,"channel重启 %d\n",channel);
    return 0;
}

int CmccOvd::OVD_SetRebootInfo(ovd_reboot_param_t *rebootinfo) {
    // iniFileLoad(CFG_FILEPATH);
    // iniSetInt("rebootinfo","reason", rebootinfo->reason,10);
    // iniSetInt("rebootinfo","ts",rebootinfo->ts,10);
    // iniSetString("rebootinfo","message",rebootinfo->message);
    // iniFileFree();
    return 0;
}

int CmccOvd::OVD_ReBootDevice(ovd_reboot_reason_e reason) {
    OVD_Printf(OVD_LOGLEVEL_INFO,"设备重启... %d sleep 2s\n",reason);
    ovd_reboot_param_t rebootinfo;
    rebootinfo.reason=reason;
     struct timeval tv = {0};

    gettimeofday(&tv, NULL);
    rebootinfo.ts= tv.tv_sec;
    strncpy(rebootinfo.message,"sdk call", sizeof(rebootinfo.message)-1);
    OVD_SetRebootInfo(&rebootinfo);

	//exit(0);
 }
 
int CmccOvd::OVD_GetGpsInfo(ovd_gps_info_t *gpsinfo) {
    //行车记录仪功能，暂为空。
    return 0;
 }

 OVD_int32 CmccOvd::OVD_FirmwareBinUpgrade(OVD_int32 channel_id, OVD_Upgrade_cmd_e cmd, void* data, size_t size, void* user_data){
    //暂为空
    return 0;
}

OVD_int32 CmccOvd::OVD_FirmwareUpgradeStatusCb(OVD_int32 channel_id, OVD_UpgradeConfirmInfo_t *info) {
    // time_t timetemp; //时间获取
    // struct tm *p;
    // time(&timetemp);
    // p = localtime(&timetemp);

    // sprintf(info->last_upgrade_time, "%d-%02d-%02dT%02d:%02d:%02d", 1900 + p->tm_year, p->tm_mon, p->tm_mday, p->tm_hour, p->tm_min, p->tm_sec);
    // info->confirm = OVD_UPGRADE_CONFIRM_SUCCUSS;

    // iniFileLoad(FILEPATH);
    // iniGetString("deviceConfiguration", "firmware_model", info->cur_version, sizeof(info->cur_version),"V1.0.0");
    // iniFileFree();

    return 0;
}

int CmccOvd::OVD_SetSDCardFormat() {
    OVD_Printf(OVD_LOGLEVEL_DEBUG,"begin to OVD_SetSDCardFormat, sd card formated\n");
    return 0;
}

ovd_ptz_cmd_code CmccOvd::OVD_PTZCmd(OVD_int32 channel, OVCPTZControlCmd ptzcmd, OVD_int32 ptzvalue) {
    //暂为空
    return OVD_PTZ_OK;
}


int CmccOvd::OVD_GetPresetList(int channel,int *presetList,int array_num, int *count) {
    OVD_Printf(OVD_LOGLEVEL_DEBUG,"begin to OVD_GetPresetList\n");
    int i=0;

    int num=0;
    for(i=0;i<256;i++)
    {
        if(m_presetlist[i]==1)
        {
            presetList[num]=i;
            num++;
        }
    }

    *count = num;


    return 0;
}    

int CmccOvd::OVD_ForceIFrame(int channel, OVDCodeStream code_stream) {
    OVD_Printf(OVD_LOGLEVEL_WARN,"demo device force i frame,channel:%d,code_stream:%d\r\n",channel,code_stream);
    return 0;
}

int CmccOvd::OVD_Snapshot(int channel,OVDImageInfo *imageInfo, int maxImageSize) {
    // int ret = 0;
    // int size = 0;
    // int buf_size = 0;
    // char *buf = NULL;
    // struct stat stat_buf;

    // ret = stat(snapshotFILEPATH, &stat_buf);
//     if(ret){

//         OVD_Printf(OVD_LOGLEVEL_INFO,"%s %d: [demo] stat pic filepath %s fail\n",__func__,__LINE__,snapshotFILEPATH);
//         return -1;

//     }
//     size =(int)stat_buf.st_size;

//     buf = (char *)malloc(size);

//     FILE *f = fopen(snapshotFILEPATH, "rb");
//     if(!f){
//         ret = -1;
//         goto _exit1;
//     }

//    buf_size = fread(buf, 1, size, f);
//    OVD_Printf(OVD_LOGLEVEL_INFO,"%s %d: [demo]=== buf_size:%d ===\n",__func__,__LINE__,buf_size);

//     if(size > maxImageSize){
//         imageInfo->size = size;
//         ret = -2;
//         goto _exit2;
//     }

//     memcpy(imageInfo->buf, buf, size);
//     imageInfo->size = size;

// _exit2:
//     fclose(f);
// _exit1:
//     free(buf);
    return 0;
}  
  
//告警状态通知回调
int CmccOvd::OVD_AlarmNotifyCalback(OVD_AlarmNotifyType_e type, const void* buf, const unsigned int len) {
    int *value;
    OVD_AlarmNotifyMsg_t *msg;
    switch (type)
    {
    case ALARMNOTIFYTYPE_SETMINDURATION:
        value=(int*)buf;
        OVD_Printf(OVD_LOGLEVEL_INFO,"===设置状态告警最短时长：%ds====\n",*value);
        break;
    case ALARMNOTIFTYPE_SETMAXDURATION:
        value=(int*) buf;
        OVD_Printf(OVD_LOGLEVEL_INFO,"===设置状态告警最长时长：%ds===\n",*value);
        break;
    case ALARMNOTIFYTYPE_ALARMEXTEND:
        msg=(OVD_AlarmNotifyMsg_t *)buf;
        OVD_Printf(OVD_LOGLEVEL_INFO,"===通道:%d 告警类型:%d 因过早结束，而被sdk自动延长===\n",msg->channel,msg->type);
        break;
    
    case ALARMNOTIFYTYPE_ALARMTRUNCATE:
        msg=(OVD_AlarmNotifyMsg_t *)buf;
        OVD_Printf(OVD_LOGLEVEL_INFO,"===通道:%d 告警类型:%d 因告警过长，而被sdk自动截断===\n",msg->channel,msg->type);
        break;

    
    default:
        break;
    }
    return 0;
}

int CmccOvd::OVD_SetMp3Url(int channel,const char *url,int repeat, int volume) {
	// OVD_Printf(OVD_LOGLEVEL_DEBUG,"OVD_SetAudioOutPlay channel:%d url:%s repeat:%d volume:%d\r\n",channel,url,repeat,volume);

    // if(NULL != Mp3PlayUrl){
    //     free(Mp3PlayUrl);
    // }

    // Mp3PlayUrl = (char*)malloc(strlen(url)+1);
    // if(NULL == Mp3PlayUrl){
    //     return -1;
    // }

    // memcpy(Mp3PlayUrl, url, strlen(url)+1);
    // Mp3PlayStatus = MP3_OTHER;

    return 0;
}

int CmccOvd::OVD_KeepAwakenUtilExpired(int channel, int notAllowHibernate, int expired, OVDHibernateReason reason) {
    time_t i;
	i=time(NULL);
	OVD_Printf(OVD_LOGLEVEL_INFO,"%d\n",i);
    OVD_Printf(OVD_LOGLEVEL_INFO,"channel %d will keep awake until %d 秒, notAllowHibernate %d, reason %d\n",channel,expired,notAllowHibernate,reason);
    m_keepAwakeTime = i+expired;

	return 0;
}

int CmccOvd::OVD_ResetConfig() {
    OVD_Printf(OVD_LOGLEVEL_INFO,"device will reset all configuration\n");
	return 0;
}


OVD_int32 CmccOvd::OVD_gettime(OVD_uint64 *out_time) {
    struct timeval tv;
    system("date");
    gettimeofday(&tv, NULL);

    printf("tv_sec: %d\n", tv.tv_sec);
    printf("tv_usec: %d\n",tv.tv_usec);

    *out_time=(unsigned long long )tv.tv_sec*1000+ (long long)tv.tv_usec/1000;
    return 0;
}

/*
由厂商实现设置系统时间戳函数，精确度为毫秒
in: input_time  设置的时间戳， 单位为毫秒
in: tolerance_value 设置的容忍值，单位为秒,即当前系统时间戳和预设置的时间相差小于容忍值，则不需要设置系统时间
*/
OVD_int32 CmccOvd::OVD_settime(OVD_uint64 input_time, OVD_int32 tolerance_value) {
    unsigned long long cur_time=0;
    OVD_gettime(&cur_time);

    struct timeval set_tv;
    set_tv.tv_sec=input_time/1000;
    set_tv.tv_usec=(input_time%1000)*1000;

    printf("OVD_settime: input_time: %lld ms, cur_time: %lld ms, diff %d s \n",input_time,cur_time,tolerance_value);

    if(cur_time > input_time)
    {
        if((cur_time - input_time)>tolerance_value*1000)
        {
            settimeofday(&set_tv,NULL);
        }
    }
    else
    {
        if((input_time - cur_time)>tolerance_value*1000)
        {
            settimeofday(&set_tv,NULL);
        }
    }
    system("date");
    return 0;
}

int CmccOvd::OVD_getsimpleovdinfo(SimpleOVDinfo *outinfo) {
    int i=0;
    outinfo->state = (ovd_state)m_iStatus;
    outinfo->channelcount = m_configureInfo.channelsInfoCount;
    Simplechannelinfo *channelarray = NULL;
    if(outinfo->channelcount <= 4){
        channelarray = outinfo->channelarray;
    }
    else {
        // 超过4个channel以上的设备，则使用ext_channelarray，由厂商自行分配内存
        outinfo->ext_channelarray = (Simplechannelinfo *)malloc(outinfo->channelcount*sizeof(Simplechannelinfo));
        channelarray = outinfo->ext_channelarray;
    }
    if (m_capInfoV2.device_type == DEVICE_TYPE_NVR)
    {
        for(i=0;i<outinfo->channelcount;i++)
        {
            channelarray[i].channelnum=m_configureInfo.channelsInfo[i].channel;
            channelarray[i].state=OVD_CHANNEL_UNUSE; //此state代表该channel是否物理在线
            sprintf(channelarray[i].name, "cam_%d", i);
        }
    }
    else if (m_capInfoV2.device_type == DEVICE_TYPE_MIPC)
    {
        outinfo->channelcount = m_configureInfo.channelsInfoCount;
        for (i = 0; i<outinfo->channelcount;i++)
        {
            channelarray[i].channelnum=m_configureInfo.channelsInfo[i].channel;
            channelarray[i].state=OVD_CHANNEL_ONLINE; //此state代表该channel是否物理在线
        }
    }
    else
    {
        outinfo->channelcount = 1;
        outinfo->channelarray[0].channelnum=m_configureInfo.channelsInfo[0].channel;
        outinfo->channelarray[0].state=OVD_CHANNEL_ONLINE; //此state代表该channel是否物理在线
    }

    return 0;
}
  
int CmccOvd::OVD_GetDiskInfo(int *out_state,int *out_total, int *out_free) {
    // OVD_Printf(OVD_LOGLEVEL_DEBUG,"begin to OVD_GetDiskInfo\n");
    // *out_state=g_sdcardstate;
    // *out_total=1000;
    // *out_free=g_freedisk;
    // OVD_Printf(OVD_LOGLEVEL_INFO,"==out_state:%d, out_total:%d M , out_free: %d M",g_sdcardstate,*out_total,*out_free);
    return 0;
 }

int CmccOvd::OVD_LogUploadAsync(char *trans_id, char *start, char* end,char* url) {
    //暂为空
    return 0;
}

OVD_int32 CmccOvd::OVD_DMEAPI_callback_RecordSearch2(OVD_int32 channel, OVD_uint64 starttime, OVD_uint64 endtime, OVD_int32 page,
            OVD_int32 numInPage, OVD_DMERecordFileListPerPage *fileinpage) {
    return 0;                
}

void* CmccOvd::OVD_DMEAPI_callback_RecordOpen(int channel) {
    return nullptr;
}

int CmccOvd::OVD_DMEAPI_callback_RecordSeek(void* ctx, long long timestamp) {
    return 0;
 }

int CmccOvd::OVD_DMEAPI_callback_RecordReadFrame(void* ctx, OVD_FrameInfo *pframe_info) {
    return 0;
}

int CmccOvd::OVD_DMEAPI_callback_RecordSpeedReadFrame(void* ctx, ovd_playback_speed_times_e speed, OVD_FrameInfo *pframe_info) {
    return 0;
 }

int CmccOvd::OVD_DMEAPI_callback_RecordClose(void *ctx) {
    return 0;
}

int CmccOvd::OVD_StopAlarm(OVDAlarmType alarmtype) {
    return 0;
}

int CmccOvd::OVD_EBOCmd(OVCEBOControlCmd cmd,void* data) {
    return 0;
}

int CmccOvd::OVD_GetDevRunningInfo(ovd_probe_devrunning_info_e info_e, void* data) {
    return 0;
}

int CmccOvd::OVD_ROBOTCmd(ovd_robot_content_t* cmd,void* data) {
    OVD_Printf(OVD_LOGLEVEL_DEBUG,"action:%s,type:%s,data:%s\n",cmd->action,cmd->type,cmd->data);
    ovd_common_info_t *rebot_data = (ovd_common_info_t *)data;
    if(strcmp(cmd->action, "getSurplusGrain") == 0) {
        cJSON *root= cJSON_CreateObject();
        cJSON_AddNumberToObject(root,"SurplusGrain",0);
        cJSON_PrintPreallocated(root, rebot_data->buf, rebot_data->size, 0);
        OVD_Printf(OVD_LOGLEVEL_INFO,"rebot_data data:%s,len:%d\n", rebot_data->buf, rebot_data->size);
    }
    
    return 0;
}

int CmccOvd::OVD_LogNotifyCb(const OVD_char *log_path) {
    OVD_Printf(OVD_LOGLEVEL_DEBUG,"OVD_LogNotifyCb called\n");
    
    return 0;
}

long long  CmccOvd::OVD_getutcMicroSec()
{
    struct timeval tv;
    gettimeofday(&tv,NULL);
    long long utcms=tv.tv_sec*1000000+tv.tv_usec;
	return utcms;
}

GstFlowReturn CmccOvd::cb_call_sink_new_sample(GstElement *appsink,
                                           gpointer user_data) {

    // GstV4l2Pipeline *gstV4L2Pipeline =
    //         reinterpret_cast<GstV4l2Pipeline *>(user_data);

    GstSample *sample = NULL;
    GstBuffer *buffer = NULL;
    GstMapInfo map;
    const GstStructure *info = NULL;
    GstCaps *caps = NULL;
    GstFlowReturn ret = GST_FLOW_OK;
    int sample_width = 0;
    int sample_height = 0;

    g_signal_emit_by_name(appsink, "pull-sample", &sample, &ret);
    if (ret != GST_FLOW_OK) {
        g_printerr("can't pull GstSample.\n");
        return ret;
    }

    if (sample && (!CmccOvd::m_bEndPushFlag)) {
        long long localTimeStamp = OVD_getutcMicroSec();
        buffer = gst_sample_get_buffer(sample);
        if (buffer == NULL) {
            g_printerr("get buffer is null\n");
            goto exit;
        }

        gst_buffer_map(buffer, &map, GST_MAP_READ);
        
        //caps = gst_sample_get_caps(sample);
        //if (caps == NULL) {
        //    g_printerr("get caps is null\n");
        //    goto exit;
        //}

        //info = gst_caps_get_structure(caps, 0);
        //if (info == NULL) {
        //    g_printerr("get info is null\n");
        //    goto exit;
        //}

        // ---- Read frame and convert to opencv format ---------------
        // convert gstreamer data to OpenCV Mat, you could actually
        // resolve height / width from caps...
        //gst_structure_get_int(info, "width", &sample_width);
        //gst_structure_get_int(info, "height", &sample_height);
       
        // appsink product queue produce
        {
            if (map.data == NULL) {
                g_printerr("appsink buffer data empty\n");
                return GST_FLOW_NOT_LINKED;
            }           
            
            bool isIFlame = false;

            if((0 == (map.data)[0]) && (0 == (map.data)[1]) && (0 == (map.data)[2]) && (1 == (map.data)[3]) && (0x67 == (map.data)[4])) {
                isIFlame = true;
                OVD_Printf(OVD_LOGLEVEL_ERROR,"----->data size is %d\n", map.size);
                printf("I frame get success\n");
            }

            CmccOvd::m_FakeVideoTimeStamp = CmccOvd::m_FakeVideoTimeStamp + 33;

            if(CmccOvd::m_FakeVideoTimeStamp + 100 < (unsigned long long)localTimeStamp/1000) {
                CmccOvd::m_FakeVideoTimeStamp = (unsigned long long)localTimeStamp/1000;
            }
            
            //printf("fake timestamp is %llu\n", CmccOvd::m_FakeVideoTimeStamp);
            //printf("real timestamp is %llu\n", (unsigned long long)localTimeStamp/1000);
            OVD_AVPushData(0, OVD_HIGH_STREAM, OVD_Video, isIFlame, map.data, map.size, CmccOvd::m_FakeVideoTimeStamp);
        }
    }

    goto exit;

    exit:
    if (buffer) {
        gst_buffer_unmap(buffer, &map);
    }
    if (sample) {
        gst_sample_unref(sample);
    }
    return GST_FLOW_OK;
}

int CmccOvd::pushHeadSensorData() {
    OVD_setloglevel(OVD_LOGLEVEL_DEBUG);
    
    if(nullptr != m_HeadPipeline) {
        OVDVideoDataFormat videoFmt;
        videoFmt.codec = source_STREAM_CODEC_H264;
        videoFmt.framerate = 30;
        videoFmt.frameInterval = 50;

        OVDAudioDataFormat audioFmt;
        audioFmt.codec = source_STREAM_CODEC_AAC_WITH_ADTS;
        audioFmt.samplesRate = 16000;
        audioFmt.channelNumber = 1;
        OVD_AVPushStart(0, OVD_HIGH_STREAM, &videoFmt, &audioFmt);
        //OVD_AVPushStart(0, OVD_HIGH_STREAM, &videoFmt, nullptr);       
        //gst_element_set_state(m_HeadPipeline, GST_STATE_PLAYING);

        //m_pushStartFlag = true;
        return 0;
    }
    
    OVD_ServiceStart();

    GstElement *sink;
    void* test;

    gst_init(nullptr, nullptr);
    // pipeline = gst_parse_launch(
    //         "v4l2src device=/dev/video0 ! image/jpeg,width=1920,height=1080,framerate=25/1 ! "
    //         "jpegdec ! videoconvert ! timeoverlay ! mpph264enc header-mode=1 profile=66 level=31 rc-mode=cbr bps-min=800000 bps= 2000000 bps-max=2500000 gop=50 ! "
    //         "appsink name=callSink",
    //         NULL);

    bool loopFlag = true;
    struct stat buffer;
    const char *filePath = "/tmp/foo_fhd";

    // 循环检查socket-path是否存在
    while (loopFlag) {
        if (stat(filePath, &buffer) == 0) {
            // 文件存在，退出循环
            printf("File exists.\n");
            loopFlag = false;
        } else {
            // 文件不存在，打印消息并 sleep 10ms
            printf("File does not exist. Waiting...\n");
            struct timespec ts;
            ts.tv_sec = 0;
            ts.tv_nsec = 10 * 1000 * 1000;  // 10ms
            nanosleep(&ts, NULL);
        }
    }
       
    sleep(1);
    
    m_HeadPipeline = gst_parse_launch(
            "./gst-launch-1.0 shmsrc socket-path=/tmp/foo_fhd ! appsink name=callSink",
            NULL);

    sleep(2);
    
    if(nullptr == m_HeadPipeline) {
        g_printerr("gst_parse_launch fail\n");
        printf("gst_parse_launch fail.\n");
        return -1;
    }

    sink = gst_bin_get_by_name(GST_BIN(m_HeadPipeline), "callSink");    
    g_object_set(sink, "emit-signals", TRUE, NULL);
    g_signal_connect(sink, "new-sample", G_CALLBACK(cb_call_sink_new_sample),test);
    gst_pipeline_set_auto_flush_bus(GST_PIPELINE(m_HeadPipeline), true);  
    
    int cnt = 0;

    while (cnt++ < 6) {
        GstStateChangeReturn ret = gst_element_set_state(m_HeadPipeline, GST_STATE_PLAYING);
        if (ret == GST_STATE_CHANGE_SUCCESS) return 0;
            
        struct timespec ts;
        ts.tv_sec = 0;
        ts.tv_nsec = 20 * 1000 * 1000;  // 10ms
        nanosleep(&ts, NULL);
        printf("Waiting for gst playing ready\n");
    }

    return -1;
}

int CmccOvd::stopPushHeadSensorData() {
    // if (nullptr != m_HeadPipeline) {   
    //     // 可选：先暂停管道
    //     gst_element_set_state(m_HeadPipeline, GST_STATE_PAUSED);  
    //     // 设置管道为 NULL 状态，释放资源
    //     gst_element_set_state(m_HeadPipeline, GST_STATE_NULL);
    //     // 释放管道引用
    //     gst_object_unref(m_HeadPipeline);
    //     m_HeadPipeline = nullptr;
    // }

    // if (nullptr != m_HeadPipeline) {
    //     gst_element_set_state(m_HeadPipeline, GST_STATE_PAUSED);
    //     m_pushStartFlag = false;
    // }
    CmccOvd::m_bEndPushFlag = true;

    sleep(2);

    OVD_AVPushEnd(0, OVD_HIGH_STREAM);

    CmccOvd::m_bEndPushFlag =false;
    return 0;
}


int CmccOvd::pushNeckSensorData() {
    return 0;
}

int CmccOvd::stopPushNeckSensorData() {
    if (m_NeckPipeline) {
        // 可选：先暂停管道
        gst_element_set_state(m_NeckPipeline, GST_STATE_PAUSED);
    
        // 设置管道为 NULL 状态，释放资源
        gst_element_set_state(m_NeckPipeline, GST_STATE_NULL);
    
        // 释放管道引用
        gst_object_unref(m_NeckPipeline);
        m_NeckPipeline = nullptr;
    }
    
    //释放pipeline
    OVD_AVPushEnd(0, OVD_HIGH_STREAM);

    return 0;
}

int CmccOvd::pushAudioData(unsigned char* aacData, int dataSize) {
    int ret = -1;
    if(!CmccOvd::m_bEndPushFlag) {
        long long localTimeStamp = OVD_getutcMicroSec();
        
        CmccOvd::m_FakeAudioTimeStamp = CmccOvd::m_FakeAudioTimeStamp + 64;

        if(CmccOvd::m_FakeAudioTimeStamp + 200 < (unsigned long long)localTimeStamp/1000) {
            CmccOvd::m_FakeAudioTimeStamp = (unsigned long long)localTimeStamp/1000;
        }
                
        //ADTS
        ret = OVD_AVPushData(0, OVD_HIGH_STREAM, OVD_Audio, false, aacData, dataSize, m_FakeAudioTimeStamp);
    }

    return ret;
}

int CmccOvd::setStreamState(bool bStreamState) {
    int ret = -1;

    if(m_pushStartFlag) {
        if(bStreamState){
            gst_element_set_state(m_HeadPipeline, GST_STATE_PLAYING);
        } else {
            gst_element_set_state(m_HeadPipeline, GST_STATE_PAUSED);        
        }

        ret = 0;
        // OVD_AVPushStop(0, OVD_HIGH_STREAM);
    }

    return ret;
}

void CmccOvd::init() {
    int result = 0;
    int init_result =-1;

    OVD_setloglevel(OVD_LOGLEVEL_DEBUG);
    OVD_Printf(OVD_LOGLEVEL_DEBUG,"====================设备上电启动====================\n");
    OVD_Printf(OVD_LOGLEVEL_DEBUG,"=======设备相关信息都放在目录configure文件夹下=======\n");
    OVD_Printf(OVD_LOGLEVEL_DEBUG,"======================SDK初始化======================\n");
    result = ovdDeviceInit();
    if(0 != result){
        OVD_Printf(OVD_LOGLEVEL_ERROR,"----->初始化文件失败，请检查配置文件，重新启动<-----\n");
        // return -1;
    }

    OVDCapInfoV2_t demo_cap;
    memset(m_jsonParam,0,sizeof(m_jsonParam));
	prepareJsonParam(m_jsonParam);

    ovd_reboot_param_t reboot_info;
    ovdGetRebootInfo(&reboot_info);
    setCallBackFunList(&m_callBackFunList);
    ovdInitCapbility(&demo_cap);    
    init_result = OVD_InitV2(&demo_cap, &m_clientParam, &m_logParam, &reboot_info, &m_callBackFunList, m_jsonParam);

    OVD_setloglevel(OVD_LOGLEVEL_DEBUG);    
}