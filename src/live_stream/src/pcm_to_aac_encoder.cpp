#include "pcm_to_aac_encoder.h"

int PcmToAacEncoder::init() {
    unsigned long SampleRate = 16000;
    unsigned int numChannels = 1;
    m_nPCMBitSize = 16;
    m_hEncoder = faacEncOpen(SampleRate, numChannels, &m_nInputSamples, &m_nMaxOutputBytes);
    if (m_hEncoder == nullptr) {
        printf("faac enc open failed\n");
        return -1;
    }
        
    //计算每次输入pcm数据大小
    m_nMaxInputBytes = m_nInputSamples * m_nPCMBitSize / 8;

    printf("......inputSamples = %lu, maxOutputBytes = %lu\n", m_nInputSamples, m_nMaxOutputBytes);

         
    //分配空间用于存储aac数据
    m_AacBuffer = new unsigned char[m_nMaxOutputBytes];
               
    pConfiguration = faacEncGetCurrentConfiguration(m_hEncoder);
    pConfiguration->inputFormat = FAAC_INPUT_16BIT; //位深16
    pConfiguration->outputFormat = 1;   //ADTS
    pConfiguration->useTns = 0;
    pConfiguration->useLfe = 0;
    pConfiguration->aacObjectType = LOW;    //AAC-LC
    pConfiguration->mpegVersion = MPEG4;
    pConfiguration->shortctl = 0;//SHORTCTL_NORMAL    0
    pConfiguration->quantqual = 100;
    pConfiguration->bandWidth = 0;
    pConfiguration->bitRate = 0;
    
    //设置编码参数
    int ret = faacEncSetConfiguration(m_hEncoder, pConfiguration);
    if(-1 == ret) {
        printf("faacEncSetConfiguration set fail\n");
        return -1;
    }
 
    return 0;
}
    
int PcmToAacEncoder::encode(const uint8_t* pcmData, int dataSize, unsigned char** ppAacData) {
    int ret = 0;
    unsigned long inputSamples = dataSize / (m_nPCMBitSize / 8);
    if(inputSamples > m_nInputSamples) {
        printf("dataSize is too long");
        return -1;
    }

    ret = faacEncEncode(m_hEncoder, (int32_t*)pcmData, inputSamples, m_AacBuffer, m_nMaxOutputBytes);
    printf(".......ret=%d\n", ret);
    *ppAacData = m_AacBuffer;

    return ret;
}
    
void PcmToAacEncoder::unInit() {
    if (nullptr != m_hEncoder) {
        faacEncClose(m_hEncoder);
    }

    //释放内存
    if (nullptr != m_AacBuffer) {
        delete []m_AacBuffer;
    }
}