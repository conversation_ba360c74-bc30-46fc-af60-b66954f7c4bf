#include "rclcpp/rclcpp.hpp"
#include "live_stream_node.h"
#include <iostream>
#include <chrono>
#include <string>
#include <csignal>  
#include <signal.h>

OVDDeviceInfo CmccOvd::m_deviceInfo;
OVDConfigrationInfo CmccOvd::m_configureInfo;
int CmccOvd::dutMTU = 1500;
OVD_DEMO_Capinfo_t CmccOvd::m_capInfoV2;
int CmccOvd::m_iStatus = 0;
int CmccOvd::m_presetlist[256] = {0};
int CmccOvd::m_keepAwakeTime = 0;
unsigned long long CmccOvd::m_FakeAudioTimeStamp = 0;
unsigned long long CmccOvd::m_FakeVideoTimeStamp = 0;
bool CmccOvd::m_bEndPushFlag = false;

int main(int argc, char **argv)
{

    // ------------------ 启动ROS ------------------ 
    /*
    ROS_INFO("deep_udp_ctrl init"); // 输出初始化日志
    ros::init(argc, argv, "deep_udp_ctrl_node"); 
    ros::NodeHandle nh;
    // auto udp_ctrl = make_shared<DeepUdpCtrl>(nh);    // 创建UDP控制类的实例
    udp_ctrl = make_shared<DeepUdpCtrl>(nh);    // 创建UDP控制类的实例
    
    // socket通信
    // udp_ctrl->initSocket("192.168.2.1", 43891, 43893);
    // udp_ctrl->initSocket("192.168.31.208", 43891, 43893);
    udp_ctrl->initSocket("192.168.1.120", 6688, 43893);
    // udp_ctrl->initSocket("192.168.1.120", 43897, 43893); // 连上3D打印机器后控制

    ros::Duration(0.2).sleep();// 等待一段时间确保UDP通信初始化完成
    // udp_ctrl->StandUp();
    */
    rclcpp::init(argc, argv);
       
    std::shared_ptr<LiveSteamNode> live_stream = std::make_shared<LiveSteamNode>();
    
    live_stream->init();

    live_stream->liveStreaming();
    
    printf("live stream test.......................\n");
    // 进入 ROS 2 事件循环
    rclcpp::spin(live_stream);
    rclcpp::shutdown();

    return 0;
}