#include "live_stream_task_manager.h"
#include <ctime>

LiveStreamTaskManager::LiveStreamTaskManager(const char* localTaskFilepath) {
    do {
        FILE *fp;

        strcpy(m_localTaskFilename, localTaskFilepath);

        memset(&m_taskListMem[0], 0, sizeof(LiveStreamTaskInfo)*MAX_TASK_COUNT);

        int fileSize = getFileSize(localTaskFilepath);
        if(0 == fileSize) {
            //文件为空
            printf("LiveStreamTaskManager: task file is empty\n");
            break;
        } else if(-1 == fileSize) {
            //文件不存在，创建文件
            FILE* newFile = fopen(localTaskFilepath, "w");
            if(newFile != nullptr) {
                fclose(newFile);
                printf("LiveStreamTaskManager: file create success %s\n", localTaskFilepath);
            } else {
                printf("LiveStreamTaskManager: file create fail!\n");
            }
            
            break;
        }

        char* fileData = nullptr;
        fileData = new char[fileSize + 1];
        if(nullptr == fileData){
            printf("LiveStreamTaskManager: malloc fail data fail!\n");
        }
        fp = fopen(localTaskFilepath,"rb");  
        fread(fileData,1,fileSize,fp);  
        fclose(fp);
        
        int ret = parseLocalFile(fileData, fileSize + 1, m_taskListMem);
        if(ret != 0) {
            printf("LiveStreamTaskManager: parse local task fail!\n");            
        }
        delete[] fileData;

    } while(false);
}


LiveStreamTaskManager::~LiveStreamTaskManager() {
    if(nullptr != m_taskJson) {
        cJSON_Delete(m_taskJson);
    }
}

//解析json字符串，解析出任务类型、任务id、开始时间、结束时间等信息。
int LiveStreamTaskManager::parseLocalFile(const char* fileData, int dataSize, LiveStreamTaskInfo* taskInfoList) {
    int ret = 0;
    cJSON *arrayItem,*item,*object, *weekDaysArray, *weekDays;
    do {
        m_taskJson = cJSON_Parse(fileData);  
        if(nullptr == m_taskJson) {  
            printf("LiveStreamTaskManager,json parse error: [%s]\n",cJSON_GetErrorPtr());
            break;  
        }

        arrayItem = cJSON_GetObjectItem(m_taskJson, "tasks");
        if(nullptr == arrayItem) {  
            printf("LiveStreamTaskManager: get json item error!\n");
            break;
        }
        

        int itemSize = cJSON_GetArraySize(arrayItem);
        if(itemSize > MAX_TASK_COUNT) {
            printf("LiveStreamTaskManager: task count is too large!\n");
            itemSize = MAX_TASK_COUNT;
        }
        //printf("cJSON_GetArraySize: size=%d\n", itemSize);
        for(int i = 0; i < itemSize; i++) {  
            object = cJSON_GetArrayItem(arrayItem,i);

            //
            item = cJSON_GetObjectItem(object,"type");
            if((item->valueint >= (int)TASK_TYPE_MAX) || (item->valueint < 0)) {
                printf("LiveStreamTaskManager: task type is invalid!\n");
                continue;
            }            
            m_taskListMem[i].taskType = (LiveStreamTaskType)item->valueint;
            
            item = cJSON_GetObjectItem(object,"id");
            m_taskListMem[i].taskId = item->valueint;

            //
            item = cJSON_GetObjectItem(object,"year");
            m_taskListMem[i].year = item->valueint;

            //
            item = cJSON_GetObjectItem(object,"month");
            m_taskListMem[i].month = item->valueint;

            //
            item = cJSON_GetObjectItem(object,"dayOfMonth");
            m_taskListMem[i].dayOfMonth = item->valueint;

            //["Mon","Tue","Wed","Thu","Fri","Sat","Sun"]
            weekDaysArray = cJSON_GetObjectItem(object,"weekdays");
            cJSON_ArrayForEach(weekDays, weekDaysArray) {
                if (strcmp(weekDays->valuestring, "Mon") == 0) {
                    m_taskListMem[i].weekdays[1] = true;
                    printf("LiveStreamTaskManager: 11!\n");
                }

                if (strcmp(weekDays->valuestring, "Tue") == 0) {
                    m_taskListMem[i].weekdays[2] = true;
                    printf("LiveStreamTaskManager: 22!\n");
                }

                if (strcmp(weekDays->valuestring, "Wed") == 0) {
                    m_taskListMem[i].weekdays[3] = true;
                    printf("LiveStreamTaskManager: 33!\n");
                }

                if (strcmp(weekDays->valuestring, "Thu") == 0) {
                    m_taskListMem[i].weekdays[4] = true;
                    printf("LiveStreamTaskManager: 44!\n");
                }

                if (strcmp(weekDays->valuestring, "Fri") == 0) {
                    m_taskListMem[i].weekdays[5] = true;
                    printf("LiveStreamTaskManager: 55!\n");
                }

                if (strcmp(weekDays->valuestring, "Sat") == 0) {
                    m_taskListMem[i].weekdays[6] = true;
                    printf("LiveStreamTaskManager: 66!\n");
                }

                if (strcmp(weekDays->valuestring, "Sun") == 0) {
                    m_taskListMem[i].weekdays[7] = true;
                    printf("LiveStreamTaskManager: 77!\n");
                }
            }

            //
            item = cJSON_GetObjectItem(object,"startSecond");
            m_taskListMem[i].startSecond = item->valueint;

            //
            item = cJSON_GetObjectItem(object,"endSecond");
            m_taskListMem[i].endSecond = item->valueint;
        }

        

    } while(false);


    return ret;
}


void LiveStreamTaskManager::parseTask(const char* taskStr) {
    int ret = -1;
    do {
        LiveStreamTaskInfo taskInfo = {0};
        //解析json·字符串，解析出任务类型、任务id、直播流地址、开始时间、结束时间等信息。
        cJSON *platformTaskJason = nullptr, *body = nullptr, *data = nullptr;
        cJSON *time = nullptr, *secondOfDay = nullptr, *endSecondOfDay = nullptr, *weekDaysArray = nullptr, *weekDays = nullptr;
        cJSON *id = nullptr, *repeatType = nullptr, *dayOfMonth = nullptr, *month = nullptr, *year = nullptr, *changeType = nullptr;
        platformTaskJason =  cJSON_Parse(taskStr);
        // body = cJSON_GetObjectItem(platformTaskJason, "body");
        // changeType = cJSON_GetObjectItem(body, "changeType");
        // data = cJSON_GetObjectItem(body, "data");
        printf("a111111111111111111111111111111111111111!\n");
        
        id = cJSON_GetObjectItem(platformTaskJason, "id");
        if(nullptr == id) {
            printf("LiveStreamTaskManager: id is null!\n");
            break;          
        }
        printf("b111111111111111111111111111111111111111!\n");
        
        time = cJSON_GetObjectItem(platformTaskJason, "time");
        if(nullptr == time) {
            printf("LiveStreamTaskManager: time is null!\n");
            break;          
        }
        printf("c111111111111111111111111111111111111111!\n");
        
        repeatType= cJSON_GetObjectItem(time, "repeatType");
        if(nullptr == repeatType) {
            printf("LiveStreamTaskManager: repeatType is null!\n");
            break;          
        }
        printf("d111111111111111111111111111111111111111!\n");
        
        year = cJSON_GetObjectItem(time, "year");
        if(nullptr == year) {
            printf("LiveStreamTaskManager: year is null!\n");
            break;          
        }
        printf("e111111111111111111111111111111111111111!\n");
        
        month = cJSON_GetObjectItem(time, "month");
        if(nullptr == month) {
            printf("LiveStreamTaskManager: month is null!\n");
            break;          
        }
        printf("f111111111111111111111111111111111111111!\n");
        
        weekDaysArray = cJSON_GetObjectItem(time, "weekDays");
        // if(nullptr == weekDaysArray) {
        //     printf("LiveStreamTaskManager: weekDaysArray is null!\n");
        //     break;          
        // }
        printf("g111111111111111111111111111111111111111!\n");
        
        dayOfMonth = cJSON_GetObjectItem(time, "dayOfMonth");
        if(nullptr == dayOfMonth) {
            printf("LiveStreamTaskManager: dayOfMonth is null!\n");
            break;          
        }
        printf("h111111111111111111111111111111111111111!\n");
        
        secondOfDay = cJSON_GetObjectItem(time, "secondOfDay");
        if(nullptr == secondOfDay) {
            printf("LiveStreamTaskManager: secondOfDay is null!\n");
            break;          
        }
        printf("i111111111111111111111111111111111111111!\n");
        
        endSecondOfDay = cJSON_GetObjectItem(time, "endSecondOfDay");
        if(nullptr == endSecondOfDay) {
            printf("LiveStreamTaskManager: endSecondOfDay is null!\n");
            break;          
        }

        printf("j111111111111111111111111111111111111111!\n");
        // taskInfo.taskId = (long long)cJSON_GetNumberValue(id);
        taskInfo.taskId = (long long)id->valueint;
        taskInfo.taskType = (LiveStreamTaskType)repeatType->valueint;
        taskInfo.year = year->valueint;
        taskInfo.month = month->valueint;
        
        if(nullptr != weekDaysArray){
            cJSON_ArrayForEach(weekDays, weekDaysArray) {
                if (strcmp(weekDays->valuestring, "Mon") == 0) {
                    taskInfo.weekdays[0] = true;
                }

                if (strcmp(weekDays->valuestring, "Tue") == 0) {
                    taskInfo.weekdays[1] = true;
                }

                if (strcmp(weekDays->valuestring, "Wed") == 0) {
                    taskInfo.weekdays[2] = true;
                }

                if (strcmp(weekDays->valuestring, "Thu") == 0) {
                    taskInfo.weekdays[3] = true;
                }

                if (strcmp(weekDays->valuestring, "Fri") == 0) {
                    taskInfo.weekdays[4] = true;
                }

                if (strcmp(weekDays->valuestring, "Sat") == 0) {
                    taskInfo.weekdays[5] = true;
                }

                if (strcmp(weekDays->valuestring, "Sun") == 0) {
                    taskInfo.weekdays[6] = true;
                }    
            
            }
        }

        printf("k111111111111111111111111111111111111111!\n");
        taskInfo.dayOfMonth = dayOfMonth->valueint;
        taskInfo.startSecond = secondOfDay->valueint;
        taskInfo.endSecond = endSecondOfDay->valueint;

        // if(1 == changeType->valueint) {
        //     addTask(&taskInfo);
        // } else if(2 == changeType->valueint) {
        //     updateTask(&taskInfo);
        // } else if(3 == changeType->valueint) {
        //     deleteTask(taskInfo.taskId);
        // }

        addTask(&taskInfo);
        printf("l111111111111111111111111111111111111111!\n");

        ret = 0;
    } while(false);

    if(-1 == ret){
        printf("LiveStreamTaskManager: parse fail!\n");
    } else {
        printf("LiveStreamTaskManager: parse success!\n");
    }
    
}

void LiveStreamTaskManager::addTask(LiveStreamTaskInfo* taskInfo) {
    for (int i = 0; i < MAX_TASK_COUNT; i++) {
        if (taskInfo->taskId == m_taskListMem[i].taskId) {
            memcpy(&m_taskListMem[i], taskInfo, sizeof(LiveStreamTaskInfo));
            break;
        } else if(0 == m_taskListMem[i].taskId) {
            memcpy(&m_taskListMem[i], taskInfo, sizeof(LiveStreamTaskInfo));
            break;
        }
    }

    cJSON *arrayItem, *task;
    arrayItem = cJSON_GetObjectItem(m_taskJson, "tasks");

    int indexToDelete = -1;
    int currentIndex = 0;
    
    //为避免平台重复下发，先删除掉重复id任务
    cJSON_ArrayForEach(task, arrayItem) {
        cJSON *idItem = cJSON_GetObjectItem(task, "id");
        if (idItem && idItem->valueint == taskInfo->taskId) {
            indexToDelete = currentIndex;
            break;
        }
        currentIndex++;
    }

    if (indexToDelete != -1) {
        cJSON_DeleteItemFromArray(arrayItem, indexToDelete);
    }

    // 创建新的任务对象
    cJSON* newTask = cJSON_CreateObject();
    cJSON_AddNumberToObject(newTask, "id", taskInfo->taskId);
    cJSON_AddNumberToObject(newTask, "type", (int)taskInfo->taskType);
    cJSON_AddNumberToObject(newTask, "year", taskInfo->year);
    cJSON_AddNumberToObject(newTask, "month", taskInfo->month);

    cJSON* weekdaysArray = cJSON_CreateArray();
    if(taskInfo->weekdays[0]) {
        cJSON_AddItemToArray(weekdaysArray, cJSON_CreateString("Mon"));
    }
    if(taskInfo->weekdays[1]) {
        cJSON_AddItemToArray(weekdaysArray, cJSON_CreateString("Tue"));
    }
    if(taskInfo->weekdays[2]) {
        cJSON_AddItemToArray(weekdaysArray, cJSON_CreateString("Wed"));
    }
    if(taskInfo->weekdays[3]) {
        cJSON_AddItemToArray(weekdaysArray, cJSON_CreateString("Thu"));
    }
    if(taskInfo->weekdays[4]) {
        cJSON_AddItemToArray(weekdaysArray, cJSON_CreateString("Fri"));
    }
    if(taskInfo->weekdays[5]) {
        cJSON_AddItemToArray(weekdaysArray, cJSON_CreateString("Sat"));
    }                
    if(taskInfo->weekdays[6]) {
        cJSON_AddItemToArray(weekdaysArray, cJSON_CreateString("Sun"));
    } 

    cJSON_AddItemToObject(newTask, "weekdays", weekdaysArray);

    cJSON_AddNumberToObject(newTask, "dayOfMonth", taskInfo->dayOfMonth);
    cJSON_AddNumberToObject(newTask, "startSecond", taskInfo->startSecond);
    cJSON_AddNumberToObject(newTask, "endSecond", taskInfo->endSecond);

    cJSON_AddItemToArray(arrayItem, newTask);

    saveJsonToFile(m_taskJson);
}

void LiveStreamTaskManager::updateTask(LiveStreamTaskInfo* taskInfo) {
    for (int i = 0; i < MAX_TASK_COUNT; i++) {
        if (taskInfo->taskId == m_taskListMem[i].taskId) {
            memcpy(&m_taskListMem[i], taskInfo, sizeof(LiveStreamTaskInfo));
            break;
        }
    }

    cJSON *arrayItem, *task;
    arrayItem = cJSON_GetObjectItem(m_taskJson, "tasks");

    int indexToDelete = -1;
    int currentIndex = 0;
    cJSON_ArrayForEach(task, arrayItem) {
        cJSON *idItem = cJSON_GetObjectItem(task, "id");
        if (idItem && idItem->valueint == taskInfo->taskId) {
            indexToDelete = currentIndex;
            break;
        }
        currentIndex++;
    }

    if (indexToDelete != -1) {
        cJSON_DeleteItemFromArray(arrayItem, indexToDelete);
    }

    // 创建新的任务对象
    cJSON* newTask = cJSON_CreateObject();
    cJSON_AddNumberToObject(newTask, "id", taskInfo->taskId);
    cJSON_AddNumberToObject(newTask, "type", (int)taskInfo->taskType);
    cJSON_AddNumberToObject(newTask, "year", taskInfo->year);
    cJSON_AddNumberToObject(newTask, "month", taskInfo->month);

    cJSON* weekdaysArray = cJSON_CreateArray();
    if(taskInfo->weekdays[0]) {
        cJSON_AddItemToArray(weekdaysArray, cJSON_CreateString("Mon"));
    }
    if(taskInfo->weekdays[1]) {
        cJSON_AddItemToArray(weekdaysArray, cJSON_CreateString("Tue"));
    }
    if(taskInfo->weekdays[2]) {
        cJSON_AddItemToArray(weekdaysArray, cJSON_CreateString("Wed"));
    }
    if(taskInfo->weekdays[3]) {
        cJSON_AddItemToArray(weekdaysArray, cJSON_CreateString("Thu"));
    }
    if(taskInfo->weekdays[4]) {
        cJSON_AddItemToArray(weekdaysArray, cJSON_CreateString("Fri"));
    }
    if(taskInfo->weekdays[5]) {
        cJSON_AddItemToArray(weekdaysArray, cJSON_CreateString("Sat"));
    }                
    if(taskInfo->weekdays[6]) {
        cJSON_AddItemToArray(weekdaysArray, cJSON_CreateString("Sun"));
    } 

    cJSON_AddItemToObject(newTask, "weekdays", weekdaysArray);

    cJSON_AddNumberToObject(newTask, "dayOfMonth", taskInfo->dayOfMonth);
    cJSON_AddNumberToObject(newTask, "startSecond", taskInfo->startSecond);
    cJSON_AddNumberToObject(newTask, "endSecond", taskInfo->endSecond);

    cJSON_AddItemToArray(arrayItem, newTask);

    saveJsonToFile(m_taskJson);
}

void LiveStreamTaskManager::deleteTask(int taskId) {
    for (int i = 0; i < MAX_TASK_COUNT; i++) {
        if (taskId == m_taskListMem[i].taskId) {
            memset(&m_taskListMem[i], 0, sizeof(LiveStreamTaskInfo));
            break;
        }
    }

    cJSON *arrayItem, *task;
    arrayItem = cJSON_GetObjectItem(m_taskJson, "tasks");

    int indexToDelete = -1;
    int currentIndex = 0;
    cJSON_ArrayForEach(task, arrayItem) {
        cJSON *idItem = cJSON_GetObjectItem(task, "id");
        if (idItem && idItem->valueint == taskId) {
            indexToDelete = currentIndex;
            break;
        }
        currentIndex++;
    }

    if (indexToDelete != -1) {
        cJSON_DeleteItemFromArray(arrayItem, indexToDelete);
    }

    saveJsonToFile(m_taskJson);
}

int LiveStreamTaskManager::getFileSize(const char *filepath) {      
    if(NULL == filepath)
        return 0;

    struct stat filestat;
    memset(&filestat,0,sizeof(struct stat));
    /*获取文件信息*/
    if(0 == stat(filepath,&filestat))
        return (int)filestat.st_size;
    else
        return -1;
}

int LiveStreamTaskManager::saveJsonToFile(cJSON* localTaskJson) {
    // 打开文件，使用写模式
    FILE* fp = fopen(m_localTaskFilename, "w");
    if (fp == nullptr) {
        printf("LiveStreamTaskManager: Failed to open file %s for writing!\n", m_localTaskFilename);
        return -1;
    }

    // 将json对象转换为字符串
    char* jsonString = cJSON_Print(localTaskJson);
    if (jsonString == nullptr) {
        printf("LiveStreamTaskManager: Failed to print json to string!\n");
        fclose(fp);
        return -1;
    }

    // 将字符串写入文件
    fputs(jsonString, fp);

    // 释放json字符串内存
    free(jsonString);

    // 关闭文件
    fclose(fp);

    return 0;

}

//遍历任务列表，检查当前时间是否满足某个任务的执行条件
bool LiveStreamTaskManager::queryCurrentState() {
    bool taskState = false;
    for (int i = 0; i < MAX_TASK_COUNT; i++) {
        if (m_taskListMem[i].taskId != 0) {            
        // printf("LiveStreamTaskManager: i = %d\n", i);
        // printf("LiveStreamTaskManager: taskId=%lld, taskType=%d, year=%d, month=%d, dayOfMonth=%d, startSecond=%d, endSecond=%d\n",
        //     m_taskListMem[i].taskId,
        //     m_taskListMem[i].taskType,
        //     m_taskListMem[i].year,
        //     m_taskListMem[i].month,
        //     m_taskListMem[i].dayOfMonth,
        //     m_taskListMem[i].startSecond,
        //     m_taskListMem[i].endSecond);
            // 检查当前时间是否满足任务执行条件
            time_t now = std::time(nullptr);
            tm *localTime = std::localtime(&now);
            switch (m_taskListMem[i].taskType)
            {
                case TASK_TYPE_ONCE:
                    // 只执行一次的任务，检查当前时间是否在任务设定的开始时间和结束时间内
                    if ((localTime->tm_year + 1900 == m_taskListMem[i].year) && (localTime->tm_mon + 1 == m_taskListMem[i].month) && (m_taskListMem[i].dayOfMonth == localTime->tm_mday)) {
                        printf("local time: %d-%d-%d\n",localTime->tm_year + 1900, localTime->tm_mon + 1, localTime->tm_mday);
                        printf("task time: %d-%d-%d\n",m_taskListMem[i].year,m_taskListMem[i].month,m_taskListMem[i].dayOfMonth);
                        printf("start second:%d\n", m_taskListMem[i].startSecond);
                        printf("end second:%d\n", m_taskListMem[i].endSecond);
                        printf("now second:%d\n", localTime->tm_hour * 3600 + localTime->tm_min * 60 + localTime->tm_sec);
                                              
                        if ((localTime->tm_hour * 3600 + localTime->tm_min * 60 + localTime->tm_sec >= m_taskListMem[i].startSecond) &&
                            (localTime->tm_hour * 3600 + localTime->tm_min * 60 + localTime->tm_sec <= m_taskListMem[i].endSecond)) {
                            printf("taskState is true\n");
                            taskState = true;
                        }                
                    }                
                    break;
                case TASK_TYPE_MONTHLY:
                    // 每月执行的任务，检查当前时间是否在任务设定的开始时间和结束时间内
                    if ((localTime->tm_mon + 1 == m_taskListMem[i].month) && (m_taskListMem[i].dayOfMonth == localTime->tm_mday)) {
                        if ((localTime->tm_hour * 3600 + localTime->tm_min * 60 + localTime->tm_sec >= m_taskListMem[i].startSecond) &&
                            (localTime->tm_hour * 3600 + localTime->tm_min * 60 + localTime->tm_sec <= m_taskListMem[i].endSecond)) {
                            taskState = true;
                        }  
                    }
                    break;
                case TASK_TYPE_EVERY_WEEK:
                    // 每周执行的任务，检查当前时间是否在任务设定的开始时间和结束时间内
                    // 只执行一次的任务，检查当前时间是否在任务设定的开始时间和结束时间内
                    if (m_taskListMem[i].weekdays[localTime->tm_wday]) {
                        if ((localTime->tm_hour * 3600 + localTime->tm_min * 60 + localTime->tm_sec >= m_taskListMem[i].startSecond) &&
                            (localTime->tm_hour * 3600 + localTime->tm_min * 60 + localTime->tm_sec <= m_taskListMem[i].endSecond)) {
                            taskState = true;
                        }                
                    }   
                    break;
                default:
                    break;
            }
        }
    }

    return taskState;
}