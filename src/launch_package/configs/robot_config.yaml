/**: 
    ros__parameters:
      max_cpu_temp: 95
      max_joint_temp: 110
      min_power_req: 13
robdog_control_node: 
    ros__parameters: 
      mapping_video_paths: ["video/emotion/neutrality/", "video/emotion/happy/", "video/greeting/", "video/greeting/", "video/emotion/festival/"]
      mapping_light_cmds: [0x00000203, 0x00000301, 0x00000501, 0x00000201, 0x00000301]
      mapping_light_params: [0, 0, 0, 0, 0]
      mapping_actions: ["sitDown", "happy", "", "greeting", ""]
      mapping_audio_paths: ["audio/boot/intro1.wav", "audio/boot/intro2.wav", "audio/boot/intro3.wav", "audio/boot/intro4.wav", "audio/boot/intro5.wav"]
      mapping_intervals: [12, 8, 3, 5, 14]
      ws_connect_url: "ws://127.0.0.1:19002"
      ws_connect_port: 19002
      client_launcher: "launcher"
      state_report_interval: 3600
      map_points_path: "/etc/cmcc_robot"
      robotdog_file_path: "/etc/cmcc_robot/config_robotdog.xml"
      max_fan_speed: 0
      half_max_fan_speed: 20
      min_fan_speed: 50         # 需跟pwm_touch_node/fan_speed配置一致

nvidia_control_node: 
    ros__parameters: 
      ws_connect_url: "ws://127.0.0.1:19002"
      ws_connect_port: 19002

homi_ws_server: 
    ros__parameters: 
      ws_connect_port: 19002

andlink_node: 
    ros__parameters:
      wifi_connect_interface: "wlan0"
      p2p_connect_interface: "wlan1"

ble_node: 
    ros__parameters:
      wifi_connect_interface: "wlan0"
      p2p_connect_interface: "wlan1"
      mobile_connect_interface: "eth1"

network_node:
  ros__parameters:
    # 日志级别配置 (DEBUG, INFO, WARN, ERROR, FATAL)
    log_level: "DEBUG"  # 默认INFO级别

    # 网络状态配置文件路径
    network_state_config_file: "/etc/cmcc_robot/network_state.conf"
    network_state_lock_file: "/var/lock/network_state.lock"

    wifi_connect_interface: "wlan0"
    p2p_connect_interface: "wlan1"
    mobile_connect_interface: "eth1"
    ethernet_interface: "eth0"  # 有线以太网接口，用于网络冲突检测
    ssid: "xiaoli51"
    static_ip: "***********"
    device_type: "unitree"
    # device_type: "ysc"
    # cellular_option: "disable"
    cellular_option: "enable"
    timer_interval: 20
    jwae_tproxy: "disable"

    # DNS服务器配置
    dns_primary_servers: ["*********", "************", "***************"]
    dns_backup_servers: ["************", "*******", "*******"]

pwm_touch_node:
  ros__parameters:
    # 基本PWM配置
    pwm_gpio_map: "pwmchip6:0,pwmchip2:0,pwmchip3:0"  # RGB LED通道
    light_pwm: "pwmchip0:0"                            # 照明灯通道
    touch_gpio_pins: [36, 38, 39]                      # 触摸GPIO引脚
    pwm_period: 10000                                   # PWM周期（纳秒）
    fan_pwm: "pwmchip5:0"                               # 风扇通道
    fan_period: 40000                                   # 风扇周期
    fan_speed: 20000                                    # 风扇转速
    
    # LED效果配置
    mode: "static"                                      # LED模式
    color: "#FFFFFF"                                    # 主要颜色（白色）
    secondary_color: "#FFFFFF"                          # 次要颜色
    effect_speed: 1.0                                   # 效果速度
    brightness: 1.0                                     # 亮度（0-1）
    
    # 触摸检测过滤配置
    min_high_duration: 0.15                            # 最小高电平持续时间（秒）
                                                        # 默认50ms，可根据需要调整：
                                                        # - 0.02 (20ms): 较敏感，适合快速点击
                                                        # - 0.05 (50ms): 标准设置，过滤大部分噪声
                                                        # - 0.1 (100ms): 较严格，只检测明确的触摸
                                                        # - 0.2 (200ms): 很严格，只检测长按
