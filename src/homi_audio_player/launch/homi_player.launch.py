import os

from ament_index_python.packages import get_package_share_directory  # 查询功能包路径的方法

from launch import LaunchDescription                 # launch文件的描述类
from launch.actions import IncludeLaunchDescription  # 节点启动的描述类
from launch.launch_description_sources import PythonLaunchDescriptionSource
from launch.actions import GroupAction               # launch文件中的执行动作
from launch_ros.actions import PushRosNamespace      # ROS命名空间配置
from launch_ros.substitutions import FindPackageShare
from launch_ros.actions import Node
import yaml
import re
from configparser import ConfigParser
import json

def replace_package_paths(params):
    if isinstance(params, dict):
        for key, value in params.items():
            params[key] = replace_package_paths(value)
    elif isinstance(params, str):
        matches = re.findall(r'\$\(\s*find-pkg-share\s+(\w+)\s*\)', params)
        for match in matches:
            try:
                package_path = FindPackageShare(package=match).find(match)
                params = params.replace(f"$(find-pkg-share {match})", package_path)
            except:
                print(f"Package {match} not found")
    return params
def replace_package_paths_from_files(files):
    params = []
    for file in files:
        with open(file, 'r') as f:
            param = yaml.safe_load(f)
        param = replace_package_paths(param)
        params.append(param)
    return params

def generate_launch_description():
    config_files = ['param.yaml']
    config_files_path = [os.path.join(
    get_package_share_directory('homi_audio_player'),
    'launch','config',
    file
    ) for file in config_files]
    package_name = 'homi_audio_player'
    name_space = 'homi_audio_player'

    return LaunchDescription([
        Node(
            package=package_name,
            executable='homi_player',
            name='homi_player',
            namespace=name_space,
            parameters=replace_package_paths_from_files(config_files_path)
        )
    ])
