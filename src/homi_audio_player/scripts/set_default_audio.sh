#!/bin/bash

# 设置默认播放设备,当设备插拔时，或者程序启动时调用
# 优先级: [USB Audio Device]  
# 指定USB设备名
usb_device_name="USB_Audio_Device"

# 通过USB设备名获取声卡ID
#soundcard_id=$(aplay -l | grep "${usb_device_name}" -A 2 | grep "card" | awk '{print $2}' | tr -d ':' | head -n 1)
#name: <alsa_output.usb-C-Media_Electronics_Inc._USB_Audio_Device-00.analog-stereo>
padevname=$(pacmd list-sinks | grep -e name: | grep -e ${usb_device_name} | head -n 1 | sed 's/.*<\(.*\)>.*/\1/')

if [ "${padevname}" = "" ]; then
echo "no playback card named: \"${usb_device_name}\"!"
else 
# 通过pulseaudio 设置默认声卡
pacmd set-default-sink ${padevname}
echo "set default sink \"${padevname}\""
fi
