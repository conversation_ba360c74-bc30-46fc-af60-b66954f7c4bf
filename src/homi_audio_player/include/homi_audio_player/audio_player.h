#ifndef __AUDIO_PLAYER_H__
#define __AUDIO_PLAYER_H__
extern "C" {
#include <libavformat/avformat.h>
#include <libavcodec/avcodec.h>
#include <libavutil/avutil.h>
#include <libswresample/swresample.h>
#include <pulse/simple.h>
#include <pulse/error.h>
#include <pulse/stream.h>
#include <pulse/thread-mainloop.h>
#include <pulse/pulseaudio.h>
}
#include <memory>
#include <functional>
#include <vector>
#include <queue>
#include <chrono>
namespace homi
{
    namespace media
    {
        enum class EventType { 
            OnDataReady,
            OnError,
            OnPlayFinished,
            OnPause,
            OnResume,
            OnClose
        };
        class IMediaPlayer
        {
        public:
            virtual int pause(void) = 0;
            virtual int resume(void) = 0;
            virtual int close(void) = 0;
            virtual int waitForFinish(std::chrono::milliseconds timeout=std::chrono::milliseconds::max()) = 0;
            virtual int volume(float percent)=0;
            virtual ~IMediaPlayer() = default;
        };
        /***
            {
                "type": "file/stream",
                "format":{
                    "sampleRate": 44100,
                    "sampleFormat": "s16le",
                    "channelCount": 2
                },
                "source": "file:///home/<USER>/Downloads/1.mp3",
                "openMode": "pause"
            } 
        ***/
        class IMediaPlayerEngine
        {
        public:
            virtual std::shared_ptr<IMediaPlayer> createMediaPlayer(const std::string &configJson
                ,const std::string &taskName
                ,std::function<void(int status,const std::string &taskName)> onTaskStaus
                ,std::function<bool(const EventType &, std::shared_ptr<std::vector<uint8_t>> )> onEvent=nullptr)=0;
            virtual std::vector<std::shared_ptr<IMediaPlayer>> searchMediaPlayers(const std::string &configJson)=0;
            virtual ~IMediaPlayerEngine() = default;
        };
        std::shared_ptr<IMediaPlayerEngine> getMediaPlayerEngine(void);
    }
}

#endif // __AUDIO_PLAYER_H__