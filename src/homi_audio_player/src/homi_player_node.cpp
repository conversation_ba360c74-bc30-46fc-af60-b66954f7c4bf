#include "def.h"
#include "json.hpp"
#include <rclcpp/rclcpp.hpp>
#include <unistd.h>
#include <homi_audio_player/audio_player.h>
#include <homi_speech_interface/msg/assistant_event.hpp>
#include <homi_speech_interface/msg/sigc_event.hpp>
#include <homi_speech_interface/srv/play_file.hpp>
#include <homi_speech_interface/srv/assistant_abort.hpp>
#include <homi_speech_interface/srv/set_wake_event.hpp>
#include <homi_speech_interface/def.h>
#include <mutex>
#include <condition_variable>
#include <sys/syscall.h>
#include <libudev.h>
#include <sys/stat.h>
#include <dirent.h>
#include <homi_speech_interface/msg/task_status.hpp>


class SIGCEventCMD
{
public:
    enum class Type
    {
        PLAY,
        PAUSE,
        RESUME,
        STOP,
        UNVAILD
    }type;
    std::string url;
};

class SIGCEventParser
{
private:
    struct CMD_INFO
    {
        std::string eventId;
        SIGCEventCMD::Type type;
        std::string url;
        void reset()
        {
            eventId = "unvaild_info";
            type = SIGCEventCMD::Type::UNVAILD;
            url = "";
        }
    }_cmdInfo;
    struct CMD_CONFIRM
    {
        std::string eventId;
        bool confirm;
        void reset()
        {
            eventId = "unvaild_confirm";
            confirm = false;
        }
    }_cmdConfirm;
    void reset()
    {
        _cmdInfo.reset();
        _cmdConfirm.reset();
    }
public:
    SIGCEventParser()
    {
        reset();
    }
    SIGCEventCMD parse(const homi_speech_interface::msg::SIGCEvent::SharedPtr sigcEvent)
    {
        if(!sigcEvent) return { SIGCEventCMD::Type::UNVAILD, "" };
        try
        {            
            nlohmann::json json = nlohmann::json::parse(sigcEvent->event);
            auto eventId = json["eventId"].get<std::string>();
            // RCLCPP_INFO(rclcpp::get_logger("homi_player_node::SIGCEventParser"),"eventId:%s",eventId.c_str());
            if(json["domain"]=="KEEP_ALIVE"&&json["event"]=="KEEP_ALIVE") {
                return { SIGCEventCMD::Type::UNVAILD, "" };
            } 
            if(json["domain"]=="DEVICE_ABILITY"&&json["event"]=="audio_media_play")
            {
                RCLCPP_INFO(rclcpp::get_logger("homi_player_node::SIGCEventParser"),"receive audio media play event");
                auto subEvent = json["body"]["subEvent"].get<std::string>();
                if(subEvent=="media.play")
                {
                    RCLCPP_INFO(rclcpp::get_logger("homi_player_node::SIGCEventParser"),"receive media.play event");
                    auto url = json["body"]["info"]["url"].get<std::string>();
                    if(url.empty()) return { SIGCEventCMD::Type::UNVAILD, "" };
                    _cmdInfo.url = url;
                    _cmdInfo.type = SIGCEventCMD::Type::PLAY;
                }
                else if(subEvent=="media.pause")
                {
                    RCLCPP_INFO(rclcpp::get_logger("homi_player_node::SIGCEventParser"),"receive media.pause event");
                    _cmdInfo.type = SIGCEventCMD::Type::PAUSE;
                }
                else if(subEvent=="media.resume")
                {
                    RCLCPP_INFO(rclcpp::get_logger("homi_player_node::SIGCEventParser"),"receive media.resume event");
                    _cmdInfo.type = SIGCEventCMD::Type::RESUME;
                }
                else if(subEvent=="media.stop")
                {
                    RCLCPP_INFO(rclcpp::get_logger("homi_player_node::SIGCEventParser"),"receive media.stop event");
                    _cmdInfo.type = SIGCEventCMD::Type::STOP;
                }
                else
                {
                    RCLCPP_INFO(rclcpp::get_logger("homi_player_node::SIGCEventParser"),"receive unvaild subEvent %s",subEvent.c_str());
                    return { SIGCEventCMD::Type::UNVAILD, "" };
                }
                _cmdInfo.eventId = eventId;
            }
            else if((json["domain"]=="SKILL_EXECUTE")&&(json["event"]=="execute_result"))
            {
                RCLCPP_INFO(rclcpp::get_logger("homi_player_node::SIGCEventParser"),"receive media play execute result event");
                _cmdConfirm.confirm = json["body"]["success"].get<bool>();
                _cmdConfirm.eventId = eventId;
                RCLCPP_INFO(rclcpp::get_logger("homi_player_node::SIGCEventParser"),"receive media play execute result event ,_cmdConfirm.confirm=%s",_cmdConfirm.confirm?"true":"false");
            }
            else
            {
                RCLCPP_INFO(rclcpp::get_logger("homi_player_node::SIGCEventParser"),"receive unvaild event!");
                return { SIGCEventCMD::Type::UNVAILD, "" };
            }
            if(_cmdConfirm.eventId==_cmdInfo.eventId)
            {
                RCLCPP_INFO(rclcpp::get_logger("homi_player_node::SIGCEventParser"),"_cmdConfirm.eventId==_cmdInfo.eventId :%s",_cmdConfirm.confirm?"success":"failed");
                SIGCEventCMD cmd = { _cmdInfo.type,_cmdInfo.url };
                if(_cmdConfirm.confirm)
                {
                    reset();
                    RCLCPP_INFO(rclcpp::get_logger("homi_player_node::SIGCEventParser"),"trigger cmd");
                    return cmd;
                }
                reset();
            }
            else
            {
                RCLCPP_INFO(rclcpp::get_logger("homi_player_node::SIGCEventParser"),"_cmdConfirm.eventId=%s _cmdInfo.eventId=%s",_cmdConfirm.eventId.c_str(),_cmdInfo.eventId.c_str());
            }
            // RCLCPP_INFO(rclcpp::get_logger("homi_player_node::SIGCEventParser"),"no need to execute cmd");
            return { SIGCEventCMD::Type::UNVAILD, "" };
        }
        catch(const std::exception& e)
        {
            RCLCPP_ERROR(rclcpp::get_logger("homi_player_node::SIGCEventParser"),"json parse error %s",e.what());
        }
        return { SIGCEventCMD::Type::UNVAILD, "" };
    }
    
};
static std::string _sSetdefaultAudioDeviceShFile;
class homiPlayerNode : public rclcpp::Node
{
public:
    homiPlayerNode()
    : Node("homi_player")                          // ROS2节点父类初始化
    {
        _mediaPlayer = nullptr;
        _canPlay = false;
    }
    bool onInit()
    {
        this->declare_parameter(HOMI_SPEECH_SPEECHCORE_SIGC_EVENT_TOPIC, "/homi_speech/sigc_event_topic");
        this->declare_parameter(HOMI_SPEECH_SPEECHCORE_ASSISTANT_EVENT_TOPIC, "/homi_speech/speech_assistant_status_topic");
        this->declare_parameter(HOMI_AUDIO_PLAYER_PARAM_DEVICE_NAMES, "[default]");
        this->declare_parameter(PARAM_SET_DEFAULT_AUDIO_DEVIVCE_SH_FILE,"");
        std::string sigcEventTopicName;
        std::string assistantEventTopicName;

        this->get_parameter(HOMI_SPEECH_SPEECHCORE_SIGC_EVENT_TOPIC, sigcEventTopicName);
        this->get_parameter(HOMI_SPEECH_SPEECHCORE_ASSISTANT_EVENT_TOPIC, assistantEventTopicName);
        this->get_parameter(HOMI_AUDIO_PLAYER_PARAM_DEVICE_NAMES, _deviceNames);
        this->get_parameter(PARAM_SET_DEFAULT_AUDIO_DEVIVCE_SH_FILE, _sSetdefaultAudioDeviceShFile);
        RCLCPP_INFO(this->get_logger(), "homi_player node init %s %s %s %s", sigcEventTopicName.c_str(), assistantEventTopicName.c_str(), _deviceNames.c_str(),_sSetdefaultAudioDeviceShFile.c_str());  // ROS2日志输出函数
        _assistantEventSubscription = this->create_subscription<homi_speech_interface::msg::AssistantEvent>(
            assistantEventTopicName, 10, std::bind(&homiPlayerNode::_assistantEventCB, this, std::placeholders::_1)
        );
        _sigcEventSubscription = this->create_subscription<homi_speech_interface::msg::SIGCEvent>(
            sigcEventTopicName, 10, std::bind(&homiPlayerNode::_sigcEventCB, this, std::placeholders::_1)
        );
        _taskStatusPublisher = this->create_publisher<homi_speech_interface::msg::TaskStatus>("/homi_speech/task_status_topic", 10);
        
        // _idleTimer = this->create_wall_timer(std::chrono::seconds(5),std::bind(&homiPlayerNode::_idleCB, this));
        
        return true;
    }
    ~homiPlayerNode()
    {
        if(_idleTimer)
        {
            _idleTimer->cancel();
            _idleTimer.reset();
        }
        if(_sigcEventSubscription)
        {
            _sigcEventSubscription.reset();
        }
        if(_assistantEventSubscription)
        {
            _assistantEventSubscription.reset();
        }
        
        RCLCPP_INFO(this->get_logger(), "homi_player node exit");
    }
private:
    void _idleCB() const
    {
        static long long lcount = 0;
        lcount++;
        RCLCPP_INFO(this->get_logger(),"idle ,run count =%lld s ...",(lcount*5));
    }
    void _assistantEventCB(const homi_speech_interface::msg::AssistantEvent::SharedPtr msg)                  // 创建回调函数，执行收到话题消息后对数据的处理
    {
        RCLCPP_INFO(this->get_logger(), "_assistantEventCB 1: %s %d %s %s", msg->section_id.c_str(),msg->status,msg->msg.c_str(),msg->description.c_str());       // 输出日志信息，提示订阅收到的话题消息
        RCLCPP_INFO(this->get_logger(), "_assistantEventCB 2: (_mediaPlayer)=%s (_canPlay)=%s",(_mediaPlayer?"true":"false"),(_canPlay?"true":"false"));
        if(msg->status==1 )
        {
            // 语音助手启动了，则暂停音乐播放
            if(_mediaPlayer)
            {
                _mediaPlayer->pause();
            }
        }
        else if(msg->status==0)
        {
            // 语音助手退出了，则继续音乐播放
            if(_mediaPlayer && _canPlay)
            {
                _mediaPlayer->resume();
            }
        }
        else{
        }
    }
    void _sigcEventCB(const homi_speech_interface::msg::SIGCEvent::SharedPtr msg)
    {
        RCLCPP_INFO(this->get_logger(), "_sigcEventCB 1: %s %ld", msg->event.c_str(),(long)syscall(SYS_gettid));
        auto cmd = _sigcEventParser.parse(msg);
        // RCLCPP_INFO(this->get_logger(), "_sigcEventCB 2: %d %s", cmd.type,cmd.url.c_str());
        if(cmd.type == SIGCEventCMD::Type::PLAY)
        {
            std::string conf = R"({
                "source": ""
            })";
            try
            {
                auto json = nlohmann::json::parse(conf);
                json["source"] = cmd.url;
                auto json2 = nlohmann::json::parse(_deviceNames);
                json["deviceNames"] = json2;
                conf = json.dump();
            }catch(const std::exception& e)
            {
                RCLCPP_ERROR(this->get_logger(), "json parse error %s", e.what());
                return;
            }
            RCLCPP_INFO(this->get_logger(), "_sigcEventCB 3: %s",conf.c_str());
            if(_mediaPlayer)
            {
                _mediaPlayer->close();
                _mediaPlayer.reset();
            }
            auto ptr = homi::media::getMediaPlayerEngine();
            if(ptr)
            {
                auto player = ptr->createMediaPlayer(conf,"audioMediaPlay",[this](int status,const std::string &taskName){
                    homi_speech_interface::msg::TaskStatus taskStatus;
                    taskStatus.name = taskName;
                    taskStatus.status = status;
                    _taskStatusPublisher->publish(taskStatus);
                });
                if(player)
                {
                    _mediaPlayer = player;
                    _canPlay = true;
                }
            }
        }
        else if(cmd.type == SIGCEventCMD::Type::STOP)
        {
            if(_mediaPlayer)
            {
                _mediaPlayer->close();
                _mediaPlayer.reset();
            }
            _canPlay = false;
        }
        else if(cmd.type == SIGCEventCMD::Type::PAUSE)
        {
            _canPlay = false;
        }
        else if(cmd.type == SIGCEventCMD::Type::RESUME)
        {
            _canPlay = true;
        }
        else{}
        RCLCPP_INFO(this->get_logger(), "_sigcEventCB 6: exit");
    }

    rclcpp::Subscription<homi_speech_interface::msg::AssistantEvent>::SharedPtr _assistantEventSubscription;
    rclcpp::Subscription<homi_speech_interface::msg::SIGCEvent>::SharedPtr _sigcEventSubscription;
    rclcpp::Publisher<homi_speech_interface::msg::TaskStatus>::SharedPtr _taskStatusPublisher;
    rclcpp::TimerBase::SharedPtr _idleTimer;
    std::shared_ptr<homi::media::IMediaPlayer> _mediaPlayer;
    SIGCEventParser _sigcEventParser;
    bool _canPlay;
    std::string _deviceNames;
};


static bool s_snd_dev_thread_exit_flag = false;
void snd_dev_thread_proc() {
    // 启动后先设置一次默认声卡
    auto sret = system(_sSetdefaultAudioDeviceShFile.c_str());
    if (sret !=0 ) {
        RCLCPP_ERROR(rclcpp::get_logger("homi_player"),"set default audio faild! sret: %d",sret);
        return;
    }
    // 设置监听
    struct udev *udev;
    struct udev_monitor *mon;
    int fd;
    // 创建 udev 上下文
    udev = udev_new();
    if (!udev) {
        RCLCPP_ERROR(rclcpp::get_logger("homi_player"),"Failed to create udev context");
        return;
    }
    // 创建一个 udev 监视器，监听声卡设备事件
    mon = udev_monitor_new_from_netlink(udev, "udev");
    if (!mon) {
        RCLCPP_ERROR(rclcpp::get_logger("homi_player"),"Failed to create udev monitor");
        udev_unref(udev);
        return;
    }
    // 过滤事件，只监听声卡设备
    udev_monitor_filter_add_match_subsystem_devtype(mon, "sound", NULL);
    udev_monitor_enable_receiving(mon);
    // 获取监视器的文件描述符
    fd = udev_monitor_get_fd(mon);
    RCLCPP_INFO(rclcpp::get_logger("homi_player"),"+++++ Monitoring sound card device events... +++++");
    bool bNeedResetAudio = false;
    while (!s_snd_dev_thread_exit_flag) {
        fd_set fds;
        struct timeval tv;
        int ret;
        FD_ZERO(&fds);
        FD_SET(fd, &fds);
        // 设置超时时间
        tv.tv_sec = 2; // 2秒刚好用于延迟
        tv.tv_usec = 0;
        // 等待事件发生
        ret = select(fd + 1, &fds, NULL, NULL, &tv);
        if (ret > 0 && FD_ISSET(fd, &fds)) {
            // 读取事件
            struct udev_device *dev = udev_monitor_receive_device(mon);
            if (dev) {
                // 处理事件
                const char *action = udev_device_get_action(dev);
                const char *subsystem = udev_device_get_subsystem(dev);
                if (action && subsystem && strcmp(subsystem, "sound") == 0) {
                    /**
                     * 一次会触发多次事件，开启延迟执行 避免执行多次
                        声卡设备插入: /devices/pci0000:00/0000:00:14.0/usb3/3-3/3-3:1.0/sound/card4
                        声卡设备插入: /devices/pci0000:00/0000:00:14.0/usb3/3-3/3-3:1.0/sound/card4/pcmC4D0p
                        声卡设备插入: /devices/pci0000:00/0000:00:14.0/usb3/3-3/3-3:1.0/sound/card4/pcmC4D0c
                        声卡设备插入: /devices/pci0000:00/0000:00:14.0/usb3/3-3/3-3:1.0/sound/card4/controlC4
                    **/
                    if (strcmp(action, "add") == 0) {
                        RCLCPP_INFO(rclcpp::get_logger("homi_player"),"======== 声卡设备插入: %s", udev_device_get_devpath(dev));
                    } else if (strcmp(action, "remove") == 0) {
                        RCLCPP_INFO(rclcpp::get_logger("homi_player"),"======== 声卡设备移除: %s", udev_device_get_devpath(dev));
                    }
                    bNeedResetAudio = true;
                }
                // 释放设备对象
                udev_device_unref(dev);
            }
        } else {
            // 超时，判断是否有需要执行的事件
            if (bNeedResetAudio) {
                RCLCPP_WARN(rclcpp::get_logger("homi_player"),"set default audio");
                auto sret = system(_sSetdefaultAudioDeviceShFile.c_str());
                if (sret !=0 ) {
                    RCLCPP_ERROR(rclcpp::get_logger("homi_player"),"set default audio faild! sret: %d",sret);
                }
                bNeedResetAudio = false;
            }
        }
    }
    // 释放资源
    udev_monitor_unref(mon);
    udev_unref(udev);
    RCLCPP_INFO(rclcpp::get_logger("homi_player"),"----- Monitoring sound card device events exited! -----");
}

// ROS2节点主入口main函数
int main(int argc, char * argv[])                               
{
    // ROS2 C++接口初始化
    rclcpp::init(argc, argv);     

    auto ptr = std::make_shared<homiPlayerNode>();

    if(!ptr||!ptr->onInit())
    {
        RCLCPP_ERROR(ptr->get_logger(),"homi_player node init failed");
        return -1;
    }
    // 创建线程，监听USB声卡插拔，并设置默认声卡
    std::thread thread_snd_dev(snd_dev_thread_proc);


    // 创建ROS2节点对象并进行初始化                 
    rclcpp::spin(ptr); 

    s_snd_dev_thread_exit_flag = true;
    if(thread_snd_dev.joinable()) {
        thread_snd_dev.join();
    }
    // 关闭ROS2 C++接口
    rclcpp::shutdown();    

    ptr.reset();

    RCLCPP_INFO(rclcpp::get_logger("homi_player_main")," this is the end of homi_player node");                        
    
    return 0;
}
