#include <homi_audio_player/audio_player.h>
#include "json.hpp"
#include <mutex>
#include <thread>
#include <unordered_map>
#include <map>
#include <condition_variable>
#include <sys/syscall.h>
#include <unistd.h>
#include "AlsaHelper.h"
#ifdef AUDIO_PLAYER_DEBUG_ON
#ifndef DIR_SEPARATOR
#define DIR_SEPARATOR       '/'
#endif
#ifndef DIR_SEPARATOR_STR
#define DIR_SEPARATOR_STR   "/"
#endif
#ifndef __FILENAME__
#define __FILENAME__  (strrchr(DIR_SEPARATOR_STR __FILE__, DIR_SEPARATOR) + 1)
#endif
#ifdef ROS2_DEBUG_ON
#include <rclcpp/rclcpp.hpp>
#define DEBUG(fmt,...) RCLCPP_INFO(rclcpp::get_logger("audio_player"),fmt" [%s:%d:%s][%ld]",## __VA_ARGS__, __FILENAME__, __LINE__, __FUNCTION__,((long)syscall(SYS_gettid)))
#else
#define DEBUG(fmt,...) fprintf(stdout, fmt" [%s:%d:%s][%ld]\n", ## __VA_ARGS__, __FILENAME__, __LINE__, __FUNCTION__,((long)syscall(SYS_gettid)))
#endif
#else 
#define DEBUG(fmt,...)
#endif

#define pa_xnew0(type,n) ((type*) _pa_xnew0_internal((n), sizeof(type)))
#define CHECK_VALIDITY_RETURN_ANY(rerror, expression, error, ret)       \
    do {                                                                \
        if (!(expression)) {                                            \
            if (rerror)                                                 \
                *(rerror) = error;                                      \
            return (ret);                                               \
        }                                                               \
    } while(false);

#define CHECK_SUCCESS_GOTO(context, rerror, expression, label)        \
    do {                                                        \
        if (!(expression)) {                                    \
            if (rerror)                                         \
                *(rerror) = pa_context_errno(context);     \
            goto label;                                         \
        }                                                       \
    } while(false);

#define CHECK_DEAD_GOTO(context, stream, rerror, label)                               \
    do {                                                                \
        if (!context || !PA_CONTEXT_IS_GOOD(pa_context_get_state(context)) || \
            !stream || !PA_STREAM_IS_GOOD(pa_stream_get_state(stream))) { \
            if ((context && pa_context_get_state(context) == PA_CONTEXT_FAILED) || \
                (stream && pa_stream_get_state(stream) == PA_STREAM_FAILED)) { \
                if (rerror)                                             \
                    *(rerror) = pa_context_errno(context);         \
            } else                                                      \
                if (rerror)                                             \
                    *(rerror) = PA_ERR_BADSTATE;                        \
            goto label;                                                 \
        }                                                               \
    } while(false);

#define PLAYBACK_STREAM_CHECK_DEAD_GOTO(context, stream, label) do { \
if (!context || pa_context_get_state(context) != PA_CONTEXT_READY || \
    !stream || pa_stream_get_state(stream) != PA_STREAM_READY) { \
    goto label; \
} \
} while(0)

namespace homi
{
    namespace media
    {
        class IPulseAudioContext
        {
        public:
            virtual pa_threaded_mainloop * getMainLoop()=0;
            virtual pa_context * getContext()=0;
            virtual ~IPulseAudioContext() = default;
        };
        class FFmpegCodec
        {
        public:
            FFmpegCodec()
            {
                _openFlag = false;
                _formatContext = nullptr;
                _codecContext = nullptr;
                _swrContext = nullptr;
                _streamIndex = -1;
                _packet = nullptr;
                _frame = nullptr;
            }
            int open(const char* filename,int &sampleRate,int &channels)
            {
                std::unique_lock<std::mutex> lock(_mutex);
                if(_openFlag) {
                    return -1;
                }
                _openFlag = true;
                _formatContext = _loadAudioFile(filename);
                if (!_formatContext) {
                    DEBUG("FFmpegCodec:open:Could not load audio file.\n");
                    _clean();
                    return -1;
                }
                _streamIndex = _findAudioStream(_formatContext);
                if( _streamIndex < 0) {
                    DEBUG("FFmpegCodec:open:Could not find audio stream.\n");
                    _clean();
                    return -1;
                }
                _codecContext = _openCodecContext(_formatContext, _streamIndex);
                if(!_codecContext)
                {
                    DEBUG("FFmpegCodec:open:Could not open codec context.\n");
                    _clean();
                    return -1;
                }
                _swrContext = _configSwrContext(_codecContext);
                if(!_swrContext)
                {
                    DEBUG("FFmpegCodec:open:Could not config swr context.\n");
                    _clean();
                    return -1;
                }
                _packet = av_packet_alloc();
                _frame = av_frame_alloc();
                if(!_packet || !_frame)
                {
                    DEBUG("FFmpegCodec:open:Could not alloc packet or frame.\n");
                    _clean();
                    return -1;
                }
                // sampleRate = _codecContext->sample_rate;
                // channels = _codecContext->channels;
                sampleRate = 16000;
                channels = 1;
                return 0;
            }
            int read(std::shared_ptr<std::vector<uint8_t>> data)
            {
                if(data==nullptr) return -1;
                std::unique_lock<std::mutex> lock(_mutex);
                if(!_openFlag) {
                    return -1;
                }
            // 读取并解码音频数据
                data->clear();
                while (av_read_frame(_formatContext, _packet) >= 0) {// av_read_frame()：读取一个数据包。
                    if (_packet->stream_index == _streamIndex) {
                        if (avcodec_send_packet(_codecContext, _packet) < 0) {// avcodec_send_packet()：将数据包发送到解码器。
                            DEBUG("FFmpegCodec:read:Error sending packet for decoding.\n");
                            av_packet_unref(_packet);
                            return -1;
                        }
                        if(avcodec_receive_frame(_codecContext, _frame) >= 0) {// avcodec_receive_frame()：从解码器接收解码后的帧。
                            // 将解码后的音频数据写入音频流进行播放
                            int bufsize = _codecContext->channels*_frame->nb_samples*2;
                            data->resize(bufsize);
                            uint8_t *out_buffer = (uint8_t *)data->data();
                            auto ret = swr_convert(_swrContext, &out_buffer, bufsize, (const uint8_t **)_frame->data, _frame->nb_samples);
                            if(ret < 0) {
                                DEBUG("FFmpegCodec:read:Error while converting.\n");
                                data->clear();
                            }
                            // ret = ret * _codecContext->channels * 2;
                            ret = ret * 2;
                            // DEBUG("FFmpegCodec:read:swr_convert ret=%d,data size=%ld\n",ret,data->size());
                            if(ret>0 &&((size_t)ret < data->size())) {
                                data->resize(ret);
                            }
                            av_packet_unref(_packet);
                            return 0;
                        }
                    }
                    av_packet_unref(_packet);// 释放packet以便重复使用
                }
                DEBUG("FFmpegCodec:read:Error while reading.\n");
                _clean();
                return 0;
            }
            void close()
            {
                std::unique_lock<std::mutex> lock(_mutex);
                _clean();
            }
            ~FFmpegCodec()
            {
                DEBUG("FFmpegCodec:~FFmpegCodec\n");
                close();
            }
        private:
            std::mutex _mutex;
            bool _openFlag;
            AVFormatContext* _formatContext;
            AVCodecContext* _codecContext;
            SwrContext * _swrContext;
            int _streamIndex;
            AVPacket* _packet;
            AVFrame* _frame;
            void _clean()
            {
                _openFlag = false;
                _streamIndex = -1;
                if(_frame) {
                    DEBUG("FFmpegCodec:_clean:Free frame.\n");
                    av_frame_free(&_frame);
                    _frame = nullptr;
                }
                if(_packet) {
                    DEBUG("FFmpegCodec:_clean:Free packet.\n");
                    av_packet_free(&_packet);
                    _packet = nullptr;
                }
                if( _swrContext) {
                    DEBUG("FFmpegCodec:_clean:Free swr context.\n");
                    swr_free(&_swrContext);
                    _swrContext = nullptr;
                }
                if (_codecContext) {
                    DEBUG("FFmpegCodec:_clean:Free codec context.\n");
                    avcodec_free_context(&_codecContext);
                    _codecContext = nullptr;
                }
                if (_formatContext) {
                    DEBUG("FFmpegCodec:_clean:Close format context.\n");
                    avformat_close_input(&_formatContext);
                    _formatContext = nullptr;
                }
            }
            // 加载音频文件
            static AVFormatContext* _loadAudioFile(const char* filename) {
                AVFormatContext* formatContext = nullptr;
                if (avformat_open_input(&formatContext, filename, nullptr, nullptr) != 0) {// avformat_open_input()：打开音频文件
                    DEBUG("FFmpegCodec:_loadAudioFile:Could not open audio file.\n");
                    return nullptr;
                }
                if (avformat_find_stream_info(formatContext, nullptr) < 0) {// avformat_find_stream_info()：获取音频文件的流信息。
                    DEBUG("FFmpegCodec:_loadAudioFile:Could not find stream information.\n");
                    avformat_close_input(&formatContext);
                    return nullptr;
                }
                return formatContext;
            }
            // 找到音频流的索引
            static int _findAudioStream(AVFormatContext* formatContext) {
                for (unsigned int i = 0; i < formatContext->nb_streams; ++i) {// 遍历文件中的所有流，找到第一个音频流的索引。
                    if (formatContext->streams[i]->codecpar->codec_type == AVMEDIA_TYPE_AUDIO) {
                        return i;
                    }
                }
                DEBUG("FFmpegCodec:_findAudioStream:Could not find audio stream.\n");
                return -1;
            }
            // 打开解码器上下文
            static AVCodecContext* _openCodecContext(AVFormatContext* formatContext, int streamIndex) {
                AVCodecParameters* codecParameters = formatContext->streams[streamIndex]->codecpar;
                AVCodec* codec = avcodec_find_decoder(codecParameters->codec_id);// avcodec_find_decoder()：查找解码器。
                if (!codec) {
                    DEBUG("FFmpegCodec:_openCodecContext:Unsupported codec.\n");
                    return nullptr;
                }
                AVCodecContext* codecContext = avcodec_alloc_context3(codec);// avcodec_alloc_context3()：分配解码器上下文。
                if (avcodec_parameters_to_context(codecContext, codecParameters) < 0) {// avcodec_parameters_to_context()：将流的参数复制到解码器上下文。
                    DEBUG("FFmpegCodec:_openCodecContext:Could not copy codec context.\n");
                    avcodec_free_context(&codecContext);
                    return nullptr;
                }
                if (avcodec_open2(codecContext, codec, nullptr) < 0) {// avcodec_open2()：打开解码器。
                    DEBUG("FFmpegCodec:_openCodecContext:Could not open codec.\n");
                    avcodec_free_context(&codecContext);
                    return nullptr;
                }
                // 如果 channel_layout 未设置，则根据声道数设定默认值
                if (codecContext->channel_layout == 0) {
                    if (codecContext->channels == 1) {
                        codecContext->channel_layout = AV_CH_LAYOUT_MONO;
                    } else if (codecContext->channels == 2) {
                        codecContext->channel_layout = AV_CH_LAYOUT_STEREO;
                    } else {
                        // 对于更多声道，可以使用 av_get_default_channel_layout() 函数
                        codecContext->channel_layout = av_get_default_channel_layout(codecContext->channels);
                    }
                }
                return codecContext;
            }
            static SwrContext * _configSwrContext(AVCodecContext* codecContext)
            {
                SwrContext *swrContext = nullptr;
                swrContext = swr_alloc_set_opts(swrContext,
                    AV_CH_LAYOUT_MONO, AV_SAMPLE_FMT_S16, 16000,
                                            codecContext->channel_layout, codecContext->sample_fmt, codecContext->sample_rate,
                                            0, nullptr);
                if (!swrContext || swr_init(swrContext) < 0) {
                    DEBUG("FFmpegCodec:_configSwrContext:Cannot create sample rate converter for conversion.\n");
                    if(swrContext){
                        swr_free(&swrContext);
                        return nullptr;
                    }
                }
                DEBUG("FFmpegCodec:_configSwrContext: channel: %d sample_rate: %d sample_fmt: %d.\n",codecContext->channel_layout,codecContext->sample_rate,codecContext->sample_fmt);
                return swrContext;
            }
        };
        class MediaPlayerStreamAlsa: public IMediaPlayer,public std::enable_shared_from_this<MediaPlayerStreamAlsa>
        {
        public:
            MediaPlayerStreamAlsa(const std::vector<std::string> deviceNames, int sampleRate, int channels,const std::string &format,bool pause,
                std::function<bool(const EventType, std::shared_ptr<std::vector<uint8_t>> )> onEvent
                ,std::function<void()> onFinish
            ):_deviceNames(deviceNames),_sampleRate(sampleRate),_channels(channels),_format(MediaPlayerStreamAlsa::_s_getSampleFormat(format)), _onEvent(onEvent),_isPause(pause),_onFinish(onFinish)
            {
                DEBUG("MediaPlayerStreamAlsa:MediaPlayerStreamAlsa() channel: %d simplerate: %d.\n",channels,sampleRate);
                _runFlag = false;
            }
            bool onInit(void)
            {
                try
                {
                    std::string deviceName = "default";
                    for(auto &name:_deviceNames)
                    {
                        auto cardId = homi_speech::getCardIndexByName(name);
                        if(cardId>=0)
                        {
                            deviceName = "plughw:" + std::to_string(cardId) + ",0";
                            DEBUG("MediaPlayerStreamAlsa:onInit:match cardId: %d\n",cardId);
                            break;
                        }
                    }
                    DEBUG("MediaPlayerStreamAlsa:onInit:deviceName: %s\n",deviceName.c_str());
                    _playback.openAndConfig(deviceName,_format,_channels,_sampleRate);
                }
                catch(const std::exception& e)
                {
                    DEBUG("MediaPlayerStreamAlsa:onInit:Error: %s\n",e.what());
                    return false;
                }
                _runFlag = true;
                _threadPtr = std::make_shared<std::thread>([this](){
                    bool doAgain = true;
                    while(doAgain)
                    {
                        {
                            std::unique_lock<std::mutex> lock(_mutex);
                            _condition.wait(lock,[this](){return !_isPause || !_runFlag;});
                            if(!_runFlag) 
                            {
                                DEBUG("FFmpegCodec:workThread:_runFlag is false.\n");
                                if(_onFinish) _onFinish();
                                return;
                            }
                        }
                        auto data = std::make_shared<std::vector<uint8_t>>();
                        if(!data) {
                            DEBUG("FFmpegCodec:workThread:data is nullptr.\n");
                            std::this_thread::sleep_for(std::chrono::milliseconds(10));
                            continue;
                        }
                        if(!_onEvent) 
                        {
                            DEBUG("FFmpegCodec:workThread:_onEvent is nullptr.\n");
                            if(_onFinish) _onFinish();
                            break;
                        }
                        doAgain = _onEvent(EventType::OnDataReady,data);
                        {
                            std::unique_lock<std::mutex> lock(_mutex);
                            _condition.wait(lock,[this](){return !_isPause || !_runFlag;});
                            if(!_runFlag) 
                            {
                                DEBUG("FFmpegCodec:workThread:_runFlag is false.\n");
                                if(_onFinish) _onFinish();
                                return;
                            }
                        }
                        if(data->size()>0)
                        {
                            // if(_writeStream(data->data(),data->size(),&error)<0)
                            if(_playback.writei(data->data(),data->size(),true)<0)
                            {
                                DEBUG("FFmpegCodec:workThread:_playback.writei failed. \n");
                                break;
                            }
                        }
                        if(!doAgain)
                        {
                            DEBUG("FFmpegCodec:workThread:doAgain is false.\n");
                            _playback.drain();
                        }
                    }
                    {
                        std::unique_lock<std::mutex> lock(_mutex);
                        _runFlag = false;
                        _condition.notify_all();
                    }
                    DEBUG("FFmpegCodec:workThread:exit.\n");
                    if(_onFinish) _onFinish();
                });
                if(!_threadPtr) {
                    _runFlag = false;
                    return false;
                }                
                return true;
            }
            virtual int pause(void) override
            {
                DEBUG("MediaPlayerStreamAlsa:pause().\n");
                {
                    std::unique_lock<std::mutex> lock(_mutex);
                    if(!_runFlag) return -1;
                    _isPause = true;
                    _condition.notify_all();
                }
                try
                {
                    _playback.acquireControl();
                }
                catch(const std::exception& e)
                {
                    DEBUG("FFmpegCodec:pause exception:%s\n",e.what());
                    return -1;
                }
                return 0;
            }
            virtual int resume(void) override
            {
                DEBUG("MediaPlayerStreamAlsa:resume().\n");
                {
                    std::unique_lock<std::mutex> lock(_mutex);
                    if(!_runFlag) return -1;
                    _isPause = false;
                    _condition.notify_all();
                }
                try
                {
                    _playback.releaseControl();
                }
                catch(const std::exception& e)
                {
                    DEBUG("FFmpegCodec:resume exception:%s\n",e.what());
                    return -1;
                }
                return 0;
            }
            virtual int close(void) override
            {
                {
                    std::unique_lock<std::mutex> lock(_mutex);
                    _runFlag = false;
                    _condition.notify_all();
                }
                _playback.close();
                return 0;
            }
            virtual int waitForFinish(std::chrono::milliseconds timeout) override
            {
                {
                    std::unique_lock<std::mutex> lock(_mutex);
                    if(timeout==std::chrono::milliseconds::max()){
                        _condition.wait(lock,[this](){return !_runFlag;});
                    }else{
                        _condition.wait_for(lock,timeout,[this](){return !_runFlag;});
                    }
                    
                }
                if(_runFlag) return -1;
                _playback.drain();
                _playback.close();
                return 0;
            }
            virtual int volume(float percent) override
            {
                
                HOMI_SPEECH_UNUSED(percent);
                return 0;
            }
            virtual ~MediaPlayerStreamAlsa() override
            {
                DEBUG("MediaPlayerStreamAlsa:~MediaPlayerStreamAlsa.\n");
                _playback.close();
                if(_threadPtr&&_threadPtr->joinable())
                {
                    DEBUG("MediaPlayerStreamAlsa:delete:join\n");
                    _threadPtr->join();
                }
                DEBUG("MediaPlayerStreamAlsa:exit\n");
            }
        private:
            const std::vector<std::string> _deviceNames;
            const int _sampleRate;
            const int _channels;
            const std::string _format;
            const std::function<bool(const EventType &, std::shared_ptr<std::vector<uint8_t>> )> _onEvent;
            std::shared_ptr<std::thread> _threadPtr;
            std::mutex _mutex;
            std::condition_variable _condition;
            bool _runFlag;
            bool _isPause;
            homi_speech::AlsaHelperPlayback _playback;
            std::function<void()> _onFinish;
            static const char * _s_getSampleFormat(const std::string &formatString)
            {
                if(formatString == "s16le") return "S16_LE";
                else if(formatString == "s16be") return "S16_BE";
                else if(formatString == "s32le") return "S32_LE";
                else if(formatString == "s32be") return "S32_BE";
                else if(formatString == "fltp") return "FLOAT";
                else if(formatString == "s8") return "S8";
                else if(formatString == "u8") return "U8";
                else if(formatString == "s24le") return "S24_LE";
                else if(formatString == "s24be") return "S24_BE";
                else if(formatString == "s24_3le") return "S24_3LE";
                else if(formatString == "s24_3be") return "S24_3BE";
                else return formatString.c_str();
            }
        };
        class MediaPlayerStream: public IMediaPlayer,public std::enable_shared_from_this<MediaPlayerStream>
        {
        public:
            MediaPlayerStream(std::shared_ptr<IPulseAudioContext> pulseAudioContextPtr ,int sampleRate, int channels,const std::string &format,bool pause,
                std::function<bool(const EventType, std::shared_ptr<std::vector<uint8_t>> )> onEvent
            ):_sampleRate(sampleRate),_channels(channels),_format(format), _onEvent(onEvent),_pulseAudioContextPtr(pulseAudioContextPtr),_isPause(pause)
            {
                _paStream = nullptr;
                _runFlag = false;
            }
            bool onInit(void)
            {
                MediaPlayerStream::_insertMediaPlayerStreamMap(shared_from_this());
                if(_pulseAudioContextPtr==nullptr)
                {
                    DEBUG("FFmpegCodec:onInit:_pulseAudioContextPtr is nullptr.\n");
                    return false;
                }
                if(_onEvent == nullptr){
                    DEBUG("FFmpegCodec:onInit:_onEvent is nullptr.\n");
                    return false;
                } 
                pa_sample_spec ss;
                ss.channels = _channels;
                ss.format = _getSampleFormat(_format);
                ss.rate = _sampleRate;
                int error;
                if(_createStream(
                    nullptr,                   // 使用默认服务器
                    "MediaPlayerStream",          // 应用程序名
                    nullptr,                   // 使用默认设备
                    "Playback",                // 流名称
                    &ss,
                    nullptr,                   // 使用默认通道映射
                    nullptr,                   // 使用默认缓冲属性
                    &error                     // 错误代码
                )<0)
                {
                    DEBUG("FFmpegCodec:onInit:_createStream failed. (%d)\n",error);
                    return false;
                }
                _runFlag = true;
                _threadPtr = std::make_shared<std::thread>([this](){
                    bool doAgain = true;
                    while(doAgain)
                    {
                        {
                            std::unique_lock<std::mutex> lock(_mutex);
                            _condition.wait(lock,[this](){return !_isPause || !_runFlag;});
                            if(!_runFlag) 
                            {
                                DEBUG("FFmpegCodec:workThread:_runFlag is false.\n");
                                return;
                            }
                        }
                        auto data = std::make_shared<std::vector<uint8_t>>();
                        if(!data) {
                            DEBUG("FFmpegCodec:workThread:data is nullptr.\n");
                            std::this_thread::sleep_for(std::chrono::milliseconds(10));
                            continue;
                        }
                        if(!_onEvent) 
                        {
                            DEBUG("FFmpegCodec:workThread:_onEvent is nullptr.\n");
                            break;
                        }
                        doAgain = _onEvent(EventType::OnDataReady,data);
                        {
                            std::unique_lock<std::mutex> lock(_mutex);
                            _condition.wait(lock,[this](){return !_isPause || !_runFlag;});
                            if(!_runFlag) 
                            {
                                DEBUG("FFmpegCodec:workThread:_runFlag is false.\n");
                                return;
                            }
                        }
                        if(data->size()>0)
                        {
                            int error;
                            if(_writeStream(data->data(),data->size(),&error)<0)
                            {
                                DEBUG("FFmpegCodec:workThread:_writeStream failed. (%d)\n",error);
                            }
                        }
                    }
                    {
                        std::unique_lock<std::mutex> lock(_mutex);
                        _runFlag = false;
                        _condition.notify_all();
                    }
                    DEBUG("FFmpegCodec:workThread:exit.\n");
                });
                if(!_threadPtr) {
                    _runFlag = false;
                    return false;
                }
                return true;
            }
            virtual int pause(void) override
            {
                {
                    std::unique_lock<std::mutex> lock(_mutex);
                    if(!_runFlag) return -1;
                    _isPause = true;
                    _condition.notify_all();
                }
                return _pauseStream(true);;
            }
            virtual int resume(void) override
            {
                {
                    std::unique_lock<std::mutex> lock(_mutex);
                    if(!_runFlag) return -1;
                    _isPause = false;
                    _condition.notify_all();
                }
                
                return _pauseStream(false);;
            }
            virtual int close(void) override
            {
                {
                    std::unique_lock<std::mutex> lock(_mutex);
                    _runFlag = false;
                    _condition.notify_all();
                }
                _closeStream();
                return 0;
            }
            virtual int waitForFinish(std::chrono::milliseconds timeout) override
            {
                {
                    std::unique_lock<std::mutex> lock(_mutex);
                    if(timeout==std::chrono::milliseconds::max()){
                        _condition.wait(lock,[this](){return !_runFlag;});
                    }else{
                        _condition.wait_for(lock,timeout,[this](){return !_runFlag;});
                    }
                    
                }
                if(_runFlag) return -1;
                _drainSream();
                _closeStream();
                return 0;
            }
            virtual int volume(float percent) override
            {
                (void)percent;
                return 0;
            }
            virtual ~MediaPlayerStream() override
            {
                DEBUG("MediaPlayerStream:delete\n");
                MediaPlayerStream::_removeMediaPlayerStreamMap(this);          
                {
                    std::unique_lock<std::mutex> lock(_mutex);
                    _runFlag = false;
                    _condition.notify_all();
                }
                _closeStream();
                if(_threadPtr&&_threadPtr->joinable())
                {
                    DEBUG("MediaPlayerStream:delete:join\n");
                    _threadPtr->join();
                }
                DEBUG("MediaPlayerStream:delete:exit\n");
            }
        private:
            static std::unordered_map<MediaPlayerStream *,std::weak_ptr<MediaPlayerStream>> _mediaPlayerStreamMap;
            static std::mutex _mediaPlayerStreamMapMutex;
            pa_stream * _paStream;
            bool _streamOperationSuccess;
            const int _sampleRate;
            const int _channels;
            const std::string _format;
            const std::function<bool(const EventType &, std::shared_ptr<std::vector<uint8_t>> )> _onEvent;
            std::shared_ptr<IPulseAudioContext> _pulseAudioContextPtr;
            std::shared_ptr<std::thread> _threadPtr;
            std::mutex _mutex;
            std::condition_variable _condition;
            bool _runFlag;
            bool _isPause;
            
            static void _insertMediaPlayerStreamMap(std::shared_ptr<MediaPlayerStream> mediaPlayerStream)
            {
                if(mediaPlayerStream==nullptr)return;
                std::lock_guard<std::mutex> lock(_mediaPlayerStreamMapMutex);
                _mediaPlayerStreamMap.emplace(mediaPlayerStream.get(),mediaPlayerStream);
            }
            static void _removeMediaPlayerStreamMap(MediaPlayerStream *mediaPlayerStream)
            {
                if(mediaPlayerStream==nullptr)return;
                std::lock_guard<std::mutex> lock(_mediaPlayerStreamMapMutex);
                _mediaPlayerStreamMap.erase(mediaPlayerStream);
            }
            static std::shared_ptr<MediaPlayerStream> _voidPtr2MediaPlayerStreamPtr(void *userdata)
            {
                MediaPlayerStream *p = static_cast<MediaPlayerStream*>(userdata);
                if(p==nullptr) return nullptr;
                std::lock_guard<std::mutex> lock(_mediaPlayerStreamMapMutex);
                auto iter = MediaPlayerStream::_mediaPlayerStreamMap.find(p);
                if(iter == MediaPlayerStream::_mediaPlayerStreamMap.end()) return nullptr;
                auto ptr = iter->second.lock();
                return ptr;
            }
            static void stream_state_cb(pa_stream *s, void * userdata) {
                (void)s;
                auto ptr = _voidPtr2MediaPlayerStreamPtr(userdata);
                if(ptr==nullptr)return;
                switch (pa_stream_get_state(s)) {

                    case PA_STREAM_READY:
                    case PA_STREAM_FAILED:
                    case PA_STREAM_TERMINATED:
                        pa_threaded_mainloop_signal(ptr->_pulseAudioContextPtr->getMainLoop(), 0);
                        break;

                    case PA_STREAM_UNCONNECTED:
                    case PA_STREAM_CREATING:
                        break;
                }
            }
            static void stream_request_cb(pa_stream *s, size_t length, void *userdata) {
                (void)s;
                (void)length;
                auto ptr = _voidPtr2MediaPlayerStreamPtr(userdata);
                if(ptr==nullptr)return;

                pa_threaded_mainloop_signal(ptr->_pulseAudioContextPtr->getMainLoop(), 0);
            }
            static void stream_latency_update_cb(pa_stream *s, void *userdata) {
                (void)s;
                auto ptr = _voidPtr2MediaPlayerStreamPtr(userdata);
                if(ptr==nullptr)return;

                pa_threaded_mainloop_signal(ptr->_pulseAudioContextPtr->getMainLoop(), 0);
            }
            static void stream_success_cb(pa_stream *s, int success, void *userdata) {
                (void)s;
                auto ptr = _voidPtr2MediaPlayerStreamPtr(userdata);
                if(ptr==nullptr)return;

                if(success>0) ptr->_streamOperationSuccess = true;

                pa_threaded_mainloop_signal(ptr->_pulseAudioContextPtr->getMainLoop(), 0);
            }
            pa_sample_format_t _getSampleFormat(const std::string &formatString)
            {
                const char * format = formatString.c_str();
                if (strcasecmp(format, "s16le") == 0)
                    return PA_SAMPLE_S16LE;
                else if (strcasecmp(format, "s16be") == 0)
                    return PA_SAMPLE_S16BE;
                else if (strcasecmp(format, "s16ne") == 0 || strcasecmp(format, "s16") == 0 || strcasecmp(format, "16") == 0)
                    return PA_SAMPLE_S16NE;
                else if (strcasecmp(format, "s16re") == 0)
                    return PA_SAMPLE_S16RE;
                else if (strcasecmp(format, "u8") == 0 || strcasecmp(format, "8") == 0)
                    return PA_SAMPLE_U8;
                else if (strcasecmp(format, "float32") == 0 || strcasecmp(format, "float32ne") == 0 || strcasecmp(format, "float") == 0)
                    return PA_SAMPLE_FLOAT32NE;
                else if (strcasecmp(format, "float32re") == 0)
                    return PA_SAMPLE_FLOAT32RE;
                else if (strcasecmp(format, "float32le") == 0)
                    return PA_SAMPLE_FLOAT32LE;
                else if (strcasecmp(format, "float32be") == 0)
                    return PA_SAMPLE_FLOAT32BE;
                else if (strcasecmp(format, "ulaw") == 0 || strcasecmp(format, "mulaw") == 0)
                    return PA_SAMPLE_ULAW;
                else if (strcasecmp(format, "alaw") == 0)
                    return PA_SAMPLE_ALAW;
                else if (strcasecmp(format, "s32le") == 0)
                    return PA_SAMPLE_S32LE;
                else if (strcasecmp(format, "s32be") == 0)
                    return PA_SAMPLE_S32BE;
                else if (strcasecmp(format, "s32ne") == 0 || strcasecmp(format, "s32") == 0 || strcasecmp(format, "32") == 0)
                    return PA_SAMPLE_S32NE;
                else if (strcasecmp(format, "s32re") == 0)
                    return PA_SAMPLE_S24RE;
                else if (strcasecmp(format, "s24le") == 0)
                    return PA_SAMPLE_S24LE;
                else if (strcasecmp(format, "s24be") == 0)
                    return PA_SAMPLE_S24BE;
                else if (strcasecmp(format, "s24ne") == 0 || strcasecmp(format, "s24") == 0 || strcasecmp(format, "24") == 0)
                    return PA_SAMPLE_S24NE;
                else if (strcasecmp(format, "s24re") == 0)
                    return PA_SAMPLE_S24RE;
                else if (strcasecmp(format, "s24-32le") == 0)
                    return PA_SAMPLE_S24_32LE;
                else if (strcasecmp(format, "s24-32be") == 0)
                    return PA_SAMPLE_S24_32BE;
                else if (strcasecmp(format, "s24-32ne") == 0 || strcasecmp(format, "s24-32") == 0)
                    return PA_SAMPLE_S24_32NE;
                else if (strcasecmp(format, "s24-32re") == 0)
                    return PA_SAMPLE_S24_32RE;

                return PA_SAMPLE_INVALID;
            }
            int _createStream(
            const char *server,
            const char *name,
            const char *dev,
            const char *stream_name,
            const pa_sample_spec *ss,
            const pa_channel_map *map,
            const pa_buffer_attr *attr,
            int *rerror) {
                (void)name;
                int error = PA_ERR_INTERNAL, r;
                CHECK_VALIDITY_RETURN_ANY(rerror, !server || *server, PA_ERR_INVALID, -1);
                CHECK_VALIDITY_RETURN_ANY(rerror, !dev || *dev, PA_ERR_INVALID, -1);
                CHECK_VALIDITY_RETURN_ANY(rerror, ss && pa_sample_spec_valid(ss), PA_ERR_INVALID, -1);
                CHECK_VALIDITY_RETURN_ANY(rerror, !map || (pa_channel_map_valid(map) && map->channels == ss->channels), PA_ERR_INVALID, -1)
                pa_threaded_mainloop_lock(_pulseAudioContextPtr->getMainLoop());
                if (!(_paStream = pa_stream_new(_pulseAudioContextPtr->getContext(), stream_name, ss, map))) {
                    error = pa_context_errno(_pulseAudioContextPtr->getContext());
                    goto _createStream_unlock_and_fail;
                }
                pa_stream_set_state_callback(_paStream, stream_state_cb, this);
                pa_stream_set_read_callback(_paStream, stream_request_cb, this);
                pa_stream_set_write_callback(_paStream, stream_request_cb, this);
                pa_stream_set_latency_update_callback(_paStream, stream_latency_update_cb, this);
                r = pa_stream_connect_playback(_paStream, dev, attr,
                                    (pa_stream_flags_t)(PA_STREAM_INTERPOLATE_TIMING|PA_STREAM_ADJUST_LATENCY|PA_STREAM_AUTO_TIMING_UPDATE), NULL, NULL);
                if (r < 0) {
                    error = pa_context_errno(_pulseAudioContextPtr->getContext());
                    goto _createStream_unlock_and_fail;
                }
                for (;;) {
                    pa_stream_state_t state;

                    state = pa_stream_get_state(_paStream);

                    if (state == PA_STREAM_READY)
                        break;

                    if (!PA_STREAM_IS_GOOD(state)) {
                        error = pa_context_errno(_pulseAudioContextPtr->getContext());
                        goto _createStream_unlock_and_fail;
                    }

                    /* Wait until the stream is ready */
                    pa_threaded_mainloop_wait(_pulseAudioContextPtr->getMainLoop());
                }
                pa_threaded_mainloop_unlock(_pulseAudioContextPtr->getMainLoop());
                return 0;
    _createStream_unlock_and_fail:
                if( _paStream){
                    pa_stream_unref(_paStream);
                    _paStream = nullptr;
                }
                pa_threaded_mainloop_unlock(_pulseAudioContextPtr->getMainLoop());

                if (rerror)
                    *rerror = error;
                return -1;
            }
            int _writeStream(const void*data, size_t length, int *rerror) {

                CHECK_VALIDITY_RETURN_ANY(rerror, data, PA_ERR_INVALID, -1);
                CHECK_VALIDITY_RETURN_ANY(rerror, length > 0, PA_ERR_INVALID, -1);
                auto context = _pulseAudioContextPtr->getContext();
                pa_threaded_mainloop_lock(_pulseAudioContextPtr->getMainLoop());

                CHECK_DEAD_GOTO(context, _paStream,rerror, _writeStream_unlock_and_fail);

                while (length > 0) {
                    size_t l;
                    int r;

                    while (!(l = pa_stream_writable_size(_paStream))) {
                        pa_threaded_mainloop_wait(_pulseAudioContextPtr->getMainLoop());
                        CHECK_DEAD_GOTO(context, _paStream, rerror, _writeStream_unlock_and_fail);
                    }

                    CHECK_SUCCESS_GOTO(context, rerror, l != (size_t) -1, _writeStream_unlock_and_fail);

                    if (l > length)
                        l = length;

                    r = pa_stream_write(_paStream, data, l, NULL, 0LL, PA_SEEK_RELATIVE);
                    CHECK_SUCCESS_GOTO(context, rerror, r >= 0, _writeStream_unlock_and_fail);

                    data = (const uint8_t*) data + l;
                    length -= l;
                }

                pa_threaded_mainloop_unlock(_pulseAudioContextPtr->getMainLoop());
                return 0;

    _writeStream_unlock_and_fail:
                pa_threaded_mainloop_unlock(_pulseAudioContextPtr->getMainLoop());
                return -1;
            }
            int _pauseStream(bool pause)
            {
                pa_operation *o = NULL;
                int r = -1;
                int b;
                pa_threaded_mainloop_lock(_pulseAudioContextPtr->getMainLoop());
                if(!_paStream) goto _pauseStream_unlock_and_fail;
                if(pause){
                    b = 1;
                }else{
                    b = 0;
                }
                _streamOperationSuccess = false;
                if (!(o = pa_stream_cork(_paStream, b, stream_success_cb, this)))
                {
                    DEBUG("pa_stream_cork failed \n");
                    goto _pauseStream_unlock_and_fail;
                }
                while (pa_operation_get_state(o) != PA_OPERATION_DONE) {
                    PLAYBACK_STREAM_CHECK_DEAD_GOTO(_pulseAudioContextPtr->getContext(), _paStream, _pauseStream_unlock_and_fail);
                    pa_threaded_mainloop_wait(_pulseAudioContextPtr->getMainLoop());
                }       
                if(!_streamOperationSuccess)goto _pauseStream_unlock_and_fail;

                r = 0;
    _pauseStream_unlock_and_fail:
                if (o)
                    pa_operation_unref(o);
                pa_threaded_mainloop_unlock(_pulseAudioContextPtr->getMainLoop());
                return r;
            }
            void _closeStream(void)
            {
                pa_threaded_mainloop_lock(_pulseAudioContextPtr->getMainLoop());
                if(_paStream){
                    pa_stream_drop(_paStream);
                    pa_stream_unref(_paStream);
                    _paStream = nullptr;
                }
                pa_threaded_mainloop_signal(_pulseAudioContextPtr->getMainLoop(), 0);
                pa_threaded_mainloop_unlock(_pulseAudioContextPtr->getMainLoop());
            }
            int _drainSream(void)
            {
                int r=-1;
                pa_threaded_mainloop_lock(_pulseAudioContextPtr->getMainLoop());
                if(_paStream){
                    pa_operation *o = pa_stream_drain(_paStream, NULL, NULL);
                    if(o) pa_operation_unref(o);
                    pa_stream_unref(_paStream);
                    _paStream = nullptr;
                    r = 0;
                }
                pa_threaded_mainloop_unlock(_pulseAudioContextPtr->getMainLoop());
                return r;
            }
        };
        std::unordered_map<MediaPlayerStream *,std::weak_ptr<MediaPlayerStream>> MediaPlayerStream::_mediaPlayerStreamMap;
        std::mutex MediaPlayerStream::_mediaPlayerStreamMapMutex;
        class MediaPlayerFile: public IMediaPlayer,public std::enable_shared_from_this<MediaPlayerFile>
        {
        public:
            MediaPlayerFile(std::shared_ptr<IPulseAudioContext> pulseAudioContextPtr,const std::string &fileName,const std::vector<std::string> deviceNames
                ,const std::string &taskName
                ,std::function<void(int status,std::string taskName)> onTaskStaus):
            _pulseAudioContextPtr(pulseAudioContextPtr),_fileName(fileName),_deviceNames(deviceNames)
            ,_taskName(taskName),_onTaskStaus(onTaskStaus)
            {}
            bool onInit(void)
            {
                int sampleRate;
                int channels;
                auto pulseAudioContextPtr = _pulseAudioContextPtr.lock();
                if(!pulseAudioContextPtr) return false;
                if(_ffmpegCodec.open(_fileName.c_str(),sampleRate,channels)<0) return false;
                auto ptr = std::make_shared<MediaPlayerStreamAlsa>(_deviceNames,sampleRate,channels,"s16le",true,[this](const EventType event, std::shared_ptr<std::vector<uint8_t>> data){
                    if(event==EventType::OnDataReady)
                    {
                        if(_ffmpegCodec.read(data)<0) return false;
                        else return true;
                    }else{
                        return true;
                    }
                },[this](){
                    if(_onTaskStaus)_onTaskStaus(0,_taskName);
                });
                if(!ptr||!ptr->onInit()) return false;
                DEBUG("MediaPlayerFile::onInit success filename: %s \n",_fileName.c_str());
                _mediaPlayerStream = ptr;
                if(_onTaskStaus)_onTaskStaus(1,_taskName);
                return true;
            }
            virtual int pause(void) override
            {
                return _mediaPlayerStream->pause();
            }
            virtual int resume(void) override
            {
                _mediaPlayerStream->resume();
                return 0;
            }
            virtual int close(void) override
            {
                _ffmpegCodec.close();
                auto ret = _mediaPlayerStream->close();
                if(_onTaskStaus)_onTaskStaus(0,_taskName);
                return ret;
            }
            virtual int waitForFinish(std::chrono::milliseconds timeout) override
            {
                return _mediaPlayerStream->waitForFinish(timeout);
            }
            virtual int volume(float percent) override
            {
                return _mediaPlayerStream->volume(percent);
            }
            virtual ~MediaPlayerFile() override
            {
                DEBUG("MediaPlayerFile::~MediaPlayerFile");
                _ffmpegCodec.close();
                if(_mediaPlayerStream)
                {
                    _mediaPlayerStream->close();
                    _mediaPlayerStream.reset();                    
                }
                DEBUG("MediaPlayerFile::~MediaPlayerFile exit");
            }
            std::weak_ptr<IPulseAudioContext> _pulseAudioContextPtr;
            const std::string _fileName;
            const std::vector<std::string> _deviceNames;
            FFmpegCodec _ffmpegCodec;
            std::shared_ptr<MediaPlayerStreamAlsa> _mediaPlayerStream;
            const std::string _taskName;
            const std::function<void(int status,std::string taskName)> _onTaskStaus;
        };
        class MediaPlayerEngineImpl : public IMediaPlayerEngine, public IPulseAudioContext, public std::enable_shared_from_this<MediaPlayerEngineImpl>
        {
        public:
            MediaPlayerEngineImpl()
            {
                _pulseAudioMainloop = nullptr;
                _pulseAudioContext = nullptr;
            }
            bool onInit(void)
            {
                _insertMediaPlayerEngineImplMap(shared_from_this());
                if(!_initializeFFmpeg()) return false;
                // if(!_initializePulseAudio("media_player_engine")) return false;
                return true;
            }
            virtual std::shared_ptr<IMediaPlayer> createMediaPlayer(const std::string &configJson
                ,const std::string &taskName
                ,std::function<void(int status,const std::string &taskName)> onTaskStaus
                ,std::function<bool(const EventType &, std::shared_ptr<std::vector<uint8_t>> )> onEvent) override
            {
                (void)onEvent;
                auto pulseAudioContextPtr = std::static_pointer_cast<IPulseAudioContext>(shared_from_this());
                try
                {
                    nlohmann::json json = nlohmann::json::parse(configJson);
                    auto deviceNames = json["deviceNames"].get<std::vector<std::string>>();
                    if(json.find("source") != json.end())
                    {
                        auto ptr = std::make_shared<MediaPlayerFile>(pulseAudioContextPtr,json["source"].get<std::string>(),deviceNames,taskName,onTaskStaus);
                        if(ptr->onInit())
                        {
                            return ptr;
                        }
                        else
                        {
                            return nullptr;
                        }
                    }
                }
                catch(const std::exception& e)
                {
                    DEBUG("IMediaPlayerEngeineImpl>createMediaPlayer> %s\n", e.what());
                }
                return nullptr;
            }
            virtual std::vector<std::shared_ptr<IMediaPlayer>> searchMediaPlayers(const std::string &configJson) override
            {
                (void)configJson;
                return {};
            }
            virtual ~MediaPlayerEngineImpl() override
            {
                DEBUG("IMediaPlayerEngeineImpl>~MediaPlayerEngineImpl 1\n");
                _removeMediaPlayerEngineImplMap(this);
                _clean();
                DEBUG("IMediaPlayerEngeineImpl>~MediaPlayerEngineImpl 2\n");
            }
            virtual pa_threaded_mainloop * getMainLoop() override
            {
                return _pulseAudioMainloop;
            }
            virtual pa_context * getContext() override
            {
                return _pulseAudioContext;
            }
            pa_threaded_mainloop * getPulseAudioMainloop(void)
            {
                return _pulseAudioMainloop;
            }
        private:
            static std::unordered_map<MediaPlayerEngineImpl *,std::weak_ptr<MediaPlayerEngineImpl>> _mediaPlayerEngineImplMap;
            static std::mutex _mediaPlayerEngineImplMapMutex;
            pa_threaded_mainloop * _pulseAudioMainloop;
            pa_context * _pulseAudioContext;
            
            bool _initializeFFmpeg() {
                avformat_network_init();
                return true;
            }
            bool _initializePulseAudio(const std::string &appName) {
                _pulseAudioMainloop = pa_threaded_mainloop_new();
                if(_pulseAudioMainloop == nullptr)
                {
                    DEBUG("IMediaPlayerEngeineImpl>_initializePulseAudio> pa_threaded_mainloop_new failed\n");
                    _clean();
                    return false;
                }
                _pulseAudioContext = pa_context_new(pa_threaded_mainloop_get_api(_pulseAudioMainloop), appName.c_str());
                if(_pulseAudioContext == nullptr)
                {
                    DEBUG("IMediaPlayerEngeineImpl>_initializePulseAudio> pa_context_new failed\n");
                    _clean();
                    return false;
                }
                pa_context_set_state_callback(_pulseAudioContext, &_context_state_cb, this);
                if (pa_context_connect(_pulseAudioContext, NULL, (pa_context_flags_t)0, NULL) < 0) {
                    DEBUG("IMediaPlayerEngeineImpl>_initializePulseAudio> pa_context_connect failed\n");
                    _clean();
                    return false;
                }
                pa_threaded_mainloop_lock(_pulseAudioMainloop);
                if (pa_threaded_mainloop_start(_pulseAudioMainloop) < 0)
                {
                    DEBUG("IMediaPlayerEngeineImpl>_initializePulseAudio> pa_threaded_mainloop_start failed\n");
                    pa_threaded_mainloop_unlock(_pulseAudioMainloop);
                    _clean();
                    return false;
                }
                for (;;) {
                    pa_context_state_t state;

                    state = pa_context_get_state(_pulseAudioContext);

                    if (state == PA_CONTEXT_READY)
                        break;

                    if (!PA_CONTEXT_IS_GOOD(state)){
                        DEBUG("IMediaPlayerEngeineImpl>_initializePulseAudio> pa_context_get_state failed\n");
                        pa_threaded_mainloop_unlock(_pulseAudioMainloop);
                        _clean();
                        return false;
                    }

                    /* Wait until the context is ready */
                    pa_threaded_mainloop_wait(_pulseAudioMainloop);
                }
                pa_threaded_mainloop_unlock(_pulseAudioMainloop);
                return true;
            }
            static void _insertMediaPlayerEngineImplMap(std::shared_ptr<MediaPlayerEngineImpl> ptr)
            {
                if(ptr==nullptr) return;
                std::unique_lock<std::mutex> lock(MediaPlayerEngineImpl::_mediaPlayerEngineImplMapMutex);
                MediaPlayerEngineImpl::_mediaPlayerEngineImplMap.emplace(ptr.get(),ptr);
            }
            static void _removeMediaPlayerEngineImplMap(MediaPlayerEngineImpl *ptr)
            {
                if(ptr==nullptr) return;
                std::unique_lock<std::mutex> lock(MediaPlayerEngineImpl::_mediaPlayerEngineImplMapMutex);
                MediaPlayerEngineImpl::_mediaPlayerEngineImplMap.erase(ptr);
            }
            static std::shared_ptr<MediaPlayerEngineImpl> _voidPtr2MediaPlayerEngineImplPtr(void *userdata)
            {
                MediaPlayerEngineImpl *p = static_cast<MediaPlayerEngineImpl*>(userdata);
                if(p==nullptr) return nullptr;
                std::unique_lock<std::mutex> lock(MediaPlayerEngineImpl::_mediaPlayerEngineImplMapMutex);
                auto iter = MediaPlayerEngineImpl::_mediaPlayerEngineImplMap.find(p);
                if(iter == MediaPlayerEngineImpl::_mediaPlayerEngineImplMap.end()) return nullptr;
                auto ptr = iter->second.lock();
                return ptr;
            }
            static void _context_state_cb(pa_context *c, void *userdata) {
                auto state = pa_context_get_state(c);
                if (state >0 && state < 7) {
                    const char* pa_states[] = { "UNCONNECTED","CONNECTING","AUTHORIZING","SETTING_NAME","READY","FAILED","TERMINATED"}; 
                    DEBUG("pulse audio state changed ==> %s\n",pa_states[state]);
                }
                auto ptr = _voidPtr2MediaPlayerEngineImplPtr(userdata);
                if(ptr==nullptr||c==nullptr) return;
                switch (state) {
                    case PA_CONTEXT_READY:
                    case PA_CONTEXT_TERMINATED:
                    case PA_CONTEXT_FAILED:
                        pa_threaded_mainloop_signal(ptr->getPulseAudioMainloop(), 0);
                        break;
                    case PA_CONTEXT_UNCONNECTED:
                    case PA_CONTEXT_CONNECTING:
                    case PA_CONTEXT_AUTHORIZING:
                    case PA_CONTEXT_SETTING_NAME:
                        break;
                }
            }
            void _clean()
            {
                if(_pulseAudioMainloop != nullptr)
                {
                    pa_threaded_mainloop_stop(_pulseAudioMainloop);
                }
                if(_pulseAudioContext != nullptr)
                {
                    pa_context_disconnect(_pulseAudioContext);
                    pa_context_unref(_pulseAudioContext);
                    _pulseAudioContext = nullptr;
                }
                if(_pulseAudioMainloop != nullptr)
                {
                    pa_threaded_mainloop_free(_pulseAudioMainloop);
                    _pulseAudioMainloop = nullptr;
                }
            }
        };
        std::unordered_map<MediaPlayerEngineImpl *,std::weak_ptr<MediaPlayerEngineImpl>> MediaPlayerEngineImpl::_mediaPlayerEngineImplMap;
        std::mutex MediaPlayerEngineImpl::_mediaPlayerEngineImplMapMutex;
        static std::shared_ptr<IMediaPlayerEngine> g_mediaPlayerEnginePtr = nullptr;
        std::mutex g_mediaPlayerEngineMutex;
        std::shared_ptr<IMediaPlayerEngine> getMediaPlayerEngine(void)
        {
            if(g_mediaPlayerEnginePtr == nullptr)
            {
                std::lock_guard<std::mutex> lock(g_mediaPlayerEngineMutex);
                if(g_mediaPlayerEnginePtr == nullptr)
                {
                    auto ptr = std::make_shared<MediaPlayerEngineImpl>();
                    if(ptr->onInit())
                    {
                        g_mediaPlayerEnginePtr = ptr;
                    }
                }
            }
            return g_mediaPlayerEnginePtr;
        }
    }
}
