#ifndef __HOMI_AUDIO_PLAYER_DEF_H__
#define __HOMI_AUDIO_PLAYER_DEF_H__
#define HOMI_SPEECH_SPEECHCORE_SIGC_EVENT_TOPIC "homi_speech_speechcore_sigc_event"
#define HOMI_SPEECH_SPEECHCORE_ASSISTANT_EVENT_TOPIC "homi_speech_speechcore_assistant_event"


#define HOMI_AUDIO_PLAYER_PARAM_DEVICE_NAMES "homi_audio_player_param_device_names"
#define PARAM_SET_DEFAULT_AUDIO_DEVIVCE_SH_FILE  "set_default_audio_device_sh_file"


#define DIR_SEPARATOR       '/'
#define DIR_SEPARATOR_STR   "/"
#define __FILENAME__  (strrchr(DIR_SEPARATOR_STR __FILE__, DIR_SEPARATOR) + 1)
#define HOMI_SPEECH_UNUSED(x) (void)x
#endif // __HOMI_AUDIO_PLAYER_DEF_H__