#ifndef __ROS1_SRC_HOMI_SPEECH_INCLUDE_HOMI_SPEECH_HOMI_ERR_H_
#define __ROS1_SRC_HOMI_SPEECH_INCLUDE_HOMI_SPEECH_HOMI_ERR_H_

#include <string>
#include <system_error>
#define MY_NAMESPACE   homi_speech
#define MY_CATEGORY    homi_

#define FOREACH_ERR(F)   \
    F(0,    OK,             "OK")               \
    F(1000, UNKNOWN,        "Unknown error")    \
    \
    F(1001, NULL_PARAM,     "Null parameter")   \
    F(1002, NULL_POINTER,   "Null pointer")     \
    F(1003, NULL_DATA,      "Null data")        \
    F(1004, NULL_HANDLE,    "Null handle")      \
    \
    F(1011, INVALID_PARAM,      "Invalid parameter")\
    F(1012, INVALID_POINTER,    "Invalid pointer")  \
    F(1013, INVALID_DATA,       "Invalid data")     \
    F(1014, INVALID_HANDLE,     "Invalid handle")   \
    \
    F(2000, ALSA_LIB_ERROR,        "alsa opration is fail")   \
    F(2001, ALSA_HELPER_ERROR,     "alsa helper err")   \
    F(2002, ALSA_PREEMPTED, "alsa helper is preempted")   \
    F(2003, ALSA_CLOSE,     "alsa helper is close")   \
    

#define INCLIDE_MY_DEFINE_ERR
#include "my_define_err.h"
#undef INCLIDE_MY_DEFINE_ERR

#undef MY_NAMESPACE
#undef MY_CATEGORY
#undef FOREACH_ERR

thread_local std::error_code _homi_err;

#endif  // __ROS1_SRC_HOMI_SPEECH_INCLUDE_HOMI_SPEECH_HOMI_ERR_H_
