#ifndef __ROS1_SRC_HOMI_<PERSON>EECH_INCLUDE_HOMI_SPEECH_MY_DEFINE_ERR_H_
#define __ROS1_SRC_HOMI_SPEECH_INCLUDE_HOMI_SPEECH_MY_DEFINE_ERR_H_

#ifdef INCLIDE_MY_DEFINE_ERR
    #include <string>
    #include <system_error>
    #define _STRING(x) #x
    #define STRING(x) _STRING(x)
    #define _SPLIT(x,y) x##y
    #define SPLIT(x,y) _SPLIT(x,y)

    namespace MY_NAMESPACE
    {
        enum class SPLIT(MY_CATEGORY,code)
        {
        #define F(errcode, name, errmsg) name = errcode,
            FOREACH_ERR(F)
        #undef  F
        };

        class SPLIT(MY_CATEGORY,category_) : public std::error_category
        {
            SPLIT(MY_CATEGORY,category_)(SPLIT(MY_CATEGORY,category_)&&) = delete;
            SPLIT(MY_CATEGORY,category_)(const SPLIT(MY_CATEGORY,category_)&) = delete;
            SPLIT(MY_CATEGORY,category_)& operator=(SPLIT(MY_CATEGORY,category_)&&) = delete;
            SPLIT(MY_CATEGORY,category_)& operator=(const SPLIT(MY_CATEGORY,category_)&) = delete;
        
        private:
            SPLIT(MY_CATEGORY,category_)() = default;
        
        public:
            virtual ~SPLIT(MY_CATEGORY,category_)()
            {

            }
            const char* name() const noexcept override
            {
                return STRING(MY_NAMESPACE) "::" STRING(MY_CATEGORY) "error_category";
            }
            std::string message(int ev) const override
            {
                switch (static_cast<SPLIT(MY_CATEGORY,code)>(ev))
                {
                    #define F(errcode, name, errmsg) \
                        case SPLIT(MY_CATEGORY,code)::name: return errmsg;
                        FOREACH_ERR(F)
                    #undef  F
        
                    default:
                        return "Undefined failure";
                }
            }
            friend const std::error_category& SPLIT(MY_CATEGORY,category)();
        };
        const std::error_category& SPLIT(MY_CATEGORY,category)()
        {
            static SPLIT(MY_CATEGORY,category_) _instance;
            return _instance;
        }
        static inline std::error_code make_error_code(SPLIT(MY_CATEGORY,code) status)
        {
            return std::error_code(static_cast<int>(status), SPLIT(MY_CATEGORY,category)());
        }
        static inline std::error_condition make_error_condition(SPLIT(MY_CATEGORY,code) status)
        {
            return std::error_condition(static_cast<int>(status), SPLIT(MY_CATEGORY,category)());
        }
    }
        
    namespace std
    {
        template<>
        struct is_error_code_enum<MY_NAMESPACE::SPLIT(MY_CATEGORY,code)> : true_type
        {
        };
        template<>
        struct is_error_condition_enum<MY_NAMESPACE::SPLIT(MY_CATEGORY,code)> : true_type
        {
        };
    
    }
    #undef _STRING
    #undef STRING
    #undef _SPLIT
    #undef SPLIT
#endif

#endif  // __ROS1_SRC_HOMI_SPEECH_INCLUDE_HOMI_SPEECH_MY_DEFINE_ERR_H_
