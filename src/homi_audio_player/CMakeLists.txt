cmake_minimum_required(VERSION 3.5)
project(homi_audio_player)

if("${CMAKE_BUILD_TYPE}" STREQUAL "Release")
  set(CMAKE_C_FLAGS_RELEASE "${CMAKE_C_FLAGS_RELEASE} -s")
  set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} -s")
endif()

# Default to C99
if(NOT CMAKE_C_STANDARD)
  set(CMAKE_C_STANDARD 99)
endif()

# Default to C++14
if(NOT CMAKE_CXX_STANDARD)
  set(CMAKE_CXX_STANDARD 14)
endif()

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

get_filename_component(SRC_DIR ${CMAKE_CURRENT_SOURCE_DIR} DIRECTORY)
get_filename_component(MAIN_DIR ${SRC_DIR} DIRECTORY)
message(${MAIN_DIR})

# find dependencies
find_package(ament_cmake REQUIRED)
find_package(rclcpp REQUIRED)
find_package(std_msgs REQUIRED)
find_package(ALSA REQUIRED) 
find_package(PkgConfig REQUIRED)
find_package(homi_speech_interface REQUIRED)
find_package(Boost REQUIRED COMPONENTS system locale)
pkg_check_modules(LIBAVFORMAT REQUIRED IMPORTED_TARGET libavformat)
pkg_check_modules(LIBAVCODEC REQUIRED IMPORTED_TARGET libavcodec)
pkg_check_modules(LIBAVUTIL REQUIRED IMPORTED_TARGET libavutil)
pkg_check_modules(LIBSWRESAMPLE REQUIRED IMPORTED_TARGET libswresample)
pkg_check_modules(LIBPULSE REQUIRED IMPORTED_TARGET libpulse-simple)

if(BUILD_TESTING)
  find_package(ament_lint_auto REQUIRED)
  # the following line skips the linter which checks for copyrights
  # uncomment the line when a copyright and license is not present in all source files
  #set(ament_cmake_copyright_FOUND TRUE)
  # the following line skips cpplint (only works in a git repo)
  # uncomment the line when this package is not in a git repo
  #set(ament_cmake_cpplint_FOUND TRUE)
  ament_lint_auto_find_test_dependencies()
endif()

add_library(homi_audio_player
  ${CMAKE_CURRENT_SOURCE_DIR}/src/audio_player.cpp
)
add_compile_definitions(AUDIO_PLAYER_DEBUG_ON=1)
add_compile_definitions(ROS2_DEBUG_ON=1)
target_include_directories(homi_audio_player
  PUBLIC
  $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
  $<INSTALL_INTERFACE:include>
)
target_link_libraries(homi_audio_player
    PkgConfig::LIBAVFORMAT
    PkgConfig::LIBAVCODEC
    PkgConfig::LIBAVUTIL
    PkgConfig::LIBSWRESAMPLE
    PkgConfig::LIBPULSE
    ${ALSA_LIBRARIES}
    ${Boost_LIBRARIES}
    -lpthread  # FFmpeg may require pthread
    -ludev
)

ament_target_dependencies(homi_audio_player rclcpp)
add_executable(homi_player ${CMAKE_CURRENT_SOURCE_DIR}/src/homi_player_node.cpp)
target_include_directories(homi_player
  PUBLIC
  ${MAIN_DIR}/src/homi_speech_interface/include
)
target_link_libraries(homi_player homi_audio_player)
ament_target_dependencies(homi_player rclcpp homi_speech_interface)
install(FILES
  ${CMAKE_CURRENT_SOURCE_DIR}/include/${PROJECT_NAME}/audio_player.h
  DESTINATION include/${PROJECT_NAME}
)
install(TARGETS
  homi_audio_player
  homi_player
  DESTINATION lib/${PROJECT_NAME})
install(DIRECTORY
  launch
  scripts
  DESTINATION share/${PROJECT_NAME})


ament_package()
