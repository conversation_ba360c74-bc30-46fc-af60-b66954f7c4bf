import os
import json
import socket
import time
import subprocess
import fcntl
import rclpy
from rclpy.node import Node
import dbus
import dbus.mainloop.glib
import threading
from gi.repository import GLib
# 导入homi_speech_interface服务 - 最优解
from homi_speech_interface.srv import NetCtrl, SIGCData
from std_msgs.msg import String
import configparser
import struct
import concurrent.futures
from concurrent.futures import ThreadPoolExecutor

import signal
import sys

# 解决导入问题 - 最优解
import os
import sys
from pathlib import Path

import pycurl
from io import BytesIO

# 获取当前文件所在目录和上级目录
current_dir = Path(__file__).parent  # .../network/network/
parent_dir = current_dir.parent      # .../network/
src_dir = parent_dir.parent          # .../src/

# 添加src目录到Python路径，确保可以找到network包
if str(src_dir) not in sys.path:
    sys.path.insert(0, str(src_dir))

try:
    # 尝试相对导入（作为包运行时）
    from .network_manager import NetworkManager
    from .cellular_control import CellularModuleController
except ImportError:
    try:
        # 尝试从network.network导入（从src目录）
        from network.network.network_manager import NetworkManager
        from network.network.cellular_control import CellularModuleController
    except ImportError:
        # 最后尝试直接导入（当前目录）
        sys.path.insert(0, str(current_dir))
        from network_manager import NetworkManager
        from cellular_control import CellularModuleController

class InternetStartNode(Node):
    def _setup_logging(self):
        """配置日志级别 - 支持多种方式配置
        优先级：命令行参数 > 环境变量 > 配置文件参数 > 默认值(INFO)
        """
        import sys

        # 默认日志级别
        log_level = "INFO"

        # 1. 检查命令行参数 (最高优先级)
        if '--log-level' in sys.argv:
            try:
                idx = sys.argv.index('--log-level')
                if idx + 1 < len(sys.argv):
                    log_level = sys.argv[idx + 1].upper()
                    self.get_logger().info(f"📝 使用命令行参数日志级别: {log_level}")
            except (IndexError, ValueError):
                pass

        # 2. 检查环境变量
        elif 'ROS_LOG_LEVEL' in os.environ:
            log_level = os.environ['ROS_LOG_LEVEL'].upper()
            self.get_logger().info(f"📝 使用环境变量日志级别: {log_level}")

        # 3. 配置文件参数会在后面的declare_parameter中处理

        # 设置日志级别
        level_map = {
            'DEBUG': rclpy.logging.LoggingSeverity.DEBUG,
            'INFO': rclpy.logging.LoggingSeverity.INFO,
            'WARN': rclpy.logging.LoggingSeverity.WARN,
            'WARNING': rclpy.logging.LoggingSeverity.WARN,
            'ERROR': rclpy.logging.LoggingSeverity.ERROR,
            'FATAL': rclpy.logging.LoggingSeverity.FATAL
        }

        if log_level in level_map:
            self.get_logger().set_level(level_map[log_level])
            self.current_log_level = log_level
        else:
            self.get_logger().set_level(rclpy.logging.LoggingSeverity.INFO)
            self.current_log_level = "INFO"
            self.get_logger().warn(f"⚠️  无效的日志级别 '{log_level}'，使用默认值 INFO")

    def __init__(self):
        super().__init__('network_node')

        # 配置日志级别 - 支持多种方式
        self._setup_logging()

        self.get_logger().info("初始化 networkNode 节点")

        # 检测调试模式
        self.debug_mode = os.environ.get('NETWORK_DEBUG_MODE', '0') == '1'
        self.test_mode = os.environ.get('NETWORK_TEST_MODE', '0') == '1'

        if self.debug_mode:
            self.get_logger().info("🐛 调试模式已启用")
        if self.test_mode:
            self.get_logger().info("🧪 测试模式已启用 - 将模拟硬件操作")

        # 声明日志级别参数 (配置文件参数，优先级第3)
        self.declare_parameter('log_level', self.current_log_level)
        config_log_level = self.get_parameter('log_level').value.upper()

        # 如果配置文件中的日志级别与当前不同，且没有命令行参数和环境变量，则使用配置文件的
        if (config_log_level != self.current_log_level and
            '--log-level' not in sys.argv and
            'ROS_LOG_LEVEL' not in os.environ):

            level_map = {
                'DEBUG': rclpy.logging.LoggingSeverity.DEBUG,
                'INFO': rclpy.logging.LoggingSeverity.INFO,
                'WARN': rclpy.logging.LoggingSeverity.WARN,
                'WARNING': rclpy.logging.LoggingSeverity.WARN,
                'ERROR': rclpy.logging.LoggingSeverity.ERROR,
                'FATAL': rclpy.logging.LoggingSeverity.FATAL
            }

            if config_log_level in level_map:
                self.get_logger().set_level(level_map[config_log_level])
                self.current_log_level = config_log_level
                self.get_logger().info(f"📝 使用配置文件日志级别: {config_log_level}")

        self.get_logger().info(f"📊 当前日志级别: {self.current_log_level}")

        self.declare_parameter('cellular_option', "enable")
        self.cellular_controller = CellularModuleController(node=self, logger=self.get_logger())

        # self.declare_parameter('device_type', "ysc")
        self.declare_parameter('device_type', "unitree")
        self.device_type = self.get_parameter('device_type').value
        
        self.declare_parameter('wifi_connect_interface', "wlan0")
        self.wifi_interface = self.get_parameter('wifi_connect_interface').value
        self.declare_parameter('mobile_connect_interface', "eth1")
        self.mobile_interface = self.get_parameter('mobile_connect_interface').value
        self.declare_parameter('ethernet_interface', "eth0")
        self.ethernet_interface = self.get_parameter('ethernet_interface').value
        self.declare_parameter('p2p_connect_interface', "wlan1")
        self.ap_interface = self.get_parameter('p2p_connect_interface').value
        self.declare_parameter('timer_interval', 20)  # 定时器时间间隔
        self.timer_interval = self.get_parameter('timer_interval').value

        self.declare_parameter('static_ip', '***********')  # 默认静态 IP 地址
        self.declare_parameter('ssid', 'xiaoli99')  # 默认 SSID
        self.static_ip = self.get_parameter('static_ip').get_parameter_value().string_value

        # DNS服务器配置参数
        self.declare_parameter('dns_primary_servers', ["*********", "************", "***************"])
        self.declare_parameter('dns_backup_servers', ["************", "*******", "*******"])
        self.dns_primary_servers = self.get_parameter('dns_primary_servers').value
        self.dns_backup_servers = self.get_parameter('dns_backup_servers').value

        self.declare_parameter('jwae_tproxy', "enable")
        self.jwae_tproxy_mode=self.get_parameter('jwae_tproxy').get_parameter_value().string_value

        # 网络状态配置文件路径参数
        self.declare_parameter('network_state_config_file', "/etc/cmcc_robot/network_state.conf")
        self.declare_parameter('network_state_lock_file', "/var/lock/network_state.lock")
        self.network_state_config_file = self.get_parameter('network_state_config_file').value
        self.network_state_lock_file = self.get_parameter('network_state_lock_file').value

        # 检查并安装必要的网络工具
        self.check_and_install_network_tools()
        
        if self.device_type == "unitree":
            # 初始化蜂窝网络模块
            self._init_cellular_module()

        # init ssid
        config_file_path = '/etc/cmcc_robot/cmcc_dev.ini'
        devNO = None
        devSn = None
        if os.path.exists(config_file_path):
            cmcc_config = configparser.ConfigParser()
            try:
                cmcc_config.read(config_file_path)
                devNO = cmcc_config.get('factory', 'devNo', fallback=None)
                devSn = cmcc_config.get('factory', 'devSn', fallback=None)
            except configparser.Error as e:
                self.get_logger().info(f"Error: Failed to read configuration file '{config_file_path}'. {e}")
        if devNO is None:
            if devSn is not None:
                devNO = devSn[-5:] if len(devSn) >= 5 else '99'
            else:
                devNO = "99"
        self.ssid = f"xiaoli{devNO}"
        self.get_logger().info(f"配置的AP接口: {self.ap_interface}  静态 IP 地址: {self.static_ip}")

        self.last_freq = None  # 用于记录上一次的频段

        self.network_manager = NetworkManager(
            node=self,
            wifi_interface=self.wifi_interface,
            mobile_interface=self.mobile_interface,
            ap_interface=self.ap_interface,
            logger=self.get_logger(),
            config_file_path=self.network_state_config_file,
            lock_file_path=self.network_state_lock_file
        )  # 移除 dhcp_service 参数

        # self.network_manager.run_ap_start()
        # self.timer = self.create_timer(self.timer_interval, self.network_manager.update_ap_name)
        # self.get_logger().info(f"定时器已启动，每 {self.timer_interval} 秒更新 AP 名称")
        self.network_manager.run_ap_stop()

        # === 创建服务 ===
        self.network_control_service = self.create_service(
            NetCtrl,
            '/homi_speech/network_service',
            self.handle_network_control_request
        )

        self.robot_control_service = self.create_service(
            SIGCData,
            '/homi_speech/sigc_data_service_APP',
            self.handle_robot_control_request
        )

        # === 创建发布者 ===
        self.network_status_publisher = self.create_publisher(String, 'network_status', 10)
        self.network_conflict_publisher = self.create_publisher(String, 'network_conflict', 10)
        self.dns_health_publisher = self.create_publisher(String, 'dns_health_status', 10)

        self.get_logger().info("✅ 网络服务和发布者已初始化")

        # === 设备特定配置 ===
        if self.device_type == "unitree":
            self.network_manager.run_command(f"nmcli dev set {self.wifi_interface} managed no")
        else:
            self.network_manager.run_command(f"nmcli dev set {self.wifi_interface} managed yes")

        self.network_manager.jwae_tproxy_mode=self.jwae_tproxy_mode
        if self.jwae_tproxy_mode == "enable":
            self.get_logger().info("启动tproxy和jwae进程")
            self.network_manager.run_command("sudo bash /usr/bin/cmcc_robot/install/env/jwae_tproxy/stop_jwae_tproxy.sh")
            self.network_manager.run_command("sudo bash /usr/bin/cmcc_robot/install/env/jwae_tproxy/tproxy_rule.sh start")
            self.network_manager.run_command("sudo bash /usr/bin/cmcc_robot/install/env/jwae_tproxy/start_jwae.sh")
            
            # 读取配置，键不存在时返回默认值"on"
            wifi_state = self.network_manager.config.get("wifiState", "on")
            mobile_state = self.network_manager.config.get("mobileDataState", "on")

            # 特殊逻辑：初始化上电时，当两个状态都为 "off" 时，强制设置为 "enable"
            if wifi_state == "off" and mobile_state == "off":
                wifi_value = "enable"
                mobile_value = "enable"
                self.get_logger().info("检测到WiFi和移动数据同时关闭,强制启用状态")
            else:
                # 正常状态转换逻辑
                wifi_value = "enable" if wifi_state == "on" else "disable"
                mobile_value = "enable" if mobile_state == "on" else "disable"

            self.get_logger().info(f"最终网络状态: WiFi={wifi_value}, 移动数据={mobile_value}")

            # 使用pycurl替换原有curl命令
            buffer = BytesIO()
            curl = pycurl.Curl()
            
            try:
                # 设置请求参数
                post_data = json.dumps({"wifi": wifi_value, "mobile": mobile_value})
                curl.setopt(pycurl.URL, 'http://127.0.0.1:12000/setNetworkStatus')
                curl.setopt(pycurl.POST, 1)
                curl.setopt(pycurl.POSTFIELDS, post_data)
                curl.setopt(pycurl.HTTPHEADER, ['Content-Type: application/json'])
                curl.setopt(pycurl.WRITEFUNCTION, buffer.write)
                
                # 设置超时时间（单位：秒）
                curl.setopt(pycurl.TIMEOUT, 10)
                
                # 执行请求
                curl.perform()
                
                # 获取响应
                status_code = curl.getinfo(pycurl.RESPONSE_CODE)
                response = buffer.getvalue().decode()
                
                # 处理响应
                if status_code == 200:
                    try:
                        response_data = json.loads(response)
                        if response_data.get("result") == "success":
                            self.get_logger().info("首次jwae_tproxy开启时setNetworkStatus wifi和mobile从内存中读取状态")
                        else:
                            self.get_logger().error(f"setNetworkStatus返回失败: HTTP {status_code}, 响应: {response}")
                    except Exception as e:
                        self.get_logger().error(f"解析响应失败: {str(e)}, 原始响应: {response}")
                else:
                    self.get_logger().error(f"请求失败: HTTP 状态码 {status_code}")
            
            except pycurl.error as e:
                errno, errstr = e.args
                self.get_logger().error(f"PyCurl请求失败: ({errno}) {errstr}")
            
            finally:
                # 清理资源
                curl.close()
                buffer.close()

        else:
            self.get_logger().info("jwae_tproxy模式未启用,跳过相关进程启动")
            self.network_manager.run_command("sudo bash /usr/bin/cmcc_robot/install/env/jwae_tproxy/stop_jwae_tproxy.sh")        


        # === 启动网络监控 ===
        # 初始网络状态检查（同步）
        self.network_manager.check_network_status()

        # 创建异步网络检查定时器
        self.network_check_timer = self.create_timer(self.timer_interval, self.async_check_network_status)
        self.get_logger().info(f"异步网络检查定时器已启动，每 {self.timer_interval} 秒检查网络状态")

        # 注意：DNS健康检查已集成到网络状态检查中，无需独立的DNS定时器
        # 网络状态检查每20秒执行，当发现连接异常时会自动执行DNS健康检查和修复

        if self.device_type != "unitree":
            self.network_manager.start_dbus_monitor()
            self.get_logger().info("启动 DBus 网络状态监控线程")

        # 冲突检测相关状态
        self.conflict_detected = False
        self.last_conflict_check = 0
        self.conflict_check_interval = 30  # 检测间隔30秒
        self.wlan0_has_ip = False  # 记录wlan0是否有IP
        self.wlan0_ip_detected = False  # 记录wlan0获得IP后是否已检测

        self.bind_mode = False  # 绑定模式标志

        self.bind_subscriber = self.create_subscription(
            String,
            'andlink_network',
            self.bind_notify_callback,
            10
        )
        self.get_logger().info("已创建绑定通知订阅者")

        # 初始化线程池用于异步网络检查
        self.thread_executor = ThreadPoolExecutor(max_workers=2, thread_name_prefix="network_check")
        self.network_check_running = False  # 标记是否有网络检查正在运行
        self.get_logger().info("已初始化异步网络检查线程池")

    def _init_cellular_module(self):
        """根据参数初始化蜂窝网络模块"""
        self.cellular_option = self.get_parameter('cellular_option').value
        self.get_logger().info(f"蜂窝网络模块配置选项: {self.cellular_option}")

        if self.cellular_option == "enable":
            self.get_logger().info("正在初始化蜂窝网络模块...")
            if self.cellular_controller.initialize():
                self.get_logger().info("蜂窝网络模块启动成功")
            else:
                self.get_logger().error("蜂窝网络模块初始化失败,请检查硬件连接")
        else:
            self.get_logger().info("蜂窝网络模块禁用,跳过初始化")

    def async_check_network_status(self):
        """异步网络状态检查入口"""
        if self.network_check_running:
            self.get_logger().debug("网络检查正在运行中，跳过本次检查")
            return

        if self.bind_mode:
            self.get_logger().debug("绑定模式中，跳过网络状态检测")
            return

        # 提交异步任务
        future = self.thread_executor.submit(self._async_network_check_worker)
        future.add_done_callback(self._network_check_completed)
        self.network_check_running = True
        self.get_logger().debug("已提交异步网络检查任务")

    def _async_network_check_worker(self):
        """异步网络检查工作线程"""
        try:
            self.get_logger().debug("开始异步网络状态检查")
            start_time = time.time()

            # 执行网络状态检查
            self.network_manager.check_network_status()

            elapsed_time = time.time() - start_time
            self.get_logger().debug(f"异步网络状态检查完成，耗时: {elapsed_time:.2f}秒")
            return True

        except Exception as e:
            self.get_logger().error(f"异步网络状态检查失败: {e}")
            return False

    def _network_check_completed(self, future):
        """网络检查完成回调"""
        try:
            result = future.result()
            if result:
                self.get_logger().debug("异步网络状态检查成功完成")
            else:
                self.get_logger().warning("异步网络状态检查失败")
        except Exception as e:
            self.get_logger().error(f"异步网络状态检查异常: {e}")
        finally:
            self.network_check_running = False

    def bind_notify_callback(self, msg):
        """处理绑定通知的回调函数"""
        if msg.data == "notify_userbind_start":
            self.get_logger().info("接收到 notify_userbind_start 消息，暂停网络状态检测")
            self.bind_mode = True
            self.network_manager.bind_mode = True

        elif msg.data == "notify_userbind_end":
            self.get_logger().info("接收到 notify_userbind_end 消息，恢复网络状态检测")
            self.bind_mode = False
            self.network_manager.bind_mode = False

            # 恢复后立即检查一次网络状态（异步）
            self.async_check_network_status()

    def destroy_node(self):
        """节点销毁时的清理工作"""
        try:
            self.get_logger().info("正在关闭异步网络检查线程池...")
            self.thread_executor.shutdown(wait=True, timeout=10)
            self.get_logger().info("异步网络检查线程池已关闭")
        except Exception as e:
            self.get_logger().error(f"关闭线程池时发生错误: {e}")
        finally:
            super().destroy_node()

    def pause_timer(self):
        """暂停网络状态检查定时器"""
        if hasattr(self, 'timer') and self.timer is not None:
            self.timer.cancel()
            self.get_logger().info("网络状态检查定时器已暂停")
            self.timer = None

    def resume_timer(self):
        """恢复网络状态检查定时器"""
        if not hasattr(self, 'timer') or self.timer is None:
            self.timer = self.create_timer(
                self.timer_interval, self.async_check_network_status
            )
            self.get_logger().info(f"异步网络状态检查定时器已恢复，每 {self.timer_interval} 秒检查一次")

    def _manage_ap_timer(self, start=True):
        """统一管理AP定时器"""
        if hasattr(self, 'ap_timer'):
            if self.ap_timer is not None:
                self.ap_timer.cancel()
                self.ap_timer = None
                
        if start:
            self.ap_timer = self.create_timer(self.timer_interval, self.network_manager.update_ap_name)
            self.get_logger().info(f"AP名称更新定时器已启动，每 {self.timer_interval} 秒更新一次")

    def handle_network_control_request(self, request, response):
        """
        处理网络控制请求

        Args:
            request: 网络控制请求
            response: 网络控制响应

        Returns:
            response: 处理后的响应
        """
        self.get_logger().info(f"处理网络控制请求: {request.data}")
        return self.network_manager.handle_network_status(request, response)

    def handle_robot_control_request(self, request, response):
        """
        处理机器人控制请求（AP开关控制）

        Args:
            request: 机器人控制请求
            response: 机器人控制响应

        Returns:
            response: 处理后的响应
        """
        self.get_logger().info(f"处理机器人控制请求: {request.data}")

        try:
            if request.data == "openap_notify":
                self.get_logger().info("收到开启AP请求，执行AP启动序列")
                self.network_manager.run_ap_start()
                self._manage_ap_timer(start=True)
                response.error_code = 0

            elif request.data == "closeap_notify":
                self.get_logger().info("收到关闭AP请求，执行AP关闭序列")
                self.network_manager.run_ap_stop()
                self._manage_ap_timer(start=False)
                response.error_code = 0

            else:
                self.get_logger().warning(f"未处理的机器人控制请求: {request.data}")
                response.error_code = 1

        except Exception as e:
            self.get_logger().error(f"处理机器人控制请求时发生错误: {e}")
            response.error_code = 1

        return response

    def check_network_conflict(self):
        """
        优化后的网络冲突检测：只当WiFi接口有IP时检测一次

        检测WiFi接口和移动网络接口是否在同一子网，避免网络冲突
        """
        current_time = time.time()
        # self.get_logger().info("###### enter check network conflict!!!")
        wifi_ip, wifi_netmask = self.get_interface_network(self.wifi_interface)
        has_ip_now = wifi_ip is not None

        if not self.wlan0_has_ip and has_ip_now:
            self.get_logger().info(f"{self.wifi_interface} 获得IP地址，准备进行冲突检测")
            self.wlan0_ip_detected = False  # 新IP，需要检测
            self.conflict_detected = False  # 重置冲突状态

        self.wlan0_has_ip = has_ip_now
        

        if not has_ip_now:
            return  # 没有IP，跳过检测
            
        if self.wlan0_ip_detected:
            return
            
        # 只有在满足时间间隔时才进行检测
        if current_time - self.last_conflict_check < self.conflict_check_interval:
            return
            
        self.last_conflict_check = current_time
        
        try:
            # 获取有线以太网接口的IP信息
            ethernet_ip, ethernet_netmask = self.get_interface_network(self.ethernet_interface)
            if not ethernet_ip or not ethernet_netmask:
                self.get_logger().info(f"{self.ethernet_interface} 没有有效的IP地址，跳过冲突检测")
                return

            ethernet_net_addr = self.calculate_network_address(ethernet_ip, ethernet_netmask)
            wifi_net_addr = self.calculate_network_address(wifi_ip, wifi_netmask)

            # 检查WiFi和有线以太网是否在同一个子网
            if ethernet_net_addr == wifi_net_addr:
                self.get_logger().warning(f"网络冲突: {self.wifi_interface} 和 {self.ethernet_interface} 在同一子网 ({wifi_ip} vs {ethernet_ip})")

                self.publish_conflict_notification()
                self.conflict_detected = True
            else:
                self.conflict_detected = False

            # 标记为已检测
            self.wlan0_ip_detected = True
            
        except Exception as e:
            self.get_logger().error(f"网络冲突检测失败: {e}")
        finally:
            self.wlan0_ip_detected = True

    def publish_conflict_notification(self):
        """
        发布网络冲突通知

        当WiFi和有线以太网接口在同一子网时发布冲突通知
        """
        msg = String()
        msg.data = f"network_conflict_notify_{self.wifi_interface}_{self.ethernet_interface}"
        self.network_conflict_publisher.publish(msg)
        self.get_logger().warning(f"已发布网络冲突通知消息: WiFi({self.wifi_interface}) 与 以太网({self.ethernet_interface}) 冲突")

    # DNS健康检查已集成到网络状态检查中，移除独立的DNS检查方法
    # 当网络连接异常时，check_external_connectivity()会自动执行DNS健康检查和修复

    def publish_dns_status(self, dns_report: dict):
        """发布DNS状态信息"""
        try:
            # 简化DNS状态信息用于发布
            status_info = {
                "current_dns": dns_report.get("current_dns"),
                "failure_count": dns_report.get("failure_count", 0),
                "last_check": dns_report.get("last_check_time", 0),
                "status": "healthy" if dns_report.get("failure_count", 0) < 3 else "degraded"
            }
            
            msg = String()
            msg.data = json.dumps(status_info)
            self.dns_health_publisher.publish(msg)
            self.get_logger().info(f"已发布DNS健康状态: {status_info}")
            
        except Exception as e:
            self.get_logger().error(f"发布DNS状态时发生错误: {e}")

    @staticmethod
    def get_interface_network(interface):
        """获取接口的IP和子网掩码"""
        try:
            cmd = ["ip", "-o", "-4", "addr", "show", "dev", interface]
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            
            if result.returncode == 0 and "inet" in result.stdout:
                line = result.stdout.split("\n")[0]
                parts = line.split()
                ip_with_mask = parts[3]
                
                if "/" in ip_with_mask:
                    ip, mask_bits = ip_with_mask.split("/")
                    mask_bits = int(mask_bits)
                    
                    mask = (0xffffffff >> (32 - mask_bits)) << (32 - mask_bits)
                    netmask = socket.inet_ntoa(struct.pack(">I", mask))
                    
                    return ip, netmask
        except Exception as e:
            return None, None
        return None, None

    @staticmethod
    def calculate_network_address(ip, netmask):
        """计算网络地址"""
        ip_int = int.from_bytes(socket.inet_aton(ip), byteorder="big")
        mask_int = int.from_bytes(socket.inet_aton(netmask), byteorder="big")
        network_addr = ip_int & mask_int
        return network_addr

    def check_and_install_network_tools(self):
        """检查并安装网络相关工具包，如 iputils-ping 和 dnsutils"""
        required_packages = ['iputils-ping', 'dnsutils']
        missing_packages = []
        
        self.get_logger().info("检查网络工具包是否安装...")
        
        for package in required_packages:
            try:
                # 使用dpkg检查软件包是否已安装
                result = subprocess.run(
                    ["dpkg", "-s", package], 
                    stdout=subprocess.PIPE, 
                    stderr=subprocess.PIPE, 
                    text=True
                )
                
                if result.returncode != 0:
                    self.get_logger().info(f"未安装软件包: {package}")
                    missing_packages.append(package)
                else:
                    self.get_logger().info(f"软件包已安装: {package}")
                    
            except Exception as e:
                self.get_logger().error(f"检查软件包 {package} 时出错: {e}")
                missing_packages.append(package)  # 检查失败时也将其标记为缺失
        
        # 如果有缺失的软件包，则安装
        if missing_packages:
            try:
                self.get_logger().info(f"开始安装缺失的软件包: {', '.join(missing_packages)}")
                
                # 使用非交互模式的apt安装
                cmd = ["sudo", "DEBIAN_FRONTEND=noninteractive", "apt-get", "update", "-y"]
                update_result = subprocess.run(
                    cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True
                )
                
                if update_result.returncode != 0:
                    self.get_logger().error(f"更新软件包仓库失败: {update_result.stderr}")
                
                # 安装所有缺失的软件包
                install_cmd = ["sudo", "DEBIAN_FRONTEND=noninteractive", "apt-get", "install", "-y"] + missing_packages
                install_result = subprocess.run(
                    install_cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True
                )
                
                if install_result.returncode == 0:
                    self.get_logger().info(f"成功安装所有缺失的软件包: {', '.join(missing_packages)}")
                else:
                    self.get_logger().error(f"安装软件包失败: {install_result.stderr}")
                    
            except Exception as e:
                self.get_logger().error(f"安装缺失软件包时出错: {e}")
        else:
            self.get_logger().info("所有必要的网络工具包均已安装")

def main(args=None):
    global node
    rclpy.init(args=args)
    node = InternetStartNode()

    conflict_timer = node.create_timer(5, node.check_network_conflict)

    try:
        rclpy.spin(node)
    except KeyboardInterrupt:
        node.get_logger().info("检测到 KeyboardInterrupt, 正在关闭节点...")
    finally:
        pass

if __name__ == "__main__":
    main()