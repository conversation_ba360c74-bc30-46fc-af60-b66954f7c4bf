import json
import os
import re
import socket
import subprocess
import threading
import time
from concurrent.futures import Thread<PERSON>oolExecutor
from threading import Lock
try:
    from .dbus_monitor import DbusMonitor
except ImportError:
    from dbus_monitor import DbusMonitor
from std_msgs.msg import String
import threading

import pycurl
from io import BytesIO

class NetworkManager:
    def __init__(self,node, wifi_interface, mobile_interface, ap_interface, logger, config_file_path=None, lock_file_path=None):
        self.wifi_interface = wifi_interface
        self.mobile_interface = mobile_interface
        self.ap_interface = ap_interface
        self.logger = logger
        self.node = node
        self.device_type = node.device_type  # 获取设备类型
        self.jwae_tproxy_mode= getattr(node, 'jwae_tproxy_mode', 'disable')  # 获取Jwae TProxy模式
        
        # 初始化配置管理器
        try:
            from .network_config_manager import NetworkConfigManager
        except ImportError:
            try:
                from network.network.network_config_manager import NetworkConfigManager
            except ImportError:
                try:
                    from network_config_manager import NetworkConfigManager
                except ImportError:
                    self.logger.error("无法导入NetworkConfigManager类")
                    self.config_manager = None
                    return
        
        try:
            # 使用传入的配置文件路径，如果没有则使用默认路径
            if config_file_path and lock_file_path:
                final_config_file_path = config_file_path
                final_lock_file_path = lock_file_path
                self.logger.info(f"使用配置的网络状态文件路径: {final_config_file_path}")
            else:
                # 默认路径
                config_dir = "/etc/cmcc_robot"
                if not os.path.exists(config_dir):
                    os.makedirs(config_dir, mode=0o755, exist_ok=True)
                    self.logger.info(f"创建网络配置目录: {config_dir}")
                
                final_config_file_path = os.path.join(config_dir, "network_config.json")
                final_lock_file_path = os.path.join(config_dir, "network_config.lock")
                self.logger.info(f"使用默认网络状态文件路径: {final_config_file_path}")
            
            # 确保配置文件目录存在
            config_dir = os.path.dirname(final_config_file_path)
            lock_dir = os.path.dirname(final_lock_file_path)
            
            if not os.path.exists(config_dir):
                os.makedirs(config_dir, mode=0o755, exist_ok=True)
                self.logger.info(f"创建网络配置目录: {config_dir}")
            
            if not os.path.exists(lock_dir):
                os.makedirs(lock_dir, mode=0o755, exist_ok=True)
                self.logger.info(f"创建锁文件目录: {lock_dir}")
            
            self.config_manager = NetworkConfigManager(
                config_file_path=final_config_file_path,
                lock_file_path=final_lock_file_path,
                logger=self.logger
            )
            self.logger.info(f"网络配置管理器初始化成功: {final_config_file_path}")
        except Exception as e:
            self.logger.error(f"初始化网络配置管理器失败: {e}")
            self.config_manager = None

        if self.device_type != "unitree":
            self.dbus_monitor = DbusMonitor(self)
        self.static_ip = node.static_ip
        self.ssid = node.ssid
        self.last_freq = node.last_freq

        self.last_published_status = None
        self.network_connected = False
        self.config = {
            "wifiState": "off",
            "mobileDataState": "off",
            "wifiName": "",
            "isWifiConnect": "false",
            "isInternetConnect": "false"
        }
        self.bind_mode = getattr(node, 'bind_mode', False)

        # DNS服务器配置 - 从节点参数获取
        self.dns_servers = {
            "primary": getattr(node, 'dns_primary_servers', [
                "*********",    # 阿里云DNS (默认)
                "************", # 腾讯DNS (默认)
                "***************", # 114DNS (默认)
            ]),
            "backup": getattr(node, 'dns_backup_servers', [
                "************", # 百度DNS (默认)
                "*******",      # sDNS (默认)
                "*******",      # Google DNS (默认备用)
            ])
        }
        
        # DNS检测状态
        self.dns_status = {
            "current_dns": None,
            "last_check_time": 0,
            "check_interval": 60,  # 60秒检查一次
            "failure_count": 0,
            "max_failures": 3
        }
        
        # WiFi重新扫描控制
        self.wifi_rescan_status = {
            "last_rescan_time": 0,
            "rescan_interval": 30,  # 30秒内不重复扫描
            "consecutive_failures": 0,
            "max_consecutive_failures": 3
        }

        # 异步WiFi扫描相关属性
        self.wifi_scan_executor = ThreadPoolExecutor(max_workers=2, thread_name_prefix="wifi_scan")
        self.wifi_scan_lock = Lock()
        self.wifi_scan_cache = {
            "networks": [],
            "last_scan_time": 0,
            "cache_duration": 30,  # 缓存30秒
            "scan_in_progress": False,
            "scan_future": None,
            "scan_start_time": 0
        }

        self.logger.info(
            f"NetworkManager节点初始化: bind_mode={self.bind_mode}, "
            f"DNS服务器: {self.dns_servers['primary']}, "
            f"初始网络状态: WiFi={self.config['wifiState']}, Mobile={self.config['mobileDataState']}"
        )
        
        # 从配置文件加载状态
        self._load_state_from_config()

    def _load_state_from_config(self):
        """从配置文件加载网络状态并实际控制网卡开关"""
        try:
            if self.config_manager:
                wifi_state = self.config_manager.get_wifi_state()
                mobile_state = self.config_manager.get_mobile_data_state()

                # 转换配置文件格式到内存格式
                wifi_switch = "on" if wifi_state.get("switch_state") == "on" else "off"
                mobile_switch = "on" if mobile_state.get("switch_state") == "on" else "off"
                wifi_set_status = wifi_state.get("wifiSetStatus", 0)
                
                # 更新内存中的配置
                self.config["wifiState"] = wifi_switch
                self.config["mobileDataState"] = mobile_switch
                self.config["wifiSetStatus"] = wifi_set_status
                
                self.logger.info(f"从配置文件加载开关状态: WiFi={wifi_switch}, 5G={mobile_switch}, WiFi配置状态={'已配置' if wifi_set_status == 1 else '未配置'}")
                
                # 根据配置文件状态实际控制网卡
                self._apply_network_states_from_config(wifi_switch, mobile_switch)
                
            else:
                # 使用默认配置
                wifi_switch = "on"
                mobile_switch = "on"
                wifi_set_status = 0  # 默认为未配置状态
                self.config["wifiState"] = wifi_switch
                self.config["mobileDataState"] = mobile_switch
                self.config["wifiSetStatus"] = wifi_set_status
                self.logger.info(f"使用默认开关状态: WiFi={wifi_switch}, 5G={mobile_switch}, WiFi配置状态=未配置")
                
                # 根据默认状态控制网卡
                self._apply_network_states_from_config(wifi_switch, mobile_switch)
                
                # 尝试将默认配置写入配置文件
                if self.config_manager:
                    try:
                        self.config_manager.update_wifi_state(
                            switch_state=wifi_switch
                        )
                        self.config_manager.update_mobile_data_state(
                            switch_state=mobile_switch
                        )
                        self.logger.info("默认WiFi和5G开关状态已写入配置文件")
                    except Exception as write_err:
                        self.logger.error(f"写入默认开关状态到配置文件失败: {write_err}")
        except Exception as e:
            self.logger.error(f"从配置文件加载状态失败: {e}")
            # 使用默认配置
            wifi_switch = "on"
            mobile_switch = "on"
            self.config["wifiState"] = wifi_switch
            self.config["mobileDataState"] = mobile_switch
            self._apply_network_states_from_config(wifi_switch, mobile_switch)

    def _apply_network_states_from_config(self, wifi_switch: str, mobile_switch: str):
        """根据配置文件中的开关状态实际控制网卡"""
        try:
            # 控制WiFi网卡
            if wifi_switch == "on":
                self.logger.info(f"根据配置文件启用WiFi网卡: {self.wifi_interface}")
                self._enable_wifi_interface()
            else:
                self.logger.info(f"根据配置文件禁用WiFi网卡: {self.wifi_interface}")
                self._disable_wifi_interface()
            
            # 控制5G/移动网卡
            if mobile_switch == "on":
                self.logger.info(f"根据配置文件启用移动网卡: {self.mobile_interface}")
                self._enable_mobile_interface()
            else:
                self.logger.info(f"根据配置文件禁用移动网卡: {self.mobile_interface}")
                self._disable_mobile_interface()
                
            self.logger.info(f"网卡状态应用完成: WiFi={wifi_switch}, 移动网络={mobile_switch}")
            
        except Exception as e:
            self.logger.error(f"应用网卡状态失败: {e}")
            # 记录具体的错误但不中断初始化过程

    def modify_json(self, key, value):
        """修改配置中的键值并同步到配置文件"""
        self.logger.info(f"修改配置中的键: {key}, 值: {value}")
        self.config[key] = value

        # 同步到配置文件
        self._sync_to_config_file(key, value)

    def _sync_to_config_file(self, key, value):
        """将状态变更同步到配置文件 - 同步WiFi、5G开关状态和WiFi配置状态"""
        try:
            if not self.config_manager:
                self.logger.warning("配置管理器未初始化，跳过文件同步")
                return

            # 同步WiFi、5G开关状态和WiFi配置状态
            if key == "wifiState":
                self.config_manager.update_wifi_state(
                    switch_state=value
                )
                self.logger.debug(f"WiFi开关状态已同步到配置文件: {value}")
            elif key == "mobileDataState":
                self.config_manager.update_mobile_data_state(
                    switch_state=value
                )
                self.logger.debug(f"5G开关状态已同步到配置文件: {value}")
            elif key == "wifiSetStatus":
                self.config_manager.update_wifi_set_status(value)
                self.logger.debug(f"WiFi配置状态已同步到配置文件: {'已配置' if value == 1 else '未配置'}")
            else:
                # 其他状态不同步到配置文件
                self.logger.debug(f"跳过同步非开关状态: {key}={value}")
                return

        except Exception as e:
            self.logger.error(f"同步状态到配置文件失败: {e}")
            # 不抛出异常，避免影响主要功能

    def get_wifi_set_status(self) -> int:
        """
        获取WiFi配置状态
        
        Returns:
            int: WiFi配置状态 (0-未配置、1-已配置)
        """
        return self.config.get("wifiSetStatus", 0)

    def set_wifi_set_status(self, status: int):
        """
        设置WiFi配置状态
        
        Args:
            status: WiFi配置状态 (0-未配置、1-已配置)
        """
        if status not in [0, 1]:
            raise ValueError("wifiSetStatus 必须是 0 (未配置) 或 1 (已配置)")
        
        self.logger.info(f"设置WiFi配置状态为: {'已配置' if status == 1 else '未配置'}")
        self.modify_json("wifiSetStatus", status)

    def publish_network_status(self):
        """
        发布网络状态（仅在状态变更时发布）

        Note:
            使用新的network_status_publisher发布网络状态信息
        """
        current_status = json.dumps(self.config)
        if current_status != self.last_published_status:
            self.last_published_status = current_status
            msg = String()
            msg.data = current_status

            # 使用新的网络状态发布者
            if hasattr(self.node, 'network_status_publisher'):
                self.node.network_status_publisher.publish(msg)
                self.logger.info(f'发布网络状态: "{msg.data}"')
            else:
                # 向后兼容：如果新发布者不存在，使用旧的
                if hasattr(self.node, 'publisher_'):
                    self.node.publisher_.publish(msg)
                    self.logger.info(f'发布网络状态（兼容模式）: "{msg.data}"')

    def delete_lower_priority_ug_route(self, interface):
        """删除指定网卡上 metric 较大的 UG 路由（优先级低的那条）"""
        try:
            routes = self.get_interface_routes(interface)
            if len(routes) <= 1:
                self.logger.info(f"{interface} 没有多余的 UG 路由，无需删除")
                return True

            # 解析路由并按 metric 排序
            parsed_routes = []
            for route in routes:
                parts = route.split()
                if len(parts) >= 8:  # 确保路由信息完整
                    try:
                        metric = int(parts[4])
                        gateway = parts[1]
                        parsed_routes.append((metric, gateway, route))
                    except (ValueError, IndexError) as e:
                        self.logger.warning(f"解析路由信息失败: {route}, 错误: {e}")
            
            if not parsed_routes:
                self.logger.warning(f"{interface} 没有可解析的 UG 路由")
                return False
                
            # 按 metric 从大到小排序（高的先删除）
            parsed_routes.sort(reverse=True)
            
            # 删除 metric 最大的路由
            metric, gateway, route_info = parsed_routes[0]
            del_cmd = f"sudo route del default gw {gateway} {interface}"
            result = self.run_command(del_cmd)
            
            if result is not None:
                self.logger.info(f"成功删除 {interface} 上 metric={metric} 的 UG 路由")
                return True
            else:
                self.logger.error(f"删除 {interface} 上 metric={metric} 的 UG 路由失败")
                return False
        except Exception as e:
            self.logger.error(f"删除 {interface} 的低优先级 UG 路由时发生错误: {e}")
            return False

    def _should_rescan_wifi(self, wifi_up: bool, wifi_internet: bool) -> bool:
        """
        判断是否应该执行WiFi重新扫描
        :param wifi_up: WiFi接口是否UP且有IP
        :param wifi_internet: WiFi是否能访问外网
        :return: True 需要重新扫描，False 不需要
        """
        current_time = time.time()
        
        # 如果接口完全DOWN或者没有IP，不需要重新扫描（可能是硬件问题或未连接）
        if not wifi_up:
            self.logger.debug(f"WiFi接口 {self.wifi_interface} DOWN或无IP，跳过重新扫描")
            # 重置连续失败计数
            self.wifi_rescan_status["consecutive_failures"] = 0
            return False
        
        # 如果接口UP且有网络连接，不需要重新扫描
        if wifi_internet:
            # 重置连续失败计数
            self.wifi_rescan_status["consecutive_failures"] = 0
            return False
        
        # 检查是否在重新扫描间隔内
        time_since_last_rescan = current_time - self.wifi_rescan_status["last_rescan_time"]
        if time_since_last_rescan < self.wifi_rescan_status["rescan_interval"]:
            self.logger.debug(f"距离上次WiFi重新扫描仅 {time_since_last_rescan:.1f} 秒，跳过本次扫描")
            return False
        
        # 检查连续失败次数，避免无限重试
        if self.wifi_rescan_status["consecutive_failures"] >= self.wifi_rescan_status["max_consecutive_failures"]:
            self.logger.warning(f"WiFi重新扫描连续失败 {self.wifi_rescan_status['consecutive_failures']} 次，暂停自动扫描")
            return False
        
        # 检查接口是否真的有IP地址但无法访问外网
        try:
            # 获取接口详细状态
            status = subprocess.run(
                ["ip", "addr", "show", self.wifi_interface],
                capture_output=True,
                text=True,
                check=True
            )
            output = status.stdout
            
            # 检查是否有有效的IP地址（非169.254.x.x自动分配地址）
            ip_match = re.search(r"inet (\d+\.\d+\.\d+\.\d+)", output)
            if ip_match:
                ip_address = ip_match.group(1)
                # 如果是自动分配的链路本地地址，说明DHCP失败，需要重新扫描
                if ip_address.startswith("169.254."):
                    self.logger.info(f"WiFi接口 {self.wifi_interface} 获得链路本地地址 {ip_address}，需要重新扫描")
                    return True
                
                # 如果有正常IP但无法访问外网，可能是网络配置问题，需要重新扫描
                self.logger.info(f"WiFi接口 {self.wifi_interface} 有IP {ip_address} 但无法访问外网，需要重新扫描")
                return True
            else:
                # 接口UP但没有IP地址，需要重新扫描
                self.logger.info(f"WiFi接口 {self.wifi_interface} UP但没有IP地址，需要重新扫描")
                return True
                
        except Exception as e:
            self.logger.error(f"检查WiFi接口详细状态时发生错误: {e}")
            # 出错时保守处理，不执行重新扫描
            return False

    def rescan_and_connect_wifi(self):
        """在子线程中执行 WiFi 扫描和连接"""
        current_time = time.time()
        
        # 更新最后扫描时间
        self.wifi_rescan_status["last_rescan_time"] = current_time
        
        self.logger.info(f"开始WiFi重新扫描和连接 (第 {self.wifi_rescan_status['consecutive_failures'] + 1} 次尝试)")
        
        try:
            if self.device_type == "unitree":
                # unitree设备使用wpa相关命令
                self.run_command(f"sudo wpa_cli -i {self.wifi_interface} disconnect")
                time.sleep(2)
                self.run_command(f"sudo wpa_cli -i {self.wifi_interface} reconnect")
                self.logger.info("使用wpa_cli重新连接WiFi")
            else:
                # ysc设备使用nmcli命令
                self.logger.info("WiFi 网络不可用，重新扫描 WiFi 网络")
                self.run_command("sudo nmcli dev wifi list --rescan yes > /dev/null 2>&1")
                time.sleep(5)
                self.run_command(f"sudo nmcli dev connect {self.wifi_interface}")
                self.logger.info("使用nmcli重新连接WiFi")
            
            # 等待一段时间让连接生效
            time.sleep(3)
            
            # 检查连接是否成功
            wifi_up_after = self.check_interface_status(self.wifi_interface)
            wifi_internet_after = self.check_external_connectivity(self.wifi_interface) if wifi_up_after else False
            
            if wifi_up_after and wifi_internet_after:
                self.logger.info("WiFi重新扫描和连接成功")
                # 重置连续失败计数
                self.wifi_rescan_status["consecutive_failures"] = 0
            else:
                self.logger.warning("WiFi重新扫描和连接后仍无法正常工作")
                # 增加连续失败计数
                self.wifi_rescan_status["consecutive_failures"] += 1
                
        except Exception as e:
            self.logger.error(f"WiFi重新扫描和连接过程中发生错误: {e}")
            # 增加连续失败计数
            self.wifi_rescan_status["consecutive_failures"] += 1

    def check_network_status_legacy(self):
        """旧版网络状态检查方法（保留向后兼容性）"""
        self.logger.warning("使用旧版网络状态检查方法，建议使用新版 check_network_status() 方法")
        self.logger.info("检查网络状态（旧版逻辑）")
        bind_mode_value = self.bind_mode
        self.logger.info(f"进入网络检测: bind_mode={bind_mode_value}")
        if self.bind_mode:
            # 绑定模式时跳过检测
            self.logger.info("绑定模式中，跳过网络状态检测")
            return

        previous_wifi_connected = self.config.get("wifiState") == "on"
        previous_mobile_connected = self.config.get("mobileDataState") == "on"

        # 检查 WiFi 网卡状态
        wifi_up = self.check_interface_status(self.wifi_interface)
        wifi_internet = self.check_external_connectivity(self.wifi_interface) if wifi_up else False
        if wifi_up and wifi_internet:
            # self.delete_lower_priority_ug_route(self.wifi_interface)
            self.modify_json("isWifiConnect", "true")
            self.modify_json("wifiState", "on")
            wifi_name = self.get_wifi_name()
            self.modify_json("wifiName", wifi_name)

            wifi_metric = self.get_interface_metric(self.wifi_interface)
            mobile_metric = self.get_interface_metric(self.mobile_interface)
            if wifi_metric is not None and mobile_metric is not None and wifi_metric > mobile_metric:
                self.logger.info(f"WiFi 跃点值 ({wifi_metric}) 大于移动网络跃点值 ({mobile_metric})，更新 WiFi 跃点值")
                self.set_interface_metric(self.wifi_interface, mobile_metric - 1)
        else:
            self.modify_json("isWifiConnect", "false")
            self.modify_json("wifiState", "off")
            self.modify_json("wifiName", "")

        # 检查移动网络状态
        mobile_up = self.check_interface_status(self.mobile_interface)
        mobile_internet = self.check_external_connectivity(self.mobile_interface) if mobile_up else False
        if mobile_up and mobile_internet:
            self.modify_json("mobileDataState", "on")
            self.delete_lower_priority_ug_route(self.mobile_interface)
        else:
            self.modify_json("mobileDataState", "off")

        # 更新总体网络状态
        self.network_connected = self.config.get("wifiState") == "on" or self.config.get("mobileDataState") == "on"
        self.modify_json("isInternetConnect", "true" if self.network_connected else "false")

        # 获取当前状态
        current_wifi_connected = self.config.get("wifiState") == "on"
        current_mobile_connected = self.config.get("mobileDataState") == "on"

        # 如果状态发生变化，则更新路由规则
        if (previous_wifi_connected != current_wifi_connected) or (previous_mobile_connected != current_mobile_connected):
            self.logger.info(f"网络状态发生变化: WiFi={current_wifi_connected}, 移动数据={current_mobile_connected}")
            self.run_command("sudo systemctl restart systemd-resolved")
            # self.configure_triple_network(current_wifi_connected,current_mobile_connected)

        self.logger.info("网络状态检查完成（旧版）")
        self.publish_network_status()

    def check_network_status(self):
        """检查网络状态，分离开关状态和连接状态检测，包含DNS健康监测功能"""
        bind_mode_value = self.bind_mode
        self.logger.info(f"进入网络检测: bind_mode={bind_mode_value}")
        if self.bind_mode:
            # 绑定模式时跳过检测
            self.logger.info("绑定模式中，跳过网络状态检测")
            return

        self.logger.info("检查网络状态（分离开关状态和连接状态，包含DNS健康监测）")

        # 保存之前的连接状态用于变化检测
        previous_wifi_connected = self.config.get("isWifiConnect") == "true"
        # 移动网络实际连接状态：通过互联网连接且WiFi未连接来推断
        # 注意：这里检测的是连接状态，不是开关状态
        previous_mobile_internet_connected = (self.config.get("isInternetConnect") == "true" and
                                            self.config.get("isWifiConnect") == "false")
        previous_internet_connected = self.config.get("isInternetConnect") == "true"

        # === 第一步：检查开关状态 ===
        if self.jwae_tproxy_mode == "enable":
            ##从内存中获取开关状态
            wifi_switch_on = self.config.get("wifiState")
            mobile_switch_on = self.config.get("mobileDataState")
            self.logger.debug(f"jwae_tproxy_mode 开关状态检测 - WiFi开关: {wifi_switch_on}, 移动数据开关: {mobile_switch_on}")
        else:    
            wifi_switch_on = self.check_wifi_switch_status()
            mobile_switch_on = self.check_mobile_switch_status()

            self.logger.debug(f"开关状态检测 - WiFi开关: {'开启' if wifi_switch_on else '关闭'}, 移动数据开关: {'开启' if mobile_switch_on else '关闭'}")

            # 更新开关状态
            self.modify_json("wifiState", "on" if wifi_switch_on else "off")
            self.modify_json("mobileDataState", "on" if mobile_switch_on else "off")

        # === 第二步：检查连接状态（仅在开关开启时检查）===
        wifi_connected = False
        mobile_connected = False
        wifi_internet = False
        mobile_internet = False

        # WiFi连接状态检查
        if wifi_switch_on:
            wifi_up = self.check_interface_status(self.wifi_interface)
            if wifi_up:
                wifi_internet = self.check_external_connectivity(self.wifi_interface)
                if wifi_internet:
                    wifi_connected = True
                    wifi_name = self.get_wifi_name()
                    self.modify_json("wifiName", wifi_name)
                    self.logger.debug(f"WiFi连接成功: {wifi_name}")

                    # 优化跃点值
                    wifi_metric = self.get_interface_metric(self.wifi_interface)
                    mobile_metric = self.get_interface_metric(self.mobile_interface)
                    if wifi_metric is not None and mobile_metric is not None and wifi_metric > mobile_metric:
                        self.logger.info(f"WiFi 跃点值 ({wifi_metric}) 大于移动网络跃点值 ({mobile_metric})，更新 WiFi 跃点值")
                        self.set_interface_metric(self.wifi_interface, mobile_metric - 1)
                else:
                    self.logger.warning(f"WiFi接口 {self.wifi_interface} 外网连接异常，已自动执行DNS健康检查")

        # 如果WiFi开关关闭或连接失败，清空WiFi名称
        if not wifi_connected:
            self.modify_json("wifiName", "")

        # 移动网络连接状态检查
        if mobile_switch_on:
            mobile_up = self.check_interface_status(self.mobile_interface)
            if mobile_up:
                mobile_internet = self.check_external_connectivity(self.mobile_interface)
                if mobile_internet:
                    mobile_connected = True
                    self.delete_lower_priority_ug_route(self.mobile_interface)
                    self.logger.debug("移动网络连接成功")
                else:
                    self.logger.warning(f"移动网络接口 {self.mobile_interface} 外网连接异常，已自动跳出执行DNS健康检查")

        # === 第三步：更新连接状态 ===
        self.modify_json("isWifiConnect", "true" if wifi_connected else "false")

        # 总体互联网连接状态：任一网络能访问外网即为连接
        internet_connected = wifi_internet or mobile_internet
        self.modify_json("isInternetConnect", "true" if internet_connected else "false")

        # 更新内部状态
        self.network_connected = internet_connected

        # === 第四步：处理状态变化 ===
        current_wifi_connected = wifi_connected
        # 当前移动网络实际连接状态：有互联网连接且WiFi未连接
        current_mobile_internet_connected = internet_connected and not wifi_connected
        current_internet_connected = internet_connected

        # 检查是否有状态变化
        state_changed = (
            previous_wifi_connected != current_wifi_connected or
            previous_mobile_internet_connected != current_mobile_internet_connected or
            previous_internet_connected != current_internet_connected
        )

        if state_changed:
            self.logger.info(f"网络状态发生变化: WiFi连接={current_wifi_connected}, 移动网络连接={current_mobile_internet_connected}, 互联网连接={current_internet_connected}")
            self.run_command("sudo systemctl restart systemd-resolved")
            self.configure_triple_network(current_wifi_connected, current_internet_connected)

            # 网络连接状态变化时，重新检测最佳DNS服务器
            if current_internet_connected:
                self.logger.info("网络连接状态改变，重新检测最佳DNS服务器")
                best_dns = self.find_best_dns_server()
                self.update_system_dns([best_dns])

        # 发布增强的网络状态信息（包含DNS信息）
        self.publish_enhanced_network_status(wifi_internet, mobile_internet)

        self.logger.info("网络状态检查完成（分离开关状态和连接状态，包含DNS健康监测）")

    def publish_enhanced_network_status(self, wifi_internet_connected: bool, mobile_internet_connected: bool):
        """
        发布增强的网络状态信息，包含DNS信息

        Args:
            wifi_internet_connected (bool): WiFi是否能访问互联网
            mobile_internet_connected (bool): 移动网络是否能访问互联网

        Note:
            这里的参数表示的是实际的互联网连接状态，不是开关状态
        """
        try:
            # 发布原有的网络状态
            self.publish_network_status()
            
            # 发布增强的连接状态信息
            enhanced_status = {
                "wifi_internet_connected": wifi_internet_connected,
                "mobile_internet_connected": mobile_internet_connected,
                "overall_internet_connected": wifi_internet_connected or mobile_internet_connected,
                "dns_server": self.dns_status.get("current_dns"),
                "dns_failure_count": self.dns_status.get("failure_count", 0),
                "timestamp": time.time(),
                "config": self.config.copy()  # 包含完整的网络配置
            }
            
            # 发布增强网络状态到专门的主题
            if hasattr(self.node, 'dns_health_publisher'):
                from std_msgs.msg import String
                msg = String()
                msg.data = json.dumps(enhanced_status)
                self.node.dns_health_publisher.publish(msg)
                self.logger.debug(f"已发布增强网络状态到DNS健康主题: {enhanced_status}")
            else:
                self.logger.warning("DNS健康状态发布者未初始化，无法发布增强网络状态")
            
        except Exception as e:
            self.logger.error(f"发布增强网络状态时发生错误: {e}")

    def check_wifi_switch_status(self):
        """
        检查WiFi开关状态 - 使用ip link命令统一检测

        Returns:
            bool: True表示WiFi开关开启，False表示关闭
        """
        self.logger.debug("开始检查WiFi开关状态")

        try:
            result = subprocess.run(
                ["sudo", "ip", "link", "show", self.wifi_interface],
                capture_output=True,
                text=True,
                check=True,
                timeout=5
            )
            output = result.stdout.strip()
            self.logger.debug(f"WiFi接口状态: {output}")

            # 检查接口是否存在
            if f"{self.wifi_interface}:" in output:
                # 接口存在，检查状态
                output_lower = output.lower()
                if "state up" in output_lower:
                    self.logger.debug("WiFi开关状态: 开启 (接口UP)")
                    return True
                elif "state down" in output_lower:
                    self.logger.debug("WiFi开关状态: 关闭 (接口DOWN)")
                    return False
                else:
                    # 接口存在但状态未知，认为是开启的
                    self.logger.debug("WiFi开关状态: 开启 (接口存在)")
                    return True
            else:
                self.logger.debug("WiFi开关状态: 关闭 (接口不存在)")
                return False

        except Exception as e:
            self.logger.warning(f"ip link检查WiFi状态失败: {e}")
            return False

    def check_mobile_switch_status(self):
        """
        检查移动数据网卡开关状态 - 统一的状态检测方法

        Returns:
            bool: True表示移动数据网卡开关开启，False表示关闭

        Note:
            这个方法检查的是硬件开关状态，不是连接状态
            移动数据开关开启不代表网络连接成功
        """
        self.logger.debug("开始检查移动数据开关状态")

        # 统一方法：使用sudo ip link作为权威来源
        try:
            result = subprocess.run(
                ["sudo", "ip", "link", "show", self.mobile_interface],
                capture_output=True,
                text=True,
                check=True,
                timeout=5
            )
            output = result.stdout.strip()
            self.logger.debug(f"移动网络接口状态: {output}")

            # 检查接口是否存在
            if f"{self.mobile_interface}:" in output:
                # 接口存在，检查状态
                output_lower = output.lower()
                if "state up" in output_lower:
                    self.logger.debug("移动数据开关状态: 开启 (接口UP)")
                    return True
                elif "state down" in output_lower:
                    self.logger.debug("移动数据开关状态: 关闭 (接口DOWN)")
                    return False
                else:
                    # 接口存在但状态未知，认为是开启的
                    self.logger.debug("移动数据开关状态: 开启 (接口存在)")
                    return True
            else:
                self.logger.debug("移动数据开关状态: 关闭 (接口不存在)")
                return False

        except Exception as e:
            self.logger.warning(f"ip link检查移动数据状态失败: {e}")

        # 如果ip命令失败，返回保守的默认值
        self.logger.warning("ip命令检查失败，返回默认值: False")
        return False

    def get_default_route_interface(self):
        """获取默认路由网卡（跃点数最小的网卡）"""
        try:
            # 获取路由表
            result = subprocess.run(
                ["ip", "route", "show", "default"],
                capture_output=True,
                text=True,
                check=True
            )

            if not result.stdout.strip():
                self.logger.warning("未找到默认路由")
                return None

            # 解析默认路由，找到跃点数最小的接口
            routes = []
            for line in result.stdout.strip().split('\n'):
                if 'default' in line and 'dev' in line:
                    # 解析路由信息: default via ************ dev wlp1s0 metric 100
                    parts = line.split()
                    interface = None
                    metric = 0  # 默认跃点数为0

                    for i, part in enumerate(parts):
                        if part == 'dev' and i + 1 < len(parts):
                            interface = parts[i + 1]
                        elif part == 'metric' and i + 1 < len(parts):
                            try:
                                metric = int(parts[i + 1])
                            except ValueError:
                                metric = 0

                    if interface:
                        routes.append((interface, metric))

            if not routes:
                self.logger.warning("未找到有效的默认路由接口")
                return None

            # 按跃点数排序，选择跃点数最小的接口
            routes.sort(key=lambda x: x[1])
            default_interface = routes[0][0]
            default_metric = routes[0][1]

            self.logger.debug(f"默认路由接口: {default_interface}, 跃点数: {default_metric}")
            return default_interface

        except Exception as e:
            self.logger.error(f"获取默认路由接口时发生错误: {e}")
            return None

    def check_interface_status(self, interface):
        """检查接口是否处于 UP 状态并有 IP 地址"""
        try:
            # 执行命令获取接口状态
            status = subprocess.run(
                ["ip", "addr", "show", interface],
                capture_output=True,
                text=True,
                check=True
            )
            output = status.stdout

            # 检查接口是否处于 UP 状态
            # self.logger.info(f"output: {output}")
            is_up = "state UP" in output

            # 使用正则表达式提取 IPv4 地址
            ip_match = re.search(r"inet (\d+\.\d+\.\d+\.\d+)", output)
            has_ip = ip_match is not None
            ip_address = ip_match.group(1) if has_ip else None
            self.logger.info(f"接口 {interface} 状态: UP={is_up}, 有IP={has_ip}")
            return is_up and has_ip
        except Exception as e:
            self.logger.error(f"检查接口 {interface} 状态时发生错误: {e}")
            return False

    def _check_interface_has_valid_ip(self, interface: str) -> bool:
        """
        检查接口是否有有效的IP地址（排除链路本地地址）
        :param interface: 网卡接口名称
        :return: True 有有效IP，False 无有效IP
        """
        try:
            status = subprocess.run(
                ["ip", "-4", "addr", "show", interface],
                capture_output=True,
                text=True,
                check=True
            )
            output = status.stdout
            
            # 查找IPv4地址，排除链路本地地址(169.254.x.x)
            ip_matches = re.findall(r"inet (\d+\.\d+\.\d+\.\d+)", output)
            
            for ip in ip_matches:
                # 排除回环地址和链路本地地址
                if not ip.startswith("127.") and not ip.startswith("169.254."):
                    self.logger.debug(f"接口 {interface} 有有效IP地址: {ip}")
                    return True
            
            self.logger.debug(f"接口 {interface} 没有有效的IP地址")
            return False
            
        except Exception as e:
            self.logger.error(f"检查接口 {interface} IP地址时发生错误: {e}")
            return False

    def _should_rescan_wifi(self, wifi_up: bool, wifi_internet: bool) -> bool:
        """
        判断是否应该执行WiFi重新扫描
        只有在以下情况下才执行重新扫描：
        1. 接口完全DOWN（没有UP状态，通常也没有IP）
        2. 接口UP但没有有效IP地址
        3. 接口UP且有IP但无法访问外网（谨慎处理，避免频繁重连）
        
        :param wifi_up: WiFi接口是否UP且有IP
        :param wifi_internet: WiFi是否能访问外网
        :return: True 需要重新扫描，False 不需要
        """
        should_rescan = False
        rescan_reason = ""
        
        if not wifi_up:
            # 情况1：接口状态异常（DOWN或无IP）
            # 进一步检查接口的真实状态
            interface_really_up = self._check_interface_up_status(self.wifi_interface)
            has_valid_ip = self._check_interface_has_valid_ip(self.wifi_interface)
            
            if not interface_really_up:
                should_rescan = True
                rescan_reason = "接口DOWN"
            elif not has_valid_ip:
                should_rescan = True
                rescan_reason = "接口UP但无有效IP地址"
                
        elif wifi_up and not wifi_internet:
            # 情况2：接口UP且有IP但无法访问外网
            # 这种情况需要谨慎处理，可能是DNS问题、路由问题或真正的网络问题
            # 先检查是否是DNS问题（DNS健康检查已经在check_external_connectivity中处理）
            # 这里只处理可能的WiFi连接质量问题
            
            # 检查WiFi信号强度和连接质量
            wifi_quality = self._check_wifi_connection_quality()
            if wifi_quality == "poor":
                should_rescan = True
                rescan_reason = "WiFi连接质量差，信号弱"
            else:
                # 有IP且信号正常但无法上网，可能是DNS或路由问题
                # 这种情况下不执行重新扫描，让DNS健康检查处理
                should_rescan = False
                rescan_reason = "有IP且信号正常但无法上网，可能是DNS或路由问题，不执行重新扫描"
        
        if should_rescan:
            self.logger.info(f"WiFi需要重新扫描连接，原因: {rescan_reason}")
        else:
            if rescan_reason:
                self.logger.debug(f"WiFi不需要重新扫描，原因: {rescan_reason}")
            else:
                self.logger.debug("WiFi状态正常，不需要重新扫描")
        
        return should_rescan

    def _check_interface_up_status(self, interface: str) -> bool:
        """
        检查接口是否真正处于UP状态
        :param interface: 网卡接口名称
        :return: True 接口UP，False 接口DOWN
        """
        try:
            status = subprocess.run(
                ["ip", "link", "show", interface],
                capture_output=True,
                text=True,
                check=True
            )
            output = status.stdout
            
            # 检查接口状态
            is_up = "state UP" in output
            self.logger.debug(f"接口 {interface} UP状态: {is_up}")
            return is_up
            
        except Exception as e:
            self.logger.error(f"检查接口 {interface} UP状态时发生错误: {e}")
            return False

    def _check_wifi_connection_quality(self) -> str:
        """
        检查WiFi连接质量
        :return: "good", "fair", "poor", "unknown"
        """
        try:
            # 使用iwconfig检查WiFi信号强度
            result = subprocess.run(
                ["iwconfig", self.wifi_interface],
                capture_output=True,
                text=True,
                check=True
            )
            output = result.stdout
            
            # 提取信号强度信息
            signal_match = re.search(r"Signal level=(-?\d+)", output)
            if signal_match:
                signal_level = int(signal_match.group(1))
                
                # 根据信号强度判断连接质量
                if signal_level >= -50:
                    return "good"
                elif signal_level >= -70:
                    return "fair"
                else:
                    return "poor"
            
            # 如果无法获取信号强度，尝试检查链路质量
            quality_match = re.search(r"Link Quality=(\d+)/(\d+)", output)
            if quality_match:
                current_quality = int(quality_match.group(1))
                max_quality = int(quality_match.group(2))
                quality_ratio = current_quality / max_quality
                
                if quality_ratio >= 0.7:
                    return "good"
                elif quality_ratio >= 0.4:
                    return "fair"
                else:
                    return "poor"
            
            return "unknown"
            
        except Exception as e:
            self.logger.debug(f"检查WiFi连接质量时发生错误: {e}")
            return "unknown"

    def check_dns_server_status(self, dns_server: str, timeout: float = 2.0) -> dict:
        """
        检查DNS服务器状态
        :param dns_server: DNS服务器地址
        :param timeout: 超时时间（秒）
        :return: 检测结果字典
        """
        result = {
            "dns_server": dns_server,
            "ping_success": False,
            "ping_time": None,
            "dns_resolve_success": False,
            "dns_resolve_time": None,
            "overall_status": "failed"
        }
        
        try:
            # 1. Ping测试
            ping_cmd = f"ping -c 1 -W {int(timeout)} {dns_server}"
            ping_result = subprocess.run(
                ping_cmd,
                shell=True,
                capture_output=True,
                text=True,
                timeout=timeout + 1
            )
            
            if ping_result.returncode == 0:
                result["ping_success"] = True
                # 提取ping时间
                import re
                time_match = re.search(r'time=(\d+\.?\d*)', ping_result.stdout)
                if time_match:
                    result["ping_time"] = float(time_match.group(1))
            
            # 2. DNS解析测试
            dns_cmd = f"nslookup www.baidu.com {dns_server}"
            dns_result = subprocess.run(
                dns_cmd,
                shell=True,
                capture_output=True,
                text=True,
                timeout=timeout + 1
            )
            
            if dns_result.returncode == 0 and "NXDOMAIN" not in dns_result.stdout:
                result["dns_resolve_success"] = True
                # 简单的DNS解析时间估算
                result["dns_resolve_time"] = timeout if result["ping_time"] is None else result["ping_time"] * 1.5
            
            # 综合评估
            if result["ping_success"] and result["dns_resolve_success"]:
                result["overall_status"] = "good"
            elif result["ping_success"]:
                result["overall_status"] = "ping_only"
            else:
                result["overall_status"] = "failed"
                
        except subprocess.TimeoutExpired:
            self.logger.warning(f"DNS服务器 {dns_server} 检测超时")
        except Exception as e:
            self.logger.error(f"检测DNS服务器 {dns_server} 时发生错误: {e}")
        
        return result

    def find_best_dns_server(self) -> str:
        """
        找到最佳的DNS服务器
        :return: 最佳DNS服务器地址
        """
        self.logger.info("开始检测最佳DNS服务器...")
        best_dns = None
        best_score = float('inf')
        
        # 检测主要DNS服务器
        for dns_server in self.dns_servers["primary"]:
            result = self.check_dns_server_status(dns_server)
            
            if result["overall_status"] == "good":
                # 计算得分（ping时间越低越好）
                score = result["ping_time"] if result["ping_time"] else 999
                self.logger.info(f"DNS服务器 {dns_server}: 状态={result['overall_status']}, ping={result['ping_time']}ms")
                
                if score < best_score:
                    best_score = score
                    best_dns = dns_server
            else:
                self.logger.warning(f"DNS服务器 {dns_server} 状态异常: {result['overall_status']}")
        
        # 如果主要DNS都不可用，尝试备用DNS
        if not best_dns:
            self.logger.warning("主要DNS服务器都不可用，尝试备用DNS服务器...")
            for dns_server in self.dns_servers["backup"]:
                result = self.check_dns_server_status(dns_server)
                
                if result["overall_status"] in ["good", "ping_only"]:
                    best_dns = dns_server
                    self.logger.info(f"使用备用DNS服务器: {dns_server}")
                    break
        
        # 更新DNS状态
        if best_dns:
            self.dns_status["current_dns"] = best_dns
            self.dns_status["failure_count"] = 0
            self.logger.info(f"选择最佳DNS服务器: {best_dns} (得分: {best_score:.2f}ms)")
        else:
            self.dns_status["failure_count"] += 1
            self.logger.error("未找到可用的DNS服务器!")
            # 使用默认DNS作为最后的备选
            best_dns = "*********"
        
        self.dns_status["last_check_time"] = time.time()
        return best_dns

    def get_system_dns_servers(self) -> list:
        """
        获取系统当前使用的DNS服务器列表，如果文件不存在则创建默认配置
        :return: DNS服务器列表
        """
        dns_servers = []
        resolv_conf_path = "/etc/resolv.conf"
        
        try:
            # 检查文件是否存在
            if not os.path.exists(resolv_conf_path):
                self.logger.warning(f"{resolv_conf_path} 文件不存在，创建默认DNS配置")
                self._create_default_resolv_conf()
            
            # 读取 /etc/resolv.conf 文件
            with open(resolv_conf_path, "r") as f:
                for line in f:
                    line = line.strip()
                    if line.startswith("nameserver"):
                        dns_ip = line.split()[1]
                        dns_servers.append(dns_ip)
            
            if not dns_servers:
                self.logger.warning("未在 /etc/resolv.conf 中找到DNS服务器，创建默认DNS配置")
                self._create_default_resolv_conf()
                dns_servers = ["*********"]  # 默认使用阿里云DNS
                
        except Exception as e:
            self.logger.error(f"读取系统DNS配置时发生错误: {e}")
            self.logger.info("尝试创建默认DNS配置")
            self._create_default_resolv_conf()
            dns_servers = ["*********"]  # 默认使用阿里云DNS
            
        return dns_servers

    def _create_default_resolv_conf(self):
        """
        创建默认的 /etc/resolv.conf 文件
        """
        try:
            # 使用国内优化的DNS服务器作为默认配置
            default_dns_servers = [
                "*********",      # 阿里云DNS（主）
                "************",   # 腾讯DNS（备）
            ]
            
            resolv_conf_content = "# Generated by NetworkManager\n"
            resolv_conf_content += "# Default DNS configuration created by xiaoli network manager\n"
            
            for dns_server in default_dns_servers:
                resolv_conf_content += f"nameserver {dns_server}\n"
            
            # 添加一些常用的配置选项
            resolv_conf_content += "\n# DNS resolution options\n"
            resolv_conf_content += "options timeout:2\n"
            resolv_conf_content += "options attempts:3\n"
            resolv_conf_content += "options rotate\n"
            
            # 写入临时文件然后移动（原子操作）
            temp_file = "/tmp/resolv.conf.default"
            with open(temp_file, "w") as f:
                f.write(resolv_conf_content)
            
            # 移动到目标位置
            result = self.run_command(f"sudo mv {temp_file} /etc/resolv.conf")
            
            if result is not None:
                self.logger.info("成功创建默认 /etc/resolv.conf 文件")
                self.logger.info(f"默认DNS服务器: {default_dns_servers}")
                
                # 重启DNS解析服务以使配置生效
                self.run_command("sudo systemctl restart systemd-resolved")
                self.logger.info("已重启 systemd-resolved 服务")
            else:
                self.logger.error("创建默认 /etc/resolv.conf 文件失败")
                
        except Exception as e:
            self.logger.error(f"创建默认 /etc/resolv.conf 文件时发生错误: {e}")

    def check_external_connectivity(self, interface: str = "eth1", timeout: float = 2.0) -> bool:
        """
        检查指定网卡是否能访问外部网络，使用系统当前DNS测试真实网络状态
        异常时自动进行DNS健康监测和修复
        :param interface: 网卡名称
        :param timeout: 超时时间（秒）
        :return: True 有网络，False 无网络
        """
        # 获取系统当前使用的DNS服务器
        system_dns_servers = self.get_system_dns_servers()
        self.logger.debug(f"系统当前DNS服务器: {system_dns_servers}")
        
        # 使用系统DNS测试连接（这反映真实的网络状态）
        connectivity_result = False
        tested_dns = None
        
        # 尝试使用系统中的每个DNS服务器进行测试
        for dns_server in system_dns_servers:
            if self._test_connectivity(interface, dns_server, timeout):
                connectivity_result = True
                tested_dns = dns_server
                self.logger.debug(f"使用系统DNS {dns_server} 连接测试成功")
                break
            else:
                self.logger.debug(f"使用系统DNS {dns_server} 连接测试失败")
        
        if connectivity_result:
            # 连接成功，重置DNS失败计数
            self.dns_status["failure_count"] = 0
            return True
        else:
            # 系统DNS连接失败，说明当前DNS配置有问题，需要进行DNS健康检查和修复
            self.logger.warning(f"接口 {interface} 使用系统DNS {system_dns_servers} 外网连接异常，开始DNS健康监测和修复...")

            # 只对默认路由网卡执行DNS健康检查和修复
            default_interface = self.get_default_route_interface()
            if default_interface and interface == default_interface:
                self.logger.info(f"接口 {interface} 是默认路由网卡，执行DNS健康检查和修复")
                dns_fixed = self._perform_dns_health_check_and_fix(interface, timeout)
            else:
                self.logger.debug(f"接口 {interface} 不是默认路由网卡（默认: {default_interface}），跳过DNS健康检查")
                self.dns_status["failure_count"] += 1
                return False
            
            if dns_fixed:
                # DNS修复后重新获取系统DNS并测试连接
                self.logger.info("DNS修复完成，重新测试网络连接...")
                updated_system_dns = self.get_system_dns_servers()
                
                # 使用更新后的系统DNS重新测试
                for dns_server in updated_system_dns:
                    if self._test_connectivity(interface, dns_server, timeout):
                        self.logger.info(f"DNS修复成功，接口 {interface} 使用DNS {dns_server} 网络连接已恢复")
                        self.dns_status["failure_count"] = 0
                        return True
                
                # 如果更新后的DNS仍然无法连接
                self.logger.error(f"DNS修复后接口 {interface} 网络连接仍然异常")
                self.dns_status["failure_count"] += 1
                return False
            else:
                self.logger.error(f"DNS健康检查未能修复网络连接问题")
                self.dns_status["failure_count"] += 1
                return False

    def _test_connectivity(self, interface: str, dns_server: str, timeout: float) -> bool:
        """
        测试网络连通性的内部方法
        :param interface: 网卡名称
        :param dns_server: DNS服务器地址
        :param timeout: 超时时间
        :return: True 连接正常，False 连接异常
        """
        try:
            # 使用ping命令检测网络连通性
            cmd = f"ping -I {interface} -c 1 -W {int(timeout)} {dns_server}"
            result = subprocess.run(
                cmd, 
                shell=True, 
                capture_output=True, 
                text=True, 
                timeout=timeout + 1
            )
            
            if result.returncode == 0:
                self.logger.debug(f"接口 {interface} 连接 {dns_server} 成功")
                return True
            else:
                self.logger.debug(f"接口 {interface} 连接 {dns_server} 失败: {result.stderr.strip()}")
                return False
                
        except subprocess.TimeoutExpired:
            self.logger.debug(f"接口 {interface} 连接 {dns_server} 超时")
            return False
        except Exception as e:
            self.logger.debug(f"测试接口 {interface} 连接时发生错误: {e}")
            return False

    def _perform_dns_health_check_and_fix(self, interface: str, timeout: float) -> bool:
        """
        执行DNS健康检查和修复
        :param interface: 网卡名称
        :param timeout: 超时时间
        :return: True 修复成功，False 修复失败
        """
        self.logger.info("开始DNS健康检查和修复流程...")
        
        try:
            # 1. 检查当前DNS服务器状态
            current_dns = self.dns_status.get("current_dns")
            if current_dns:
                self.logger.info(f"检查当前DNS服务器 {current_dns} 的健康状态...")
                current_dns_status = self.check_dns_server_status(current_dns, timeout)
                
                if current_dns_status["overall_status"] == "good":
                    self.logger.info(f"当前DNS服务器 {current_dns} 状态正常，确保其已正确配置到系统中")
                    # 确保当前正常的DNS服务器被正确写入到系统配置中
                    self._ensure_dns_in_resolved([current_dns])
                    # 刷新网络配置
                    self._refresh_network_config()
                    return True
                else:
                    self.logger.warning(f"当前DNS服务器 {current_dns} 状态异常: {current_dns_status['overall_status']}")
            
            # 2. 检查所有主要DNS服务器状态
            self.logger.info("检查所有主要DNS服务器状态...")
            available_dns_servers = []
            
            for dns_server in self.dns_servers["primary"]:
                status = self.check_dns_server_status(dns_server, timeout)
                self.logger.info(f"DNS服务器 {dns_server}: {status['overall_status']}")
                
                if status["overall_status"] in ["good", "ping_only"]:
                    available_dns_servers.append({
                        "dns": dns_server,
                        "score": status.get("ping_time", 999),
                        "status": status
                    })
            
            # 3. 如果主要DNS都不可用，检查备用DNS
            if not available_dns_servers:
                self.logger.warning("主要DNS服务器都不可用，检查备用DNS服务器...")
                for dns_server in self.dns_servers["backup"]:
                    status = self.check_dns_server_status(dns_server, timeout)
                    self.logger.info(f"备用DNS服务器 {dns_server}: {status['overall_status']}")
                    
                    if status["overall_status"] in ["good", "ping_only"]:
                        available_dns_servers.append({
                            "dns": dns_server,
                            "score": status.get("ping_time", 999),
                            "status": status
                        })
            
            # 4. 选择最佳可用DNS并更新系统配置
            if available_dns_servers:
                # 按响应时间排序
                available_dns_servers.sort(key=lambda x: x["score"])
                best_dns = available_dns_servers[0]["dns"]
                
                self.logger.info(f"选择最佳可用DNS服务器: {best_dns}")
                
                # 更新DNS状态
                self.dns_status["current_dns"] = best_dns
                self.dns_status["failure_count"] = 0
                self.dns_status["last_check_time"] = time.time()
                
                # 更新系统DNS配置（包括resolved配置）
                self._ensure_dns_in_resolved([best_dns])
                self.update_system_dns([best_dns])
                
                # 等待DNS配置生效
                time.sleep(2)
                
                return True
            else:
                self.logger.error("所有DNS服务器都不可用，无法修复DNS问题")
                return False
                
        except Exception as e:
            self.logger.error(f"DNS健康检查和修复过程中发生错误: {e}")
            return False

    def _ensure_dns_in_resolved(self, dns_servers: list):
        """
        确保DNS服务器配置被正确写入到/etc/resolv.conf文件中
        :param dns_servers: DNS服务器列表
        """
        self.logger.info(f"确保DNS服务器 {dns_servers} 被正确配置到/etc/resolv.conf中...")
        
        try:
            # 更新 /etc/resolv.conf
            resolv_conf_path = "/etc/resolv.conf"
            resolv_conf_backup = "/etc/resolv.conf.backup"
            
            # 备份原始配置文件
            self.run_command(f"sudo cp {resolv_conf_path} {resolv_conf_backup}")
            
            # 构建新的resolv.conf内容
            resolv_conf_content = "# Generated by NetworkManager\n"
            resolv_conf_content += "# DNS servers configured by xiaoli network manager\n"
            
            # 添加DNS服务器
            for dns in dns_servers:
                resolv_conf_content += f"nameserver {dns}\n"
            
            # 添加一些优化选项
            resolv_conf_content += "\n# DNS resolution options\n"
            resolv_conf_content += "options timeout:2\n"
            resolv_conf_content += "options attempts:3\n"
            resolv_conf_content += "options rotate\n"
            
            # 写入临时文件然后移动（原子操作）
            temp_file = "/tmp/resolv.conf.tmp"
            with open(temp_file, "w") as f:
                f.write(resolv_conf_content)
            
            # 移动到目标位置
            result = self.run_command(f"sudo mv {temp_file} {resolv_conf_path}")
            if result is not None:
                self.logger.info(f"/etc/resolv.conf 已更新为: {dns_servers}")
            else:
                self.logger.error("更新/etc/resolv.conf失败")
                return False
            
            # 通过resolvectl设置DNS（针对特定接口）
            for interface in [self.wifi_interface, self.mobile_interface]:
                if self.check_interface_status(interface):
                    dns_cmd = f"sudo resolvectl dns {interface} {' '.join(dns_servers)}"
                    result = self.run_command(dns_cmd)
                    if result is not None:
                        self.logger.info(f"已通过resolvectl为接口 {interface} 设置DNS: {dns_servers}")
                    else:
                        self.logger.warning(f"通过resolvectl为接口 {interface} 设置DNS失败")
            
            # 设置全局DNS
            global_dns_cmd = f"sudo resolvectl dns {' '.join(dns_servers)}"
            result = self.run_command(global_dns_cmd)
            if result is not None:
                self.logger.info(f"已设置全局DNS: {dns_servers}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"确保DNS配置到/etc/resolv.conf时发生错误: {e}")
            return False

    def _refresh_network_config(self):
        """
        刷新网络配置
        """
        self.logger.info("刷新网络配置...")
        try:
            # 刷新DNS缓存
            self.run_command("sudo resolvectl flush-caches", timeout=10)
            
            # 重启网络解析服务
            self.run_command("sudo systemctl restart systemd-resolved", timeout=15)
            
            # 刷新路由表
            self.run_command("sudo ip route flush cache", timeout=10)
            
            # 验证DNS配置是否生效
            self._verify_dns_config()
            
            self.logger.info("网络配置刷新完成")
            
        except Exception as e:
            self.logger.error(f"刷新网络配置时发生错误: {e}")

    def _verify_dns_config(self):
        """
        验证DNS配置是否正确生效
        """
        try:
            # 检查systemd-resolved状态
            result = self.run_command("sudo resolvectl status")
            if result:
                self.logger.debug(f"systemd-resolved状态:\n{result}")
            
            # 检查当前DNS配置
            current_dns = self.get_system_dns_servers()
            self.logger.info(f"当前系统DNS配置: {current_dns}")
            
            # 测试DNS解析
            test_result = self.run_command("nslookup www.baidu.com")
            if test_result and "NXDOMAIN" not in test_result:
                self.logger.info("DNS解析测试成功")
            else:
                self.logger.warning("DNS解析测试失败")
                
        except Exception as e:
            self.logger.error(f"验证DNS配置时发生错误: {e}")

    def get_network_diagnostic_info(self, interface: str) -> dict:
        """
        获取网络诊断信息
        :param interface: 网卡名称
        :return: 诊断信息字典
        """
        diagnostic_info = {
            "interface": interface,
            "interface_status": "unknown",
            "current_dns": self.dns_status.get("current_dns"),
            "dns_failure_count": self.dns_status.get("failure_count", 0),
            "connectivity_tests": {},
            "dns_health": {},
            "system_dns": []
        }
        
        try:
            # 检查接口状态
            diagnostic_info["interface_status"] = "up" if self.check_interface_status(interface) else "down"
            
            # 测试多个DNS服务器的连通性
            test_dns_servers = ["*********", "************", "*******"]
            for dns in test_dns_servers:
                diagnostic_info["connectivity_tests"][dns] = self._test_connectivity(interface, dns, 3.0)
            
            # 获取DNS健康状态
            for category, servers in self.dns_servers.items():
                diagnostic_info["dns_health"][category] = {}
                for dns_server in servers[:2]:  # 只检查前两个以节省时间
                    status = self.check_dns_server_status(dns_server, timeout=2.0)
                    diagnostic_info["dns_health"][category][dns_server] = status["overall_status"]
            
            # 获取系统当前DNS配置
            diagnostic_info["system_dns"] = self.get_system_dns_servers()
                
        except Exception as e:
            self.logger.error(f"获取网络诊断信息时发生错误: {e}")
        
        return diagnostic_info

    def get_dns_status_report(self) -> dict:
        """
        获取DNS状态报告
        :return: DNS状态报告字典
        """
        report = {
            "current_dns": self.dns_status["current_dns"],
            "last_check_time": self.dns_status["last_check_time"],
            "failure_count": self.dns_status["failure_count"],
            "dns_servers_status": {}
        }
        
        # 检测所有DNS服务器状态
        for category, servers in self.dns_servers.items():
            report["dns_servers_status"][category] = {}
            for dns_server in servers:
                status = self.check_dns_server_status(dns_server, timeout=1.0)
                report["dns_servers_status"][category][dns_server] = status
        
        return report

    def _update_systemd_resolved_conf(self, dns_servers: list):
        """
        更新 /etc/systemd/resolved.conf 配置文件
        :param dns_servers: DNS服务器列表
        """
        self.logger.info(f"更新 /etc/systemd/resolved.conf 配置: {dns_servers}")
        
        try:
            resolved_conf_path = "/etc/systemd/resolved.conf"
            resolved_conf_backup = "/etc/systemd/resolved.conf.backup"
            
            # 备份原始配置文件
            self.run_command(f"sudo cp {resolved_conf_path} {resolved_conf_backup}")
            
            # 读取现有配置
            try:
                with open(resolved_conf_path, 'r') as f:
                    lines = f.readlines()
            except FileNotFoundError:
                lines = []
            
            # 构建新的配置内容
            new_lines = []
            dns_line_added = False
            
            for line in lines:
                # 跳过现有的DNS配置行
                if line.strip().startswith('DNS=') or line.strip().startswith('#DNS='):
                    continue
                new_lines.append(line)
            
            # 确保有[Resolve]段
            has_resolve_section = any('[Resolve]' in line for line in new_lines)
            if not has_resolve_section:
                new_lines.append('[Resolve]\n')
            
            # 添加DNS配置
            dns_config_line = f"DNS={' '.join(dns_servers)}\n"
            
            # 在[Resolve]段后添加DNS配置
            resolve_section_found = False
            for i, line in enumerate(new_lines):
                if '[Resolve]' in line:
                    resolve_section_found = True
                    # 在[Resolve]段后插入DNS配置
                    new_lines.insert(i + 1, dns_config_line)
                    dns_line_added = True
                    break
            
            if not dns_line_added:
                # 如果没有找到合适的位置，添加到文件末尾
                new_lines.append('[Resolve]\n')
                new_lines.append(dns_config_line)
            
            # 写入新配置
            temp_file = "/tmp/systemd_resolved.conf.tmp"
            with open(temp_file, 'w') as f:
                f.writelines(new_lines)
            
            # 移动到目标位置
            result = self.run_command(f"sudo mv {temp_file} {resolved_conf_path}")
            if result is not None:
                self.logger.info(f"/etc/systemd/resolved.conf 已更新为: DNS={' '.join(dns_servers)}")
                return True
            else:
                self.logger.error("更新 /etc/systemd/resolved.conf 失败")
                return False
                
        except Exception as e:
            self.logger.error(f"更新 /etc/systemd/resolved.conf 时发生错误: {e}")
            return False

    def update_system_dns(self, dns_servers: list = None):
        """
        更新系统DNS配置（包括resolv.conf和systemd-resolved）
        :param dns_servers: DNS服务器列表，如果为None则使用当前最佳DNS
        """
        if not dns_servers:
            if self.dns_status["current_dns"]:
                dns_servers = [self.dns_status["current_dns"]]
            else:
                dns_servers = self.dns_servers["primary"][:2]  # 使用前两个主要DNS
        
        try:
            # 1. 更新 /etc/resolv.conf
            self._ensure_dns_in_resolved(dns_servers)
            
            # 2. 更新 /etc/systemd/resolved.conf
            self._update_systemd_resolved_conf(dns_servers)
            
            # 3. 重启DNS解析服务
            self.run_command("sudo systemctl restart systemd-resolved")
            
            # 4. 验证配置是否生效
            self._verify_dns_config()
            
            self.logger.info(f"系统DNS配置完成: {dns_servers}")
            
        except Exception as e:
            self.logger.error(f"更新系统DNS配置时发生错误: {e}")

    def get_wifi_name(self):
        """获取当前连接的 WiFi 名称"""
        try:
            result = subprocess.run(
                ["iwgetid", self.wifi_interface, "-r"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                check=True
            )
            return result.stdout.strip() or "Unknown"
        except subprocess.CalledProcessError:
            return "Error"

    def scan_wifi_networks(self):
        """
        扫描可用的WiFi网络（多方法智能扫描）

        Returns:
            list: WiFi网络列表，每个元素包含SSID、信号强度、安全性等信息
        """
        self.logger.info("开始扫描WiFi网络...")
        
        # 扫描方法列表，按优先级排序
        scan_methods = [
            ("iwlist", self._scan_wifi_with_iwlist),
            ("iw", self._scan_wifi_with_iw),
            ("nmcli", self._scan_wifi_with_nmcli),
            ("wpa_cli", self._scan_wifi_with_wpa_cli)
        ]
        
        all_networks = []
        successful_methods = []
        
        try:
            for method_name, method_func in scan_methods:
                try:
                    self.logger.info(f"尝试使用{method_name}命令扫描WiFi网络...")
                    networks = method_func(self.wifi_interface)
                    
                    if networks:
                        self.logger.info(f"{method_name}扫描成功，发现 {len(networks)} 个网络")
                        all_networks.extend(networks)
                        successful_methods.append(method_name)
                        
                        # 如果第一个方法就成功且网络数量足够，直接返回
                        if len(networks) >= 3 and method_name in ["iwlist", "iw"]:
                            return self._merge_and_deduplicate_networks(networks)
                    else:
                        self.logger.warning(f"{method_name}扫描无结果")
                        
                except Exception as e:
                    self.logger.warning(f"{method_name}扫描失败: {e}")
                    continue
            
            # 合并和去重所有扫描结果
            if all_networks:
                merged_networks = self._merge_and_deduplicate_networks(all_networks)
                self.logger.info(f"总共发现 {len(merged_networks)} 个去重后的网络，使用方法: {successful_methods}")
                return merged_networks
            
            # 如果所有方法都失败，进行重试
            self.logger.warning("所有WiFi扫描方法都失败，尝试重试...")
            return self._retry_wifi_scan()
            
        except Exception as e:
            self.logger.error(f"扫描WiFi网络时发生错误: {e}")
            return []

    def _merge_and_deduplicate_networks(self, all_networks):
        """
        合并和去重WiFi网络列表
        
        Args:
            all_networks (list): 包含重复网络的列表
            
        Returns:
            list: 去重后的网络列表
        """
        network_dict = {}
        
        for network in all_networks:
            ssid = network.get('ssid', '').strip()
            if not ssid:
                continue
            
            # 使用SSID作为键，保留信号最强的
            if ssid not in network_dict:
                network_dict[ssid] = network
            else:
                current_signal = int(network_dict[ssid].get('signal_level', '-100'))
                new_signal = int(network.get('signal_level', '-100'))
                if new_signal > current_signal:
                    network_dict[ssid] = network
        
        # 转换回列表并按信号强度排序
        merged_networks = list(network_dict.values())
        merged_networks.sort(key=lambda x: int(x.get('signal_level', '-100')), reverse=True)
        
        return merged_networks

    def _retry_wifi_scan(self):
        """
        重试WiFi扫描（使用最简单的方法）
        
        Returns:
            list: WiFi网络列表
        """
        self.logger.info("执行重试扫描...")
        
        retry_methods = [
            ("iwlist", lambda: subprocess.run(["sudo", "iwlist", self.wifi_interface, "scan"], 
                                            capture_output=True, text=True, timeout=15)),
            ("iw", lambda: subprocess.run(["sudo", "iw", "dev", self.wifi_interface, "scan"], 
                                        capture_output=True, text=True, timeout=15))
        ]
        
        for method_name, method_func in retry_methods:
            try:
                self.logger.info(f"重试使用{method_name}...")
                result = method_func()
                
                if result.returncode == 0 and result.stdout.strip():
                    # 简单解析，只获取SSID
                    networks = []
                    if method_name == "iwlist":
                        for line in result.stdout.split('\n'):
                            if 'ESSID:' in line:
                                ssid = line.split('ESSID:')[-1].strip('"')
                                if ssid and ssid != '':
                                    networks.append({
                                        'ssid': ssid,
                                        'signal_level': '-50',
                                        'security': 'Unknown',
                                        'frequency': '',
                                        'channel': '',
                                        'bssid': ''
                                    })
                    
                    if networks:
                        # 标准化网络格式
                        standardized_networks = []
                        for network in networks:
                            if isinstance(network, dict) and 'ssid' in network:
                                # 确保所有字段都存在
                                std_network = {
                                    'ssid': network.get('ssid', ''),
                                    'bssid': network.get('bssid', ''),
                                    'channel': network.get('channel', ''),
                                    'frequency': network.get('frequency', ''),
                                    'band': network.get('band', '2.4GHz'),
                                    'signal': network.get('signal', 50),
                                    'signal_level': network.get('signal_level', -50),
                                    'quality': network.get('quality', 'Fair'),
                                    'security': network.get('security', 'Unknown'),
                                    'rate': network.get('rate', '')
                                }
                                standardized_networks.append(std_network)
                        
                        self.logger.info(f"重试{method_name}成功，发现 {len(standardized_networks)} 个网络")
                        return standardized_networks
                        
            except Exception as e:
                self.logger.warning(f"重试{method_name}失败: {e}")
                continue
        
        self.logger.error("所有重试方法都失败")
        return []

    def _frequency_to_channel(self, frequency):
        """
        频率转换为信道
        
        Args:
            frequency (str): 频率字符串
            
        Returns:
            str: 信道号
        """
        try:
            freq = float(frequency)
            if freq > 5000:
                # 5GHz频段
                return str(int((freq - 5000) / 5))
            else:
                # 2.4GHz频段
                return str(int((freq - 2407) / 5))
        except:
            return "1"

    def scan_wifi_networks_async(self):
        """
        实时响应的异步WiFi扫描接口
        - 有缓存且有效：立即返回缓存结果
        - 正在扫描：立即返回扫描中状态
        - 无缓存且未扫描：启动扫描并立即返回扫描中状态

        Returns:
            dict: 包含扫描结果和状态信息的字典
        """
        with self.wifi_scan_lock:
            current_time = time.time()

            # 检查缓存是否有效
            if (self.wifi_scan_cache["networks"] and
                current_time - self.wifi_scan_cache["last_scan_time"] < self.wifi_scan_cache["cache_duration"]):
                self.logger.info("使用缓存的WiFi扫描结果")
                return {
                    "success": True,
                    "status": "completed",
                    "code": 200,  # 成功状态码
                    "networks": self.wifi_scan_cache["networks"],
                    "count": len(self.wifi_scan_cache["networks"]),
                    "cached": True,
                    "scan_time": self.wifi_scan_cache["last_scan_time"],
                    "message": "返回缓存的扫描结果"
                }

            # 检查是否有扫描正在进行
            if self.wifi_scan_cache["scan_in_progress"]:
                self.logger.info("WiFi扫描正在进行中，返回扫描中状态")
                return {
                    "success": True,
                    "status": "scanning",
                    "code": 202,  # 扫描进行中状态码（区别于首次启动）
                    "networks": [],
                    "count": 0,
                    "cached": False,
                    "scan_time": 0,
                    "message": "WiFi扫描正在进行中，请稍后再次调用获取结果"
                }

            # 启动新的异步扫描
            self.logger.info("启动新的异步WiFi扫描")
            self.wifi_scan_cache["scan_in_progress"] = True
            self.wifi_scan_cache["scan_start_time"] = current_time
            self.wifi_scan_cache["scan_future"] = self.wifi_scan_executor.submit(self._perform_wifi_scan_with_callback)

            return {
                "success": True,
                "status": "scanning",
                "code": 102,  # 首次启动扫描状态码
                "networks": [],
                "count": 0,
                "cached": False,
                "scan_time": 0,
                "message": "已启动WiFi扫描，请稍后再次调用获取结果"
            }

    def _perform_wifi_scan(self):
        """
        执行实际的WiFi扫描操作（在线程池中运行）

        Returns:
            list: WiFi网络列表
        """
        self.logger.info("开始执行异步WiFi网络扫描...")
        return self.scan_wifi_networks()

    def _perform_wifi_scan_with_callback(self):
        """
        执行WiFi扫描并自动更新缓存（在线程池中运行）

        Returns:
            list: WiFi网络列表
        """
        try:
            self.logger.info("开始执行异步WiFi网络扫描（带回调）...")
            networks = self.scan_wifi_networks()

            # 更新缓存前处理网络数据中的特殊字符
            for network in networks:
                # 处理所有可能包含转义字符的字段
                for field in ["bssid", "channel", "security", "rate"]:
                    if field in network and isinstance(network[field], str):
                        network[field] = network[field].replace("\\", "")
            
            # 更新缓存
            with self.wifi_scan_lock:
                self.wifi_scan_cache["networks"] = networks
                self.wifi_scan_cache["last_scan_time"] = time.time()
                self.wifi_scan_cache["scan_in_progress"] = False
                self.wifi_scan_cache["scan_future"] = None

            self.logger.info(f"异步WiFi扫描完成，发现 {len(networks)} 个网络")
            return networks

        except Exception as e:
            self.logger.error(f"异步WiFi扫描失败: {e}")
            # 清理扫描状态
            with self.wifi_scan_lock:
                self.wifi_scan_cache["scan_in_progress"] = False
                self.wifi_scan_cache["scan_future"] = None
            raise e

    def get_wifi_scan_status(self):
        """
        获取WiFi扫描状态

        Returns:
            dict: 扫描状态信息
        """
        with self.wifi_scan_lock:
            current_time = time.time()
            cache_valid = (
                bool(self.wifi_scan_cache["networks"]) and
                current_time - self.wifi_scan_cache["last_scan_time"] < self.wifi_scan_cache["cache_duration"]
            )

            # 计算扫描进度
            scan_duration = 0
            if self.wifi_scan_cache["scan_in_progress"] and self.wifi_scan_cache["scan_start_time"] > 0:
                scan_duration = current_time - self.wifi_scan_cache["scan_start_time"]

            return {
                "scan_in_progress": self.wifi_scan_cache["scan_in_progress"],
                "cache_valid": cache_valid,
                "last_scan_time": self.wifi_scan_cache["last_scan_time"],
                "cached_networks_count": len(self.wifi_scan_cache["networks"]),
                "scan_duration": round(scan_duration, 2),
                "scan_start_time": self.wifi_scan_cache["scan_start_time"]
            }

    def clear_wifi_scan_cache(self):
        """
        清除WiFi扫描缓存（不影响正在进行的扫描）
        """
        with self.wifi_scan_lock:
            self.wifi_scan_cache["networks"] = []
            self.wifi_scan_cache["last_scan_time"] = 0
            # 注意：不清除scan_start_time，因为可能有扫描正在进行
            self.logger.info("WiFi扫描缓存已清除")

    def _calculate_signal_quality(self, signal_level_dbm):
        """
        根据信号强度(dBm)计算信号质量百分比

        Args:
            signal_level_dbm (int): 信号强度(dBm)

        Returns:
            str: 信号质量描述
        """
        if signal_level_dbm >= -50:
            return "excellent"
        elif signal_level_dbm >= -60:
            return "good"
        elif signal_level_dbm >= -70:
            return "fair"
        else:
            return "poor"

    def _calculate_signal_quality_from_percentage(self, signal_percentage):
        """
        根据信号强度百分比计算信号质量

        Args:
            signal_percentage (int): 信号强度百分比

        Returns:
            str: 信号质量描述
        """
        if signal_percentage >= 80:
            return "excellent"
        elif signal_percentage >= 60:
            return "good"
        elif signal_percentage >= 40:
            return "fair"
        else:
            return "poor"

    def _convert_to_rssi(self, signal_level_dbm):
        """
        将dBm信号强度转换为RSSI值
        
        Args:
            signal_level_dbm (int): 信号强度(dBm)
            
        Returns:
            int: RSSI值 (0-100)
        """
        # RSSI通常是一个0-100的数值，表示信号强度
        # 将dBm转换为RSSI：-100dBm = 0, -50dBm = 100
        if signal_level_dbm >= -50:
            return 100
        elif signal_level_dbm <= -100:
            return 0
        else:
            # 线性映射：-100dBm到-50dBm映射到0-100
            return int(((signal_level_dbm + 100) / 50) * 100)

    def _convert_percentage_to_rssi(self, signal_percentage):
        """
        将百分比信号强度转换为RSSI值
        
        Args:
            signal_percentage (int): 信号强度百分比(0-100)
            
        Returns:
            int: RSSI值 (0-100)
        """
        # 百分比信号强度直接对应RSSI值
        return max(0, min(100, signal_percentage))
        
    def _scan_wifi_with_iw(self, interface):
        """
        使用iw命令扫描WiFi网络（优化版本，解决超时问题）
        
        Args:
            interface (str): 要使用的WiFi接口名称
            
        Returns:
            list: WiFi网络列表
        """
        self.logger.info(f"使用iw命令扫描WiFi网络，接口: {interface}")
        wifi_networks = []
        
        try:
            # 1. 首先检查接口是否可用
            if not self._check_interface_up_status(interface):
                self.logger.error(f"WiFi接口 {interface} 不可用")
                return []
            
            # 2. 执行扫描命令（带超时控制）
            scan_cmd = f"sudo iw dev {interface} scan"
            scan_result = self.run_command(scan_cmd, timeout=15)  # 15秒超时
            
            if scan_result is None:
                self.logger.error(f"WiFi扫描命令执行失败或超时")
                return []
            
            # 3. 等待扫描完成（动态等待）
            max_wait_time = 10  # 最大等待10秒
            wait_interval = 0.5  # 每0.5秒检查一次
            total_wait_time = 0
            
            while total_wait_time < max_wait_time:
                # 检查扫描是否完成
                check_cmd = f"sudo iw dev {interface} scan dump"
                try:
                    result = subprocess.run(
                        ["sudo", "iw", "dev", interface, "scan", "dump"],
                        capture_output=True,
                        text=True,
                        timeout=5  # 5秒超时
                    )
                    
                    if result.returncode == 0 and result.stdout.strip():
                        # 扫描结果可用，跳出等待循环
                        break
                    else:
                        # 扫描可能还在进行中，继续等待
                        time.sleep(wait_interval)
                        total_wait_time += wait_interval
                        
                except subprocess.TimeoutExpired:
                    self.logger.warning(f"检查扫描状态超时，继续等待...")
                    time.sleep(wait_interval)
                    total_wait_time += wait_interval
                except Exception as e:
                    self.logger.warning(f"检查扫描状态时出错: {e}")
                    time.sleep(wait_interval)
                    total_wait_time += wait_interval
            
            # 4. 最终获取扫描结果
            try:
                result = subprocess.run(
                    ["sudo", "iw", "dev", interface, "scan", "dump"],
                    capture_output=True,
                    text=True,
                    timeout=10  # 10秒超时获取结果
                )
                
                if result.returncode != 0:
                    self.logger.error(f"获取扫描结果失败: {result.stderr}")
                    return []
                    
            except subprocess.TimeoutExpired:
                self.logger.error(f"获取扫描结果超时")
                return []
            
            # 记录原始输出以便调试
            self.logger.debug(f"iw扫描原始输出长度: {len(result.stdout)}")
            
            # 5. 解析扫描结果
            current_network = None
            current_ssid = None
            current_signal = None
            current_frequency = None
            current_security = []
            
            for line in result.stdout.split('\n'):
                line = line.strip()
                
                # 新的BSS (Basic Service Set) 表示新的AP
                if line.startswith('BSS '):
                    # 保存之前的网络信息
                    if current_network and current_ssid:
                        wifi_networks.append(current_network)
                    
                    # 提取BSSID (MAC地址)
                    bssid_match = re.search(r'BSS ([0-9a-f:]{17})', line)
                    if bssid_match:
                        bssid = bssid_match.group(1)
                        current_network = {
                            "bssid": bssid,
                            "security": "Open"  # 默认为开放网络
                        }
                        current_ssid = None
                        current_signal = None
                        current_frequency = None
                        current_security = []
                
                # 提取SSID
                elif line.startswith('SSID: '):
                    current_ssid = line[6:].strip()
                    if current_ssid and current_network:
                        current_network["ssid"] = current_ssid if current_ssid else "Hidden Network"
                
                # 提取信号强度
                elif line.startswith('signal: '):
                    signal_match = re.search(r'signal: ([-\d.]+) dBm', line)
                    if signal_match and current_network:
                        current_signal = float(signal_match.group(1))
                        current_network["signal_level"] = current_signal
                        # 直接设置signal字段为信号百分比值（0-100）
                        if current_signal >= -50:
                            signal_percentage = 100
                        elif current_signal <= -100:
                            signal_percentage = 0
                        else:
                            # 线性映射：-100dBm到-50dBm映射到0-100
                            signal_percentage = int(((current_signal + 100) / 50) * 100)
                        current_network["signal"] = signal_percentage
                        current_network["quality"] = self._calculate_signal_quality(current_signal)
                
                # 提取频率
                elif line.startswith('freq: '):
                    current_frequency = int(line[6:].strip())
                    if current_network:
                        current_network["frequency"] = current_frequency
                        # 确定WiFi频段 (2.4GHz 或 5GHz)
                        if current_frequency < 3000:
                            current_network["band"] = "2.4GHz"
                        else:
                            current_network["band"] = "5GHz"
                
                # 提取信道
                elif line.startswith('DS Parameter set: channel '):
                    channel = line.split('channel ')[1].strip()
                    if current_network:
                        current_network["channel"] = channel
                
                # 提取安全性信息
                elif any(security in line for security in ['WEP', 'WPA', 'RSN']):
                    if 'WEP' in line:
                        current_security.append('WEP')
                    if 'WPA' in line:
                        current_security.append('WPA')
                    if 'RSN' in line:
                        current_security.append('WPA2')
                    
                    if current_network and current_security:
                        current_network["security"] = '/'.join(current_security)
            
            # 添加最后一个网络
            if current_network and current_ssid:
                wifi_networks.append(current_network)
            
            # 过滤掉没有SSID的网络
            wifi_networks = [network for network in wifi_networks if "ssid" in network]
            
            # 按信号强度排序
            wifi_networks.sort(key=lambda x: x.get('signal', 0), reverse=True)
            
            self.logger.info(f"iw扫描完成，发现 {len(wifi_networks)} 个WiFi网络")
            return wifi_networks
            
        except Exception as e:
            self.logger.error(f"使用iw扫描WiFi网络时发生错误: {e}")
            return []

    def _scan_wifi_with_iwlist(self, interface):
        """
        使用iwlist命令扫描WiFi网络（更稳定的传统方法）
        
        Args:
            interface (str): 要使用的WiFi接口名称
            
        Returns:
            list: WiFi网络列表
        """
        self.logger.info(f"使用iwlist命令扫描WiFi网络，接口: {interface}")
        wifi_networks = []
        
        try:
            # 检查接口是否可用
            if not self._check_interface_up_status(interface):
                self.logger.error(f"WiFi接口 {interface} 不可用")
                return []
            
            # 执行iwlist扫描
            result = subprocess.run(
                ["sudo", "iwlist", interface, "scan"],
                capture_output=True,
                text=True,
                timeout=20
            )
            
            if result.returncode != 0:
                self.logger.error(f"iwlist扫描失败: {result.stderr}")
                return []
            
            # 解析iwlist输出
            current_network = {}
            for line in result.stdout.split('\n'):
                line = line.strip()
                
                if 'Cell' in line and 'Address:' in line:
                    # 保存上一个网络
                    if current_network.get('ssid'):
                        wifi_networks.append(current_network.copy())
                    
                    # 开始新网络
                    current_network = {
                        'bssid': line.split('Address: ')[-1],
                        'frequency': '',
                        'signal_level': '',
                        'signal': 0,
                        'ssid': '',
                        'security': 'Open',
                        'channel': '',
                        'band': '',
                        'quality': '',
                        'rate': ''
                    }
                
                elif 'ESSID:' in line:
                    ssid = line.split('ESSID:')[-1].strip('"')
                    current_network['ssid'] = ssid
                
                elif 'Quality=' in line:
                    try:
                        signal_part = line.split('Signal level=')[-1].split()[0]
                        signal_dbm = int(signal_part.replace('dBm', ''))
                        current_network['signal_level'] = signal_dbm
                        
                        # 计算信号百分比 (与nmcli格式一致)
                        if signal_dbm >= -50:
                            signal_percentage = 100
                        elif signal_dbm <= -100:
                            signal_percentage = 0
                        else:
                            signal_percentage = int(((signal_dbm + 100) / 50) * 100)
                        
                        current_network['signal'] = signal_percentage
                        current_network['quality'] = self._calculate_signal_quality(signal_dbm)
                    except:
                        current_network['signal_level'] = -50
                        current_network['signal'] = 50
                        current_network['quality'] = "Fair"
                
                elif 'Frequency:' in line:
                    freq_str = line.split('Frequency:')[-1].split()[0]
                    current_network['frequency'] = freq_str
                    
                    # 计算信道和频段
                    try:
                        freq_ghz = float(freq_str)
                        if freq_ghz > 5:
                            current_network['band'] = "5GHz"
                            channel = int((freq_ghz - 5.0) * 200)
                        else:
                            current_network['band'] = "2.4GHz"
                            channel = int((freq_ghz - 2.407) * 1000 / 5)
                        current_network['channel'] = str(channel)
                    except:
                        current_network['channel'] = '1'
                        current_network['band'] = "2.4GHz"
                
                elif 'Encryption key:on' in line:
                    current_network['security'] = 'WPA/WPA2'
                    
                elif 'Bit Rates:' in line:
                    # 提取速率信息
                    try:
                        rate_part = line.split('Bit Rates:')[-1].strip()
                        current_network['rate'] = rate_part.split()[0] if rate_part else ''
                    except:
                        current_network['rate'] = ''
            
            # 添加最后一个网络
            if current_network.get('ssid'):
                # 确保所有必需字段都存在
                if 'rate' not in current_network:
                    current_network['rate'] = ''
                wifi_networks.append(current_network)
            
            # 过滤和排序
            wifi_networks = [n for n in wifi_networks if n['ssid'] and n['ssid'] != '']
            wifi_networks.sort(key=lambda x: x.get('signal', 0), reverse=True)
            
            self.logger.info(f"iwlist扫描完成，发现 {len(wifi_networks)} 个网络")
            return wifi_networks
            
        except subprocess.TimeoutExpired:
            self.logger.error("iwlist扫描超时")
            return []
        except Exception as e:
            self.logger.error(f"iwlist扫描时发生错误: {e}")
            return []

    def _scan_wifi_with_wpa_cli(self, interface):
        """
        使用wpa_cli命令扫描WiFi网络（适用于unitree设备）
        
        Args:
            interface (str): 要使用的WiFi接口名称
            
        Returns:
            list: WiFi网络列表
        """
        self.logger.info(f"使用wpa_cli命令扫描WiFi网络，接口: {interface}")
        wifi_networks = []
        
        try:
            # 触发扫描
            scan_result = subprocess.run(
                ["sudo", "wpa_cli", "-i", interface, "scan"],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if scan_result.returncode != 0:
                self.logger.error(f"wpa_cli扫描触发失败: {scan_result.stderr}")
                return []
            
            # 等待扫描完成
            time.sleep(3)
            
            # 获取扫描结果
            result = subprocess.run(
                ["sudo", "wpa_cli", "-i", interface, "scan_results"],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode != 0:
                self.logger.error(f"wpa_cli获取结果失败: {result.stderr}")
                return []
            
            # 解析结果
            lines = result.stdout.strip().split('\n')[1:]  # 跳过标题行
            for line in lines:
                if not line.strip():
                    continue
                
                parts = line.split('\t')
                if len(parts) >= 5:
                    # 解析信号强度
                    signal_dbm = int(parts[2])
                    if signal_dbm >= -50:
                        signal_percentage = 100
                    elif signal_dbm <= -100:
                        signal_percentage = 0
                    else:
                        signal_percentage = int(((signal_dbm + 100) / 50) * 100)
                    
                    # 解析频率和频段
                    frequency = parts[1]
                    try:
                        freq_mhz = int(frequency)
                        if freq_mhz > 5000:
                            band = "5GHz"
                        else:
                            band = "2.4GHz"
                    except:
                        band = "2.4GHz"
                    
                    wifi_networks.append({
                        'ssid': parts[4],
                        'bssid': parts[0],
                        'channel': self._frequency_to_channel(parts[1]),
                        'frequency': frequency,
                        'band': band,
                        'signal': signal_percentage,
                        'signal_level': signal_dbm,
                        'quality': self._calculate_signal_quality(signal_dbm),
                        'security': parts[3],
                        'rate': ''
                    })
            
            # 过滤和排序
            wifi_networks = [n for n in wifi_networks if n['ssid'] and n['ssid'] != '']
            wifi_networks.sort(key=lambda x: int(x.get('signal_level', '-100')), reverse=True)
            
            self.logger.info(f"wpa_cli扫描完成，发现 {len(wifi_networks)} 个网络")
            return wifi_networks
            
        except subprocess.TimeoutExpired:
            self.logger.error("wpa_cli扫描超时")
            return []
        except Exception as e:
            self.logger.error(f"wpa_cli扫描时发生错误: {e}")
            return []

    def _scan_wifi_with_nmcli(self, interface):
        """
        使用nmcli命令扫描WiFi网络（备用方案）
        
        Args:
            interface (str): 要使用的WiFi接口名称
            
        Returns:
            list: WiFi网络列表
        """
        self.logger.info(f"使用nmcli命令扫描WiFi网络，接口: {interface}")
        wifi_networks = []
        
        try:
            # 1. 检查接口是否可用
            if not self._check_interface_up_status(interface):
                self.logger.error(f"WiFi接口 {interface} 不可用")
                return []
            
            # 2. 执行扫描命令
            scan_cmd = f"sudo nmcli device wifi rescan ifname {interface}"
            scan_result = self.run_command(scan_cmd, timeout=10)
            
            if scan_result is None:
                self.logger.error(f"nmcli扫描命令执行失败")
                return []
            
            # 3. 等待扫描完成
            time.sleep(3)
            
            # 4. 获取扫描结果
            list_cmd = f"sudo nmcli -t -f SSID,BSSID,CHAN,RATE,SIGNAL,SECURITY device wifi list ifname {interface}"
            result = self.run_command(list_cmd, timeout=10)
            
            if result is None:
                self.logger.error(f"获取nmcli扫描结果失败")
                return []
            
            # 5. 解析扫描结果
            for line in result.split('\n'):
                if not line.strip():
                    continue
                
                # 跳过没有SSID的行（以冒号开头的行）
                if line.startswith(':'):
                    continue
                    
                # nmcli输出格式: SSID:BSSID:CHAN:RATE:SIGNAL:SECURITY
                # 注意：BSSID可能包含转义字符，需要特殊处理
                parts = line.split(':')
                if len(parts) >= 6:
                    ssid = parts[0]
                    
                    # 跳过空SSID或隐藏网络
                    if not ssid or ssid == '*':
                        continue
                    
                    # 处理BSSID（移除转义字符）
                    bssid_raw = parts[1]
                    bssid = bssid_raw.replace('\\:', ':')  # 移除转义字符
                    
                    # 处理其他字段
                    channel = parts[2]
                    rate = parts[3]
                    signal = parts[4]
                    security = parts[5]
                    
                    # 转换信号强度
                    try:
                        signal_percentage = int(signal)
                        # 估算信号强度dBm
                        if signal_percentage >= 80:
                            signal_dbm = -40
                        elif signal_percentage >= 60:
                            signal_dbm = -50
                        elif signal_percentage >= 40:
                            signal_dbm = -60
                        elif signal_percentage >= 20:
                            signal_dbm = -70
                        else:
                            signal_dbm = -80
                    except ValueError:
                        signal_dbm = -70
                        signal_percentage = 50
                    
                    # 确定频段
                    try:
                        channel_num = int(channel)
                        if channel_num <= 14:
                            band = "2.4GHz"
                            frequency = 2400 + (channel_num * 5)
                        else:
                            band = "5GHz"
                            frequency = 5000 + (channel_num * 5)
                    except ValueError:
                        band = "Unknown"
                        frequency = 0
                    
                    # 处理安全类型
                    if not security or security == '*':
                        security_type = "Open"
                    else:
                        security_type = security
                    
                    network = {
                        "ssid": ssid,
                        "bssid": bssid,
                        "channel": channel,
                        "frequency": frequency,
                        "band": band,
                        "signal": signal_percentage,
                        "signal_level": signal_dbm,
                        "quality": self._calculate_signal_quality(signal_dbm),
                        "security": security_type,
                        "rate": rate
                    }
                    
                    wifi_networks.append(network)
            
            # 按信号强度排序
            wifi_networks.sort(key=lambda x: x.get('signal', 0), reverse=True)
            
            self.logger.info(f"nmcli扫描完成，发现 {len(wifi_networks)} 个WiFi网络")
            return wifi_networks
            
        except Exception as e:
            self.logger.error(f"使用nmcli扫描WiFi网络时发生错误: {e}")
            return []

    def connect_wifi_network(self, ssid, password=None):
        """
        连接到指定的WiFi网络

        Args:
            ssid (str): WiFi网络名称
            password (str, optional): WiFi密码，开放网络时为None

        Returns:
            dict: 连接结果，包含成功状态和消息
        """
        self.logger.info(f"尝试连接WiFi网络: {ssid}")

        try:
            # 首先检查是否已经连接到目标WiFi
            current_ssid = self.get_wifi_name()
            if current_ssid == ssid:
                self.logger.info(f"检测到已连接到目标网络: {ssid}")
                
                # 检查连接质量和网络状态
                interface_ok = self.check_interface_status(self.wifi_interface)
                
                if interface_ok:
                    # 获取更详细的连接信息
                    try:
                        wifi_status = self.get_wifi_connection_status()
                        signal_info = f", 信号强度: {wifi_status.get('signal_strength', 'Unknown')}" if wifi_status.get('signal_strength') else ""
                        ip_info = f", IP: {wifi_status.get('ip_address', 'Unknown')}" if wifi_status.get('ip_address') else ""
                    except:
                        signal_info = ""
                        ip_info = ""
                    
                    self.logger.info(f"网络连接状态良好，无需重新连接{signal_info}{ip_info}")
                    return {
                        "success": True,
                        "message": f"已连接到 {ssid}，网络状态正常{signal_info}",
                        "error_code": 200,
                        "already_connected": True
                    }
                else:
                    self.logger.warning(f"已连接到 {ssid} 但网络状态不佳，尝试重新连接")
            else:
                if current_ssid and current_ssid not in ["Error", "Unknown", ""]:
                    self.logger.info(f"当前连接网络: {current_ssid}, 目标网络: {ssid}, 需要切换网络")
            
            if self.device_type == "unitree":
                # unitree设备使用wpa_cli连接
                return self._connect_wifi_unitree(ssid, password)
            else:
                # ysc设备使用nmcli连接
                return self._connect_wifi_nmcli(ssid, password)

        except Exception as e:
            self.logger.error(f"连接WiFi网络时发生错误: {e}")
            return {
                "success": False,
                "message": f"连接失败: {str(e)}",
                "error_code": 500
            }

    def check_wpa_supplicant_status(self):
        """
        检查wpa_supplicant服务状态
        
        Returns:
            dict: wpa_supplicant状态信息
        """
        try:
            import psutil
            
            wpa_processes = []
            for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'status']):
                try:
                    if proc.info['name'] == 'wpa_supplicant':
                        wpa_processes.append({
                            'pid': proc.info['pid'],
                            'cmdline': ' '.join(proc.info['cmdline']),
                            'status': proc.info['status']
                        })
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            # 检查wpa_cli是否能正常通信
            wpa_cli_working = False
            try:
                result = subprocess.run(
                    ["sudo", "wpa_cli", "-i", self.wifi_interface, "status"],
                    capture_output=True,
                    text=True,
                    timeout=5
                )
                wpa_cli_working = result.returncode == 0
            except:
                wpa_cli_working = False
            
            return {
                "processes": wpa_processes,
                "process_count": len(wpa_processes),
                "wpa_cli_working": wpa_cli_working,
                "is_healthy": len(wpa_processes) > 0 and wpa_cli_working
            }
            
        except Exception as e:
            self.logger.error(f"检查wpa_supplicant状态时发生错误: {e}")
            return {
                "processes": [],
                "process_count": 0,
                "wpa_cli_working": False,
                "is_healthy": False,
                "error": str(e)
            }

    def restart_wpa_supplicant(self):
        """
        重启wpa_supplicant服务
        
        Returns:
            dict: 重启结果
        """
        try:
            self.logger.info("开始重启wpa_supplicant服务...")
            
            # 1. 停止现有的wpa_supplicant进程
            stop_result = self._stop_wpa_supplicant()
            if not stop_result["success"]:
                self.logger.warning(f"停止wpa_supplicant失败: {stop_result['message']}")
            
            # 2. 等待进程完全终止
            time.sleep(3)
            
            # 3. 启动新的wpa_supplicant进程
            start_result = self._start_wpa_supplicant()
            
            if start_result["success"]:
                # 4. 验证启动是否成功
                time.sleep(2)
                status = self.check_wpa_supplicant_status()
                
                if status["is_healthy"]:
                    self.logger.info("wpa_supplicant重启成功")
                    return {
                        "success": True,
                        "message": "wpa_supplicant重启成功",
                        "status": status
                    }
                else:
                    return {
                        "success": False,
                        "message": "wpa_supplicant启动后状态异常",
                        "status": status
                    }
            else:
                return start_result
                
        except Exception as e:
            self.logger.error(f"重启wpa_supplicant时发生错误: {e}")
            return {
                "success": False,
                "message": f"重启失败: {str(e)}"
            }

    def _stop_wpa_supplicant(self):
        """停止wpa_supplicant服务"""
        try:
            import psutil
            
            stopped_count = 0
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if (proc.info['name'] == 'wpa_supplicant' and 
                        self.wifi_interface in ' '.join(proc.info['cmdline'])):
                        
                        self.logger.info(f"停止wpa_supplicant进程 PID: {proc.info['pid']}")
                        process = psutil.Process(proc.info['pid'])
                        process.terminate()
                        
                        # 等待进程终止，如果不行就强制杀死
                        try:
                            process.wait(timeout=5)
                        except psutil.TimeoutExpired:
                            self.logger.warning(f"进程 {proc.info['pid']} 未正常终止，强制杀死")
                            process.kill()
                        
                        stopped_count += 1
                        
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            return {
                "success": True,
                "message": f"停止了 {stopped_count} 个wpa_supplicant进程"
            }
            
        except Exception as e:
            self.logger.error(f"停止wpa_supplicant时发生错误: {e}")
            return {
                "success": False,
                "message": f"停止失败: {str(e)}"
            }

    def _start_wpa_supplicant(self):
        """启动wpa_supplicant服务"""
        try:
            # 检查配置文件是否存在
            wpa_conf_path = f"/etc/wpa_supplicant/wpa_supplicant-{self.wifi_interface}.conf"
            
            if not os.path.exists(wpa_conf_path):
                # 创建基础配置文件
                self._create_basic_wpa_conf(wpa_conf_path)
            
            # 启动wpa_supplicant
            cmd = [
                "sudo", "wpa_supplicant",
                "-i", self.wifi_interface,
                "-c", wpa_conf_path,
                "-B"  # 后台运行
            ]
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                self.logger.info("wpa_supplicant启动成功")
                return {
                    "success": True,
                    "message": "wpa_supplicant启动成功"
                }
            else:
                error_msg = result.stderr.strip() if result.stderr else "未知错误"
                self.logger.error(f"wpa_supplicant启动失败: {error_msg}")
                return {
                    "success": False,
                    "message": f"启动失败: {error_msg}"
                }
                
        except Exception as e:
            self.logger.error(f"启动wpa_supplicant时发生错误: {e}")
            return {
                "success": False,
                "message": f"启动失败: {str(e)}"
            }

    def _create_basic_wpa_conf(self, conf_path):
        """创建基础的wpa_supplicant配置文件"""
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(conf_path), exist_ok=True)
            
            basic_conf = f"""# Auto-generated wpa_supplicant configuration
ctrl_interface=/var/run/wpa_supplicant
ctrl_interface_group=0
update_config=1
country=CN
ap_scan=1

# Empty network block - will be populated by wpa_cli
"""
            
            with open(conf_path, 'w') as f:
                f.write(basic_conf)
            
            # 设置适当的权限
            os.chmod(conf_path, 0o600)
            
            self.logger.info(f"创建基础wpa_supplicant配置文件: {conf_path}")
            
        except Exception as e:
            self.logger.error(f"创建wpa_supplicant配置文件时发生错误: {e}")
            raise

    def _connect_wifi_unitree(self, ssid, password):
        """使用wpa_cli连接WiFi (unitree设备)"""
        try:
            # 1. 检查wpa_supplicant状态
            wpa_status = self.check_wpa_supplicant_status()
            if not wpa_status["is_healthy"]:
                self.logger.warning("wpa_supplicant状态异常，尝试重启...")
                restart_result = self.restart_wpa_supplicant()
                if not restart_result["success"]:
                    return {
                        "success": False,
                        "message": f"wpa_supplicant重启失败: {restart_result['message']}",
                        "error_code": 503
                    }
                
                # 重启后再次检查
                time.sleep(2)
                wpa_status = self.check_wpa_supplicant_status()
                if not wpa_status["is_healthy"]:
                    return {
                        "success": False,
                        "message": "wpa_supplicant服务异常，无法连接WiFi",
                        "error_code": 503
                    }
            
            # 2. 检查是否已有该网络的配置
            existing_networks = self._get_existing_wifi_networks()
            existing_network_id = None
            
            for net_id, net_ssid in existing_networks.items():
                if net_ssid == ssid:
                    existing_network_id = net_id
                    break
            
            if existing_network_id:
                # 使用已有配置
                self.logger.info(f"使用已有网络配置: {existing_network_id}")
                self.run_command(f"sudo wpa_cli -i {self.wifi_interface} select_network {existing_network_id}")
                network_id = existing_network_id
            else:
                # 添加新网络配置
                result = subprocess.run(
                    ["sudo", "wpa_cli", "-i", self.wifi_interface, "add_network"],
                    capture_output=True,
                    text=True,
                    check=True
                )
                network_id = result.stdout.strip()

                # 设置SSID
                self.run_command(f'sudo wpa_cli -i {self.wifi_interface} set_network {network_id} ssid \\"{ssid}\\"')

                if password:
                    # 设置密码
                    self.run_command(f'sudo wpa_cli -i {self.wifi_interface} set_network {network_id} psk \\"{password}\\"')
                else:
                    # 开放网络
                    self.run_command(f'sudo wpa_cli -i {self.wifi_interface} set_network {network_id} key_mgmt NONE')

                # 启用网络
                self.run_command(f"sudo wpa_cli -i {self.wifi_interface} enable_network {network_id}")
                self.run_command(f"sudo wpa_cli -i {self.wifi_interface} select_network {network_id}")

            # 智能等待连接并监控状态
            connection_result = self._wait_for_wifi_connection_unitree(ssid, network_id, timeout=30)
            
            # 根据连接结果处理
            if connection_result["success"]:
                self.logger.info(f"成功连接到WiFi网络: {ssid}")
                return {
                    "success": True,
                    "message": f"成功连接到 {ssid}",
                    "error_code": 200
                }
            else:
                # 连接失败，如果是新添加的网络则移除配置
                if not existing_network_id:
                    self.run_command(f"sudo wpa_cli -i {self.wifi_interface} remove_network {network_id}")
                
                return {
                    "success": False,
                    "message": connection_result["message"],
                    "error_code": connection_result["error_code"]
                }

        except Exception as e:
            self.logger.error(f"使用wpa_cli连接WiFi时发生错误: {e}")
            return {
                "success": False,
                "message": f"连接失败: {str(e)}",
                "error_code": 500
            }

    def _get_existing_wifi_networks(self):
        """获取wpa_cli中已配置的WiFi网络"""
        try:
            result = subprocess.run(
                ["sudo", "wpa_cli", "-i", self.wifi_interface, "list_networks"],
                capture_output=True,
                text=True,
                check=True
            )
            
            networks = {}
            lines = result.stdout.strip().split('\n')[1:]  # 跳过标题行
            for line in lines:
                if line.strip():
                    parts = line.split('\t')
                    if len(parts) >= 2:
                        network_id = parts[0].strip()
                        ssid = parts[1].strip()
                        networks[network_id] = ssid
            
            return networks
        except Exception as e:
            self.logger.error(f"获取已配置WiFi网络时发生错误: {e}")
            return {}

    def _connect_wifi_nmcli(self, ssid, password):
        """使用nmcli连接WiFi (ysc设备)"""
        try:
            # 先记录连接开始时间
            connect_start_time = time.time()
            
            if password:
                # 有密码的网络
                result = subprocess.run(
                    ["sudo", "nmcli", "dev", "wifi", "connect", ssid, "password", password],
                    capture_output=True,
                    text=True,
                    timeout=30
                )
            else:
                # 开放网络
                result = subprocess.run(
                    ["sudo", "nmcli", "dev", "wifi", "connect", ssid],
                    capture_output=True,
                    text=True,
                    timeout=30
                )

            if result.returncode == 0:
                # 等待连接稳定并验证
                connection_result = self._wait_for_wifi_connection_nmcli(ssid, timeout=20)
                
                if connection_result["success"]:
                    self.logger.info(f"成功连接到WiFi网络: {ssid}")
                    return {
                        "success": True,
                        "message": f"成功连接到 {ssid}",
                        "error_code": 200
                    }
                else:
                    return {
                        "success": False,
                        "message": connection_result["message"],
                        "error_code": connection_result["error_code"]
                    }
            else:
                # 分析错误原因
                error_msg = result.stderr.strip() if result.stderr else "未知错误"
                self.logger.error(f"nmcli连接失败: {error_msg}")
                
                return self._analyze_nmcli_connection_error(error_msg, ssid)

        except subprocess.TimeoutExpired:
            return {
                "success": False,
                "message": f"连接 {ssid} 超时 - 命令执行超过30秒",
                "error_code": 408
            }
        except Exception as e:
            self.logger.error(f"使用nmcli连接WiFi时发生错误: {e}")
            return {
                "success": False,
                "message": f"连接失败: {str(e)}",
                "error_code": 500
            }

    def _wait_for_wifi_connection_unitree(self, ssid, network_id, timeout=30):
        """
        智能等待unitree设备WiFi连接并分析失败原因
        
        Args:
            ssid (str): 目标SSID
            network_id (str): 网络ID
            timeout (int): 超时时间(秒)
            
        Returns:
            dict: 连接结果和详细原因
        """
        start_time = time.time()
        check_interval = 2
        last_status = None
        
        self.logger.info(f"等待连接到 {ssid}，超时时间: {timeout}秒")
        
        while time.time() - start_time < timeout:
            try:
                # 检查wpa_cli状态
                status_result = subprocess.run(
                    ["sudo", "wpa_cli", "-i", self.wifi_interface, "status"],
                    capture_output=True,
                    text=True,
                    timeout=5
                )
                
                if status_result.returncode == 0:
                    status_output = status_result.stdout
                    
                    # 解析状态
                    wpa_state = ""
                    ssid_line = ""
                    for line in status_output.split('\n'):
                        if line.startswith('wpa_state='):
                            wpa_state = line.split('=')[1]
                        elif line.startswith('ssid='):
                            ssid_line = line.split('=')[1]
                    
                    # 检查连接状态
                    if wpa_state == "COMPLETED" and ssid_line == ssid:
                        # 进一步验证IP地址
                        if self.check_interface_status(self.wifi_interface):
                            return {
                                "success": True,
                                "message": f"成功连接到 {ssid}"
                            }
                    
                    # 检查是否是密码错误
                    if wpa_state in ["4WAY_HANDSHAKE", "GROUP_HANDSHAKE"]:
                        if time.time() - start_time > 15:  # 握手超过15秒通常是密码错误
                            return {
                                "success": False,
                                "message": f"连接 {ssid} 失败: 密码错误或网络拒绝连接",
                                "error_code": 401
                            }
                    
                    # 检查是否无法找到网络
                    if wpa_state == "SCANNING" and time.time() - start_time > 10:
                        return {
                            "success": False,
                            "message": f"连接 {ssid} 失败: 网络不可达或信号太弱",
                            "error_code": 404
                        }
                
                # 检查wpa_supplicant是否异常
                if wpa_state == "DISCONNECTED" and time.time() - start_time > 8:
                    # 可能是wpa_supplicant异常或配置错误
                    self.logger.warning("检测到wpa_supplicant可能异常，检查服务状态...")
                    wpa_status = self.check_wpa_supplicant_status()
                    if not wpa_status["is_healthy"]:
                        return {
                            "success": False,
                            "message": f"连接 {ssid} 失败: wpa_supplicant服务异常",
                            "error_code": 503
                        }
                
                last_status = wpa_state
                
                time.sleep(check_interval)
                
            except Exception as e:
                self.logger.warning(f"检查连接状态时出错: {e}")
                time.sleep(check_interval)
        
        # 超时处理
        return {
            "success": False,
            "message": f"连接 {ssid} 超时 - 最后状态: {last_status or '未知'}",
            "error_code": 408
        }

    def _wait_for_wifi_connection_nmcli(self, ssid, timeout=20):
        """
        智能等待nmcli WiFi连接并分析失败原因
        
        Args:
            ssid (str): 目标SSID
            timeout (int): 超时时间(秒)
            
        Returns:
            dict: 连接结果和详细原因
        """
        start_time = time.time()
        check_interval = 2
        
        self.logger.info(f"验证连接到 {ssid}，超时时间: {timeout}秒")
        
        while time.time() - start_time < timeout:
            try:
                # 检查连接状态
                if self._check_wifi_connection_success(ssid):
                    return {
                        "success": True,
                        "message": f"成功连接到 {ssid}"
                    }
                
                # 检查是否有错误状态
                status_result = subprocess.run(
                    ["sudo", "nmcli", "-t", "-f", "STATE,CONNECTIVITY", "general", "status"],
                    capture_output=True,
                    text=True,
                    timeout=5
                )
                
                if status_result.returncode == 0:
                    status_lines = status_result.stdout.strip().split('\n')
                    if len(status_lines) >= 1:
                        state = status_lines[0].split(':')[0] if ':' in status_lines[0] else status_lines[0]
                        if state == "disconnected":
                            return {
                                "success": False,
                                "message": f"连接 {ssid} 后验证失败: 网络已断开",
                                "error_code": 402
                            }
                
                time.sleep(check_interval)
                
            except Exception as e:
                self.logger.warning(f"验证连接状态时出错: {e}")
                time.sleep(check_interval)
        
        # 超时处理
        return {
            "success": False,
            "message": f"连接 {ssid} 后验证超时",
            "error_code": 408
        }

    def _analyze_nmcli_connection_error(self, error_msg, ssid):
        """
        分析nmcli连接错误并返回具体原因
        
        Args:
            error_msg (str): 错误消息
            ssid (str): 目标SSID
            
        Returns:
            dict: 错误分析结果
        """
        error_msg_lower = error_msg.lower()
        
        # 检查是否已经连接
        if "already connected" in error_msg_lower or "connection already active" in error_msg_lower:
            if self._check_wifi_connection_success(ssid):
                self.logger.info(f"nmcli报告已连接到 {ssid}，验证连接状态成功")
                return {
                    "success": True,
                    "message": f"已连接到 {ssid}，网络状态正常",
                    "error_code": 200,
                    "already_connected": True
                }
        
        # 密码错误相关
        password_error_keywords = [
            "secrets were required", "password", "authentication", 
            "802-11-wireless-security", "psk", "key_mgmt"
        ]
        if any(keyword in error_msg_lower for keyword in password_error_keywords):
            return {
                "success": False,
                "message": f"连接 {ssid} 失败: 密码错误或认证失败",
                "error_code": 401
            }
        
        # 网络不可达
        network_error_keywords = [
            "no network with ssid", "not found", "no such device", 
            "activation failed", "no suitable device found"
        ]
        if any(keyword in error_msg_lower for keyword in network_error_keywords):
            return {
                "success": False,
                "message": f"连接 {ssid} 失败: 网络不可达或不存在",
                "error_code": 404
            }
        
        # 超时相关
        timeout_keywords = ["timeout", "timed out", "no response"]
        if any(keyword in error_msg_lower for keyword in timeout_keywords):
            return {
                "success": False,
                "message": f"连接 {ssid} 失败: 连接超时",
                "error_code": 408
            }
        
        # 权限问题
        permission_keywords = ["permission denied", "not authorized", "access denied"]
        if any(keyword in error_msg_lower for keyword in permission_keywords):
            return {
                "success": False,
                "message": f"连接 {ssid} 失败: 权限不足",
                "error_code": 403
            }
        
        # 设备忙碌
        busy_keywords = ["device busy", "resource busy", "operation already in progress"]
        if any(keyword in error_msg_lower for keyword in busy_keywords):
            return {
                "success": False,
                "message": f"连接 {ssid} 失败: 设备忙碌，请稍后重试",
                "error_code": 409
            }
        
        # 默认未知错误
        return {
            "success": False,
            "message": f"连接 {ssid} 失败: {error_msg}",
            "error_code": 500
        }

    def _check_wifi_connection_success(self, expected_ssid):
        """
        检查WiFi连接是否成功

        Args:
            expected_ssid (str): 期望连接的SSID

        Returns:
            bool: 连接是否成功
        """
        try:
            # 检查当前连接的SSID
            current_ssid = self.get_wifi_name()
            if current_ssid == expected_ssid:
                # 检查是否有IP地址
                if self.check_interface_status(self.wifi_interface):
                    return True
            return False
        except Exception as e:
            self.logger.error(f"检查WiFi连接状态时发生错误: {e}")
            return False

    def disconnect_wifi(self):
        """
        断开当前WiFi连接

        Returns:
            dict: 断开结果
        """
        self.logger.info("断开WiFi连接...")

        try:
            if self.device_type == "unitree":
                # unitree设备使用wpa_cli断开
                self.run_command(f"sudo wpa_cli -i {self.wifi_interface} disconnect")
            else:
                # ysc设备使用nmcli断开
                self.run_command(f"sudo nmcli dev disconnect {self.wifi_interface}")

            # 等待断开生效
            time.sleep(3)

            # 验证断开状态
            current_ssid = self.get_wifi_name()
            if current_ssid in ["Error", "Unknown", ""]:
                self.logger.info("WiFi连接已成功断开")
                return {
                    "success": True,
                    "message": "WiFi连接已断开",
                    "error_code": 200
                }
            else:
                return {
                    "success": False,
                    "message": f"WiFi断开失败，仍连接到: {current_ssid}",
                    "error_code": 500
                }

        except Exception as e:
            self.logger.error(f"断开WiFi连接时发生错误: {e}")
            return {
                "success": False,
                "message": f"断开失败: {str(e)}",
                "error_code": 500
            }

    def get_wifi_connection_status(self):
        """
        获取WiFi连接详细状态

        Returns:
            dict: WiFi连接状态信息
        """
        try:
            status = {
                "interface": self.wifi_interface,
                "is_up": self.check_interface_status(self.wifi_interface),
                "current_ssid": self.get_wifi_name(),
                "has_internet": False,
                "signal_quality": "unknown",
                "signal_strength": None,
                "ip_address": None,
                "mac_address": None,
                "connection_time": None
            }

            # 获取IP地址
            status["ip_address"] = self._get_interface_ip(self.wifi_interface)

            # 获取MAC地址
            status["mac_address"] = self._get_interface_mac(self.wifi_interface)

            # 检查互联网连接
            if status["is_up"]:
                status["has_internet"] = self.check_external_connectivity(self.wifi_interface)

                # 获取信号强度和质量
                signal_info = self._get_wifi_signal_info()
                status.update(signal_info)

            return status

        except Exception as e:
            self.logger.error(f"获取WiFi连接状态时发生错误: {e}")
            return {
                "interface": self.wifi_interface,
                "error": str(e)
            }

    def _get_interface_ip(self, interface):
        """获取网络接口的IP地址"""
        try:
            result = subprocess.run(
                ["ip", "addr", "show", interface],
                capture_output=True,
                text=True,
                check=True
            )

            # 解析IP地址
            import re
            ip_match = re.search(r'inet (\d+\.\d+\.\d+\.\d+)', result.stdout)
            return ip_match.group(1) if ip_match else None

        except Exception:
            return None

    def _get_interface_mac(self, interface):
        """获取网络接口的MAC地址"""
        try:
            result = subprocess.run(
                ["ip", "link", "show", interface],
                capture_output=True,
                text=True,
                check=True
            )

            # 解析MAC地址
            import re
            mac_match = re.search(r'link/ether ([a-f0-9:]{17})', result.stdout)
            return mac_match.group(1) if mac_match else None

        except Exception:
            return None

    def _get_wifi_signal_info(self):
        """获取WiFi信号信息"""
        signal_info = {
            "signal_quality": "unknown",
            "signal_strength": None,
            "link_quality": None,
            "frequency": None
        }

        try:
            if self.device_type == "unitree":
                # 使用wpa_cli获取信号信息
                result = subprocess.run(
                    ["sudo", "wpa_cli", "-i", self.wifi_interface, "signal_poll"],
                    capture_output=True,
                    text=True,
                    check=True
                )

                for line in result.stdout.split('\n'):
                    if 'RSSI=' in line:
                        rssi = int(line.split('=')[1])
                        signal_info["signal_strength"] = rssi
                        signal_info["signal_quality"] = self._calculate_signal_quality(rssi)
                    elif 'FREQUENCY=' in line:
                        signal_info["frequency"] = line.split('=')[1]
            else:
                # 使用iwconfig获取信号信息
                result = subprocess.run(
                    ["iwconfig", self.wifi_interface],
                    capture_output=True,
                    text=True,
                    check=True
                )

                import re
                # 解析信号强度
                signal_match = re.search(r"Signal level=(-?\d+)", result.stdout)
                if signal_match:
                    signal_level = int(signal_match.group(1))
                    signal_info["signal_strength"] = signal_level
                    signal_info["signal_quality"] = self._calculate_signal_quality(signal_level)

                # 解析链路质量
                quality_match = re.search(r"Link Quality=(\d+)/(\d+)", result.stdout)
                if quality_match:
                    current = int(quality_match.group(1))
                    maximum = int(quality_match.group(2))
                    signal_info["link_quality"] = f"{current}/{maximum}"

                # 解析频率
                freq_match = re.search(r"Frequency:(\d+\.?\d*)", result.stdout)
                if freq_match:
                    signal_info["frequency"] = f"{freq_match.group(1)} GHz"

        except Exception as e:
            self.logger.debug(f"获取WiFi信号信息时发生错误: {e}")

        return signal_info

    def get_wifi_hardware_status(self):
        """
        获取WiFi硬件状态

        Returns:
            dict: WiFi硬件状态信息
        """
        try:
            status = {
                "interface": self.wifi_interface,
                "driver": None,
                "firmware": None,
                "supported_bands": [],
                "capabilities": [],
                "power_management": None,
                "is_managed": None
            }

            # 获取驱动信息
            try:
                result = subprocess.run(
                    ["ethtool", "-i", self.wifi_interface],
                    capture_output=True,
                    text=True,
                    check=True
                )

                for line in result.stdout.split('\n'):
                    if 'driver:' in line:
                        status["driver"] = line.split(':')[1].strip()
                    elif 'firmware-version:' in line:
                        status["firmware"] = line.split(':')[1].strip()

            except Exception:
                pass

            # 获取接口能力信息
            try:
                result = subprocess.run(
                    ["iw", self.wifi_interface, "info"],
                    capture_output=True,
                    text=True,
                    check=True
                )

                for line in result.stdout.split('\n'):
                    if 'type' in line:
                        status["capabilities"].append(line.strip())

            except Exception:
                pass

            # 检查是否被NetworkManager管理
            if self.device_type != "unitree":
                try:
                    result = subprocess.run(
                        ["nmcli", "dev", "show", self.wifi_interface],
                        capture_output=True,
                        text=True,
                        check=True
                    )
                    status["is_managed"] = "GENERAL.STATE" in result.stdout
                except Exception:
                    status["is_managed"] = False

            return status

        except Exception as e:
            self.logger.error(f"获取WiFi硬件状态时发生错误: {e}")
            return {
                "interface": self.wifi_interface,
                "error": str(e)
            }

    def configure_triple_network(self, wifi_internet_connected, current_internet_connected):
        """
        配置三网卡逻辑：内网使用 p2p0，外网优先使用 wifi，其次是 mobile

        Args:
            wifi_internet_connected (bool): WiFi是否能访问互联网
            mobile_internet_connected (bool): 移动网络是否能访问互联网

        Note:
            这里的参数表示的是实际的互联网连接状态，不是开关状态
        """
        self.logger.info("开始配置三网卡逻辑")
        try:

            # # 配置 WiFi NAT 规则
            # if wifi_connected:
            #     self.add_iptables_rule("***********/24", self.wifi_interface)
            #     self.logger.info(f"NAT 配置已应用于 {self.wifi_interface}")

            # # 配置移动网络 NAT 规则
            # elif mobile_connected:
            #     self.add_iptables_rule("***********/24", self.mobile_interface)
            #     self.logger.info(f"NAT 配置已应用于 {self.mobile_interface}")

            # 更新跃点值
            self.update_interface_metrics(wifi_internet_connected, current_internet_connected)
        except Exception as e:
            self.logger.error(f"配置三网卡逻辑时发生错误: {e}")

    def run_command(self, command, timeout=30):
        """执行系统命令"""
        try:
            result = subprocess.run(
                command, 
                shell=True, 
                text=True, 
                capture_output=True, 
                check=False,
                timeout=timeout
            )
            
            if result.returncode == 0:
                self.logger.info(f"命令执行成功: {command}")
                if result.stdout.strip():
                    self.logger.debug(f"命令输出: {result.stdout.strip()}")
                return result.stdout.strip()
            else:
                self.logger.warning(f"命令执行失败 (返回码: {result.returncode}): {command}")
                if result.stderr.strip():
                    self.logger.warning(f"错误信息: {result.stderr.strip()}")
                return None
                
        except subprocess.TimeoutExpired:
            self.logger.error(f"命令执行超时: {command}")
            return None
        except Exception as e:
            self.logger.error(f"执行命令时发生异常: {command}, 错误: {e}")
            return None

    def get_interface_metric(self, interface):
        """获取指定接口的跃点值"""
        try:
            result = subprocess.run(
                ["ip", "route", "show", "dev", interface],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                check=True
            )
            for line in result.stdout.splitlines():
                if "default" in line:
                    parts = line.split()
                    if "metric" in parts:
                        metric_index = parts.index("metric") + 1
                        return int(parts[metric_index])
            self.logger.warning(f"未找到接口 {interface} 的跃点值")
            return None
        except subprocess.CalledProcessError as e:
            self.logger.error(f"获取接口 {interface} 跃点值时发生错误: {e.stderr}")
            return None

    def update_interface_metrics(self, wifi_connect, internet_connected):
        """根据网络状态动态更新网络接口的跃点值
        Args:
            wifi_connect: WiFi是否连接 (1=连接, 0=未连接)
            internet_connected: 互联网是否连接 (1=连接, 0=未连接)
        """
        self.logger.info("开始更新网络接口的跃点值")
        try:
            # 获取当前默认路由表状态
            current_routes = self.get_current_routes()
            self.logger.info(f"当前路由表状态: {current_routes}")
            
            # 当wifi连接时，设置wifi跃点数最小
            if wifi_connect == 1:
                self.set_interface_metric_safely(self.wifi_interface, 100)
                self.logger.info(f"WiFi连接状态: 设置 {self.wifi_interface} 跃点值为100")
            
            # 当互联网连接但wifi未连接时，设置mobile跃点数最小
            elif internet_connected == 1 and wifi_connect == 0:
                self.set_interface_metric_safely(self.mobile_interface, 100)
                self.logger.info(f"移动网络连接状态: 设置 {self.mobile_interface} 跃点值为100")
            
            # 其他状态不更新路由表
            else:
                self.logger.info("无需更新路由表跃点数")
            
            # 确保系统DNS解析服务正常
            self.run_command("sudo systemctl restart systemd-resolved")
            
            # 验证路由更新结果
            new_routes = self.get_current_routes()
            self.logger.info(f"路由表更新完成，当前路由: {new_routes}")
        except Exception as e:
            self.logger.error(f"更新接口跃点值时发生错误: {e}")

    def get_current_routes(self):
        """获取当前系统的默认路由"""
        try:
            result = subprocess.run(
                ["ip", "route", "show", "default"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                check=True
            )
            return result.stdout.strip().splitlines()
        except subprocess.CalledProcessError as e:
            self.logger.error(f"获取默认路由时发生错误: {e.stderr}")
            return []

    def set_interface_metric_safely(self, interface, metric):
        """安全地设置指定接口的跃点值，并调整其他接口的跃点值"""
        self.logger.info(f"设置接口 {interface} 的跃点值为 {metric}，并调整其他接口跃点值")
        
        # 检查接口是否存在并且是UP状态
        if not self.check_interface_status(interface):
            self.logger.warning(f"接口 {interface} 不可用，跳过设置跃点值")
            return False
            
        try:
            # 获取网关
            gateway = self.get_gateway(interface)
            if not gateway:
                self.logger.error(f"无法获取接口 {interface} 的网关，跳过设置跃点值")
                return False

                            # 设置其他接口的跃点值为metric+50
            other_interfaces = []
            if interface == self.wifi_interface and hasattr(self, 'mobile_interface'):
                other_interfaces.append(self.mobile_interface)
            elif interface == self.mobile_interface and hasattr(self, 'wifi_interface'):
                other_interfaces.append(self.wifi_interface)
            
            for other_iface in other_interfaces:
                if self.check_interface_status(other_iface):
                    other_gateway = self.get_gateway(other_iface)
                    if other_gateway:
                        # 删除其他接口的现有默认路由
                        other_routes = self.get_interface_routes(other_iface)
                        if other_routes:
                            for route in other_routes:
                                other_gw = route.split()[1]
                                self.run_command(f"sudo route del default gw {other_gw} {other_iface}")
                        
                        # 设置其他接口的跃点值为metric+50
                        self.run_command(f"sudo route add default gw {other_gateway} dev {other_iface} metric {metric+50}")
                        self.logger.info(f"设置其他接口 {other_iface} 的跃点值为 {metric+50}")
                
                
            # 删除现有的默认路由（如果存在）
            current_routes = self.get_interface_routes(interface)
            if current_routes:
                for route in current_routes:
                    gateway_from_route = route.split()[1]  # 提取网关地址
                    del_command = f"sudo route del default gw {gateway_from_route} {interface}"
                    self.run_command(del_command)
                    self.logger.info(f"删除 {interface} 的默认路由: 网关 {gateway_from_route}")
            
            # 添加新的默认路由
            add_command = f"sudo route add default gw {gateway} dev {interface} metric {metric}"
            result = self.run_command(add_command)
            
            if result is not None:
                self.logger.info(f"成功设置 {interface} 的默认路由: 网关 {gateway}, 跃点值 {metric}")
                

                return True
            else:
                self.logger.error(f"设置 {interface} 的默认路由失败")
                return False
        except Exception as e:
            self.logger.error(f"设置接口 {interface} 的跃点值时发生错误: {e}")
            return False

    def get_interface_routes(self, interface):
        """获取特定接口的默认路由"""
        try:
            result = subprocess.run(
                f"route -n | grep 'UG' | grep '{interface}'",
                shell=True,
                text=True,
                capture_output=True,
                check=False  # 即使找不到路由也不报错
            )
            if result.returncode == 0:
                return result.stdout.strip().splitlines()
            return []
        except Exception as e:
            self.logger.error(f"获取接口 {interface} 的路由时发生错误: {e}")
            return []

    def delete_interface_routes_safely(self, interface):
        """安全地删除指定接口的所有默认路由"""
        try:
            routes = self.get_interface_routes(interface)
            if not routes:
                self.logger.info(f"接口 {interface} 没有默认路由，无需删除")
                return True
                
            for route in routes:
                try:
                    gateway = route.split()[1]  # 提取网关地址
                    del_command = f"sudo route del default gw {gateway} {interface}"
                    result = self.run_command(del_command)
                    if result is not None:
                        self.logger.info(f"成功删除 {interface} 的默认路由: 网关 {gateway}")
                    else:
                        self.logger.warning(f"删除 {interface} 的默认路由失败: 网关 {gateway}")
                except Exception as e:
                    self.logger.error(f"处理路由 '{route}' 时发生错误: {e}")
            
            # 验证是否删除成功
            remaining_routes = self.get_interface_routes(interface)
            if not remaining_routes:
                self.logger.info(f"接口 {interface} 的所有默认路由已删除")
                return True
            else:
                self.logger.warning(f"接口 {interface} 仍有 {len(remaining_routes)} 个默认路由未删除")
                return False
        except Exception as e:
            self.logger.error(f"删除接口 {interface} 的路由时发生错误: {e}")
            return False

    def get_gateway(self, interface):
        """获取特定网卡的网关地址"""
        try:
            # 使用 ip route 命令获取路由信息
            result = subprocess.run(
                ["ip", "route", "show", "dev", interface],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                check=True
            )
            # 解析输出，查找默认网关
            for line in result.stdout.splitlines():
                if line.startswith("default via"):
                    gateway = line.split()[2]  # 默认网关地址在第三个字段
                    self.logger.info(f"接口 {interface} 的网关地址为: {gateway}")
                    return gateway
            # 如果未找到默认网关，尝试推断网关地址
            self.logger.warning(f"接口 {interface} 未找到默认网关，尝试推断网关地址")
            ip_result = subprocess.run(
                ["ip", "-4", "addr", "show", interface],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                check=True
            )
            # 使用正则表达式提取 IP 地址
            match = re.search(r"inet (\d+\.\d+\.\d+)\.\d+", ip_result.stdout)
            if match:
                inferred_gateway = f"{match.group(1)}.1"  # 假设网关地址为前 24 位 + .1
                self.logger.info(f"推断的网关地址为: {inferred_gateway}")
                return inferred_gateway

            self.logger.error(f"无法推断接口 {interface} 的网关地址")
            return None
        except subprocess.CalledProcessError as e:
            self.logger.error(f"获取接口 {interface} 网关时发生错误: {e.stderr}")
            return None
        except Exception as e:
            self.logger.error(f"获取接口 {interface} 网关时发生未知错误: {e}")
            return None

    def set_interface_metric(self, interface, metric):
        """设置指定接口的跃点值"""
        self.logger.info(f"设置接口 {interface} 的跃点值为 {metric}")
        try:
            # 获取所有默认路由
            result = subprocess.run(
                f"route -n | grep 'UG' | grep '{interface}'",
                shell=True,
                text=True,
                capture_output=True,
                check=False
            )
            routes = result.stdout.strip().splitlines()
            if len(routes) >= 1:
                for route in routes:
                    gateway = route.split()[1]  # 提取网关地址
                    del_command = f"sudo route del default gw {gateway} {interface}"
                    self.run_command(del_command)
                    self.logger.info(f"删除 {interface} 的默认路由: {route}")
                add_command = f"sudo route add default gw {gateway} dev {interface} metric {metric}"
                self.run_command(add_command)
                self.logger.info(f"更新 {interface} 的默认路由: 网关 {gateway}, 跃点值 {metric}")
                return
            gateway = self.get_gateway(interface)
            add_command = f"sudo route add default gw {gateway} dev {interface} metric {metric}"
            self.run_command(add_command)
            self.logger.info(f"更新 {interface} 的默认路由: 网关 {gateway}, 跃点值 {metric}")
        except Exception as e:
            self.logger.error(f"更新接口 {interface} 的跃点值时发生错误: {e}")

    def start_dbus_monitor(self):
        """启动 DBus 监控线程"""
        self.dbus_thread = threading.Thread(target=self.monitor_dbus_signals, daemon=True)
        self.dbus_thread.start()

    def monitor_dbus_signals(self):
        """通过 gdbus 监听网络状态变更信号"""
        self.dbus_monitor.monitor_network_status()

    def handle_network_status(self, request, response):
        """处理网络服务请求"""
        self.logger.info("开始处理网络状态请求...")
        
        try:
            request_data = json.loads(request.data)
            self.logger.info(f"成功解析请求数据: {request.data[:100]}...")
        except json.JSONDecodeError as e:
            self.logger.error(f"请求数据JSON解析失败: {e}")
            response.error_code = 400
            response.result = "Invalid JSON in request."
            self.logger.info(f"返回错误响应: {response.error_code}, {response.result}")
            return response

        self.logger.info(f"接收到的请求数据: {json.dumps(request_data, indent=2)}")
        command = request_data.get("command")
        self.logger.info(f"请求命令: {command}")

        # ==============================================
        # 新增 jwae_tproxy_mode 处理逻辑
        # ==============================================
        if self.jwae_tproxy_mode == "enable":
            self.logger.info("进入 jwae_tproxy 代理模式处理流程")
            
            # 处理获取网络状态请求 - 使用 pycurl 实现
            if command == "getNetworkStatus":
                try:
                    buffer = BytesIO()
                    curl = pycurl.Curl()
                    
                    # 设置请求参数
                    curl.setopt(pycurl.URL, 'http://127.0.0.1:12000/getNetworkStatus')
                    curl.setopt(pycurl.WRITEFUNCTION, buffer.write)
                    curl.setopt(pycurl.CONNECTTIMEOUT, 2)  # 连接超时 2 秒
                    curl.setopt(pycurl.TIMEOUT, 3)         # 总超时 3 秒
                    
                    # 执行请求
                    curl.perform()
                    
                    # 检查响应状态
                    status_code = curl.getinfo(pycurl.RESPONSE_CODE)
                    if status_code != 200:
                        self.logger.error(f"代理服务返回异常状态码: {status_code}")
                        response.error_code = 502
                        response.result = f"代理服务异常 (HTTP {status_code})"
                        return response
                        
                    # 获取响应内容
                    result = buffer.getvalue().decode('utf-8')
                    try:
                        proxy_data = json.loads(result)  
                        active_network = proxy_data.get("network", "")
                        self.logger.info(f"代理服务返回的网络状态: {active_network}")                                     
                        
                        network_status = self.config.copy()
                        
                        if active_network == "wifi":
                            self.logger.info("代理服务报告 WiFi 网络活跃")
                            network_status.update({
                                "wifiState": "on",
                                "isWifiConnect": "true",
                                "wifiName": self.get_wifi_name()
                            })
                        elif active_network == "mobile":
                            self.logger.info("代理服务报告移动网络活跃")
                            network_status.update({
                                "mobileDataState": "on",
                                "isInternetConnect": "true",
                                "isWifiConnect": "false",
                                "wifiName": ""

                            })
                        
                        # 添加绑定状态信息
                        bind_status = self.get_bind_status()
                        network_status.update(bind_status)
                        
                        response.error_code = 200
                        response.result = json.dumps(network_status, indent=2)
                        self.logger.info(f"返回网络状态: {response.result[:200]}...")
                        self.logger.info("返回基于代理服务的网络状态")
                        return response
                        
                    except json.JSONDecodeError:
                        self.logger.error(f"解析代理服务响应失败: {result[:200]}")
                        response.error_code = 502
                        response.result = "代理服务返回无效JSON"
                        return response
                        
                except pycurl.error as e:
                    errno, errstr = e.args
                    self.logger.error(f"获取网络状态请求失败: ({errno}) {errstr}")
                    response.error_code = 504
                    response.result = f"代理服务不可达: {errstr}"
                    return response
                    
                finally:
                    curl.close()
                    buffer.close()
            
            # 处理设置网络状态请求 - 使用 pycurl 实现
            elif command == "setNetworkStatus":
                try:
                    self.logger.info("处理jwae_tproxy_mode set网络状态请求")
                    network_status = request_data.get("networkStatus")
                    self.logger.info(f"要设置的网络状态: {network_status}")
                    
                    wifi_state = network_status.get("wifiState")
                    mobile_state = network_status.get("mobileDataState")

                    # 如果某个状态参数缺失，从当前状态中补全
                    if wifi_state is None:
                        wifi_state = self.config.get("wifiState", "off")
                        self.logger.info(f"WiFi状态参数缺失，从当前状态补全: {wifi_state}")

                    if mobile_state is None:
                        mobile_state = self.config.get("mobileDataState", "off")
                        self.logger.info(f"移动数据状态参数缺失，从当前状态补全: {mobile_state}")

                    self.logger.info(f"最终WiFi状态: {wifi_state}, 移动数据状态: {mobile_state}")

                    if (wifi_state, mobile_state) == ("off", "off"):
                        self.logger.error("WiFi和移动数据不能同时为关闭状态")
                        response.error_code = 400
                        # response.result = "Cannot be off at the same time."
                        # 返回JSON格式的错误信息
                        response.result = json.dumps({
                            "error": "Cannot be off at the same time.",
                            "code": 400
                        })
                        self.logger.info(f"返回错误响应: {response.error_code}, {response.result}")
                        return response                    

                    proxy_payload = {
                        "wifi": "enable" if wifi_state == "on" else "disable",
                        "mobile": "enable" if mobile_state == "on" else "disable"
                    }
                    
                    payload_json = json.dumps(proxy_payload)
                    
                    # 准备 pycurl 请求
                    buffer = BytesIO()
                    curl = pycurl.Curl()
                    
                    # 设置请求参数
                    curl.setopt(pycurl.URL, 'http://127.0.0.1:12000/setNetworkStatus')
                    curl.setopt(pycurl.POST, 1)
                    curl.setopt(pycurl.POSTFIELDS, payload_json)
                    curl.setopt(pycurl.HTTPHEADER, ['Content-Type: application/json'])
                    curl.setopt(pycurl.WRITEFUNCTION, buffer.write)
                    curl.setopt(pycurl.CONNECTTIMEOUT, 2)  # 连接超时 2 秒
                    curl.setopt(pycurl.TIMEOUT, 3)         # 总超时 3 秒
                    
                    # 执行请求
                    curl.perform()
                    
                    # 检查响应状态
                    status_code = curl.getinfo(pycurl.RESPONSE_CODE)
                    if status_code != 200:
                        self.logger.error(f"代理服务返回异常状态码: {status_code}")
                        response.error_code = 502
                        # response.result = f"代理服务异常 (HTTP {status_code})"
                        response.result = json.dumps({
                            "error": f"代理服务异常 (HTTP {status_code})",
                            "code": 502
                        })
                        return response
                        
                    # 获取响应内容
                    result = buffer.getvalue().decode('utf-8')
                    self.logger.info(f"代理服务响应: {result}")
                    
                    if result == "success":
                        self.modify_json("wifiState", wifi_state)
                        self.modify_json("mobileDataState", mobile_state)
                        response.error_code = 200
                        # response.result = "通过代理服务更新网络配置成功"
                        # 返回JSON格式的成功响应
                        if wifi_state == "on":
                            response_data = {
                                "wifiState": wifi_state,
                                "wifiName": self.get_wifi_name(),  
                                "mobileDataState": mobile_state
                            }
                        else:
                            response_data = {
                                "wifiState": wifi_state,
                                "mobileDataState": mobile_state
                            }
                        response.result = json.dumps(response_data)
                        self.logger.info(f"更新网络设置成功: {response.result[:200]}...")
                        self.logger.info("通过代理服务更新网络设置成功")
                        return response
                    else:
                        self.logger.error(f"代理服务业务错误: {result}")
                        response.error_code = 502
                        # response.result = f"代理服务失败: {result}"
                        response.result = json.dumps({
                            "error": f"代理服务失败: {result}",
                            "code": 502
                        })
                        return response

                except pycurl.error as e:
                    errno, errstr = e.args
                    self.logger.error(f"设置网络状态请求失败: ({errno}) {errstr}")
                    response.error_code = 504
                    # response.result = f"代理服务不可达: {errstr}"
                    response.result = json.dumps({
                        "error": f"代理服务不可达: {errstr}",
                        "code": 504
                    })                    
                    return response
                    
                finally:
                    curl.close()
                    buffer.close()
                    

        if command == "getNetworkStatus":
            self.logger.info("处理获取网络状态请求")
            # 获取网络状态和绑定状态
            network_status = self.config.copy()
            bind_status = self.get_bind_status()
            
            if network_status.get("wifiName", "") == "CMCC-GUIDE-LINK":
                # 统一使用布尔值或字符串，根据接口需求选择
                # 选项1：布尔值（推荐，Python 内部处理）
                network_status.update({
                    "wifiState": "off",       # 保持字符串类型一致
                    "wifiName": "",
                    "isWifiConnect": False    # 使用 Python 布尔值
                })
            network_status.update(bind_status)
            
            response.error_code = 200
            response.result = json.dumps(network_status, indent=2, ensure_ascii=False)
            self.logger.info(f"返回网络状态(包含绑定状态): {response.result[:100]}...")
            
        elif command == "setNetworkStatus":
            self.logger.info("处理设置网络状态请求")
            network_status = request_data.get("networkStatus")
            self.logger.info(f"要设置的网络状态: {network_status}")
         
            wifi_state = network_status.get("wifiState")
            mobile_data_state = network_status.get("mobileDataState")

            # 如果某个状态参数缺失，从当前状态中补全
            if wifi_state is None:
                wifi_state = self.config.get("wifiState", "off")
                self.logger.info(f"WiFi状态参数缺失，从当前状态补全: {wifi_state}")

            if mobile_data_state is None:
                mobile_data_state = self.config.get("mobileDataState", "off")
                self.logger.info(f"移动数据状态参数缺失，从当前状态补全: {mobile_data_state}")

            self.logger.info(f"最终WiFi状态: {wifi_state}, 移动数据状态: {mobile_data_state}")

            if (wifi_state, mobile_data_state) == ("off", "off"):
                self.logger.error("WiFi和移动数据不能同时为关闭状态")
                response.error_code = 400
                response.result = "Cannot be off at the same time."
                self.logger.info(f"返回错误响应: {response.error_code}, {response.result}")
            else:
                # 检查 WiFi 状态是否需要更新
                current_wifi_state = self.config.get("wifiState")
                self.logger.info(f"当前WiFi状态: {current_wifi_state}, 目标状态: {wifi_state}")
                
                if current_wifi_state != wifi_state:
                    self.logger.info(f"WiFi网卡状态需要从 {current_wifi_state} 更新为 {wifi_state}")
                    if wifi_state == "on":
                        self._enable_wifi_interface()
                    elif wifi_state == "off":
                        self._disable_wifi_interface()

                    self.modify_json("wifiState", wifi_state)
                    self.logger.info(f"WiFi网卡状态已更新为: {wifi_state}")
                else:
                    self.logger.info(f"WiFi网卡状态无需更新，保持为: {wifi_state}")

                # 检查移动网络状态是否需要更新
                current_mobile_state = self.config.get("mobileDataState")
                self.logger.info(f"当前移动数据状态: {current_mobile_state}, 目标状态: {mobile_data_state}")
                
                if current_mobile_state != mobile_data_state:
                    self.logger.info(f"移动网络接口状态需要从 {current_mobile_state} 更新为 {mobile_data_state}")
                    if mobile_data_state == "on":
                        self._enable_mobile_interface()
                    elif mobile_data_state == "off":
                        self._disable_mobile_interface()

                    self.modify_json("mobileDataState", mobile_data_state)
                    self.logger.info(f"移动网络接口状态已更新为: {mobile_data_state}")
                else:
                    self.logger.info(f"移动网络接口状态无需更新，保持为: {mobile_data_state}")

                response.error_code = 200
                if wifi_state == "on":
                    response_data = {
                        "wifiState": wifi_state,
                        "wifiName": self.get_wifi_name(),  
                        "mobileDataState": mobile_data_state
                    }
                else:
                    response_data = {
                        "wifiState": wifi_state,
                        "mobileDataState": mobile_data_state
                    }
                response.result = json.dumps(response_data)
                self.logger.info(f"网络配置更新成功: {response.result[:100]}...") 
                # response.result = "Network configuration updated successfully."
                self.logger.info("网络配置更新成功")
                
        elif command == "getDnsStatus":
            self.logger.info("处理获取DNS状态请求")
            # 获取DNS状态报告
            try:
                dns_report = self.get_dns_status_report()
                self.logger.info(f"成功获取DNS状态报告，当前DNS: {dns_report.get('current_dns')}")
                response.error_code = 200
                response.result = json.dumps(dns_report, indent=2, ensure_ascii=False)
                self.logger.info("DNS状态报告已返回")
            except Exception as e:
                self.logger.error(f"获取DNS状态报告时发生错误: {e}")
                response.error_code = 500
                response.result = f"获取DNS状态报告时发生错误: {str(e)}"
            
        elif command == "switchDnsServer":
            self.logger.info("处理切换DNS服务器请求")
            # 手动切换DNS服务器
            target_dns = request_data.get("dnsServer")
            self.logger.info(f"目标DNS服务器: {target_dns}")
            
            if target_dns:
                try:
                    # 验证DNS服务器是否可用
                    self.logger.info(f"验证DNS服务器 {target_dns} 是否可用")
                    dns_status = self.check_dns_server_status(target_dns)
                    self.logger.info(f"DNS服务器 {target_dns} 状态: {dns_status['overall_status']}")
                    
                    if dns_status["overall_status"] in ["good", "ping_only"]:
                        self.logger.info(f"DNS服务器 {target_dns} 可用，开始切换")
                        self.update_system_dns([target_dns])
                        self.dns_status["current_dns"] = target_dns
                        self.dns_status["failure_count"] = 0
                        response.error_code = 200
                        response.result = f"DNS服务器已切换为: {target_dns}"
                        self.logger.info(f"DNS服务器已成功切换为: {target_dns}")
                    else:
                        self.logger.warning(f"DNS服务器 {target_dns} 不可用: {dns_status['overall_status']}")
                        response.error_code = 400
                        response.result = f"DNS服务器 {target_dns} 不可用: {dns_status['overall_status']}"
                except Exception as e:
                    self.logger.error(f"切换DNS服务器时发生错误: {e}")
                    response.error_code = 500
                    response.result = f"切换DNS服务器时发生错误: {str(e)}"
            else:
                self.logger.error("请求中缺少 dnsServer 参数")
                response.error_code = 400
                response.result = "缺少 dnsServer 参数"
                
        elif command == "refreshDnsServers":
            self.logger.info("处理刷新DNS服务器请求")
            # 重新检测最佳DNS服务器
            try:
                self.logger.info("开始查找最佳DNS服务器")
                best_dns = self.find_best_dns_server()
                self.logger.info(f"找到最佳DNS服务器: {best_dns}，开始更新系统DNS")
                self.update_system_dns([best_dns])
                response.error_code = 200
                response.result = f"已重新检测并设置最佳DNS服务器: {best_dns}"
                self.logger.info(f"系统DNS已更新为: {best_dns}")
            except Exception as e:
                self.logger.error(f"刷新DNS服务器时发生错误: {e}")
                response.error_code = 500
                response.result = f"刷新DNS服务器时发生错误: {str(e)}"
                
        elif command == "getNetworkDiagnostic":
            self.logger.info("处理获取网络诊断信息请求")
            # 获取网络诊断信息
            interface = request_data.get("interface", self.wifi_interface)
            self.logger.info(f"要诊断的网络接口: {interface}")
            
            try:
                self.logger.info(f"开始获取接口 {interface} 的网络诊断信息")
                diagnostic_info = self.get_network_diagnostic_info(interface)
                self.logger.info(f"成功获取接口 {interface} 的诊断信息，状态: {diagnostic_info.get('interface_status')}")
                response.error_code = 200
                response.result = json.dumps(diagnostic_info, indent=2, ensure_ascii=False)
                self.logger.info("网络诊断信息已返回")
            except Exception as e:
                self.logger.error(f"获取接口 {interface} 的网络诊断信息时发生错误: {e}")
                response.error_code = 500
                response.result = f"获取网络诊断信息时发生错误: {str(e)}"
                
        elif command == "performDnsHealthCheck":
            self.logger.info("处理执行DNS健康检查请求")
            # 手动执行DNS健康检查和修复
            interface = request_data.get("interface", self.wifi_interface)
            self.logger.info(f"要执行DNS健康检查的接口: {interface}")
            
            try:
                self.logger.info(f"开始对接口 {interface} 执行DNS健康检查...")
                dns_fixed = self._perform_dns_health_check_and_fix(interface, 3.0)
                
                if dns_fixed:
                    current_dns = self.dns_status.get('current_dns')
                    self.logger.info(f"DNS健康检查成功修复问题，当前DNS: {current_dns}")
                    response.error_code = 200
                    response.result = f"DNS健康检查完成，当前DNS: {current_dns}"
                else:
                    self.logger.warning("DNS健康检查未能修复网络问题")
                    response.error_code = 500
                    response.result = "DNS健康检查未能修复网络问题"
            except Exception as e:
                self.logger.error(f"执行DNS健康检查时发生错误: {e}")
                response.error_code = 500
                response.result = f"执行DNS健康检查时发生错误: {str(e)}"

        elif command == "scanWifiNetworks":
            self.logger.info("处理实时响应WiFi扫描请求")
            try:
                scan_result = self.scan_wifi_networks_async()

                if scan_result["success"]:
                    # 处理网络数据中的特殊字符，确保正确序列化
                    networks = scan_result["networks"]
                    for network in networks:
                        # 处理所有可能包含转义字符的字段
                        for field in ["bssid", "channel", "security", "rate"]:
                            if field in network and isinstance(network[field], str):
                                network[field] = network[field].replace("\\", "")
                    
                    response.error_code = 200
                    response.result = json.dumps({
                        "status": scan_result["status"],
                        "code": scan_result.get("code", 200),
                        "networks": networks,
                        "count": scan_result["count"],
                        "cached": scan_result.get("cached", False),
                        "scan_time": scan_result.get("scan_time", 0),
                        "message": scan_result.get("message", "")
                    }, indent=2, ensure_ascii=False)

                    status_msg = scan_result["status"]
                    if status_msg == "completed":
                        cache_info = "（使用缓存）" if scan_result.get("cached", False) else "（扫描完成）"
                        self.logger.info(f"返回 {scan_result['count']} 个WiFi网络{cache_info}")
                    elif status_msg == "scanning":
                        self.logger.info("WiFi扫描正在进行中，返回扫描中状态")
                else:
                    response.error_code = 500
                    response.result = json.dumps({
                        "status": "error",
                        "code": 500,  # 错误状态码
                        "error": scan_result.get("error", "未知错误"),
                        "networks": [],
                        "count": 0,
                        "message": scan_result.get("message", "扫描失败")
                    }, indent=2, ensure_ascii=False)
                    self.logger.error(f"WiFi网络扫描失败: {scan_result.get('error', '未知错误')}")

            except Exception as e:
                self.logger.error(f"处理WiFi网络扫描请求时发生错误: {e}")
                response.error_code = 500
                response.result = json.dumps({
                    "status": "error",
                    "code": 500,  # 服务器错误状态码
                    "error": f"处理扫描请求时发生错误: {str(e)}",
                    "networks": [],
                    "count": 0,
                    "message": "服务器内部错误"
                }, indent=2, ensure_ascii=False)

        elif command == "connectWifi":
            self.logger.info("处理连接WiFi网络请求")
            ssid = request_data.get("ssid")
            password = request_data.get("password")

            if not ssid:
                self.logger.error("连接WiFi请求缺少SSID参数")
                response.error_code = 400
                response.result = "缺少SSID参数"
            else:
                try:
                    self.logger.info(f"尝试连接WiFi网络: {ssid}")
                    connect_result = self.connect_wifi_network(ssid, password)
                    response.error_code = connect_result["error_code"]
                    response.result = json.dumps(connect_result, indent=2, ensure_ascii=False)
                    self.logger.info(f"WiFi连接结果: {connect_result['message']}")
                except Exception as e:
                    self.logger.error(f"连接WiFi网络时发生错误: {e}")
                    response.error_code = 500
                    response.result = f"连接WiFi网络时发生错误: {str(e)}"

        elif command == "disconnectWifi":
            self.logger.info("处理断开WiFi连接请求")
            try:
                disconnect_result = self.disconnect_wifi()
                response.error_code = disconnect_result["error_code"]
                response.result = json.dumps(disconnect_result, indent=2, ensure_ascii=False)
                self.logger.info(f"WiFi断开结果: {disconnect_result['message']}")
            except Exception as e:
                self.logger.error(f"断开WiFi连接时发生错误: {e}")
                response.error_code = 500
                response.result = f"断开WiFi连接时发生错误: {str(e)}"

        elif command == "getWifiConnectionStatus":
            self.logger.info("处理获取WiFi连接状态请求")
            try:
                wifi_status = self.get_wifi_connection_status()
                response.error_code = 200
                response.result = json.dumps(wifi_status, indent=2, ensure_ascii=False)
                self.logger.info(f"WiFi连接状态: 接口={wifi_status.get('interface')}, 连接={wifi_status.get('current_ssid')}")
            except Exception as e:
                self.logger.error(f"获取WiFi连接状态时发生错误: {e}")
                response.error_code = 500
                response.result = f"获取WiFi连接状态时发生错误: {str(e)}"

        elif command == "getWifiHardwareStatus":
            self.logger.info("处理获取WiFi硬件状态请求")
            try:
                hardware_status = self.get_wifi_hardware_status()
                response.error_code = 200
                response.result = json.dumps(hardware_status, indent=2, ensure_ascii=False)
                self.logger.info(f"WiFi硬件状态: 接口={hardware_status.get('interface')}, 驱动={hardware_status.get('driver')}")
            except Exception as e:
                self.logger.error(f"获取WiFi硬件状态时发生错误: {e}")
                response.error_code = 500
                response.result = f"获取WiFi硬件状态时发生错误: {str(e)}"

        elif command == "getWifiScanStatus":
            self.logger.info("处理获取WiFi扫描状态请求")
            try:
                scan_status = self.get_wifi_scan_status()
                response.error_code = 200
                response.result = json.dumps(scan_status, indent=2, ensure_ascii=False)
                self.logger.info(f"WiFi扫描状态: 正在扫描={scan_status.get('scan_in_progress')}, 缓存有效={scan_status.get('cache_valid')}")
            except Exception as e:
                self.logger.error(f"获取WiFi扫描状态时发生错误: {e}")
                response.error_code = 500
                response.result = f"获取WiFi扫描状态时发生错误: {str(e)}"

        elif command == "clearWifiScanCache":
            self.logger.info("处理清除WiFi扫描缓存请求")
            try:
                self.clear_wifi_scan_cache()
                response.error_code = 200
                response.result = json.dumps({"message": "WiFi扫描缓存已清除"}, indent=2, ensure_ascii=False)
                self.logger.info("WiFi扫描缓存清除成功")
            except Exception as e:
                self.logger.error(f"清除WiFi扫描缓存时发生错误: {e}")
                response.error_code = 500
                response.result = f"清除WiFi扫描缓存时发生错误: {str(e)}"

        elif command == "checkWpaSupplicantStatus":
            self.logger.info("处理检查wpa_supplicant状态请求")
            try:
                wpa_status = self.check_wpa_supplicant_status()
                response.error_code = 200
                response.result = json.dumps(wpa_status, indent=2, ensure_ascii=False)
                self.logger.info(f"wpa_supplicant状态: 健康={wpa_status.get('is_healthy')}, 进程数={wpa_status.get('process_count')}")
            except Exception as e:
                self.logger.error(f"检查wpa_supplicant状态时发生错误: {e}")
                response.error_code = 500
                response.result = f"检查wpa_supplicant状态时发生错误: {str(e)}"

        elif command == "restartWpaSupplicant":
            self.logger.info("处理重启wpa_supplicant请求")
            try:
                restart_result = self.restart_wpa_supplicant()
                if restart_result["success"]:
                    response.error_code = 200
                    response.result = json.dumps(restart_result, indent=2, ensure_ascii=False)
                    self.logger.info("wpa_supplicant重启成功")
                else:
                    response.error_code = 500
                    response.result = json.dumps(restart_result, indent=2, ensure_ascii=False)
                    self.logger.error(f"wpa_supplicant重启失败: {restart_result['message']}")
            except Exception as e:
                self.logger.error(f"重启wpa_supplicant时发生错误: {e}")
                response.error_code = 500
                response.result = f"重启wpa_supplicant时发生错误: {str(e)}"

        else:
            if command == "getBindStatus":
                self.logger.info("处理获取绑定状态请求")
                bind_status = self.get_bind_status()
                response.error_code = 200
                response.result = json.dumps(bind_status, indent=2, ensure_ascii=False)
                self.logger.info(f"返回绑定状态: {response.result}")
            else:
                self.logger.warning(f"未知命令: {command}")
                response.error_code = 400
                response.result = "Unknown command."
                self.logger.info(f"返回错误响应: {response.error_code}, {response.result}")

        # 记录最终响应信息
        self.logger.info(f"网络状态请求处理完成，响应码: {response.error_code}")
        if len(str(response.result)) > 200:
            self.logger.debug(f"响应结果: {str(response.result)[:200]}...")
        else:
            self.logger.debug(f"响应结果: {response.result}")
            
        return response

    def run_ap_start(self):
        
        self.run_command(f"nmcli dev set {self.ap_interface} managed no")
        # self.run_command(f"nmcli dev set {self.wifi_interface} managed no")
        self.run_command(f"iw dev {self.ap_interface} set type __ap")

        # 检查并解除 hostapd 服务的屏蔽状态
        self.logger.info("检查 hostapd 服务是否被屏蔽...")
        hostapd_masked = self.run_command("systemctl is-enabled hostapd") == "masked"
        if hostapd_masked:
            self.logger.warn("hostapd 服务被屏蔽，尝试解除屏蔽...")
            self.run_command("sudo systemctl unmask hostapd")
            self.logger.info("hostapd 服务已解除屏蔽。")

        self.enable_ip_forwarding()
        self.clear_nat_rules()
        self.add_iptables_rule("***********/24", "*", exclude_dest="***********/24")
        self.add_iptables_rule("***********/24", "*", exclude_dest="***********/24")
        
        self.update_ap_name()

        # 添加 iptables 规则
        # self.add_iptables_rule("***********/24", "wlan0")
        # self.add_iptables_rule("***********/24", "wlan0")

        # 启用 IP 转发

        self.logger.info("AP启动完成")

    def run_ap_stop(self):
        self.run_command(f"nmcli dev set {self.ap_interface} managed no")
        self.run_command(f"iw dev {self.ap_interface} set type __ap")

        self.logger.info("检查 hostapd 服务是否被屏蔽...")
        hostapd_masked = self.run_command("systemctl is-enabled hostapd") == "masked"
        if hostapd_masked:
            self.logger.warn("hostapd 服务被屏蔽，尝试解除屏蔽...")
            self.run_command("sudo systemctl unmask hostapd")
            self.logger.info("hostapd 服务已解除屏蔽。")

        self.run_command("sudo systemctl stop hostapd")
        self.run_command(f"sudo ifconfig {self.ap_interface} down")

        self.enable_ip_forwarding()
        self.clear_nat_rules()
        self.add_iptables_rule("***********/24", "*", exclude_dest="***********/24")
        self.logger.info("AP已关闭")

    def check_hostapd_status(slef):
        try:
            result = subprocess.run(['sudo', 'systemctl', 'status', 'hostapd'],
                capture_output=True, text=True, check=True)
            output = result.stdout
 
            for line in output.split('\n'):
                if 'Active:' in line:
                    if 'active (running)' in line:
                        return 'active'
                    elif 'inactive (dead)' in line:
                        return 'inactive'
            return 'unknown'
        except subprocess.CalledProcessError as e:
            print(f"Error checking hostapd status: {e}")
            return 'error'
    def update_ap_name(self):
        """获取当前 WiFi 频段并更新 AP 名称"""
        freq = self.get_wireless_freq(self.ap_interface)
        if freq:
            if freq != self.last_freq:  # 仅在频段变化时更新
                self.configure_hostapd(freq)
                # self.run_command(f"sudo ifconfig {self.wifi_interface} down")
                self.run_command("sudo systemctl stop hostapd")
                self.run_command(f"sudo ifconfig {self.ap_interface} down")
                self.run_command(f"sudo ifconfig {self.ap_interface} up")
                self.run_command("sudo systemctl start hostapd")
                self.run_command("sudo systemctl stop hostapd")
                self.run_command(f"sudo ifconfig {self.ap_interface} down")
                self.run_command(f"sudo ifconfig {self.ap_interface} up")
                self.run_command("sudo systemctl start hostapd")
                # self.run_command("sudo systemctl restart dnsmasq")
                self.last_freq = freq  # 更新记录的频段

                self.logger.info(f"为接口 {self.ap_interface} 设置静态 IP 地址 {self.static_ip}")
                # 确保移除已存在的 IP 地址
                self.run_command(f"sudo ip addr flush dev {self.ap_interface}")
                # 设置静态 IP 地址
                result = self.run_command(f"sudo ip addr add {self.static_ip}/24 dev {self.ap_interface}")
                if result is not None:
                    self.logger.info(f"成功为接口 {self.ap_interface} 设置静态 IP 地址 {self.static_ip}")
                else:
                    self.logger.error(f"为接口 {self.ap_interface} 设置静态 IP 地址失败，请检查网络配置。")

                # self.run_command(f"sudo ifconfig {self.wifi_interface} up")
                # self.run_command(f"sudo nmcli dev connect {self.wifi_interface}")
            else:
                self.logger.info(f"频段未变化 ({freq})，无需更新 AP 名称")
                status = self.check_hostapd_status()
                if status == "inactive":
                    self.logger.info(f"hostapd is inactive, restart it")
                    self.run_command("sudo systemctl stop hostapd")
                    self.run_command(f"sudo ifconfig {self.ap_interface} down")
                    self.run_command(f"sudo ifconfig {self.ap_interface} up")
                    self.run_command("sudo systemctl start hostapd")
                    self.run_command("sudo systemctl stop hostapd")
                    self.run_command(f"sudo ifconfig {self.ap_interface} down")
                    self.run_command(f"sudo ifconfig {self.ap_interface} up")
                    self.run_command("sudo systemctl start hostapd")

        else:
            self.logger.warning("无法获取频段信息，AP 名称未更新")

    def enable_ip_forwarding(self):
        """启用 IP 转发功能"""
        self.logger.info("启用 IP 转发功能...")
        result = self.run_command("sudo sysctl -w net.ipv4.ip_forward=1")
        if result is not None:
            self.logger.info("IP 转发功能已启用。")
        else:
            self.logger.error("启用 IP 转发功能失败，请检查系统配置。")

    def get_wireless_freq(self, interface):
        """获取无线接口的频段 (2.4GHz 或 5GHz)"""
        try:
            freq = self.run_command(f"iw dev {interface} info | grep -oP '(?<=channel\\s)\\d+'")
            if freq:
                freq = int(freq)
                if 1 <= freq <= 14:
                    band = "2.4GHz"
                elif 36 <= freq <= 165:
                    band = "5GHz"
                else:
                    freq = 6
                self.logger.info(f"接口 {interface} 的无线频段为: {band}")
            else:
                freq = 6
                self.logger.error(f"未能获取接口 {interface} 的频段信息")
        except Exception as e:
            self.logger.error(f"获取接口 {interface} 的频段时发生错误: {e}")
            freq = 6
        return freq

    def configure_hostapd(self, freq=None):
        """配置 hostapd"""
        hostapd_conf_path = "/etc/hostapd/hostapd.conf"
        self.logger.info(f"配置 hostapd 文件: {hostapd_conf_path}")

        if not freq:
            # 如果未提供新的 SSID 或频段，则获取当前频段并生成默认 SSID
            freq = self.get_wireless_freq(self.ap_interface)
        if 1 <= freq <= 14:
            band = "2.4GHz"
            hw_mode = "g"
        elif 36 <= freq <= 165:
            band = "5GHz"
            hw_mode = "a"
        else:
            band = "2.4GHz"
            hw_mode = "g"
        self.logger.info(f"接口 {self.ap_interface} 的无线频段为: {band}")

        channel = freq
        hostapd_config = (
            f"interface={self.ap_interface}\n"
            f"driver=nl80211\n"
            f"ssid={self.ssid}_{band}\n"
            f"hw_mode={hw_mode}\n"
            f"channel={channel}\n"
            f"country_code=CN\n"
            f"ieee80211d=1\n"
            f"ieee80211n=1\n"
            f"ieee80211ac=1\n"
            f"wpa=2\n"
            f"wpa_key_mgmt=WPA-PSK\n"
            f"wpa_passphrase=12345678\n"
            f"rsn_pairwise=CCMP\n"
            # f"max_num_sta=8\n"
            # f"wmm_enabled=1\n"
            # f"auth_algs=1\n"
            f"ignore_broadcast_ssid=0\n"
        )
        try:
            with open(hostapd_conf_path, "w") as conf_file:
                conf_file.write(hostapd_config)
            self.logger.info(f"hostapd 配置文件已更新，SSID: {self.ssid}_{band}, 频段: {band}, 模式: {hw_mode}, 信道: {channel}")
        except Exception as e:
            self.logger.error(f"更新 hostapd 配置文件时发生错误: {e}")

    def clear_nat_rules(self):
        """清除所有 NAT 规则"""
        self.logger.info("清除所有 NAT 规则...")
        result = self.run_command("sudo iptables -t nat -F")
        if result is not None:
            self.logger.info("所有 NAT 规则已成功清除。")
        else:
            self.logger.error("清除 NAT 规则失败，请检查系统配置。")

    def add_iptables_rule(self, source, output_interface, exclude_dest=None):
        """添加 iptables 规则"""
        if exclude_dest:
            rule = f"-s {source} ! -d {exclude_dest} -j MASQUERADE"
        else:
            rule = f"-s {source} -o {output_interface} -j MASQUERADE"

        self.logger.info(f"添加 iptables 规则: {rule}")

        # 添加新规则
        self.run_command(f"sudo iptables -t nat -A POSTROUTING {rule}")
        self.logger.info(f"成功添加 iptables 规则: {rule}")

    def get_bind_status(self):
        """从/etc/cmcc_robot/andlinkSdk.conf获取绑定状态
        文件格式为INI类似格式，包含 userBind=1 这样的键值对
        """
        try:
            self.logger.info("尝试读取绑定状态...")
            config_file = "/etc/cmcc_robot/andlinkSdk.conf"
            
            if not os.path.exists(config_file):
                self.logger.warning(f"配置文件不存在: {config_file}")
                return {"userBind": "false"}
            
            # 读取文件内容
            try:
                with open(config_file, 'r') as f:
                    content = f.read()
                self.logger.debug(f"读取到的配置文件内容: '{content}'")
                # 移除可能的BOM和隐藏字符
                content = content.strip().replace('\ufeff', '')
                self.logger.debug(f"处理后的内容: '{content}'")
            except Exception as e:
                self.logger.error(f"读取配置文件失败: {e}")
                return {"userBind": "false"}
            
            # 尝试多种方式解析
            # 1. 首先尝试按行解析INI格式
            try:
                lines = content.splitlines()
                self.logger.info(f"分行后的内容: {lines}")
                for line in lines:
                    line = line.strip()
                    self.logger.info(f"处理行: '{line}'")
                    if not line or line.startswith('#'):
                        self.logger.debug(f"跳过空行或注释行: '{line}'")
                        continue
                        
                    if line.startswith('userBind='):
                        self.logger.info(f"找到userBind行: '{line}'")
                        parts = line.split('=', 1)
                        self.logger.info(f"拆分结果: {parts}")
                        if len(parts) == 2:
                            value = parts[1].strip()
                            self.logger.info(f"提取的值: '{value}'")
                            # 只有值为"1"时才是"true"，其他所有情况都是"false"
                            user_bind = "true" if value == "1" else "false"
                            self.logger.info(f"成功读取绑定状态(INI格式): 原始值='{value}', userBind={user_bind}")
                            return {"userBind": user_bind}
            except Exception as e:
                self.logger.warning(f"按INI格式解析失败: {e}")
            
            # 2. 尝试作为JSON解析
            try:
                config = json.loads(content)
                if "userBind" in config:
                    value = str(config["userBind"])
                    user_bind = "true" if value == "1" else "false"
                    self.logger.info(f"成功读取绑定状态(JSON格式): 原始值={value}, userBind={user_bind}")
                    return {"userBind": user_bind}
            except json.JSONDecodeError:
                self.logger.debug("配置文件不是JSON格式")
            except Exception as e:
                self.logger.warning(f"按JSON格式解析失败: {e}")
            
            # 3. 尝试正则表达式匹配
            try:
                import re
                match = re.search(r'userBind\s*=\s*(\S+)', content)
                if match:
                    value = match.group(1).strip()
                    user_bind = "true" if value == "1" else "false"
                    self.logger.info(f"成功读取绑定状态(正则匹配): 原始值={value}, userBind={user_bind}")
                    return {"userBind": user_bind}
            except Exception as e:
                self.logger.warning(f"正则表达式匹配失败: {e}")
            
            # 如果所有方法都失败了
            self.logger.warning("无法从配置文件中提取userBind值")
            return {"userBind": "false"}
                
        except Exception as e:
            self.logger.error(f"读取绑定状态时发生错误: {e}")
            return {"userBind": "false"}

    def _enable_wifi_interface(self):
        """
        启用WiFi网卡 - 使用sudo ip link统一控制
        """
        self.logger.info("开始启用WiFi网卡...")

        # 使用sudo ip link启用接口
        result = self.run_command(f"sudo ip link set {self.wifi_interface} up")
        if result is not None:
            self.logger.info("WiFi接口启用命令执行成功")

            # 等待接口状态生效
            time.sleep(5)

            if self.device_type != "unitree":
                result = self.run_command(f"sudo nmcli dev connect {self.wifi_interface}")
                if result is not None:
                    self.logger.info("WiFi重连命令执行成功")


            # 验证状态
            if self.check_wifi_switch_status():
                self.logger.info("WiFi网卡启用成功并验证通过")
                return True
            else:
                self.logger.warning("WiFi网卡启用后验证失败")
                return False
        else:
            self.logger.error("WiFi接口启用命令执行失败")
            return False

    def _disable_wifi_interface(self):
        """
        禁用WiFi网卡 - 使用sudo ip link统一控制
        """
        self.logger.info("开始禁用WiFi网卡...")

        # 使用sudo ip link禁用接口
        result = self.run_command(f"sudo ip link set {self.wifi_interface} down")
        if result is not None:
            self.logger.info("WiFi接口禁用命令执行成功")

            # 等待接口状态生效
            time.sleep(2)

            # 验证状态
            if not self.check_wifi_switch_status():
                self.logger.info("WiFi网卡禁用成功并验证通过")
                return True
            else:
                self.logger.warning("WiFi网卡禁用后验证失败")
                return False
        else:
            self.logger.error("WiFi接口禁用命令执行失败")
            return False

    def _enable_mobile_interface(self):
        """
        启用移动网络接口 - 使用sudo ip link统一控制
        """
        self.logger.info(f"开始启用移动网络接口: {self.mobile_interface}")

        # 使用sudo ip link启用接口
        result = self.run_command(f"sudo ip link set {self.mobile_interface} up")
        if result is not None:
            self.logger.info("移动网络接口启用命令执行成功")

            # 等待接口状态生效
            time.sleep(2)

            # 验证状态
            if self.check_mobile_switch_status():
                self.logger.info("移动网络接口启用成功并验证通过")
                return True
            else:
                self.logger.warning("移动网络接口启用后验证失败")
                return False
        else:
            self.logger.error("移动网络接口启用命令执行失败")
            return False

    def _disable_mobile_interface(self):
        """
        禁用移动网络接口 - 使用sudo ip link统一控制
        """
        self.logger.info(f"开始禁用移动网络接口: {self.mobile_interface}")

        # 使用sudo ip link禁用接口
        result = self.run_command(f"sudo ip link set {self.mobile_interface} down")
        if result is not None:
            self.logger.info("移动网络接口禁用命令执行成功")

            # 等待接口状态生效
            time.sleep(2)

            # 验证状态
            if not self.check_mobile_switch_status():
                self.logger.info("移动网络接口禁用成功并验证通过")
                return True
            else:
                self.logger.warning("移动网络接口禁用后验证失败")
                return False
        else:
            self.logger.error("移动网络接口禁用命令执行失败")
            return False

    def cleanup(self):
        """
        清理NetworkManager资源，包括关闭线程池
        """
        self.logger.info("开始清理NetworkManager资源...")

        try:
            # 关闭WiFi扫描线程池
            if hasattr(self, 'wifi_scan_executor'):
                self.logger.info("关闭WiFi扫描线程池...")
                self.wifi_scan_executor.shutdown(wait=True)
                self.logger.info("WiFi扫描线程池已关闭")
        except Exception as e:
            self.logger.error(f"关闭WiFi扫描线程池时发生错误: {e}")

        self.logger.info("NetworkManager资源清理完成")

    def __del__(self):
        """
        析构函数，确保资源被正确清理
        """
        try:
            self.cleanup()
        except Exception:
            pass  # 析构函数中不应该抛出异常
