#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Network节点异常处理器
专门处理WiFi连接、网络接口等异常情况和恢复策略
"""

import time
import subprocess
import threading
import socket
from typing import Dict, Any, Optional
import os

# 使用相对导入，如果失败则使用绝对导入
try:
    from ..ble.ble.exception_manager import ExceptionManager, ExceptionType, ExceptionSeverity, ExceptionRecord
except ImportError:
    try:
        from ble.ble.exception_manager import ExceptionManager, ExceptionType, ExceptionSeverity, ExceptionRecord
    except ImportError:
        import sys
        sys.path.append('/mine/worktrees/unitree-debug/xiaoli_application_ros2/src')
        from ble.ble.exception_manager import ExceptionManager, ExceptionType, ExceptionSeverity, ExceptionRecord


class NetworkExceptionHandler:
    """Network异常处理器"""
    
    def __init__(self, network_node, exception_manager: ExceptionManager):
        self.network_node = network_node
        self.exception_manager = exception_manager
        self.logger = network_node.get_logger()
        
        # 网络健康检查配置
        self.network_health_check_interval = 60  # 60秒检查一次
        self.wifi_check_timeout = 10  # WiFi连接检查超时
        self.connectivity_check_timeout = 5  # 网络连通性检查超时
        
        # WiFi重连配置
        self.max_wifi_reconnect_attempts = 3
        self.wifi_reconnect_delay = 10  # WiFi重连延迟（秒）
        
        # 网络接口重启配置
        self.interface_restart_delay = 5
        
        # 连续失败计数
        self.wifi_failure_count = 0
        self.connectivity_failure_count = 0
        
        # 注册异常处理器
        self._register_handlers()
        self._register_recovery_strategies()
        
        # 启动健康检查
        self.health_check_thread = threading.Thread(target=self._health_check_loop, daemon=True)
        self.health_check_thread.start()
        
        self.logger.info("Network异常处理器已初始化")

    def _register_handlers(self):
        """注册异常处理器"""
        self.exception_manager.register_handler(
            ExceptionType.WIFI_CONNECTION_ERROR, 
            self._handle_wifi_connection_error
        )
        self.exception_manager.register_handler(
            ExceptionType.WIFI_SCAN_ERROR, 
            self._handle_wifi_scan_error
        )
        self.exception_manager.register_handler(
            ExceptionType.WIFI_AUTHENTICATION_ERROR, 
            self._handle_wifi_auth_error
        )
        self.exception_manager.register_handler(
            ExceptionType.NETWORK_INTERFACE_ERROR, 
            self._handle_interface_error
        )
        self.exception_manager.register_handler(
            ExceptionType.NETWORK_CONNECTIVITY_ERROR, 
            self._handle_connectivity_error
        )
        self.exception_manager.register_handler(
            ExceptionType.DNS_RESOLUTION_ERROR, 
            self._handle_dns_error
        )

    def _register_recovery_strategies(self):
        """注册恢复策略"""
        self.exception_manager.register_recovery_strategy(
            ExceptionType.WIFI_CONNECTION_ERROR, 
            self._recover_wifi_connection
        )
        self.exception_manager.register_recovery_strategy(
            ExceptionType.WIFI_SCAN_ERROR, 
            self._recover_wifi_scan
        )
        self.exception_manager.register_recovery_strategy(
            ExceptionType.WIFI_AUTHENTICATION_ERROR, 
            self._recover_wifi_auth
        )
        self.exception_manager.register_recovery_strategy(
            ExceptionType.NETWORK_INTERFACE_ERROR, 
            self._recover_interface_error
        )
        self.exception_manager.register_recovery_strategy(
            ExceptionType.NETWORK_CONNECTIVITY_ERROR, 
            self._recover_connectivity
        )
        self.exception_manager.register_recovery_strategy(
            ExceptionType.DNS_RESOLUTION_ERROR, 
            self._recover_dns_error
        )

    def report_wifi_connection_error(self, error_message: str, context: Optional[Dict[str, Any]] = None):
        """报告WiFi连接错误"""
        self.wifi_failure_count += 1
        severity = ExceptionSeverity.HIGH if self.wifi_failure_count >= 3 else ExceptionSeverity.MEDIUM
        
        self.exception_manager.report_exception(
            exception_type=ExceptionType.WIFI_CONNECTION_ERROR,
            message=f"WiFi连接失败: {error_message}",
            severity=severity,
            context={
                **(context or {}),
                "failure_count": self.wifi_failure_count
            }
        )

    def report_wifi_scan_error(self, error_message: str, context: Optional[Dict[str, Any]] = None):
        """报告WiFi扫描错误"""
        self.exception_manager.report_exception(
            exception_type=ExceptionType.WIFI_SCAN_ERROR,
            message=f"WiFi扫描失败: {error_message}",
            severity=ExceptionSeverity.MEDIUM,
            context=context
        )

    def report_connectivity_error(self, error_message: str, context: Optional[Dict[str, Any]] = None):
        """报告网络连通性错误"""
        self.connectivity_failure_count += 1
        severity = ExceptionSeverity.HIGH if self.connectivity_failure_count >= 5 else ExceptionSeverity.MEDIUM
        
        self.exception_manager.report_exception(
            exception_type=ExceptionType.NETWORK_CONNECTIVITY_ERROR,
            message=f"网络连通性异常: {error_message}",
            severity=severity,
            context={
                **(context or {}),
                "failure_count": self.connectivity_failure_count
            }
        )

    def report_successful_wifi_connection(self):
        """报告WiFi连接成功"""
        self.wifi_failure_count = 0

    def report_successful_connectivity(self):
        """报告网络连通性正常"""
        self.connectivity_failure_count = 0

    def _handle_wifi_connection_error(self, record: ExceptionRecord):
        """处理WiFi连接错误"""
        self.logger.warning(f"处理WiFi连接错误: {record.message}")
        
        # 检查WiFi接口状态
        if hasattr(self.network_node, 'network_manager'):
            try:
                wifi_interface = getattr(self.network_node.network_manager, 'wifi_interface', 'wlan0')
                status = self._check_interface_status(wifi_interface)
                self.logger.info(f"WiFi接口 {wifi_interface} 状态: {status}")
            except Exception as e:
                self.logger.error(f"检查WiFi接口状态失败: {e}")

    def _handle_wifi_scan_error(self, record: ExceptionRecord):
        """处理WiFi扫描错误"""
        self.logger.warning(f"处理WiFi扫描错误: {record.message}")

    def _handle_wifi_auth_error(self, record: ExceptionRecord):
        """处理WiFi认证错误"""
        self.logger.warning(f"处理WiFi认证错误: {record.message}")

    def _handle_interface_error(self, record: ExceptionRecord):
        """处理网络接口错误"""
        self.logger.error(f"处理网络接口错误: {record.message}")

    def _handle_connectivity_error(self, record: ExceptionRecord):
        """处理网络连通性错误"""
        self.logger.warning(f"处理网络连通性错误: {record.message}")

    def _handle_dns_error(self, record: ExceptionRecord):
        """处理DNS错误"""
        self.logger.warning(f"处理DNS错误: {record.message}")

    def _recover_wifi_connection(self, record: ExceptionRecord) -> bool:
        """恢复WiFi连接"""
        try:
            self.logger.info("尝试恢复WiFi连接...")
            
            if not hasattr(self.network_node, 'network_manager'):
                self.logger.error("Network Manager未初始化")
                return False
            
            network_manager = self.network_node.network_manager
            wifi_interface = getattr(network_manager, 'wifi_interface', 'wlan0')
            
            # 步骤1: 重启WiFi接口
            if self._restart_wifi_interface(wifi_interface):
                time.sleep(self.interface_restart_delay)
                
                # 步骤2: 尝试自动连接
                if hasattr(network_manager, 'auto_scan_and_connect_wifi'):
                    network_manager.auto_scan_and_connect_wifi()
                    
                    # 步骤3: 验证连接
                    time.sleep(10)  # 等待连接建立
                    if self._verify_wifi_connection(wifi_interface):
                        self.logger.info("WiFi连接恢复成功")
                        self.report_successful_wifi_connection()
                        return True
            
            # 步骤4: 如果自动连接失败，尝试手动重连已保存的网络
            if self._reconnect_saved_networks():
                self.logger.info("WiFi连接恢复成功（手动重连）")
                self.report_successful_wifi_connection()
                return True
            
            self.logger.warning("WiFi连接恢复失败")
            return False
            
        except Exception as e:
            self.logger.error(f"恢复WiFi连接时发生异常: {e}")
            return False

    def _recover_wifi_scan(self, record: ExceptionRecord) -> bool:
        """恢复WiFi扫描功能"""
        try:
            self.logger.info("尝试恢复WiFi扫描功能...")
            
            wifi_interface = getattr(self.network_node.network_manager, 'wifi_interface', 'wlan0')
            
            # 重启WiFi接口
            if self._restart_wifi_interface(wifi_interface):
                time.sleep(5)
                
                # 测试扫描功能
                if self._test_wifi_scan(wifi_interface):
                    self.logger.info("WiFi扫描功能恢复成功")
                    return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"恢复WiFi扫描功能时发生异常: {e}")
            return False

    def _recover_wifi_auth(self, record: ExceptionRecord) -> bool:
        """恢复WiFi认证"""
        try:
            self.logger.info("尝试恢复WiFi认证...")
            
            # 重启wpa_supplicant服务
            if self._restart_wpa_supplicant():
                time.sleep(5)
                
                # 重新加载配置
                subprocess.run(['sudo', 'wpa_cli', 'reconfigure'], 
                             check=False, capture_output=True)
                
                self.logger.info("WiFi认证恢复完成")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"恢复WiFi认证时发生异常: {e}")
            return False

    def _recover_interface_error(self, record: ExceptionRecord) -> bool:
        """恢复网络接口错误"""
        try:
            self.logger.info("尝试恢复网络接口...")
            
            # 重启网络服务
            if self._restart_network_services():
                time.sleep(10)
                
                # 验证接口状态
                wifi_interface = getattr(self.network_node.network_manager, 'wifi_interface', 'wlan0')
                mobile_interface = getattr(self.network_node.network_manager, 'mobile_interface', 'eth0')
                
                wifi_ok = self._check_interface_status(wifi_interface)
                mobile_ok = self._check_interface_status(mobile_interface)
                
                if wifi_ok or mobile_ok:
                    self.logger.info("网络接口恢复成功")
                    return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"恢复网络接口时发生异常: {e}")
            return False

    def _recover_connectivity(self, record: ExceptionRecord) -> bool:
        """恢复网络连通性"""
        try:
            self.logger.info("尝试恢复网络连通性...")
            
            # 步骤1: 检查并重启DNS服务
            if self._restart_dns_service():
                time.sleep(3)
                
                # 步骤2: 测试连通性
                if self._test_internet_connectivity():
                    self.logger.info("网络连通性恢复成功")
                    self.report_successful_connectivity()
                    return True
            
            # 步骤3: 如果DNS重启无效，尝试重启网络接口
            wifi_interface = getattr(self.network_node.network_manager, 'wifi_interface', 'wlan0')
            if self._restart_wifi_interface(wifi_interface):
                time.sleep(10)
                
                if self._test_internet_connectivity():
                    self.logger.info("网络连通性恢复成功（重启接口）")
                    self.report_successful_connectivity()
                    return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"恢复网络连通性时发生异常: {e}")
            return False

    def _recover_dns_error(self, record: ExceptionRecord) -> bool:
        """恢复DNS错误"""
        try:
            self.logger.info("尝试恢复DNS解析...")
            
            # 重启DNS服务
            if self._restart_dns_service():
                time.sleep(3)
                
                # 测试DNS解析
                if self._test_dns_resolution():
                    self.logger.info("DNS解析恢复成功")
                    return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"恢复DNS解析时发生异常: {e}")
            return False

    def _restart_wifi_interface(self, interface: str) -> bool:
        """重启WiFi接口"""
        try:
            self.logger.info(f"重启WiFi接口: {interface}")
            
            # 禁用接口
            subprocess.run(['sudo', 'ip', 'link', 'set', interface, 'down'], 
                         check=False, capture_output=True)
            time.sleep(2)
            
            # 启用接口
            result = subprocess.run(['sudo', 'ip', 'link', 'set', interface, 'up'], 
                                  check=False, capture_output=True)
            
            return result.returncode == 0
            
        except Exception as e:
            self.logger.error(f"重启WiFi接口失败: {e}")
            return False

    def _restart_wpa_supplicant(self) -> bool:
        """重启wpa_supplicant服务"""
        try:
            self.logger.info("重启wpa_supplicant服务...")
            
            # 停止服务
            subprocess.run(['sudo', 'systemctl', 'stop', 'wpa_supplicant'], 
                         check=False, capture_output=True)
            time.sleep(2)
            
            # 启动服务
            result = subprocess.run(['sudo', 'systemctl', 'start', 'wpa_supplicant'], 
                                  check=False, capture_output=True)
            
            return result.returncode == 0
            
        except Exception as e:
            self.logger.error(f"重启wpa_supplicant服务失败: {e}")
            return False

    def _restart_network_services(self) -> bool:
        """重启网络服务"""
        try:
            self.logger.info("重启网络服务...")
            
            services = ['systemd-networkd', 'NetworkManager']
            
            for service in services:
                try:
                    subprocess.run(['sudo', 'systemctl', 'restart', service], 
                                 check=False, capture_output=True, timeout=30)
                    self.logger.info(f"重启服务成功: {service}")
                    return True
                except:
                    continue
            
            return False
            
        except Exception as e:
            self.logger.error(f"重启网络服务失败: {e}")
            return False

    def _restart_dns_service(self) -> bool:
        """重启DNS服务"""
        try:
            self.logger.info("重启DNS服务...")
            
            result = subprocess.run(['sudo', 'systemctl', 'restart', 'systemd-resolved'], 
                                  check=False, capture_output=True)
            
            return result.returncode == 0
            
        except Exception as e:
            self.logger.error(f"重启DNS服务失败: {e}")
            return False

    def _check_interface_status(self, interface: str) -> bool:
        """检查网络接口状态"""
        try:
            result = subprocess.run(['ip', 'link', 'show', interface], 
                                  check=False, capture_output=True, text=True)
            
            if result.returncode == 0:
                return 'state UP' in result.stdout
            return False
            
        except Exception as e:
            self.logger.error(f"检查接口状态失败: {e}")
            return False

    def _verify_wifi_connection(self, interface: str) -> bool:
        """验证WiFi连接"""
        try:
            # 检查接口是否有IP地址
            result = subprocess.run(['ip', 'addr', 'show', interface], 
                                  check=False, capture_output=True, text=True)
            
            if result.returncode == 0 and 'inet ' in result.stdout:
                # 进一步检查网络连通性
                return self._test_internet_connectivity()
            
            return False
            
        except Exception as e:
            self.logger.error(f"验证WiFi连接失败: {e}")
            return False

    def _test_wifi_scan(self, interface: str) -> bool:
        """测试WiFi扫描功能"""
        try:
            result = subprocess.run(['sudo', 'iw', interface, 'scan'], 
                                  check=False, capture_output=True, timeout=10)
            
            return result.returncode == 0
            
        except Exception as e:
            self.logger.error(f"测试WiFi扫描失败: {e}")
            return False

    def _test_internet_connectivity(self) -> bool:
        """测试互联网连通性"""
        try:
            # 尝试连接到公共DNS服务器
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(self.connectivity_check_timeout)
            result = sock.connect_ex(('*******', 53))
            sock.close()
            
            return result == 0
            
        except Exception as e:
            self.logger.error(f"测试网络连通性失败: {e}")
            return False

    def _test_dns_resolution(self) -> bool:
        """测试DNS解析"""
        try:
            socket.gethostbyname('www.google.com')
            return True
        except Exception as e:
            self.logger.error(f"DNS解析测试失败: {e}")
            return False

    def _reconnect_saved_networks(self) -> bool:
        """重连已保存的网络"""
        try:
            if hasattr(self.network_node, 'network_manager'):
                network_manager = self.network_node.network_manager
                if hasattr(network_manager, '_read_wpa_supplicant_config'):
                    saved_networks = network_manager._read_wpa_supplicant_config()
                    
                    if saved_networks:
                        for network in saved_networks:
                            ssid = network.get('ssid', '')
                            password = network.get('psk', '')
                            
                            if ssid:
                                self.logger.info(f"尝试连接到保存的网络: {ssid}")
                                
                                # 使用网络管理器的连接方法
                                if hasattr(network_manager, 'connect_wifi_network'):
                                    result = network_manager.connect_wifi_network(ssid, password)
                                    if result and result.get('success'):
                                        return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"重连已保存网络失败: {e}")
            return False

    def _health_check_loop(self):
        """健康检查循环"""
        while True:
            try:
                time.sleep(self.network_health_check_interval)
                self._perform_health_check()
            except Exception as e:
                self.logger.error(f"网络健康检查时发生错误: {e}")

    def _perform_health_check(self):
        """执行网络健康检查"""
        try:
            # 检查WiFi接口状态
            if hasattr(self.network_node, 'network_manager'):
                wifi_interface = getattr(self.network_node.network_manager, 'wifi_interface', 'wlan0')
                
                if not self._check_interface_status(wifi_interface):
                    self.exception_manager.report_exception(
                        exception_type=ExceptionType.NETWORK_INTERFACE_ERROR,
                        message=f"WiFi接口 {wifi_interface} 状态异常",
                        severity=ExceptionSeverity.HIGH,
                        context={"interface": wifi_interface}
                    )
                
                # 检查网络连通性
                if not self._test_internet_connectivity():
                    self.report_connectivity_error(
                        "网络连通性检查失败",
                        {"check_type": "health_check"}
                    )
                else:
                    self.report_successful_connectivity()
                
                # 检查DNS解析
                if not self._test_dns_resolution():
                    self.exception_manager.report_exception(
                        exception_type=ExceptionType.DNS_RESOLUTION_ERROR,
                        message="DNS解析检查失败",
                        severity=ExceptionSeverity.MEDIUM,
                        context={"check_type": "health_check"}
                    )
                    
        except Exception as e:
            self.logger.error(f"健康检查执行失败: {e}")

    def get_health_status(self) -> Dict[str, Any]:
        """获取网络健康状态"""
        try:
            wifi_interface = getattr(self.network_node.network_manager, 'wifi_interface', 'wlan0')
            mobile_interface = getattr(self.network_node.network_manager, 'mobile_interface', 'eth0')
            
            return {
                "wifi_health": {
                    "interface": wifi_interface,
                    "is_up": self._check_interface_status(wifi_interface),
                    "failure_count": self.wifi_failure_count
                },
                "mobile_health": {
                    "interface": mobile_interface,
                    "is_up": self._check_interface_status(mobile_interface)
                },
                "connectivity_health": {
                    "internet_reachable": self._test_internet_connectivity(),
                    "dns_working": self._test_dns_resolution(),
                    "failure_count": self.connectivity_failure_count
                }
            }
        except Exception as e:
            self.logger.error(f"获取健康状态失败: {e}")
            return {"error": str(e)} 