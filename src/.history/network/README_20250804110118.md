# Network模块文档

## 📋 模块概述

Network模块是xiaoli_application_ros2项目的核心网络管理组件，负责机器人的网络连接管理、状态监控和用户绑定控制。

## 🏗️ 架构设计

```
Network模块
├── network_node.py          # 主节点 - ROS2节点入口
├── network_manager.py       # 网络管理器 - 核心业务逻辑
├── cellular_control.py      # 蜂窝网络控制 - 4G/5G模块管理
└── dbus_monitor.py         # D-Bus监控 - 系统网络事件监听
```

## 🔧 核心功能

### 1. 网络连接管理
- **WiFi连接**: 自动连接和管理WiFi网络
- **AP热点**: 创建和管理接入点模式
- **蜂窝网络**: 4G/5G模块控制和管理
- **网络冲突检测**: 防止多网络接口冲突

### 2. 状态监控
- **实时网络状态**: 监控所有网络接口状态
- **DNS状态检测**: 检测DNS服务器可用性
- **互联网连通性**: 验证外网连接状态
- **IP地址管理**: 动态IP分配和静态IP配置

### 3. 用户绑定控制
- **绑定模式**: 支持用户绑定过程中的网络控制
- **状态暂停/恢复**: 在绑定过程中暂停网络检测
- **消息订阅**: 通过ROS2话题接收控制指令

## 📡 ROS2接口

### 订阅话题
| 话题名称 | 消息类型 | 功能描述 | 队列大小 |
|---------|---------|---------|---------|
| `/andlink_network` | std_msgs/msg/String | 用户绑定控制消息 | 10 |

### 发布话题
| 话题名称 | 消息类型 | 功能描述 | 发布频率 |
|---------|---------|---------|---------|
| `/dns_status` | std_msgs/msg/String | DNS服务器状态 | 5分钟间隔 |
| `/internet_conflict` | std_msgs/msg/String | 网络冲突信息 | 事件触发 |
| `/internet_connect_status` | std_msgs/msg/String | 互联网连接状态 | 定时检查(20秒间隔) |

### 服务接口
| 服务名称 | 服务类型 | 功能描述 | 实际服务名 |
|---------|---------|---------|---------|
| 网络控制服务 | homi_speech_interface/srv/NetCtrl | 网络状态查询和控制 | `/homi_speech/network_service` |
| 机器人控制服务 | homi_speech_interface/srv/SIGCData | AP热点开关控制 | `/homi_speech/robot_control_service` |

#### 网络控制服务支持的命令
| 命令 | 功能描述 | 必需参数 | 可选参数 |
|------|---------|---------|---------|
| `getNetworkStatus` | 获取当前网络状态 | 无 | 无 |
| `setNetworkStatus` | 设置网络开关状态 | 无 | `networkStatus` |
| `getWifiSetStatus` | 获取WiFi配置状态 | 无 | 无 |
| `setWifiSetStatus` | 设置WiFi配置状态 | `status` | 无 |
| `scanWifiNetworks` | 扫描可用WiFi网络 | 无 | 无 |
| `connectWifi` | 连接到指定WiFi网络 | `ssid` | `password` |
| `disconnectWifi` | 断开当前WiFi连接 | 无 | 无 |
| `getWifiConnectionStatus` | 获取WiFi连接状态 | 无 | 无 |
| `getWifiHardwareStatus` | 获取WiFi硬件状态 | 无 | 无 |
| `getWifiScanStatus` | 获取WiFi扫描状态 | 无 | 无 |
| `clearWifiScanCache` | 清除WiFi扫描缓存 | 无 | 无 |
| `getDnsStatus` | 获取DNS状态信息 | 无 | 无 |
| `switchDnsServer` | 切换DNS服务器 | `dnsServer` | 无 |
| `refreshDnsServers` | 刷新并选择最佳DNS | 无 | 无 |
| `getNetworkDiagnostic` | 获取网络诊断信息 | 无 | `interface` |
| `performDnsHealthCheck` | 执行DNS健康检查 | 无 | `interface` |
| `getBindStatus` | 获取用户绑定状态 | 无 | 无 |

#### setNetworkStatus命令详细说明
`setNetworkStatus`命令用于控制WiFi和移动数据的开关状态：

**参数格式**：
```json
{
  "command": "setNetworkStatus",
  "networkStatus": {
    "wifiState": "on|off",      // 可选，缺失时使用当前状态
    "mobileDataState": "on|off" // 可选，缺失时使用当前状态
  }
}
```

**使用限制**：
- `wifiState`和`mobileDataState`不能同时为"off"
- 参数可以只提供其中一个，缺失的参数会从当前状态自动补全

**示例用法**：
```bash
# 开启WiFi，关闭移动数据
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"setNetworkStatus\", \"networkStatus\": {\"wifiState\": \"on\", \"mobileDataState\": \"off\"}}'}"

# 关闭WiFi，开启移动数据
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"setNetworkStatus\", \"networkStatus\": {\"wifiState\": \"off\", \"mobileDataState\": \"on\"}}'}"

# 同时开启WiFi和移动数据
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"setNetworkStatus\", \"networkStatus\": {\"wifiState\": \"on\", \"mobileDataState\": \"on\"}}'}"

# 只设置WiFi状态（移动数据状态保持当前状态）
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"setNetworkStatus\", \"networkStatus\": {\"wifiState\": \"off\"}}'}"

# 只设置移动数据状态（WiFi状态保持当前状态）
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"setNetworkStatus\", \"networkStatus\": {\"mobileDataState\": \"on\"}}'}"
```

#### WiFi配置状态命令详细说明

**getWifiSetStatus命令**：
获取当前WiFi配置状态，用于判断用户是否已经配置过WiFi连接。

**参数格式**：
```json
{
  "command": "getWifiSetStatus"
}
```

**返回示例**：
```json
{
  "success": true,
  "data": {
    "wifiSetStatus": 0,
    "statusText": "未配置"
  }
}
```

**setWifiSetStatus命令**：
设置WiFi配置状态，用于标记WiFi是否已经配置。

**参数格式**：
```json
{
  "command": "setWifiSetStatus",
  "status": 0  // 0=未配置, 1=已配置
}
```

**使用限制**：
- `status` 参数必须是 0 或 1
- 0 表示未配置状态
- 1 表示已配置状态

**示例用法**：
```bash
# 获取WiFi配置状态
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"getWifiSetStatus\"}'}"

# 设置WiFi为已配置状态
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"setWifiSetStatus\", \"status\": 1}'}"

# 设置WiFi为未配置状态
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"setWifiSetStatus\", \"status\": 0}'}"
```

**自动更新机制**：
- 当WiFi成功连接时，系统会自动将 `wifiSetStatus` 设置为 1（已配置）
- 可以手动设置为 0 来重置配置状态，便于重新引导用户配置WiFi

#### WiFi扫描和连接命令详细说明

**scanWifiNetworks命令**：
扫描附近可用的WiFi网络，支持异步扫描和缓存机制。如果WiFi处于关闭状态，会自动开启WiFi进行扫描。

**参数格式**：
```json
{
  "command": "scanWifiNetworks"
}
```

**返回示例**：
```json
{
  "status": "completed",
  "code": 200,
  "networks": [
    {
      "ssid": "MyWiFi",
      "bssid": "00:11:22:33:44:55",
      "signal_strength": "85%",
      "signal_level": -35,
      "frequency": "2437 MHz",
      "channel": "6",
      "security": "WPA2",
      "rate": "54 Mbps"
    }
  ],
  "count": 1,
  "cached": false,
  "scan_time": 3.2,
  "message": "扫描完成",
  "wifi_auto_enabled": false,
  "wifi_status_changed": "WiFi已从关闭状态自动开启以进行扫描"
}
```

**字段说明**：
- `wifi_auto_enabled`: 布尔值，表示是否自动开启了WiFi
- `wifi_status_changed`: 字符串，当WiFi被自动开启时的说明信息

**connectWifi命令**：
连接到指定的WiFi网络，支持开放网络和加密网络。

**参数格式**：
```json
{
  "command": "connectWifi",
  "ssid": "MyWiFi",
  "password": "mypassword"  // 可选，开放网络时不需要
}
```

**返回示例**：
```json
{
  "success": true,
  "message": "成功连接到 MyWiFi",
  "error_code": 200,
  "already_connected": false
}
```

**disconnectWifi命令**：
断开当前WiFi连接。

**参数格式**：
```json
{
  "command": "disconnectWifi"
}
```

**getWifiConnectionStatus命令**：
获取当前WiFi连接的详细状态信息。

**返回示例**：
```json
{
  "interface": "wlp1s0",
  "current_ssid": "MyWiFi",
  "signal_strength": "85%",
  "ip_address": "*************",
  "mac_address": "aa:bb:cc:dd:ee:ff",
  "connection_status": "connected"
}
```

**getWifiScanStatus命令**：
获取WiFi扫描的当前状态，用于检查是否正在扫描或缓存是否有效。

**clearWifiScanCache命令**：
清除WiFi扫描缓存，强制下次扫描时重新获取网络列表。

**示例用法**：
```bash
# 扫描WiFi网络
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"scanWifiNetworks\"}'}"

# 连接WiFi网络（有密码）
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"connectWifi\", \"ssid\": \"MyWiFi\", \"password\": \"mypassword\"}'}"

# 连接WiFi网络（开放网络）
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"connectWifi\", \"ssid\": \"OpenWiFi\"}'}"

# 断开WiFi连接
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"disconnectWifi\"}'}"

# 获取WiFi连接状态
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"getWifiConnectionStatus\"}'}"

# 获取WiFi硬件状态
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"getWifiHardwareStatus\"}'}"

# 获取WiFi扫描状态
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"getWifiScanStatus\"}'}"

# 清除WiFi扫描缓存
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"clearWifiScanCache\"}'}"
```

## ⚙️ 配置参数

### 网络接口配置
```yaml
network_node:
  ros__parameters:
    wifi_connect_interface: "wlp1s0"        # WiFi接口
    p2p_connect_interface: "wlan1"          # AP热点接口
    mobile_connect_interface: "enx00e04c6801d0"  # 蜂窝网络接口
    ssid: "xiaoli51"                        # AP热点名称
    static_ip: "***********"                # 静态IP地址
    device_type: "yes"                      # 设备类型
    cellular_option: "disable"              # 蜂窝网络选项
    timer_interval: 20                      # 定时器间隔(秒)
    log_level: "INFO"                       # 日志级别
```

### DNS服务器配置
```yaml
    dns_primary_servers: ["*********", "************", "***************"]
    dns_backup_servers: ["************", "*******", "*******"]
```

## 🚀 使用方法

### 启动网络节点
```bash
# 方法1: 使用调试脚本（推荐）
cd xiaoli_application_ros2
./debug_network_node.sh --test

# 方法2: 直接运行
python3 src/network/network/network_node.py

# 方法3: 使用launch文件
ros2 launch launch_package robot_launch.py
```

### 配置日志级别
```bash
# 命令行参数
./debug_network_node.sh --log-level DEBUG

# 环境变量
export ROS_LOG_LEVEL=DEBUG
./debug_network_node.sh

# 配置文件
# 修改 src/launch_package/configs/robot_config.yaml 中的 log_level 参数
```

## 📊 网络状态字段说明

### 🔧 开关状态 vs 连接状态

Network模块v2.0.0引入了**开关状态**和**连接状态**的明确分离：

| 字段名 | 类型 | 分类 | 说明 | 示例值 |
|--------|------|------|------|--------|
| `wifiState` | string | **开关状态** | WiFi无线电开关状态 | "on"/"off" |
| `mobileDataState` | string | **开关状态** | 移动数据开关状态 | "on"/"off" |
| `wifiSetStatus` | int | **配置状态** | WiFi配置状态 | 0/1 (0=未配置, 1=已配置) |
| `isWifiConnect` | string | **连接状态** | WiFi实际连接并可用状态 | "true"/"false" |
| `isInternetConnect` | string | **连接状态** | 整体互联网连接状态 | "true"/"false" |
| `wifiName` | string | **连接信息** | 当前连接的WiFi名称 | "MyWiFi" |
| `userBind` | string | **系统状态** | 用户是否绑定 | "true"/"false" |

### 🎯 状态逻辑说明

- **开关状态**: 表示硬件开关是否开启，通过`nmcli radio wifi`等命令检测
- **连接状态**: 表示实际网络连接并能访问外网，通过接口状态+外网连通性检测
- **独立性**: 开关开启不代表连接成功，连接成功必须开关开启

### 📋 状态组合示例

| WiFi开关 | WiFi连接 | 移动数据开关 | 互联网连接 | 说明 |
|----------|----------|--------------|------------|------|
| on | true | on | true | 双网络正常 |
| on | false | on | true | WiFi断开，移动网络正常 |
| off | false | on | true | WiFi关闭，移动网络正常 |
| on | true | off | true | 移动网络关闭，WiFi正常 |
| off | false | off | false | 所有网络关闭 |

## 🧪 测试和验证

### 快速测试脚本
```bash
# 一键检查所有网络话题状态
./src/network/test/check_network_topics.sh

# 验证订阅消息功能
python3 src/network/test/verify_network_subscriptions.py

# 测试服务和发布者功能
python3 src/network/test/test_services_and_publishers.py
```

### 订阅消息测试
```bash
# 手动测试绑定控制消息
ros2 topic pub --once /andlink_network std_msgs/msg/String '{data: "notify_userbind_start"}'
ros2 topic pub --once /andlink_network std_msgs/msg/String '{data: "notify_userbind_end"}'

# 验证订阅者是否正常工作
ros2 topic echo /andlink_network &
ros2 topic pub --once /andlink_network std_msgs/msg/String '{data: "test_message"}'
```

### 服务接口测试
```bash
# 检查服务状态
ros2 service list | grep -E "(network|homi_speech)"

# 测试网络控制服务（需要JSON格式的数据）
# 获取网络状态
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"getNetworkStatus\"}'}"

# 获取DNS状态
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"getDnsStatus\"}'}"

# 获取绑定状态
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"getBindStatus\"}'}"

# 获取WiFi配置状态
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"getWifiSetStatus\"}'}"

# 设置WiFi配置状态
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"setWifiSetStatus\", \"status\": 1}'}"

# 扫描WiFi网络
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"scanWifiNetworks\"}'}"

# 连接WiFi网络
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"connectWifi\", \"ssid\": \"MyWiFi\", \"password\": \"mypassword\"}'}"

# 断开WiFi连接
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"disconnectWifi\"}'}"

# 获取WiFi连接状态
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"getWifiConnectionStatus\"}'}"

# 刷新DNS服务器
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"refreshDnsServers\"}'}"

# 获取网络诊断信息
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"getNetworkDiagnostic\", \"interface\": \"wlp1s0\"}'}"

# 设置网络状态（开启/关闭WiFi或移动数据）
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"setNetworkStatus\", \"networkStatus\": {\"wifiState\": \"on\", \"mobileDataState\": \"off\"}}'}"

# 切换DNS服务器
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"switchDnsServer\", \"dnsServer\": \"*******\"}'}"

# 执行DNS健康检查
ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl "{data: '{\"command\": \"performDnsHealthCheck\", \"interface\": \"wlp1s0\"}'}"

# 测试AP控制服务（机器人控制服务）
ros2 service call /homi_speech/robot_control_service homi_speech_interface/srv/SIGCData "{data: 'openap_notify'}"
ros2 service call /homi_speech/robot_control_service homi_speech_interface/srv/SIGCData "{data: 'closeap_notify'}"

# 检查服务接口定义
ros2 interface show homi_speech_interface/srv/NetCtrl
ros2 interface show homi_speech_interface/srv/SIGCData
```

### 发布者功能测试
```bash
# 监听网络状态话题
ros2 topic echo /internet_connect_status

# 监听网络冲突信息
ros2 topic echo /internet_conflict

# 监听DNS状态
ros2 topic echo /dns_status

# 检查话题发布频率
ros2 topic hz /internet_connect_status
ros2 topic hz /dns_status
```

### 调试工具
```bash
# 查看节点状态
ros2 node list | grep network

# 检查话题信息
ros2 topic info /internet_connect_status
ros2 topic info /dns_status
ros2 topic info /internet_conflict

# 检查服务信息
ros2 service type /homi_speech/network_service
ros2 service type /homi_speech/robot_control_service

# 查看节点详细信息
ros2 node info /network_node

# 检查话题发布频率
ros2 topic hz /internet_connect_status
ros2 topic hz /dns_status
```

### 可用测试文件
```bash
# 测试文件目录: src/network/test/
├── check_network_topics.sh              # 网络话题检查脚本
├── verify_network_subscriptions.py      # 订阅消息验证
├── test_services_and_publishers.py      # 服务和发布者测试
├── test_network_messages.py             # 网络消息测试
├── test_network_services.sh             # 网络服务测试脚本
├── test_async_performance.py            # 异步性能测试
├── test_dns_async.py                    # DNS异步测试
├── test_interface_control.py            # 接口控制测试
├── test_network_conflict.py             # 网络冲突测试
├── test_state_fields.py                 # 状态字段测试
└── test_wifi_switch.py                  # WiFi开关测试

# 运行特定测试
python3 src/network/test/test_async_performance.py
python3 src/network/test/test_dns_async.py
python3 src/network/test/test_interface_control.py
```

## 🔍 故障排除

### 常见问题

1. **网络接口未找到**
   - 检查接口名称配置是否正确
   - 确认硬件设备已连接

2. **蜂窝模块初始化失败**
   - 检查硬件连接
   - 确认cellular_option配置
   - 查看详细错误日志

3. **DNS解析失败**
   - 检查DNS服务器配置
   - 验证网络连通性
   - 尝试更换DNS服务器

### 日志分析
```bash
# 查看实时日志
ros2 topic echo /rosout

# 设置DEBUG级别查看详细日志
./debug_network_node.sh --log-level DEBUG
```

## 📚 相关文档

- [服务和发布者测试文档](test/SERVICE_AND_PUBLISHER_TESTING.md)
- [调试配置指南](../../DEBUG_README.md)
- [VS Code调试配置](../../../.vscode/launch.json)

## 🔄 开发指南

### 添加新功能
1. 在相应的模块文件中添加功能代码
2. 更新配置参数（如需要）
3. 添加相应的测试用例
4. 更新文档

### 代码规范
- 使用Python类型提示
- 添加详细的文档字符串
- 遵循ROS2编码规范
- 使用适当的日志级别

## 🔗 相关文档

- [服务和发布者测试文档](test/SERVICE_AND_PUBLISHER_TESTING.md)
- [网络话题检查脚本](test/check_network_topics.sh)
- [订阅消息验证脚本](test/verify_network_subscriptions.py)
- [调试配置指南](../../DEBUG_README.md)
- [VS Code调试配置](../../../.vscode/launch.json)

## 📅 版本信息

- **版本**: v2.2.0 (WiFi配置状态增强版本)
- **创建日期**: 2025-01-22
- **最后更新**: 2025-01-23
- **兼容性**: ROS2 Foxy/Humble
- **维护状态**: 🟢 活跃维护
- **重要更新**: ✅ 新增WiFi配置状态字段和相关接口

## 🚀 最新更新

### v2.2.0 - WiFi配置状态增强版本 (2025-01-23)
- ✅ **新增 wifiSetStatus 字段**: 添加WiFi配置状态跟踪功能
- ✅ **新增服务命令**: 添加 `getWifiSetStatus` 和 `setWifiSetStatus` 命令
- ✅ **WiFi扫描和连接**: 完善 `scanWifiNetworks`、`connectWifi`、`disconnectWifi` 等命令文档
- ✅ **WiFi状态查询**: 添加 `getWifiConnectionStatus`、`getWifiHardwareStatus` 等命令说明
- ✅ **扫描缓存管理**: 添加 `getWifiScanStatus`、`clearWifiScanCache` 命令文档
- ✅ **智能WiFi扫描**: `scanWifiNetworks` 命令自动检测并开启关闭的WiFi
- ✅ **自动状态更新**: WiFi连接成功时自动设置为已配置状态
- ✅ **向后兼容**: 自动迁移旧配置文件，添加新字段
- ✅ **配置持久化**: 状态信息持久化存储到配置文件
- ✅ **完整文档**: 提供详细的使用说明和测试示例

### v2.1.0 - 文档更新版本 (2025-07-24)
- ✅ **ROS2接口文档更新**: 修正了服务名称和接口定义
- ✅ **服务调用参数修正**: 更新了NetCtrl服务的正确JSON格式调用方式
- ✅ **完整命令文档**: 添加了所有支持的NetCtrl命令示例，包括setNetworkStatus
- ✅ **setNetworkStatus智能补全**: 改进了参数处理逻辑，缺失参数自动从当前状态补全
- ✅ **setNetworkStatus详细说明**: 提供了网络状态设置的完整使用指南
- ✅ **测试文档完善**: 添加了完整的测试文件列表和使用方法
- ✅ **服务名称修正**: 更新了实际的服务名称映射
- ✅ **测试脚本整理**: 提供了所有可用测试脚本的详细说明
- ✅ **调试工具增强**: 添加了更多调试和验证命令

### v2.0.0 - 异步优化版本 (2025-01-22)
- ✅ **异步网络检查**: 网络状态检查不再阻塞ROS2主线程
- ✅ **性能提升**: 服务响应时间显著改善
- ✅ **线程池管理**: 使用ThreadPoolExecutor管理异步任务
- ✅ **完整监控**: 提供异步操作的详细日志
- ✅ **向后兼容**: 保持所有现有功能不变

---

**注意**:
- 本模块是机器人系统的核心网络组件，修改时请谨慎测试
- 异步优化已在生产环境验证通过
- 所有ROS2接口定义已更新至最新版本
- 建议在修改代码前运行完整的测试套件验证功能正常
