#include "robdog_ctrl_node.h"
#include "robotMgr/robot_info_mgr.h"
#include "public/tools.h"
#include "robotState/RobotState.h" // 为了存取xml
#include <arpa/inet.h>
#include <errno.h>
#include <fcntl.h>
#include <poll.h>
#include <sys/file.h>
#include <sys/socket.h>
#include <unistd.h>
#include <sstream>
#include <string>
#include <filesystem>
#include <vector>
#include <random> 
#include <arpa/inet.h> 
#include <cmath> 
#include <thread>
#include <stdexcept>
#include <array>
#include <regex>
#include <mutex>
#include <condition_variable>
#include <sys/stat.h>
#include <dirent.h>
#include <homi_speech_interface/srv/play_file.hpp>
#include <homi_speech_interface/def.h>
#include <ament_index_cpp/get_package_share_directory.hpp>
#include <iostream>
#include <ifaddrs.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <cstring>

#pragma pack(4)

uint32_t hton_int(int value) {
    return htonl(value);
}

// ros::Timer timer;
rclcpp::TimerBase::SharedPtr timer;
// rclcpp::Timer::SharedPtr timer;

bool timer_active = false;
homi_speech_interface::msg::ContinueMove::SharedPtr g_robot_motion;

CtrlMoveData g_MoveData = {"", 0, 0, 0, 0, 0, 0};

class FloatRandomGenerator {
public:
    FloatRandomGenerator(float min, float max, size_t num_samples)
        : min(min), max(max), current_index(0) {
        float step = (max - min) / static_cast<float>(num_samples - 1);
        for (size_t i = 0; i < num_samples; ++i) {
            float value = min + i * step;
            numbers.push_back(value);
        }
        std::shuffle(numbers.begin(), numbers.end(), rng);
    }
    float next() {
        if (current_index >= numbers.size()) {
            std::cout<<"No more numbers available.";
        }
        return numbers[current_index++];
    }
private:
    float min, max;
    std::vector<float> numbers;
    size_t current_index;
    std::default_random_engine rng{static_cast<unsigned int>(std::time(nullptr))};
};

long getVideoDurationInMilliseconds(const std::string& filePath) {
    if (filePath.empty()) {
        // ROS_INFO("File path cannot be empty.");
        RCLCPP_ERROR(rclcpp::get_logger("deep_udp_ctrl_node"), "File path cannot be empty.");
        throw std::invalid_argument("File path cannot be empty.");
    }
    std::string command = "ffmpeg -i \"" + filePath + "\" 2>&1 | grep \"Duration\"";
    std::array<char, 128> buffer;
    std::string result;
    FILE* pipe = popen(command.c_str(), "r");
    if (!pipe) {
        RCLCPP_ERROR(rclcpp::get_logger("deep_udp_ctrl_node"), "popen() failed!");
        // ROS_INFO("popen() failed!");
    }
    while (fgets(buffer.data(), buffer.size(), pipe) != nullptr) {
        result += buffer.data();
    }
    pclose(pipe);
    std::regex durationRegex(R"(Duration: (\d+):(\d+):(\d+)\.(\d+))");
    std::smatch match;
    if (std::regex_search(result, match, durationRegex)) {
        int hours = std::stoi(match[1]);
        int minutes = std::stoi(match[2]);
        int seconds = std::stoi(match[3]);
        int milliseconds = std::stoi(match[4]);
        long totalMilliseconds = (hours * 3600 + minutes * 60 + seconds) * 1000 + milliseconds * 10;
        return totalMilliseconds;
    } else {
        throw std::runtime_error("Could not extract duration from ffmpeg output.");
    }
}

std::string get_random_file_from_directory(const std::string &directory)
{
    DIR *dir = opendir(directory.c_str());
    if (dir == nullptr) {
        std::cerr << "Failed to open directory: " << directory << std::endl;
        return "";
    }
    struct DirectoryCloser {
        void operator()(DIR* dir) const {
            if (dir != nullptr) closedir(dir);
        }
    };
    std::unique_ptr<DIR, DirectoryCloser> safe_dir(dir);
    std::vector<std::string> video_files;
    struct dirent *entry;
    while ((entry = readdir(safe_dir.get())) != nullptr) {
        if (entry->d_name[0] == '.' && (entry->d_name[1] == '\0' || (entry->d_name[1] == '.' && entry->d_name[2] == '\0'))) {
            continue;
        }
        std::string full_path = directory + "/" + entry->d_name;
        struct stat file_info;
        if (stat(full_path.c_str(), &file_info) == 0 && S_ISREG(file_info.st_mode)) {
            video_files.push_back(full_path);
        }
    }
    if (video_files.empty()) {
        std::cerr << "No files found in directory: " << directory << std::endl;
        return "";
    }
    static bool seeded = false;
    if (!seeded) {
        srand(static_cast<unsigned>(time(nullptr)));
        seeded = true;
    }
    int random_index = rand() % video_files.size();
    std::cout << "Selected file: " << video_files[random_index] << std::endl;
    return video_files[random_index];
}

// void execute_script(const std::string& directory) {
//     std::vector<std::string> files;    long duration;
//     try {
//         if (!std::filesystem::exists(directory) || !std::filesystem::is_directory(directory)) {
//             std::cerr << "Directory does not exist or is not a directory: " << directory << std::endl;
//             return;
//         }
//         for (const auto &entry : std::filesystem::directory_iterator(directory)) {
//             if (entry.is_regular_file()) {
//                 files.push_back(entry.path().string());
//             }
//         }
//     } catch (const std::filesystem::filesystem_error &e) {
//         std::cerr << "Filesystem error: " << e.what() << std::endl;
//         return; // 处理完错误后正常返回
//     } catch (const std::exception &e) {
//         std::cerr << "Exception: " << e.what() << std::endl;
//         return; // 处理其他异常
//     }    
//     std::random_device rd;
//     std::mt19937 gen(rd());
//     std::uniform_int_distribution<> dis(0, files.size() - 1);
//     try {
//         duration = getVideoDurationInMilliseconds(files[dis(gen)]);
//     } 
//     catch (const std::exception& e) {
//         std::cerr << "Error: " << e.what() << std::endl;
//         return ;
//     }
//     std::string command_1 = "/home/<USER>/updateexpression.sh " + files[dis(gen)];
//     system(command_1.c_str());
//     sleep(duration/1000);
//     command_1 = "/home/<USER>/updateexpression.sh /home/<USER>/resource/vedio/default.mp4";
//     system(command_1.c_str());
// }

// void specific_expression(const std::string& path) {
//     if (path.empty()) {
//         // ROS_INFO("File path cannot be empty.");
//         throw std::invalid_argument("File path cannot be empty.");
//     }
//     std::string command_2 = "/home/<USER>/updateexpression.sh " + path;
//     system(command_2.c_str());
// }

RobdogCtrlNode::RobdogCtrlNode() : Node("robdog_control_node"),last_vel_steady_time_(Clock::now())
{
    RobdogCtrlNode::setRobManuModel();
}

RobdogCtrlNode::~RobdogCtrlNode() {
    if (sockfd_ != -1) {
        close(sockfd_);
    }
}

/* 获取机器人厂商型号 */
RobManuModel RobdogCtrlNode::getRobManuModel()
{
    return robManuModel;
}

/* 设置机器人厂商型号 */
void RobdogCtrlNode::setRobManuModel(){
#if defined(YSC1_0)
    robManuModel = ROB_MANU_DEEP_LITE;
#elif defined(YSC1_1)
    robManuModel = ROB_MANU_DEEP_LITE;
#elif defined(UNITREE)
    robManuModel = ROB_MANU_UNITREE_GO2;
#else
    robManuModel = ROB_MANU_DEEP_LITE;
#endif
}

std::string RobdogCtrlNode::getResourcePath(const std::string &file_name){
  static const std::string package_share_dir = ament_index_cpp::get_package_share_directory("robdog_control");
  auto pathstr= package_share_dir + "/resource/" + file_name;
  RCLCPP_INFO(this->get_logger(), "file dir is : %s", pathstr.c_str());
  return pathstr;
}

void RobdogCtrlNode::initNode() {
    try {
        RCLCPP_INFO(this->get_logger(), "Creating velocity command subscription...");
        velCmd_ = this->create_subscription<geometry_msgs::msg::Twist>(
            "/catch_turtle/ctrl_instruct", 10,
            std::bind(&RobdogCtrlNode::velCmdCallback, this, std::placeholders::_1)
        );
        RCLCPP_INFO(this->get_logger(), "Velocity command subscription created successfully");

        // 订阅新的UDP连接消息
        if (robManuModel == ROB_MANU_DEEP_LITE)
        {
            RCLCPP_INFO(this->get_logger(), "Creating UDP connection subscription...");
            UdpConnectCmd_ = this->create_subscription<homi_speech_interface::msg::NewUdpConnect>(
                "/catch_turtle/ctrl_udpconnect", 1,
                std::bind(&RobdogCtrlNode::updateSocket, this, std::placeholders::_1)
            );
            RCLCPP_INFO(this->get_logger(), "UDP connection subscription created successfully");
        }

        // 订阅特定运动消息
        RCLCPP_INFO(this->get_logger(), "Creating action command subscription...");
        actionCmd_ = this->create_subscription<homi_speech_interface::msg::RobdogAction>(
            "/catch_turtle/action_type", 1,
            std::bind(&RobdogCtrlNode::MoveSkillscallback, this, std::placeholders::_1)
        );
        RCLCPP_INFO(this->get_logger(), "Action command subscription created successfully");

        // 订阅持续运动消息
        RCLCPP_INFO(this->get_logger(), "Creating continue move subscription...");
        continueMoveCmd_ = this->create_subscription<homi_speech_interface::msg::ContinueMove>(
            "/catch_turtle/continue_move", 1,
            std::bind(&RobdogCtrlNode::continueMovecallback, this, std::placeholders::_1)
        );
        RCLCPP_INFO(this->get_logger(), "Continue move subscription created successfully");

        if (robManuModel == ROB_MANU_DEEP_LITE)
        {
            RCLCPP_INFO(this->get_logger(), "Creating heartbeat timer...");
            timer_ = this->create_wall_timer(
                std::chrono::milliseconds(100),
                std::bind(&RobdogCtrlNode::HeartBeatCallback, this)
            );
            RCLCPP_INFO(this->get_logger(), "Heartbeat timer created successfully");
        }

        /*ifly_sub_ = this->create_subscription<homi_speech_interface::msg::Wakeup>(
            "/audio_recorder/wakeup_event", 1,
            [this](const std::shared_ptr<homi_speech_interface::msg::Wakeup> msg) {
                this->iflyCallback(msg);
            }
        );*/
        RCLCPP_INFO(this->get_logger(), "ifly_sub_ subscribe topic /audio_node/wakeup_event");

        RCLCPP_INFO(this->get_logger(), "Creating user defined subscription...");
        userDefinedSub_ = this->create_subscription<homi_speech_interface::msg::ProprietySet>(
            "/deep_udp_ctrl/status_ctrl",
            10,
            [this](const std::shared_ptr<homi_speech_interface::msg::ProprietySet> msg) {
                this->userDefinedCtrlCallback(msg);
            }
        );
        RCLCPP_INFO(this->get_logger(), "User defined subscription created successfully");

        RCLCPP_INFO(this->get_logger(), "userDefinedSub_ subscribe topic /deep_udp_ctrl/status_ctrl");

        RCLCPP_INFO(this->get_logger(), "Creating status report publisher...");
        statusReportPub = this->create_publisher<homi_speech_interface::msg::ProprietySet>(
            "/deep_udp_ctrl/status_report", 1
        );
        RCLCPP_INFO(this->get_logger(), "Status report publisher created successfully");

    robotStatusPub_ = this->create_publisher<homi_speech_interface::msg::RobdogState>(
        "/robdog_control/robdog_state", 1
    );
    wakeupPub_=this->create_publisher<homi_speech_interface::msg::Wakeup>("/robdog_control/wakeupHomi",1);
    robotExpressionPub_= this->create_publisher<std_msgs::msg::String>("/robdog_control/changeExpression", 1);
    this->declare_parameter("state_paths", std::vector<std::string>());
    // sockfd_ = -1;

    peripherals_ctrl_client_ = this->create_client<homi_speech_interface::srv::PeripheralsCtrl>("/robdog_control/peripherals_ctrl");
    peripherals_send_request();
    peripherals_status_client_ = this->create_client<homi_speech_interface::srv::PeripheralsStatus>("/robdog_control/peripherals_status");
    peripherals_monitor_sub_ =  this->create_subscription<std_msgs::msg::String>(
        "/robdog_control/peripherals_monitor", 10,
        std::bind(&RobdogCtrlNode::handle_peripherals_monitor_message, this, std::placeholders::_1)
    );

    ut_status_report_timer = this->create_wall_timer(
        std::chrono::seconds(2),
        std::bind(&RobdogCtrlNode::utStatusReportCallback, this));
}

void RobdogCtrlNode::setRobotMove(float x, float y, float z)
{
    strncpy(g_MoveData.event, "robot_move", sizeof(g_MoveData.event) -1);
    g_MoveData.event[sizeof(g_MoveData.event) -1] = '\0';
    g_MoveData.x = x;
    g_MoveData.y = y;
    g_MoveData.z = z;

    return;
}

void RobdogCtrlNode::setRobotView(float yaw, float pitch, float roll)
{
    strncpy(g_MoveData.event, "robot_view", sizeof(g_MoveData.event) - 1);
    g_MoveData.event[sizeof(g_MoveData.event) -1] = '\0';
    g_MoveData.yaw = yaw;
    g_MoveData.pitch = pitch;
    g_MoveData.roll = roll;

    return;
}

void RobdogCtrlNode::peripherals_send_request() {
  if (!peripherals_ctrl_client_->wait_for_service(1s)) {
    RCLCPP_WARN(this->get_logger(), "Service /robdog_control/peripherals_ctrl not available.");
    return;
  }
  try {

    Json::Value request_json;

    const std::string config_path = ament_index_cpp::get_package_share_directory("robdog_control") 
                                  + "/resource/config/peripherals_config.json";
    
    std::ifstream config_file(config_path);
    if (!config_file) {
      throw std::runtime_error("Cannot open config file: " + config_path);
    }

    Json::Value config_json;
    std::string parse_errors;
    if (!Json::parseFromStream(Json::CharReaderBuilder(), config_file, &config_json, &parse_errors)) {
      throw std::runtime_error("Config parse failed: " + parse_errors);
    }

    request_json["command"] = config_json["command"];
    request_json["data"] = config_json["data"];

    Json::StreamWriterBuilder writer;
    const std::string json_str = Json::writeString(writer, request_json);

    auto request = std::make_shared<homi_speech_interface::srv::PeripheralsCtrl::Request>();
    request->data = json_str;

    auto future = peripherals_ctrl_client_->async_send_request(
      request,
      [this](rclcpp::Client<homi_speech_interface::srv::PeripheralsCtrl>::SharedFuture result) {
        try {
          auto response = result.get();
                 
          Json::Value response_json;
          Json::CharReaderBuilder reader;
          std::string errors;
          std::istringstream stream(response->result);
          
          if (Json::parseFromStream(reader, stream, &response_json, &errors)) {
            if (response_json.get("success", false).asBool()) {
              RCLCPP_INFO(this->get_logger(), "RGB PWM updated: R=%d, G=%d, B=%d",
                        response_json["red"].asInt(),
                        response_json["green"].asInt(),
                        response_json["blue"].asInt());
            } else {
              RCLCPP_ERROR(this->get_logger(), "Service error: %s", 
                         response_json["error"].asString().c_str());
            }
          } else {
            RCLCPP_ERROR(this->get_logger(), "Response parse error: %s", errors.c_str());
          }
        } catch (const std::exception &e) {
          RCLCPP_ERROR(this->get_logger(), "Callback error: %s", e.what());
        }
      }
    );

  } catch (const Json::Exception& e) {
    RCLCPP_ERROR(this->get_logger(), "JSON error: %s", e.what());
  } catch (const std::exception& e) {
    RCLCPP_ERROR(this->get_logger(), "System error: %s", e.what());
  }
}


void RobdogCtrlNode::send_peripherals_ctrl_request() {
    if (!peripherals_ctrl_client_->wait_for_service(std::chrono::seconds(1))) {
        RCLCPP_WARN(this->get_logger(), "Service /robdog_control/peripherals_ctrl not available.");
        return;
    }

    Json::Value request_json;
    request_json["command"] = "start_control";
    request_json["timestamp"] = static_cast<unsigned int>(this->now().seconds());

    Json::StreamWriterBuilder writer;
    std::string json_request = Json::writeString(writer, request_json);

    auto request = std::make_shared<homi_speech_interface::srv::PeripheralsCtrl::Request>();
    request->data = json_request;

    auto future = peripherals_ctrl_client_->async_send_request(
        request,
        [this](rclcpp::Client<homi_speech_interface::srv::PeripheralsCtrl>::SharedFuture result) {
            try {
                auto response = result.get();
                Json::CharReaderBuilder reader;
                Json::Value response_json;
                std::string errors;
                std::istringstream stream(response->result);
                if (Json::parseFromStream(reader, stream, &response_json, &errors)) {
                    RCLCPP_INFO(this->get_logger(), "Peripherals control response: %s", response_json.toStyledString().c_str());
                } else {
                    RCLCPP_ERROR(this->get_logger(), "Failed to parse JSON response: %s", errors.c_str());
                }
            } catch (const std::exception &e) {
                RCLCPP_ERROR(this->get_logger(), "Failed to call service: %s", e.what());
            }
        }
    );
}

void RobdogCtrlNode::send_peripherals_status_request() {
    if (!peripherals_status_client_->wait_for_service(std::chrono::seconds(1))) {
        RCLCPP_WARN(this->get_logger(), "Service /robdog_control/peripherals_status not available.");
        return;
    }

    Json::Value request_json;
    request_json["query"] = "status_check";
    request_json["timestamp"] = static_cast<unsigned int>(this->now().seconds());

    Json::StreamWriterBuilder writer;
    std::string json_request = Json::writeString(writer, request_json);

    auto request = std::make_shared<homi_speech_interface::srv::PeripheralsStatus::Request>();
    request->data = json_request;

    auto future = peripherals_status_client_->async_send_request(
        request,
        [this](rclcpp::Client<homi_speech_interface::srv::PeripheralsStatus>::SharedFuture result) {
            try {
                auto response = result.get();
                Json::CharReaderBuilder reader;
                Json::Value response_json;
                std::string errors;
                std::istringstream stream(response->result);
                if (Json::parseFromStream(reader, stream, &response_json, &errors)) {
                    RCLCPP_INFO(this->get_logger(), "Peripherals status response: %s", response_json.toStyledString().c_str());
                } else {
                    RCLCPP_ERROR(this->get_logger(), "Failed to parse JSON response: %s", errors.c_str());
                }
            } catch (const std::exception &e) {
                RCLCPP_ERROR(this->get_logger(), "Failed to call service: %s", e.what());
            }
        }
    );
}

void RobdogCtrlNode::handle_peripherals_monitor_message(const std_msgs::msg::String::SharedPtr msg) {
    try {
        Json::Value root;
        JSONCPP_STRING err;
        Json::CharReaderBuilder readerBuilder;
        
        const std::string& raw_json = msg->data;
        std::istringstream json_stream(raw_json);

        if (!Json::parseFromStream(readerBuilder, json_stream, &root, &err)) {
            RCLCPP_ERROR(this->get_logger(), "JSON解析失败: %s", err.c_str());
            return;
        }

        if (!root.isMember("command") || root["command"].asString() != "touch_event") {
            RCLCPP_DEBUG(this->get_logger(), "忽略非触控事件命令");
            return;
        }

        // 检查data成员是否存在
        if (!root.isMember("data") || !root["data"].isObject()) {
            RCLCPP_WARN(this->get_logger(), "触摸事件消息缺少data对象");
            return;
        }

        // 检查data对象中是否有任何一个值为true
        bool any_touch_active = false;
        const Json::Value& data = root["data"];
        Json::Value::Members members = data.getMemberNames();
        
        for (const auto& key : members) {
            if (data[key].asBool()) {
                any_touch_active = true;
                RCLCPP_INFO(this->get_logger(), "检测到触摸: 引脚 %s = true", key.c_str());
                break;
            }
        }
        
        if (any_touch_active) {
            RCLCPP_INFO(this->get_logger(), "HEAD has been touched");

            if(true == RobotInfoMgr::getInstance().utStandCheck())
            {
                // 站立状态
                 robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_TWIST);
            }

            // robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_TWIST);

            RCLCPP_INFO(this->get_logger(), "before ExpressionChange::getInstance().async_callback_work");
            ExpressionChange::getInstance().async_callback_work(getResourcePath("video/touch/"),1);
            RCLCPP_INFO(this->get_logger(), "after ExpressionChange::getInstance().async_callback_work");
            
            std::thread t_audio(&RobdogCtrlNode::playAudio,this, getResourcePath("audio/touch/"));
            t_audio.detach();
        }

    } catch (const std::exception& e) {
        RCLCPP_ERROR(this->get_logger(), "消息处理异常: %s", e.what());
    }
}


// ************************************* 把playAudio函数放到了节点类里面，具体功能需要后续测试 ***************************************
void RobdogCtrlNode::playAudio(const std::string& filePath) {
#if 0
// 通过调用服务播放本地文件
    playAudioFile(filePath);
#else
    string audioFile=filePath;
    struct stat statbuf;
    if (stat(filePath.c_str(), &statbuf) != 0) {
        RCLCPP_ERROR(this->get_logger(), "Can not get path");
        return;
    }
    if (S_ISDIR(statbuf.st_mode)){
        std::string random_file = get_random_file_from_directory(filePath);
        if (!random_file.empty()) {
            audioFile = random_file;
            RCLCPP_INFO(this->get_logger(), "Going to play random audio file: %s", audioFile.c_str());
        } else {
            RCLCPP_ERROR(this->get_logger(), "No video files found in directory: %s", random_file.c_str());
            return;
        }
    }
    // FILE* fp;
    // char buffer[50];
    // fp = popen(R"(aplay -l | grep "USB Audio Devic" -A 2 | grep "card" | awk '{print $2}' | tr -d ':')", "r");
    // if (!fp) {
    //     RCLCPP_ERROR(this->get_logger(), "ThirdpartyAudioDevice Search audio device failed");
    //     return;
    // }
    // fgets(buffer, sizeof(buffer), fp);
    // std::string device_name = "plughw:" + std::string(1, buffer[0]) + ",0";

    auto client_abort = this->create_client<homi_speech_interface::srv::AssistantAbort>("/homi_speech/helper_assistant_abort_service");
    auto request_abort = std::make_shared<homi_speech_interface::srv::AssistantAbort::Request>();
    auto response_abort = client_abort->async_send_request(request_abort);

    auto client_wake = this->create_client<homi_speech_interface::srv::SetWakeEvent>("/audio_node/set_wake_event_service");
    auto request_wake = std::make_shared<homi_speech_interface::srv::SetWakeEvent::Request>();
    request_wake->target = false;
    auto response_wake = client_wake->async_send_request(request_wake);

    // std::string command_3 = std::string("aplay") + " \"" + audioFile + "\"";
    std::string command_3= "aplay  \"" +  audioFile + "\"";
    RCLCPP_INFO(this->get_logger(), "Going to exe command: %s", command_3.c_str());
    std::system(command_3.c_str());

    // 开启语音唤醒事件
    request_wake->target = true;
    response_wake = client_wake->async_send_request(request_wake);
#endif
}

void RobdogCtrlNode::playAudioFile (const std::string& filePath) {
    // // 调用 homi_audio_player play file 服务
    // RCLCPP_INFO(this->get_logger()," playAudioFile: %s",filePath.c_str());
    // auto clt = this->create_client<homi_speech_interface::srv::PlayFile>(HOMI_AUDIO_PLAYER_PLAYFILE_SERVICE);
    // auto req = std::make_shared<homi_speech_interface::srv::PlayFile::Request>();
    // req->url = filePath;
    // auto ret = clt->wait_for_service(std::chrono::seconds(1));
    // if(ret==false){
    //     RCLCPP_WARN(this->get_logger(),"wait_for_service %s failed! ",HOMI_AUDIO_PLAYER_PLAYFILE_SERVICE);
    // } else {
    //     auto result = clt->async_send_request(req);
    //     // 同步等待结果
    //     if (rclcpp::spin_until_future_complete(this->shared_from_this(), result) == rclcpp::FutureReturnCode::SUCCESS) {
    //         RCLCPP_INFO(this->get_logger(), "call playfile service ret: %d", result.get()->error_code);
    //     } else {
    //         RCLCPP_ERROR(this->get_logger(), "call service failed! %s",HOMI_AUDIO_PLAYER_PLAYFILE_SERVICE);
    //     }
    // } 
}

void RobdogCtrlNode::userDefinedCtrlCallback(const std::shared_ptr<homi_speech_interface::msg::ProprietySet>& msg) {
// void RobdogCtrlNode::userDefinedCtrlCallback(const homi_speech_interface::msg::ProprietySetPtr& msg){
    // ROS_INFO("Received msg form Platintera Node: cmd:%x,value:%d", msg->cmd,msg->value);
    
    switch (msg->cmd)
    {
    case DEEP_CMD_ACTION:
        
        robdogCtrlDev->robdogCtrl_UserDefined(msg->value);

        break;
    case DEEP_CMD_FLASHLIGHT:
        deep_ctl(DEEP_CMD_FLASHLIGHT,msg->value,msg->exvalue);
        break;
    case DEEP_CMD_LIGHT:
        deep_ctl(DEEP_CMD_LIGHT,msg->value,msg->exvalue);
    // case DEEP_CMD_AI_MOTION: // 打开多地形自适应
    //     deep_ctl(DEEP_CMD_AI_MOTION,msg->value,msg->exvalue);
    default:
        break;
    }
}
void RobdogCtrlNode::positionCtrl(float x, float y,float radian)
{
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Going to excute positionCtrl");

    unsigned seed = std::chrono::system_clock::now().time_since_epoch().count();
    std::default_random_engine generator(seed);
    std::uniform_real_distribution<double> distribution(0.01, 0.1);
    double randomNumber = distribution(generator);
    radian=radian+randomNumber;

    robdogCtrlDev->robdogCtrl_Position(x, y, radian);

    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Start turn left or right move");        
}

void RobdogCtrlNode::startRotation(std::function<void()> callback, std::chrono::seconds timeout,const std::shared_ptr<homi_speech_interface::msg::Wakeup> msg) {
    if (RobotState::getInstance().getCurrentState()!=RobotStateEnum::NORMAL)
        return;
    auto should_continue_ = std::make_shared<std::atomic<bool>>(true);
    float real_angle=(msg->angle+30)*static_cast<float>(M_PI) / 180.0f; 
    if (real_angle<60*static_cast<float>(M_PI) / 180.0f ||2*M_PI-real_angle<60*static_cast<float>(M_PI)/ 180.0f) {
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "real_angle<30,do not need rotate redirect msg to homi directly"); 
        callback();        
        return;
    }
    positionCtrl(0.0f,0.0f,(msg->angle+30)<180?real_angle:-(2*M_PI-real_angle));
    std::thread([this,callback, should_continue_,timeout]() mutable {
        auto start_time = std::chrono::steady_clock::now();
        bool has_called_callback = false;
        while (should_continue_->load())
        {
            RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "RobotInfoMgr::getInstance().getRobotStatus()=%d",RobotInfoMgr::getInstance().getRobotStatus());
	        if (RobotInfoMgr::getInstance().getRobotStatus()==ROBDOG_STATUS_BODYROTWISTING_BY_AXISCOMMAND){
            	RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "The robot completes its rotation within the specified time!");
                try {
                    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "going to execute callback");
                    callback(); 
                    has_called_callback = true;
                    break; 
                } catch (const std::exception& e) {
                    RCLCPP_ERROR(this->get_logger(), "Error in callback: %s", e.what());
                }
            }
            std::this_thread::sleep_for(std::chrono::milliseconds(500));
            RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "sleep_for(std::chrono::milliseconds(500)，should_continue_=%d",should_continue_->load());
            if (!should_continue_->load()) {
                RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "should_continue_=%d",should_continue_->load());
                break;
            }
            auto elapsed_time = std::chrono::steady_clock::now() - start_time;
            RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "elapsed_time=%lld,now=%lld,start_time=%lld",elapsed_time,std::chrono::steady_clock::now(),start_time);
            RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "has_called_callback=%d",has_called_callback);
            if (std::chrono::duration_cast<std::chrono::seconds>(elapsed_time)  >= timeout && !has_called_callback) {
                RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Timeout reached, executing callback.");
                try {
                    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "going to execute callback");
                    callback();
                    has_called_callback = true; 
                } catch (const std::exception& e) {
                    RCLCPP_ERROR(this->get_logger(), "Error in callback: %s", e.what());
                }
                break;
            }
        }
        should_continue_->store(false);
    }).detach();
}

void RobdogCtrlNode::iflyCallback(const std::shared_ptr<homi_speech_interface::msg::Wakeup> msg)
{
    RCLCPP_INFO(this->get_logger(),"Received msg form IFly Node: event:%s,angle:%d", msg->ivw_word.c_str(),msg->angle);

    robdogCtrlDev->robdogCtrl_PositionAngVel();

    try {
        startRotation([this,msg]() {
            wakeupPub_->publish(*msg);
            RCLCPP_INFO(this->get_logger(),"Forward homi_speech_interface::msg::Wakeup topic messages");
        },
        std::chrono::seconds(6),msg);
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << '\n';
    }
}

void RobdogCtrlNode::initSocket(std::string devip_str, int port, int remote_port) {
    devip_str_ = devip_str;
    port_ = port;
    remote_port_ = remote_port;
    RCLCPP_INFO(this->get_logger(), "initSocket: %s, %d %d" , devip_str.c_str(), port_, remote_port);
    // ROS_INFO_STREAM("Initializing socket: " << devip_str << ", " << port << ", " << remote_port);

    sockfd_ = -1;
    if (!devip_str_.empty()) {
        inet_aton(devip_str_.c_str(), &client_addr.sin_addr);
    }

    // 创建UDP套接字「指令通信」
    sockfd_ = socket(PF_INET, SOCK_DGRAM, 0);
    if (sockfd_ == -1) {
        perror("socket");
        return;
    }
    // 创建UDP套接字「touch」
    sockfd_customized=socket(PF_INET, SOCK_DGRAM, 0);
    if (sockfd_customized == -1) {
        perror("socket");
        return;
    }
    // 创建UDP套接字「light」
    // sockfd_light=socket(PF_INET, SOCK_DGRAM, 0);
    // if (sockfd_light == -1) {
    //     perror("socket");
    //     return;
    // }
    sockaddr_in my_addr,my_addr_pro,my_addr_light;
    memset(&my_addr, 0, sizeof(my_addr));
    my_addr.sin_family = AF_INET;
    my_addr.sin_port = htons(port_);
    my_addr.sin_addr.s_addr = INADDR_ANY;

    memset(&my_addr_pro, 0, sizeof(my_addr_pro));
    my_addr_pro.sin_family = AF_INET;
    my_addr_pro.sin_port = htons(1234);
    my_addr_pro.sin_addr.s_addr = INADDR_ANY;

    // memset(&my_addr_light, 0, sizeof(my_addr_light));
    // my_addr_light.sin_family = AF_INET;
    // my_addr_light.sin_port = htons(12345);
    // my_addr_light.sin_addr.s_addr = INADDR_ANY;

    if (bind(sockfd_, (sockaddr *)&my_addr, sizeof(my_addr)) == -1) {  
        perror("bind");
        return;
    }
    if (bind(sockfd_customized, (sockaddr *)&my_addr_pro, sizeof(my_addr_pro)) == -1) {  
        perror("bind");
        return;
    }
    // if (bind(sockfd_light, (sockaddr *)&my_addr_light, sizeof(my_addr_light)) == -1) {  
    //     perror("bind");
    //     return;
    // }

    if (fcntl(sockfd_, F_SETFL, O_NONBLOCK | FASYNC) < 0) {
        perror("fcntl");
        return;
    }
    if (fcntl(sockfd_customized, F_SETFL, O_NONBLOCK | FASYNC) < 0) {
        perror("fcntl");
        return;
    }
    // if (fcntl(sockfd_light, F_SETFL, O_NONBLOCK | FASYNC) < 0) {
    //     perror("fcntl");
    //     return;
    // }
    client_addr.sin_family = AF_INET;
    client_addr.sin_port = htons(remote_port_);

    // ROS_INFO_STREAM("Socket fd is " << sockfd_);
    RCLCPP_INFO(this->get_logger(), "socket fd is %d", sockfd_);

}

// 根据QT界面传入的IP地址，更新新的UDP连接
void RobdogCtrlNode::updateSocket(const homi_speech_interface::msg::NewUdpConnect::SharedPtr msg) {
    // std::string new_devip_str, int new_remote_port, int new_local_port
    std::string new_devip_str = msg->new_devip_str;
    int new_remote_port = msg->new_remote_port;
    int new_local_port = msg->new_local_port;
    // Check if the new IP address or local port is different from the current ones
    if (new_devip_str != devip_str_ || new_remote_port != remote_port_ || new_local_port != port_) {
        // Close the existing sockets
        if (sockfd_ != -1) {
            close(sockfd_);
            sockfd_ = -1;
        }
        if (sockfd_customized != -1) {
            close(sockfd_customized);
            sockfd_customized = -1;
        }

        // Update the device IP, remote port, and local port
        devip_str_ = new_devip_str;
        remote_port_ = new_remote_port;
        port_ = new_local_port; // Update local port

        // Reinitialize the socket with the new parameters
        initSocket(devip_str_, port_, remote_port_);
    } else {
        // ROS_INFO_STREAM("No change in IP address, remote port, or local port. No update needed.");
    }
}

int RobdogCtrlNode::getPacket(char *pkt, size_t packet_size) {
    struct pollfd fds[2];
    fds[0].fd = sockfd_;
    fds[0].events = POLLIN;
    fds[1].fd = sockfd_customized;
    fds[1].events = POLLIN;
    const int POLL_TIMEOUT = 1000; // 毫秒
    sockaddr_in sender_address;
    socklen_t sender_address_len = sizeof(sender_address);
	int retval = poll(fds, 2, POLL_TIMEOUT);
	if (retval < 0) {
			if (errno != EINTR) {
				// ROS_ERROR_STREAM("poll() error: " << strerror(errno));
			}
			return 1;
	}
	if (retval == 0) {
			return 1;
	}
	if (fds[0].revents & (POLLERR | POLLHUP | POLLNVAL)) {
			// ROS_ERROR("poll() reports error");
			return 1;
	}
    // 接收数据
    if (fds[0].revents & POLLIN){
        memset(pkt, 0, packet_size);
        ssize_t nbytes = recvfrom(sockfd_, pkt, packet_size, 0,
                                    (sockaddr *)&sender_address, &sender_address_len);

        if (nbytes < 0) {
            if (errno != EWOULDBLOCK) {
                perror("recvfail");
                // ROS_ERROR("recvfail");
                return 1;
                // continue;
            }
        } 
        else if (nbytes >= 195){   // 按照云深处给的例程
        // 收到了数据
        // else if (static_cast<size_t>(nbytes) == packet_size) {
            if (!devip_str_.empty() && sender_address.sin_addr.s_addr != devip_.s_addr){
                // ROS_INFO("devip_str_: %s", devip_str_.c_str());
                // Convert the IP addresses to strings
                std::string sender_ip = inet_ntoa(sender_address.sin_addr);
                std::string dev_ip = inet_ntoa(devip_);
                // ROS_INFO("sender_address.sin_addr: %s, devip_.s_addr: %s", sender_ip.c_str(), dev_ip.c_str());
                return 0;
            }
        }
  }
	if (fds[1].revents & POLLIN) {
			memset(pkt, 0, packet_size);
			int num = recvfrom(sockfd_customized, pkt, packet_size, 0,
												(struct sockaddr*)&sender_address, &sender_address_len);
			if (num < 0) {
					perror("recvfrom");
					return 1;
			}
			char ip_str[INET_ADDRSTRLEN];
			inet_ntop(AF_INET, &sender_address.sin_addr, ip_str, sizeof(ip_str));
			std::cout << "Received from " << ip_str << ":" 
								<< ntohs(sender_address.sin_port) 
								<< " on socket " << sockfd_customized <<std::endl;
			handle_UDP_data(pkt,num);
  }
  return 1;
}

// 接收温度数据
int RobdogCtrlNode::getPacket_temperature(char *pkt, size_t packet_size) {
    // EthCommand c;
    // CommandMessage cm;
    // // Command cmd_test;
    // timespec test_time;
    // static uint32_t imu_count = 0;

    struct pollfd fds[2];
    fds[0].fd = sockfd_;
    fds[0].events = POLLIN;
    fds[1].fd = sockfd_customized;
    fds[1].events = POLLIN;
    const int POLL_TIMEOUT = 1000; // 毫秒
    sockaddr_in sender_address;
    socklen_t sender_address_len = sizeof(sender_address);
	int retval = poll(fds, 2, POLL_TIMEOUT);
	if (retval < 0) {
			if (errno != EINTR) {
				// ROS_ERROR_STREAM("poll() error: " << strerror(errno));
			}
			return 1;
	}
	if (retval == 0) {
			return 1;
	}
	if (fds[0].revents & (POLLERR | POLLHUP | POLLNVAL)) {
			// ROS_ERROR("poll() reports error");
			return 1;
	}
    // 接收数据
    if (fds[0].revents & POLLIN){
        memset(pkt, 0, packet_size);
        ssize_t nbytes = recvfrom(sockfd_, pkt, packet_size, 0,
                                    (sockaddr *)&sender_address, &sender_address_len);

        if (nbytes < 0) {
            if (errno != EWOULDBLOCK) {
                perror("recvfail");
                // ROS_ERROR("recvfail");
                return 1;
                // continue;
            }
        } 
        else if (nbytes > 0){   // 接收到了数据
            if (!devip_str_.empty() && sender_address.sin_addr.s_addr != devip_.s_addr){
                // ROS_INFO("devip_str_: %s", devip_str_.c_str());
                // Convert the IP addresses to strings
                std::string sender_ip = inet_ntoa(sender_address.sin_addr);
                std::string dev_ip = inet_ntoa(devip_);
                // ROS_INFO("sender_address.sin_addr: %s, devip_.s_addr: %s", sender_ip.c_str(), dev_ip.c_str());
                return 0;
            }
        }
    }
// 	if (fds[1].revents & POLLIN) {
// 			memset(pkt, 0, packet_size);
// 			int num = recvfrom(sockfd_customized, pkt, packet_size, 0,
// 												(struct sockaddr*)&sender_address, &sender_address_len);
// 			if (num < 0) {
// 					perror("recvfrom");
// 					return 1;
// 			}
// 			char ip_str[INET_ADDRSTRLEN];
// 			inet_ntop(AF_INET, &sender_address.sin_addr, ip_str, sizeof(ip_str));
// 			std::cout << "Received from " << ip_str << ":" 
// 								<< ntohs(sender_address.sin_port) 
// 								<< " on socket " << sockfd_customized <<std::endl;
// 			handle_UDP_data(pkt,num);
//   }   
  return 1;
}

int RobdogCtrlNode::sendPacket(uint8_t *pkt, size_t packet_size)
{
    ssize_t nbytes = sendto(sockfd_, pkt, packet_size, 0,
                            (struct sockaddr *)&client_addr, sizeof(client_addr));
    if (nbytes < 0) {
        RCLCPP_ERROR(this->get_logger(), "Failed to send packet to ysc!!!");
         return 1;
    } else if ((size_t)nbytes == packet_size) {
        //ROS_INFO("Packet sent successfully"); 
    } else if  ((size_t)nbytes < packet_size&&(size_t)nbytes>0)
    {
        RCLCPP_ERROR(this->get_logger(), "Failed to send packet to ysc!!!Send byte=%d, Should send =%d",nbytes,packet_size);
        return 1;
    }

    return 0;
}

int RobdogCtrlNode::sendPacketUserDefine(uint8_t *pkt, size_t packet_size) {
    sockaddr_in userClient_addr;
    userClient_addr.sin_family = AF_INET;
    userClient_addr.sin_port = htons(12345);
    userClient_addr.sin_addr.s_addr = inet_addr("127.0.0.1"); 
    ssize_t nbytes = sendto(sockfd_, pkt, packet_size, 0,
                            (struct sockaddr *)&userClient_addr, sizeof(userClient_addr));
    if (nbytes < 0) {
        RCLCPP_INFO(this->get_logger(), "Failed to send packetFailed to send packetFailed to send packetFailed to send packetFailed to send packet");
        return 1;
    } else if ((size_t)nbytes == packet_size) {
    
    }else if ((size_t)nbytes < packet_size&&(size_t)nbytes>0)
    {
        RCLCPP_ERROR(this->get_logger(), "Failed to send packet to ysc!!!Send byte=%d, Should send =%d",nbytes,packet_size);
        return 1;
    }
    return 0;
}

void RobdogCtrlNode::StandUp()
{
    RCLCPP_INFO(this->get_logger(), "StandUp StandUp StandUp StandUp StandUp");

    int flag = RobotInfoMgr::getInstance().checkActionState("motorSkill", "standUp");
    if (!flag)  // 不执行动作
        robdogCtrlDev->robdogCtrl_StandUp();

    rclcpp::sleep_for(std::chrono::milliseconds(200)); 
    // ros::Duration(0.2).sleep(); // 替换rclcpp::sleep_for

    robdogCtrlDev->robdogCtrl_MoveMode();
    rclcpp::sleep_for(std::chrono::milliseconds(200)); 
    // ros::Duration(0.2).sleep();

    robdogCtrlDev->robdogCtrl_AutoMode();
    rclcpp::sleep_for(std::chrono::milliseconds(200)); 
    // ros::Duration(0.2).sleep();

    // command.code = 0x21012109; //开启停障
    // command.type = 0;
    // command.size = 0x20;
    // sendPacket((uint8_t*)&command, sizeof(command));

}

void RobdogCtrlNode::GetDown() {
    RCLCPP_INFO(this->get_logger(), "GetDown GetDown GetDown GetDown GetDown");
    int flag = RobotInfoMgr::getInstance().checkActionState("motorSkill", "getDown");
    if (!flag) 
        robdogCtrlDev->robdogCtrl_GetDown();

}


// static int testcnt=0;
void RobdogCtrlNode::HeartBeatCallback() { // 新增了一个参数输入
    // sleep(10);
    // RCLCPP_INFO(this->get_logger(), "TEST SLEEP 10S");
}

void RobdogCtrlNode:: executeAction(const std::string& MoveSkill) {

    if (MoveSkill == "standUp")
    {
        robdogCtrlDev->robdogCtrl_StandUp();
    }
    else if (MoveSkill == "getDown")
    {
        robdogCtrlDev->robdogCtrl_GetDown();
    }
    else if (MoveSkill == "twistBody")
    {
        robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_TWIST);
    }
    else if (MoveSkill == "turnOver")
    {
        robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_TURNOVER);
    }
    else if (MoveSkill == "backflip")
    {
        robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_BACKFLIP);
    }
    else if (MoveSkill == "greeting")
    {
        robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_HELLO);
    }
    else if (MoveSkill == "jumpForward")
    {
        robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_JUMPFORWARD);
    }
    else if (MoveSkill == "twistJump")
    {
        robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_TWISTJUMP);
    }
    else if (MoveSkill == "twistAss")
    {
        robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_TWISTASS);
    }
    else if (MoveSkill == "shakeBody")
    {
        robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_FASTSHAKEBODY);
    }
    else if (MoveSkill == "dance")
    {
        robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_DANCE);
    }

    std::cout << "Executing action: " << MoveSkill << std::endl;
}

void RobdogCtrlNode::startStandUpToExcute(std::function<void()> callback, std::chrono::seconds timeout) {
    std::atomic<bool> is_standing_{false};
    std::atomic<bool> should_continue_{true};
    std::mutex mtx_;
    std::condition_variable cv_;
    std::thread([this, &cv_, &mtx_, &is_standing_, &should_continue_]() mutable {
        executeAction("standUp");
        while (should_continue_)
        {
            if (RobotInfoMgr::getInstance().getRobotStatus()==ROBDOG_STATUS_FORCE_CTRL_LEVEL_LOWSPPEED_GAIT){
                std::lock_guard<std::mutex> lock(mtx_);
                is_standing_ = true; 
                cv_.notify_one();  
                break; 
            }
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
    }).detach();
    std::unique_lock<std::mutex> lock(mtx_);
    if (cv_.wait_for(lock, timeout, [&]() { return is_standing_.load(); })) {
        std::cout << "Robot is standing!" << std::endl;
        callback();
    } else {
        RCLCPP_INFO(this->get_logger(), "Timeout: Robot did not stand up in time.");
        should_continue_.store(false);  
        callback();
    }
}
void RobdogCtrlNode::sendAudio(const std::string& path) {
    if (!path.empty()){
        std::thread t_audio(&RobdogCtrlNode::playAudio, this, path);
        t_audio.detach();
    }
}
// 运动功能
void RobdogCtrlNode::MoveSkillscallback(const homi_speech_interface::msg::RobdogAction::SharedPtr msg) { 

    robdogCtrlDev->robdogCtrl_AutoMode();
    
    // 新增了一个参数输入
    // RCLCPP_INFO(this->get_logger(), "actiontype: %s, actionargument: %s", msg->actiontype.c_str(), msg->actionargument.c_str());
    // actionargument 不一定有
    // 在执行动作之前把 当前状态类型 以及 动作指令 传到状态管理模块    
    std::string actiontype = msg->actiontype;
    if (msg && !msg->actionargument.empty()) {
        std::string actionargument = msg->actionargument;
        if (RobotInfoMgr::getInstance().checkActionState(actiontype, actionargument)) return; // 不执行动作
    }
    // 跟随或避障
    if (actiontype == "followMe"||actiontype=="tripStart"){
      std::string follome = msg->actionargument; // 详细的运动技能
      if (follome == "on") {
        
        robdogCtrlDev->robdogCtrl_AutoMode();

        RCLCPP_INFO(this->get_logger(), "Set mode Autonomous mode(followMe/Trip Start)");
      }else if (follome == "off"||follome == "comeHere") {
        robdogCtrlDev->robdogCtrl_AutoMode();
      }

    }
    // 运动模式
    if (actiontype == "sportMode"){
      std::string SportMode = msg->actionargument; 
      // SportMode = msg.actionargument; 
      if (SportMode == "walk") {
	  	//低速步态
        robdogCtrlDev->robdogCtrl_ChangeGait(ROBDOGCTRL_GAIT_WALK);
      }
      else if (SportMode == "run") {
	  	//高速步态
        robdogCtrlDev->robdogCtrl_ChangeGait(ROBDOGCTRL_GAIT_RUN);
      }
      else if (SportMode == "stairClimbe") {
        robdogCtrlDev->robdogCtrl_ChangeGait(ROBDOGCTRL_GAIT_STAIRCLIMB);
      }
      else if (SportMode == "climbe") {
        robdogCtrlDev->robdogCtrl_ChangeGait(ROBDOGCTRL_GAIT_CLIMB);
      }
      else if (SportMode == "AIClassic") {
        robdogCtrlDev->robdogCtrl_ChangeGait(ROBDOGCTRL_GAIT_AICLASSIC);
      }
      else if (SportMode == "AINimble") {
        robdogCtrlDev->robdogCtrl_ChangeGait(ROBDOGCTRL_GAIT_AINIMBLE);
      }
      else if (SportMode == "jumpRun") {
        robdogCtrlDev->robdogCtrl_ChangeGait(ROBDOGCTRL_GAIT_FREEJUMP);
      }
      else if (SportMode == "runSide") {
        robdogCtrlDev->robdogCtrl_ChangeGait(ROBDOGCTRL_GAIT_FREEBOUND);
      }
	  else if (SportMode == "medium_speed")
	  {
	  	//中速步态
        #if defined(UNITREE)
        #else
        #endif
	  }

    // 牵引模式后续会提供接口
    //   else if (SportMode == "traction") {
    //       command.code = 0x21010300; // 平地低速步态
    //       command.type = 0;
    //       command.size = 0;
    //       sendPacket((uint8_t*)&command, sizeof(command));
    //   }
    }
    // 运动技能
    if (actiontype == "motorSkill"){
        RCLCPP_INFO(this->get_logger(), "actiontype: %s, actionargument: %s", msg->actiontype.c_str(), msg->actionargument.c_str());
        /*
        扭身体 0x21010204
        翻身 0x21010205
        太空步 0x2101030C
        后空翻 0x21010502
        打招呼 0x21010507
        向前跳 0x2101050B
        扭身跳 0x2101020D
        
        还没实现的指令：
            比心：fingerHeart 
            作yi:makeBow
        */
        std::string MoveSkill = msg->actionargument;
        // 原本：在站立和趴下之间切换【如果在站立情况下再喊站立会导致趴下】 
        // try {
        //     if (RobotInfoMgr::getInstance().getRobotStatus()==ROBDOG_STATUS_FORCE_CTRL_LEVEL_LOWSPPEED_GAIT) {
        //         executeAction(MoveSkill);
        //     } else {
        //         startStandUpToExcute([this, MoveSkill]() { this->executeAction(MoveSkill);}, std::chrono::seconds(5));
        //     }
        // } catch (const std::exception& e) {
        //     std::cerr << "Error: " << e.what() << '\n';
        // }

        if(MoveSkill == "standUp"){// 趴下状态 && robdog_status == 1 
            robdogCtrlDev->robdogCtrl_StandUp();
            ExpressionChange::getInstance().async_callback_work(getResourcePath("video/standUp/"),1);
        }

        else if(MoveSkill == "getDown"){ // && robdog_status != 1){ 
            robdogCtrlDev->robdogCtrl_GetDown();
            ExpressionChange::getInstance().async_callback_work(getResourcePath("video/getDown/"),1);
        }

        else if(MoveSkill == "twistBody"){
            robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_TWIST);
            ExpressionChange::getInstance().async_callback_work(getResourcePath("video/twist/"),1);
            std::thread t_audio(&RobdogCtrlNode::playAudio,this, getResourcePath("audio/twist/"));
            t_audio.detach();
            // std::thread audioTwist(&RobdogCtrlNode::playAudio,this, "/home/<USER>/resource/audio/twist/");
            // audioTwist.detach();
            // ExpressionChange::getInstance().async_callback_work("/home/<USER>/resource/vedio/twist/",1);
            this->handleLightControl(DEEP_CMD_LIGHT_ORANGE_KEEP_AWAKE, 8);
        }
        // else if(MoveSkill == "turnOver"){
        //     system("/home/<USER>/updateexpression.sh /home/<USER>/resource/眨眼+开心+星星眼.mp4"); 
        //     robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_TURNOVER);
        // }
        
        // else if(MoveSkill == "backflip"){
        //     system("/home/<USER>/updateexpression.sh /home/<USER>/resource/眨眼+开心+星星眼.mp4"); 
        //     robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_BACKFLIP);
        // }

        else if(MoveSkill == "greeting"){
            robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_HELLO);
            ExpressionChange::getInstance().async_callback_work(getResourcePath("video/greeting/"),1);
            std::thread t_audio(&RobdogCtrlNode::playAudio,this, getResourcePath("audio/hello/"));
            t_audio.detach();
            // std::thread audioGreet(&RobdogCtrlNode::playAudio,this, "/home/<USER>/resource/audio/hello/");
            // audioGreet.detach();
            // ExpressionChange::getInstance().async_callback_work("/home/<USER>/resource/vedio/hello/",1);
        }

        // else if(MoveSkill == "jumpForward"){
        //     system("/home/<USER>/updateexpression.sh /home/<USER>/resource/step1_戴墨镜.mp4"); 
        //     deep_ctl(DEEP_CMD_LIGHT, DEEP_CMD_LIGHT_8, 0);
        //     robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_JUMPFORWARD);
        // }

        // else if(MoveSkill == "twistJump"){
        //     system("/home/<USER>/updateexpression.sh /home/<USER>/resource/step1_戴墨镜.mp4"); 
        //     robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_TWISTJUMP);
        // }

        else if(MoveSkill == "sitDown"){ 
            robdogCtrlDev->robdogCtrl_Sit();
            ExpressionChange::getInstance().async_callback_work(getResourcePath("video/sitDown/"),1);
            std::thread t_audio(&RobdogCtrlNode::playAudio,this, getResourcePath("audio/sitDown/"));
            t_audio.detach();
            // std::thread audioGreet(&RobdogCtrlNode::playAudio, this,"/home/<USER>/resource/audio/sitDown");
            // audioGreet.detach();           
            // ExpressionChange::getInstance().async_callback_work("/home/<USER>/resource/左看上看眨眼.mp4",1);
        }
        else if(MoveSkill == "twistAss"){ // 蹦达 twistAss
            robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_TWISTASS);
            RCLCPP_INFO(this->get_logger(), "before ExpressionChange::getInstance().async_callback_work");
            // ExpressionChange::getInstance().async_callback_work("/home/<USER>/resource/vedio/jumping/",1);
            ExpressionChange::getInstance().async_callback_work(getResourcePath("video/twistAss/"),1);
            RCLCPP_INFO(this->get_logger(), "AFTER ExpressionChange::getInstance().async_callback_work");
            // std::thread audioGreet(&RobdogCtrlNode::playAudio, this,"/home/<USER>/resource/audio/jumping/153164244739772416.wav");
            // audioGreet.detach();
            std::thread t_audio(&RobdogCtrlNode::playAudio,this, getResourcePath("audio/jumping/153164244739772416.wav"));
            t_audio.detach();
            this->handleLightControl(DEEP_CMD_LIGHT_ORANGE_KEEP_AWAKE, 8);
        }
        else if(MoveSkill == "shakeBody"){ // 摇摆 shakeBody
            robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_SHAKEBODY);
            // ExpressionChange::getInstance().async_callback_work("/home/<USER>/resource/vedio/shake/",1);
            // std::thread audioGreet(&RobdogCtrlNode::playAudio,this, "/home/<USER>/resource/audio/Slowshake/");
            // audioGreet.detach();
            ExpressionChange::getInstance().async_callback_work(getResourcePath("video/shake/"),1);
            std::thread t_audio(&RobdogCtrlNode::playAudio,this, getResourcePath("audio/Slowshake/"));
            t_audio.detach();
            this->handleLightControl(DEEP_CMD_LIGHT_ORANGE_KEEP_AWAKE, 8);
        }
        else if(MoveSkill == "fastShakeBody"){ // 快速摇摆 FastshakeBody
            robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_FASTSHAKEBODY);
            // ExpressionChange::getInstance().async_callback_work("/home/<USER>/resource/vedio/shake/",1);
            // std::thread audioGreet(&RobdogCtrlNode::playAudio,this, "/home/<USER>/resource/audio/shake/");
            // audioGreet.detach();
            ExpressionChange::getInstance().async_callback_work(getResourcePath("video/fastShakeBody/"),1);
            std::thread t_audio(&RobdogCtrlNode::playAudio,this, getResourcePath("audio/shake/"));
            t_audio.detach();
            this->handleLightControl(DEEP_CMD_LIGHT_ORANGE_KEEP_AWAKE, 8);
        }
        else if(MoveSkill == "dance"){
            robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_DANCE);
            // ExpressionChange::getInstance().async_callback_work("/home/<USER>/resource/vedio/dance/",1);
            // std::thread audioGreet(&RobdogCtrlNode::playAudio, this,"/home/<USER>/resource/audio/dance/");
            // audioGreet.detach();
            ExpressionChange::getInstance().async_callback_work(getResourcePath("video/dance/"),1);
            std::thread t_audio(&RobdogCtrlNode::playAudio,this, getResourcePath("audio/dance/"));
            t_audio.detach();
        }
        else if (MoveSkill=="stretch") // 伸懒腰
        {
            robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_STRETCH);
            // ExpressionChange::getInstance().async_callback_work("/home/<USER>/resource/vedio/dance/",1);
            // std::thread audioGreet(&RobdogCtrlNode::playAudio, this,"/home/<USER>/resource/audio/stretch/");
            // audioGreet.detach();
            ExpressionChange::getInstance().async_callback_work(getResourcePath("video/stretch/"),1);
            std::thread t_audio(&RobdogCtrlNode::playAudio,this, getResourcePath("audio/stretch/"));
            t_audio.detach();
        }
        else if (MoveSkill=="chestOut") // 挺胸
        {
            robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_CHESTOUT);
            // ExpressionChange::getInstance().async_callback_work("/home/<USER>/resource/vedio/星星眼.mp4",1);
            // std::thread audioGreet(&RobdogCtrlNode::playAudio, this,"/home/<USER>/resource/audio/chestOut/");
            // audioGreet.detach();
            ExpressionChange::getInstance().async_callback_work(getResourcePath("video/chestOut/"),1);
            std::thread t_audio(&RobdogCtrlNode::playAudio,this, getResourcePath("audio/chestOut/"));
            t_audio.detach();
            this->handleLightControl(DEEP_CMD_LIGHT_ORANGE_KEEP_AWAKE, 8);
        }
        else if (MoveSkill=="newYearCall")//拜年，作揖
        {
            robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_NEWYEARCALL);
            // ExpressionChange::getInstance().async_callback_work("/home/<USER>/resource/vedio/笑眯眯飘爱心.mp4",1);
            // std::thread audioGreet(&RobdogCtrlNode::playAudio, this,"/home/<USER>/resource/audio/happyNewYear/");
            // audioGreet.detach();
            ExpressionChange::getInstance().async_callback_work(getResourcePath("video/happyNewYear/"),1);
            std::thread t_audio(&RobdogCtrlNode::playAudio,this, getResourcePath("audio/happyNewYear/"));
            t_audio.detach();
            this->handleLightControl(DEEP_CMD_LIGHT_PINK_KEEP_AWAKE, 8);
        }
        else if (MoveSkill=="fingerHeart")//比心
        {
            robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_FINGERHEART);
            // ExpressionChange::getInstance().async_callback_work("/home/<USER>/resource/vedio/笑眯眯飘爱心.mp4",1);
            // std::thread audioGreet(&RobdogCtrlNode::playAudio, this,"/home/<USER>/resource/audio/Fingerheart/");
            // audioGreet.detach();
            ExpressionChange::getInstance().async_callback_work(getResourcePath("video/fingerHeart/"),1);
            std::thread t_audio(&RobdogCtrlNode::playAudio,this, getResourcePath("audio/Fingerheart/"));
            t_audio.detach();
            this->handleLightControl(DEEP_CMD_LIGHT_PINK_KEEP_AWAKE, 8);
        }
        else if (MoveSkill=="happy")            // 开心
        {
            robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_HAPPY);
        }
        else if(MoveSkill == "jumpForward")     // 向前跳
        {
            robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_JUMPFORWARD);
        }
        else if (MoveSkill=="leap")             // 扑人
        {
            robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_LEAP);
        }
        else if (MoveSkill=="danceV2")          // 跳舞2
        {
            robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_DANCEV2);
        }
        else if (MoveSkill=="walkUpsideDown")   // 倒立
        {
            robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_HANDSTAND);
        }
        else if (MoveSkill=="standErect")       // 直立
        {
            robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_WALKUPRIGHT);
        }
    }
    if (actiontype == "emergencyStop"){
        
        robdogCtrlDev->robdogCtrl_StopMove();
        memset(&g_MoveData, 0, sizeof(g_MoveData));
        
        if (timer_active) {
            robdogCtrlDev->robdogCtrl_AutoMode();
            // timer.stop();
            timer->cancel();
            timer_active = false;
            // ROS_INFO("Timer is stopped,Robot move stopped.");
        }

        robdogCtrlDev->robdogCtrl_EmergencyStop();
    }
    if (actiontype == "resetZero"){ 
        robdogCtrlDev->robdogCtrl_ResetZero();
    }
    // 和强化学习有关的
    // if (actiontype == "ObstaclesRL"){ 
    //     command.code = 0x21010529;
    //     command.type = 0;
    //     command.size = 2;  // 指令值
    //     sendPacket((uint8_t*)&command, sizeof(command));
    // }
    // if (actiontype == "FlatRL"){ 
    //     command.code = 0x2101052a;
    //     command.type = 0;
    //     command.size = 2;  // 指令值
    //     sendPacket((uint8_t*)&command, sizeof(command));
    // }
    // if (actiontype == "ExitRL"){ 
    //     command.code = 0x2101052b;
    //     command.type = 0;
    //     command.size = 2;  // 指令值
    //     sendPacket((uint8_t*)&command, sizeof(command));
    // }
    if (actiontype == "NavCtrl"){
        std::string MoveSkill = msg->actionargument;
        if(MoveSkill == "AutoMode"){ 
            robdogCtrlDev->robdogCtrl_AutoMode();
        }
    }
    // 和强化学习有关的（最终平台用到的接口）
    if (actiontype == "gaitControl"){ 
        std::string MoveSkill = msg->actionargument;
        if(MoveSkill == "obstacleCross"){ 
            robdogCtrlDev->robdogCtrl_ChangeGait(ROBDOGCTRL_GAIT_OBSTACLE_CROSS);
            RobotState::getInstance().setIntelligentSwitch("on");
        }
        if(MoveSkill == "flatGround"){ 
            robdogCtrlDev->robdogCtrl_ChangeGait(ROBDOGCTRL_GAIT_FLATGROUND);
        }
        if(MoveSkill == "exit"){ 
            robdogCtrlDev->robdogCtrl_ChangeGait(ROBDOGCTRL_GAIT_EXIT);
            RobotState::getInstance().setIntelligentSwitch("off");
        }
    }
    if (actiontype == "rotateCtl"){
        std::string angle = msg->actionargument;
        float real_angle=std::stoi(angle)*static_cast<float>(M_PI) / 180.0f;
        positionCtrl(0.0f,0.0f,real_angle);
    }
}

void RobdogCtrlNode::timerCallbackForRobotMove() 
{
    //RCLCPP_WARN(this->get_logger(), "================================");
    //RCLCPP_WARN(this->get_logger(), "event: %s", g_MoveData.event);
    //RCLCPP_WARN(this->get_logger(), "x: %d, y: %d, z: %d", g_MoveData.x, g_MoveData.y, g_MoveData.z);
    //RCLCPP_WARN(this->get_logger(), "yaw: %d, pitch: %d, roll: %d", g_MoveData.yaw, g_MoveData.pitch, g_MoveData.roll);
    //RCLCPP_WARN(this->get_logger(), "================================");

    robdogCtrlDev->robdogCtrl_ContinueMove(&g_MoveData);
}

void RobdogCtrlNode::continueMovecallback(const homi_speech_interface::msg::ContinueMove::SharedPtr robot_motion){

    g_robot_motion=robot_motion;

    if(robot_motion->event == "robot_move")
    {
        // std::array<double, 2> ultralS = RobotInfoMgr::getInstance().getUltrasound();
        // if (ultralS[0]<=ULTS_THR){
        //     RCLCPP_WARN(this->get_logger(), "An obstacle was detected 0.28 meters forward");
        //     std::thread t_audio(&RobdogCtrlNode::playAudio,this, "/home/<USER>/resource/audio/Obstacle/");
        //     t_audio.detach();
        // }
        setRobotMove(robot_motion->x, robot_motion->y, robot_motion->z);
        if (robot_motion->x != 0||robot_motion->y!= 0||robot_motion->z!=0)
        {
            if (!timer_active) 
            {
                robdogCtrlDev->robdogCtrl_MoveMode();
                robdogCtrlDev->robdogCtrl_ManualMode();
                robdogCtrlDev->robdogCtrl_AvoidClose();

                timer = this->create_wall_timer(std::chrono::milliseconds(50), // 0.03秒
                    std::bind(&RobdogCtrlNode::timerCallbackForRobotMove, this));
                timer_active = true;
            }
        }
        else
            RCLCPP_WARN(this->get_logger(), "Receive robot move cmd. But value is 0");
    }
    else if (robot_motion->event == "robot_view")
    {
        setRobotView(robot_motion->yaw, robot_motion->pitch, robot_motion->roll);
        if (robot_motion->pitch != 0||robot_motion->yaw!= 0||robot_motion->roll!=0) 
        {
            if (!timer_active) 
            {
                robdogCtrlDev->robdogCtrl_MoveMode();
                robdogCtrlDev->robdogCtrl_ManualMode();
                robdogCtrlDev->robdogCtrl_AvoidClose();

                // timer = ros::NodeHandle().createTimer(ros::Duration(0.03), &RobdogCtrlNode::timerCallbackForRobotMove,this);
                timer = this->create_wall_timer(
                    std::chrono::milliseconds(50), // 0.03秒
                    std::bind(&RobdogCtrlNode::timerCallbackForRobotMove, this));
                timer_active = true;
                // ROS_INFO("Timer is running,Robot move started.");
            }
        }
        else
            // ROS_WARN("Recieve robot view cmd.But value is 0");
            RCLCPP_WARN(this->get_logger(), "Receive robot view cmd. But value is 0");
    }
    // 移动固定距离和转向
    else if(robot_motion->event == "stepMode")
    {
        // ROS_INFO("Recieve Remote Ctrl robot_motion.event=%s,x=%d,y=%d,z=%d,yaw=%d,pitch=%d,roll=%d",robot_motion.event.c_str(),robot_motion.x,robot_motion.y,robot_motion.z,robot_motion.yaw,robot_motion.pitch,robot_motion.roll);
        // ROS_INFO("stepMode: x = %d, y = %d, yaw = %d", robot_motion.x, robot_motion.y, robot_motion.yaw);
        // robot_motion.x表示向前走的步数，一步表示20cm
        // robot_motion.y表示向左走的步数，一步表示20cm
        // robot_motion.yaw表示向右转的角度(角度制)
        float stepx = robot_motion->x * 0.2;
        float stepy = robot_motion->y * 0.2;
        positionCtrl(stepx,stepy,((robot_motion->yaw)*static_cast<float>(M_PI) / 180.0f));
    }
    else if(robot_motion->event == "stopAction")
    {
        if ((g_MoveData.x != 0) || (g_MoveData.y != 0) || (g_MoveData.z != 0) || 
            (g_MoveData.pitch != 0) || (g_MoveData.yaw != 0) || (g_MoveData.roll != 0))
        {
            RCLCPP_INFO(this->get_logger(), "=============== Not Stop =================");
            RCLCPP_INFO(this->get_logger(), "event: %s", g_MoveData.event);
            RCLCPP_INFO(this->get_logger(), "x: %d, y: %d, z: %d", g_MoveData.x, g_MoveData.y, g_MoveData.z);
            RCLCPP_INFO(this->get_logger(), "yaw: %d, pitch: %d, roll: %d", g_MoveData.yaw, g_MoveData.pitch, g_MoveData.roll);
            RCLCPP_INFO(this->get_logger(), "==========================================");

            return;
        }

        robdogCtrlDev->robdogCtrl_StopMove();
        memset(&g_MoveData, 0, sizeof(g_MoveData));
        
        if (timer_active) {
            robdogCtrlDev->robdogCtrl_AutoMode();
            // timer.stop();
            timer->cancel();
            timer_active = false;
            // ROS_INFO("Timer is stopped,Robot move stopped.");
        }
        // ROS_INFO("Timer has already stopped.");
    }
    else if(robot_motion->event == "stopActionDelay")
    {
        robdogCtrlDev->robdogCtrl_StopMove();
        memset(&g_MoveData, 0, sizeof(g_MoveData));
        
        if (timer_active) {
            robdogCtrlDev->robdogCtrl_AutoMode();
            // timer.stop();
            timer->cancel();
            timer_active = false;
            // ROS_INFO("Timer is stopped,Robot move stopped.");
        }
        // ROS_INFO("Timer has already stopped.");
    }
}

// 收到速度之后的回调函数(把指令发给机器狗) 
void RobdogCtrlNode::velCmdCallback(const geometry_msgs::msg::Twist::SharedPtr msg)
{
    const auto current_steady = Clock::now();
    const auto elapsed = current_steady - last_vel_steady_time_;
    const auto elapsed_ms = std::chrono::duration_cast<std::chrono::milliseconds>(elapsed);
    if (elapsed_ms.count() < VEL_TIME_INTERVAL) {
        return; 
    }
    last_vel_steady_time_ = current_steady;

    robdogCtrlDev->robdogCtrl_AutoMode();
    
    robdogCtrlDev->robdogCtrl_Move(msg->linear.x, msg->linear.y, msg->angular.z);

    RCLCPP_INFO_THROTTLE(this->get_logger(),*this->get_clock(),3000,"send vel message to dog successful");
}

// 向机器狗请求机器人状态信息【在线程里面去执行】
void RobdogCtrlNode::send_command() 
{
    robdogCtrlDev->robdogCtrl_State();

    // std::this_thread::sleep_for(std::chrono::milliseconds(20)); // 1/50 second
}

// 向机器人请求温度信息
void RobdogCtrlNode::send_command_temperature()
{
    robdogCtrlDev->robdogCtrl_Temperature();
    // std::this_thread::sleep_for(std::chrono::milliseconds(20)); // 1/50 second
}

// 在这里将机器人状态信息发布出去【应该在界面按键触发之后再去发布还是定时发布？】
void RobdogCtrlNode::publishRobdagStateToQt(std::string robot_state_details) {
    homi_speech_interface::msg::RobdogState stateMsg;    
    stateMsg.robot_state_details = robot_state_details;
    robotStatusPub_->publish(stateMsg);
}
void RobdogCtrlNode::changeExpression(const int status) {
    auto it = state_path_map_.find(status);
    if (it != state_path_map_.end()) {
        std_msgs::msg::String path_msg;
        path_msg.data = it->second;
        robotExpressionPub_->publish(path_msg);
        RCLCPP_INFO(this->get_logger(), "State changed to %d, publishing path: %s", status, path_msg.data.c_str());
    } else {
        RCLCPP_WARN(this->get_logger(), "No path configured for state %d", status);
    }
}

void RobdogCtrlNode:: handle_UDP_data(char *data,size_t length)
{
    head_t* header = reinterpret_cast<head_t*>(data);
    rsp_body_t body;
    pk_t *pkg=(pk_t*)data;

   
    if (length >= sizeof(head_t)) {
        switch (ntohl(header->cmd)) {
            case DEEP_CMD_SENSOR:
                if (ntohl(pkg->rsp_body.sensor.value)==0x30){
                    robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_TWIST);
                    RCLCPP_INFO(this->get_logger(), "before ExpressionChange::getInstance().async_callback_work");
                    ExpressionChange::getInstance().async_callback_work(getResourcePath("video/touch/"),1);
                    RCLCPP_INFO(this->get_logger(), "after ExpressionChange::getInstance().async_callback_work");
                    // std::thread t_expression(execute_script, "/home/<USER>/resource/vedio/touch/");
                    // t_expression.detach();
                    std::thread t_audio(&RobdogCtrlNode::playAudio,this, getResourcePath("audio/touch/"));
                    t_audio.detach();
                    // deep_ctl(DEEP_CMD_LIGHT, DEEP_CMD_LIGHT_8, 0);
                }
                break;
            case DEEP_CMD_POWER:
                if (length >= sizeof(head_t) + sizeof(power_status)) {
                    homi_speech_interface::msg::ProprietySet repMsg;
                    memcpy(&body.powerAttribute, data + sizeof(head_t), sizeof(power_status));
                    repMsg.cmd=POWER_LEVEL_FROM_NODE;
                    repMsg.value=body.powerAttribute.power_level;
                    repMsg.exvalue=body.powerAttribute.chargeStatus;
                    statusReportPub->publish(repMsg);
                    // ROS_INFO("Recieve battery info,going to pubulish.Battery level is[%d],Battery charging state is [%d]",repMsg.value,repMsg.exvalue);
                }
                else
                    // ROS_ERROR_STREAM("Battery's data is wrong");

            default:
                // ROS_ERROR_STREAM( "Unknown command code: " << std::hex << static_cast<int>(header->cmd) << std::dec << std::endl);
                break;
        }
    } else {
        // ROS_ERROR_STREAM( "Invalid packet length: " << length << std::endl);
    }
}

void RobdogCtrlNode::deep_ctl(int cmdID,int cmdValue,int cmdValueEx)
{
    pk_t pkg_deep;
    memset(&pkg_deep,0,sizeof(pkg_deep));
    // ROS_INFO_STREAM("Start to control deep robot,cmd id is "<<std::hex<<cmdID);
    // ROS_INFO("Start to control deep robot,cmd id is 0x%x,cmdValue=0x%x,cmdValueEx=0x%x",cmdID,cmdValue,cmdValueEx);
    // 设置头部信息
    pkg_deep.head.len=hton_int(sizeof(pk_t));
    pkg_deep.head.stat = hton_int(DEEP_STATUS_RUN); 
    pkg_deep.head.cmd=hton_int(cmdID);
    std::string directory_head = "/data/test/audio/head/";
    std::string directory_back = "/data/test/audio/back/";
    std::string randomFile ;

    switch (cmdID)
    {
    case DEEP_CMD_LIGHT:
      pkg_deep.rqt_body.light.color = hton_int(cmdValue); 
    //   pkg_deep.rqt_body.light.duration = 5000;          // 假设持续时间为5秒
      break;
    case DEEP_CMD_EXPRESSION:
      pkg_deep.rqt_body.expression.type = cmdValue; 
      break;
    case DEEP_CMD_MOTION:
      pkg_deep.rqt_body.motion.type = cmdValue; 
      break;
    case DEEP_CMD_NET:
      pkg_deep.rqt_body.net.netswitch = cmdValue; 
      break;
    case DEEP_CMD_MOVEANDRO:
      pkg_deep.rqt_body.attitude.angle=cmdValue;
      pkg_deep.rqt_body.attitude.angle=cmdValueEx;
      break;
    case DEEP_CMD_FLASHLIGHT:
      if (cmdValue==0)
        pkg_deep.rqt_body.flashlight.brightness=0;
      else
        {pkg_deep.rqt_body.flashlight.brightness=hton_int(cmdValue);}
        break;
    case DEEP_CMD_AI_MOTION:
      pkg_deep.rqt_body.intelligentSwitch.type =cmdValue;
      break;
    case DEEP_CMD_AUDIO:
        // if (cmdValue==DEEP_CMD_AUDIO_HEAD)
        //   randomFile=getRandomFilePath(directory_head);
        // else 
        //   randomFile=getRandomFilePath(directory_back);
        if (!randomFile.empty()) {
            // ROS_INFO_STREAM("Play audio file: " << randomFile << std::endl);
        } else {
            // ROS_ERROR_STREAM("No files found in the directory" << std::endl);
        }
      memcpy(pkg_deep.rqt_body.audio.path,randomFile.c_str(),sizeof(pkg_deep.rqt_body.audio.path));
      break;
    default:
      break;
    }
    sendPacketUserDefine((uint8_t*)&pkg_deep, sizeof(pkg_deep));
}

void RobdogCtrlNode::leftNine(){
    // CommandHead command_handpos;  
    // command_handpos.code = 0x21010C0A; // 左转90度
    // command_handpos.type = 0;
    // command_handpos.size = 13;  
    // sendPacket((uint8_t*)&command_handpos, sizeof(command_handpos));

    positionCtrl(0,0,(90*static_cast<float>(M_PI) / 180.0f));

    if (robActionSuspendTimer_) {
        robActionSuspendTimer_->cancel();
    }
}

void RobdogCtrlNode::rightNine(){
    // CommandHead command_handpos;  
    // command_handpos.code = 0x21010C0A; // 右转90度
    // command_handpos.type = 0;
    // command_handpos.size = 14;  
    // sendPacket((uint8_t*)&command_handpos, sizeof(command_handpos));

    positionCtrl(0,0,(-90*static_cast<float>(M_PI) / 180.0f));

    if (robActionSuspendTimer_) {
        robActionSuspendTimer_->cancel();
    }
}

void RobdogCtrlNode::cancelHandCode(){

    robdogCtrlDev->robdogCtrl_VoiceStand(0);

    if (robCancelHandCode_) {
        robCancelHandCode_->cancel();
    }
}

// 手势识别对应的动作的执行
void RobdogCtrlNode::handleHandPosAction(int pos_type){

    robdogCtrlDev->robdogCtrl_ManualMode();

    if(pos_type == 1){ // 打招呼
        robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_HELLO);
        // ExpressionChange::getInstance().async_callback_work("/home/<USER>/resource/vedio/shake/",1); // 播放表情 心心眼
        // std::thread audioGreet(&RobdogCtrlNode::playAudio, this,"/home/<USER>/resource/audio/handpos/handposGreeting.wav");
        // audioGreet.detach();
        ExpressionChange::getInstance().async_callback_work(getResourcePath("video/shake/"),1);
        std::thread t_audio(&RobdogCtrlNode::playAudio,this, getResourcePath("audio/handpos/handposGreeting.wav"));
        t_audio.detach();
    } else if(pos_type == 2){ // 扭身体
        robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_TWIST);
        RCLCPP_WARN(this->get_logger(), "sockfd_sockfd_sockfd_sockfd_sockfd_sockfd_sockfd_ %d", sockfd_);
        // ExpressionChange::getInstance().async_callback_work("/home/<USER>/resource/vedio/dance/",1); // 播放表情 墨镜
        // std::thread audioGreet(&RobdogCtrlNode::playAudio, this,"/home/<USER>/resource/audio/handpos/handposTwistbody.wav");
        // audioGreet.detach();
        ExpressionChange::getInstance().async_callback_work(getResourcePath("video/dance/"),1);
        std::thread t_audio(&RobdogCtrlNode::playAudio,this, getResourcePath("audio/handpos/handposTwistbody.wav"));
        t_audio.detach();
    } else if(pos_type == 3){ // 坐下
        robdogCtrlDev->robdogCtrl_VoiceStand(2);

        // 消除手动指令
        robCancelHandCode_ = this->create_wall_timer(
            std::chrono::seconds(1),   
            std::bind(&RobdogCtrlNode::cancelHandCode, this)
        ); 

        // ExpressionChange::getInstance().async_callback_work("/home/<USER>/resource/vedio/shake/",1); // 播放表情 心心眼
        // std::thread audioGreet(&RobdogCtrlNode::playAudio, this,"/home/<USER>/resource/audio/handpos/handposSitdown.wav");
        // audioGreet.detach();
        ExpressionChange::getInstance().async_callback_work(getResourcePath("video/shake/"),1);
        std::thread t_audio(&RobdogCtrlNode::playAudio,this, getResourcePath("audio/handpos/handposSitdown.wav"));
        t_audio.detach();
    } else if(pos_type == 4){ // 站立
            robdogCtrlDev->robdogCtrl_VoiceStand(1);

        // 消除手动指令
        robCancelHandCode_ = this->create_wall_timer(
            std::chrono::seconds(1),   
            std::bind(&RobdogCtrlNode::cancelHandCode, this)
        ); 

        // ExpressionChange::getInstance().async_callback_work("/home/<USER>/resource/vedio/touch/stareyes.mp4", 1); // 播放表情 星星眼
        // std::thread audioGreet(&RobdogCtrlNode::playAudio, this,"/home/<USER>/resource/audio/handpos/handposStandup.wav");
        // audioGreet.detach();
        ExpressionChange::getInstance().async_callback_work(getResourcePath("video/touch/stareyes.mp4"),1);
        std::thread t_audio(&RobdogCtrlNode::playAudio,this, getResourcePath("audio/handpos/handposStandup.wav"));
        t_audio.detach();
    } else if(pos_type == 5){
        // std::thread audioGreet(&RobdogCtrlNode::playAudio, this,"/home/<USER>/resource/audio/handpos/handposTurnleftorright.wav");
        // audioGreet.detach();
        std::thread t_audio(&RobdogCtrlNode::playAudio,this, getResourcePath("audio/handpos/handposTurnleftorright.wav"));
        t_audio.detach();
        leftNine();
        //std::this_thread::sleep_for(std::chrono::seconds(3));
        robActionSuspendTimer_ = this->create_wall_timer(
            std::chrono::seconds(6),   
            std::bind(&RobdogCtrlNode::rightNine, this)
        );
        
    } else if(pos_type == 6){ // 右转转回
        // std::thread audioGreet(&RobdogCtrlNode::playAudio, this,"/home/<USER>/resource/audio/handpos/handposTurnleftorright.wav");
        // audioGreet.detach();
        std::thread t_audio(&RobdogCtrlNode::playAudio,this, getResourcePath("audio/handpos/handposTurnleftorright.wav"));
        t_audio.detach();
        rightNine();
        // std::this_thread::sleep_for(std::chrono::seconds(3));
        robActionSuspendTimer_ = this->create_wall_timer(
            std::chrono::seconds(6),   
            std::bind(&RobdogCtrlNode::leftNine, this)
        ); 
    }
}

// 无表情和离线语音的动作执行纯净版 宇树动作："standUp"，"getDown"，"twistBody"，"greeting"，"sitDown"，"dance"，"stretch"，
// "newYearCall"，"fingerHeart"，"happy"，"jumpForward"，"leap"，"danceV2"，"walkUpsideDown"，"standErect"
void RobdogCtrlNode::handleInteractionAction(std::string MoveSkill){

    robdogCtrlDev->robdogCtrl_AutoMode();
    RCLCPP_INFO(this->get_logger(), "InteractionAction actionargument: %s", MoveSkill.c_str());

    if(MoveSkill == "standUp"){
        robdogCtrlDev->robdogCtrl_StandUp();
    }
    else if(MoveSkill == "getDown"){ // && robdog_status != 1){ 
        robdogCtrlDev->robdogCtrl_GetDown();
    }
    else if(MoveSkill == "twistBody"){
        robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_TWIST);
    }
    else if(MoveSkill == "greeting"){
        robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_HELLO);
    }
    else if(MoveSkill == "sitDown"){ 
        robdogCtrlDev->robdogCtrl_Sit();
    }
    else if(MoveSkill == "dance"){
        robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_DANCE);
    }
    else if (MoveSkill=="stretch") // 伸懒腰
    {
        robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_STRETCH);
    }
    else if (MoveSkill=="newYearCall")//拜年，作揖
    {
        robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_NEWYEARCALL);
    }
    else if (MoveSkill=="fingerHeart")//比心
    {
        robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_FINGERHEART);
    }
    else if (MoveSkill=="happy")            // 开心
    {
        robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_HAPPY);
    }
    else if(MoveSkill == "jumpForward")     // 向前跳
    {
        robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_JUMPFORWARD);
    }
    else if (MoveSkill=="leap")             // 扑人
    {
        robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_LEAP);
    }
    else if (MoveSkill=="danceV2")          // 跳舞2
    {
        robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_DANCEV2);
    }
    else if (MoveSkill=="walkUpsideDown")   // 倒立
    {
        robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_HANDSTAND);
    }
    else if (MoveSkill=="standErect")       // 直立
    {
        robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_WALKUPRIGHT);
    }
}

void RobdogCtrlNode::utStatusReportCallback() 
{
    homi_speech_interface::msg::ProprietySet repMsg;

    unitree_go::msg::dds_::SportModeState_ utSportState;
    unitree_go::msg::dds_::LowState_ utLowState;

    RobotInfoMgr::getInstance().utGetHighState(utSportState);
    RobotInfoMgr::getInstance().utGetLowState(utLowState);

    repMsg.cmd=POWER_LEVEL_FROM_NODE;
    repMsg.value=utLowState.bms_state().soc();

    //云深处定义：0未在充电，1充电中 2 充满
    //宇树定义：正代表充电，负代表放电，与云深处定义不同，需转化
    if (utLowState.bms_state().current() > 0)
    {
        repMsg.exvalue = 1;
    }
    else
    {
        repMsg.exvalue = 0;
    }

    statusReportPub->publish(repMsg);

    return;
}

int RobdogCtrlNode::sendPacket_light(uint8_t *pkt, size_t packet_size) {
    int sockfd_light = socket(PF_INET, SOCK_DGRAM, 0);
    sockaddr_in light_addr;
    light_addr.sin_family = AF_INET;
    light_addr.sin_port = htons(12345);
    light_addr.sin_addr.s_addr = inet_addr("127.0.0.1"); 
    ssize_t nbytes = sendto(sockfd_light, pkt, packet_size, 0,
                            (struct sockaddr *)&light_addr, sizeof(light_addr));
    if (nbytes < 0) {
        RCLCPP_INFO(this->get_logger(), "Failed to send packetFailed to send packetFailed to send packetFailed to send packetFailed to send packet");
        return 1;
    } else if ((size_t)nbytes == packet_size) {
    
    }else if ((size_t)nbytes < packet_size&&(size_t)nbytes>0)
    {
        RCLCPP_ERROR(this->get_logger(), "Failed to send packet to ysc!!!Send byte=%d, Should send =%d",nbytes,packet_size);
        return 1;
    }
    return 0;
}

void SYS_L_2_B_32(unsigned char *buf, size_t &off, unsigned int value) {
    unsigned int net_value = htonl(value);
    memcpy(&buf[off], &net_value, sizeof(unsigned int));
    off += sizeof(unsigned int);
}

// light_control
void RobdogCtrlNode::handleLightControl(unsigned int send_cmd,int duration) {
    // 检查service客户端是否已创建
    if (!peripherals_ctrl_client_) {
        RCLCPP_ERROR(this->get_logger(), "peripherals_ctrl_client_未初始化");
        return;
    }

    // 等待服务可用
    if (!peripherals_ctrl_client_->wait_for_service(std::chrono::seconds(1))) {
        RCLCPP_WARN(this->get_logger(), "服务/robdog_control/peripherals_ctrl不可用");
        return;
    }

    if (restore_timer_) {
        restore_timer_->cancel();
        restore_timer_.reset();
        RCLCPP_DEBUG(this->get_logger(), "已取消之前的灯光恢复定时器");
    }  

    // 准备请求数据
    std::string mode;
    std::string color = "#FFFFFF"; // 默认白色
    double effect_speed = 1.0;
    double brightness = 0.8;
    std::string secondary_color;

    // 根据send_cmd参数(宏定义)配置不同的灯光模式
    switch (send_cmd) {
        case DEEP_CMD_LIGHT_01:  // 0x00000202 橙色闪烁 惊讶（surprised）、愤怒（angry）
            mode = "blink";
            color = "#FF2A00"; // 橙色
            effect_speed = 2.0;
            brightness = 0.8;
            RCLCPP_INFO(this->get_logger(), "设置LED模式: 橙色闪烁 (惊讶/愤怒)");
            break;

        case DEEP_CMD_LIGHT_ORANGE_KEEP_AWAKE:  // 0x00000202 橙色常亮 
            mode = "static";
            color = "#FF2A00"; // 橙色
            brightness = 0.8;
            RCLCPP_INFO(this->get_logger(), "设置LED模式: 橙色常亮");
            break;
        case DEEP_CMD_LIGHT_05:  // 0x00000203 橙色呼吸 厌恶（disgusted）
            mode = "breath";
            color = "#FF2A00"; // 橙色
            effect_speed = 1.0;
            brightness = 0.8;
            RCLCPP_INFO(this->get_logger(), "设置LED模式: 橙色呼吸 (厌恶)");
            break;
            
        case DEEP_CMD_LIGHT_06:  // 0x00000204 橙色流水 恐惧（fearful）
            mode = "flow";
            color = "#FF2A00"; // 橙色
            effect_speed = 1.5;
            brightness = 0.8;
            RCLCPP_INFO(this->get_logger(), "设置LED模式: 橙色流水 (恐惧)");
            break;            

        case DEEP_CMD_LIGHT_02:  // 0x00000302 粉色闪烁 高兴（happy）
            mode = "blink";
            color = "#580C1F"; // 粉色
            effect_speed = 2.0;
            brightness = 0.8;
            RCLCPP_INFO(this->get_logger(), "设置LED模式: 粉色闪烁 (高兴)");
            break;

        case DEEP_CMD_LIGHT_PINK_KEEP_AWAKE:  // 0x00000302 粉色常亮
            mode = "static";
            color = "#580C1F"; // 粉色
            brightness = 0.8;
            RCLCPP_INFO(this->get_logger(), "设置LED模式: 粉色常亮");
            break;
            
        case DEEP_CMD_LIGHT_PINK_BREATHING:  // 0x00000302 粉色呼吸
            mode = "breath";
            color = "#580C1F"; // 粉色
            effect_speed = 1.0;
            brightness = 0.8;
            RCLCPP_INFO(this->get_logger(), "设置LED模式: 粉色呼吸");
            break;            

        case DEEP_CMD_LIGHT_PINK_RUNNING:  // 0x00000302 粉色流水
            mode = "flow";
            color = "#580C1F"; // 粉色
            effect_speed = 1.5;
            brightness = 0.8;
            RCLCPP_INFO(this->get_logger(), "设置LED模式: 粉色流水");
            break;            

        case DEEP_CMD_LIGHT_03:  // 0x00000403 白色呼吸
            mode = "breath";
            color = "#FFFFFF"; // 白色
            effect_speed = 1.0;
            brightness = 0.8;
            RCLCPP_INFO(this->get_logger(), "设置LED模式: 白色呼吸");
            break;
            
        case DEEP_CMD_LIGHT_04:  // 0x00000401 白色常亮 中立（neutral）
            mode = "static";
            color = "#FFFFFF"; // 白色
            brightness = 0.8;
            RCLCPP_INFO(this->get_logger(), "设置LED模式: 白色常亮 (中立)");
            break;

        case DEEP_CMD_LIGHT_WHITE_BLINKING:  // 0x00000403 白色闪烁
            mode = "breath";
            color = "#FFFFFF"; // 白色
            effect_speed = 2.0;
            brightness = 0.8;
            RCLCPP_INFO(this->get_logger(), "设置LED模式: 白色闪烁");
            break;            

        case DEEP_CMD_LIGHT_WHITE_RUNNING:  // 0x00000403 白色流水
            mode = "flow";
            color = "#FFFFFF"; // 白色
            effect_speed = 1.5;
            brightness = 0.8;
            RCLCPP_INFO(this->get_logger(), "设置LED模式: 白色流水");
            break;             

        case DEEP_CMD_LIGHT_07:  // 0x00000101 黄色常亮
            mode = "static";
            color = "#FFFF00"; // 黄色
            brightness = 0.8;
            RCLCPP_INFO(this->get_logger(), "设置LED模式: 黄色常亮");
            break;

        case DEEP_CMD_LIGHT_YELLOW_BLINKING:  // 0x00000101 黄色闪烁
            mode = "blink";
            color = "#FFFF00"; // 黄色
            effect_speed = 2.0;
            brightness = 0.8;
            RCLCPP_INFO(this->get_logger(), "设置LED模式: 黄色闪烁");
            break;

        case DEEP_CMD_LIGHT_YELLOW_BREATHING:  // 0x00000101 黄色呼吸
            mode = "breath";
            color = "#FFFF00"; // 黄色
            effect_speed = 1.0;
            brightness = 0.8;
            RCLCPP_INFO(this->get_logger(), "设置LED模式: 黄色呼吸");
            break;

        case DEEP_CMD_LIGHT_YELLOW_RUNNING:  // 0x00000101 黄色流水
            mode = "flow";
            color = "#FFFF00"; // 黄色
            effect_speed = 1.5;
            brightness = 0.8;
            RCLCPP_INFO(this->get_logger(), "设置LED模式: 黄色流水");
            break;


        case DEEP_CMD_LIGHT_08:  // 0x00000503 蓝色呼吸 悲伤（sad）
            mode = "breath";
            color = "#0000FF"; // 蓝色
            effect_speed = 1.0;
            brightness = 0.8;
            RCLCPP_INFO(this->get_logger(), "设置LED模式: 蓝色呼吸 (悲伤)");
            break;

        case DEEP_CMD_LIGHT_BLUE_KEEP_AWAKE:  // 0x00000503 蓝色常亮
            mode = "static";
            color = "#0000FF"; // 蓝色
            brightness = 0.8;
            RCLCPP_INFO(this->get_logger(), "设置LED模式: 蓝色常亮");
            break;            

        case DEEP_CMD_LIGHT_BLUE_BLINKING:  // 0x00000503 蓝色闪烁
            mode = "blink";
            color = "#0000FF"; // 蓝色
            effect_speed = 2.0;
            brightness = 0.8;
            RCLCPP_INFO(this->get_logger(), "设置LED模式: 蓝色闪烁");
            break;            
        case DEEP_CMD_LIGHT_BLUE_RUNNING:  // 0x00000503 蓝色流水
            mode = "flow";
            color = "#0000FF"; // 蓝色
            effect_speed = 1.5;
            brightness = 0.8;
            RCLCPP_INFO(this->get_logger(), "设置LED模式: 蓝色流水");
            break;

        case 0: // 关闭灯光
            mode = "off";
            RCLCPP_INFO(this->get_logger(), "设置LED模式: 关闭");
            break;
            
        default: // 默认白灯
            mode = "static";
            color = "#FFFFFF"; // 白色
            RCLCPP_INFO(this->get_logger(), "设置LED模式: 未知命令 0x%X, 使用默认白灯", send_cmd);
            break;
    }

    // 构造并发送灯光请求
    Json::Value request_json;
    request_json["command"] = "set_led_mode";
    request_json["mode"] = mode;
    request_json["color"] = color;
    request_json["effect_speed"] = effect_speed;
    request_json["brightness"] = brightness;

    // 如果是渐变模式，添加次要颜色参数
    if (mode == "gradient" && !secondary_color.empty()) {
        request_json["secondary_color"] = secondary_color;
    }

    // 序列化JSON
    Json::StreamWriterBuilder writer;
    std::string json_str = Json::writeString(writer, request_json);
    
    RCLCPP_INFO(this->get_logger(), "发送灯光控制请求: %s", json_str.c_str());

    // 创建请求
    auto request = std::make_shared<homi_speech_interface::srv::PeripheralsCtrl::Request>();
    request->data = json_str;

    auto future = peripherals_ctrl_client_->async_send_request(
        request,
        [this](rclcpp::Client<homi_speech_interface::srv::PeripheralsCtrl>::SharedFuture future) {
            try {
                auto response = future.get();
                if (response->error_code) {
                    RCLCPP_INFO(this->get_logger(), "灯光控制成功: %s", response->result.c_str());
                } else {
                    RCLCPP_ERROR(this->get_logger(), "灯光控制失败: %s", response->result.c_str());
                }
            } catch (const std::exception &e) {
                RCLCPP_ERROR(this->get_logger(), "调用灯光控制服务异常: %s", e.what());
            }
        }
    );

    // 设置定时器恢复默认灯光
    if (duration > 0) {
        auto duration_sec = std::chrono::seconds(duration);
        restore_timer_ = this->create_wall_timer(
            duration_sec,
            [this]() {
                RCLCPP_INFO(this->get_logger(), "灯光持续时间结束，恢复默认灯光");
                this->sendDefaultLight();
            }
        );
        RCLCPP_INFO(this->get_logger(), "已设置灯光持续时间: %d 秒", duration);
    }
}

void RobdogCtrlNode::sendDefaultLight() {
    // 构造默认灯光请求（白色常亮）
    Json::Value request_json;
    request_json["command"] = "set_led_mode";
    request_json["mode"] = "static";
    request_json["color"] = "#FFFFFF"; // 白色
    request_json["brightness"] = 0.8;
    
    Json::StreamWriterBuilder writer;
    std::string json_str = Json::writeString(writer, request_json);
    
    RCLCPP_INFO(this->get_logger(), "恢复默认灯光: %s", json_str.c_str());
    
    auto request = std::make_shared<homi_speech_interface::srv::PeripheralsCtrl::Request>();
    request->data = json_str;

    peripherals_ctrl_client_->async_send_request(request);
}

void RobdogCtrlNode::peripherals_set_fan_speed(int speed) {
  if (!peripherals_ctrl_client_->wait_for_service(1s)) {
    RCLCPP_WARN(this->get_logger(), "Service /robdog_control/peripherals_ctrl not available.");
    return;
  }
  try {

    Json::Value request_json;

    request_json["command"] = "set_fan_speed";
    request_json["speed"] = speed;
    RCLCPP_WARN(this->get_logger(), "testing send speed: %d", speed);
    Json::StreamWriterBuilder writer;
    const std::string json_str = Json::writeString(writer, request_json);

    auto request = std::make_shared<homi_speech_interface::srv::PeripheralsCtrl::Request>();
    request->data = json_str;

    auto future = peripherals_ctrl_client_->async_send_request(
      request,
      [this](rclcpp::Client<homi_speech_interface::srv::PeripheralsCtrl>::SharedFuture result) {
        try {
          auto response = result.get();

          Json::Value response_json;
          Json::CharReaderBuilder reader;
          std::string errors;
          std::istringstream stream(response->result);

          if (Json::parseFromStream(reader, stream, &response_json, &errors)) {
            if (response_json.get("success", false).asBool()) {
              RCLCPP_INFO(this->get_logger(), "Fan speed updated: %d\%",
                        response_json["speed"].asInt());
            } else {
              RCLCPP_ERROR(this->get_logger(), "Service error: %s", 
                         response_json["error"].asString().c_str());
            }
          } else {
            RCLCPP_ERROR(this->get_logger(), "Response parse error: %s", errors.c_str());
          }
        } catch (const std::exception &e) {
          RCLCPP_ERROR(this->get_logger(), "Callback error: %s", e.what());
        }
      }
    );

  } catch (const Json::Exception& e) {
    RCLCPP_ERROR(this->get_logger(), "JSON error: %s", e.what());
  } catch (const std::exception& e) {
    RCLCPP_ERROR(this->get_logger(), "System error: %s", e.what());
  }
}
