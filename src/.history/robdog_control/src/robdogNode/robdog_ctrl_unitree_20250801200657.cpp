#include "public/tools.h"
#include "robotMgr/robot_info_mgr.h" // 为了读取状态
#include "robdog_ctrl_node.h"

// 读控制指令类型
UtCtrlType_E RobDog_Ctrl_Unitree::utRdCtrlType()
{
    std::shared_lock<std::shared_mutex> lock(utTypeMutex);

    return utCtrlType;
}

// 写控制指令类型
void RobDog_Ctrl_Unitree::utWrCtrlType(UtCtrlType_E type)
{
    std::unique_lock<std::shared_mutex> lock(utTypeMutex);

    utCtrlType = type;

    return;
}

// 读步态信息
RobdogCtrlGait RobDog_Ctrl_Unitree::utRdCtrlGait()
{
    std::shared_lock<std::shared_mutex> lock(utGaitMutex);

    return utMoveGait;
}

// 写步态信息
void RobDog_Ctrl_Unitree::utWrCtrlGait(RobdogCtrlGait gait)
{
    std::unique_lock<std::shared_mutex> lock(utGaitMutex);

    utMoveGait = gait;

    return;
}

// 读动作信息
RobdogCtrlMotion RobDog_Ctrl_Unitree::utRdCtrlMotion()
{
    std::shared_lock<std::shared_mutex> lock(utMotionMutex);

    return utLocomotion;
}

// 写动作信息
void RobDog_Ctrl_Unitree::utWrCtrlMotion(RobdogCtrlMotion motion)
{
    std::unique_lock<std::shared_mutex> lock(utMotionMutex);

    utLocomotion = motion;

    return;
}

// 开启避障服务
int32_t RobDog_Ctrl_Unitree::utAvoidOpen()
{
    int32_t ret = ROBDOGCTRL_ERROR_SUCCESS;
    int32_t status = 0;

    RCLCPP_INFO(node_ctrl_->get_logger(), "Enter Function: %s", __func__);

    utAvoidSwitch = true;

    ret = utRsc.ServiceSwitch("obstacles_avoid", 1, status);
    if (ret != ROBDOGCTRL_ERROR_SUCCESS)
    {
        RCLCPP_INFO(node_ctrl_->get_logger(), "obstacles_avoid on failed! ret = %d, status = %d", ret, status);
        return ROBDOGCTRL_ERROR_FAILED;
    }

    ret = ov_client.UseRemoteCommandFromApi(true);
    if (ret != ROBDOGCTRL_ERROR_SUCCESS)
    {
        RCLCPP_INFO(node_ctrl_->get_logger(), "UseRemoteCommandFromApi failed! ret = %d", ret);
        return ROBDOGCTRL_ERROR_FAILED;
    }
    
    return ROBDOGCTRL_ERROR_SUCCESS;
}

// 关闭避障服务
int32_t RobDog_Ctrl_Unitree::utAvoidClose()
{
    int32_t ret = ROBDOGCTRL_ERROR_SUCCESS;
    int32_t status = 0;

    RCLCPP_INFO(node_ctrl_->get_logger(), "Enter Function: %s", __func__);

    utAvoidSwitch = false;

    ret = ov_client.UseRemoteCommandFromApi(false);
    if (ret != ROBDOGCTRL_ERROR_SUCCESS)
    {
        RCLCPP_INFO(node_ctrl_->get_logger(), "UseRemoteCommandFromApi failed! ret = %d", ret);
        return ROBDOGCTRL_ERROR_FAILED;
    }

    ret = utRsc.ServiceSwitch("obstacles_avoid", 0, status);
    if (ret != ROBDOGCTRL_ERROR_SUCCESS)
    {
        RCLCPP_INFO(node_ctrl_->get_logger(), "obstacles_avoid off failed! ret = %d, status = %d", ret, status);
        return ROBDOGCTRL_ERROR_FAILED;
    }

    return ROBDOGCTRL_ERROR_SUCCESS;
}

void RobDog_Ctrl_Unitree::utSetCtrlPara(const std_msgs::msg::String::SharedPtr msg)
{
    const std::string& dema_cmd = msg->data;
    int32_t ret = ROBDOGCTRL_ERROR_SUCCESS;

    // ros2 topic pub /robdog_control/unitree_para std_msgs/msg/String 'data: "joystick_on"' -1
    RCLCPP_INFO(node_ctrl_->get_logger(), "================= start ================");
    RCLCPP_INFO(node_ctrl_->get_logger(), "========================================");
    RCLCPP_INFO(node_ctrl_->get_logger(), "Unitree SetCtrlPara: %s", dema_cmd.c_str());
    RCLCPP_INFO(node_ctrl_->get_logger(), "========================================");

    if (dema_cmd == "imu1")
    {
        RCLCPP_INFO(node_ctrl_->get_logger(), "Unitree SetCtrlPara: imu1");
        ret = robdogCtrl_IMUDemaStage1();
    }
    else if (dema_cmd == "imu2")
    {
        RCLCPP_INFO(node_ctrl_->get_logger(), "Unitree SetCtrlPara: imu2");
        ret = robdogCtrl_IMUDemaStage2();
    }
    else if (dema_cmd == "joint1")
    {
        RCLCPP_INFO(node_ctrl_->get_logger(), "Unitree SetCtrlPara: joint1");
        ret = robdogCtrl_JointDemaStage1();
    }
    else if (dema_cmd == "joint2")
    {
        RCLCPP_INFO(node_ctrl_->get_logger(), "Unitree SetCtrlPara: joint2");
        ret = robdogCtrl_JointDemaStage2();
    }
    else if (dema_cmd == "joint3")
    {
        RCLCPP_INFO(node_ctrl_->get_logger(), "Unitree SetCtrlPara: joint3");
        ret = robdogCtrl_JointDemaStage3();
    }
    else if (dema_cmd == "joint4")
    {
        RCLCPP_INFO(node_ctrl_->get_logger(), "Unitree SetCtrlPara: joint4");
        ret = robdogCtrl_JointDemaStage4();
    }
    else if (dema_cmd == "joystick_on")
    {
        RCLCPP_INFO(node_ctrl_->get_logger(), "Unitree SetCtrlPara: joystick_on");
        ret = utJoystickSwitch = true;
    }
    else if (dema_cmd == "joystick_off")
    {
        RCLCPP_INFO(node_ctrl_->get_logger(), "Unitree SetCtrlPara: joystick_off");
        ret = utJoystickSwitch = false;
    }
    else if (dema_cmd == "avoid_on")
    {
        RCLCPP_INFO(node_ctrl_->get_logger(), "Unitree SetCtrlPara: avoid_on");
        ret = utAvoidOpen();
    }
    else if (dema_cmd == "avoid_off")
    {
        RCLCPP_INFO(node_ctrl_->get_logger(), "Unitree SetCtrlPara: avoid_off");
        ret = utAvoidClose();
    }
    else if (dema_cmd == "speedlevel_low")
    {
        RCLCPP_INFO(node_ctrl_->get_logger(), "Unitree SetCtrlPara: speedlevel_low");
        utSetSpeedLevel(-1);
    }
    else if (dema_cmd == "speedlevel_high")
    {
        RCLCPP_INFO(node_ctrl_->get_logger(), "Unitree SetCtrlPara: speedlevel_high");
        utSetSpeedLevel(1);
    }

    if (ret != ROBDOGCTRL_ERROR_SUCCESS)
    {
        RCLCPP_INFO(node_ctrl_->get_logger(), "Unitree SetCtrlPara Error.");
    }

    RCLCPP_INFO(node_ctrl_->get_logger(), "================= end ================");

    return;
}

// 设置速度档位
void RobDog_Ctrl_Unitree::utSetSpeedLevel(int32_t level)
{
    int32_t ret = ROBDOGCTRL_ERROR_SUCCESS;

    // 设置速度档位
    RCLCPP_INFO(node_ctrl_->get_logger(), "SpeedLevel Set %d!", level);
    ret = utSc.SpeedLevel(level);
    if (ret != ROBDOGCTRL_ERROR_SUCCESS)
    {
        RCLCPP_ERROR(node_ctrl_->get_logger(), "Speed Level Set failed! ret = %d", ret);
    }

    return;
}

// 速度清零
void RobDog_Ctrl_Unitree::utSpeedClear()
{
    utLastX = 0.0;
    utLastY = 0.0;
    utLastYaw = 0.0;
	
    utJoystickMsg.lx(0.0);
    utJoystickMsg.ly(0.0);
    utJoystickMsg.rx(0.0);
    utJoystickMsg.ry(0.0);

    return;
}

// 等待站立动作完成
bool RobDog_Ctrl_Unitree::utStandUpWait()
{
    int cnt = 0;

    while((false == RobotInfoMgr::getInstance().utStandCheck()) && (cnt < 60))
    {
        RCLCPP_INFO(node_ctrl_->get_logger(), "Wait Stand: %d", cnt);
        std::this_thread::sleep_for(std::chrono::milliseconds(200));
        cnt++;
    }

    if (cnt == 60)
    {
        RCLCPP_INFO(node_ctrl_->get_logger(), "Not Stand Up.");
        return false;
    }

    RCLCPP_INFO(node_ctrl_->get_logger(), "Stand Up Now.");

    return true;
}

// 检查步态是否与用户设置一致
bool RobDog_Ctrl_Unitree::utGaitCheck()
{
    HomiUtRobotStatus enLastStatus = UT_ROBDOG_STATUS_STATE;
    RobdogCtrlGait gait;

    // 获取步态设置信息
    gait = utRdCtrlGait();

    // 获取状态信息
    enLastStatus = RobotInfoMgr::getInstance().getUtRobotStatus();

    // 检查设置步态（常规步行）是否与状态一致
    if ((enLastStatus == UT_ROBDOG_STATUS_STATIC_WALK) && 
        (gait == ROBDOGCTRL_GAIT_WALK || gait == ROBDOGCTRL_GAIT_EXIT))
    {
        return true;
    }

    // 检查设置步态（常规跑步）是否与状态一致
    if ((enLastStatus == UT_ROBDOG_STATUS_TROT_RUN) && 
        (gait == ROBDOGCTRL_GAIT_RUN))
    {
        return true;
    }

    // 检查设置步态（经典）是否与状态一致
    if ((enLastStatus == UT_ROBDOG_STATUS_CLASSIC) && 
        (gait == ROBDOGCTRL_GAIT_AICLASSIC))
    {
        return true;
    }

    // 检查设置步态（灵动）是否与状态一致
    if ((enLastStatus == UT_ROBDOG_STATUS_FREE_WALK) && 
        (gait == ROBDOGCTRL_GAIT_AINIMBLE))
    {
        return true;
    }

    // 检查设置步态（跳跃跑）是否与状态一致
    if ((enLastStatus == UT_ROBDOG_STATUS_FREE_JUMP) && 
        (gait == ROBDOGCTRL_GAIT_FREEJUMP))
    {
        return true;
    }

    // 检查设置步态（并腿跑）是否与状态一致
    if ((enLastStatus == UT_ROBDOG_STATUS_FREE_BOUND) && 
        (gait == ROBDOGCTRL_GAIT_FREEBOUND))
    {
        return true;
    }

    RCLCPP_INFO(node_ctrl_->get_logger(), "Gait State Not Match With User Set.");

    return false;
}

// 起立
int32_t RobDog_Ctrl_Unitree::utStandUpProc(bool changeGait)
{
    std::string form;
    std::string name;
    int32_t ret = ROBDOGCTRL_ERROR_SUCCESS;

    RCLCPP_INFO(node_ctrl_->get_logger(), "Thread Enter Function: %s", __func__);

    // 已经站立
    if (true == RobotInfoMgr::getInstance().utStandCheck())
    {
        RCLCPP_INFO(node_ctrl_->get_logger(), "Already Standup Now!");
        return ROBDOGCTRL_ERROR_SUCCESS;
    }

    // 正在执行动作或在运动状态，退出
    if ((true == RobotInfoMgr::getInstance().utLocomotionCheck()) || 
        (true == RobotInfoMgr::getInstance().utMoveCheck()))
    {
        RCLCPP_INFO(node_ctrl_->get_logger(), "Moving or Locomotion!");
        return ROBDOGCTRL_ERROR_INVALID_STATE;
    }

    if (true == RobotInfoMgr::getInstance().utSitCheck())
    {
        // 从坐下状态恢复到平衡站立
        ret = utSc.RiseSit();
        if (ret != 0)
        {
            RCLCPP_ERROR(node_ctrl_->get_logger(), "RiseSit Action failed! ret = %d", ret);
            return ret;
        }
    }
    else
    {
        // 从翻倒或趴下状态恢复至平衡站立状态。不论是否翻倒，都会恢复至站立。
        RCLCPP_INFO(node_ctrl_->get_logger(), "Recover Stand Up!");
        ret = utSc.RecoveryStand();
        if (ret != ROBDOGCTRL_ERROR_SUCCESS)
        {
            RCLCPP_ERROR(node_ctrl_->get_logger(), "RecoveryStand Action failed! ret = %d", ret);
            return ret;
        }
    }

    // 狗子趴下站起来默认为灵动模式
    // 常规模式下，做完动作，默认为平衡站立，行走为原步态（趴下站起来除外）
    // ai步态下，做完动作，默认为平衡站立，行走为灵动步态（趴下站起来除外）

    // 检查是否站立成功
    if (false == utStandUpWait())
    {
        RCLCPP_ERROR(node_ctrl_->get_logger(), "Wait Standup Error");
        return ROBDOGCTRL_ERROR_FAILED;
    }

    // 是否需要切换步态
    if(true == changeGait)
    {
        ret = utChangeGaitProc();
        if (ret != ROBDOGCTRL_ERROR_SUCCESS)
        {
            RCLCPP_ERROR(node_ctrl_->get_logger(), "Change Gait Proc Failed! ret = %d", ret);
        }
    }

    RCLCPP_INFO(node_ctrl_->get_logger(), "Standup Proc Success!");

    return ROBDOGCTRL_ERROR_SUCCESS;
}

// 趴下
int32_t RobDog_Ctrl_Unitree::utGetDownProc()              
{
    int32_t ret = ROBDOGCTRL_ERROR_SUCCESS;

    RCLCPP_INFO(node_ctrl_->get_logger(), "Thread Enter Function: %s", __func__);

    // 已经趴下
    if (true == RobotInfoMgr::getInstance().utStandDownCheck())
    {
        RCLCPP_INFO(node_ctrl_->get_logger(), "Already Stand Down!");
        return ROBDOGCTRL_ERROR_SUCCESS;
    }

    // 正在运动，或者做动作，不执行趴下
    if ((true == RobotInfoMgr::getInstance().utMoveCheck()) || (true == RobotInfoMgr::getInstance().utLocomotionCheck()))
    {
        RCLCPP_INFO(node_ctrl_->get_logger(), "Invalid State: Moving or Locomotion!");
        return ROBDOGCTRL_ERROR_INVALID_STATE;
    }

    // 非站立状态，先站立
    ret = utStandUpProc(false);
    if (ret != ROBDOGCTRL_ERROR_SUCCESS)
    {
        RCLCPP_ERROR(node_ctrl_->get_logger(), "Stand Up failed! ret = %d", ret);
        return ret;
    }

    // 趴下
    ret = utSc.StandDown();
    if (ret != ROBDOGCTRL_ERROR_SUCCESS)
    {
        RCLCPP_ERROR(node_ctrl_->get_logger(), "StandDown Action failed! ret = %d", ret);
        return ret;
    }

    RCLCPP_INFO(node_ctrl_->get_logger(), "StandDown Proc Success!");
    
    return ROBDOGCTRL_ERROR_SUCCESS;
}

// 坐下
int32_t RobDog_Ctrl_Unitree::utSitProc()       	        
{
    int32_t ret = ROBDOGCTRL_ERROR_SUCCESS;

    RCLCPP_INFO(node_ctrl_->get_logger(), "Thread Enter Function: %s", __func__);

    // 已坐下，无需处理
    if (true == RobotInfoMgr::getInstance().utSitCheck())
    {
        RCLCPP_INFO(node_ctrl_->get_logger(), "Already Sit!");
        return ROBDOGCTRL_ERROR_SUCCESS;
    }

    // 正在运动，或者做动作，不执行坐下
    if (true == RobotInfoMgr::getInstance().utLocomotionCheck() || true == RobotInfoMgr::getInstance().utMoveCheck())
    {
        RCLCPP_INFO(node_ctrl_->get_logger(), "Invalid State: Moving or Locomotion!");
        return ROBDOGCTRL_ERROR_INVALID_STATE;
    }

    // 非站立状态，先站立
    ret = utStandUpProc(false);
    if (ret != ROBDOGCTRL_ERROR_SUCCESS)
    {
        RCLCPP_ERROR(node_ctrl_->get_logger(), "Stand Up failed! ret = %d", ret);
        return ret;
    }

    // 坐下
    ret = utSc.Sit();
    if (ret != ROBDOGCTRL_ERROR_SUCCESS)
    {
        RCLCPP_ERROR(node_ctrl_->get_logger(), "Sit Action failed! ret = %d", ret);
        return ret;
    }

    RCLCPP_INFO(node_ctrl_->get_logger(), "Sit Proc Success!");

    // 坐下8秒后站起来
    std::this_thread::sleep_for(std::chrono::milliseconds(8000));
    ret = utStandUpProc(true);
    if (ret != ROBDOGCTRL_ERROR_SUCCESS)
    {
        RCLCPP_ERROR(node_ctrl_->get_logger(), "Stand Up failed! ret = %d", ret);
        return ret;
    }

    return ROBDOGCTRL_ERROR_SUCCESS;
}

// 动作处理函数
int32_t RobDog_Ctrl_Unitree::utLocomotionProc()
{
    int32_t ret = ROBDOGCTRL_ERROR_SUCCESS;
    RobdogCtrlMotion motion = ROBDOGCTRL_MOTION_INVALID;

    RCLCPP_INFO(node_ctrl_->get_logger(), "Thread Enter Function: %s", __func__);

    // 获取用户下发动作
    motion = utRdCtrlMotion();

    // 无效或者不支持动作，直接返回
    if ((motion == ROBDOGCTRL_MOTION_INVALID) || (motion == ROBDOGCTRL_MOTION_TURNOVER) ||
        (motion == ROBDOGCTRL_MOTION_TWISTJUMP) || (motion == ROBDOGCTRL_MOTION_BACKFLIP))
    {
        utWrCtrlMotion(ROBDOGCTRL_MOTION_INVALID);
        return ROBDOGCTRL_ERROR_SUCCESS;
    }

    //正在做动作或运动中
    if ((true == RobotInfoMgr::getInstance().utLocomotionCheck()) || (true == RobotInfoMgr::getInstance().utMoveCheck()))
    {
        RCLCPP_INFO(node_ctrl_->get_logger(), "Already Locomotion or Moving!");
        utWrCtrlMotion(ROBDOGCTRL_MOTION_INVALID);
        return ROBDOGCTRL_ERROR_INVALID_STATE;
    }

    // 非站立状态，先站立
    ret = utStandUpProc(false);
    if (ret != ROBDOGCTRL_ERROR_SUCCESS)
    {
        RCLCPP_ERROR(node_ctrl_->get_logger(), "Stand Up failed! ret = %d", ret);
        utWrCtrlMotion(ROBDOGCTRL_MOTION_INVALID);
        return ret;
    }

    //做动作
    switch(motion)
    {
        case ROBDOGCTRL_MOTION_TWIST:            //扭身体  -->开心
        {
            RCLCPP_INFO(node_ctrl_->get_logger(), "Locomotion: Twist!");
            ret = utSc.Content();
            break;
        }
        case ROBDOGCTRL_MOTION_DANCE:            // 跳舞
        {
            RCLCPP_INFO(node_ctrl_->get_logger(), "Locomotion: Dance!");
            ret = utSc.Dance1();
            break;
        }
        case ROBDOGCTRL_MOTION_FINGERHEART:      //比心
        {
            RCLCPP_INFO(node_ctrl_->get_logger(), "Locomotion: Finger Heart!");
            ret = utSc.Heart();
            break;
        }
        case ROBDOGCTRL_MOTION_HELLO:            //打招呼
        {
            RCLCPP_INFO(node_ctrl_->get_logger(), "Locomotion: Hello!");
            ret = utSc.Hello();
            break;
        }
        case ROBDOGCTRL_MOTION_STRETCH:          //伸懒腰
        {
            RCLCPP_INFO(node_ctrl_->get_logger(), "Locomotion: Stretch!");
            ret = utSc.Stretch();
            break;
        }
        case ROBDOGCTRL_MOTION_NEWYEARCALL:      //拜年
        {
            RCLCPP_INFO(node_ctrl_->get_logger(), "Locomotion: New Year Call!");
            ret = utSc.Scrape();
            break;
        }
        case ROBDOGCTRL_MOTION_HAPPY:            //开心
        {
            RCLCPP_INFO(node_ctrl_->get_logger(), "Locomotion: Happy!");
            ret = utSc.Content();
            break;
        }
        case ROBDOGCTRL_MOTION_JUMPFORWARD:      //向前跳
        {
            RCLCPP_INFO(node_ctrl_->get_logger(), "Locomotion: Jump Forward!");
            ret = utSc.FrontJump();
            break;
        }
        case ROBDOGCTRL_MOTION_LEAP:             //向前扑人
        {
            RCLCPP_INFO(node_ctrl_->get_logger(), "Locomotion: Front Pounce!");
            ret = utSc.FrontPounce();
            break;
        }
        case ROBDOGCTRL_MOTION_DANCEV2:          //跳舞2
        {
            RCLCPP_INFO(node_ctrl_->get_logger(), "Locomotion: Dance v2!");
            ret = utSc.Dance2();
            break;
        }
        case ROBDOGCTRL_MOTION_WALKUPRIGHT:       //站立
        {
            RCLCPP_INFO(node_ctrl_->get_logger(), "Locomotion: Walk Upright!");
            ret = utSc.WalkUpright(true);
            std::this_thread::sleep_for(std::chrono::milliseconds(3000));
            ret = utSc.WalkUpright(false);
            break;
        }
        case ROBDOGCTRL_MOTION_HANDSTAND:         //倒立
        {
            RCLCPP_INFO(node_ctrl_->get_logger(), "Locomotion: Hand Stand!");
            ret = utSc.HandStand(true);
            std::this_thread::sleep_for(std::chrono::milliseconds(3000));
            ret = utSc.HandStand(false);
            break;
        }
        default:
        {
            RCLCPP_ERROR(node_ctrl_->get_logger(), "Invalid Sport Motion! motion = %d", motion);
            utWrCtrlMotion(ROBDOGCTRL_MOTION_INVALID);
            return ROBDOGCTRL_ERROR_INVALID_PARA;
        }
    }

    if (ret != ROBDOGCTRL_ERROR_SUCCESS)
    {
        RCLCPP_ERROR(node_ctrl_->get_logger(), "Local Motion failed! ret = %d, motion = %d", ret, motion);
        utWrCtrlMotion(ROBDOGCTRL_MOTION_INVALID);
        return ret;
    }

    // 检查是否站立成功
    if (false == utStandUpWait())
    {
        RCLCPP_ERROR(node_ctrl_->get_logger(), "Wait Standup Error");
        utWrCtrlMotion(ROBDOGCTRL_MOTION_INVALID);
        return ROBDOGCTRL_ERROR_INVALID_STATE;
    }

    // 执行完动作恢复步态
    ret = utChangeGaitProc();
    if (ret != ROBDOGCTRL_ERROR_SUCCESS)
    {
        RCLCPP_ERROR(node_ctrl_->get_logger(), "RecoveryStand Action failed! ret = %d", ret);
        utWrCtrlMotion(ROBDOGCTRL_MOTION_INVALID);
        return ret;
    }

    utWrCtrlMotion(ROBDOGCTRL_MOTION_INVALID);

    RCLCPP_INFO(node_ctrl_->get_logger(), "Locomotion Proc Success!");

    return ROBDOGCTRL_ERROR_SUCCESS;
}

// 步态切换处理
int32_t RobDog_Ctrl_Unitree::utChangeGaitProc()
{
    int32_t ret = ROBDOGCTRL_ERROR_SUCCESS;
    RobdogCtrlGait gait;

    RCLCPP_INFO(node_ctrl_->get_logger(), "Thread Enter Function: %s", __func__);

    // 趴下、坐下或者执行动作，不能切换步态
    if ((true == RobotInfoMgr::getInstance().utStandDownCheck()) || 
        (true == RobotInfoMgr::getInstance().utSitCheck()) || 
        (true == RobotInfoMgr::getInstance().utLocomotionCheck()))
    {
        RCLCPP_INFO(node_ctrl_->get_logger(), "Stand Down, Sit or Locomotion. Can not Change Gait!");
        return ROBDOGCTRL_ERROR_INVALID_STATE;
    }

    // 获取用户设置步态信息
    gait = utRdCtrlGait();
    if (gait == ROBDOGCTRL_GAIT_INVALID)
    {
        RCLCPP_ERROR(node_ctrl_->get_logger(), "Invalid Gait!");
        return ROBDOGCTRL_ERROR_INVALID_PARA;
    }

    // 检查当前步态和设置步态是否一致
    if (true == utGaitCheck())
    {
        RCLCPP_INFO(node_ctrl_->get_logger(), "Gait Match!");
        return ROBDOGCTRL_ERROR_SUCCESS;
    }

    switch(gait)
    {
        case ROBDOGCTRL_GAIT_WALK:
        {
            RCLCPP_INFO(node_ctrl_->get_logger(), "Change Walk Gait!");
            ret = utSc.StaticWalk();
            utSpeedGait = UT_SPEED_GAIT_WALK;
            break;
        }
        case ROBDOGCTRL_GAIT_RUN:
        {
            RCLCPP_INFO(node_ctrl_->get_logger(), "Change Run Gait!");
            ret = utSc.TrotRun();
            utSpeedGait = UT_SPEED_GAIT_RUN;
            break;
        }
        case ROBDOGCTRL_GAIT_AICLASSIC:
        {
            RCLCPP_INFO(node_ctrl_->get_logger(), "Change AI Classic Gait!");
            ret = utSc.ClassicWalk(true);
            utSpeedGait = UT_SPEED_GAIT_AICLASSIC;
            utSetSpeedLevel(utSpeedLevel);
            break;
        }
        case ROBDOGCTRL_GAIT_AINIMBLE:
        {
            RCLCPP_INFO(node_ctrl_->get_logger(), "Change AI Flex Gait!");
            ret = utSc.FreeWalk();
            utSpeedGait = UT_SPEED_GAIT_AINIMBLE;
            utSetSpeedLevel(utSpeedLevel);
            break;
        }
        case ROBDOGCTRL_GAIT_FREEJUMP:
        {
            RCLCPP_INFO(node_ctrl_->get_logger(), "Change FreeJump Gait!");
            ret = utSc.FreeJump(true);
            utSpeedGait = UT_SPEED_GAIT_FREEJUMP;
            utSetSpeedLevel(utSpeedLevel);
            break;
        }
        case ROBDOGCTRL_GAIT_FREEBOUND:
        {
            RCLCPP_INFO(node_ctrl_->get_logger(), "Change FreeBound Gait!");
            ret = utSc.FreeBound(true);
            utSpeedGait = UT_SPEED_GAIT_FREEBOND;
            utSetSpeedLevel(utSpeedLevel);
            break;
        }
        case ROBDOGCTRL_GAIT_EXIT:
        {
            RCLCPP_INFO(node_ctrl_->get_logger(), "Change exit ai Gait!");
            ret = utSc.StaticWalk();
            utSpeedGait = UT_SPEED_GAIT_WALK;
            break;
        }
        default:
        {
            // 步态信息有误，强制修改为默认步态
            RCLCPP_ERROR(node_ctrl_->get_logger(), "Invalid Gait! gait = %d", gait);
            utWrCtrlGait(ROBDOGCTRL_GAIT_AICLASSIC);
            return ROBDOGCTRL_ERROR_INVALID_PARA;
        }
    }

    // 检测步态切换是否成功
    if (ret != ROBDOGCTRL_ERROR_SUCCESS)
    {
        RCLCPP_ERROR(node_ctrl_->get_logger(), "Change Gait failed! ret = %d, gait = %d", ret, gait);
        return ret;
    }

    RCLCPP_INFO(node_ctrl_->get_logger(), "Change Gait Proc Success!");

    return ROBDOGCTRL_ERROR_SUCCESS;
}

int32_t RobDog_Ctrl_Unitree::utMoveProc(bool joystick, float x, float y, float yaw)
{
    int32_t ret = ROBDOGCTRL_ERROR_SUCCESS;
    UtCtrlType_E ctrlType = UT_CTRL_TYPE_INVALID;

    // 存在关节温度过热
    if (RobotInfoMgr::getInstance().utCheckTempStatus())
    {
        RCLCPP_INFO(node_ctrl_->get_logger(), "Over Heat, Can not Move!");
        return ROBDOGCTRL_ERROR_INVALID_STATE;
    }

    // 检查是否有其他动作在执行
    ctrlType = utRdCtrlType();
    if (ctrlType != UT_CTRL_TYPE_INVALID)
    {
        RCLCPP_INFO(node_ctrl_->get_logger(), "Doing other action! ctrlType: %d", ctrlType);
        return ROBDOGCTRL_ERROR_INVALID_STATE;
    }

    ret = utChangeGaitProc();
    if (ret != ROBDOGCTRL_ERROR_SUCCESS)
    {
        RCLCPP_ERROR(node_ctrl_->get_logger(), "utChangeGaitProc failed! ret = %d", ret);
        return ret;
    }

    if (utJoystickSwitch)
    {
        utJoystickMsg.lx(y);
        utJoystickMsg.ly(x);
        utJoystickMsg.rx(yaw);

        RCLCPP_INFO(node_ctrl_->get_logger(), "====== JoyStick: x: %f, y: %f, yaw: %f ======", 
                    utJoystickMsg.lx(), utJoystickMsg.ly(), utJoystickMsg.rx());

        utPubJoystick->Write(utJoystickMsg);
    }
    else
    {
        if (utAvoidSwitch == true)
        {
            RCLCPP_INFO(node_ctrl_->get_logger(), "====== avoid on, Move: x: %f, y: %f, yaw: %f ======", x, y, yaw);
            ret = ov_client.Move(x, y, yaw);
        }
        else
        {
            RCLCPP_INFO(node_ctrl_->get_logger(), "====== avoid off, Move: x: %f, y: %f, yaw: %f ======", x, y, yaw);
            ret = utSc.Move(x, y, yaw);
        }

        if (ret != ROBDOGCTRL_ERROR_SUCCESS)
        {
            RCLCPP_ERROR(node_ctrl_->get_logger(), "Move Action failed! ret = %d", ret);
            return ret;
        }
    }

    return ROBDOGCTRL_ERROR_SUCCESS;
}

// 宇树线程处理函数
int32_t RobDog_Ctrl_Unitree::utThreadProc()
{
    int32_t ret = ROBDOGCTRL_ERROR_SUCCESS;
    UtCtrlType_E ctrlType = UT_CTRL_TYPE_INVALID;

    RCLCPP_INFO(node_ctrl_->get_logger(), "Enter Function: %s", __func__);

    while(1)
    {
        // 等待运控板状态信息更新
        if (false == RobotInfoMgr::getInstance().utConnectWait())
        {
            RCLCPP_ERROR(node_ctrl_->get_logger(), "utThreadProc: Wait Connect Error");
            continue;
        }

        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        ret = ROBDOGCTRL_ERROR_SUCCESS;
        
        // 获取指令类型
        ctrlType = utRdCtrlType();
        if ((ctrlType == UT_CTRL_TYPE_INVALID) || (ctrlType >= UT_CTRL_TYPE_MAX))
        {
            RCLCPP_INFO_THROTTLE(node_ctrl_->get_logger(), *node_ctrl_->get_clock(), 5000, "===== No Action =====");
            continue;
        }

        RCLCPP_INFO(node_ctrl_->get_logger(), "===== Action: ctrlType %d =====", ctrlType);

        // 调用处理函数
        switch (ctrlType)
        {
            case UT_CTRL_TYPE_STANDUP:
            {
                ret = utStandUpProc(true);
                break;
            }
            case UT_CTRL_TYPE_GETDOWN:
            {
                ret = utGetDownProc();
                break;
            }
            case UT_CTRL_TYPE_SIT:
            {
                ret = utSitProc();
                break;
            }
            case UT_CTRL_TYPE_LOCOMOTION:
            {
                ret = utLocomotionProc();
                break;
            }
            case UT_CTRL_TYPE_CHANGE_GAIT:
            {
                ret = utChangeGaitProc();
                break;
            }
            default:
            {
                //do nothing
                break;
            }
        }

        // 错误检查
        if (ret != ROBDOGCTRL_ERROR_SUCCESS)
        {
            RCLCPP_INFO(node_ctrl_->get_logger(), "Unitree Control Thread Proc Error. ctrlType = %d, ret = %d", ctrlType, ret);
        }

        utWrCtrlType(UT_CTRL_TYPE_INVALID);
    }

    return ROBDOGCTRL_ERROR_SUCCESS;
}

int32_t RobDog_Ctrl_Unitree::utThreadInit()
{
    int32_t ret = ROBDOGCTRL_ERROR_SUCCESS;
    int32_t status;

    RCLCPP_INFO(node_ctrl_->get_logger(), "Enter Function: %s", __func__);

    while(1)
    {
        // 等待通信正常
        if (false == RobotInfoMgr::getInstance().utConnectWait())
        {
            RCLCPP_ERROR(node_ctrl_->get_logger(), "utThreadInit: Wait Connect Error");
            continue;
        }

        // 关闭宇树避障服务
        if (utAvoidInitFlag == false)
        {
            ret = utRsc.ServiceSwitch("obstacles_avoid", 0, status);
            if (ret == ROBDOGCTRL_ERROR_SUCCESS)
            {
                utAvoidInitFlag = true;
            }

            RCLCPP_INFO(node_ctrl_->get_logger(), "ServiceSwitch return: %d , status: %d", ret, status);
        }

        // 自动翻身设置为不生效
        if (utAutoRecoverInitFlag == false)
        {
            ret = utSc.AutoRecoverSet(false);
            if (ret == ROBDOGCTRL_ERROR_SUCCESS)
            {
                utAutoRecoverInitFlag = true;
            }

            RCLCPP_INFO(node_ctrl_->get_logger(), "AutoRecoverSet return: %d", ret);
        }

        // 全都设置成功，退出
        if ((utAvoidInitFlag == true) && (utAutoRecoverInitFlag == true))
        {
            break;
        }

        std::this_thread::sleep_for(std::chrono::milliseconds(2000));
    }

    return ROBDOGCTRL_ERROR_SUCCESS;
}

// 检测是否有动作在执行
bool RobDog_Ctrl_Unitree::robdogCtrl_CheckCtrlType()              	
{
    UtCtrlType_E ctrlType = UT_CTRL_TYPE_INVALID;

    RCLCPP_INFO(node_ctrl_->get_logger(), "Enter Function: %s", __func__);

    // 检查是否有其他动作在执行
    ctrlType = utRdCtrlType();
    if (ctrlType != UT_CTRL_TYPE_INVALID)
    {
        return true;
    }

    return false;
}

// 起立
int32_t RobDog_Ctrl_Unitree::robdogCtrl_StandUp()              	
{
    UtCtrlType_E ctrlType = UT_CTRL_TYPE_INVALID;

    RCLCPP_INFO(node_ctrl_->get_logger(), "Enter Function: %s", __func__);

    // 存在关节温度过热
    if (RobotInfoMgr::getInstance().utCheckTempStatus())
    {
        RCLCPP_INFO(node_ctrl_->get_logger(), "robdogCtrl_StandUp: Over Heat, Can not Move!");
        return ROBDOGCTRL_ERROR_INVALID_STATE;
    }

    // 检查是否有其他动作在执行
    ctrlType = utRdCtrlType();
    if (ctrlType != UT_CTRL_TYPE_INVALID)
    {
        RCLCPP_INFO(node_ctrl_->get_logger(), "Doing other action! ctrlType: %d", ctrlType);
        return ROBDOGCTRL_ERROR_INVALID_STATE;
    }

    // 写入站立动作
    utWrCtrlType(UT_CTRL_TYPE_STANDUP);

    return ROBDOGCTRL_ERROR_SUCCESS;
}

// 趴下
int32_t RobDog_Ctrl_Unitree::robdogCtrl_GetDown()              
{
    UtCtrlType_E ctrlType = UT_CTRL_TYPE_INVALID;

    RCLCPP_INFO(node_ctrl_->get_logger(), "Enter Function: %s", __func__);

    // 检查是否有其他动作在执行
    ctrlType = utRdCtrlType();
    if (ctrlType != UT_CTRL_TYPE_INVALID)
    {
        RCLCPP_INFO(node_ctrl_->get_logger(), "Doing other action! ctrlType: %d", ctrlType);
        return ROBDOGCTRL_ERROR_INVALID_STATE;
    }

    // 写入动作
    utWrCtrlType(UT_CTRL_TYPE_GETDOWN);

    return ROBDOGCTRL_ERROR_SUCCESS;
}

// 坐下
int32_t RobDog_Ctrl_Unitree::robdogCtrl_Sit()       	        
{
    UtCtrlType_E ctrlType = UT_CTRL_TYPE_INVALID;

    RCLCPP_INFO(node_ctrl_->get_logger(), "Enter Function: %s", __func__);

    // 存在关节温度过热
    if (RobotInfoMgr::getInstance().utCheckTempStatus())
    {
        RCLCPP_INFO(node_ctrl_->get_logger(), "robdogCtrl_Sit: Over Heat, Can not Move!");
        return ROBDOGCTRL_ERROR_INVALID_STATE;
    }

    // 检查是否有其他动作在执行
    ctrlType = utRdCtrlType();
    if (ctrlType != UT_CTRL_TYPE_INVALID)
    {
        RCLCPP_INFO(node_ctrl_->get_logger(), "Doing other action! ctrlType: %d", ctrlType);
        return ROBDOGCTRL_ERROR_INVALID_STATE;
    }

    // 写入动作
    utWrCtrlType(UT_CTRL_TYPE_SIT);

    return ROBDOGCTRL_ERROR_SUCCESS;
}

// 做动作
int32_t RobDog_Ctrl_Unitree::robdogCtrl_Locomotion(RobdogCtrlMotion motion)
{
    UtCtrlType_E ctrlType = UT_CTRL_TYPE_INVALID;

    RCLCPP_INFO(node_ctrl_->get_logger(), "Enter Function: %s", __func__);

    // 存在关节温度过热
    if (RobotInfoMgr::getInstance().utCheckTempStatus())
    {
        RCLCPP_INFO(node_ctrl_->get_logger(), "robdogCtrl_Locomotion: Over Heat, Can not Move!");
        return ROBDOGCTRL_ERROR_INVALID_STATE;
    }

    // 检查是否有其他动作在执行
    ctrlType = utRdCtrlType();
    if (ctrlType != UT_CTRL_TYPE_INVALID)
    {
        RCLCPP_INFO(node_ctrl_->get_logger(), "Doing other action! ctrlType: %d", ctrlType);
        return ROBDOGCTRL_ERROR_INVALID_STATE;
    }

    // 写入动作
    utWrCtrlMotion(motion);
    utWrCtrlType(UT_CTRL_TYPE_LOCOMOTION);

    return ROBDOGCTRL_ERROR_SUCCESS;
}

// 切换步态
int32_t RobDog_Ctrl_Unitree::robdogCtrl_ChangeGait(RobdogCtrlGait gait)           	    
{
    UtCtrlType_E ctrlType = UT_CTRL_TYPE_INVALID;

    RCLCPP_INFO(node_ctrl_->get_logger(), "Enter Function: %s", __func__);

    // 检查是否有其他动作在执行
    ctrlType = utRdCtrlType();
    if (ctrlType != UT_CTRL_TYPE_INVALID)
    {
        RCLCPP_INFO(node_ctrl_->get_logger(), "Doing other action! ctrlType: %d", ctrlType);
        return ROBDOGCTRL_ERROR_INVALID_STATE;
    }

    // 写入步态
    utWrCtrlGait(gait);
    utWrCtrlType(UT_CTRL_TYPE_CHANGE_GAIT);

    return ROBDOGCTRL_ERROR_SUCCESS;
}

// 语音指令（起立/趴下）
int32_t RobDog_Ctrl_Unitree::robdogCtrl_VoiceStand(int32_t cmd)        	
{
    int32_t ret = ROBDOGCTRL_ERROR_SUCCESS;

    RCLCPP_INFO(node_ctrl_->get_logger(), "Enter Function: %s", __func__);

    if (cmd == 1)
    {
        ret = robdogCtrl_StandUp();
    }
    else if (cmd ==2)
    {
        ret = robdogCtrl_Sit();
    }

    if (ret != ROBDOGCTRL_ERROR_SUCCESS)
    {
        RCLCPP_ERROR(node_ctrl_->get_logger(), "VoiceStand Set Failed! ret = %d, cmd = %d", ret, cmd);
        return ret;
    }

    return ROBDOGCTRL_ERROR_SUCCESS;
}

// 持续运动
int32_t RobDog_Ctrl_Unitree::robdogCtrl_ContinueMove(CtrlMoveData *pMoveData)
{
    int32_t level = 0;
    int32_t speedIndex = 0;
    RobdogCtrlGait gait;

    RCLCPP_INFO(node_ctrl_->get_logger(), "level = %d !!!!!!!!", level);

    level = RobotInfoMgr::getInstance().getSpeedLevel();
    speedIndex = utSpeedGait + UT_SPEED_GAIT_MAX * level;

    // 平滑处理
    if (utJoystickSwitch)
    {
        utLastX = utLastX * (1 - utSmooth) + pMoveData->x * utSpeedJoystick[speedIndex][UT_SPEED_DIRECT_X] * utSmooth;
        utLastY = utLastY * (1 - utSmooth) + pMoveData->y * utSpeedJoystick[speedIndex][UT_SPEED_DIRECT_Y] * utSmooth;
        utLastYaw = utLastYaw * (1 - utSmooth) + pMoveData->yaw * utSpeedJoystick[speedIndex][UT_SPEED_DIRECT_YAW] * utSmooth;
    }
    else
    {
        utLastX = utLastX * (1 - utSmoothMove) + pMoveData->x * utSpeedMove[speedIndex][UT_SPEED_DIRECT_X] * utSmoothMove;
        utLastY = utLastY * (1 - utSmoothMove) + pMoveData->y * utSpeedMove[speedIndex][UT_SPEED_DIRECT_Y] * utSmoothMove;
        utLastYaw = utLastYaw * (1 - utSmoothMove) + pMoveData->yaw * utSpeedMove[speedIndex][UT_SPEED_DIRECT_YAW] * utSmoothMove;
    }

    // 经典步态低中档位切换为宇树慢速
    gait = utRdCtrlGait();
    if (gait == ROBDOGCTRL_GAIT_AICLASSIC && level != 2)
    {
        if (utSpeedLevel != -1)
        {
            utSpeedLevel = -1;
            utSetSpeedLevel(utSpeedLevel);
        }
    }

    utMoveProc(utJoystickSwitch, utLastX, utLastY, utLastYaw);

    return ROBDOGCTRL_ERROR_SUCCESS;
}

// 运动接口
int32_t RobDog_Ctrl_Unitree::robdogCtrl_Move(float x, float y, float yaw)
{
    utMoveProc(false, x, y, yaw);

    return ROBDOGCTRL_ERROR_SUCCESS;
}

int32_t RobDog_Ctrl_Unitree::robdogCtrl_StopMove()
{
    int32_t ret = ROBDOGCTRL_ERROR_SUCCESS;
    int32_t level = 0;
    RobdogCtrlGait gait;

    RCLCPP_INFO(node_ctrl_->get_logger(), "Enter Function: %s", __func__);

    utSpeedClear();

    if (utJoystickSwitch)
    {
        utPubJoystick->Write(utJoystickMsg);
    }
    else
    {
        if (utAvoidSwitch == true)
        {
            ret = ov_client.Move(0, 0, 0);
        }
        else
        {
            ret = utSc.Move(0, 0, 0);
        }

        if (ret != ROBDOGCTRL_ERROR_SUCCESS)
        {
            RCLCPP_ERROR(node_ctrl_->get_logger(), "StopMove Action failed! ret = %d", ret);
            return ret;
        }
    }

    // 经典步态低中档停止后，恢复宇树快速
    gait = utRdCtrlGait();
    level = RobotInfoMgr::getInstance().getSpeedLevel();
    if (gait == ROBDOGCTRL_GAIT_AICLASSIC && level != 2)
    {
        if (utSpeedLevel != 1)
        {
            utSpeedLevel = 1;
            utSetSpeedLevel(utSpeedLevel);
        }
    }

    return ROBDOGCTRL_ERROR_SUCCESS;
}

// 回零
int32_t RobDog_Ctrl_Unitree::robdogCtrl_ResetZero()
{
    int32_t ret = ROBDOGCTRL_ERROR_SUCCESS;

    RCLCPP_INFO(node_ctrl_->get_logger(), "Enter Function: %s", __func__);

    utSpeedClear();

    if (utAvoidSwitch == true)
    {
        ret = ov_client.Move(0, 0, 0);
    }
    else
    {
        ret = utSc.Move(0, 0, 0);
    }

    if (ret != ROBDOGCTRL_ERROR_SUCCESS)
    {
        RCLCPP_ERROR(node_ctrl_->get_logger(), "StopMove Action failed! ret = %d", ret);
        return ret;
    }

    return ROBDOGCTRL_ERROR_SUCCESS;
}

// 软急停
int32_t RobDog_Ctrl_Unitree::robdogCtrl_EmergencyStop()
{
    int32_t ret = ROBDOGCTRL_ERROR_SUCCESS;
    
    RCLCPP_INFO(node_ctrl_->get_logger(), "Enter Function: %s", __func__);

    utSpeedClear();

    ret = utSc.Damp();
    if (ret != ROBDOGCTRL_ERROR_SUCCESS)
    {
        RCLCPP_ERROR(node_ctrl_->get_logger(), "Damp Action failed! ret = %d", ret);
        return ret;
    }

    return ROBDOGCTRL_ERROR_SUCCESS;
}

// 机器人IMU标定1阶段，平趴在地
int32_t RobDog_Ctrl_Unitree::robdogCtrl_IMUDemaStage1()
{
    int32_t ret = ROBDOGCTRL_ERROR_SUCCESS;
    int32_t status;

    RCLCPP_INFO(node_ctrl_->get_logger(), "Enter Function: %s", __func__);

    // 关闭运控
    ret = utRsc.ServiceSwitch("mcf", 0, status);
    if (ret != ROBDOGCTRL_ERROR_SUCCESS)
    {
        RCLCPP_ERROR(node_ctrl_->get_logger(), "MCF Service Switch Off Failed! ret = %d", ret);
        return ret;
    }

    ret = calibration_client.DoBasicDemaStartImuDemaInZeroTorque();
    if (ret != ROBDOGCTRL_ERROR_SUCCESS)
    {
        RCLCPP_ERROR(node_ctrl_->get_logger(), "DoBasicDemaStartImuDemaInZeroTorque Failed! ret = %d", ret);
        return ret;
    }

    return ROBDOGCTRL_ERROR_SUCCESS;
}

// 机器人IMU标定2阶段，平趴在地
int32_t RobDog_Ctrl_Unitree::robdogCtrl_IMUDemaStage2()
{
    int32_t ret = ROBDOGCTRL_ERROR_SUCCESS;

    RCLCPP_INFO(node_ctrl_->get_logger(), "Enter Function: %s", __func__);

    ret = calibration_client.DoBasicDemaStartImuDema();
    if (ret != ROBDOGCTRL_ERROR_SUCCESS)
    {
        RCLCPP_ERROR(node_ctrl_->get_logger(), "DoBasicDemaStartImuDema Failed! ret = %d", ret);
        return ret;
    }

    return ROBDOGCTRL_ERROR_SUCCESS;
}

// 机器人关节标定1阶段，平趴在地
int32_t RobDog_Ctrl_Unitree::robdogCtrl_JointDemaStage1()
{
    int32_t ret = ROBDOGCTRL_ERROR_SUCCESS;
    int32_t status;
    
    RCLCPP_INFO(node_ctrl_->get_logger(), "Enter Function: %s", __func__);

    // 关闭运控
    ret = utRsc.ServiceSwitch("mcf", 0, status);
    if (ret != ROBDOGCTRL_ERROR_SUCCESS)
    {
        RCLCPP_ERROR(node_ctrl_->get_logger(), "MCF Service Switch Off Failed! ret = %d", ret);
        return ret;
    }

    ret = calibration_client.DoBasicDemaStartJointDemaInZeroTorque();
    if (ret != ROBDOGCTRL_ERROR_SUCCESS)
    {
        RCLCPP_ERROR(node_ctrl_->get_logger(), "DoBasicDemaStartJointDemaInZeroTorque Failed! ret = %d", ret);
        return ret;
    }

    return ROBDOGCTRL_ERROR_SUCCESS;
}

// 机器人关节标定2阶段，所有机身电机摆对称三角
int32_t RobDog_Ctrl_Unitree::robdogCtrl_JointDemaStage2()
{
    int32_t ret = ROBDOGCTRL_ERROR_SUCCESS;

    RCLCPP_INFO(node_ctrl_->get_logger(), "Enter Function: %s", __func__);

    ret = calibration_client.DoBasicDemaStartJointDemaAllJoint0();
    if (ret != ROBDOGCTRL_ERROR_SUCCESS)
    {
        RCLCPP_ERROR(node_ctrl_->get_logger(), "DoBasicDemaStartJointDemaAllJoint0 Failed! ret = %d", ret);
        return ret;
    }

    return ROBDOGCTRL_ERROR_SUCCESS;
}

// 机器人关节标定3阶段，所有大腿电机，小腿电机标定工装卡位置
int32_t RobDog_Ctrl_Unitree::robdogCtrl_JointDemaStage3()
{
    int32_t ret = ROBDOGCTRL_ERROR_SUCCESS;

    RCLCPP_INFO(node_ctrl_->get_logger(), "Enter Function: %s", __func__);

    ret = calibration_client.DoBasicDemaStartJointDemaFrontJoint12();
    if (ret != ROBDOGCTRL_ERROR_SUCCESS)
    {
        RCLCPP_ERROR(node_ctrl_->get_logger(), "DoBasicDemaStartJointDemaFrontJoint12 Failed! ret = %d", ret);
        return ret;
    }

    ret = calibration_client.DoBasicDemaStartJointDemaRearJoint12();
    if (ret != ROBDOGCTRL_ERROR_SUCCESS)
    {
        RCLCPP_ERROR(node_ctrl_->get_logger(), "DoBasicDemaStartJointDemaRearJoint12 Failed! ret = %d", ret);
        return ret;
    }

    return ROBDOGCTRL_ERROR_SUCCESS;
}

// 机器人关节标定4阶段，保存数据
int32_t RobDog_Ctrl_Unitree::robdogCtrl_JointDemaStage4()
{
    int32_t ret = ROBDOGCTRL_ERROR_SUCCESS;

    RCLCPP_INFO(node_ctrl_->get_logger(), "Enter Function: %s", __func__);

    ret = calibration_client.DoBasicDemaStartJointDemaSaveData();
    if (ret != ROBDOGCTRL_ERROR_SUCCESS)
    {
        RCLCPP_ERROR(node_ctrl_->get_logger(), "DoBasicDemaStartJointDemaSaveData Failed! ret = %d", ret);
        return ret;
    }

    return ROBDOGCTRL_ERROR_SUCCESS;
}

// 开启避障功能
int32_t RobDog_Ctrl_Unitree::robdogCtrl_AvoidOpen()
{
    int32_t ret = ROBDOGCTRL_ERROR_SUCCESS;

    RCLCPP_INFO(node_ctrl_->get_logger(), "Enter Function: %s", __func__);
    
    return ROBDOGCTRL_ERROR_SUCCESS;
}

// 关闭避障功能
int32_t RobDog_Ctrl_Unitree::robdogCtrl_AvoidClose()
{
    int32_t ret = ROBDOGCTRL_ERROR_SUCCESS;

    RCLCPP_INFO(node_ctrl_->get_logger(), "Enter Function: %s", __func__);

    return ROBDOGCTRL_ERROR_SUCCESS;
}

// 手动模式
int32_t RobDog_Ctrl_Unitree::robdogCtrl_ManualMode()
{
    RCLCPP_INFO(node_ctrl_->get_logger(), "Enter Function: %s", __func__);

    return ROBDOGCTRL_ERROR_SUCCESS;
}

// 自主模式
int32_t RobDog_Ctrl_Unitree::robdogCtrl_AutoMode()
{
    RCLCPP_INFO(node_ctrl_->get_logger(), "Enter Function: %s", __func__);
    
    return ROBDOGCTRL_ERROR_SUCCESS;
}

// 移动模式
int32_t RobDog_Ctrl_Unitree::robdogCtrl_MoveMode()        	    
{
    RCLCPP_INFO(node_ctrl_->get_logger(), "Enter Function: %s", __func__);
    return ROBDOGCTRL_ERROR_SUCCESS;
}

// 心跳
int32_t RobDog_Ctrl_Unitree::robdogCtrl_HeartBeat()        	
{
    //RCLCPP_INFO(node_ctrl_->get_logger(), "Enter Function: %s", __func__);
    return ROBDOGCTRL_ERROR_SUCCESS;
}

// 机器人温度信息
int32_t RobDog_Ctrl_Unitree::robdogCtrl_Temperature()       	
{
    RCLCPP_INFO(node_ctrl_->get_logger(), "Enter Function: %s", __func__);
    return ROBDOGCTRL_ERROR_SUCCESS;
}

// 0x31010D07
int32_t RobDog_Ctrl_Unitree::robdogCtrl_Position(float x, float y, float radian)   	        
{
    RCLCPP_INFO(node_ctrl_->get_logger(), "Enter Function: %s", __func__);
    return ROBDOGCTRL_ERROR_SUCCESS;
}

// 0x122
int32_t RobDog_Ctrl_Unitree::robdogCtrl_PositionAngVel()       
{
    RCLCPP_INFO(node_ctrl_->get_logger(), "Enter Function: %s", __func__);
    return ROBDOGCTRL_ERROR_SUCCESS;
}

// 用户定义, 0x00000160
int32_t RobDog_Ctrl_Unitree::robdogCtrl_UserDefined(int32_t cmd)          	
{
    RCLCPP_INFO(node_ctrl_->get_logger(), "Enter Function: %s", __func__);
    return ROBDOGCTRL_ERROR_SUCCESS;
}

// 机器人状态信息
int32_t RobDog_Ctrl_Unitree::robdogCtrl_State()             	
{
    RCLCPP_INFO(node_ctrl_->get_logger(), "Enter Function: %s", __func__);
    return ROBDOGCTRL_ERROR_SUCCESS;
}

int32_t RobDog_Ctrl_Unitree::robdogCtrl_Init(void* ctrl_ptr_)
{
    int32_t ret = ROBDOGCTRL_ERROR_SUCCESS;

    if(nullptr == ctrl_ptr_) 
    {
        return ROBDOGCTRL_ERROR_INVALID_PARA;
    }

    node_ctrl_ = (RobdogCtrlNode *)ctrl_ptr_;
    
    RCLCPP_INFO(node_ctrl_->get_logger(), "Enter Function: %s", __func__);

    utSc.SetTimeout(20.0f);
    utSc.Init();

    utRsc.SetTimeout(10.0f);
    utRsc.Init();

    ov_client.SetTimeout(10.0f);
    ov_client.Init();

    calibration_client.SetTimeout(30.0f);
    calibration_client.Init();

    thread ut_threadProc(bind(&RobDog_Ctrl_Unitree::utThreadProc, this));
    ut_threadProc.detach();

    thread ut_threadInit(bind(&RobDog_Ctrl_Unitree::utThreadInit, this));
    ut_threadInit.detach();

    utPubJoystick.reset(new ChannelPublisher<unitree_go::msg::dds_::WirelessController_>("rt/wirelesscontroller_unprocessed"));
    utPubJoystick->InitChannel();

    ut_para_sub_ =  node_ctrl_->create_subscription<std_msgs::msg::String>(
        "/robdog_control/unitree_para", 10,
        std::bind(&RobDog_Ctrl_Unitree::utSetCtrlPara, this, std::placeholders::_1)
    );

    //关闭雷达
    //lidarSwitch.data("ON");
    //utPubLidarSwitch.reset(new ChannelPublisher<std_msgs::msg::dds_::String_>("rt/utlidar/switch"));
    //utPubLidarSwitch->InitChannel();
    //utPubLidarSwitch->Write(lidarSwitch);

    return ROBDOGCTRL_ERROR_SUCCESS;
}
