/*
 * @Author: 高亚军 <EMAIL>
 * @Date: 2024-10-01 10:04:29
 * @LastEditors: 高亚军 <EMAIL>
 * @LastEditTime: 2024-10-02 21:02:13
 * @FilePath: \robot-application\deeprobots_application_ros1\src\robdog_platintera\src\robdog_subpub\read_map_point_config\read_map_point_cfg.cpp
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

#include <iostream>
#include <fstream>
#include <sstream>
#include <chrono>
#include <cstring>
#include <rclcpp/rclcpp.hpp>
#include <std_msgs/msg/string.hpp>
#include <ament_index_cpp/get_package_share_directory.hpp>
#include "robdogNode/robdog_ctrl_node.h"
#include "robot_info_mgr.h"
#include <homi_com/homi_utils.hpp>
#include "robotState/RobotState.h"
#include "xiaoli_com/xiaoli_pub_def.h"
#include <regex>
#include <mutex>
#include <condition_variable>

extern string g_netctrl_ret;

#define TOPIC_HIGHSTATE       "rt/lf/sportmodestate"
#define TOPIC_LOWSTATE        "rt/lf/lowstate"
#define TOPIC_CLOUD           "rt/utlidar/cloud"
#define TOPIC_CLOUD_DESKEWED  "rt/utlidar/cloud_deskewed"
#define TOPIC_LIDARSTATE      "rt/utlidar/lidar_state"
#define utStateDumpSwitch  0

std::map<std::string, std::map<std::string, std::string>> brocastText = {
    {"CPU", {
        {"TooLow", "机器狗身体温度过低，已停止运行!请等温度恢复正常后，再尝试作业"},
        {"Low_more", "哎呀，当前我的身体温度过低！当前温度为%0.0f度，请停止作业，将我移动至合适位置吧"},
        {"Low", "哎呀，当前我的身体温度接近危险值！当前温度为%0.0f度，建议休息一下或者减少负载"},
        {"High", "哎呀，当前我的身体温度接近危险值！当前温度为%0.0f度，建议休息一下或者减少负载"},
        {"High_more", "哎呀，当前我的身体温度过高！当前温度为%0.0f度，请停止作业，将我移动至合适位置吧"},
        {"TooHigh", "机器狗身体温度过高，已停止运行!请等温度恢复正常后，再尝试作业"}
    }},
    {"joint", {
        {"TooLow", "机器狗关节温度过低，已停止运行!请等温度恢复正常后，再尝试作业"},
        {"Low_more", "哎呀，当前我的关节温度过低！当前温度为%0.0f度，请停止作业，将我移动至合适位置吧"},
        {"Low", "哎呀，当前我的关节温度接近危险值！当前温度为%0.0f度，建议休息一下或者减少负载"},
        {"High", "哎呀，当前我的关节温度接近危险值！当前温度为%0.0f度，建议休息一下或者减少负载"},
        {"High_more", "哎呀，当前我的关节温度过高！当前温度为%0.0f度，请停止作业，将我移动至合适位置吧"},
        {"TooHigh", "机器狗关节温度过高，已停止运行!请等温度恢复正常后，再尝试作业"}
    }}
};

std::string strArrayPart[ROB_MANU_MAX][3] =
{
    {"swing ", "knee ", "hip "},
    {"Hip ", "Thigh ", "Calf "}
};

std::string strArrayLocation[ROB_MANU_MAX][4] = 
{
    {"front left ", "front right ", "back left ", "back right "},
    {"front right ", "front left ", "back right ", "back left "}
};

// 定义状态查询表
using StateKey = std::tuple<int32_t, int32_t, int32_t>;
using StateValue = std::tuple<std::string, HomiRobotStatus>;
static std::map<StateKey, StateValue> stateQueryTableString = {
    {{1, 0, 0}, {"趴下状态", ROBDOG_STATUS_GETDOWN}},
    {{1, 0, 11}, {"正在执行向前跳", ROBDOG_STATUS_FORWARD_JUMPPING}},   
    {{4, 0, 0}, {"准备起立状态", ROBDOG_STATUS_READYTOSTAND}},
    {{5, 0, 0}, {"正在起立状态", ROBDOG_STATUS_STANDING}},
    {{6, 0, 0}, {"力控状态（静止站立）且步态为平地低速步态", ROBDOG_STATUS_FORCE_CTRL_LEVEL_LOWSPPEED_GAIT}},
    {{6, 0, 1}, {"正在以平地低速步态踏步或正在根据轴指令扭动身体", ROBDOG_STATUS_BODYROTWISTING_BY_AXISCOMMAND}}, 
    {{6, 0, 2}, {"正在执行扭身体", ROBDOG_STATUS_BODYROTWISTING}},   
    {{6, 0, 4}, {"正在执行扭身跳", ROBDOG_STATUS_BODYJUMPPING}},
    {{6, 2, 0}, {"力控状态（静止站立）且步态为通用越障步态", ROBDOG_STATUS_FORCE_CTRL_GEN_OBS_GAIT}},
    {{6, 2, 1}, {"正在以通用越障步态踏步", ROBDOG_STATUS_GEN_OBS_GAIT_STEPPING}},
    {{6, 4, 0}, {"力控状态（静止站立）且步态为平地中速步态", ROBDOG_STATUS_FORCE_CTRL_LEVEL_MIDSPEED_GAIT}},
    {{6, 4, 1}, {"正在以平地中速步态踏步", ROBDOG_STATUS_LEVEL_MIDSPEED_GAIT_STEPPING}},
    {{6, 5, 0}, {"力控状态（静止站立）且步态为平地高速步态", ROBDOG_STATUS_FORCE_CTRL_LEVEL_HIGHSPEED_GAIT}},
    {{6, 5, 1}, {"正在以平地高速步态踏步", ROBDOG_STATUS_LEVEL_HIGHSPEED_GAIT_STEPPING}},
    {{6, 6, 0}, {"力控状态（静止站立）且步态为抓地越障步态", ROBDOG_STATUS_FORCE_CTRL_GROUND_GRIPPING_GAIT}},
    {{6, 6, 1}, {"正在以抓地越障步态踏步", ROBDOG_STATUS_GROUND_GRIPPING_GAIT_STEPPING}},
    {{6, 12, 1}, {"正在执行太空步", ROBDOG_STATUS_MOONWALKING}},
    {{6, 13, 0}, {"力控状态（静止站立）且步态为高踏步越障步态", ROBDOG_STATUS_FORCE_CTRL_HIGHSTEP_OBS_GAIT}},
    {{6, 13, 1}, {"正在以高踏步越障步态踏步", ROBDOG_STATUS_HIGHSTEP_OBS_GAIT_STEPPING}},
    {{7, 0, 0}, {"正在趴下状态", ROBDOG_STATUS_PRONEING}},
    {{8, 0, 0}, {"失控保护状态", ROBDOG_STATUS_LOSS_CTRL}},
    {{9, 0, 0}, {"姿态调整状态", ROBDOG_STATUS_ATTITUDE_ADJUST_MODE}},
    {{11, 0, 0}, {"正在执行翻身", ROBDOG_STATUS_ROLL_OVERING}},
    {{17, 0, 0}, {"回零状态", ROBDOG_STATUS_RESET}},
    {{18, 0, 0}, {"正在执行后空翻", ROBDOG_STATUS_BACKFLIP}},
    {{20, 0, 0}, {"正在执行打招呼", ROBDOG_STATUS_HELLO}},
    {{16, 16, 14}, {"AI模式下站立", ROBDOG_STATUS_AI}},
    {{19, 0, 0}, {"正在执行坐下", ROBDOG_STATUS_SITDOWNING}},
    {{25, 0, 0}, {"坐下状态", ROBDOG_STATUS_SITDOWN}}
};

// 解析机器人上报的数据
DeepRobotState::DeepRobotState(const char* data, RobdogCtrlNode* udp_ctrl) {
    if (!data) return; 
    std::memcpy(&robot_basic_state, data, sizeof(robot_basic_state));
    std::memcpy(&robot_gait_state, data + sizeof(robot_basic_state), sizeof(robot_gait_state));
    std::memcpy(rpy.data(), data + 8, sizeof(double) * 3);
    std::memcpy(rpy_vel.data(), data + 32, sizeof(double) * 3);
    std::memcpy(xyz_acc.data(), data + 56, sizeof(double) * 3);
    std::memcpy(pos_world.data(), data + 80, sizeof(double) * 3);
    std::memcpy(vel_world.data(), data + 104, sizeof(double) * 3);
    std::memcpy(vel_body.data(), data + 128, sizeof(double) * 3);
    std::memcpy(&touch_down_and_stair_trot, data + 152, sizeof(touch_down_and_stair_trot));
    std::memcpy(&is_charging, data + 156, sizeof(is_charging));
    std::memcpy(&error_state, data + 160, sizeof(error_state));   // 因为对齐要偏移四个字节
    std::memcpy(&robot_motion_state, data + 164, sizeof(robot_motion_state));
    std::memcpy(&battery_level, data + 168, sizeof(battery_level));
    std::memcpy(&task_state, data + 176, sizeof(task_state));
    std::memcpy(&is_robot_need_move, data + 180, sizeof(is_robot_need_move));
    std::memcpy(&zero_position_flag, data + 181, sizeof(zero_position_flag));
    std::memcpy(ultrasound.data(), data + 184, sizeof(double) * 2);  
}

// RobotStateReceived 构造函数实现
DeepRobotStateReceived::DeepRobotStateReceived(const char* data, RobdogCtrlNode* udp_ctrl) 
: robot_state(data + 12, udp_ctrl) {
    // 解析 RobotStateReceived 的数据
    std::memcpy(&code, data, sizeof(code));
    data += sizeof(code);
    std::memcpy(&size, data, sizeof(size));
    data += sizeof(size);
    std::memcpy(&cons_code, data, sizeof(cons_code));
}

RobotInfoMgr::RobotInfoMgr() {
}

RobotInfoMgr::~RobotInfoMgr() {
}

void RobotInfoMgr::init(RobdogCtrlNode* ctrl_ptr_) {
    if(nullptr == ctrl_ptr_) {
        return;
    }
    node_ctrl_ = ctrl_ptr_;

    lastBroadcastTime = node_ctrl_->now();

    robotInfoWarnPub_=node_ctrl_->create_publisher<std_msgs::msg::String>("/device_alarm_report", 10);
#ifndef UNITREE
    external_device_status_sub = node_ctrl_->create_subscription<std_msgs::msg::String>(
            "/external_device_status", 10, std::bind(&RobotInfoMgr::external_device_status_callback, this, std::placeholders::_1));
#endif

	/****************************** 智能播报相关 **********************************/
    // 智能播报服务的客户端【上传播报文本】
    brocast_client = node_ctrl_->create_client<homi_speech_interface::srv::AssistantSpeechText>(
        "/homi_speech/helper_assistant_speech_text_service");

    platform_client = node_ctrl_->create_client<homi_speech_interface::srv::SIGCData>(
        "/homi_speech/sigc_data_service"); // 上发给平台的消息

    devAlarmRep_sub = node_ctrl_->create_subscription<std_msgs::msg::String>(
        "/device_alarm_report", 20,
        std::bind(&RobotInfoMgr::devAlarmReportCallback, this, std::placeholders::_1));

    // std::thread data_collect_thread = std::thread(&RobotInfoMgr::dataCollect, this);
    // data_collect_thread.detach();

    node_ctrl_->declare_parameter<int>("state_report_interval", 10);
    uint32_t state_report_interval = node_ctrl_->get_parameter("state_report_interval").as_int();
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "state_report_interval: %d", state_report_interval);

    state_report_timer_ = node_ctrl_->create_wall_timer(
        std::chrono::seconds(state_report_interval),
        std::bind(&RobotInfoMgr::stateReporter, this));

    if (node_ctrl_->robManuModel == ROB_MANU_DEEP_LITE)
    {
        thread send_thread(bind(&RobotInfoMgr::sendrobdogcall, this));
        send_thread.detach();
        thread recv_thread(bind(&RobotInfoMgr::receiveAndProcessData, this));
        recv_thread.detach();

        // 接收温度信息
        thread send_thread_2(bind(&RobotInfoMgr::sendrobdogcall_temperature, this));
        send_thread_2.detach();
        thread recv_thread_2(bind(&RobotInfoMgr::receiveAndProcessData_temperature, this));
        recv_thread_2.detach();
    }
    else if (node_ctrl_->robManuModel == ROB_MANU_UNITREE_GO2)
    {

        node_ctrl_->declare_parameter<int>("max_fan_speed", 0);
        max_fan_speed = node_ctrl_->get_parameter("max_fan_speed").as_int();
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "max_fan_speed: %d", max_fan_speed);

        node_ctrl_->declare_parameter<int>("half_max_fan_speed", 20);
        half_max_fan_speed = node_ctrl_->get_parameter("half_max_fan_speed").as_int();
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "half_max_fan_speed: %d", half_max_fan_speed);

        node_ctrl_->declare_parameter<int>("min_fan_speed", 50);
        min_fan_speed = node_ctrl_->get_parameter("min_fan_speed").as_int();
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "min_fan_speed: %d", min_fan_speed);

        current_fan_speed = min_fan_speed;

        thread ut_state_thread(bind(&RobotInfoMgr::utStateThreadCall, this));
        ut_state_thread.detach();
    }

    {
        std::unique_lock<std::mutex> lock(data_mutex_);
        data_cv_.wait(lock, [this]() { return data_collected_; });
    }
    stateReporter();
}

//  ****************** 异步回调函数 ******************

// 创建接收到服务器(平台的交互)回复的异步回调函数
void RobotInfoMgr::plat_srv_callback(rclcpp::Client<homi_speech_interface::srv::SIGCData>::SharedFuture response) // (std::shared_ptr<homi_speech_interface::srv::SIGCData::Request> response)
{
    // 使用response的get()获取
    auto response_value = response.get();
    int errorCode = response_value->error_code;
    if (errorCode!=0)
        RCLCPP_WARN(rclcpp::get_logger("robdog_control"),"service assistant return error code %d",errorCode);
}

// 给平台的回复
void RobotInfoMgr::sendRequestData(const std::string &data)
{
    auto request_sigc_data = std::make_shared<homi_speech_interface::srv::SIGCData::Request>();
    request_sigc_data->data = data;  // 设置请求数据
    auto ret = platform_client->wait_for_service(std::chrono::seconds(1));
    if (!ret)
	{
        RCLCPP_WARN(rclcpp::get_logger("robdog_control"), "Failed to waitForExistence service assistant");
    }
    auto result = platform_client->async_send_request(request_sigc_data, std::bind(&RobotInfoMgr::plat_srv_callback, this, std::placeholders::_1));
}

void RobotInfoMgr::to_json(const DeepRobotState& state, Json::Value& j) {
    j["robot_basic_state"] = state.robot_basic_state;
    j["robot_gait_state"] = state.robot_gait_state;

    j["rpy"] = Json::arrayValue;
    for (double val : state.rpy) {
        j["rpy"].append(val);
    }

    j["rpy_vel"] = Json::arrayValue;
    for (double val : state.rpy_vel) {
        j["rpy_vel"].append(val);
    }

    j["xyz_acc"] = Json::arrayValue;
    for (double val : state.xyz_acc) {
        j["xyz_acc"].append(val);
    }

    j["pos_world"] = Json::arrayValue;
    for (double val : state.pos_world) {
        j["pos_world"].append(val);
    }

    j["vel_world"] = Json::arrayValue;
    for (double val : state.vel_world) {
        j["vel_world"].append(val);
    }

    j["vel_body"] = Json::arrayValue;
    for (double val : state.vel_body) {
        j["vel_body"].append(val);
    }

    j["touch_down_and_stair_trot"] = state.touch_down_and_stair_trot;
    j["is_charging"] = state.is_charging;
    j["error_state"] = state.error_state;
    j["robot_motion_state"] = state.robot_motion_state;
    j["battery_level"] = state.battery_level;
    j["task_state"] = state.task_state;
    j["is_robot_need_move"] = state.is_robot_need_move;
    j["zero_position_flag"] = state.zero_position_flag;

    j["ultrasound"] = Json::arrayValue;
    for (double val : state.ultrasound) {
        j["ultrasound"].append(val);
    }
    RobotInfoMgr::getInstance().setRobotBasicState(state.robot_basic_state);
    RobotInfoMgr::getInstance().setRobotGaitState(state.robot_gait_state);
    RobotInfoMgr::getInstance().setRPY(state.rpy);
    RobotInfoMgr::getInstance().setRPYVel(state.rpy_vel);
    RobotInfoMgr::getInstance().setXYZAcc(state.xyz_acc);
    RobotInfoMgr::getInstance().setPosWorld(state.pos_world);
    RobotInfoMgr::getInstance().setVelWorld(state.vel_world); 
    RobotInfoMgr::getInstance().setVelBody(state.vel_body);
    RobotInfoMgr::getInstance().setTouchDownAndStairTrot(state.touch_down_and_stair_trot);
    RobotInfoMgr::getInstance().setIsCharging(state.is_charging); 
    RobotInfoMgr::getInstance().setErrorState(state.error_state);
    RobotInfoMgr::getInstance().setRobotMotionState(state.robot_motion_state);
    RobotInfoMgr::getInstance().setBatteryLevel(state.battery_level);
    RobotInfoMgr::getInstance().setTaskState(state.task_state);    
    RobotInfoMgr::getInstance().setIsRobotNeedMove(state.is_robot_need_move);
    RobotInfoMgr::getInstance().setZeroPositionFlag(state.zero_position_flag);
    RobotInfoMgr::getInstance().setUltrasound(state.ultrasound);  
    int robot_basic_state = RobotInfoMgr::getInstance().getRobotBasicState();
    int robot_gait_state = RobotInfoMgr::getInstance().getRobotGaitState();
    int robot_motion_state = RobotInfoMgr::getInstance().getRobotMotionState();
    std::array<double, 2> ultralS=RobotInfoMgr::getInstance().getUltrasound();
    auto key = std::make_tuple(robot_basic_state, robot_gait_state, robot_motion_state);
    auto it = stateQueryTableString.find(key);
    if (it != stateQueryTableString.end() && node_ctrl_) {
        HomiRobotStatus enLastStatus = RobotInfoMgr::getInstance().getRobotStatus();
        if(enLastStatus != std::get<1>(it->second)){
            enLastStatus = std::get<1>(it->second);
            RobotInfoMgr::getInstance().setRobotStatus(enLastStatus);
            // node_ctrl_->changeExpression(enLastStatus);

            std::string strDetails = std::get<0>(it->second);
            node_ctrl_->publishRobdagStateToQt(strDetails);
            std::cout << "Current Robot State: " << strDetails << std::endl;
        }
    } else {
        std::cout << "Current Robot State: " << "Unknown State" << std::endl;
        std::cout << "robot_basic_state: " << robot_basic_state << std::endl;
        std::cout << "robot_gait_state: " << robot_gait_state << std::endl;
        std::cout << "robot_motion_state: " << robot_motion_state << std::endl;
    }
}

// ****************************************************************************
// 判断函数，接收tuple和指令
int RobotInfoMgr::checkActionState(const std::string& actionType, const std::string& actionArgument)
{
    if (node_ctrl_->robManuModel == ROB_MANU_DEEP_LITE)
    {
        auto state = getRobotStatus();
        if (state == ROBDOG_STATUS_GETDOWN) 
        { 
            // 趴下状态
            if (actionType == "motorSkill" && actionArgument == "getDown")
                return 1;
        }
        if (state == ROBDOG_STATUS_FORCE_CTRL_LEVEL_LOWSPPEED_GAIT) 
        {  
            // 站立状态
            if (actionType == "motorSkill" && actionArgument == "standUp")
                return 1;
        }
    }
    else if (node_ctrl_->robManuModel == ROB_MANU_UNITREE_GO2)
    {
        if (true == utStandDownCheck()) 
        { 
            // 趴下状态
            if (actionType == "motorSkill" && actionArgument == "getDown")
                return 1;
        }
        if(true == utStandCheck())
        {
            // 站立状态
            if (actionType == "motorSkill" && actionArgument == "standUp")
                return 1;
        }
    }

    return 0;
}

//持续发送指令
void RobotInfoMgr::sendrobdogcall() {
    while (true) {
        if (node_ctrl_) {
            node_ctrl_->send_command();
            std::this_thread::sleep_for(std::chrono::seconds(1));
            // RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "send robdog state!!!!!!!!!!!!!!!");
        }
    }
}

//持续发送指令
void RobotInfoMgr::sendrobdogcall_temperature() {
    while (true) {
        if (node_ctrl_) {
            node_ctrl_->send_command_temperature();
            std::this_thread::sleep_for(std::chrono::seconds(3));
            // RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "send robdog temperature!!!!!!!!!!!!!!!");
        }
    }
}

std::string RobotInfoMgr::get_unitree_temperature_status(const double threshold, const double temp)
{
    if (temp >= threshold) return "TooHigh";
    if (temp >= (threshold * 0.9)) return "High";
    if (temp <= TEMP_TOOLOW_THREADHOLD) return "TooLow";
    if (temp <= TEMP_LOW_THREADHOLD)  return "Low";
    return "Normal";
}

std::string RobotInfoMgr::get_temperature_status()
{
    for (double tempJoint : RobotInfoMgr::getInstance().getRobotTemperature()) {
        if (tempJoint >= 110.0f) return "TooHigh";
        if (tempJoint >= 100.0f) return "High";
        if (tempJoint < 0.0f)    return "TooLow";
        if (tempJoint < 10.0f)   return "Low";
    }
}

std::string RobotInfoMgr::get_cpu_temperature_status(const double tempCPU)
{
    if (tempCPU >= 65.0f) return "TooHigh";
    if (tempCPU >= 50.0f) return "High";
    if (tempCPU < 0.0f)   return "TooLow";
    if (tempCPU < 10.0f)  return "Low";
    return "Normal";
}

// 持续接收指令
void RobotInfoMgr::receiveAndProcessData() {
    char packet[4096];
    size_t packet_size = sizeof(packet);
    while (true) {
        if(node_ctrl_) {
            int result = node_ctrl_->getPacket(packet, packet_size);
            std::this_thread::sleep_for(std::chrono::milliseconds(20)); // 适当的延时
            if(!result){
                // RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "receive robdog state!!!!!!!!!!!!!!!");
                // 处理接收到的数据包
                DeepRobotStateReceived robot_state_received(packet, node_ctrl_);
                if (robot_state_received.code == 2305) {
                    //2305为状态包，解析
                    Json::Value status_json;
                    to_json(robot_state_received.robot_state, status_json);
                    double battery_level_tmp = RobotInfoMgr::getInstance().getBatteryLevel();
                    batteryLevelAlarmHandle(battery_level_tmp);
                }
            }
        }
    }
}

void RobotInfoMgr::brocast_srv_callback(rclcpp::Client<homi_speech_interface::srv::AssistantSpeechText>::SharedFuture response)
{
    // 使用response的get()获取
    auto response_value = response.get();
    std::string sectionId  = response_value->section_id;
    RobotState::getInstance().setSectionId(sectionId);
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Received sectionId from server: %s", sectionId.c_str());

}

// 调用服务向语音助手发送智能播报文本（收到的sectionId要保存起来）
void RobotInfoMgr::sendStringToBrocast(const std::string &message) 
{
    RCLCPP_DEBUG(rclcpp::get_logger("robdog_control"), "Sending to brocast: %s", message.c_str()); // 播报文本

    // 创建请求消息
    auto request = std::make_shared<homi_speech_interface::srv::AssistantSpeechText::Request>();
    request->msg = message;

    // 调用服务并处理响应
    if (!brocast_client->wait_for_service(std::chrono::seconds(1))) 
    {
        RCLCPP_ERROR(rclcpp::get_logger("robdog_control"), "Service not available after waiting");
        return;
    }

    auto ret = brocast_client->wait_for_service(std::chrono::seconds(1));
    if(ret==false)
    {
        RCLCPP_WARN(rclcpp::get_logger("robdog_control"),"Failed to waitForExistence service assistant");
    }
    auto result = brocast_client->async_send_request(request, std::bind(&RobotInfoMgr::brocast_srv_callback, this, std::placeholders::_1));
}

// 持续接收指令
void RobotInfoMgr::receiveAndProcessData_temperature() {
    EthCommand c;
    CommandMessage cm;
    // CommandTemp cmd_test;
    timespec test_time;
    static uint32_t imu_count = 0;

    char packet[4096];
    size_t packet_size = sizeof(packet);
    std::array<double, MAX_JOINT_NUM> temp_array = {};
    while (true) {
        if(node_ctrl_) {
            int result = node_ctrl_->getPacket_temperature(packet, packet_size);
            std::this_thread::sleep_for(std::chrono::milliseconds(20)); // 适当的延时
            if(!result){
                // RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "receive robdog temperature!!!!!!!!!!!!!!!");
                // 处理接收到的数据包
                // std::memcpy(&robot_temperature, packet, sizeof(robot_temperature));
                // RobotInfoMgr::getInstance().setRobotTemperature(robot_temperature);
                memcpy(&cm,packet,sizeof(cm));
                // CommandTemp nc(cm.command.code,cm.command.paramters_size, cm.data_buffer);
                if(cm.command.type == command_type::CommandType::kMessValues || cm.command.type == command_type::CommandType::kSingleValue){
                    switch (cm.command.code){
                        case ROBOT_STATE_CMD:
                            // clock_gettime(1,&test_time);
                            // memcpy(&state_rec_, cm.data_buffer, sizeof(state_rec_));
                            // if (CallBack_) CallBack_(ROBOT_STATE_CMD);
                            break;
                        case BATTERY_CODE:
                            RCLCPP_INFO_THROTTLE(node_ctrl_->get_logger(),*node_ctrl_->get_clock(),3000,"battery is %f",cm.command.value);
			                batteryLevelAlarmHandle(cm.command.value);
                            break;
                        case CPU_TEMPERATURE_CODE:
                            RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "receive robdog temperature: %u", cm.command.value);
                            
                            RobotInfoMgr::getInstance().setRobotCPUTemperature(cm.command.value);
                            RobotInfoMgr::getInstance().setCPUTempStatus(get_cpu_temperature_status(cm.command.value));
                            CPUOverheatingAlarmHandle(cm.command.value);
                            break;
                        case TEMPERATURE_CODE:
                            // 12个关节的温度，一个腿上三个
                            float temp[MAX_JOINT_NUM];
                            memcpy(temp, cm.data_buffer, sizeof(temp));
                            for(int i = 0 ; i < MAX_JOINT_NUM; i++) {
                                // printf("Joint %d Temp %+3.3f\r\n", i , temp[i]);
                                RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Joint %d Temp %+3.3f\r\n", i , temp[i]);
                                temp_array[i] = static_cast<double>(temp[i]);
                                jointOverheatingAlarmHandle(temp_array[i], i);
                            }
                            RobotInfoMgr::getInstance().setRobotTemperature(temp_array);
                            RobotInfoMgr::getInstance().setTempStatus(get_temperature_status());
                            break;
                        // case IMU_DATA_CODE:
                        //     imu_count ++;
                        //     ImuData imu_data;
                        //     memcpy(&imu_data, cm.data_buffer, sizeof(imu_data));
                        //     if(imu_count % 200 == 0){
                        //         printf("angle %+3.3f  %+3.3f  %+3.3f \r\n", imu_data.angle_roll, imu_data.angle_pitch, imu_data.angle_yaw);
                        //         printf("vel   %+3.3f  %+3.3f  %+3.3f \r\n", imu_data.angular_velocity_roll, imu_data.angular_velocity_pitch, imu_data.angular_velocity_yaw);
                        //         printf("accel %+3.3f  %+3.3f  %+3.3f \r\n", imu_data.acc_x, imu_data.acc_y, imu_data.acc_z);
                        //         std::cout << "===========" << imu_data.timestamp << "=============" << std::endl;
                        //     }
                        //     break;

                        default:
                            // printf("recv code 0x%x", cm.command.code);
                            // std::cout << std::endl;
                            break;
                    }
                }
            }
        }
    }
}

void RobotInfoMgr::batteryLevelAlarmHandle(double BatteryLevel) {
    Json::Value body;
    int report_level=-1;
    bool *alarmBattery_p = NULL;
    static const std::string package_share_dir = ament_index_cpp::get_package_share_directory("robdog_control");

    if(BatteryLevel > BATTERY_LEVEL_3) {
        /* BatteryLevel>30% : clear all alarm(if alarm triggered) */
        alarmBattery10Percent = false;
        alarmBattery20Percent = false;
        alarmBattery30Percent = false;
        return;
    } else if (BatteryLevel > BATTERY_LEVEL_2) {
        /* BatteryLevel: 20%-30% : clear BATTERY_LEVEL_1 and BATTERY_LEVEL_2 alarm(if alarm triggered), 
         * and send BATTERY_LEVEL_3 alarm
         * */
        alarmBattery10Percent = false;
        alarmBattery20Percent = false;
        if(alarmBattery30Percent) {
            return;
        }
	report_level=3;
        alarmBattery_p = &alarmBattery30Percent;
    } else if(BatteryLevel > BATTERY_LEVEL_1) {
        /* BatteryLevel: 10%-20% : clear BATTERY_LEVEL_1 alarm(if alarm triggered), 
         * and send BATTERY_LEVEL_2 alarm
         * */
        alarmBattery10Percent = false;
        if(alarmBattery20Percent) {
            return;
        }
	report_level=2;
        alarmBattery_p = &alarmBattery20Percent;
    } else {
        /* BatteryLevel: 0%-10%
         * send BATTERY_LEVEL_1 alarm
         * */
        if(alarmBattery10Percent) {
            return;
        }
	report_level=1;
        alarmBattery_p = &alarmBattery10Percent;
    }
    // need report battery level alarm
    body["alarmCode"] = BATTERY_ALARM_BASE*100+report_level;
    body["alarmName"] = "batteryLevel"+std::to_string(report_level);
    body["alarmLevel"] = 2;
    body["alarmType"] = "batteryLevel";
    body["alarmDesc"] = "Battery level less than "+std::to_string(report_level*10);
    body["launcherModel"] = "Battery";
    body["data"]["batteryLevel"] = BatteryLevel;

    Json::StreamWriterBuilder writer;
    writer["commentStyle"] = "None";
    writer["indentation"] = "";
    std_msgs::msg::String bodyStr;
    bodyStr.data=Json::writeString(writer, body);

    robotInfoWarnPub_->publish(bodyStr);
    *alarmBattery_p = true;

    auto pathstr= package_share_dir + "/resource/audio/BatteryLevelLessThan" + std::to_string(report_level) + "0.wav";
    std::string command = "aplay "+pathstr;
    RCLCPP_DEBUG(rclcpp::get_logger("robdog_control"), "play audio : %s", command.c_str());
    system(command.c_str());
    return;
}

void RobotInfoMgr::CPUOverheatingAlarmHandle(uint32_t temp) {
    Json::Value body;
    if(temp <= CPU_TEMP_THREADHOLD) {
        CPUTempPublished = false;
        return;
    } else {
        if(CPUTempPublished) {
            return;
        }
    }
    
    body["alarmCode"] = HW_MOTION_CPU_OVERHEATING;
    body["alarmName"] = "CPUOverheating";
    body["alarmLevel"] = 2;
    body["alarmType"] = "CPUOverheating";
    body["alarmDesc"] = "Sport motherboard CPU overheating";
    body["launcherModel"] = "MotionCPU";
    body["data"]["temperature"] = temp;
    Json::StreamWriterBuilder writer;
    writer["commentStyle"] = "None";
    writer["indentation"] = "";
    std_msgs::msg::String bodyStr;
    bodyStr.data=Json::writeString(writer, body);

    robotInfoWarnPub_->publish(bodyStr);
    CPUTempPublished = true;

    return;
}

void RobotInfoMgr::jointOverheatingAlarmHandle(double temp, int num) {
    Json::Value body;
    std::string H = "";
    std::string L = "";
    int indexPart = num % 3;
    int indexLocation = num / 3;

    if(temp <= TEMP_THREADHOLD) 
    {
        JointTempPublished[num] = false;
        return;
    } 
    else
    {
        if(JointTempPublished[num]) 
        {
            return;
        }
    }

    H = strArrayPart[node_ctrl_->robManuModel][indexPart];
    L = strArrayLocation[node_ctrl_->robManuModel][indexLocation];

    // 01 02 001 XX 02
    body["alarmCode"] = (HW_MOTION_JOINT*100+num+1)*100+2;
    body["alarmName"] = "JointOverheating"+std::to_string(num+1);
    body["alarmLevel"] = 2;
    body["alarmType"] = "JointOverheating"+std::to_string(num+1);
    std::ostringstream oss;
    oss << "Sport motherboard " << L << H << "overheating";
    body["alarmDesc"] = oss.str();
    body["launcherModel"] = "MotionJoint";
    body["data"]["temperature"] = temp;
    Json::StreamWriterBuilder writer;
    writer["commentStyle"] = "None";
    writer["indentation"] = "";
    std_msgs::msg::String bodyStr;
    bodyStr.data=Json::writeString(writer, body);

    std::this_thread::sleep_for(std::chrono::milliseconds(1));
    robotInfoWarnPub_->publish(bodyStr);
    JointTempPublished[num] = true;

    return;
}

/*
运动模式
0. idle, default stand
1. balanceStand
2. pose
3. locomotion
4. reserve
5. lieDown
6. jointLock
7. damping
8. recoveryStand
9. reserve
10. sit
11. frontFlip
12. frontJump
13. frontPounc
*/

bool sport_state_flag = false;
bool low_state_flag = false;
bool lidar_flag = false;
unitree_go::msg::dds_::SportModeState_ sport_state;
unitree_go::msg::dds_::LowState_ low_state;
uint32_t JointHeatFlag = 0;
unitree_go::msg::dds_::LidarState_ lidar_state;

std::map<UtStateKey, UtStateValue> utStateQueryTableString = 
{
    {{UT_SPORT_ERROR_CODE_FREE_WALK},        {"灵动模式", UT_ROBDOG_STATUS_FREE_WALK}},
    {{UT_SPORT_ERROR_CODE_DAMPING},          {"阻尼状态", UT_ROBDOG_STATUS_DAMPING}},
    {{UT_SPORT_ERROR_CODE_LOCK_STAND},       {"站⽴锁定状态", UT_ROBDOG_STATUS_LOCK_STAND}},
    {{UT_SPORT_ERROR_CODE_SQUAT},            {"蹲下状态", UT_ROBDOG_STATUS_SQUAT}},
    {{UT_SPORT_ERROR_CODE_SQUAT_2},          {"蹲下状态", UT_ROBDOG_STATUS_SQUAT}},
    {{UT_SPORT_ERROR_CODE_LOCOMOTION},       {"做动作(打招呼/伸懒腰/舞蹈/拜年/⽐心/开心)", UT_ROBDOG_STATUS_LOCOMOTION}},
    {{UT_SPORT_ERROR_CODE_SIT},              {"坐下状态", UT_ROBDOG_STATUS_SIT}},
    {{UT_SPORT_ERROR_CODE_FRONT_JUMP},       {"前跳状态", UT_ROBDOG_STATUS_FRONT_JUMP}},
    {{UT_SPORT_ERROR_CODE_FRONT_POUNCE},     {"扑人状态", UT_ROBDOG_STATUS_FRONT_POUNCE}},
    {{UT_SPORT_ERROR_CODE_BALANCE_STAND},    {"平衡站立", UT_ROBDOG_STATUS_BALANCE_STAND}},
    {{UT_SPORT_ERROR_CODE_STATIC_WALK},      {"常规行走状态", UT_ROBDOG_STATUS_STATIC_WALK}},
    {{UT_SPORT_ERROR_CODE_TROT_RUN},         {"常规跑步状态", UT_ROBDOG_STATUS_TROT_RUN}},
    {{UT_SPORT_ERROR_CODE_ECONOMIC_GAIT},    {"常规续航状态", UT_ROBDOG_STATUS_ECONOMIC_GAIT}},
    {{UT_SPORT_ERROR_CODE_POSE},             {"摆姿势状态", UT_ROBDOG_STATUS_POSE}},
    {{UT_SPORT_ERROR_CODE_RECOVERYSTANDING}, {"正在恢复站立", UT_ROBDOG_STATUS_RECOVERYSTANDING}},
    {{UT_SPORT_ERROR_CODE_FREE_AVOID},       {"闪避状态", UT_ROBDOG_STATUS_FREE_AVOID}},
    {{UT_SPORT_ERROR_CODE_FREE_BOUND},       {"并腿跑状态", UT_ROBDOG_STATUS_FREE_BOUND}},
    {{UT_SPORT_ERROR_CODE_FREE_JUMP},        {"跳跃跑状态", UT_ROBDOG_STATUS_FREE_JUMP}},
    {{UT_SPORT_ERROR_CODE_CLASSIC},          {"经典状态", UT_ROBDOG_STATUS_CLASSIC}},
    {{UT_SPORT_ERROR_CODE_HAND_STAND},       {"倒立状态", UT_ROBDOG_STATUS_HAND_STAND}},
    {{UT_SPORT_ERROR_CODE_FRONT_FLIP},       {"前空翻状态", UT_ROBDOG_STATUS_FRONT_FLIP}},
    {{UT_SPORT_ERROR_CODE_BACK_FLIP},        {"后空翻状态", UT_ROBDOG_STATUS_BACK_FLIP}},
    {{UT_SPORT_ERROR_CODE_LEFT_FLIP},        {"左空翻状态", UT_ROBDOG_STATUS_LEFT_FLIP}},
    {{UT_SPORT_ERROR_CODE_CROSS_STEP},       {"交叉步状态", UT_ROBDOG_STATUS_CROSS_STEP}},
    {{UT_SPORT_ERROR_CODE_WALK_UPRIGHT},     {"直立状态", UT_ROBDOG_STATUS_WALK_UPRIGHT}},
    {{UT_SPORT_ERROR_CODE_PULL},             {"牵引状态", UT_ROBDOG_STATUS_PULL}}
};

using UtMotorErrorKey = std::tuple<int32_t>;
using UtMotorErrorValue = std::tuple<std::string>;
static std::map<UtMotorErrorKey, UtMotorErrorValue> utMotorErrorTableString = 
{
    {{0x01}, {"过流"}},
    {{0x02}, {"过压"}},
    {{0x04}, {"驱动过热"}},
    {{0x08}, {"母线欠压"}},
    {{0x10}, {"绕组过热"}},
    {{0x20}, {"编码器异常"}},
    {{0x100}, {"电机通信中断"}}
};

void RobotInfoMgr::utMotorAlarmHandle(std::array<uint32_t, 2> &reserve, int num) 
{
    int32_t newCode = reserve[0];
    int32_t oldCode = MotorErrorPublished[num];
    Json::Value body;
    std::string H = "";
    std::string L = "";
    std::string Desc = "";
    int indexPart = num % 3;
    int indexLocation = num / 3;

    /* 更新数据 */
    MotorErrorPublished[num] = newCode;

    /* 判断是否需告警 */
    if(oldCode == newCode || 0 == newCode) 
    {
        return;
    } 
    
    H = strArrayPart[node_ctrl_->robManuModel][indexPart];
    L = strArrayLocation[node_ctrl_->robManuModel][indexLocation];
    auto key = std::make_tuple(newCode);
    auto it = utMotorErrorTableString.find(key);
    if (it != utMotorErrorTableString.end() && node_ctrl_) 
    {
        Desc = std::get<0>(it->second);
    }

    // 01 02 001 XX 03
    body["alarmCode"] = (HW_MOTION_JOINT * 100 + num + 1) * 100 + 3;
    body["alarmName"] = "MotorError" + std::to_string(num + 1);
    body["alarmLevel"] = 2;
    body["alarmType"] = "MotorError" + std::to_string(num + 1);
    std::ostringstream oss;
    oss << "Sport motherboard " << L << H << "MotorError：" << Desc;
    body["alarmDesc"] = oss.str();
    body["launcherModel"] = "Motor";
    body["data"]["code"] = newCode;
    Json::StreamWriterBuilder writer;
    writer["commentStyle"] = "None";
    writer["indentation"] = "";
    std_msgs::msg::String bodyStr;
    bodyStr.data=Json::writeString(writer, body);

    std::this_thread::sleep_for(std::chrono::milliseconds(1));
    robotInfoWarnPub_->publish(bodyStr);

    return;
}

// 向平台上传智能播报状态，被唤醒词打断了需上报
void RobotInfoMgr::utRobotBroadcastStatusToPlat(std::string device)
{
    char text[256] = {0};
    std::string highTempStatus;
    std::string lowTempStatus;
    std::string tempStatus;
    double Temp = -100;
    double highTemp = -100;
    double lowTemp = 100;
    float threshold = 0.0;

    // 更新温度状态信息
    if (device == "CPU") 
    {
        Temp = getRobotCPUTemperature();
        tempStatus = get_unitree_temperature_status(CPU_TEMP_THREADHOLD, Temp);
        setCPUTempStatus(tempStatus);
        threshold = CPU_TEMP_THREADHOLD;
    }
    else 
    {
        // 获取关节最高温度和最低温度
        auto jointTemp = getRobotTemperature();
        for (double tempJoint : jointTemp) 
        {
            if (tempJoint > highTemp)
            {
                highTemp = tempJoint;
            }

            if (tempJoint < lowTemp)
            {
                lowTemp = tempJoint;
            }
        }

        // 检测温度是否正常
        highTempStatus = get_unitree_temperature_status(TEMP_THREADHOLD, highTemp);
        lowTempStatus = get_unitree_temperature_status(TEMP_THREADHOLD, lowTemp);
        if ((highTempStatus == "Normal") && (lowTempStatus == "Normal"))
        {
            tempStatus = "Normal";
        }
        else
        {
            if (highTempStatus != "Normal")
            {
                Temp = highTemp;
                tempStatus = highTempStatus;
            }
            else
            {
                Temp = lowTemp;
                tempStatus = lowTempStatus;
            }
        }

        setTempStatus(tempStatus);
        threshold = TEMP_THREADHOLD;
    }

    // 温度正常不播报，距离上次播报60s内不播报
    if ((tempStatus == "Normal") || ((node_ctrl_->now() - lastBroadcastTime).seconds() < 60.0))
    {
        return;
    }

    RCLCPP_INFO(node_ctrl_->get_logger(), "##### Temp Status Abnormal: %s #####", tempStatus.c_str());

    //检查是否提醒用户移动至安全区域
    if (Temp >= threshold * 0.95 && Temp < threshold)
    {
        tempStatus = "High_more";
    }
    else if (Temp > TEMP_TOOLOW_THREADHOLD && Temp <= TEMP_LOW_THREADHOLD * 0.5)
    {
        tempStatus = "Low_more";
    }

    // 调用语音播报
    std::string message = "";
    snprintf(text, sizeof(text), brocastText.at(device).at(tempStatus).c_str(), Temp);
    lastBroadcastTime = node_ctrl_->now();
    message = text;
    sendStringToBrocast(message);

    // 温度异常趴下
    utAbnormalStandDown();
}

// 检查温度是否异常
bool RobotInfoMgr::utCheckTempStatus()
{
    std::string currStatus;
    std::string tempStatus;

    // cpu温度异常
    currStatus = getCPUTempStatus();
    if ((tempStatus == "TooHigh") || (tempStatus == "TooLow"))
    {
        return true;
    }

    // 关节温度异常
    tempStatus = getTempStatus();
    if ((tempStatus == "TooHigh") || (tempStatus == "TooLow"))
    {
        return true;
    }

    return false;
}

// 温度异常趴下
void RobotInfoMgr::utAbnormalStandDown()
{
    HomiUtRobotStatus enLastStatus = UT_ROBDOG_STATUS_STATE;

    // 非过热或过冷状态
    if (utCheckTempStatus() == false)
    {
        return;
    }

    // 趴下状态无需处理
    enLastStatus = getUtRobotStatus();
    if (enLastStatus == UT_ROBDOG_STATUS_DAMPING || enLastStatus == UT_ROBDOG_STATUS_SQUAT)
    {
        return;
    }

    // 站立状态趴下
    if (true == utStandCheck())
    {
        // 静止站立状态，趴下
        RCLCPP_INFO(node_ctrl_->get_logger(),  "Joint Over Heat!");
        node_ctrl_->robdogCtrlDev->robdogCtrl_GetDown();
        
        // 等待狗子趴下
        std::this_thread::sleep_for(std::chrono::milliseconds(3000));
    }

    return;
}

// 等待与运控板通信正常
bool RobotInfoMgr::utConnectWait()
{
    int cnt = 0;

    while ((getUtRobotStatus() == UT_ROBDOG_STATUS_STATE) && (cnt < 60))
    {
        std::this_thread::sleep_for(std::chrono::milliseconds(1000));
        cnt++;
        // RCLCPP_INFO(node_ctrl_->get_logger(), "Wait Connect: %d", cnt);
    }

    if (cnt == 60)
    {
        // RCLCPP_INFO(node_ctrl_->get_logger(), "Sport Control Board Connection Error.");
        return false;
    }

    RCLCPP_INFO_THROTTLE(node_ctrl_->get_logger(), *node_ctrl_->get_clock(), 3000, "Sport Control Board Connected.");

    return true;
}

// 检测是否静止站立状态
bool RobotInfoMgr::utStandCheck()
{
    unitree_go::msg::dds_::SportModeState_ utSportState;
    float absNum = 0;
    uint32_t error_code = 0;

    utGetHighState(utSportState);
    error_code = utSportState.error_code();
    if (error_code == UT_SPORT_ERROR_CODE_FREE_WALK || error_code == UT_SPORT_ERROR_CODE_CLASSIC ||
        error_code == UT_SPORT_ERROR_CODE_FREE_BOUND || error_code == UT_SPORT_ERROR_CODE_FREE_JUMP)
    {
        // 三维速度,绝对值小于0.15认为静止
        for (float elem : utSportState.velocity()) 
        {
            absNum = std::fabs(elem);
            if (absNum > 0.15)
            {
                RCLCPP_INFO(node_ctrl_->get_logger(), "AI Mode, Not Stand Up Status (X,Y).");
                return false;
            }
        }

        //偏航速度,绝对值小于0.15认为静止
        absNum = std::fabs(utSportState.yaw_speed());
        if (absNum > 0.15)
        {
            RCLCPP_INFO(node_ctrl_->get_logger(), "AI Mode, Not Stand Up Status (Yaw).");
            return false;
        }
    }
    else if (error_code != UT_SPORT_ERROR_CODE_BALANCE_STAND)
    {
        RCLCPP_INFO(node_ctrl_->get_logger(), "Normal Mode, Not Stand Up Status. code=%d", error_code);
        return false;
    }

    RCLCPP_INFO(node_ctrl_->get_logger(), "Stand Up Status. Not Moving!");

    return true;
}

// 检测是否趴下状态
bool RobotInfoMgr::utStandDownCheck()
{
    HomiUtRobotStatus enLastStatus = UT_ROBDOG_STATUS_STATE;
    int ret = 0;

    enLastStatus = getUtRobotStatus();
    if (enLastStatus == UT_ROBDOG_STATUS_DAMPING || enLastStatus == UT_ROBDOG_STATUS_SQUAT)
    {
        RCLCPP_INFO(node_ctrl_->get_logger(), "Stand Down Status.");
        return true;
    }

    RCLCPP_INFO(node_ctrl_->get_logger(), "Not Stand Down Status.");

    return false;
}

// 检测是否坐下状态
bool RobotInfoMgr::utSitCheck()
{
    HomiUtRobotStatus enLastStatus = UT_ROBDOG_STATUS_STATE;
    int ret = 0;

    enLastStatus = getUtRobotStatus();
    if (enLastStatus == UT_ROBDOG_STATUS_SIT)
    {
        RCLCPP_INFO(node_ctrl_->get_logger(), "Sit Status.");
        return true;
    }

    RCLCPP_INFO(node_ctrl_->get_logger(), "Not Sit Status.");

    return false;
}

// 检测是否在执行动作
bool RobotInfoMgr::utLocomotionCheck()
{
    HomiUtRobotStatus enLastStatus = UT_ROBDOG_STATUS_STATE;
    int ret = 0;

    enLastStatus = getUtRobotStatus();
    if (enLastStatus == UT_ROBDOG_STATUS_LOCOMOTION)
    {
        RCLCPP_INFO(node_ctrl_->get_logger(), "Locomotion Status.");
        return true;
    }
    else if (enLastStatus == UT_ROBDOG_STATUS_HAND_STAND || enLastStatus == UT_ROBDOG_STATUS_WALK_UPRIGHT)
    {
        RCLCPP_INFO(node_ctrl_->get_logger(), "HandStand or WalkUpright Status.");
        return true;
    }
    else if (enLastStatus == UT_ROBDOG_STATUS_FRONT_JUMP || enLastStatus == UT_ROBDOG_STATUS_FRONT_POUNCE)
    {
        RCLCPP_INFO(node_ctrl_->get_logger(), "Front jump or Front pounce Status.");
        return true;
    }
    
    RCLCPP_INFO(node_ctrl_->get_logger(), "Not Locomotion Status.");

    return false;
}

// 检测是否运动状态
bool RobotInfoMgr::utMoveCheck()
{
    unitree_go::msg::dds_::SportModeState_ utSportState;
    float absNum = 0;
    uint32_t error_code = 0;

    utGetHighState(utSportState);
    error_code = utSportState.error_code();
    if (error_code == UT_SPORT_ERROR_CODE_FREE_WALK || error_code == UT_SPORT_ERROR_CODE_CLASSIC ||
        error_code == UT_SPORT_ERROR_CODE_FREE_BOUND || error_code == UT_SPORT_ERROR_CODE_FREE_JUMP)
    {
        // 三维速度,绝对值小于0.15认为静止
        for (float elem : utSportState.velocity()) 
        {
            absNum = std::fabs(elem);
            if (absNum > 0.15)
            {
                RCLCPP_INFO(node_ctrl_->get_logger(), "AI Mode, Moving Status (x,y).");
                return true;
            }
        }

        //偏航速度,绝对值小于0.15认为静止
        absNum = std::fabs(utSportState.yaw_speed());
        if (absNum > 0.15)
        {
            RCLCPP_INFO(node_ctrl_->get_logger(), "AI Mode, Moving Status (yaw).");
            return true;
        }
    }
    else if (error_code == UT_SPORT_ERROR_CODE_STATIC_WALK || error_code == UT_SPORT_ERROR_CODE_TROT_RUN ||
             error_code == UT_SPORT_ERROR_CODE_HAND_STAND || error_code == UT_SPORT_ERROR_CODE_WALK_UPRIGHT)
    {
        RCLCPP_INFO(node_ctrl_->get_logger(), "Walk, run, hand stand or walk upright!");
        return true;
    }

    RCLCPP_INFO(node_ctrl_->get_logger(), "Not Moving.");

    return false;
}

// 获取高层状态
void RobotInfoMgr::utGetHighState(unitree_go::msg::dds_::SportModeState_ &sport_state_info)
{
    sport_state_info = sport_state;

    return;
}

// 获取低层状态
void RobotInfoMgr::utGetLowState(unitree_go::msg::dds_::LowState_ &low_state_info)
{
    low_state_info = low_state;

    return;
}

// 获取雷达状态
void RobotInfoMgr::utGetLidarState(unitree_go::msg::dds_::LidarState_ &lidar_state_info)
{
    lidar_state_info = lidar_state;

    return;
}

/* 处理状态信息 */
void RobotInfoMgr::utHighStateHandler(const void *message)
{
    if (message == nullptr)
    {
        RCLCPP_ERROR(node_ctrl_->get_logger(),  "utHighStateHandler Null Pointer!");
        return;
    }

    sport_state = *(unitree_go::msg::dds_::SportModeState_ *)message;
    sport_state_flag = true;

    return;
}

/* 处理状态信息 */
void RobotInfoMgr::utLowStateHandler(const void *message)
{
    if (message == nullptr)
    {
        RCLCPP_ERROR(node_ctrl_->get_logger(),  "utLowStateHandler Null Pointer!");
        return;
    }

    low_state = *(unitree_go::msg::dds_::LowState_ *)message;
    low_state_flag = true;

    return;
}

/* 处理雷达状态信息 */
void RobotInfoMgr::utLidarStateHandler(const void *message)
{
    if (message == nullptr)
    {
        RCLCPP_ERROR(node_ctrl_->get_logger(),  "utLidarStateHandler Null Pointer!");
        return;
    }

    lidar_state = *(unitree_go::msg::dds_::LidarState_ *)message;
    lidar_flag = true;

    return;
}

void RobotInfoMgr::utCloudHandler(const void *message)
{
  const sensor_msgs::msg::dds_::PointCloud2_ *cloud_msg = (const sensor_msgs::msg::dds_::PointCloud2_ *)message;

  #if 0
  std::cout << "Received a raw cloud here!"
            << "\n\tstamp = " << cloud_msg->header().stamp().sec() << "." << cloud_msg->header().stamp().nanosec()
            << "\n\tframe = " << cloud_msg->header().frame_id()
            << "\n\tpoints number = " << cloud_msg->width()
            << std::endl
            << std::endl;
  #endif
}

void RobotInfoMgr::setHeadFanSpeed()
{
    double CPUTemp = RobotInfoMgr::getInstance().getRobotCPUTemperature();
    if (CPUTemp >= CPU_TEMP_THREADHOLD && current_fan_speed != max_fan_speed)
    {
        node_ctrl_->peripherals_set_fan_speed(max_fan_speed);
        current_fan_speed = max_fan_speed;
    }
    else if (CPUTemp < CPU_TEMP_THREADHOLD
          && CPUTemp >= CPU_TEMP_THREADHOLD * 0.8
          && current_fan_speed != min_fan_speed)
    {
        node_ctrl_->peripherals_set_fan_speed(min_fan_speed);
        current_fan_speed = min_fan_speed;
    }
    else if  (current_fan_speed != min_fan_speed && CPUTemp < CPU_TEMP_THREADHOLD * 0.8)
    {
        node_ctrl_->peripherals_set_fan_speed(min_fan_speed);
        current_fan_speed = min_fan_speed;
    }
}

void RobotInfoMgr::utCPUTemperatureSet() 
{
    /* 更新状态信息 */
    double CPUTemp;
    CPUTemp = getCpuTemperature();

    setRobotCPUTemperature(CPUTemp);
    utRobotBroadcastStatusToPlat("CPU");
    CPUOverheatingAlarmHandle(CPUTemp);
    setHeadFanSpeed();
}

void RobotInfoMgr::utSportStateSet() 
{
    unitree_go::msg::dds_::SportModeState_ utSportState;
    std::array<float, 3> rpy = {};
    std::array<float, 3> gyroscope = {};
    std::array<float, 3> accelerometer = {};
    std::array<float, 3> position = {};
    std::array<float, 3> velocity = {};
    std::array<double, 3> rpy_d = {};
    std::array<double, 3> gyroscope_d = {};
    std::array<double, 3> accelerometer_d = {};
    std::array<double, 3> position_d = {};
    std::array<double, 3> velocity_d = {};

    /* 获取状态信息 */
    utGetHighState(utSportState);

    /* 设置运动状态信息 */
    if (sport_state_flag == true)
    {
        /* 类型转换 */
        rpy = utSportState.imu_state().rpy();
        gyroscope = utSportState.imu_state().gyroscope();
        accelerometer = utSportState.imu_state().accelerometer();
        position = utSportState.position();
        velocity = utSportState.velocity();
        std::transform(rpy.begin(), rpy.end(), rpy_d.begin(), [](float f) { return static_cast<double>(f); });
        std::transform(gyroscope.begin(), gyroscope.end(), gyroscope_d.begin(), [](float f) { return static_cast<double>(f); });
        std::transform(accelerometer.begin(), accelerometer.end(), accelerometer_d.begin(), [](float f) { return static_cast<double>(f); });
        std::transform(position.begin(), position.end(), position_d.begin(), [](float f) { return static_cast<double>(f); });
        std::transform(velocity.begin(), velocity.end(), velocity_d.begin(), [](float f) { return static_cast<double>(f); });

        /* 先按照宇树实现来赋值 */
        setRPY(rpy_d);
        setRPYVel(gyroscope_d);
        setXYZAcc(accelerometer_d);
        setPosWorld(position_d);
        setVelWorld(velocity_d); 
        setErrorState(utSportState.error_code());
        setRobotBasicState(utSportState.error_code());
        setRobotGaitState(utSportState.gait_type());
        setRobotMotionState(utSportState.progress());

        #if 0
        /* 无下列信息，且暂时用不到 */
        setVelBody(state.vel_body);
        setTouchDownAndStairTrot(state.touch_down_and_stair_trot);
        setTaskState(state.task_state);    
        setIsRobotNeedMove(state.is_robot_need_move);
        setZeroPositionFlag(state.zero_position_flag);
        setUltrasound(state.ultrasound);  
        #endif

        /* 设置机器狗状态，状态值先和云深处区分开 */
        int robot_basic_state = getRobotBasicState();
        //int robot_gait_state = getRobotGaitState();
        //int robot_motion_state = getRobotMotionState();
        //auto key = std::make_tuple(robot_basic_state, robot_gait_state, robot_motion_state);
        auto key = std::make_tuple(robot_basic_state);
        auto it = utStateQueryTableString.find(key);
        if (it != utStateQueryTableString.end() && node_ctrl_) 
        {
            HomiUtRobotStatus enLastStatus = getUtRobotStatus();
            if(enLastStatus != std::get<1>(it->second))
            {
                enLastStatus = std::get<1>(it->second);
                setUtRobotStatus(enLastStatus);
                // node_ctrl_->changeExpression(enLastStatus);

                std::string strDetails = std::get<0>(it->second);
                node_ctrl_->publishRobdagStateToQt(strDetails);
                std::cout << "Current Robot State: " << strDetails << std::endl;
                RCLCPP_INFO(node_ctrl_->get_logger(),  "##### Current Robot State Value: %d #####", robot_basic_state);
            }
        }
        else
        {
            std::cout << "Current Robot State: " << "Unknown State" << std::endl;
            std::cout << "robot_basic_state: " << robot_basic_state << std::endl;
            RCLCPP_INFO(node_ctrl_->get_logger(),  "##### Current Robot State Value: %d #####", robot_basic_state);
            //std::cout << "robot_gait_state: " << robot_gait_state << std::endl;
            //std::cout << "robot_motion_state: " << robot_motion_state << std::endl;
        }
    }

    return;
}

void RobotInfoMgr::utLowStateSet() 
{
    unitree_go::msg::dds_::LowState_ utLowState;
    int index = 0;
    std::array<double, MAX_JOINT_NUM> temperature = {};
    std::array<uint32_t, 2> reserve = {};

    /* 获取状态信息 */
    utGetLowState(utLowState);

    /* 设置低层状态信息 */
    if (low_state_flag == true)
    {
        /* 设置电池电量，告警检测
           云深处定义：0未在充电，1充电中 2 充满
           宇树定义：正代表充电，负代表放电，与云深处定义不同，需转化
        */
        setBatteryLevel(utLowState.bms_state().soc());
        batteryLevelAlarmHandle(utLowState.bms_state().soc());

        /* 设置充电状态信息 */
        if (utLowState.bms_state().current() > 0)
        {
            setIsCharging(1);
        }
        else
        {
            setIsCharging(0);
        }

        //设置关节温度信息，告警检测
        for(index = 0 ; index < MAX_JOINT_NUM; index++) 
        {
            temperature[index] = utLowState.motor_state()[index].temperature();
            jointOverheatingAlarmHandle(temperature[index], index);

            reserve = utLowState.motor_state()[index].reserve();
            utMotorAlarmHandle(reserve, index);
        }
        setRobotTemperature(temperature);
        utRobotBroadcastStatusToPlat("joint");
    }

    return;
}

/* 机器人全量高层状态信息 */
void RobotInfoMgr::utHighStateDump() 
{
    unitree_go::msg::dds_::SportModeState_ utSportState;

    /* 获取状态信息 */
    utGetHighState(utSportState);

    std::cout << "---------------------------- high state ----------------------------" << std::endl;

    // 时间戳
    std::cout << "stamp: " << utSportState.stamp().sec() << std::endl;

    // 错误代码
    std::cout << "error_code: " << utSportState.error_code() << std::endl;

    // IMU状态, 四元数 (w,x,y,z)
    std::cout << "imu_state.quaternion: ";
    for (float elem : utSportState.imu_state().quaternion()) 
    {
        std::cout << elem << " ";
    }
    std::cout << std::endl;

    // IMU状态, 角速度（unit: rad/s)
    std::cout << "imu_state.gyroscope: ";
    for (float elem : utSportState.imu_state().gyroscope()) 
    {
        std::cout << elem << " ";
    }
    std::cout << std::endl;

    // IMU状态, 加速度 m/(s2)
    std::cout << "imu_state.accelerometer: ";
    for (float elem : utSportState.imu_state().accelerometer()) 
    {
        std::cout << elem << " ";
    }
    std::cout << std::endl;

    // IMU状态, 欧拉角（unit: rad)
    std::cout << "imu_state.rpy: ";
    for (float elem : utSportState.imu_state().rpy()) 
    {
        std::cout << elem << " ";
    }
    std::cout << std::endl;

    // IMU状态, 温度
    std::cout << "imu_state.temperature: " << static_cast<int>(utSportState.imu_state().temperature()) << std::endl;

    /*
    运动模式：
    0. idle, default stand
    1. balanceStand
    2. pose
    3. locomotion
    4. reserve
    5. lieDown
    6. jointLock
    7. damping
    8. recoveryStand
    9. reserve
    10. sit
    11. frontFlip
    12. frontJump
    13. frontPounc
    */
    std::cout << "mode: " << static_cast<int>(utSportState.mode()) << std::endl;

    /*
    是否动作执行状态
    0. dance false;
    1. dance true
    */
    std::cout << "progress: " << utSportState.progress() << std::endl;

    /*
    0.idle  
    1.trot
    2.run  
    3.climb stair  
    4.forwardDownStair   
    9.adjust
    */
    std::cout << "gait_type: " << static_cast<int>(utSportState.gait_type()) << std::endl;

    // 抬腿高度
    std::cout << "foot_raise_height: " << utSportState.foot_raise_height() << std::endl;

    // 三维位置
    std::cout << "position: ";
    for (float elem : utSportState.position()) 
    {
        std::cout << elem << " ";
    }
    std::cout << std::endl;

    // 机体高度
    std::cout << "body_height: " << utSportState.body_height() << std::endl;

    // 三维速度
    std::cout << "velocity: ";
    for (float elem : utSportState.velocity()) 
    {
        std::cout << elem << " ";
    }
    std::cout << std::endl;

    //偏航速度
    std::cout << "yaw_speed: " << utSportState.yaw_speed() << std::endl;

    // 障碍物距离
    std::cout << "range_obstacle: ";
    for (float elem : utSportState.range_obstacle()) 
    {
        std::cout << elem << " ";
    }
    std::cout << std::endl;

    // 四个足端力
    std::cout << "foot_force: ";
    for (int16_t elem : utSportState.foot_force()) 
    {
        std::cout << elem << " ";
    }
    std::cout << std::endl;

    // 足端相对于机体的位置
    std::cout << "foot_position_body: ";
    for (float elem : utSportState.foot_position_body()) 
    {
        std::cout << elem << " ";
    }
    std::cout << std::endl;    

    // 足端相对与机体的速度
    std::cout << "foot_speed_body: ";
    for (float elem : utSportState.foot_speed_body()) 
    {
        std::cout << elem << " ";
    }
    std::cout << std::endl;

    // 当前跟踪的路径点
    std::cout << "path_point: " << std::endl;
    for (unitree_go::msg::dds_::PathPoint_ elem : utSportState.path_point()) 
    {
        std::cout << "    t_from_start: " << elem.t_from_start();     // 路径点所处时刻
        std::cout << "    x: " << elem.x();                           // x位置
        std::cout << "    y: " << elem.y();                           // y位置
        std::cout << "    yaw: " << elem.yaw() ;                      // 偏航角
        std::cout << "    vx: " << elem.vx();                         // x速度 
        std::cout << "    vy: " << elem.vy();                         // y速度
        std::cout << "    vyaw: " << elem.vyaw();                     // 偏航速度
        std::cout << std::endl;
    }

    std::cout << "----------------------------- end ---------------------------" << std::endl;
    
    return;
}

/* 机器人全量低层状态信息 */
void RobotInfoMgr::utLowStateDump() 
{
    unitree_go::msg::dds_::LowState_ utLowState;

    /* 获取状态信息 */
    utGetLowState(utLowState);

    std::cout << "---------------------------- low state ----------------------------" << std::endl;

    // 帧头，数据校验用（0xFE,0xEF）。
    std::cout << "head: ";
    for (uint8_t elem : utLowState.head()) 
    {
        std::cout << static_cast<int>(elem) << " ";
    }
    std::cout << std::endl;    

    //沿用的，但是目前不用。
    std::cout << "level_flag: " << static_cast<int>(utLowState.level_flag()) << std::endl;

    //沿用的，但是目前不用。
    std::cout << "frame_reserve: " << static_cast<int>(utLowState.frame_reserve()) << std::endl;

    //已经改为文件存储形式，目前没用。
    std::cout << "sn: ";
    for (uint32_t elem : utLowState.sn()) 
    {
        std::cout << elem << " ";
    }
    std::cout << std::endl;    

    //沿用的，但是目前不用。
    std::cout << "version: ";
    for (uint32_t elem : utLowState.version()) 
    {
        std::cout << elem << " ";
    }
    std::cout << std::endl;  

    //沿用的，但是目前不用。
    std::cout << "bandwidth: " << utLowState.bandwidth() << std::endl;

    //IMU数据信息。四元数数据
    std::cout << "imu_state.quaternion: ";
    for (float elem : utLowState.imu_state().quaternion()) 
    {
        std::cout << elem << " ";
    }
    std::cout << std::endl;  

    //IMU数据信息。角速度信息（0 -> x ,0 -> y ,0 -> z）
    std::cout << "imu_state.gyroscope: ";
    for (float elem : utLowState.imu_state().gyroscope()) 
    {
        std::cout << elem << " ";
    }
    std::cout << std::endl;  

    //IMU数据信息。加速度信息（0 -> x ,0 -> y ,0 -> z）
    std::cout << "imu_state.accelerometer: ";
    for (float elem : utLowState.imu_state().accelerometer()) 
    {
        std::cout << elem << " ";
    }
    std::cout << std::endl;  

    //IMU数据信息。欧拉角信息：默认为弧度值（可按照实际情况改为角度值），
    //可按照实际数值显示（弧度值范围：-7 - +7，显示3位小数）。（数组：0-roll（翻滚角），1-pitch（俯仰角），2-yaw（偏航角））
    std::cout << "imu_state.rpy: ";
    for (float elem : utLowState.imu_state().rpy()) 
    {
        std::cout << elem << " ";
    }
    std::cout << std::endl;  

    //IMU数据信息。IMU 温度信息（摄氏度）。
    std::cout << "imu_state.temperature: " << static_cast<int>(utLowState.imu_state().temperature()) << std::endl;

    /*
    电机反馈的实时信息：
    mode:          电机控制模式（Foc模式（工作模式）-> 0x01 ，stop模式（待机模式）-> 0x00。）
    q:             关机反馈位置信息：默认为弧度值（可按照实际情况改为角度值），可按照实际数值显示（弧度值范围：-7 - +7，显示3位小数）。
    dq:            关节反馈速度
    ddq:           关节反馈加速度
    tau_est:       关节反馈力矩
    q_raw:         沿用的，但是目前不用。
    dq_raw:        沿用的，但是目前不用。
    ddq_raw:       沿用的，但是目前不用。
    temperature:   电机温度信息：类型：int8_t ，可按照实际数值显示（范围：-100 - 150）。
    lost:          电机丢包信息：可按照实际数值显示（范围：0-9999999999）。
    reserve[2]:    当前电机通信频率+电机错误标志位：（数组：0-电机错误标志位（范围：0-255，可按照实际数值显示），1-当前电机通信频率（范围：0-800，可按照实际数值显示））
    
    电机顺序，目前只用12电机，后面保留。
        FR_0 -> 0 , FR_1 -> 1  , FR_2 -> 2
        FL_0 -> 3 , FL_1 -> 4  , FL_2 -> 5
        RR_0 -> 6 , RR_1 -> 7  , RR_2 -> 8
        RL_0 -> 9 , RL_1 -> 10 , RL_2 -> 11
        Leg 0 ：FR，右前腿
        Leg 1 ：FL，左前腿
        Leg 2 ：RR，右后腿
        Leg 3 ：RL，左后腿
        Joint 0 ：Hip，机身关节
        Joint 1 ：Thigh，大腿关节
        Joint 2 ：Calf，小腿关节
    */
    std::cout << "motor_state: " << std::endl;
    for (unitree_go::msg::dds_::MotorState_ elem : utLowState.motor_state()) 
    {
        std::cout << "    ----------------" << std::endl;
        std::cout << "    mode: " << static_cast<int>(elem.mode()) << std::endl;
        std::cout << "    q: " << elem.q() << std::endl;
        std::cout << "    dq: " << elem.dq() << std::endl;
        std::cout << "    ddq: " << elem.ddq() << std::endl;
        std::cout << "    tau_est: " << elem.tau_est() << std::endl;
        std::cout << "    q_raw: " << elem.q_raw() << std::endl;
        std::cout << "    dq_raw: " << elem.dq_raw() << std::endl;
        std::cout << "    ddq_raw: " << elem.ddq_raw() << std::endl;
        std::cout << "    temperature: " << static_cast<int>(elem.temperature()) << std::endl;
        std::cout << "    lost: " << elem.lost() << std::endl;
        std::cout << "    reserve: ";
        for (uint32_t elem_in : elem.reserve()) 
        {
            std::cout << elem_in << " ";
        }
        std::cout << std::endl;
    }

    /* 
    octet version_high;    电池版本
    octet version_low;     电池版本
    octet status;          电池状态信息。
        0：SAFE,（未开启电池）
        1：WAKE_UP,（唤醒事件）
        6：PRECHG, （电池预冲电中）
        7：CHG, （电池正常充电中）
        8：DCHG, （电池正常放电中）
        9：SELF_DCHG, （电池自放电中）
        11：ALARM, （电池存在警告）
        12：RESET_ALARM, （等待按键复位警告中）
        13：AUTO_RECOVERY （复位中）
    octet soc;             电池电量信息：（类型：uint8_t）(范围1% - 100%)
    long current;          充放电信息：（正：代表充电，负代表放电）可按照实际数值显示
    unsigned short cycle;  充电循环次数
    */
    std::cout << "bms_state.version_high: " << static_cast<int>(utLowState.bms_state().version_high()) << std::endl;
    std::cout << "bms_state.version_low: " << static_cast<int>(utLowState.bms_state().version_low()) << std::endl;
    std::cout << "bms_state.status: " << static_cast<int>(utLowState.bms_state().status()) << std::endl;
    std::cout << "bms_state.soc: " << static_cast<int>(utLowState.bms_state().soc()) << std::endl;
    std::cout << "bms_state.current: " << utLowState.bms_state().current() << std::endl;
    std::cout << "bms_state.cycle: " << utLowState.bms_state().cycle() << std::endl;

    // 电池内部两个NTC的温度（int8_t）（范围：-100 - 150）。  0- BAT1; 1- BAT2 
    std::cout << "bms_state.bq_ntc: ";
    for (uint8_t elem : utLowState.bms_state().bq_ntc()) 
    {
        std::cout << static_cast<int>(elem) << " ";
    }
    std::cout << std::endl;  

    // 电池NTC数组：0 - RES，1 - MOS （int8_t）（范围：-100 - 150）
    std::cout << "bms_state.mcu_ntc: ";
    for (uint8_t elem : utLowState.bms_state().mcu_ntc()) 
    {
        std::cout << static_cast<int>(elem) << " ";
    }
    std::cout << std::endl;  

    // 电池内部15节电池的电压。
    std::cout << "bms_state.cell_vol: ";
    for (uint16_t elem : utLowState.bms_state().cell_vol()) 
    {
        std::cout << elem << " ";
    }
    std::cout << std::endl;  

    //足端力（范围0-4095），可按照实际数值显示。（数组：0-FR，1-FL，2-RR, 3-RL）
    std::cout << "foot_force: ";
    for (int16_t elem : utLowState.foot_force()) 
    {
        std::cout << elem << " ";
    }
    std::cout << std::endl;  

    //沿用的，但是目前不用。
    std::cout << "foot_force_est: ";
    for (int16_t elem : utLowState.foot_force_est()) 
    {
        std::cout << elem << " ";
    }
    std::cout << std::endl;  

    //1ms计时用，按照1ms递增。
    std::cout << "tick: " << utLowState.tick() << std::endl;

    //遥控器原始数据。
    std::cout << "wireless_remote: ";
    for (uint8_t elem : utLowState.wireless_remote()) 
    {
        std::cout << static_cast<int>(elem) << " ";
    }
    std::cout << std::endl;  

    /* 各个组件状态显示
        &0x80 -  电机               超时标志          1-超时   0-正常
        &0x40 -  小Mcu              超时标志          1-超时   0-正常
        &0x20 -  遥控器             超时标志          1-超时   0-正常
        &0x10 -  电池               超时标志          1-超时   0-正常
        &0x04 -  自动充电           自动充电状态标志  1-不充电           0-充电
        &0x02 -  板载电流错误标志   错误标志          1-板载电流异常     0-正常
        &0x01 -  运控命令超时       超时标志          1-超时             0-正常
    */
    std::cout << "bit_flag: " << static_cast<int>(utLowState.bit_flag()) << std::endl;

    // 卷线器电流（范围：0 - 3A）。
    std::cout << "adc_reel: " << utLowState.adc_reel() << std::endl;

    // 主板中心温度值（范围：-20 - 100℃）。
    std::cout << "temperature_ntc1: " << static_cast<int>(utLowState.temperature_ntc1()) << std::endl;

    // 自动充电温度（范围：-20 - 100℃）。
    std::cout << "temperature_ntc2: " << static_cast<int>(utLowState.temperature_ntc2()) << std::endl;

    // 此电压值为主板电压 -> 电池电压 。
    std::cout << "power_v: " << utLowState.power_v() << std::endl;

    // 此电流值为主板电流值 -> 电机电流。
    std::cout << "power_a: " << utLowState.power_a() << std::endl;

    /*
    风扇转速（目前可按照实际数值显示0-10000）。（0-左后转速 , 1-右后转速，2-前转速，单位转/分钟）
    （堵转检测：3-&0x01：左后堵转 , &0x02：右后堵转，&0x04：前堵转）
    */
    std::cout << "fan_frequency: ";
    for (int16_t elem : utLowState.fan_frequency()) 
    {
        std::cout << elem << " ";
    }
    std::cout << std::endl;  

    //保留位。
    std::cout << "reserve: " << utLowState.reserve() << std::endl;

    //数据CRC校验用。
    std::cout << "crc: " << utLowState.crc() << std::endl;

    std::cout << "----------------------------- end ---------------------------" << std::endl;
    
    return;
}

void RobotInfoMgr::utStateDump() 
{
    if (utStateDumpSwitch == 0)
    {
        return;
    }

    if (low_state_flag == true)
    {
        utLowStateDump();
    }

    /* 设置运动状态信息 */
    if (sport_state_flag == true)
    {
        utHighStateDump();
    }

    return;
}

void RobotInfoMgr::utStateThreadCall() 
{
    double battery_level_tmp = 0;
    double ntc1 = 0;

    suber_cloud.reset(new ChannelSubscriber<sensor_msgs::msg::dds_::PointCloud2_>(TOPIC_CLOUD));
    suber_cloud->InitChannel(std::bind(&RobotInfoMgr::utCloudHandler, this, std::placeholders::_1), 1);

    suber_sport.reset(new ChannelSubscriber<unitree_go::msg::dds_::SportModeState_>(TOPIC_HIGHSTATE));
    suber_sport->InitChannel(std::bind(&RobotInfoMgr::utHighStateHandler, this, std::placeholders::_1), 1);

    suber_low.reset(new ChannelSubscriber<unitree_go::msg::dds_::LowState_>(TOPIC_LOWSTATE));
    suber_low->InitChannel(std::bind(&RobotInfoMgr::utLowStateHandler, this, std::placeholders::_1), 1);

    suber_lidar.reset(new ChannelSubscriber<unitree_go::msg::dds_::LidarState_>(TOPIC_LIDARSTATE));
    suber_lidar->InitChannel(std::bind(&RobotInfoMgr::utLidarStateHandler, this, std::placeholders::_1), 1);

    while (true) 
    {
        /* 更新状态信息 */
        utCPUTemperatureSet();
        utSportStateSet();
        utLowStateSet();
        utStateDump();
        std::this_thread::sleep_for(std::chrono::milliseconds(100)); // 适当的延时
    }

    return;
}

void RobotInfoMgr::devAlarmReportCallback(const std_msgs::msg::String::SharedPtr msg) {
    Json::Value body;
    Json::Reader reader;
    std::string strMsg = msg->data;
    if (!reader.parse(strMsg, body))
        RCLCPP_WARN(rclcpp::get_logger("robdog_control"),"Can not parse warning msg");
    if (body["alarmName"] == "NoForeheadCamera")
        RobotState::getInstance().setForeheadCameraStatus(1);
    else if (body["alarmName"] == "NoForeheadCameraClear")
        RobotState::getInstance().setForeheadCameraStatus(0);
    else if (body["alarmName"] == "NoTieCamera")
        RobotState::getInstance().setTieCameraStatus(1);
    else if (body["alarmName"] == "NoTCameraClear")
        RobotState::getInstance().setTieCameraStatus(0);
    else if (body["alarmName"] == "NoMicrophone")
        RobotState::getInstance().setMicStatus(1);
    else if (body["alarmName"] == "NoMicrophoneClear")
        RobotState::getInstance().setMicStatus(0);
}

#ifndef UNITREE
/*
 * Receive external devices status msg and record
 * */
void RobotInfoMgr::external_device_status_callback(const std_msgs::msg::String::SharedPtr msg) {
    std::string strMsg = msg->data;
    Json::Value body;
    Json::Reader reader;

    if (!reader.parse(strMsg, body)) {
        RCLCPP_WARN(rclcpp::get_logger("robdog_control"),"Can not parse warning msg");
        return;
    }

    if (!body.isMember("extDev") || !body.isMember("stateCode")) {
        RCLCPP_WARN(rclcpp::get_logger("robdog_control"), "Missing 'extDev' or 'stateCode' in JSON message: %s", strMsg.c_str());
        return;
    }
    if(body["extDev"] == "lidar") {
        int oldStateCode = RobotState::getInstance().getLidarState();
        if(oldStateCode != body["stateCode"].asInt()) {
            RobotState::getInstance().setLidarState(body["stateCode"].asInt());
        }
    } else if (body["extDev"] == "realSense") {
        int oldStateCode = RobotState::getInstance().getRealSenseState();
        if(oldStateCode != body["stateCode"].asInt()) {
            RobotState::getInstance().setRealSenseState(body["stateCode"].asInt());
        }
    } else if (body["extDev"] == "kernel") {
        RobotState::getInstance().setNvidiaCpuTemp(body["stateCode"]["temperature"].asDouble());

        std::string oldStatue = RobotState::getInstance().getNvidiaCpuUsage();
        if(oldStatue != body["stateCode"]["cpuusage"].asString()) {
            RobotState::getInstance().setNvidiaCpuUsage(body["stateCode"]["cpuusage"].asString());
        }

        oldStatue = RobotState::getInstance().getNvidiaMemUsage();
        if(oldStatue != body["stateCode"]["memusage"].asString()) {
            RobotState::getInstance().setNvidiaMemUsage(body["stateCode"]["memusage"].asString());
        }

        RobotState::getInstance().setNvidiaProcess(body["stateCode"]["ProcessList"]);
    } else if (body["extDev"] == "RTK") {
        int oldStateCode = RobotState::getInstance().getRTKState();
        if(oldStateCode != body["stateCode"].asInt()) {
            RobotState::getInstance().setRTKState(body["stateCode"].asInt());
        }
    }
}
#endif

std::string RobotInfoMgr::executeCommand(const std::string& cmd) {

    char buffer[128];
    std::string result = "";
    FILE* pipe = popen(cmd.c_str(), "r");
    if (!pipe) {
        throw std::runtime_error("popen() failed!");
    }
    try {
        while (fgets(buffer, sizeof(buffer), pipe) != nullptr) {
            result += buffer;
        }
    } catch (...) {
        pclose(pipe);
        throw;
    }
    pclose(pipe);
    return result;
}

double RobotInfoMgr::getCpuTemperature() {

    double cpu_temp = -99;
    std::string cmdOutput;
    std::string cmd;

    if (access("/sys/class/thermal/thermal_zone0/temp", F_OK) == 0) {
        cmd = "cat /sys/class/thermal/thermal_zone0/temp";
        cmdOutput = executeCommand(cmd);
        cmdOutput.erase(cmdOutput.find_last_not_of(" \n\r\t") + 1);
        cpu_temp = std::stoi(cmdOutput) / 1000.0f;
    }
    else {
        try {
            cmd = "sensors";
            cmdOutput = executeCommand(cmd);
            std::regex temp_regex(R"(temp1:\s*([+-]?[0-9]+(?:\.[0-9]+)?)°C)");
            std::sregex_iterator it(cmdOutput.begin(), cmdOutput.end(), temp_regex);
            std::sregex_iterator end;
            double temp;
            for (; it != end; ++it) {
                temp = std::stod((*it)[1]);
                if (temp > cpu_temp)
                    cpu_temp = temp;
            }
        } catch (const std::exception& e) {
            RCLCPP_ERROR(rclcpp::get_logger("robdog_control"), "sensors command failed with error: %s", e.what());
        }
    }
    return cpu_temp;
}

void RobotInfoMgr::getCpuWarningMsg(char *msgBuffer, size_t bufferSize, double CPUTemp) {
    if (CPUTemp >= CPU_TEMP_THREADHOLD) {
        snprintf(msgBuffer, bufferSize,
                "机器狗温度过高(当前温度:%.1f)！已停止运行！请等待温度回复正常后，再尝试作业", CPUTemp);
    } else if (CPUTemp < 0) {
        snprintf(msgBuffer, bufferSize,
                "机器狗温度过低(当前温度:%.1f)！已停止运行！请等待温度回复正常后，再尝试作业", CPUTemp);
    } else {
        msgBuffer[0] = '\0';
    }
}

void RobotInfoMgr::getCpuTemperatureStatus() {

    Json::Value BoardTemps;
#ifndef UNITREE
    double CPUTemp;
    char msg[128] = "";
    CPUTemp = getCpuTemperature();
    BoardTemps["InteractiveBoard"]["status"] = RobotInfoMgr::getInstance().get_cpu_temperature_status(CPUTemp);
    getCpuWarningMsg(msg, sizeof(msg), CPUTemp);
    BoardTemps["InteractiveBoard"]["msg"] = msg;

    BoardTemps["MotionControlBoard"]["status"] = RobotInfoMgr::getInstance().getCPUTempStatus();
    getCpuWarningMsg(msg, sizeof(msg), RobotInfoMgr::getInstance().getRobotCPUTemperature());
    BoardTemps["MotionControlBoard"]["msg"] = msg;

    CPUTemp = RobotState::getInstance().getNvidiaCpuTemp();
    BoardTemps["NvidiaBoard"]["status"] = RobotInfoMgr::getInstance().get_cpu_temperature_status(CPUTemp);
    getCpuWarningMsg(msg, sizeof(msg), CPUTemp);
    BoardTemps["NvidiaBoard"]["msg"] = msg;
#else
    double CPUTemp;
    char msg[128] = "";
    CPUTemp = RobotInfoMgr::getInstance().getRobotCPUTemperature();
    BoardTemps["status"] = RobotInfoMgr::getInstance().getCPUTempStatus();
    getCpuWarningMsg(msg, sizeof(msg), CPUTemp);
    BoardTemps["msg"] = msg;
#endif
    RobotState::getInstance().setBoardTempStatus(BoardTemps);
}

std::string RobotInfoMgr::getCpuUtility(const std::string& cmd_base) {
    std::string cpuUsage;
    std::string cmd = cmd_base + "top -bn1 | grep '%Cpu' | awk '{print $2}'";

    cpuUsage = executeCommand(cmd);
    cpuUsage.erase(cpuUsage.find_last_not_of(" \n\r\t") + 1);
    cpuUsage += "%";
    return cpuUsage;
}

std::string RobotInfoMgr::getMemUtility(const std::string& cmd_base) {
    std::string cmd = cmd_base + "free | awk '/Mem:/ {printf(\"%.2f%%\\n\", $3/$2 * 100)}'";
    std::string memUsabe = executeCommand(cmd);
    memUsabe.erase(memUsabe.find_last_not_of(" \n\r\t") + 1);
    return memUsabe;
}

void RobotInfoMgr::getHardwareUsage() {
    Json::Value usages;
#ifndef UNITREE
    usages["InteractiveBoard"]["cpu"] = getCpuUtility("");
    usages["InteractiveBoard"]["memory"] = getMemUtility("");
    usages["MotionControlBoard"]["cpu"] = getCpuUtility(MotionControlBoard_cmd_base);
    usages["MotionControlBoard"]["memory"] = getMemUtility(MotionControlBoard_cmd_base);
    usages["NvidiaBoard"]["cpu"] = RobotState::getInstance().getNvidiaCpuUsage();
    usages["NvidiaBoard"]["memory"] = RobotState::getInstance().getNvidiaMemUsage();
#else
    usages["cpu"] = getCpuUtility("");
    usages["memory"] = getMemUtility("");
#endif
    RobotState::getInstance().setHDUtilization(usages);
}

Json::Value RobotInfoMgr::getProcessStatus(const std::string& cmd_base, const std::vector<std::string>& ProcessList) {

    Json::Value errProcess(Json::arrayValue);
    std::string cmd = cmd_base + "ps -efww";
    std::string output = executeCommand(cmd);

    for (const auto& proc : ProcessList) {
        if (output.find(proc) == std::string::npos) {
            errProcess.append(proc);
        }
    }
    return errProcess;
}

void RobotInfoMgr::getBoardProcessStatus() {

    Json::Value Processes;

#ifndef UNITREE
    Processes["InteractiveBoard"]["ProcessList"] = getProcessStatus("", InteractiveBoardProcessList);
    if (Processes["InteractiveBoard"]["ProcessList"].empty())
        Processes["InteractiveBoard"]["status"] = 0;
    else
        Processes["InteractiveBoard"]["status"] = 1;

    Processes["MotionControlBoard"]["ProcessList"] = getProcessStatus(MotionControlBoard_cmd_base, MotionControlBoardProcessList);
    if (Processes["MotionControlBoard"]["ProcessList"].empty())
        Processes["MotionControlBoard"]["status"] = 0;
    else
        Processes["MotionControlBoard"]["status"] = 1;

    Processes["NvidiaBoard"]["ProcessList"] = RobotState::getInstance().getNvidiaProcess();
    if (Processes["NvidiaBoard"]["ProcessList"].empty())
        Processes["NvidiaBoard"]["status"] = 0;
    else
        Processes["NvidiaBoard"]["status"] = 1;
#else
    Processes["status"] = getProcessStatus("", UnitreeProcessList);
    if (Processes["ProcessList"].empty())
        Processes["status"] = 0;
    else
        Processes["status"] = 1;
#endif
    RobotState::getInstance().setProcessStatus(Processes);
}

void RobotInfoMgr::getUSBAudioDeviceStatus() {

    std::string output = executeCommand("aplay -l");
    if (output.find("no soundcards found") == std::string::npos) {
        RobotState::getInstance().setAudioStatus(0); // normal
    } else {
        RobotState::getInstance().setAudioStatus(1); // error
    }
}

void RobotInfoMgr::collectUWBStatus() {
#ifndef UNITREE
	std::string uwb_serial_ports[] = {"tty0", "tty8", "tty9"};
#else
    std::string uwb_serial_ports[] = {"tty3", "tty4", "tty5"};
#endif
    int statusCode = 0;
	for (const auto& port : uwb_serial_ports) {
		std::string cmd = "ls /dev/" + port + " > /dev/null 2>&1";
		if (system(cmd.c_str()) != 0) {
            statusCode = 1;
			break;
		}
	}
    RobotState::getInstance().setUWBStatus(statusCode);
}

void RobotInfoMgr::collectBluetoothStatus() {

#ifndef UNITREE
    std::string cmd = MotionControlBoard_cmd_base + "bluetoothctl list";
#else
    std::string cmd = "bluetoothctl list";
#endif
    std::string output;
    output = executeCommand(cmd);
    if (output.find("Controller") != std::string::npos)
        RobotState::getInstance().setBluetoothStatus(0);
    else
        RobotState::getInstance().setBluetoothStatus(1);
}

#ifdef UNITREE
void RobotInfoMgr::getRTKStatusCode() {
    if (access("/dev/ttyS0", F_OK) == 0) {
        RobotState::getInstance().setRTKState(0);
    }
    else {
        RobotState::getInstance().setRTKState(1);
    }
}
#endif

void RobotInfoMgr::getNetworkInfo(Json::Value& body) {

    Json::Reader reader;
    Json::Value value;

    reader.parse(g_netctrl_ret, value);
    if (value.isNull())
    {
        body["networkStatus"]["wifiState"] = Json::Value();
        body["networkStatus"]["mobileDataState"] = Json::Value();
        body["networkStatus"]["wifiName"] = Json::Value();
        body["networkStatus"]["isWifiConnect"] = Json::Value();
    }
    else {
        body["networkStatus"]["wifiState"] = value["wifiState"];
        body["networkStatus"]["mobileDataState"] = value["mobileDataState"];
        body["networkStatus"]["wifiName"] = value["wifiName"];
        body["networkStatus"]["isWifiConnect"] = value["isWifiConnect"];
    }
}

void RobotInfoMgr::dataCollect() {
    // 定义数据收集lambda函数
    auto collectAllData = [this]() {
        getCpuTemperatureStatus();
        getHardwareUsage();
        getBoardProcessStatus();
        getUSBAudioDeviceStatus();
        collectUWBStatus();
        collectBluetoothStatus();
#ifdef UNITREE
        getRTKStatusCode();
        collectLidarState();
#endif
    };

    // 第一次执行数据收集
    collectAllData();

    // 设置标志并通知
    {
        std::lock_guard<std::mutex> lock(data_mutex_);
        data_collected_ = true;
    }
    data_cv_.notify_all();

    // 进入定期收集循环
    while(true) {
        sleep(30);
        collectAllData();
    }
}

#ifdef UNITREE
void RobotInfoMgr::collectLidarState() {
    if (lidar_flag == true) {
        unitree_go::msg::dds_::LidarState_ lidar_state;
        utGetLidarState(lidar_state);
        RobotState::getInstance().setLidarDirtyPercent(lidar_state.dirty_percentage());
        if (lidar_state.error_state() == 0)
            RobotState::getInstance().setLidarState(0);
        else
            RobotState::getInstance().setLidarState(1);
    }
    else
    {
        RobotState::getInstance().setLidarDirtyPercent(0xFF);
        RobotState::getInstance().setLidarState(2);
    }
}
#endif
void RobotInfoMgr::stateReporter() {

    std::mutex mtx_;
    std::condition_variable cv_;

    Json::Value root;
    Json::Value body;

#ifdef UNITREE
    unitree_go::msg::dds_::LowState_ low_state_temp;
    if (low_state_flag == true) {
        utGetLowState(low_state_temp);
    }
    unitree_go::msg::dds_::LidarState_ lidar_state_temp;
    if (lidar_flag == true) {
        utGetLidarState(lidar_state_temp);
    }
#endif

    auto jointTemp = RobotInfoMgr::getInstance().getRobotTemperature();
    std::string propertyName = "";

    // Joint temperature
    for(int i = 0;i< MAX_JOINT_NUM;i++) {
        propertyName = jointNames[i];
        body["hardwareStatus"][propertyName]["temperature"] = (double)jointTemp[i];
        body["hardwareStatus"][propertyName]["stateCode"] = (double)jointTemp[i]>TEMP_THREADHOLD ? 1 : 0;
#ifdef UNITREE
        body["hardwareStatus"][propertyName]["status"] = low_state_temp.motor_state()[i].reserve()[0];
        body["hardwareStatus"][propertyName]["frequency"] = low_state_temp.motor_state()[i].reserve()[1];
#endif
    }
    body["lidar"]["stateCode"] = RobotState::getInstance().getLidarState();
#ifdef UNITREE
    body["lidar"]["dirtyPercent"] = RobotState::getInstance().getLidarDirtyPercent();
    body["lidar"]["status"] = static_cast<int>(lidar_state_temp.error_state());
    // imu
    body["imu"]["temperature"] = static_cast<int>(low_state_temp.imu_state().temperature());
#endif
#ifndef UNITREE
    body["realSense"]["stateCode"] = RobotState::getInstance().getRealSenseState();
#endif
	body["battery"]["power"] = RobotInfoMgr::getInstance().getBatteryLevel();
    body["battery"]["status"] = (RobotInfoMgr::getInstance().getIsCharging())?1:0;
#ifdef UNITREE
    body["battery"]["stateCode"] = static_cast<int>(low_state_temp.bms_state().status());
    body["battery"]["cycle"] = static_cast<int>(low_state_temp.bms_state().cycle());
#endif
    getNetworkInfo(body);
    body["temperature"] = RobotState::getInstance().getBoardTempStatus();
    body["Utilization"] = RobotState::getInstance().getHDUtilization();
    body["process"] = RobotState::getInstance().getProcessStatus();
    body["AudioDevice"]["stateCode"] = RobotState::getInstance().getAudioStatus();
    body["uwb"]["statusCode"] = RobotState::getInstance().getUWBStatus();
    body["bluetooth"]["status"] = RobotState::getInstance().getBluetoothStatus();
    body["rtk"]["stateCode"] = RobotState::getInstance().getRTKState();
    body["mic"]["stateCode"] = RobotState::getInstance().getMicStatus();
    body["camera"]["forehead"]["stateCode"] = RobotState::getInstance().getForeheadCameraStatus();
    body["camera"]["bowtie"]["stateCode"] = RobotState::getInstance().getTieCameraStatus();

    root["deviceId"] = RobotState::getInstance().getDeviceId();
    RCLCPP_DEBUG(rclcpp::get_logger("robdog_control"), ">>>>>>> deviceid %s", RobotState::getInstance().getDeviceId().c_str());
    root["domain"] = "DEVICE_MONITOR";
    root["event"] = "hardware_state_report";
    std::time_t now = std::time(nullptr);
    root["eventId"] = "state_report_" + std::to_string(now);
    root["seq"] = std::to_string(now);
    root["response"] = "false";
    root["body"] = body;

    Json::StreamWriterBuilder writerBuilder;
    std::string jsonString = Json::writeString(writerBuilder, root);
    RCLCPP_DEBUG(rclcpp::get_logger("robdog_control"), "Request to platform is %s",root.toStyledString().c_str());
    sendRequestData(jsonString);
}
