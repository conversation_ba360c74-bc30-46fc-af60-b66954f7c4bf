//
/*****websocket通讯服务端节点******/
/**********默认端口为19002********/
//
#include "rclcpp/rclcpp.hpp"
#include <cstring>

#include <iostream>
#include <fstream>
#include <tuple>
#include <unordered_map>
#include <map>
#include <chrono>
#include <string>
#include <csignal>  // 新增
#include <signal.h> // 新增
#include <jsoncpp/json/json.h>

#include <cstdint> // For fixed-width integer types
#include <array>   // For std::array

#include <thread>       // 引入线程库
#include <atomic>       // 引入原子操作库
#include <chrono>   // 用于 std::chrono
#include "robdogNode/robdog_ctrl_node.h"
#include "robotMgr/robot_info_mgr.h"
#include "robdogCenter/robdog_center_mgr.h"
#include "taskMgr/task_info_mgr.h"
#include "alarmMgr/robor_alarm_mgr.h"
#include "robdogHandPosCtrl/robdog_hand_pos.h"
#include "robotState/RobotState.h"
#include "followMe/followMeNode.h"
using namespace std;

//bool stopRequested = false;
//bool pauseRequested = false;
#pragma pack(4)

// 信号处理函数
void signal_handler(int signal) {
    switch (signal) {
        case SIGINT:
            std::cout << "Caught Ctrl+C (SIGINT)!" << std::endl;
            //stopRequested = true;
            //rclcpp::shutdown();   // 否则程序结束无法坐下
            break;
        case SIGTSTP:
            std::cout << "Caught Ctrl+Z!" << std::endl;
            //pauseRequested = !pauseRequested; // 切换暂停状态
            break;
        default:
            std::cout << "Caught " << signal << std::endl;
            // 处理其他信号
            break;
    }
}

int main(int argc, char **argv)
{
    //  ------------------ 注册信号处理函数（ctrl+c） ------------------ 
    struct sigaction sa_ctrl_c;
    sa_ctrl_c.sa_handler = &signal_handler;
    sigemptyset(&sa_ctrl_c.sa_mask);
    sa_ctrl_c.sa_flags = 0;
    if (sigaction(SIGINT, &sa_ctrl_c, NULL) == -1) {
        std::cerr << "Failed to register SIGINT handler" << std::endl;
        return 1;
    }
    
    // ------------------ 注册信号处理函数（ctrl+z） ------------------ 
    struct sigaction sa_tstp;
    sa_tstp.sa_handler = &signal_handler;
    sigemptyset(&sa_tstp.sa_mask);
    sa_tstp.sa_flags = 0;
    if (sigaction(SIGTSTP, &sa_tstp, NULL) == -1) {
        std::cerr << "Failed to register SIGTSTP handler" << std::endl;
        return 1;
    }

    boost::filesystem::path file_path = boost::filesystem::canonical(argv[0]);
    boost::filesystem::path folder_path = file_path.parent_path();
    
    // ROS2代码：
    rclcpp::init(argc, argv);

    // 创建一个节点
    std::shared_ptr<RobdogCtrlNode> ctrl_node_ = std::make_shared<RobdogCtrlNode>();
    auto follow_node_ = std::make_shared<FollowNode>();

    rclcpp::executors::MultiThreadedExecutor executor(rclcpp::ExecutorOptions(), 4);  // 指定线程数
    // auto executor = rclcpp::executors::SingleThreadedExecutor();
    executor.add_node(ctrl_node_);
    executor.add_node(follow_node_);

    // socket 通信
    RobManuModel manu = ROB_MANU_DEEP_LITE;
    manu = ctrl_node_->getRobManuModel();
    //ctrl_node_->initSocket("192.168.1.120", 6688, 43893);

    try {
        if (manu == ROB_MANU_DEEP_LITE)
        {
            ctrl_node_->initSocket("192.168.1.120", 6688, 43893);
            ctrl_node_->robdogCtrlDev = std::make_shared<RobDog_Ctrl_Deep>();
            ctrl_node_->robdogCtrlDev->robdogCtrl_Init((void *)ctrl_node_.get());
        }
        else if (manu == ROB_MANU_UNITREE_GO2)
        {
            RCLCPP_INFO(ctrl_node_->get_logger(), "Initializing Unitree ChannelFactory...");
            unitree::robot::ChannelFactory::Instance()->Init(0);
            RCLCPP_INFO(ctrl_node_->get_logger(), "ChannelFactory initialized successfully");

            RCLCPP_INFO(ctrl_node_->get_logger(), "Creating RobDog_Ctrl_Unitree instance...");
            ctrl_node_->robdogCtrlDev = std::make_shared<RobDog_Ctrl_Unitree>();
            RCLCPP_INFO(ctrl_node_->get_logger(), "RobDog_Ctrl_Unitree instance created successfully");

            RCLCPP_INFO(ctrl_node_->get_logger(), "Calling robdogCtrl_Init...");
            ctrl_node_->robdogCtrlDev->robdogCtrl_Init((void *)ctrl_node_.get());
            RCLCPP_INFO(ctrl_node_->get_logger(), "robdogCtrl_Init completed successfully");
        }
    }
    catch (const std::bad_alloc& e) {
        RCLCPP_ERROR(ctrl_node_->get_logger(), "Memory allocation failed during robot control initialization: %s", e.what());
        RCLCPP_ERROR(ctrl_node_->get_logger(), "This may be due to insufficient memory or memory fragmentation");
        RCLCPP_ERROR(ctrl_node_->get_logger(), "Try restarting the system or closing other applications");
        return -1;
    }
    catch (const std::exception& e) {
        RCLCPP_ERROR(ctrl_node_->get_logger(), "Exception during robot control initialization: %s", e.what());
        return -1;
    }
    catch (...) {
        RCLCPP_ERROR(ctrl_node_->get_logger(), "Unknown exception during robot control initialization");
        return -1;
    }

    // 等待一段时间确保 UDP 通信初始化完成
    rclcpp::sleep_for(std::chrono::milliseconds(200));

    ctrl_node_->declare_parameter<string>("devSN", "12345"); 
    std::string devSN = ctrl_node_->get_parameter("devSN").as_string();  
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "devSN: %s", devSN.c_str());

    //机器人信息管理类初始化
    try {
        RCLCPP_INFO(ctrl_node_->get_logger(), "Initializing node...");
        ctrl_node_->initNode();
        RCLCPP_INFO(ctrl_node_->get_logger(), "Node initialized successfully");

        RCLCPP_INFO(ctrl_node_->get_logger(), "Initializing singleton managers...");
        RobotState::getInstance().setDeviceId(devSN);
        RobotInfoMgr::getInstance().init(ctrl_node_.get());
        RobdogCenter::getInstance().init(ctrl_node_.get());
        TaskInfoMgr::getInstance().init(ctrl_node_.get());
        AlarmInfoMgr::getInstance().init(ctrl_node_.get());
        RobdogHandPosCtrl::getInstance().init(ctrl_node_.get());
        RobotState::getInstance().setFolderPath(folder_path);
        RCLCPP_INFO(ctrl_node_->get_logger(), "All singleton managers initialized successfully");
    }
    catch (const std::bad_alloc& e) {
        RCLCPP_ERROR(ctrl_node_->get_logger(), "Memory allocation failed during singleton initialization: %s", e.what());
        RCLCPP_ERROR(ctrl_node_->get_logger(), "This may be due to insufficient memory or memory fragmentation");
        return -1;
    }
    catch (const std::exception& e) {
        RCLCPP_ERROR(ctrl_node_->get_logger(), "Exception during singleton initialization: %s", e.what());
        return -1;
    }
    catch (...) {
        RCLCPP_ERROR(ctrl_node_->get_logger(), "Unknown exception during singleton initialization");
        return -1;
    }
    std::atomic<bool> keep_running(true);  
    auto send_thread = std::thread([&keep_running, ctrl_node_]() {
        while (keep_running.load()) 
        {
            ctrl_node_->robdogCtrlDev->robdogCtrl_HeartBeat();
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
            RCLCPP_INFO_THROTTLE(ctrl_node_->get_logger(),*ctrl_node_->get_clock(),4000,"SEND Heartbeat Pack To JY_EXE!");
        }
    });

    // ctrl_node_->StandUp();

    // 进入 ROS 2 事件循环
    executor.spin();
    keep_running.store(false);  
    if (send_thread.joinable()) {
        send_thread.join(); 
    }
    rclcpp::shutdown();
    return 0;
}
