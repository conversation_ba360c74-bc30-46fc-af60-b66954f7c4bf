#include "libWebSocket.h"
#include "public/tools.h"
#include "robdog_center_mgr.h"
#include "robotState/RobotState.h" // 为了存取xml
#include "robdogNode/robdog_ctrl_node.h"
#include "robdogHandPosCtrl/robdog_hand_pos.h" // 为了控制手势识别

#include "robotSmartRemind/robot_smart_remind.h" // 为了使用智能提醒

#include "robotInfoCfg/read_map_point_cfg.h"
#include "robotMgr/robot_info_mgr.h" // 为了读取状态
#include <rclcpp/rclcpp.hpp>
#include <netinet/in.h>
#include <stdio.h>
#include <unistd.h>
#include <uuid/uuid.h>
#include <geometry_msgs/msg/twist.hpp>
#include <std_msgs/msg/string.hpp>
#include <cmath> 
#include <filesystem>
#include <fstream>
#include <geometry_msgs/msg/twist.hpp>
#include <homi_speech_interface/srv/assistant_abort.hpp>
#include <homi_speech_interface/srv/assistant_take_photo.hpp>
#include <homi_speech_interface/srv/iot_control.hpp>
#include <homi_speech_interface/srv/net_ctrl.hpp>
#include <iostream>
#include <jsoncpp/json/json.h>
#include <random>
#include <string>
#include <tf2/transform_datatypes.h> // ROS 2 中的 tf2
#include <thread>
#include <chrono>
#include <tf2/transform_datatypes.h> // ROS 2 中的 tf2
#include <vector>
#include <atomic>
#include <chrono>
#include <homi_speech_interface/srv/set_wake_event.hpp>
#include <iostream>
#include <thread>
#include "xiaoli_com/xiaoli_pub_def.h"
#include <homi_com/homi_utils.hpp>
#include <future>
#include <unordered_set>
#include <sensor_msgs/msg/nav_sat_fix.hpp>
#include <cmath>
#include <mutex>
#include <condition_variable>
#include <regex>
#include <stdexcept>              // 异常处理

#include <openssl/evp.h>
#include <openssl/err.h>
#include <sys/file.h>
#include <cerrno>    // 用于errno变量
#include <cstring>   // 用于strerror()函数
#include <fcntl.h>   // 用于flock()和锁类型定义


using namespace std;
using namespace WS;
namespace fs = std::filesystem;
const std::string dynamic_nobind_video_path="video/dynamic_nobind/dynamic_nobind.mp4";
const std::string static_nobind_video_path="video/static_nobind/static_nobind.mp4";
const std::string resource_default_video_path="video/default";
const std::string resource_nonet_video_path="video/nonet/";

// ********************************************* 和导航相关的代码 *******************************************
/*自主导航的步态，下发多点任务时带上
"option": {
    "even_low_speed": False,      // 平地低速
    "even_medium_speed": False,   // 平地中速
    "uneven_high_step": False,    // 越障高速
    "even_rl": False,             // 平地学习
    "uneven_rl": False            // 越障学习
}*/

unsigned long currentTimeStramp_ = base::homiUtils::getCurrentTimeStamp();
std::chrono::seconds timeout_duration(15); // 设置超时时间为15秒
auto lastCancelMovementTime = std::chrono::steady_clock::now(); // 记录上次收到2200取消移动的时间
bool bConnected_ = false;
string strConnectUrl_ = "ws://192.168.1.110:19002";
int nConnectIndex_ = 0;
string g_netctrl_ret;
long long g_wifi_set_time =0;
std::string g_wifi_set_name;
std::string g_ifname = "wlan0";

// 静态变量：缓存配置值（仅当前文件可见）
static std::string dt;         // 对应devType（设备类型）
static std::string sn;         // 对应devSn（设备序列号）
static std::string snPwd;      // 对应OVDMediaEncPassword（加密密码）
static std::string timestamp;

// 配置读取状态标志（确保配置只读取一次）
static std::once_flag config_read_flag;

void init_openssl() {
    static std::once_flag ssl_init_flag;
    std::call_once(ssl_init_flag, []() {
        RCLCPP_INFO(rclcpp::get_logger("RobdogCenter"), "初始化OpenSSL库...");
        OPENSSL_init_crypto(
            OPENSSL_INIT_LOAD_CONFIG |
            OPENSSL_INIT_LOAD_CRYPTO_STRINGS |
            OPENSSL_INIT_ADD_ALL_DIGESTS,
            nullptr
        );
        RCLCPP_INFO(rclcpp::get_logger("RobdogCenter"), "OpenSSL库初始化完成");
    });
}

// 新增静态变量存储API URL
static std::string api_url;  // 用于存储从param.yaml中提取的URL
static bool online = true;  // 用于显示当前是测试还是现网

/**
 * @brief 读取配置文件并初始化静态变量（线程安全，只执行一次）
 */
void ensure_config_loaded() {
    std::call_once(config_read_flag, []() {
        // ===================== 第一部分：读取cmcc_dev.ini =====================
        const std::string dev_config_path = "/etc/cmcc_robot/cmcc_dev.ini";
        RCLCPP_INFO(rclcpp::get_logger("RobdogCenter"), "开始读取配置文件: %s", dev_config_path.c_str());
        
        // 打开INI配置文件
        std::ifstream dev_config_file(dev_config_path);
        if (!dev_config_file.is_open()) {
            RCLCPP_ERROR(
                rclcpp::get_logger("RobdogCenter"), 
                "无法打开配置文件: %s (请检查文件权限或路径)", 
                dev_config_path.c_str()
            );
            throw std::runtime_error("配置文件打开失败");
        }

        // 解析INI文件
        std::string line;
        bool in_factory_section = false;

        while (std::getline(dev_config_file, line)) {
            // 去除行首尾空白
            size_t start = line.find_first_not_of(" \t\n\r");
            if (start == std::string::npos) continue;
            line = line.substr(start);
            size_t end = line.find_last_not_of(" \t\n\r");
            line = line.substr(0, end + 1);

            // 处理Section
            if (line.front() == '[' && line.back() == ']') {
                std::string section = line.substr(1, line.size() - 2);
                in_factory_section = (section == "factory");
                continue;
            }

            // 跳过注释
            if (line.front() == ';' || line.front() == '#') continue;

            // 处理键值对
            if (in_factory_section) {
                size_t eq_pos = line.find('=');
                if (eq_pos == std::string::npos) continue;
                
                std::string key = line.substr(0, eq_pos);
                std::string value = line.substr(eq_pos + 1);

                // 去除键值空白
                start = key.find_first_not_of(" \t");
                end = key.find_last_not_of(" \t");
                if (start != std::string::npos && end != std::string::npos) {
                    key = key.substr(start, end - start + 1);
                }
                
                start = value.find_first_not_of(" \t");
                end = value.find_last_not_of(" \t");
                if (start != std::string::npos && end != std::string::npos) {
                    value = value.substr(start, end - start + 1);
                }

                // 赋值给静态变量
                if (key == "devType") dt = value;
                else if (key == "devSn") sn = value;
                else if (key == "OVDMediaEncPassword") snPwd = value;
            }
        }

        dev_config_file.close();

        // ===================== 第二部分：读取param.yaml =====================
        const std::string yaml_config_path = "/usr/bin/cmcc_robot/install/homi_speech/share/homi_speech/launch/config/param.yaml";
        RCLCPP_INFO(rclcpp::get_logger("RobdogCenter"), "开始读取配置文件: %s", yaml_config_path.c_str());
        
        // 打开YAML配置文件
        std::ifstream yaml_config_file(yaml_config_path);
        if (!yaml_config_file.is_open()) {
            RCLCPP_ERROR(
                rclcpp::get_logger("RobdogCenter"), 
                "无法打开配置文件: %s (请检查文件权限或路径)", 
                yaml_config_path.c_str()
            );
            throw std::runtime_error("配置文件打开失败");
        }

        // 读取整个文件内容
        std::stringstream yaml_buffer;
        yaml_buffer << yaml_config_file.rdbuf();
        std::string yaml_content = yaml_buffer.str();
        yaml_config_file.close();

        // 提取JSON字符串（去除首尾单引号）
        std::string json_str;
        if (yaml_content.size() >= 2 && 
            yaml_content.front() == '\'' && 
            yaml_content.back() == '\'') {
            json_str = yaml_content.substr(1, yaml_content.size() - 2);
        } else {
            json_str = yaml_content;
        }

        // 查找并提取URL
        const std::string url_key = "\"url\":\"";
        size_t url_start_pos = json_str.find(url_key);
        if (url_start_pos == std::string::npos) {
            RCLCPP_ERROR(
                rclcpp::get_logger("RobdogCenter"), 
                "在param.yaml中未找到url字段"
            );
            throw std::runtime_error("配置项缺失");
        }

        url_start_pos += url_key.length();
        size_t url_end_pos = json_str.find('\"', url_start_pos);
        if (url_end_pos == std::string::npos) {
            RCLCPP_ERROR(
                rclcpp::get_logger("RobdogCenter"), 
                "在param.yaml中url字段格式错误"
            );
            throw std::runtime_error("配置项格式错误");
        }

        api_url = json_str.substr(url_start_pos, url_end_pos - url_start_pos);

        // 定义常量URL模式
        constexpr const char* PRODUCTION_URL_PATTERN = "business.homibot.komect.com:9443";
        constexpr const char* TEST_URL_PATTERN = "36.140.17.36:10000";

        // 在配置读取部分添加环境判断
        if (api_url.find(PRODUCTION_URL_PATTERN) != std::string::npos) {
            online = true;
            RCLCPP_INFO(rclcpp::get_logger("RobdogCenter"), "当前为现网环境 (%s)", api_url.c_str());
        } else if (api_url.find(TEST_URL_PATTERN) != std::string::npos) {
            online = false;
            RCLCPP_INFO(rclcpp::get_logger("RobdogCenter"), "当前为测试环境 (%s)", api_url.c_str());
        } else {

            RCLCPP_WARN(rclcpp::get_logger("RobdogCenter"), 
                    "未知API环境: %s (默认使用测试环境配置)", 
                    api_url.c_str());
        }
        
        
        // ===================== 配置项验证 =====================
        bool dev_config_ok = !dt.empty() && !sn.empty() && !snPwd.empty();
        bool yaml_config_ok = !api_url.empty();
        
        if (!dev_config_ok || !yaml_config_ok) {
            std::string error_msg;
            if (!dev_config_ok) {
                error_msg += "dev.ini配置缺失: ";
                if (dt.empty()) error_msg += "devType, ";
                if (sn.empty()) error_msg += "devSn, ";
                if (snPwd.empty()) error_msg += "OVDMediaEncPassword, ";
            }
            if (!yaml_config_ok) {
                error_msg += "param.yaml配置缺失: url";
            }
            
            // 移除末尾多余的逗号和空格
            if (!error_msg.empty() && error_msg.back() == ' ') {
                error_msg = error_msg.substr(0, error_msg.size() - 2);
            }
            
            RCLCPP_ERROR(
                rclcpp::get_logger("RobdogCenter"), 
                "%s", error_msg.c_str()
            );
            throw std::runtime_error("配置项缺失");
        }

        RCLCPP_INFO(rclcpp::get_logger("RobdogCenter"), "配置文件读取成功");
        RCLCPP_INFO(rclcpp::get_logger("RobdogCenter"), 
                    "配置值: devType=%s, devSn=%s, snPwd=%s, api_url=%s", 
                    dt.c_str(), sn.c_str(), snPwd.c_str(), api_url.c_str());
    });
}

/**
 * @brief 获取当前时间戳（毫秒级）
 * @return 13位毫秒级时间戳字符串
 */
std::string get_current_timestamp() {
    using namespace std::chrono;
    auto now_ms = time_point_cast<milliseconds>(system_clock::now());
    auto epoch = now_ms.time_since_epoch();
    auto value = duration_cast<milliseconds>(epoch);
    return std::to_string(value.count());
}

/**
 * @brief 计算SHA-256哈希 + Base64编码签名
 * @param data 待签名数据
 * @return 签名字符串
 */
std::string compute_signature(const std::string& data) {
    init_openssl();  // 确保OpenSSL已初始化

    // 创建哈希上下文
    EVP_MD_CTX* ctx = EVP_MD_CTX_new();
    if (!ctx) {
        char err_buf[1024];
        ERR_error_string_n(ERR_get_error(), err_buf, sizeof(err_buf));
        RCLCPP_INFO(rclcpp::get_logger("RobdogCenter"), "创建哈希上下文失败: %s", err_buf);
        throw std::runtime_error("Failed to create MD context");
    }

    unsigned char hash[EVP_MAX_MD_SIZE];
    unsigned int hash_len = 0;

    try {
        // 初始化哈希计算
        if (1 != EVP_DigestInit_ex(ctx, EVP_sha256(), nullptr)) {
            char err_buf[1024];
            ERR_error_string_n(ERR_get_error(), err_buf, sizeof(err_buf));
            RCLCPP_INFO(rclcpp::get_logger("RobdogCenter"), "初始化SHA-256失败: %s", err_buf);
            throw std::runtime_error("Failed to initialize SHA-256");
        }

        // 更新数据
        if (1 != EVP_DigestUpdate(ctx, data.data(), data.size())) {
            char err_buf[1024];
            ERR_error_string_n(ERR_get_error(), err_buf, sizeof(err_buf));
            RCLCPP_INFO(rclcpp::get_logger("RobdogCenter"), "更新哈希数据失败: %s", err_buf);
            throw std::runtime_error("Failed to update digest");
        }

        // 完成计算
        if (1 != EVP_DigestFinal_ex(ctx, hash, &hash_len)) {
            char err_buf[1024];
            ERR_error_string_n(ERR_get_error(), err_buf, sizeof(err_buf));
            RCLCPP_INFO(rclcpp::get_logger("RobdogCenter"), "完成哈希计算失败: %s", err_buf);
            throw std::runtime_error("Failed to finalize digest");
        }

        // 验证哈希长度
        if (hash_len != EVP_MD_size(EVP_sha256())) {
            RCLCPP_INFO(rclcpp::get_logger("RobdogCenter"), "无效的哈希长度: %d (应为32)", hash_len);
            throw std::runtime_error("Invalid hash length");
        }
    } catch (...) {
        EVP_MD_CTX_free(ctx);
        throw;
    }
    EVP_MD_CTX_free(ctx);

    // Base64编码
    size_t base64_len = ((hash_len + 2) / 3) * 4;
    std::string base64_str(base64_len, '\0');
    int result = EVP_EncodeBlock(
        reinterpret_cast<unsigned char*>(base64_str.data()),
        hash,
        hash_len
    );

    if (result != static_cast<int>(base64_len)) {
        char err_buf[1024];
        ERR_error_string_n(ERR_get_error(), err_buf, sizeof(err_buf));
        RCLCPP_INFO(rclcpp::get_logger("RobdogCenter"), 
                    "Base64编码失败: %s (预期长度: %zu, 实际: %d)",
                    err_buf, base64_len, result);
        throw std::runtime_error("Base64 encoding failed");
    }

    return base64_str;
}

/**
 * @brief URL编码函数（处理所有非保留字符，生成%XX格式）
 * @param s 待编码的字符串（如Base64签名）
 * @return 编码后的字符串（大写十六进制，如+→%2B）
 */
string urlEncode(const string& s) {
    ostringstream oss;
    oss << hex << uppercase; // 设置为十六进制大写格式

    for (char c : s) {
        // 保留字符：直接输出（字母、数字、-、_、.、~）
        if (isalnum(static_cast<unsigned char>(c)) || 
            c == '-' || c == '_' || c == '.' || c == '~') {
            oss << c;
        } else {
            // 非保留字符：编码为%XX（补0至两位十六进制）
            oss << "%" 
                << setw(2) << setfill('0') // 确保两位，不足补0
                << static_cast<int>(static_cast<unsigned char>(c)); // 避免符号扩展
        }
    }
    return oss.str();
}

/**
 * @brief 生成签名
 * @return 签名字符串
 * @note 此函数会确保配置加载、生成时间戳、拼接数据并计算签名
 */
std::string generate_signature() {
    try {
        
        // 获取当前时间戳（毫秒级）
        timestamp = get_current_timestamp();
        RCLCPP_INFO(rclcpp::get_logger("RobdogCenter"), "生成时间戳: %s", timestamp.c_str());
        
        // 拼接待签名字符串
        std::ostringstream data_stream;
        data_stream << "did=" << sn
                   << "&dt=" << dt
                   << "&timestamp=" << timestamp
                   << "&snPwd=" << snPwd;
        
        std::string data = data_stream.str();
        RCLCPP_INFO(rclcpp::get_logger("RobdogCenter"), "待签名数据: %s", data.c_str());
        
        // 计算签名
        std::string signature = compute_signature(data);
        RCLCPP_INFO(rclcpp::get_logger("RobdogCenter"), "生成签名: %s", signature.c_str());
        
        return signature;
    } catch (const std::exception& e) {
        RCLCPP_INFO(rclcpp::get_logger("RobdogCenter"), "生成签名失败: %s", e.what());
        throw;
    }
}

bool dynamic_QRcode() {
    try {
        // 确保配置已加载（线程安全）
        ensure_config_loaded();

        // ---------------------- 1. 定义路径（固定） ----------------------
        const std::string nobind_dir = "/usr/bin/cmcc_robot/install/robdog_control/share/robdog_control/resource/video/dynamic_nobind";
        const std::string img_path = nobind_dir + "/nobind.jpg";      // 二维码图片路径
        const std::string video_path = nobind_dir + "/dynamic_nobind.mp4";    // 输出视频路径

        // ---------------------- 2. 创建文件夹（避免路径不存在） ----------------------
        if (!std::filesystem::exists(nobind_dir)) {
            if (!std::filesystem::create_directories(nobind_dir)) {
                RCLCPP_INFO(rclcpp::get_logger("RobdogCenter"), "无法创建文件夹: %s", nobind_dir.c_str());
                return false;
            }
            RCLCPP_INFO(rclcpp::get_logger("RobdogCenter"), "创建文件夹成功: %s", nobind_dir.c_str());
        }

        // ---------------------- 3. 生成签名与构造API URL ----------------------
        std::string sign = generate_signature();  // 生成签名（依赖ensure_config_loaded）
        std::string encoded_sign = urlEncode(sign); // 2. 对签名进行URL编码（关键步骤）        
        std::ostringstream url_stream;

        // 根据环境选择API地址（修正原逻辑：online=true为测试环境，false为现网）
        if (online) {
            url_stream << "https://business.homibot.komect.com:9443/robot/business/api/user/client/robot/qr/getDeviceInfo?";
        } else {
            url_stream << "http://36.140.17.36:10000/robot/business/api/user/client/robot/qr/getDeviceInfo?";
        }

        // 添加查询参数（注意：参数名需与API要求一致，此处假设为`sign`）
        url_stream << "did=" << sn                 // 设备序列号（来自配置）
                   << "&dt=" << dt                 // 设备类型（来自配置）
                   << "&timestamp=" << timestamp   // 时间戳（generate_signature中生成）
                   << "&sign=" << encoded_sign;            // 签名（generate_signature的返回值）

        std::string api_url = url_stream.str();
        RCLCPP_INFO(rclcpp::get_logger("RobdogCenter"), "二维码API URL: %s", api_url.c_str());

        // ---------------------- 4. 生成二维码图片（使用qrencode） ----------------------
        std::ostringstream qr_cmd_stream;
        qr_cmd_stream << "qrencode -o \"" << img_path << "\" "  // 输出图片路径（双引号避免空格问题）
                      << "-s 10 "                               // 二维码像素大小（10x10）
                      << "'" << api_url << "'";                 // 编码内容（API URL）

        std::string qr_cmd = qr_cmd_stream.str();
        RCLCPP_INFO(rclcpp::get_logger("RobdogCenter"), "执行二维码生成命令: %s", qr_cmd.c_str());

        // 执行命令（返回0表示成功）
        int qr_status = system(qr_cmd.c_str());
        if (qr_status != 0) {
            RCLCPP_ERROR(rclcpp::get_logger("RobdogCenter"), "二维码生成失败，返回状态: %d", qr_status);
            return false;
        }
        RCLCPP_INFO(rclcpp::get_logger("RobdogCenter"), "二维码已生成: %s", img_path.c_str());

        // ---------------------- 5. 生成循环视频（使用ffmpeg） ----------------------
        std::ostringstream ffmpeg_cmd_stream;
        ffmpeg_cmd_stream << "ffmpeg "
                          << "-loop 1 "                                  // 循环输入图片（静态图转视频）
                          << "-i \"" << img_path << "\" "                 // 输入图片路径
                          << "-c:v libx264 "                             // 使用H.264编码
                          << "-preset ultrafast "                         // 最快编码速度（牺牲画质换速度）
                          << "-tune stillimage "                         // 针对静态图片优化（减少文件大小）
                          << "-vf \"transpose=2,scale=800:480:force_original_aspect_ratio=decrease,pad=800:480:(ow-iw)/2:(oh-ih)/2\" "
                          << "-r 1 "                                     // 帧率：1帧/秒（静态图无需高帧率）
                          << "-g 1 "                                     // GOP大小：1（每帧都是关键帧，方便seek）
                          << "-x264-params keyint=1 "                    // 强制每帧为关键帧（与-g 1配合）
                          << "-t 2 "                                     // 视频时长：2秒（循环播放）
                          << "-pix_fmt yuv420p "                         // 像素格式：兼容大部分设备
                          << "-an "                                      // 禁用音频（静态图无需音频）
                          << "-y "                                       // 覆盖输出文件（无需确认）
                          << "\"" << video_path << "\"";                 // 输出视频路径

        std::string ffmpeg_cmd = ffmpeg_cmd_stream.str();
        RCLCPP_INFO(rclcpp::get_logger("RobdogCenter"), "执行视频生成命令: %s", ffmpeg_cmd.c_str());

        // 执行命令（返回0表示成功）
        int ffmpeg_status = system(ffmpeg_cmd.c_str());
        if (ffmpeg_status != 0) {
            RCLCPP_INFO(rclcpp::get_logger("RobdogCenter"), "视频生成失败，返回状态: %d", ffmpeg_status);
            return false;
        }
        RCLCPP_INFO(rclcpp::get_logger("RobdogCenter"), "视频已生成: %s", video_path.c_str());

        // ---------------------- 6. 成功返回 ----------------------
        return true;
    } catch (const std::exception& e) {
        RCLCPP_INFO(rclcpp::get_logger("RobdogCenter"), "生成二维码/视频失败: %s", e.what());
        return false;
    }
}

void notifyWsMsgCallback(void *handle, const char *msg, int index) {
    nConnectIndex_ = index;
    // RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "notifyWsMsgCallback: %s", msg);
    currentTimeStramp_ = base::homiUtils::getCurrentTimeStamp();

    RobdogCenter *parent = (RobdogCenter *)handle;
    Json::Reader reader;
    Json::Value value;

    if (false == reader.parse(msg, value)) {
        return;
    }
    if (!value["type"].isNull()) {
        string strType = value["type"].asString();
        if ("connect_success" == strType) {
            bConnected_ = true;
            std::this_thread::sleep_for(std::chrono::milliseconds(20));
            Json::Value value;
            Json::Value params;
            value["client_type"] = CLIENT_LAUNCHER;
            value["action"] = "success";
            RobdogCenter::getInstance().SendtoNvOrin(value.toStyledString().c_str(), nConnectIndex_);
        }
    }
    if (!value["action"].isNull()) {
        if (parent) {
            parent->parseWsActionMsg(value);
        }
    }
}

std::string removeEscapeCharacters(const std::string& input) {
    std::string result;
    for (size_t i = 0; i < input.length(); ++i) {
        if (input[i] == '\\') {
            // Skip the escape character and handle the next character
            ++i;
            if (i < input.length()) {
                switch (input[i]) {
                    case 'n':  // Newline
                    case 't':  // Tab
                    case 'r':  // Carriage return
                    case 'b':  // Backspace
                    case 'f':  // Form feed
                    case 'a':  // Alert (bell)
                    case '\\': // Backslash
                    case '"':  // Double quote
                    case '\'': // Single quote
                        // Do nothing, skip these characters
                        result += input[i];
                        break;
                    default:
                        // If it's an unknown escape sequence, add the backslash and the character
                        result += '\\';
                        result += input[i];
                        break;
                }
            }
        } else {
            result += input[i];
        }
    }
    return result;
}

void replaceKeyInJsonList(Json::Value& jsonList, const std::string& oldKey, const std::string& newKey) {
    for (Json::Value::iterator it = jsonList.begin(); it != jsonList.end(); ++it) {
        if (it->isMember(oldKey)) {
            (*it)[newKey] = (*it)[oldKey];
            it->removeMember(oldKey);
        }
    }
}

void RobdogCenter::loadMappingTableFromParameters()
{
    try {
        auto video_paths = node_->get_parameter("mapping_video_paths").as_string_array();
        auto light_cmds = node_->get_parameter("mapping_light_cmds").as_integer_array();
        auto light_params = node_->get_parameter("mapping_light_params").as_integer_array();
        auto actions = node_->get_parameter("mapping_actions").as_string_array();
        auto audio_paths = node_->get_parameter("mapping_audio_paths").as_string_array();
        auto intervals = node_->get_parameter("mapping_intervals").as_integer_array();
        const size_t count = video_paths.size();
        if (light_cmds.size() != count || 
            light_params.size() != count ||
            actions.size() != count ||
            audio_paths.size() != count ||
            intervals.size() != count) {
            RCLCPP_ERROR(rclcpp::get_logger("robdog_control"), "Inconsistent array sizes in mapping parameters!");
            return;
        }
        mapping_table_.clear();
        for (size_t i = 0; i < count; ++i) {
            MappingEntry entry{
                video_paths[i],
                static_cast<int>(light_cmds[i]),
                static_cast<int>(light_params[i]),
                actions[i],
                audio_paths[i],
                static_cast<uint32_t>(intervals[i])
            };
            mapping_table_.push_back(entry);
        }
    }catch (const rclcpp::exceptions::InvalidParameterTypeException& e) {
        RCLCPP_ERROR(rclcpp::get_logger("robdog_control"), "Invalid parameter type: %s", e.what());
    } catch (const std::exception& e) {
        RCLCPP_ERROR(rclcpp::get_logger("robdog_control"), "Error loading mapping table: %s", e.what());
    }
}

RobdogCenter::RobdogCenter() {
    mapping_table_ = {
        {"video/emotion/neutrality/",DEEP_CMD_LIGHT_05,0,"sitDown","audio/boot/intro1.wav",12},
        {"video/emotion/happy/",DEEP_CMD_LIGHT_PINK_KEEP_AWAKE,0,"happy","audio/boot/intro2.wav",8},
        {"videos/greeting/",DEEP_CMD_LIGHT_BLUE_KEEP_AWAKE,0,"","audio/boot/intro3.wav",3},
        {"videos/greeting/",DEEP_CMD_LIGHT_ORANGE_KEEP_AWAKE,0,"greeting","audio/boot/intro4.wav",5},
        {"videos/emotion/festival/",DEEP_CMD_LIGHT_PINK_KEEP_AWAKE,0,"","audio/boot/intro5.wav",14},
    };
}

RobdogCenter::~RobdogCenter() {
    stopSequence();
    if (sequence_thread_.joinable()) {
        sequence_thread_.join();
    }
}

void RobdogCenter::update_map_points_path() {
    std::string map_points_path_dft = node_->getResourcePath("config/map_points.json");
    std::string current_mapid = RobotState::getInstance().getMapId();
    // std::string map_points_path_dft = "/etc/cmcc_robot";
    if (!node_->has_parameter("map_points_path")) {
        node_->declare_parameter<std::string>("map_points_path", map_points_path_dft); 
    }
    map_points_path = node_->get_parameter("map_points_path").as_string()+"/"+ current_mapid +".json";
    if(map_points_path_dft.c_str() != map_points_path && access(map_points_path.c_str(), F_OK) != 0) {
        // if map_points_path not config/map_points.json and map_points_path does not exist, copy it from config/map_points.json
        fs::copy_file(map_points_path_dft.c_str(), map_points_path.c_str());
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "missing %s, copy from %s", map_points_path.c_str(), map_points_path_dft.c_str());
    }
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "load map_points %s", map_points_path.c_str());
    //ReadMapCfg::getInstance().loadConfig(map_points_path); // 点位信息
}

void RobdogCenter::init(RobdogCtrlNode* node) {
    node_ = node;

    node_->handleLightControl(DEEP_CMD_LIGHT_03, 0);

    lastMoveMessageTime = node_->now();
    //Yaml文件读取参数，写到形参
    std::string robotdog_file_path_dft = node_->getResourcePath("config/config_robotdog.xml");
    if (!node_->has_parameter("robotdog_file_path")) {
        node_->declare_parameter<std::string>("robotdog_file_path", robotdog_file_path_dft); 
    }
    std::string robotdog_file_path = node_->get_parameter("robotdog_file_path").as_string();  
    if (access(robotdog_file_path.c_str(), F_OK) != 0) {
        //copyFile(robotdog_file_path_dft, robotdog_file_path);
        fs::copy_file(robotdog_file_path_dft.c_str(), robotdog_file_path.c_str());
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "missing %s, copy from %s", robotdog_file_path.c_str(), robotdog_file_path_dft.c_str());
    }
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "robotdog_file_path: %s", robotdog_file_path.c_str());
    RobotState::getInstance().loadConfig(robotdog_file_path);

    update_map_points_path();
    ReadMapCfg::getInstance().loadConfig(map_points_path); // 点位信息

    node_->declare_parameter<string>("ws_connect_url", "ws://192.168.1.110:19002"); 
    strConnectUrl_ = node_->get_parameter("ws_connect_url").as_string();  
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "ws_connect_url: %s", strConnectUrl_.c_str());

    node_->declare_parameter<int>("ws_connect_port", 19002); 
    uint32_t ws_port = node_->get_parameter("ws_connect_port").as_int();  
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "ws_connect_port: %d", ws_port);

    node_->declare_parameter<int>("max_cpu_temp", 67);
    max_cpu_temp_ = node_->get_parameter("max_cpu_temp").as_int();
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "max_cpu_temp: %d", max_cpu_temp_);

    node_->declare_parameter<int>("max_joint_temp", 110);
    max_joint_temp_ = node_->get_parameter("max_joint_temp").as_int();
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "max_joint_temp: %d", max_joint_temp_);

    node_->declare_parameter<int>("min_power_req", 13);
    min_power_req_ = node_->get_parameter("min_power_req").as_int();
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "min_power_req: %d", min_power_req_);

    std::vector<std::string> default_string_array;
    std::vector<int64_t> default_int_array;
    
    node_->declare_parameter("mapping_video_paths", default_string_array);
    node_->declare_parameter("mapping_light_cmds", default_int_array);
    node_->declare_parameter("mapping_light_params", default_int_array);
    node_->declare_parameter("mapping_actions", default_string_array);
    node_->declare_parameter("mapping_audio_paths", default_string_array);
    node_->declare_parameter("mapping_intervals", default_int_array);
    loadMappingTableFromParameters();
    if (mapping_table_.empty()) {
        RCLCPP_ERROR(rclcpp::get_logger("robdog_control"), "Failed to load mapping table!");
    } else {
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Loaded %zu mapping entries", mapping_table_.size());
        for (const auto& entry : mapping_table_) {
            RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Video: %s, Action: %s, Audio: %s, Interval: %u",
                        entry.video_path.c_str(),
                        entry.action.c_str(),
                        entry.audio_path.c_str(),
                        entry.interval_seconds);
        }
    }
    // WS服务启动
    WS_Init(EN_WS_ClIENT, ws_port);
    
    //设置接受msg的回调函数
    WS_SetMsgCallback(notifyWsMsgCallback, this);
    WS_Connect(strConnectUrl_.c_str());
    // ******************************************** ROS2
    platCmd_sub_ = node_->create_subscription<homi_speech_interface::msg::SIGCEvent>(
            "/homi_speech/sigc_event_topic", 10,
            [this, topic_name = "/homi_speech/sigc_event_topic"](
                const homi_speech_interface::msg::SIGCEvent::SharedPtr msg) {
                robctrlCallback(msg, topic_name);
            }
    );
    
    app_sub = node_->create_subscription<homi_speech_interface::msg::SIGCEvent>(
            "/homi_speech/sigc_event_topic_APP", 10,
            [this, topic_name = "/homi_speech/sigc_event_topic_APP"](
                const homi_speech_interface::msg::SIGCEvent::SharedPtr msg) {
                robctrlCallback(msg, topic_name);
            }
    );
    devAlarmRep_sub_ = node_->create_subscription<std_msgs::msg::String>(
        "/device_alarm_report", 20, 
        std::bind(&RobdogCenter::devAlarmReportCallback, this, std::placeholders::_1)); 

    
    deepCtrl_sub_ = node_->create_subscription<homi_speech_interface::msg::ProprietySet>(
        "/deep_udp_ctrl/status_report", 1, 
        std::bind(&RobdogCenter::deepStatusCallback, this, std::placeholders::_1)); // 接收到控制节点的消息后回调，写状态（主要是电量,充电状态等主动获取的状态）c

    rtk_status_sub_=node_->create_subscription<sensor_msgs::msg::NavSatFix>(
        "/fix", 10,
        std::bind(&RobdogCenter::navSatStatusCallback, this, std::placeholders::_1));

    navTripStatus_sub_ = node_->create_subscription<std_msgs::msg::String>("/task_status", 
        10, std::bind(&RobdogCenter::navTripStatusCallback, this, std::placeholders::_1));

    internet_connect_status_sub_ = node_->create_subscription<std_msgs::msg::String>(
        "internet_connect_status", 10,
        std::bind(&RobdogCenter::checkInternetConnectStatus, this, std::placeholders::_1));

    internet_conflict_sub_=node_->create_subscription<std_msgs::msg::String>(
        "internet_conflict", 10,
        std::bind(&RobdogCenter::checkInternetConflictStatus, this, std::placeholders::_1));

    net_sub_  = node_->create_subscription<std_msgs::msg::String>(
      "/net_monitor", 10,
      std::bind(&RobdogCenter::netMonitorCallback, this, std::placeholders::_1));
    
    wake_sub_=node_->create_subscription<homi_speech_interface::msg::Wakeup>(
        "/audio_recorder/wakeup_event", 10,
        std::bind(&RobdogCenter::wakeupCallback, this, std::placeholders::_1));
    velCmd_pub = node_->create_publisher<geometry_msgs::msg::Twist>(
        "/catch_turtle/ctrl_instruct", 10); // 向狗子发布速度命令话题（robot_move）
    actionCmd_pub = node_->create_publisher<homi_speech_interface::msg::RobdogAction>(
        "/catch_turtle/action_type", 1); // 向狗子发送特定运动指令消息（robot_action）
    continueMoveCmd_pub = node_->create_publisher<homi_speech_interface::msg::ContinueMove>(
        "/catch_turtle/continue_move", 1); // 向狗子发送持续运动信息（robot_move）
    follow_control_pub_ = node_->create_publisher<std_msgs::msg::String>("/follow_me/from_control", 10);
    adjust_distance_publisher_=node_->create_publisher<std_msgs::msg::String>("/adjust_distance",10);
    andlink_userkey_pub_ = node_->create_publisher<std_msgs::msg::String>("andlink_userkey", 10);
    flashlight_control_pub_ = node_->create_publisher<std_msgs::msg::String>("light_control", 10);

    /****************************** 感知算法交互模块 **************************/
    actionPlanningMove_pub = node_->create_publisher<std_msgs::msg::String>(
        "/navigation_control", 10); // 发布机器狗固定点位坐标到感知主机
    mappingControl_pub = node_->create_publisher<std_msgs::msg::String>(
        "/mapping_control", 10); // 地图更新给感知主机
    publishVirtualWall = node_->create_publisher<std_msgs::msg::String>(
        "/virtual_wall_control", 10); // 虚拟墙
    nvidiaService_client = node_->create_client<std_srvs::srv::Trigger>(
        "/current_task");//查询算法当前任务状态，包括建图导航漫步充电
    client_command_publisher_ = node_->create_publisher<std_msgs::msg::String>(
        "/uslam/client_command", 10);// 宇树slam接口
    point_transform_publisher_ = node_->create_publisher<std_msgs::msg::String>(
        "/point_transform", 10);
    /****************************************************************************************/
    platform_client = node_->create_client<homi_speech_interface::srv::SIGCData>(
        "/homi_speech/sigc_data_service"); // 上发给平台的消息
    app_client = node_->create_client<homi_speech_interface::srv::SIGCData>(
        "/homi_speech/sigc_data_service_APP");
    net_client = node_->create_client<homi_speech_interface::srv::NetCtrl>(
        "/homi_speech/network_service");
    // 向APP发送设备状态消息
    prope2app_pub = node_->create_publisher<homi_speech_interface::msg::ProperToApp>(
        "/catch_turtle/prope_toapp", 1);
    /****************************** 定时器 **********************************/
    ws_heartbeat_timer_ = node_->create_wall_timer(
        std::chrono::seconds(15),
        std::bind(&RobdogCenter::heartbeatTimerCallback, this));

    
    internet_timer_ = node_->create_wall_timer(
        std::chrono::seconds(5),
        std::bind(&RobdogCenter::internetTimerCallback, this));

    trip_timer_ = node_->create_wall_timer(
        std::chrono::seconds(2),
        std::bind(&RobdogCenter::tripTimerCallback, this));
    
    abnormal_monitor_trip = node_->create_wall_timer(
        std::chrono::milliseconds(300),
        std::bind(&RobdogCenter::trip_abnormal_monitor, this));
    // 定时器，1秒触发一次
    timer_2 = node_->create_wall_timer(
        std::chrono::seconds(1),
        std::bind(&RobdogCenter::publishProperties2APP, this));

    timer_robMove = node_->create_wall_timer(
        std::chrono::milliseconds(static_cast<int>(timer_interval * 1000)),
        std::bind(&RobdogCenter::timerCallback, this));

    // robPoseStatusTimer_ = node_->create_wall_timer(
    //     std::chrono::seconds(1),
    //     std::bind(&RobdogCenter::timerRobotPoseCallback, this));

    utStateTimer_ = node_->create_wall_timer(
        std::chrono::seconds(2),
        std::bind(&RobdogCenter::utStatusCallback, this));

    // robPathStatusTimer_ = node_->create_wall_timer(
    //     std::chrono::seconds(1),
    //     std::bind(&RobdogCenter::timerRobotPathCallback, this));

    // 初始化10分钟定时器用于检测是否需要主动求陪伴
    last_active_time_ = node_->now();
    inactivity_timer_ = node_->create_wall_timer(
        std::chrono::minutes(10),
        std::bind(&RobdogCenter::handleInactivityTimeout, this));

    // 初始化5分钟定时器用于检测是否进入休眠状态
    sleep_timer_ = node_->create_wall_timer(
        std::chrono::minutes(5),
        std::bind(&RobdogCenter::handleSleepTimeout, this));

    check_pos_timer_ = node_->create_wall_timer(
      std::chrono::seconds(1), std::bind(&RobdogCenter::check_pos_timeout, this));

    // odom_sub_ = node_->create_subscription<nav_msgs::msg::Odometry>(
    //   "/leg_odom2", 10, std::bind(&RobdogCenter::odom_callback, this, std::placeholders::_1));

    // 停止5分钟定时器，因为默认是亲密互动模式
    sleep_timer_->cancel();
    
    /****************************** 智能播报相关 **********************************/
    // 智能播报服务的客户端【上传播报文本】
    brocast_client = node_->create_client<homi_speech_interface::srv::AssistantSpeechText>(
        "/homi_speech/helper_assistant_speech_text_service");

    //是否结束当前任务
    endTask_client = node_->create_client<homi_speech_interface::srv::AssistantSpeechText>(
        "/homi_speech/helper_assistant_end_text_service");
    
    // wake_pub_ = node_->create_publisher<homi_speech_interface::msg::Wakeup>("/audio_recorder/wakeup_event", 10);
    developer_mode_pub_ = node_->create_publisher<std_msgs::msg::String>("/developer_mode_topic", 10);

    // 请求语音助手打断当前正在播放的内容
    brocast_abort_client = node_->create_client<homi_speech_interface::srv::AssistantAbort>(
        "/homi_speech/helper_assistant_abort_service");
    
    // 关闭和开启语音助手
    set_wake_client = node_->create_client<homi_speech_interface::srv::SetWakeEvent>(
        "/audio_node/set_wake_event_service");

    timer_brocast = node_->create_wall_timer(
        std::chrono::seconds(40),
        std::bind(&RobdogCenter::SendBrocastCallback, this));

    // 语音助手下发的是否播报被打断指令
    brocast_sub = node_->create_subscription<homi_speech_interface::msg::AssistantEvent>(
        "/homi_speech/speech_assistant_status_topic", 100,
        std::bind(&RobdogCenter::BrocastIfAbortCallBack, this, std::placeholders::_1));
    /****************************** 定时器 **********************************/
    // 发布状态信息话题
    status_pub_ = node_->create_publisher<homi_speech_interface::msg::ProprietySet>(
        "/deep_udp_ctrl/status_ctrl", 1);

    // 定时器检查状态
    timerDog = node_->create_wall_timer(
        std::chrono::milliseconds(100),
        std::bind(&RobdogCenter::checkStatusWatchdog, this));

    // 自定义唤醒词
    set_diyWakeup_client_ = node_->create_client<homi_speech_interface::srv::SetDiyWord>(
        "/audio_recorder/diy_wakeup_service");

    // 接听/挂断/呼叫
    phone_call_client_ = node_->create_client<homi_speech_interface::srv::PhoneCall>(
        "/cmcc_rtc/phone_call_service");

    /****************************** RTK帐号下发 **********************************/
    ntrip_account_client_ = node_->create_client<homi_speech_interface::srv::NtripAccount>("/ntrip_user_pwd");
    ntrip_expire_day_sub_  = node_->create_subscription<std_msgs::msg::String>(
      "/ntrip_expire_day", 10,
      std::bind(&RobdogCenter::ntripExpireDayCallback, this, std::placeholders::_1));
    ntrip_status_sub_  = node_->create_subscription<std_msgs::msg::Int64>(
      "/ntrip_status", 10,
      std::bind(&RobdogCenter::ntripStatusCallback, this, std::placeholders::_1));
    ntrip_account_sub_  = node_->create_subscription<std_msgs::msg::String>(
      "/ntrip_account_topic", 10,
      std::bind(&RobdogCenter::ntripAccountCallback, this, std::placeholders::_1));
    rtkAccountReportTimer_ = node_->create_wall_timer(
      std::chrono::seconds(30),  // 30秒后首次触发
      std::bind(&RobdogCenter::rtkAccountReportTimerCallback, this)
    );

    // use unitree API check nvi status
    unitreeServerLog_sub_ = node_->create_subscription<std_msgs::msg::String>(
        "/uslam/server_log", 10, 
        std::bind(&RobdogCenter::uslamServerLogCallback, this, std::placeholders::_1)); 
    unitreeCmdClient_pub_ = node_->create_publisher<std_msgs::msg::String>(
        "/uslam/client_command", 10);
    
    point_transform_result_sub_  = node_->create_subscription<std_msgs::msg::String>(
        "/point_transform_result", 10,
        std::bind(&RobdogCenter::pointTransformResultCallback, this, std::placeholders::_1));
    
    /****************************** 安防任务相关 **********************************/    
    liveStreamTask_pub = node_->create_publisher<homi_speech_interface::msg::LiveStreamTask>("/live_stream/live_stream_task", 10);

    buildStatusToCodeMapping();
}

// void RobdogCenter::resetInactivityTimer() {
//     inactivity_timer_->cancel();
//     inactivity_timer_->reset();
// }

void RobdogCenter::SendtoNvOrin(const char* message, int nInd)
{
    WS_Send(message, nInd);
}

std::string RobdogCenter::getSingleMessage(const std::vector<std::string>& messages)
{
    if (messages.empty()){
    return "";
    }
    return messages[0];
}

 void RobdogCenter::navSatStatusCallback(const sensor_msgs::msg::NavSatFix::SharedPtr msg)
 {
    rtkMsg=*msg;
    status_.rtk.current=rtkMsg.status.status;
    if(rtkMsg.status.status<=0){
        rtkMsg.latitude=0;
        rtkMsg.longitude=0;
    }
    int8_t status=msg->status.status;
    if(status == 2 || status == 1){
        rtk_status_flag = true;
    }
    else{
        rtk_status_flag = false;
    }
 }

void RobdogCenter::checkInternetConnectStatus(const std_msgs::msg::String::SharedPtr msg)
{
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Received message: '%s'", msg->data.c_str());
    std::string strMsg = msg->data; // JSON字段
    Json::Value value;
    Json::Reader reader;
    if (!reader.parse(strMsg, value)) return; // 解析 JSON

    if (value.isMember("isInternetConnect"))
    {
        std::string isInternetConnect = value["isInternetConnect"].asString();
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "isInternetConnect: %s", isInternetConnect.c_str());

        bool new_status = (isInternetConnect == "true");

        // 仅在网络状态发生变化时执行动作
        if (new_status != is_internet_connected_ ) {
            RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Internet status is changed !!!");
            std::string video_path;
            if (new_status) {
                // video_path = node_->getResourcePath(resource_default_video_path);
                // // 播报网络连接成功提示音
                // std::thread t_audio(&RobdogCtrlNode::playAudio,node_,node_->getResourcePath("internet/internet_normal.wav"));
                // t_audio.detach();   

                handleDeviceSettingQuery();             
            }
            else {
                RCLCPP_WARN(rclcpp::get_logger("robdog_control"), "Internet is disconnected.");
            }
            ExpressionChange::getInstance().async_callback_work(video_path, 0);
        } 

        is_internet_connected_ = new_status;

    } 
    else {
        RCLCPP_ERROR(rclcpp::get_logger("robdog_control"), "Key 'isInternetConnect' not found in JSON");
    }

    if (value.isMember("isWifiConnect"))
    {
        std::string isWifiConnect = value["isWifiConnect"].asString();
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "isWifiConnect: %s", isWifiConnect.c_str());

        bool new_status = (isWifiConnect == "true");

        if (new_status != is_wifi_connected_ ) {
            RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "wifi is connected.");
            if (new_status) {
                std::vector<std::string> messages = {
                    "我们是回家了吗"};
                std::string message = getSingleMessage(messages);
                RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "current message: %s", message.c_str());
                sendStringToBrocast(message);                
            }
            else {
                RCLCPP_WARN(rclcpp::get_logger("robdog_control"), "wifi is disconnected.");
                current_mode_ = RobotState::getInstance().getRobdogStatus();
                if(current_mode_ == 0 && rtk_status_flag)
                {
                    std::vector<std::string> messages = {
                    "我们是要去外面玩了吗"};
                    std::string message = getSingleMessage(messages);
                    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "current message: %s", message.c_str());
                    sendStringToBrocast(message);
                }

            }
        }

        is_wifi_connected_ = new_status;  

    }
    else {
        RCLCPP_ERROR(rclcpp::get_logger("robdog_control"), "Key 'isWifiConnect' not found in JSON");
    }

}


void RobdogCenter::checkInternetConflictStatus(const std_msgs::msg::String::SharedPtr msg){
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Received checkInternetConflictStatus message: '%s'", msg->data.c_str());
    std::string strMsg = msg->data; 
    if(strMsg =="wlan0_conflict_notify"){
      
        // 播报网络冲突提示音
        std::thread t_audio(&RobdogCtrlNode::playAudio,node_,node_->getResourcePath("internet/internet_conflict.wav"));
        t_audio.detach(); 
        
    }

}

// 更新上次活动时间
void RobdogCenter::updateLastActiveTime()
{
    last_active_time_ = node_->now();
    asked_in_last_hour_ = false;
    quiet_for_three_hours_ = false;
}

// 机器狗主动求陪伴逻辑
void RobdogCenter::activeDog()
{
    //室内漫步需要切换到自主模式
    homi_speech_interface::msg::RobdogAction zzmsg;
    zzmsg.actiontype = "NavCtrl";
    zzmsg.actionargument = "AutoMode";
    // publishAction(zzmsg);
    publishAction(std::make_shared<homi_speech_interface::msg::RobdogAction>(zzmsg));

    // 预留和导航原地溜达接口
    Json::Value reqValue;
    reqValue["client_type"] = CLIENT_LAUNCHER;
    reqValue["target_client"] = CLIENT_NVIDIA;         
    reqValue["action"] = "ramble_control";
    
    Json::Value value_ramble;
    value_ramble["action"] = 0;
    reqValue["params"] = value_ramble;
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"),"The message of value_ramble: %s", reqValue.toStyledString().c_str());
    SendtoNvOrin(reqValue.toStyledString().c_str(), nConnectIndex_);
    
    std::vector<std::string> messages = {
        "唉, 我这会儿感觉挺无聊的, 有没有人能陪我玩会儿呢?",
        "哎哟, 我无聊得很啦, 有没有人来和我一起玩会儿呀?",
        "呀, 我有点无聊, 真希望有人能陪我玩一会儿.",
        "哎呀, 我实在无聊, 谁能来跟我消遣一会儿呀?",
        "嘿, 我这会儿无聊透顶, 有没有人能来和我玩会儿?",
        "哟, 我觉得好无聊, 有没有人愿意陪我玩上一会儿呢?",
        "哎, 无聊感袭来, 有没有人可以跟我玩一会儿呀?",
        "哎呀, 无聊得慌, 有没有人能抽空和我玩会儿呢?",
        "嗨, 我无聊得不行, 有没有人能陪我娱乐一会儿呀?",
        "哟呵, 我太无聊啦, 有没有人来跟我玩一阵子呀?",
        "哎, 我无聊得快受不了, 有没有人来和我玩会儿?",
        "哎呀, 我无聊得发慌, 有没有人能陪我玩会儿解解闷?",
        "嘿呀, 我无聊得很, 有没有人能跟我玩会儿打发时间?",
        "哟, 无聊至极, 有没有人愿意陪我玩一会儿呀?",
        "哎, 我这无聊劲儿上来了, 有没有人跟我玩会儿呀?",
        "哎呀, 无聊得快长草了, 有没有人来和我玩会儿呀?",
        "嗨哟, 我无聊得不知所措, 有没有人陪我玩会儿?",
        "哟呵, 无聊得很呐, 有没有人能跟我玩一会儿呢?",
        "哎, 无聊到爆了, 有没有人来和我玩会儿呀?",
        "哎呀, 无聊得不行了, 有没有人能陪我玩一会儿呀?"
    };
    
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, messages.size() - 1);
    std::string message = messages[dis(gen)];

    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Requesting accompany: %s", message.c_str());
    auto request = std::make_shared<homi_speech_interface::srv::AssistantSpeechText::Request>();
    request->msg = message;

    if (!endTask_client->wait_for_service(std::chrono::seconds(1))) {
        RCLCPP_ERROR(rclcpp::get_logger("robdog_control"), "Service not available after waiting");
        return;
    }
    endTask_client->async_send_request(request);

    timer_ = node_->create_wall_timer(
        std::chrono::seconds(15),
        [this]() {
            // homi_speech_interface::msg::Wakeup wake_msg;
            // wake_msg.ivw_word = "xiaolixiaoli";
            // wake_msg.angle = 0;
            // wake_msg.extra_info = "";
            // wake_pub_->publish(wake_msg);
            // RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Wakeup message published after 15 seconds");

            // 结束室内漫步
            Json::Value reqValue;
            reqValue["client_type"] = CLIENT_LAUNCHER;
            reqValue["target_client"] = CLIENT_NVIDIA;         
            reqValue["action"] = "ramble_control";
            Json::Value value_ramble;
            value_ramble["action"] = 1;
            reqValue["params"] = value_ramble;
            RCLCPP_INFO(rclcpp::get_logger("robdog_control"),"The message of end_ramble: %s", reqValue.toStyledString().c_str());
            SendtoNvOrin(reqValue.toStyledString().c_str(), nConnectIndex_);
            
            // 取消定时器以确保只执行一次
            timer_->cancel();
        }
    );
}

// 处理10分钟定时器到期事件，检查是否需要主动求陪伴
void RobdogCenter::handleInactivityTimeout() {
    if (!inactivity_mode_)
    {
        return;
    }

    // 检查是否已经超过三个小时没有主动求陪伴
    auto now = std::chrono::system_clock::now();
    std::time_t now_time = std::chrono::system_clock::to_time_t(now);

    if (quiet_for_three_hours_ && now_time > quiet_for_three_hours_until_) {
        quiet_for_three_hours_ = false;
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Quiet for three hours period has ended.");
    }

    rclcpp::Duration inactive_diff = node_->now() - last_active_time_;
    if (inactive_diff >= std::chrono::hours(1) - std::chrono::minutes(1))
    {
        asked_in_last_hour_ = false; // 重置 asked_in_last_hour_
    }

    if (!quiet_for_three_hours_ && !asked_in_last_hour_)
    {
        if (inactive_diff >= std::chrono::minutes(10) - std::chrono::seconds(10))
        {
            asked_in_last_hour_ = true;
            last_active_time_ = node_->now();
            inactivity_timer_->reset();
            activeDog();
        } 
    }
}

// 处理5分钟定时器到期事件，检查是否进入休眠状态
void RobdogCenter::handleSleepTimeout() {
        if (inactivity_mode_)
        {
            return;
        }
        rclcpp::Duration sleep_diff = node_->now() - last_active_time_;
        if (sleep_diff >= std::chrono::minutes(5) - std::chrono::seconds(5))
        {
            // 进入休眠状态，趴下
            sleep_timer_->reset();
            RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Machine dog entering sleep mode due to inactivity.");
            homi_speech_interface::msg::RobdogAction msg;
            msg.actiontype = "motorSkill";
            msg.actionargument = "getDown";
            publishAction(std::make_shared<homi_speech_interface::msg::RobdogAction>(msg));
        }    
}

void RobdogCenter::readMappingPoints() {
    // 检查参数是否已经声明
    // if (!node_->has_parameter("map_points_path")) {
    //     std::string map_points_path = node_->getResourcePath("config/map_points.json");
    //     node_->declare_parameter<std::string>("map_points_path", map_points_path);
    // } else {
    //     RCLCPP_WARN(rclcpp::get_logger("robdog_control"), "参数 'map_points_path' 已经声明，跳过重新声明");
    // } 
    // map_points_path = node_->get_parameter("map_points_path").as_string();

    ReadMapCfg::getInstance().loadConfig(map_points_path); // 点位信息

}

void RobdogCenter::updateMapPoints(const std::string& event, const Json::Value& points) {
    // 创建一个新的 root 对象
    Json::Value root;
    bool enable_points = true;

    update_map_points_path();
    // 根据 event 字段确定对应的点位类型
    std::string point_type;
    if (event == "deliverExpress" || event == "fetchExpress") {
        point_type = "press_point";
    } else if (event == "takePhotos") {
        point_type = "photo_point";
    } else if (event == "familyMovePoint") {
        point_type = "familyMovePoint";
    } else if (event == "parkPatrol") {
        point_type = "patrol_point";
    } else if (event == "goHome") {
        point_type = "goHome_point";
    } else if (event == "deliverCake") {
        point_type = "deliverCake_point";
    } else if (event == "batteryCharging") {
        point_type = "batteryChargingPoint";
    } else if (event == "chargeNav") {
        point_type = "chargeNavPoint";
        enable_points = false;
    } else {
        std::cerr << "Unknown updateMapPoints event type: " << event << std::endl;
        return;
    }

    // 2. load current map(if exist)
    std::ifstream inFile(map_points_path);
    if (inFile.is_open()) {
        try {
            inFile >> root;
        } catch (...) {
            std::cerr << "Error parsing existing JSON file" << std::endl;
            root = Json::objectValue;
        }
        inFile.close();
    } else {
        // if map not exist, create new
        root = Json::objectValue;
    }

    // 3. update point
    if (!root.isMember("mapping_point")) {
        root["mapping_point"] = Json::objectValue;
    }
    
    // only update current point type

    if (enable_points == true) {
        root["mapping_point"][point_type]["points"] = points;
    } else {
        root["mapping_point"][point_type] = points;
    }

    // 4. write to file
    std::ofstream outFile(map_points_path);
    if (!outFile.is_open()) {
        std::cerr << "Failed to open file for writing: " << map_points_path << std::endl;
        return;
    }
    
    outFile << root.toStyledString();
    outFile.close();

    // verify content (optional)
    std::ifstream verifyFile(map_points_path);
    if (!verifyFile.is_open()) {
        RCLCPP_ERROR(rclcpp::get_logger("robdog_control"), "Failed to open file: %s", map_points_path.c_str());
        return;
    }

    std::string content((std::istreambuf_iterator<char>(verifyFile)), std::istreambuf_iterator<char>());
    verifyFile.close();
    std::cout << "Updated content of map_points.json:\n" << content << std::endl;
}

// 暂停函数
void RobdogCenter::sleepForDuration() {
    // ros::Duration(seconds).sleep();
    rclcpp::sleep_for((std::chrono::seconds(2))); 
}

// 发送建图初始化响应
void RobdogCenter::sendMapInitialResponse(int code, const std::string& msg) {
    Json::Value response;
    response["deviceId"] = RobotState::getInstance().getDeviceId();
    response["domain"] = "APP_DEVICE_INTERACTION";
    response["event"] = "robot_map_draw_init_response";
    response["eventId"] = getEventId();
    response["requestId"] = "requestId"+to_string(base::homiUtils::getCurrentTimeStamp());
    response["seq"] = to_string(base::homiUtils::getCurrentTimeStamp());

    Json::Value body;
    body["code"] = code;
    body["msg"] = msg;
    response["body"] = body;

    Json::FastWriter writer;
    std::string jsonString = writer.write(response);
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "robot_map_draw_init_response: %s", jsonString.c_str());
    sendRequestData(jsonString);
}

// 发送充电桩标记响应
void RobdogCenter::sendChargeMarkResponse(int code, const std::string& msg) {
    Json::Value response;
    response["deviceId"] = RobotState::getInstance().getDeviceId();
    response["domain"] = "APP_DEVICE_INTERACTION";
    if(isChargeMarking){
        response["event"] = "charging_pile_mark_response";
    }else if(is_charging){
        response["event"] = "charging_action_response";
    }
    response["eventId"] = "eventId_" + to_string(base::homiUtils::getCurrentTimeStamp());
    response["requestId"] = "requestId"+to_string(base::homiUtils::getCurrentTimeStamp());
    response["seq"] = to_string(base::homiUtils::getCurrentTimeStamp());

    Json::Value body;
    body["code"] = code;
    body["msg"] = msg;
    std::string charge_pile_id = RobotState::getInstance().getMapId();
    
    body["mapId"] = static_cast<Json::Int64>(std::stoll(charge_pile_id));
    response["body"] = body;

    Json::FastWriter writer;
    std::string jsonString = writer.write(response);
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "%s response: %s", response["event"].asString().c_str(),jsonString.c_str());
    sendRequestData(jsonString);
}

//超时处理函数
// void RobdogCenter::startTimeout(std::function<void()> timeoutCallback, std::atomic<bool>& waiting_flag, std::chrono::seconds timeout_duration) {
//     waiting_flag = true;
//     auto start_time = std::chrono::steady_clock::now(); // 记录开始等待的时间

//     // 异步执行超时处理
//     std::async(std::launch::async, [this, timeoutCallback, start_time, &waiting_flag, timeout_duration]() {
//         std::this_thread::sleep_for(timeout_duration);
//         if (waiting_flag) {
//             waiting_flag = false;
//             timeoutCallback(); // 执行超时回调函数
//         }
//     });
// }
void RobdogCenter::startTimeout(std::function<void()> timeoutCallback, std::function<void()> changeCallback, std::atomic<bool>& waiting_flag, std::chrono::seconds timeout_duration) {
    waiting_flag = true;
    auto start_time = std::chrono::steady_clock::now(); // 记录开始等待的时间

    // 异步执行超时处理
    std::async(std::launch::async, [this, timeoutCallback, changeCallback, start_time, &waiting_flag, timeout_duration]() {
        while (std::chrono::steady_clock::now() - start_time < timeout_duration) {
            if (!waiting_flag) {
                changeCallback(); // 调用changeCallback函数
                return; // 如果waiting_flag变为false，退出循环
            }
            std::this_thread::sleep_for(std::chrono::milliseconds(100)); // 每100毫秒检查一次
        }
        if (waiting_flag) {
            waiting_flag = false;
            timeoutCallback(); // 执行超时回调函数
        }
    });
}
//导航节点关闭超时函数
void RobdogCenter::navTimeoutHandler(const Json::Value &value,const std::string& topic_name) {
    startTimeout([this]() {
        int taskStatusCode = ERROR_CODE_MAPINI_TIME_OUT;
        std::string taskStatusMsg = getDescription(taskStatusCode);
        sendMapInitialResponse(taskStatusCode,taskStatusMsg); // 超时未关闭按初始化失败处理
        RobotState::getInstance().setCurrentState(RobotStateEnum::NORMAL); // 超时未重连，设置状态为 NORMAL
    }, [this, value, topic_name]() {
        response_start_time = std::chrono::steady_clock::now();
        handleEvent(value["event"].asString(), value, value["body"], topic_name); 
        mapTimeoutHandler();
    },Navigation_node, std::chrono::seconds(20));
}
//建图初始化超时函数
void RobdogCenter::mapTimeoutHandler() {
    startTimeout([this]() {
        int taskStatusCode = ERROR_CODE_MAPINI_TIME_OUT;
        std::string taskStatusMsg = getDescription(taskStatusCode);
        sendMapInitialResponse(taskStatusCode,taskStatusMsg); // 超时视为失败
        RobotState::getInstance().setCurrentState(RobotStateEnum::NORMAL); // 超时未重连，设置状态为 NORMAL
    }, [this]() {
        // 处理Navigation_node变为false的情况
    },waiting_for_response, std::chrono::seconds(20));
}

void RobdogCenter::disconnectTimeoutHandler() {
    startTimeout([this]() {
        RobotState::getInstance().setCurrentState(RobotStateEnum::NORMAL); // 超时未重连，设置状态为 NORMAL
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Timeout waiting for reconnect, setting state to NORMAL");
        sendCommandToUSLAM("mapping/stop");
        std::string current_mapid = RobotState::getInstance().getMapId();
        sendMapAction(2,"",current_mapid);
    },  []() {}, waiting_for_reconnect, std::chrono::seconds(15));
}

// ******************************************  处理平台的事件 *********************************************************************
// ****************** 处理平台的消息 **************************
void RobdogCenter::robctrlCallback(const homi_speech_interface::msg::SIGCEvent::SharedPtr msg,const std::string& topic_name) {
    static int count = 0;
    
    try {
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Received msg from topic %s: %s",topic_name.c_str(), msg->event.c_str());
        // 解析控制指令
        std::string strMsg = msg->event; // JSON字段
        Json::Value value;
        Json::Reader reader;
        if (!reader.parse(strMsg, value)) return; // 解析 JSON
        Json::Value body = value["body"];
        if (!body["points"].isNull()) {
            updateMapPoints(body["event"].asString(), body["points"]);
        }
        // 除了心跳，更新休眠时间
        if (value["event"] != "keep_alive"){
            updateLastActiveTime(); 
        }
        else{
            count++;
            RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "count: %d", count);
            if(count == 1 || count == 2)
            {
                handleBindStatusQuery();
                handleDeviceSettingQuery();
            }

        }

        // 定义建图语音不可执行的event
        const std::unordered_set<std::string> map_events = {"robot_action", "move_points"};
        // 定义导航时不可执行的event
        const std::unordered_set<std::string> nav_events = {"robot_action", "map_draw"};
        // 检查必须的字段
        if (!value["deviceId"].isNull() && !value["event"].isNull()) {
            RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Event: %s", value["event"].asString().c_str()); // 打印 event
            RobotState::getInstance().setDeviceId(value["deviceId"].asString());
            RobotStateEnum currentState = RobotState::getInstance().getCurrentState();
            RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "CurrentState: %d", static_cast<int>(currentState));
            switch (currentState) {
                case RobotStateEnum::NORMAL:
                    if (value["event"] == "map_draw" && value["body"]["action"] == "start") {  
                        response_start_time = std::chrono::steady_clock::now();
                        RobotState::getInstance().setCurrentState(RobotStateEnum::MAP_INITIALIZING);                     
                        setEventId(value["eventId"].asString());
                        // handleEvent(value["event"].asString(), value, value["body"], topic_name); 
                        // std::thread t(std::bind(&RobdogCenter::mapTimeoutHandler,this));
                        std::thread t(std::bind(&RobdogCenter::navTimeoutHandler, this, value, topic_name));
                        t.detach(); 
                        checkNvidiaServiceStatus(false,"");//关闭建图导航节点
                    } else {
                        handleEvent(value["event"].asString(), value, value["body"], topic_name); 
                    }
                    break;
                case RobotStateEnum::MAP_INITIALIZING:
                    if (value["event"] == "app_disconnect") {
                        disconnectTimeoutHandler();
                    } else if (value["event"] == "app_connected") {
                        waiting_for_reconnect = false; // 收到连接指令，取消超时处理
                    } else if (value["event"] == "map_draw" && value["body"]["action"] == "start") {
                        setEventId(value["eventId"].asString());
                        mapTimeoutHandler();
                    } else {
                        handleEvent(value["event"].asString(), value, value["body"], topic_name); 
                    }
                    break;
                case RobotStateEnum::MAPPING:
                    if (value["event"] == "app_disconnect") {
                        disconnectTimeoutHandler();
                    } else if (value["event"] == "app_connected") {
                        waiting_for_reconnect = false; // 收到连接指令，取消超时处理
                    } else if (value["event"] == "map_draw" && value["body"]["action"] == "start") {
                        setEventId(value["eventId"].asString());
                        std::this_thread::sleep_for(std::chrono::milliseconds(500));
                        handleEvent(value["event"].asString(), value, value["body"], topic_name); 
                        std::thread t(std::bind(&RobdogCenter::mapTimeoutHandler,this));
                        t.detach(); 
                        checkNvidiaServiceStatus(false,"");//关闭建图导航节点
                    } else if (map_events.find(value["event"].asString()) != map_events.end()) {
                        std::string message = "我正在建图呢,你希望我现在结束任务,跟你互动吗";
                        auto request = std::make_shared<homi_speech_interface::srv::AssistantSpeechText::Request>();
                        request->msg = message;
                        if (!endTask_client->wait_for_service(std::chrono::seconds(1))) {
                            RCLCPP_ERROR(rclcpp::get_logger("robdog_control"), "Service not available after waiting");
                            return;
                        }
                        endTask_client->async_send_request(request);
                    } else {    
                        handleEvent(value["event"].asString(), value, value["body"], topic_name);
                    }
                    break;
                case RobotStateEnum::NAVIGATION:
                    if (nav_events.find(value["event"].asString()) != nav_events.end()) {
                        std::string message = "我在导航呢,请先结束导航任务";
                        sendStringToBrocast(message);
                        RobotState::getInstance().setFollowMeStatus("off");
                        RobotState::getInstance().setCurrentState(RobotStateEnum::NORMAL); 
                        Json::Value response;
                        response["deviceId"] = RobotState::getInstance().getDeviceId();
                        response["domain"] = "BUSINESS_REPORT";
                        response["event"] = "data_report";
                        response["eventId"] = to_string(std::chrono::duration_cast<std::chrono::seconds>(std::chrono::system_clock::now().time_since_epoch()).count());
                        response["seq"] = to_string(std::chrono::duration_cast<std::chrono::seconds>(std::chrono::system_clock::now().time_since_epoch()).count());
                        response["body"]["type"] = "followMe";
                        response["body"]["data"]["status"]=RobotState::getInstance().getFollowMeStatus();
                        response["body"]["data"]["code"]= 0;
                        response["body"]["data"]["isFirstTime"]=false;
                        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "RobotState::getInstance().getFollowMeStatus is %s",RobotState::getInstance().getFollowMeStatus().c_str());
                        Json::FastWriter writer;
                        std::string jsonString = writer.write(response);
                        RCLCPP_INFO(rclcpp::get_logger("robdog_control"),"Follow me report msg is : %s", jsonString.c_str());
                        sendRequestData(jsonString);
                    } else {    
                        handleEvent(value["event"].asString(), value, value["body"], topic_name);
                    }
                    break;
                case RobotStateEnum::FOLLOWME:
                {
                    if (value["event"] == "app_disconnect") {
                        disconnectTimeoutHandler();
                    } else if (value["event"] == "app_connected") {
                        waiting_for_reconnect = false; // 收到连接指令，取消超时处理
                    } else if (value["event"] == "map_draw" && value["body"]["action"] == "start") {
                        setEventId(value["eventId"].asString());
                        RCLCPP_WARN(rclcpp::get_logger("robdog_control"),"%d----In the middle of a follow-up mission, stop mapping?",__LINE__); 
                        sendMapInitialResponse(ERROR_CODE_MAP_COMMAND_DURING_FOLLOW, "设备正在跟随任务中,无法建图");   
                        std::string message = "我正在跟随任务中呢，你是希望我结束跟随吗？";
                        sendStringToBrocast(message);
                    } else if(value["event"].asString()=="robot_action")
                    {
                        const std::string ACTION_TYPE_FOLLOW_ME = "followMe";
                        const std::string ACTION_TYPE_EMERGENCY_STOP = "emergencyStop";
                        const std::string ARGUMENT_OFF = "off";
                        const std::vector<std::string> MOVEMENT_COMMANDS = {
                            "comeClose", "goFar", "goLeft", "goRight", "comeHere","goBehind"
                        };
                        const auto& body = value["body"];
                        const std::string actionType = body["actionType"].asString();
                        std::string actionArg;
                        if (body.isMember("actionArgument")) {
                            actionArg = body["actionArgument"].asString();
                        } else if (body.isMember("actionArguement")) { 
                            actionArg = body["actionArguement"].asString();
                        }
                        if ((actionType == ACTION_TYPE_EMERGENCY_STOP) ||
                            (actionType == ACTION_TYPE_FOLLOW_ME && 
                            (ARGUMENT_OFF == actionArg || 
                            std::find(MOVEMENT_COMMANDS.begin(), MOVEMENT_COMMANDS.end(), actionArg) != MOVEMENT_COMMANDS.end()))) 
                            handleEvent(value["event"].asString(), value, value["body"], topic_name);                        
                        else {
                            RCLCPP_WARN(rclcpp::get_logger("robdog_control"),"In the middle of a follow-up mission, is it over?");    
                            std::string message = "我正在跟随任务中呢，你是希望我结束跟随吗？";
                            sendStringToBrocast(message);
                        }                        
                    }
                    else if ((value["event"].asString()=="robot_move") || (value["event"].asString()=="robot_view")){
                        RCLCPP_INFO(rclcpp::get_logger("robdog_control"),"The remote control has taken over, and the end of the follow");
                        std::string message = "遥控已经接管，结束自动跟随";
                        sendStringToBrocast(message);
                        actionFollow(0);
                        RobotState::getInstance().setFollowMeStatus("off");
                        RobotState::getInstance().setCurrentState(RobotStateEnum::NORMAL); 
                        Json::Value response;
                        response["deviceId"] = RobotState::getInstance().getDeviceId();
                        response["domain"] = "BUSINESS_REPORT";
                        response["event"] = "data_report";
                        response["eventId"] = to_string(std::chrono::duration_cast<std::chrono::seconds>(std::chrono::system_clock::now().time_since_epoch()).count());
                        response["seq"] = to_string(std::chrono::duration_cast<std::chrono::seconds>(std::chrono::system_clock::now().time_since_epoch()).count());
                        response["body"]["type"] = "followMe";
                        response["body"]["data"]["status"]=RobotState::getInstance().getFollowMeStatus();
                        response["body"]["data"]["code"]= 0;
                        response["body"]["data"]["isFirstTime"]=false;
                        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "RobotState::getInstance().getFollowMeStatus is %s",RobotState::getInstance().getFollowMeStatus().c_str());
                        Json::FastWriter writer;
                        std::string jsonString = writer.write(response);
                        RCLCPP_INFO(rclcpp::get_logger("robdog_control"),"Follow me report msg is : %s", jsonString.c_str());
                        sendRequestData(jsonString);
                    }
                    else if (value["event"].asString()=="move_points")
                    {
                        RCLCPP_WARN(rclcpp::get_logger("robdog_control"),"In the middle of a follow-up mission, is it over?");    
                        std::string message = "我正在跟随任务中呢，你是希望我结束跟随吗？";
                        sendStringToBrocast(message);
                    }
                    else {
                        handleEvent(value["event"].asString(), value, value["body"], topic_name);
                    }
                    break;
                default:
                    if (value["event"] == "map_draw" && value["body"]["action"] == "start") {
                        handleEvent(value["event"].asString(), value, value["body"], topic_name);
                        mapTimeoutHandler();
                    } else {
                        handleEvent(value["event"].asString(), value, value["body"], topic_name); 
                        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "current status cannot be obtained normally,handleEvent is used,CurrentState: %d", static_cast<int>(currentState));
                    }
                    break;
                }
            }
        }else {
            RCLCPP_INFO_STREAM(rclcpp::get_logger("robdog_control"), "NO ACTION!");
        }
    } catch (const Json::LogicError &e) {
        RCLCPP_ERROR(rclcpp::get_logger("robdog_control"), "Logic error: %s", e.what());
    } catch (const Json::RuntimeError &e) {
        RCLCPP_ERROR(rclcpp::get_logger("robdog_control"), "Runtime error: %s", e.what());
    }
}

// ****************** 处理不同的event字段 **************************

void RobdogCenter::handleEvent(const std::string &eventType, const Json::Value &inValue, const Json::Value &jBody,const std::string& topic_name) {
    static const std::unordered_map<std::string, std::function<void(const Json::Value&,const Json::Value&,const std::string& )>> eventHandlers = {
        {"robot_action", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleRobotAction(inValue); }},
        {"robot_move", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleRobotMove(inValue, body); }},
        {"robot_view", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleRobotView(inValue, body); }},
        {"mode_set", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleModeSet(inValue); }},
        {"robot_speed_level", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleSpeed_level(inValue); }},

        {"properties_write", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handlePropertiesWrite(inValue); }},
        {"properties_read", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handlePropertiesRead(inValue); }},
        {"hardware_state_query", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleHardwareStateRead(inValue); }},
        {"connect_info_request", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleConnectInfoRequest(inValue); }},

        {"phone_call", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handlePhoneCall(body); }},
        {"user_connect_change", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleUserConnectChange(body,name); }},
        {"map_draw", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleMapDraw(body); }},
        {"data_update", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleDataUpdate(inValue, body); }},
        {"move_points", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleMovePoints(inValue); }},
        {"point_report", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handlePointReport(body); }},
        {"remind_ontime", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleRemindOnTime(body); }},
        {"navigation_notify", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleNavigationNotify(body); }},
        {"unbind_notify", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleUnbindNotify(body); }},
        {"bind_notify", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handlebindNotify(body); }},        
        {"unbind_notify_voice", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleUnbindNotifyVoice(body); }},        
        // {"gest_Rec", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleGestRec(body); }},
        {"inter_mode_set", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleUserInteraction(body); }},
        {"stop_task", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleFinishTask(body); }},
        {"voice_response_nlu_raw", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleVoiceResponseNluRaw(body); }},
        {"items_read",[this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleFollowMeStatus(inValue); }},
        {"navigation_request", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleNavigationRequest(inValue); }},
        {"trip_simple_query", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleTripSimpleQuery(inValue); }},
        {"bind_status_response", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleBindStatusResponse(body); }},
        {"coord_report", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleCoordReportRequest(body); }},
        {"device_settings_read", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleDeviceSettingResponse(body); }},
        {"map_reposition", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleMapReposition(inValue); }},
        {"robot_skill_organize", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleRobotSkillOrganize(body); }},
        {"interaction_skills", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleInteractionSkills(inValue); }},
        {"group_skill_organize_res", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleInteractionSkills(inValue); }},
        {"develop_mode", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleDevelopMode(body); }},
        {"guard_ontime", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleGuardInstruction(body); }},
        {"charging_pile_mark", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleChargeMark(body); }},
        {"guard_done", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleGuardDone(body); }},
        {"map_reposition_manual",[this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleMapRepositionManual(inValue);}},
        {"charging_action",[this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleChargingAction(body);}},
        {"create_trace_action",[this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleTraceTrip(inValue);}},
        {"data_report_ctl",[this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleDataReportCtl(inValue);}},
        {"trace_trip_request",[this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleTracetripNaviRequest(inValue);}},
        {"rtk_account_read",[this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleRtkAccountRead(body);}},
        {"login_info_notify", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleUserkey(body); }},
        {"wifi_list_query",[this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleWifiListQuery(inValue, body);}},
        {"wifi_set",[this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleWifiSet(inValue, body);}},
        {"wifi_set_result_query",[this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleWifiSetResultQuery(inValue, body);}},
        {"unknow", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) {}}

    };

    auto it = eventHandlers.find(eventType);
    if (it != eventHandlers.end()) {
        it->second(inValue, jBody, topic_name); // 调用对应的处理函数
    } 
    // 有可能是keep_alive
    //else {
    //     RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "NO ACTION for event type: %d", eventType);
    // }
}

// ****************** event是robot_action **************************
void RobdogCenter::handleRobotAction(const Json::Value &inValue) {
    Json::Value jBody = inValue["body"];
    if (jBody["actionType"].isNull()) {
        handleGeneralAction(jBody);
    } else {
        handleSpecificAction(inValue);
    }
}

// ******************** 反馈运动状态 **************************
bool RobdogCenter::isActionOver(const std::string& actionName) { 
    int robot_basic_state = RobotInfoMgr::getInstance().getRobotBasicState();

    auto key = std::make_tuple(robot_basic_state);
    auto it = utStateQueryTableString.find(key);
    if (it != utStateQueryTableString.end()) 
    {
        HomiUtRobotStatus enLastStatus = RobotInfoMgr::getInstance().getUtRobotStatus();
        std::string strDetails = std::get<0>(it->second);
        std::cout << "Current Robot State: " << strDetails << std::endl;
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"),  "##### Current Robot State Value: %d #####", robot_basic_state);
    }
    if(robot_basic_state == UT_ROBDOG_STATUS_WALK_UPRIGHT) return true;
    else return false;
}

void RobdogCenter::handleRobotStatusGet(const Json::Value &inValue) { 
    // 回调给平台
    Json::Value response;
    response["deviceId"] = RobotState::getInstance().getDeviceId();
    response["domain"] = inValue["domain"];
    response["event"] = "group_skill_organize_res"; // 
    response["eventId"] = inValue["eventId"];
    response["seq"] = to_string(base::homiUtils::getCurrentTimeStamp());
    response["response"] = "false";
    
    Json::Value event_body;
    // 重新设一个全局变量
    event_body["status"] = true;
    // if(robot_basic_state == UT_ROBDOG_STATUS_WALK_UPRIGHT) event_body["status"] = true;
    // else event_body["status"] = false;
    response["body"] = event_body;
    // 将 Json::Value 转换成字符串
    Json::FastWriter writer;
    std::string jsonString = writer.write(response);

    sendRequestData(jsonString);
}

// ******************** 动作编排 **************************
void RobdogCenter::handleRobotSkillOrganize(const Json::Value &jBody) {
    std::vector<Json::Value> robotActions;
    // 
    // robotActions = jBody["signalCommands"][0]; 
    // std::string actionarguement = robotAction["body"]["actionArguement"].asString();
    // auto it = action_times.find(actionarguement);
    // if (it != action_times.end()) {
    //     int action_time = it->second;
    //     handleActionOrg(robotAction); // 先执行动作，再等待动作执行完成
    //     robActionSuspendTimer_ = node_->create_wall_timer(
    //         std::chrono::seconds(action_time),   
    //         [this, body_copy]() { handleActionOrg(robotAction); }
    //     );
    // }

    // for (const auto &robotAction : jBody["signalCommands"]) {
    //     std::string actionarguement = robotAction["body"]["actionArguement"].asString();
    //     auto it = action_times.find(actionarguement);
    //     if (it != action_times.end()) {
    //         int action_time = it->second;
    //         handleRobotAction(robotAction);  // 需要考虑动作的执行时间
    //         // std::cout << "开始执行动作: " << actionarguement << "，预计执行时间: " << action_time << " 秒。" << std::endl;
    //         RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "need %d seconds", action_time);
    //         // std::this_thread::sleep_for(std::chrono::seconds(action_time)); // 模拟任务执行时间
    //         // robActionSuspendTimer_ = node_->create_wall_timer(
    //         //     std::chrono::seconds(action_time),   
    //         //     std::bind(&RobdogCenter::ActionSuspend, this)
    //         // );
    //         std::cout << "动作: " << actionarguement << " 执行完成！" << std::endl;
    //     } else {
    //         std::cout << "找不到动作: " << actionarguement << std::endl;
    //     }
    // }

    std::thread th([this, jBody]() {
        for (const auto &robotAction : jBody["signalCommands"]) {
            std::string actionarguement = robotAction["body"]["actionArguement"].asString();
            auto it = action_times.find(actionarguement);
            if (it != action_times.end()) {
                int action_time = it->second;
                handleRobotAction(robotAction);  // 需要考虑动作的执行时间
                // std::cout << "开始执行动作: " << actionarguement << "，预计执行时间: " << action_time << " 秒。" << std::endl;
                RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "need %d seconds", action_time);
                
                std::this_thread::sleep_for(std::chrono::seconds(action_time)); // 模拟任务执行时间
                
                // robActionSuspendTimer_ = node_->create_wall_timer(
                //     std::chrono::seconds(action_time),   
                //     std::bind(&RobdogCenter::ActionSuspend, this)
                // );
                // robActionSuspendTimer_ = node_->create_wall_timer(
                // std::chrono::seconds(action_time),
                //     [this]() {
                //         RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "action finish!!!!!!");
                //     }
                // );
                std::cout << "动作: " << actionarguement << " 执行完成！" << std::endl;
            } else {
                std::cout << "找不到动作: " << actionarguement << std::endl;
            }
        }
     });
     th.detach();  // 分离线程使其在后台执行
}

// 根据颜色和样式生成命令
unsigned int RobdogCenter::generateLightCommand(const std::string& light_color, const std::string& light_style) {
    // 查找颜色和样式在map中的对应值
    auto colorIt = colorMap.find(light_color);
    auto styleIt = styleMap.find(light_style);

    // 如果找不到对应的颜色或样式，返回无效命令
    if (colorIt == colorMap.end() || styleIt == styleMap.end()) {
        std::cerr << "Invalid color or style" << std::endl;
        return 0;
    }

    // 生成命令：颜色和样式代码结合
    return (colorIt->second << 8) | styleIt->second;
}

void RobdogCenter::handleInteractionSkills(const Json::Value &inValue){
    std::thread th([this, inValue]() {
        Json::Value jBody = inValue["body"];
        for (const auto &command : jBody["commandSetSeq"]) { // 可能有很多套动作、表情和灯光
            // ------------- 表情处理 ------------- 
            if (!command["expression"].isNull()) {
                Json::Value expression = command["expression"];
                std::string exp_type = expression["type"].asString();
                int exp_duration = expression["duration"].asInt();
                static const std::unordered_map<std::string, std::string> InteractionVideos = {
                    {"sad", "video/emotion/sad"},
                    {"happy", "video/emotion/happy"},
                    {"angry", "video/emotion/angry"},
                    {"criticism", "video/emotion/criticism"},
                    {"neutrality", "video/emotion/neutrality"},
                    {"praise", "video/emotion/praise"},
                    {"neutral", "video/emotion/neutrality"},
                    
                    {"surprised", "video/emotion/praise"},
                    {"weather", "video/emotion/weather"},   // 天气标签
                    {"festival", "video/emotion/festival"}   // 节日标签
                };
                auto it = InteractionVideos.find(exp_type);
                if (it != InteractionVideos.end()) {
                    ExpressionChange::getInstance().async_callback_work(node_->getResourcePath(it->second),1); // 暂时只播放一次
                }
            }

            // ------------- 灯光处理 ------------- 
            // 【暂不处理】
            if (!command["light"].isNull()) {
                Json::Value light = command["light"];
                std::string light_color = light["color"].asString(); // white、orange、pink、yellow、blue
                std::string light_style = light["style"].asString(); // 常亮 — keepAwake;闪烁 — blinking;呼吸灯 — breathing;流水灯 — running
                
                std::string light_brightness = light["brightness"].asString();
                std::string light_frequency = light["frequency"].asString(); // 目前默认为0
                std::string light_speed = light["speed"].asString(); // 目前默认为0
                int light_duration = light["duration"].asInt(); // 目前默认为0

                unsigned int cmd = generateLightCommand(light_color, light_style);
                // std::cout << "Generated Command: 0x" << std::hex << cmd << std::dec << std::endl;

                // 根据命令值来匹配对应的宏
                switch (cmd) {
                    case DEEP_CMD_LIGHT_01: std::cout << "Command: DEEP_CMD_LIGHT_01 (橙色闪烁)" << std::endl; break;
                    case DEEP_CMD_LIGHT_02: std::cout << "Command: DEEP_CMD_LIGHT_02 (粉色闪烁)" << std::endl; break;
                    case DEEP_CMD_LIGHT_03: std::cout << "Command: DEEP_CMD_LIGHT_03 (白色呼吸)" << std::endl; break;
                    case DEEP_CMD_LIGHT_04: std::cout << "Command: DEEP_CMD_LIGHT_04 (白色常亮)" << std::endl; break;
                    case DEEP_CMD_LIGHT_05: std::cout << "Command: DEEP_CMD_LIGHT_05 (橙色呼吸)" << std::endl; break;
                    case DEEP_CMD_LIGHT_06: std::cout << "Command: DEEP_CMD_LIGHT_06 (橙色流水)" << std::endl; break;
                    default: std::cout << "Unknown command!" << std::endl; break;
                }
                if(cmd != 0) node_->handleLightControl(cmd, 8);
            }

            // ------------- 语音处理 ------------- 
            if (!command["tts"].isNull()) {
                Json::Value tts = command["tts"];
                std::string tts_type = tts["type"].asString(); // text和audio
                std::string tts_value = tts["value"].asString();
                if(tts_type == "text"){
                    sendStringToBrocast(tts_value);
                }
                else if(tts_type == "audio"){
                    std::thread([this, tts_value]() {
                        node_->playAudio(node_->getResourcePath("audio/" + tts_value));
                    }).detach();
                }
            }

            // ------------- 动作处理 -------------
            if (!command["action"].isNull()) {
                Json::Value action = command["action"];
                std::string actionArgument;
                // 提取动作参数
                if (!action["actionArguement"].isNull()) {
                    actionArgument = action["actionArguement"].asString();
                } else if (!action["actionArgument"].isNull()) {
                    actionArgument = action["actionArgument"].asString();
                }
                // 查找并发布动作
                auto it_action = actionMap.find(actionArgument);
                if (it_action != actionMap.end()) {
                    node_->handleInteractionAction(actionArgument);
                    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "执行 %s 动作", actionArgument);
                } else {
                    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "找不到动作: %s", actionArgument);
                }

                // 创建 promise/future 对象用于等待动作完成
                std::promise<bool> actionDonePromise;
                std::future<bool> actionDoneFuture = actionDonePromise.get_future();

                // 启动定时器检查动作状态
                robActionSuspendTimer_ = node_->create_wall_timer(
                    std::chrono::milliseconds(500), // 保证动作已经开始执行
                    [this, &actionDonePromise, actionArgument]{
                        if (isActionOver(actionArgument)) {
                            RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "动作 %s 执行完成！", actionArgument.c_str());
                            robActionSuspendTimer_->cancel();  // 停止定时器
                            actionDonePromise.set_value(true);  // 触发 future 完成
                        }
                });
                // 等待动作完成
                actionDoneFuture.wait();
            }
            
            // 每一套交互之间的间隔
            int interaction_time = command["nextDelay"].asInt();
            std::this_thread::sleep_for(std::chrono::seconds(interaction_time)); // 模拟任务执行时间
        }
        // 退出循环说明所有动作已经完成
        handleRobotStatusGet(inValue);
     });
     th.detach();  // 分离线程使其在后台执行
}

void RobdogCenter::handleDevelopMode(const Json::Value &jBody) {
    if (!jBody.isMember("isUse")) {
	RCLCPP_ERROR(rclcpp::get_logger("robdog_control"), "develop_mode: Invalid format");
        return;
    }
    bool isUse = jBody["isUse"].asBool();
    auto message = std_msgs::msg::String();
    message.data = isUse ? "1" : "0";
    developer_mode_pub_->publish(message);
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "pub develop_mode: %s", message.data.c_str());

    return;
}

void RobdogCenter::handleGuardInstruction(const Json::Value &jBody) {

    //RCLCPP_ERROR(rclcpp::get_logger("robdog_control"), "task signalling detected:%s",data_string.c_str());
    homi_speech_interface::msg::LiveStreamTask msg;
    msg.task = "task";
    msg.start_second = jBody["time"]["secondOfDay"].asInt();
    msg.end_second = jBody["time"]["endSecondOfDay"].asInt();
    liveStreamTask_pub->publish(msg); 

    return;
}

void RobdogCenter::GuardDone() {
    // aplay: 主人，我又完成了一次守卫任务哦，你可以在移动爱家APP里查看我录制的任务录像呢主人，我又完成了一次守卫任务哦，你可以在移动爱家APP里查看我录制的任务录像呢
    node_->playAudio(node_->getResourcePath("audio/GuardDone.wav"));
    int max_wait = 50;
    int stateChange = 0;

    // standUp
    if(RobotInfoMgr::getInstance().getRobotStatus() == ROBDOG_STATUS_GETDOWN) {
        homi_speech_interface::msg::RobdogAction sumsg;
        sumsg.actiontype = "motorSkill";
        sumsg.actionargument = "standUp";
        publishAction(std::make_shared<homi_speech_interface::msg::RobdogAction>(sumsg));
    }
    //wait for standup
    while(max_wait-->=0) {
        if(RobotInfoMgr::getInstance().getRobotStatus() == ROBDOG_STATUS_FORCE_CTRL_LEVEL_LOWSPPEED_GAIT) {
            break;
        }
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
    if(RobotInfoMgr::getInstance().getRobotStatus() != ROBDOG_STATUS_FORCE_CTRL_LEVEL_LOWSPPEED_GAIT) {
        RCLCPP_WARN(rclcpp::get_logger("robdog_control"), "GuardDone RobDog still not standing: %d:%d", RobotInfoMgr::getInstance().getRobotStatus(),ROBDOG_STATUS_FORCE_CTRL_LEVEL_LOWSPPEED_GAIT);
        node_->playAudio(node_->getResourcePath("audio/GuardDoneRequestForInteraction.wav"));
        return;
    }

    // move 8 steps; "body":{"direction":{"x":1},"actionType":2}
    Json::Value body;
    body["direction"]["x"] = 1;
    body["actionType"] = 2;
    fspeed_x = 0.3;
    RobotMoveProc(body);

    max_wait = 100;
    while(max_wait-->=0) {
        if(RobotInfoMgr::getInstance().getRobotStatus() == ROBDOG_STATUS_BODYROTWISTING_BY_AXISCOMMAND) {
            stateChange = 1;
        } else {
            if(stateChange == 1) {
                //move done
                RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "GuardDone move done: %d", RobotInfoMgr::getInstance().getRobotStatus());
                break;
            }
        }
        std::this_thread::sleep_for(std::chrono::milliseconds(50));
    }
    fspeed_x = DF_FSPEED_X;
    if(RobotInfoMgr::getInstance().getRobotStatus() != ROBDOG_STATUS_FORCE_CTRL_LEVEL_LOWSPPEED_GAIT || stateChange != 1) {
        RCLCPP_WARN(rclcpp::get_logger("robdog_control"), "robdog may not move, do not stretch, %d", RobotInfoMgr::getInstance().getRobotStatus());
        node_->playAudio(node_->getResourcePath("audio/GuardDoneRequestForInteraction.wav"));
        return;
    }

    // action 伸懒腰
    homi_speech_interface::msg::RobdogAction sumsg;
    sumsg.actiontype = "motorSkill";
    sumsg.actionargument = "stretch";
    publishAction(std::make_shared<homi_speech_interface::msg::RobdogAction>(sumsg));
    std::this_thread::sleep_for(std::chrono::milliseconds(8000));

    // aplay 我现在很精神呢，可以跟我互动哦
    node_->playAudio(node_->getResourcePath("audio/GuardDoneRequestForInteraction.wav"));
}

void RobdogCenter::handleGuardDone(const Json::Value &jBody) {
    std::thread t(&RobdogCenter::GuardDone, this);
    t.detach();
}

// -----------间隔固定的时延开启算法 -----------------
// void RobdogCenter::handleActionOrg(){
//     handleRobotAction(robotAction);
//     // 停止定时器
//     if (robActionSuspendTimer_) {
//         robActionSuspendTimer_->cancel();
//     }
// }

// ****************** event是robot_action但是没有actiontype **************************
void RobdogCenter::handleGeneralAction(const Json::Value &jBody) {
    // 处理导航任务和其他通用动作
    expresstion_count = 0;
    // RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "8959379483739289398");
    //导航任务需要切换到自主模式
    homi_speech_interface::msg::RobdogAction zzmsg;
    zzmsg.actiontype = "NavCtrl";
    zzmsg.actionargument = "AutoMode";
    // publishAction(zzmsg);
    publishAction(std::make_shared<homi_speech_interface::msg::RobdogAction>(zzmsg));

    // system("/home/<USER>/updateexpression.sh  "
    //         "/home/<USER>/resource/left_right_look.mp4");
    std::string taskType;
    if (!jBody["event"].isNull()) {
        taskType = jBody["event"].asString();
        RobotState::getInstance().setMoveTaskType(taskType);
    } else {
        RobotState::getInstance().setMoveTaskType("");
    }
    // ******************* 处理不同的事件类型 ****************
    if (taskType == "takePhotos") {
        // 处理拍照任务
        handleTakePhotos();
    } else if (taskType == "deliverExpress") {
        // 取快递
        handleDeliverExpress();
    } else if (taskType == "fetchExpress") {
        // 寄快递
        handleFetchExpress();
    } else if (taskType == "cancelMovement") {
        // 处理取消移动任务
        handleCancelMovement();
    } else {
        // 处理其他任务
        homi_speech_interface::msg::SIGCEvent move_msg;
        Json::StreamWriterBuilder writerBuilder;
        std::string moveMsgs = Json::writeString(writerBuilder, jBody);
        move_msg.event = moveMsgs;
        // moveToTarget(move_msg);
        moveToTarget(std::make_shared<homi_speech_interface::msg::SIGCEvent>(move_msg));

    }
}

// ****************** event是robot_action但是有actiontype **************************
void RobdogCenter::handleSpecificAction(const Json::Value &inValue) {
    Json::Value jBody = inValue["body"];
    std::string actionType = jBody["actionType"].asString();
    if (actionType == "followMe") {    
        handleFollowMe(jBody);
    } else if (actionType == "gestRec") {
        // 手势识别开启
        handleGestRec(jBody);
    }  else if (actionType == "sportMode") {
        // 运动模式设置
        handleSportMode(jBody);
    } else if (actionType == "motorSkill") {
        // 运动技能设置
        handleMotorSkill(jBody);
    } else if (actionType == "emergencyStop") {
        // 急停
        homi_speech_interface::msg::RobdogAction msg;
        msg.actiontype = "emergencyStop";
        msg.actionargument = "emergencyStop";
        // publishAction(msg);
        publishAction(std::make_shared<homi_speech_interface::msg::RobdogAction>(msg));

    } else if (actionType == "resetZero") {
        // 关节回零
        homi_speech_interface::msg::RobdogAction msg;
        msg.actiontype = "resetZero";
        msg.actionargument = "resetZero";
        // publishAction(msg);
        publishAction(std::make_shared<homi_speech_interface::msg::RobdogAction>(msg));

    } else if (actionType == "gaitControl") {
        // 和强化学习有关的
        handleRLSkill(jBody);
    } else if (actionType == "stopCompanyAsk") {
        handleAcccompany(jBody);
    } else if (actionType == "tripStart") {
        homi_speech_interface::msg::LiveStreamTask msg;
        msg.task = "start";
        msg.start_second = 0;
        msg.end_second = 0;
        liveStreamTask_pub->publish(msg); 
        
        handleTripStart(inValue);
    } else if (actionType == "tripPause") {
        handleTripPause();
    }else if(actionType == "selfIntroduce"){
        startSequence();
    } else if (actionType == "tripCancel") {
        homi_speech_interface::msg::LiveStreamTask msg;
        msg.task = "stop";
        msg.start_second = 0;
        msg.end_second = 0;
        liveStreamTask_pub->publish(msg); 
        
        
        handleTripCancel();
    } else if (actionType == "phoneCallAction") {
        if (jBody["actionArgument"].asString() == "on") {
            auto callReq = std::make_shared<homi_speech_interface::srv::PhoneCall_Request>();
            callReq->type = "pickup";
            callReq->phone_number = "";
            phone_call_client_->async_send_request(callReq);
        } else if (jBody["actionArgument"].asString() == "off") {
            auto callReq = std::make_shared<homi_speech_interface::srv::PhoneCall_Request>();
            callReq->type = "hangup";
            callReq->phone_number = "";
            phone_call_client_->async_send_request(callReq);
        } else {
            RCLCPP_WARN(rclcpp::get_logger("robdog_control"), "phoneCallAction: Invalid actionArgument: %s", jBody["actionArgument"].asString().c_str());
        }
    } else {
        RCLCPP_WARN(rclcpp::get_logger("robdog_control"), "Unhandled action type: %s", actionType.c_str());
    }
}

// ****************** event是robot_move或者robot_view **************************
void RobdogCenter::RobotMoveProc(const Json::Value &jBody) {

    // 处理机器人动作的逻辑
    // 初始化 current_twist_msg_ 和 current_continue_msg_ 为全0
    // current_twist_msg_ = {0}; // 使用聚合初始化
    // current_continue_msg_ = {"strType", 0, 0, 0, 0, 0, 0}; // 使用聚合初始化
    // geometry_msgs::msg::Twist current_twist_msg_; // 默认构造
    current_twist_msg_.linear.x = 0.0; // 根据需要初始化每个字段
    current_twist_msg_.linear.y = 0.0;
    current_twist_msg_.linear.z = 0.0;
    current_twist_msg_.angular.x = 0.0;
    current_twist_msg_.angular.y = 0.0;
    current_twist_msg_.angular.z = 0.0;

    // 对于其他消息类型，使用类似的方法
    current_continue_msg_.event = "robot_move";
    current_continue_msg_.x = 0;
    current_continue_msg_.y = 0;
    current_continue_msg_.z = 0;
    current_continue_msg_.yaw = 0;
    current_continue_msg_.pitch = 0;
    current_continue_msg_.roll = 0;

    if (!jBody["actionType"].isNull()) {
        int actionType = jBody["actionType"].asInt();
        
        // 更新移动状态
        // if (actionType == 1 && !move_status_flag) {
        //     move_status_flag = true;
        //     system("/home/<USER>/updateexpression2.sh /home/<USER>/resource/vedio/look_right_left_step1.mp4 2 /home/<USER>/resource/vedio/look_right_left_step2.mp4");
        // } else if (actionType == 0 && move_status_flag) {
        //     move_status_flag = false;
        //     system("/home/<USER>/updateexpression.sh /home/<USER>/resource/vedio/default.mp4");
        // }

        if (actionType == 2) {
            // 每次执行一个动作之前先静止一会【只有步进模式下才需要】
            timer_robMove->cancel();
            velCmd_pub->publish(current_twist_msg_);
        }

        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "fspeed_x: %f, fspeed_y: %f, fspeed_z: %f", fspeed_x, fspeed_y, fspeed_z);

        int count = 0; // 要走的步数

        // 处理方向信息
        if(!jBody["direction"].isNull()){
            Json::Value direction = jBody["direction"];
            for (const auto& axis : {"x", "y", "z", "yaw", "pitch", "roll"}) {
                if (!direction[axis].isNull() && direction[axis].asInt() != 0) {
                    int step = direction[axis].asInt();
                    if (std::string(axis)== "x") {
                        count = std::abs(step);
                        current_continue_msg_.x = step;
                        current_twist_msg_.linear.x = (step > 0) ? fspeed_x : -fspeed_x;
                        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Continue Move, CMD FROM PLATFORM OR APP. STEP_X: %d, fspeed_x: %f", step, fspeed_x);
                    } else if (std::string(axis) == "y") {
                        count = std::abs(step);
                        current_continue_msg_.y = step;
                        current_twist_msg_.linear.y = (step > 0) ? fspeed_y : -fspeed_y;
                        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Continue Move, CMD FROM PLATFORM OR APP. STEP_Y: %d, fspeed_y: %f", step, fspeed_y);
                    } else if (std::string(axis) == "z") {
                        current_continue_msg_.z = step;
                    } else if (std::string(axis) == "yaw") {
                        count = std::ceil(static_cast<double>(step) / 15);
                        current_continue_msg_.yaw = step;
                        current_twist_msg_.angular.z = (step > 0) ? fspeed_z : -fspeed_z; // 转一次的角速度
                    } else if (std::string(axis) == "pitch") {
                        current_continue_msg_.pitch = step;
                    } else if (std::string(axis) == "roll") {
                        current_continue_msg_.roll = step;
                    }
                }
            }
        }
        Proceationtype(actionType, count);
    }
}

void RobdogCenter::RobotViewProc(const Json::Value &jBody) {
    // 处理机器人动作的逻辑
    // 初始化 current_twist_msg_ 和 current_continue_msg_ 为全0
    // current_twist_msg_ = {0}; // 使用聚合初始化
    // current_continue_msg_ = {"strType", 0, 0, 0, 0, 0, 0}; // 使用聚合初始化
    // geometry_msgs::msg::Twist current_twist_msg_; // 默认构造
    current_twist_msg_.linear.x = 0.0; // 根据需要初始化每个字段
    current_twist_msg_.linear.y = 0.0;
    current_twist_msg_.linear.z = 0.0;
    current_twist_msg_.angular.x = 0.0;
    current_twist_msg_.angular.y = 0.0;
    current_twist_msg_.angular.z = 0.0;

    // 对于其他消息类型，使用类似的方法
    current_continue_msg_.event = "robot_view";
    current_continue_msg_.x = 0;
    current_continue_msg_.y = 0;
    current_continue_msg_.z = 0;
    current_continue_msg_.yaw = 0;
    current_continue_msg_.pitch = 0;
    current_continue_msg_.roll = 0;

    if (!jBody["actionType"].isNull()) {
        int actionType = jBody["actionType"].asInt();
        
        // 更新移动状态
        // if (actionType == 1 && !move_status_flag) {
        //     move_status_flag = true;
        //     system("/home/<USER>/updateexpression2.sh /home/<USER>/resource/vedio/look_right_left_step1.mp4 2 /home/<USER>/resource/vedio/look_right_left_step2.mp4");
        // } else if (actionType == 0 && move_status_flag) {
        //     move_status_flag = false;
        //     system("/home/<USER>/updateexpression.sh /home/<USER>/resource/vedio/default.mp4");
        // }

        if (actionType == 2) {
            // 每次执行一个动作之前先静止一会【只有步进模式下才需要】
            timer_robMove->cancel();
            velCmd_pub->publish(current_twist_msg_);
        }

        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "fspeed_x: %f, fspeed_y: %f, fspeed_z: %f", fspeed_x, fspeed_y, fspeed_z);

        int count = 0; // 要走的步数

        // 处理方向信息
        if(!jBody["direction"].isNull()){
            Json::Value direction = jBody["direction"];
            for (const auto& axis : {"x", "y", "z", "yaw", "pitch", "roll"}) {
                if (!direction[axis].isNull() && direction[axis].asInt() != 0) {
                    int step = direction[axis].asInt();
                    if (std::string(axis)== "x") {
                        count = std::abs(step);
                        current_continue_msg_.x = step;
                        current_twist_msg_.linear.x = (step > 0) ? fspeed_x : -fspeed_x;
                        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Continue Move, CMD FROM PLATFORM OR APP. STEP_X: %d, fspeed_x: %f", step, fspeed_x);
                    } else if (std::string(axis) == "y") {
                        count = std::abs(step);
                        current_continue_msg_.y = step;
                        current_twist_msg_.linear.y = (step > 0) ? fspeed_y : -fspeed_y;
                        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Continue Move, CMD FROM PLATFORM OR APP. STEP_Y: %d, fspeed_y: %f", step, fspeed_y);
                    } else if (std::string(axis) == "z") {
                        current_continue_msg_.z = step;
                    } else if (std::string(axis) == "yaw") {
                        count = std::ceil(static_cast<double>(step) / 15);
                        current_continue_msg_.yaw = step;
                        current_twist_msg_.angular.z = (step > 0) ? fspeed_z : -fspeed_z; // 转一次的角速度
                    } else if (std::string(axis) == "pitch") {
                        current_continue_msg_.pitch = step;
                    } else if (std::string(axis) == "roll") {
                        current_continue_msg_.roll = step;
                    }
                }
            }
        }
        Proceationtype(actionType, count);
    }
}

bool RobdogCenter::timeStampCheck(const Json::Value &inValue) 
{
    // 超时过滤，待时间同步功能完成后放开
    return true;

    // 获取event时间戳
    std::string event_time = inValue["seq"].asString();
    long long event_ts = std::stoll(event_time);

    // 获取当前时间
    if (event_time.length() == 13)
    {
        auto now = std::chrono::system_clock::now();
        auto timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()).count();

        // 当前时间戳与event时间戳差距超过2s，不执行
        if (std::abs(timestamp - event_ts) > 2000)
        {
            RCLCPP_ERROR(rclcpp::get_logger("robdog_control"), "Time not correct: %lld, %lld.", timestamp, event_ts);
            return false;
        }
    }
    else if (event_time.length() == 10)
    {
        auto now = std::chrono::system_clock::now();
        auto timestamp = std::chrono::duration_cast<std::chrono::seconds>(now.time_since_epoch()).count();

        // 当前时间戳与event时间戳差距超过2s，不执行
        if (std::abs(timestamp - event_ts) > 2)
        {
            RCLCPP_ERROR(rclcpp::get_logger("robdog_control"), "Time not correct: %lld, %lld.", timestamp, event_ts);
            return false;
        }
    }

    RCLCPP_ERROR(rclcpp::get_logger("robdog_control"), "Time correct: %lld.", event_ts);

    return true;
}

void RobdogCenter::handleRobotMove(const Json::Value &inValue, const Json::Value &jBody) 
{
    // 检查时间戳
    if (true == timeStampCheck(inValue))
    {
        RobotMoveProc(jBody);
    }
}

void RobdogCenter::handleRobotView(const Json::Value &inValue, const Json::Value &jBody) 
{
    // 检查时间戳
    if (true == timeStampCheck(inValue))
    {
        RobotViewProc(jBody);
    }
}

// ****************** event是robot_speed_level **************************
void RobdogCenter::handleSpeed_level(const Json::Value &inValue)
{
    Json::Value SpeedLevel = inValue["body"]["speedLevel"];
	int Speed = SpeedLevel.asInt();
	RobotInfoMgr::getInstance().setSpeedLevel(Speed);
	homi_speech_interface::msg::RobdogAction speedmsg;
	
    #if defined(UNITREE)
    #else
    speedmsg.actiontype = "sportMode";
	if (0 == Speed)
	{
        speedmsg.actionargument = "walk";
	}
	else if (1 == Speed)
	{
		speedmsg.actionargument = "medium_speed";
	}
	else if (2 == Speed)
	{
        speedmsg.actionargument = "run";
	}
    publishAction(std::make_shared<homi_speech_interface::msg::RobdogAction>(speedmsg));
    #endif

}

// ****************** event是mode_set **************************
void RobdogCenter::handleModeSet(const Json::Value &inValue) {
    Json::Value deviceMode = inValue["body"]["deviceMode"];
    // 机器狗模式：0-宅家模式 1-遛狗模式 2-外出模式 3-遥控模式
    //  【目前只保留宅家模式和外出模式】
    int modeAfter = deviceMode.asInt();
    int modeBefore = RobotState::getInstance().getRobdogStatus();
    RobotState::getInstance().setRobdogStatus(modeAfter);
    setDeviceMode(modeAfter);
	homi_speech_interface::msg::RobdogAction speedmsg;
    Json::Value body;
    switch (modeAfter) {
        case 0:
            // 默认切换到此步态
            RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Set mode to Home mode");
		
			speedmsg.actiontype = "sportMode";
            #if defined(UNITREE)
            speedmsg.actionargument = "AIClassic";
            #else
            speedmsg.actionargument = "walk";
            #endif

			publishAction(std::make_shared<homi_speech_interface::msg::RobdogAction>(speedmsg));
			RobotInfoMgr::getInstance().setSpeedLevel(0);
            break;
        case 1:
            RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Set mode to Walking mode");
            break;
        case 2:
            // 切换到AI步态
            // handleTripStart(body); // 切换到自主出行模式
            RCLCPP_WARN(rclcpp::get_logger("robdog_control"), "Outdoor mode not supported in this version");

			speedmsg.actiontype = "sportMode";

            #if defined(UNITREE)
            speedmsg.actionargument = "AIClassic";
            #else
            speedmsg.actionargument = "walk";
            #endif

			publishAction(std::make_shared<homi_speech_interface::msg::RobdogAction>(speedmsg));
			RobotInfoMgr::getInstance().setSpeedLevel(0);
            break;
        case 3:
            RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Set mode to Remote control mode");
            break;
        default:
            RCLCPP_ERROR(rclcpp::get_logger("robdog_control"), "Undefined mode: %d", modeAfter);
            break;
    }
    // 回调给平台
    Json::Value response;
    response["deviceId"] = RobotState::getInstance().getDeviceId();
    response["domain"] = inValue["domain"];
    response["event"] = "mode_set";
    response["eventId"] = inValue["eventId"];
    response["seq"] = to_string(base::homiUtils::getCurrentTimeStamp());
    response["response"] = "false";
    
    Json::Value event_body;
    event_body["deviceId"] = RobotState::getInstance().getDeviceId();
    event_body["modeAfter"] = modeAfter;
    event_body["modeBefore"] = modeBefore;
    if(modeAfter == modeBefore)
        event_body["status"] = false;
    else 
        event_body["status"] = true; // 切换失败返回false
    response["body"] = event_body;
    // 将 Json::Value 转换成字符串
    Json::FastWriter writer;
    std::string jsonString = writer.write(response);

    sendRequestData(jsonString);
}

// 给平台的回复
void RobdogCenter::sendRequestData(const std::string &data) {
    auto request_sigc_data = std::make_shared<homi_speech_interface::srv::SIGCData::Request>();
    request_sigc_data->data = data;  // 设置请求数据
    // RCLCPP_INFO_STREAM(rclcpp::get_logger("robdog_control"), "Send res to Server, res is " << request_sigc_data->data);
    auto ret = platform_client->wait_for_service(std::chrono::seconds(1));
    if (!ret) {
        RCLCPP_WARN(rclcpp::get_logger("robdog_control"), "Failed to waitForExistence service assistant");
    }
    auto result = platform_client->async_send_request(request_sigc_data, std::bind(&RobdogCenter::plat_srv_callback, this, std::placeholders::_1));
}

// ****************** event是properties_write **************************
// 处理属性写入
void RobdogCenter::handlePropertiesWrite(const Json::Value &inValue) {
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "platform set properties");
    setProperties(inValue);
}

// ****************** event是properties_read **************************
// 处理属性读取
void RobdogCenter::handlePropertiesRead(const Json::Value &inValue) {
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "platform get properties");
    std::thread([this, inValue]() {
        std::string properties_data = get_robot_properties(inValue);  // 获取属性数据
        sendRequestData(properties_data);  // 发送数据到平台
    }).detach();  // 分离线程
}


void RobdogCenter::handleHardwareStateRead(const Json::Value &inValue) {
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "platform get hardwareState");
    std::thread([this, inValue]() {
        std::string hardware_state_data = get_hardware_state(inValue);  // 获取属性数据
        sendRequestData(hardware_state_data);  // 发送数据到平台
    }).detach();  // 分离线程
}

// ****************** event是connect_info_request **************************
void RobdogCenter::handleConnectInfoRequest(const Json::Value &inValue) {
    // 处理连接信息请求的逻辑
    RCLCPP_INFO_STREAM(rclcpp::get_logger("robdog_control"), "CMD: connect_info_request");
    std::string connect_info_data = get_connect_info_request(inValue);  
    sendRequestData(connect_info_data);     
}

void RobdogCenter::handlePhoneCall(const Json::Value &jBody) {
    if (jBody["phoneNumber"].isNull()) {
        RCLCPP_WARN(rclcpp::get_logger("robdog_control"), "handlePhoneCall: phoneNumber is null");
    } else {
        auto callReq = std::make_shared<homi_speech_interface::srv::PhoneCall_Request>();
        callReq->type = "callout";
        callReq->phone_number = jBody["phoneNumber"].asString();
        phone_call_client_->async_send_request(callReq);
    }    
}

// ****************** event是user_connect_change **************************
void RobdogCenter::handleUserConnectChange(const Json::Value &jBody,const std::string& topic_name) {
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "user_connect_change");
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "set operateTs %ld", jBody["operateTs"].asInt64());
    if (topic_name=="/homi_speech/sigc_event_topic")
        RobotState::getInstance().setTimeStamp(jBody["operateTs"].asInt64());
    if (jBody["changeType"].asInt() == 1) // 1代表建联
    {
        RobotState::getInstance().setUserConnectStatus(1); // 1代表有用户连接
        RobotState::getInstance().setUserPhoneNumber(jBody["phone"].asString());
    } else if (jBody["changeType"].asInt() == 2) // 2代表取消建联
    {
        RobotState::getInstance().setUserConnectStatus(0); // 0代表无用户连接
        RobotState::getInstance().setUserPhoneNumber("13933333333");
    } else{
        RCLCPP_ERROR(rclcpp::get_logger("robdog_control"), "Undefined changeType: %d", jBody["changeType"].asInt());
    }
    RobotState::getInstance().saveConfig();
}

void RobdogCenter::sendNavigationAction(int action,std::string mapId) {
    Json::Value reqValue;
    reqValue["client_type"] = CLIENT_LAUNCHER;
    reqValue["target_client"] = CLIENT_NVIDIA;
    reqValue["action"] = "navigation_control";

    Json::Value params;
    params["action"] = action;
    params["mapId"] = mapId;
    // std::string current_mapid = RobotState::getInstance().getMapId();
    // RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "%d---current_mapid: %s", __LINE__, current_mapid.c_str());
    // RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "%d---send mapid: %s", __LINE__, mapId.c_str());
    reqValue["params"] = params;

    // 添加 action 的值到日志中
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "The message of val_env: %s, action: %d", reqValue.toStyledString().c_str(), action);
    SendtoNvOrin(reqValue.toStyledString().c_str(), nConnectIndex_);
}

void RobdogCenter::sendCommandToUSLAM(const std::string& command) {
    std_msgs::msg::String msg;
    msg.data = command;
    client_command_publisher_->publish(msg);
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Published command: %s", msg.data.c_str());
}

void RobdogCenter::sendMapAction(int action,std::string url,std::string mapId) {
    std::ostringstream oss;
    oss << "{"
        << "\"mapId\":\"" << mapId << "\","
        << "\"action\":" << action << ","
        << "\"url\":\"" << url << "\""
        << "}";

    std_msgs::msg::String msg;
    msg.data = oss.str(); 
    mappingControl_pub->publish(msg);
    
    if(action == 3) {
        string map_points_file = node_->get_parameter("map_points_path").as_string()+"/"+mapId+".json";
        if (access(map_points_file.c_str(), F_OK) == 0) {
            unlink(map_points_file.c_str());
        }
    }

    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "sendMapAction: %s", msg.data.c_str());
}

// void RobdogCenter::sendLoaclizationAction(const std::string& action) {
//     sendCommandToUSLAM("localization/" + action);
// }

// void RobdogCenter::sendNavigationAction(const std::string& action) {
//     sendCommandToUSLAM("navigation/" + action);
// }

// void RobdogCenter::sendPatrolAction(const std::string& action) {
//     sendCommandToUSLAM("patrol/" + action);
// }

void RobdogCenter::sendChargeAction(int action,std::string mapId,const Json::Value& jBody) {
    Json::Value reqValue;
    reqValue["client_type"] = CLIENT_LAUNCHER;
    reqValue["target_client"] = CLIENT_NVIDIA;
    reqValue["action"] = "charge_control";

    Json::Value modifiedJBody;
    modifiedJBody["action"] = action;
    modifiedJBody["mapId"] = mapId;
    modifiedJBody["point"] = jBody;
    reqValue["params"] = modifiedJBody;
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"),"The message of value_charge: %s,action: %d", reqValue.toStyledString().c_str(),action);
    WS_Send(reqValue.toStyledString().c_str(), nConnectIndex_);
}

void RobdogCenter::handleChargeMark(const Json::Value &jBody) {
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "%d -----handleChargeMark", __LINE__);
    std::string current_mapid =  jBody["mapId"].asString();
    std::string action = jBody["action"].asString();
    int actionInt;

    if (action == "start") {
        // actionInt = 0;
        isChargeMarking = true;
        action = "autocharge/start";

    } else if (action == "cancel") {
        // actionInt = 2;
        isChargeMarking = false;
        action = "autocharge/stop";
    } else {
        RCLCPP_WARN(rclcpp::get_logger("robdog_control"), "Undefined chargemark action: %s", action.c_str());
        return ; 
    }

    // sendChargeAction(actionInt,current_mapid,{});
    sendCommandToUSLAM(action);
}

void RobdogCenter::handleChargingAction(const Json::Value &jBody) { 
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "%d -----handleChargingAction", __LINE__);
    std::string current_mapid =  jBody["mapId"].asString();
    std::string action = jBody["action"].asString();
    int actionInt;

    if (action == "start") {
        is_charging = true;
        handleBatteryChargingPoint();
    } else if (action == "cancel") {
        actionInt = 4;
        is_charging = false;
        action = "autocharge/stop";
        sendCommandToUSLAM(action);
        // sendChargeAction(actionInt,current_mapid,{});
    } else {
        RCLCPP_WARN(rclcpp::get_logger("robdog_control"), "Undefined charge action: %s", action.c_str());
        return ; 
    }
}

// ****************** event是map_draw **************************
void RobdogCenter::handleMapDraw(const Json::Value &jBody) {
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "%d -----map_draw", __LINE__);

    long long mapId = jBody["mapId"].asInt64();
    std::string current_mapid = std::to_string(mapId);
    std::string url = jBody["url"].asString();
    std::string action = jBody.get("action", "").asString();
    int actionInt;
    if(mapId == -1){
        RobotState::getInstance().setMapId(current_mapid); //清空地图，逻辑待实现
        update_map_points_path();
    }


    if (action == "start") {
        RobotState::getInstance().setMapId(current_mapid);
        update_map_points_path();
        RobotState::getInstance().saveConfig();
        actionInt = 0;
    } else if (action == "cancel") {
        actionInt = 2;
        RobotState::getInstance().setCurrentState(RobotStateEnum::NORMAL); 
        
    } else if (action == "delete") {
        actionInt = 3;
        //需要调用接口删除保存地图的文件夹，待实现
        RobotState::getInstance().setCurrentState(RobotStateEnum::NORMAL); 
    } else if (action == "complete") {
        actionInt = 1;
        action = "stop";
        // RobotState::getInstance().setCurrentState(RobotStateEnum::NORMAL); 
    } else {
        return ; // 处理其他情况，当前仅有暂停，直接丢弃不处理
    }

    if (action == "cancel") {
        sendCommandToUSLAM("mapping/stop");
    }
    else{
        sendCommandToUSLAM("mapping/" + action);
    }
   
    sendMapAction(actionInt,url,current_mapid);
}

// ****************** event是data_update **************************
// 函数：解析 JSON 字符串并返回 Json::Value 对象
Json::Value parseJson(const std::string& jsonString) {
    Json::CharReaderBuilder readerBuilder;
    Json::Value data;
    std::string errs;
    std::istringstream stream(jsonString);
    if (!Json::parseFromStream(readerBuilder, stream, &data, &errs)) {
        std::cerr << "JSON 解析错误: " << errs ;
    }
    return data;
}

// ****************** event是rtk_account_read **************************
void RobdogCenter::handleRtkAccountRead(const Json::Value &jBody) {
    Json::Value response;
    Json::Value body;

    response["deviceId"] = RobotState::getInstance().getDeviceId();
    response["domain"] = "DEVICE_PLATFORM_INTERACTION";
    response["event"] = "rtk_account_report";
    response["eventId"] = "eventId_rtk" + to_string(base::homiUtils::getCurrentTimeStamp());
    response["seq"] = to_string(base::homiUtils::getCurrentTimeStamp());

    body["rtkAccount"] = rtkAccount;
    body["rtkPass"] = rtkPass;
    body["status"] = Json::Int64(rtkAccountStatus);
    body["expire_day"] = rtkAccountExipreDay;
    response["body"] = body;

    Json::FastWriter writer;
    std::string jsonString = writer.write(response);
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "report: %s", jsonString.c_str());

    sendRequestData(jsonString);
    rtkAccountStatusReport = false;
}

void RobdogCenter::handleDataUpdate(const Json::Value &inValue, const Json::Value &jBody) {
    // 处理数据更新的逻辑
    if (!jBody["changeType"].isNull()) {
        int entityType = jBody["entityType"].asInt();
        std::string entityId = jBody["entityId"].asString();
        // RobotState::getInstance().setMapId(entityId);
        // update_map_points_path();
        // RobotState::getInstance().saveConfig();

        if (entityType == 10001) { // 智能播报【创建和更新提醒】
            // RobdogSmartRemindCtrl::getInstance().CreatAndUpdate(jBody);

            //RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "entityType == 10001");
            std::string data_string = jBody["data"].asString();
            Json::Value data = parseJson(data_string);

            long id = data["id"].asInt64();
            RobotState::getInstance().setRemindId(id);
            std::string deviceId = data["deviceId"].asString();
            std::string title = data["title"].asString();
            // int remindType = data["remindType"].asInt();
            bool enabled = data["enabled"].asBool();

            // std::vector<std::string> weekDays;

            // for (const auto& day : data["time"]["weekDays"]) {
            //     weekDays.push_back(day.asString());
            // }

            // int repeatType = data["time"]["repeatType"].asInt();
            // int dayOfMonth = data["time"]["dayOfMonth"].asInt();
            // int month = data["time"]["month"].asInt();
            // int year = data["time"]["year"].asInt();

            std::vector<std::string> contents;
            std::string text_single = ""; // 提醒文本
            std::string text = ""; // 所有提醒文本
            for (const auto& content : data["contents"]) {
                text_single = content["text"].asString();
                contents.push_back(text_single);
                text += text_single;
                text += "          ";
            }

            std::string remindLocationUid = data["remindLocation"]["uid"].asString();
            std::string remindLocationName = data["remindLocation"]["name"].asString();
            // int familyMemberId = data["familyMember"]["familyMemberId"].asInt();
            std::string nickname = data["familyMember"]["nickname"].asString();
            bool running = data["running"].asBool();

            if (!enabled && jBody["changeType"].asInt() != 1) { // changeType为3的时候需要打断播报
                // RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "!enabled && jBody[changeType].asInt() != 1");
                timer_brocast->cancel();
                brocast_send_count_ = 0;
                // homi_speech_interface::srv::AssistantAbort::Request resMsg;
                // brocast_abort_client->async_send_request(std::make_shared<homi_speech_interface::srv::AssistantAbort::Request>(resMsg));
                auto reqMsg = std::make_shared<homi_speech_interface::srv::AssistantAbort::Request>();
                auto ret = brocast_abort_client->wait_for_service(std::chrono::seconds(1));
                if(ret==false)
                {
                    RCLCPP_WARN(rclcpp::get_logger("robdog_control"),"Failed to waitForExistence service assistant");
                }
                auto result = brocast_abort_client->async_send_request(reqMsg);   
                RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "cut the brocast!!");

                // 此处要取消定点移动
                handleCancelMovement();
            }

            if (running) {
                brocast_text = text;
                int secondOfDay = 0;
                int endSecondOfDay = 0;
                Json::Value time_broc = data["time"];
                if (!time_broc["secondOfDay"].isNull()){
                    secondOfDay = time_broc["secondOfDay"].asInt();
                }
                if (!time_broc["endSecondOfDay"].isNull()) {
                    endSecondOfDay = time_broc["endSecondOfDay"].asInt();
                    if (endSecondOfDay != 0 && endSecondOfDay > secondOfDay) { // 如果是时间段的提醒
                        brocast_total_count_ = (endSecondOfDay - secondOfDay) / 40;}
                    else{
                        brocast_total_count_ = 1;// 说明是单点的提醒 (endSecondOfDay等于0也可能是单点提醒)
                    }
                } else {
                    // endSecondOfDay 字段不存在，默认处理为单点提醒
                    brocast_total_count_ = 1;
                }
                long long mapId = 0;
                std::string current_mapid = "";
                if (!data["remindLocation"]["mapId"].isNull()) {
                    mapId = data["remindLocation"]["mapId"].asInt64();
                    current_mapid = std::to_string(mapId);
                }
                if(data["remindLocation"]["xCoordinate"].isNull() && data["remindLocation"]["yCoordinate"].isNull() && data["remindLocation"]["angle"].isNull()){
                    // 原地播报
                    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Start On-site broadcasting!!");
                    brocast_text = text;
                    timer_brocast->cancel();
                    brocast_send_count_ = 0;
                    timer_brocast->reset();
                    SendBrocastCallback();
                }
                // 判断mapId是否一致，不一致则不前往也不播报
                else if(!current_mapid.empty() && current_mapid != RobotState::getInstance().getMapId()){
                    return;
                }
                else{
                    // 新造一个类似于平台的msg
                    // homi_speech_interface::msg::SIGCEvent::SharedPtr broadcast_msg;
                    auto broadcast_msg = std::make_shared<homi_speech_interface::msg::SIGCEvent>();
                    Json::Value value_brocast;
                    value_brocast["body"] = jBody;
                    Json::StreamWriterBuilder writer;
                    std::string jsonString = Json::writeString(writer, value_brocast);
                    broadcast_msg->event = jsonString;  
                    // RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "The message will be broadcasted, %s", broadcast_msg->event.c_str());             
                    moveToTargetAndBrocast(broadcast_msg); // 放到定点移动模块去执行      
                }
            }

        } else if (entityType == 10006) { // 10006表示家庭建图
            if (!jBody["data"].isNull()) {
                std::string data_string = jBody["data"].asString(); 
                Json::Value data = parseJson(data_string);
                data["mapId"] = entityId; // 加入 entityId (发生变更的数据id,填的是地图id，用于后续有多张地图的时候，更新指定地图的数据)
                // 平台这边还是用entityId表示mapId
                Json::Value reqValue;
                reqValue["client_type"] = CLIENT_LAUNCHER;
                reqValue["target_client"] = CLIENT_NVIDIA;         
                reqValue["action"] = "virtual_wall_control";

                // ************ 按照导航协议构造virtual_wall_control数据 ************
                // Json::Value value_wall;
                // value_wall["entityId"] = entityId;
                // value_wall["virtualWall"] = data;
                reqValue["params"] = data;
                RCLCPP_INFO(rclcpp::get_logger("robdog_control"),"The message of virtual_wall_control: %s", reqValue.toStyledString().c_str());
                SendtoNvOrin(reqValue.toStyledString().c_str(), nConnectIndex_);

                // Json::StreamWriterBuilder writerBuilder;
                // std::string navCtrlMsgs = Json::writeString(writerBuilder, data);
                // std_msgs::msg::String virtualwall_msg;
                // virtualwall_msg.data = navCtrlMsgs;
                // publishVirtualWall->publish(virtualwall_msg);
                // RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Publish virtualwall_msg: %s", virtualwall_msg.data.c_str());
            } else if(jBody["changeType"].asInt() == 2){
                std::string current_mapid = RobotState::getInstance().getMapId();
                if(entityId != current_mapid){
                    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "%d----handleDataUpdate mapId %s is not the same as current mapId %s", __LINE__,entityId.c_str(), current_mapid.c_str());
                    RobotState::getInstance().setMapId(entityId);
                    update_map_points_path();
                    RobotState::getInstance().saveConfig();
                    sendMapAction(4,"",entityId);
                }
            }
        } else if (entityType == 10010) {
            std::thread([this]() {
                node_->playAudio(node_->getResourcePath("audio/diyww.wav"));
            }).detach();
            std::string data = jBody["data"].asString();
            Json::Value jdata = parseJson(data);
            auto diyReq = std::make_shared<homi_speech_interface::srv::SetDiyWord_Request>();
            diyReq->wakeup_word = jdata["wakeupWord"].asString();
            set_diyWakeup_client_->async_send_request(diyReq, [this, inValue](rclcpp::Client<homi_speech_interface::srv::SetDiyWord>::SharedFuture resp_future){
                auto resp = resp_future.get();
                std::thread([this, resp]() {
                    std::string message = "";
                    if (resp->status == 0) message = "唤醒词修改成功,你可以叫我" + resp->enable_ww + ",我很喜欢我的新名字.经常叫我,我会越来越灵敏哦~";
                    else message = "唤醒词修改失败,你还是叫我" + resp->enable_ww + "吧";
                    std::this_thread::sleep_for(std::chrono::seconds(10));
                    sendStringToBrocast(message);
                }).detach();
                
                Json::Value response;
                response["deviceId"] = RobotState::getInstance().getDeviceId();
                response["domain"] = inValue["domain"];
                response["event"] = inValue["event"];
                response["eventId"] = inValue["eventId"];
                response["seq"] = to_string(base::homiUtils::getCurrentTimeStamp());
                response["response"] = "false";
                Json::Value event_body;
                event_body["code"] = resp->status;
                if (event_body["code"] == 0)
                    event_body["msg"] = "";
                else if (event_body["code"] == 1)
                    event_body["msg"] = "设备版本不支持";
                else if (event_body["code"] == 2)
                    event_body["msg"] = "禁用当前唤醒词失败";
                else if (event_body["code"] == 3)
                    event_body["msg"] = "删除已有唤醒词失败";
                else if (event_body["code"] == 4)
                    event_body["msg"] = "唤醒词添加失败";
                else if (event_body["code"] == 5)
                    event_body["msg"] = "唤醒词启用失败";
                else
                    event_body["msg"] = "未知错误";
                response["body"] = event_body;
                Json::FastWriter writer;
                std::string jsonString = writer.write(response);

                this->sendRequestData(jsonString);
            });
        } else if (entityType == 11200)
        {
            RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Update emergency contacts");
            std::string entityId = jBody["entityId"].asString();
            int changeType = jBody["changeType"].asInt();
            update_emergency_contacts(changeType,entityId,jBody["emergencyContact"]);
        }
        else if(entityType == 10013)
        {
            int changeType = jBody["changeType"].asInt();
            std::string data_string = jBody["data"].asString();
            Json::Value data = parseJson(data_string);
            std::string uwbTag = data["uwbTag"].asString();
            if(changeType == 1 || changeType == 2)
            {
                RobotState::getInstance().setUwbTag(uwbTag);
                RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "UWB tag updated: %s", uwbTag.c_str());
            }
            else if(changeType == 3)
            {
                RobotState::getInstance().setUwbTag("");
                RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "UWB tag deleted: %s", uwbTag.c_str());
            }
        }
        else if (entityType == 10011) 
        {//页面开关
            RCLCPP_ERROR(rclcpp::get_logger("robdog_control"), "pushStream signalling detected");
            
            std::string data_string = jBody["data"].asString();
            Json::Value data = parseJson(data_string);
            std::string push_string = data["pushStream"].asString();
            homi_speech_interface::msg::LiveStreamTask msg;
            msg.task = push_string;           
            liveStreamTask_pub->publish(msg);
        }

    } else {
        RCLCPP_ERROR(rclcpp::get_logger("robdog_control"), "Undefined changeType: %d", jBody["changeType"].asInt());
    }
}

// ****************** event是move_points【导航任务都是从这个字段下发】 **************************
void RobdogCenter::handleMovePoints(const Json::Value &inValue) {
    Json::Value jBody = inValue["body"];
    // 处理移动点的逻辑
    //导航任务需要切换到自主模式
    homi_speech_interface::msg::RobdogAction zzmsg;
    zzmsg.actiontype = "NavCtrl";
    zzmsg.actionargument = "AutoMode";
    // publishAction(zzmsg);
    publishAction(std::make_shared<homi_speech_interface::msg::RobdogAction>(zzmsg));

    std::string PathEventId = inValue["eventId"].asString();
    RobotState::getInstance().setPathEventId(PathEventId);
    
    Json::Value additionalParams;
    uuid_t uuid;
    uuid_generate_random(uuid);  // 生成随机 UUID
    char uuid_str[37];
    uuid_unparse_lower(uuid, uuid_str); 
    additionalParams["batchId"] = uuid_str; // "1234"; // 多点导航任务的id【每次平台下发导航任务后都到这个地方来为taskid编号】
    RobotState::getInstance().setbatchId(uuid_str);
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"),"The UUID of TASK: %s", uuid_str);

    HomiRobotStatus enLastStatus = RobotInfoMgr::getInstance().getRobotStatus();
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "query robotdog action status before nav_task: %d", enLastStatus);
    if(RobotInfoMgr::getInstance().getRobotStatus() == ROBDOG_STATUS_GETDOWN){
        sendNavigationReport(4);
        return;
    }
    expresstion_count = 0;
    // 定点移动
    // event：throwGarbage：丢垃圾；deliverExpress：寄快递；takePhotos：拍照；welcomeHome：欢迎回家;园区巡逻(v1.8)：parkPatrol
    // system("/home/<USER>/updateexpression.sh  /home/<USER>/resource/left_right_look.mp4");
    std::string taskType;
    if (!jBody["event"].isNull()) {
        taskType = jBody["event"].asString();
        RobotState::getInstance().setMoveTaskType(taskType);
    } else {
        RobotState::getInstance().setMoveTaskType("");
    }
   
    //五楼演示专用事件，固定点位
    const std::string EVENT_1 = "deliverMedication";
    const std::string EVENT_2 = "tripRoom";
    // // ******************* 处理不同的事件类型 ****************
    if (taskType == "takePhotos") {
        // 处理拍照任务
        RobotState::getInstance().setCurrentState(RobotStateEnum::NAVIGATION); // 设置当前状态为导航中
        handleTakePhotos();
    } else if (taskType == "deliverExpress") {
        // 处理快递任务
        RobotState::getInstance().setCurrentState(RobotStateEnum::NAVIGATION); // 设置当前状态为导航中
        handleDeliverExpress();
    } else if (taskType == "fetchExpress") {
        // 寄快递
        RobotState::getInstance().setCurrentState(RobotStateEnum::NAVIGATION); // 设置当前状态为导航中
        handleFetchExpress();
    } else if (taskType == "parkPatrol") {
        // 园区巡逻
        RobotState::getInstance().setCurrentState(RobotStateEnum::NAVIGATION); // 设置当前状态为导航中
        handleParkPatrol();
    } else if (taskType == EVENT_1) {
        // 园区巡逻
        handlegoHome();
    } else if (taskType == EVENT_2) {
        // 园区巡逻
        handledeliverCake();
    } else if (taskType == "cancelMovement") {
        // 处理取消移动任务
        RobotState::getInstance().setCurrentState(RobotStateEnum::NORMAL); // 取消导航状态
        handleCancelMovement();
    } else if (taskType == "familyMovePoint") {
        handleRepositioningTask(jBody, [this]() { handleReportFamilyMovePoint(); });
    } else if (taskType == "batteryCharging") {
        is_charging = true;
        handleRepositioningTask(jBody, [this]() { handleBatteryChargingPoint(); });
    } else {
            // 处理其他任务
            RobotState::getInstance().setCurrentState(RobotStateEnum::NAVIGATION); // 设置当前状态为导航中
            homi_speech_interface::msg::SIGCEvent move_msg;
            Json::StreamWriterBuilder writerBuilder;
            std::string moveMsgs = Json::writeString(writerBuilder, jBody);
            move_msg.event = moveMsgs;
            // moveToTarget(move_msg);
            moveToTarget(std::make_shared<homi_speech_interface::msg::SIGCEvent>(move_msg));           
    }
}

// 导航前先判断是否已重定位过
void RobdogCenter::handleRepositioningTask(const Json::Value& jBody, const std::function<void()>& onSuccess) {
    repositioning = true;
    std::string current_mapid; 
    if (!jBody["mapId"].isNull()) {
        current_mapid = jBody["mapId"].asString();
    }
    else {
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "plat move mapId is null");
        current_mapid = RobotState::getInstance().getMapId();
    }
    if (repositioningResult.load()) {
        RobotState::getInstance().setCurrentState(RobotStateEnum::NAVIGATION);
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Already relocalized, performing navigation");
        RobotState::getInstance().setCurrentState(RobotStateEnum::NAVIGATION);
        onSuccess();
    } else {
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"),"not relocation, start to do relocation");
        // checkNvidiaServiceStatus(true, current_mapid);
        std::thread([this, onSuccess, jBody]() {
            std::unique_lock<std::mutex> lock(resultMutex);
            auto timeout = std::chrono::milliseconds(5000); 
            if (resultCV.wait_for(lock, timeout, [this] { return repositioningResult.load(); })) {
                if (repositioningResult.load()) {
                    RobotState::getInstance().setCurrentState(RobotStateEnum::NAVIGATION);
                    onSuccess();
                } else {
                    RCLCPP_WARN(rclcpp::get_logger("robdog_control"), "Repositioning failed!");
                    std::thread t_audio(&RobdogCtrlNode::playAudio,node_,node_->getResourcePath("audio/nav/auto_repositon_failed.wav"));
                    t_audio.detach();
                    sendNavigationReport(4);
                    return;
                }
            } else {
                RCLCPP_WARN(rclcpp::get_logger("robdog_control"), "Timeout waiting for repositioning result!");
                std::thread t_audio(&RobdogCtrlNode::playAudio,node_,node_->getResourcePath("audio/nav/auto_repositon_failed.wav"));
                t_audio.detach();
                sendNavigationReport(4);
                return;
            }
        }).detach();
    }
}

// ****************** event是point_report **************************
void RobdogCenter::handlePointReport(const Json::Value &jBody) {
    int type = 0;
    int interval = 0;
    if(!jBody["type"].isNull() && !jBody["interval"].isNull()){
        type = jBody["type"].asInt();
        interval = jBody["interval"].asInt();
    }
    else{
        return;
    }
    if (type == 1) {
        // 处理点报告的逻辑
        RCLCPP_INFO_STREAM(rclcpp::get_logger("robdog_control"), "cmd: point_report");
        //robPoseStatusTimer_->cancel();
        auto new_duration = std::chrono::milliseconds(interval);
        robPoseStatusTimer_ = node_->create_wall_timer(new_duration, 
            std::bind(&RobdogCenter::timerRobotPoseCallback, this));
    } else if (type == 2) { // 取消点位上报
        if (robPoseStatusTimer_ && !robPoseStatusTimer_->is_canceled()) {
            robPoseStatusTimer_->cancel();
            sendCommandToUSLAM("localization/stop");
        } // 停止点位上报
    }
}

// ****************** event是remind_ontime **************************
void RobdogCenter::handleRemindOnTime(const Json::Value &jBody) {

    // RobdogSmartRemindCtrl::getInstance().RemindOnTime(jBody);

    // 处理定时提醒的逻辑（播报立即执行）
    long id = jBody["id"].asInt64();     // 数据 ID // 存起来
    RobotState::getInstance().setRemindId(id);
    std::string deviceId = jBody["deviceId"].asString(); // 设备 ID
    std::string title = jBody["title"].asString();       // 标题
    // int remindType = jBody["remindType"].asInt(); // 1-吃药提醒 2-日程提醒
    bool enabled = jBody["enabled"].asBool(); // 启用状态

    // std::vector<std::string> weekDays;
    // for (const auto &day : jBody["time"]["weekDays"]) {
    //     weekDays.push_back(day.asString()); // 取值集合"Mon","Tue","Wed","Thu","Fri","Sat","Sun"
    // }

    // int repeatType = jBody["time"]["repeatType"].asInt(); // 重复类型：1:每周; 2:单次
    // int dayOfMonth = jBody["time"]["dayOfMonth"].asInt();    // 一个月中的第几天
    // int month = jBody["time"]["month"].asInt(); // 月份
    // int year = jBody["time"]["year"].asInt();   // 年份

    // 播报的点位信息：
    std::string uid = jBody["remindLocation"]["uid"].asString(); // 点位唯一id
    std::string name = jBody["remindLocation"]["name"].asString(); // 位置名称

    std::vector<std::string> contents;
    std::string text_single = ""; // 提醒文本
    std::string text = ""; // 所有提醒文本
    for (const auto &content : jBody["contents"]) {
        std::string contentType = content["contentType"].asString(); // 1-播报内容  2-天气预报
        text_single = content["text"].asString();     // 提醒文本
        // int location = content["location"].asInt(); // 所在城市
        std::string locationStr = content["locationStr"].asString(); // 所在城市，天气提醒必填(冗余)

        // 可以根据需要对内容进行处理或存储
        contents.push_back(text_single); // 示例：将文本内容存入 vector
        // 要把文本拼成一个长文本播放
        // text += "         "; 
        text += text_single;
        text += "          ";
    }

    std::string remindLocationUid = jBody["remindLocation"]["uid"].asString(); // 提醒位置 uid
    std::string remindLocationName = jBody["remindLocation"]["name"].asString(); // 提醒位置名称

    // int familyMemberId = jBody["familyMember"]["familyMemberId"].asInt(); // 关联家庭成员 id
    std::string nickname = jBody["familyMember"]["nickname"].asString(); // 家庭成员昵称

    if (!enabled) {
        // RCLCPP_INFO_STREAM(rclcpp::get_logger("robdog_control"), "No broadcasting!");
        // RobotBroadcastStatusToPlat(0);
        timer_brocast->cancel();
        brocast_send_count_ = 0;
        // RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "APP Stop Broadcast!");
        
        // homi_speech_interface::srv::AssistantAbort::Request resMsg;
        // brocast_abort_client->async_send_request(std::make_shared<homi_speech_interface::srv::AssistantAbort::Request>(resMsg));
        auto reqMsg= std::make_shared<homi_speech_interface::srv::AssistantAbort::Request>();
        auto ret = brocast_abort_client->wait_for_service(std::chrono::seconds(1));
        if(ret==false)
        {
            RCLCPP_WARN(rclcpp::get_logger("robdog_control"),"Failed to waitForExistence service assistant");
        }
        auto result = brocast_abort_client->async_send_request(reqMsg);   
        
        // 取消定点移动
        handleCancelMovement();
        
    } else {
        brocast_text = text;
        int secondOfDay = 0;
        int endSecondOfDay = 0;
        if (!jBody["time"]["secondOfDay"].isNull()){
            secondOfDay = jBody["time"]["secondOfDay"].asInt();
        }
        if (!jBody["time"]["endSecondOfDay"].isNull()) {
            endSecondOfDay = jBody["time"]["endSecondOfDay"].asInt();
            if (endSecondOfDay != 0 && endSecondOfDay > secondOfDay) { // 如果是时间段的提醒
                brocast_total_count_ = (endSecondOfDay - secondOfDay) / 40;}
            else{
                brocast_total_count_ = 1;// 说明是单点的提醒 (endSecondOfDay等于0也可能是单点提醒)
            }
        } else {
            // endSecondOfDay 字段不存在，默认处理为单点提醒
            brocast_total_count_ = 1;
        }
        long long mapId = 0;
        std::string current_mapid = "";
        if (!jBody["remindLocation"]["mapId"].isNull()) {
            mapId = jBody["remindLocation"]["mapId"].asInt64();
            current_mapid = std::to_string(mapId);
        }
        if((jBody["remindLocation"]["xCoordinate"].isNull() && jBody["remindLocation"]["yCoordinate"].isNull() && jBody["remindLocation"]["angle"].isNull()) || RobotState::getInstance().getCurrentState() == RobotStateEnum::NAVIGATION ){
            // 原地播报
            RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Start On-site broadcasting!!");
            brocast_text = text;
            timer_brocast->cancel();
            brocast_send_count_ = 0;
            timer_brocast->reset();
            SendBrocastCallback();
        }
        // else if (jBody["remindLocation"]["mapId"].isNull()) { // 有点位但是没有mapid也会不播报
        //     // 原地播报
        //     RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Start On-site broadcasting!!");
        //     brocast_text = text;
        //     timer_brocast->cancel();
        //     brocast_send_count_ = 0;
        //     timer_brocast->reset();
        //     SendBrocastCallback();
        // }
        // 判断mapId是否一致，不一致则不前往也不播报
        else if(!current_mapid.empty() && current_mapid != RobotState::getInstance().getMapId()){
            return;
        }
        else{ // 默认同时有x、y、angle
            // 新造一个类似于平台的msg
            // homi_speech_interface::msg::SIGCEvent::SharedPtr broadcast_msg;
            auto broadcast_msg = std::make_shared<homi_speech_interface::msg::SIGCEvent>();
            Json::Value value_brocast;
            value_brocast["body"] = jBody;
            Json::StreamWriterBuilder writer;
            std::string jsonString = Json::writeString(writer, value_brocast);
            broadcast_msg->event = jsonString;  
            // RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "The message will be broadcasted, %s", broadcast_msg->event.c_str());             
            moveToTargetAndBrocast(broadcast_msg); // 放到定点移动模块去执行      
        }
    }
}

// ****************** event是unbind_notify **************************
void RobdogCenter::handleUnbindNotify(const Json::Value &jBody) {
    // 处理机器人平台发来的解绑请求
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "unbind_notify");
    
    homi_speech_interface::srv::SIGCData::Request req;
    
    // 将发送的数据设置为固定的字符串 "unbind_notify"
    req.data = "unbind_notify";
    
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Send req to andlinkscript, req is %s", req.data.c_str());

    // 创建客户端并同步调用
    auto future = app_client->async_send_request(std::make_shared<homi_speech_interface::srv::SIGCData::Request>(req));
    
    // 等待并获取响应
    if (future.wait_for(std::chrono::seconds(1)) == std::future_status::ready) {
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Response from APPserver");
    } else {
        RCLCPP_ERROR(rclcpp::get_logger("robdog_control"), "Failed to call APPservice Service_demo");
    }
}

// ****************** event是bind_notify **************************
void RobdogCenter::handlebindNotify(const Json::Value &jBody) {
    // 处理机器人平台发来的绑定成功状态
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "bind_notify");
    //解析event事件和设备信息查询
    Json::FastWriter fastWriter;
    std::string jsonStr = fastWriter.write(jBody);
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "handlebindNotify jBody:%s ", jsonStr.c_str());

    // 恢复默认唤醒词
    auto diyReq = std::make_shared<homi_speech_interface::srv::SetDiyWord_Request>();
    diyReq->wakeup_word = "灵犀灵犀";
    set_diyWakeup_client_->async_send_request(diyReq);

    // 设备绑定成功，异步播放提示音频
    // std::string audio_path = node_->getResourcePath("audio/device_bind_success.wav");
    // RCLCPP_INFO(rclcpp::get_logger("RobdogCenter"), "播放绑定成功提示音: %s", audio_path.c_str());
    // std::thread t_audio(&RobdogCtrlNode::playAudio, node_, audio_path);
    // t_audio.detach();  // 使线程在后台运行

    // 更新绑定状态
    setBindStatus(1);
    
    // 查询设备设置
    handleDeviceSettingQuery();
}

// ****************** event是login_info_notify **************************
void RobdogCenter::handleUserkey(const Json::Value &jBody) {
    // 处理机器人平台发来的Userkey
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "login_info_notify");

    std::string userkey = jBody["userKey"].asString();
    
    // 将发送的数据设置为userkey
    auto msg = std_msgs::msg::String();
    msg.data = userkey;  
    andlink_userkey_pub_->publish(msg);
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Sending userkey to 'andlink_userkey' topic: %s", msg.data.c_str());
}

// ****************** wifi扫描是否正在进行 **************************
int RobdogCenter::is_scan_running() {
    const char* lock_path = "/etc/cmcc_robot/wifi_scan.lock";
    
    // 1. 检查锁文件是否存在（保留原逻辑）
    if (access(lock_path, F_OK) == -1) {
        if (errno == ENOENT) {
            RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "锁文件不存在，无扫描进行");
            return 2; // 文件不存在，返回2（原逻辑）
        } else {
            RCLCPP_ERROR(rclcpp::get_logger("robdog_control"), "访问锁文件失败: %s (错误: %s)", 
                         lock_path, strerror(errno));
            return -1; // 其他错误，返回-1
        }
    }
    
    // 2. 用`open()`打开锁文件（替代`std::ofstream`）
    //  flags说明：
    //  - O_WRONLY：只写模式（对应原`ios::out`）
    //  - O_CREAT：若文件不存在则创建（对应原`ios::out`的隐含行为）
    //  - O_APPEND：追加模式（对应原`ios::app`）
    //  权限说明：0666 → 文件权限为`rw-rw-rw-`（与原`std::ofstream`默认权限一致）
    int fd = open(lock_path, O_WRONLY | O_CREAT | O_APPEND, 0666);
    if (fd == -1) {
        RCLCPP_ERROR(rclcpp::get_logger("robdog_control"), "无法打开锁文件: %s (错误: %s)", 
                     lock_path, strerror(errno));
        return -1; // 打开失败，返回-1
    }
    
    // 3. 尝试获取**非阻塞排他锁**（保留原逻辑）
    //  - LOCK_EX：排他锁（不允许其他进程加锁）
    //  - LOCK_NB：非阻塞（无法加锁时立即返回，不阻塞进程）
    int ret = flock(fd, LOCK_EX | LOCK_NB);
    if (ret == 0) {
        // 3.1 加锁成功 → 无进程持有锁，扫描未运行
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "成功获取锁，无扫描进行");
        flock(fd, LOCK_UN); // 释放锁（保留原逻辑）
        close(fd);          // 手动关闭文件描述符（避免泄漏）
        return 0;           // 返回0（无扫描）
    } else {
        // 3.2 加锁失败 → 处理错误情况
        if (errno == EWOULDBLOCK || errno == EAGAIN) {
            // 3.2.1 锁已被占用 → 扫描正在运行（原逻辑）
            RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "锁已被占用，扫描进行中");
            close(fd);          // 关闭文件描述符
            return 1;           // 返回1（正在扫描）
        } else {
            // 3.2.2 其他错误（如权限不足）
            RCLCPP_ERROR(rclcpp::get_logger("robdog_control"), "获取文件锁失败: %s (错误: %s)", 
                         lock_path, strerror(errno));
            close(fd);          // 关闭文件描述符
            return -1;          // 返回-1（错误）
        }
    }
}

// ****************** event是wifi_list_query **************************
void RobdogCenter::handleWifiList(const Json::Value &jBody) {
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "处理WiFi列表查询请求");
    const std::string json_path = "/etc/cmcc_robot/wifi_scan_result.json";
    
    // 获取当前时间戳（秒级）
    auto now = std::chrono::system_clock::now();
    long long current_ts = std::chrono::duration_cast<std::chrono::seconds>(now.time_since_epoch()).count();
    std::string seq_str = std::to_string(current_ts); 
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "生成时间戳: %s", seq_str.c_str());
    
    Json::Value response;
    int scan_status = is_scan_running();
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "扫描状态: %d", scan_status);
    
    if (scan_status == 1) {
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "扫描正在进行中，返回扫描中响应");
        
        response["deviceId"] = jBody["deviceId"].asString();
        response["domain"] = "DEVICE_PROPERTIES";
        response["event"] = "wifi_list_query";
        response["eventId"] = jBody["eventId"].asString();
        response["seq"] = seq_str;
        
        Json::Value body;
        body["queryStatus"] = 0; // 查询中
        body["wifiList"] = Json::arrayValue; // 空数组
        response["body"] = body;
        
        Json::FastWriter writer;
        std::string jsonString = writer.write(response);
        
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "响应内容: %s", jsonString.c_str());
        sendRequestData(jsonString);
    } 
    else {       
        // 尝试读取Python扫描结果
        try {
            RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "尝试读取现有扫描结果: %s", json_path.c_str());
            
            // 1. 读取JSON文件内容
            std::ifstream result_file(json_path);
            if (!result_file.is_open()) {
                throw std::runtime_error("无法打开结果文件: " + json_path);
            }
            
            // 读取完整文件内容
            std::stringstream buffer;
            buffer << result_file.rdbuf();
            std::string json_content = buffer.str();
            result_file.close();
            RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "文件内容: %s", json_content.c_str());
            
            // 2. 解析JSON
            RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "解析JSON...");
            Json::Value result_json = parseJson(json_content);
            
            // 3. 验证时间戳
            if (!result_json.isMember("seq") || !result_json["seq"].isString()) {
                throw std::runtime_error("JSON缺失有效的seq字段");
            }
            
            // 4. 检查时间戳有效期
            long long result_ts = std::stoll(result_json["seq"].asString());
            long long time_diff = current_ts - result_ts;
            RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "结果时间戳: %lld, 差值: %lld秒", result_ts, time_diff);
            
            if (time_diff > 60) {
                throw std::runtime_error("结果已过期");
            }
            
            // 5. 更新结果的时间戳
            result_json["seq"] = seq_str;
            RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "更新结果时间戳为当前时间");
            
            // 6. 发送更新后的结果
            Json::FastWriter writer;
            std::string jsonString = writer.write(result_json);
            
            RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "发送有效扫描结果");
            RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "结果内容: %s", jsonString.c_str());
            sendRequestData(jsonString);
            
            return;
        } 
        catch (const std::exception& e) {
            RCLCPP_WARN(rclcpp::get_logger("robdog_control"), "无法使用现有扫描结果: %s", e.what());
        }
        
        // 7. 启动新的Python扫描
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "启动新的WiFi扫描...");
        
        std::string cmd = "python3 /usr/bin/cmcc_robot/install/ble/lib/python3.8/site-packages/ble/wifi_scan_script.py scan &";
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "执行命令: %s", cmd.c_str());
        
        int ret = system(cmd.c_str());
        if (ret != 0) {
            RCLCPP_ERROR(rclcpp::get_logger("robdog_control"), "启动扫描脚本失败，返回值: %d", ret);
        } else {
            RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "扫描脚本启动成功");
        }
        
        // 返回"扫描中"响应
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "返回扫描中响应");
        
        response["deviceId"] = jBody["deviceId"].asString();
        response["domain"] = "DEVICE_PROPERTIES";
        response["event"] = "wifi_list_query";
        response["eventId"] = jBody["eventId"].asString();
        response["seq"] = seq_str;
        
        Json::Value body;
        body["queryStatus"] = 0; // 查询中
        body["wifiList"] = Json::arrayValue; // 空数组
        response["body"] = body;
        
        Json::FastWriter writer;
        std::string jsonString = writer.write(response);
        
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "响应内容: %s", jsonString.c_str());
        sendRequestData(jsonString);
    }
}
bool RobdogCenter::is_hex_digit(char c) {
    return (c >= '0' && c <= '9') || 
           (c >= 'a' && c <= 'f') || 
           (c >= 'A' && c <= 'F');
}

// 增强的SSID解码函数（处理转义序列）
std::string RobdogCenter::decode_ssid(const std::string& input) {
    std::string result;
    size_t i = 0;
    size_t len = input.size();

    while (i < len) {
        // 处理\xXX转义（如中文）
        if (i + 3 < len && input[i] == '\\' && input[i+1] == 'x' &&
            is_hex_digit(input[i+2]) && is_hex_digit(input[i+3])) {
            
            int hex_value;
            std::stringstream ss;
            ss << std::hex << input.substr(i+2, 2);
            ss >> hex_value;
            
            result += static_cast<char>(hex_value);
            i += 4;
        }
        // 处理其他转义（如\t、\n）
        else if (i + 1 < len && input[i] == '\\') {
            switch (input[i+1]) {
                case '\\': result += '\\'; break;
                case '"':  result += '"';  break;
                case '\'': result += '\''; break;
                case 't':  result += '\t'; break;
                case 'n':  result += '\n'; break;
                case 'r':  result += '\r'; break;
                default:   result += '\\' + input[i+1]; break; // 未知转义保留原样
            }
            i += 2;
        }
        // 普通字符
        else {
            result += input[i];
            i++;
        }
    }
    return result;
}

// 提取SSID行（移除空白字符）
std::string RobdogCenter::extract_ssid_line(const std::string& line) {
    size_t pos = line.find("SSID: ");
    if (pos == std::string::npos) return "";
    
    std::string ssid_line = line.substr(pos + 6); // "SSID: " 长度为6
    size_t endpos = ssid_line.find_last_not_of(" \r\n\t"); // 移除行尾空白
    return (endpos != std::string::npos) ? ssid_line.substr(0, endpos + 1) : "";
}

// 检查WiFi是否连接到目标SSID
bool RobdogCenter::isWlan0ConnectedToSSID(const std::string& ssid) {
    std::string cmd = "iw dev " + g_ifname + " link";
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Executing command: %s", cmd.c_str());
    
    // 执行命令并读取输出
    std::array<char, 512> buffer;
    std::unique_ptr<FILE, decltype(&pclose)> pipe(popen(cmd.c_str(), "r"), pclose);
    if (!pipe) {
        RCLCPP_ERROR(rclcpp::get_logger("robdog_control"), "Failed to execute iw command");
        return false;
    }

    bool is_connected = false;
    bool found_not_connected = false;
    std::string raw_ssid;

    while (fgets(buffer.data(), buffer.size(), pipe.get()) != nullptr) {
        std::string line(buffer.data());
        
        // 检查是否未连接
        if (line.find("Not connected") != std::string::npos) {
            found_not_connected = true;
        }
        
        // 提取SSID
        if (line.find("SSID: ") != std::string::npos) {
            RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Found SSID field in command output");
            is_connected = true;
            
            raw_ssid = extract_ssid_line(line);
            if (raw_ssid.empty()) {
                RCLCPP_WARN(rclcpp::get_logger("robdog_control"), "SSID field found but could not extract value");
                continue;
            }
            
            // 解码SSID（处理转义）
            std::string actual_ssid = decode_ssid(raw_ssid);
            RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Raw SSID: '%s', Decoded SSID: '%s', Target SSID: '%s'",
                        raw_ssid.c_str(), actual_ssid.c_str(), ssid.c_str());
            
            // 比较SSID
            if (actual_ssid == ssid) {
                RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Already connected to SSID '%s'", ssid.c_str());
                return true;
            } else {
                RCLCPP_WARN(rclcpp::get_logger("robdog_control"), "Connected to different SSID: '%s' (expected '%s')",
                            actual_ssid.c_str(), ssid.c_str());
                return false;
            }
        }
    }

    // 根据结果输出日志
    if (found_not_connected) {
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Interface %s is not connected to any network", g_ifname.c_str());
    } else if (is_connected) {
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Interface %s is connected but no SSID found", g_ifname.c_str());
    } else {
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Interface %s status unknown", g_ifname.c_str());
    }
    
    return false;
}

// ****************** event是wifi_set **************************
void RobdogCenter::handleWifiSet(const Json::Value &jBody) {
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Received wifi_set request");
    
    // 记录设置时间和SSID
    g_wifi_set_time = std::chrono::duration_cast<std::chrono::seconds>(
        std::chrono::system_clock::now().time_since_epoch()
    ).count();
    g_wifi_set_name = jBody["body"]["wifiName"].asString();
    std::string wifiPassword = jBody["body"]["wifiPassword"].asString();

    // 执行Python脚本设置WiFi（后台运行）
    std::string cmd = "python3 /usr/bin/cmcc_robot/install/ble/lib/python3.8/site-packages/ble/wifi_scan_script.py set " +
                      g_wifi_set_name + " " + wifiPassword + " &";
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Executing command: %s", cmd.c_str());
}

// ****************** event是wifi_set_result_query **************************
void RobdogCenter::handleWifiSetResult(const Json::Value &jBody) {
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Received wifi_set_result_query request");
    
    // 获取当前时间戳（秒级）
    auto now = std::chrono::system_clock::now();
    long long current_ts = std::chrono::duration_cast<std::chrono::seconds>(now.time_since_epoch()).count();
    Json::Value response;
    response["deviceId"] = jBody["deviceId"].asString();
    response["domain"] = "DEVICE_PROPERTIES";
    response["event"] = "wifi_set_result_query";
    response["eventId"] = jBody["eventId"].asString();
    response["seq"] = std::to_string(current_ts);
    
    Json::Value body;
    
    // 检查是否连接到目标SSID
    if (isWlan0ConnectedToSSID(g_wifi_set_name)) {
        // 情况1：已成功连接
        body["queryStatus"] = 1; // 连接成功
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), 
                   "WiFi connected to SSID '%s'", g_wifi_set_name.c_str());
    } 
    else {
        // 计算从设置WiFi到现在的时间差（秒）
        long long time_diff = current_ts - g_wifi_set_time;
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), 
                    "Time since WiFi set: %lld seconds", time_diff);
        
        if (time_diff > 60) {
            // 情况2：超时（超过60秒仍未连接成功）
            body["queryStatus"] = 2; // 连接超时
            RCLCPP_WARN(rclcpp::get_logger("robdog_control"),
                       "WiFi connection to SSID '%s' TIMED OUT (after %lld seconds)",
                       g_wifi_set_name.c_str(), time_diff);
        }
        else {
            // 情况3：仍在连接中（未超时）
            body["queryStatus"] = 0; // 连接中/未连接
            RCLCPP_INFO(rclcpp::get_logger("robdog_control"),
                       "WiFi not connected to SSID '%s' (still connecting, %lld seconds elapsed)",
                       g_wifi_set_name.c_str(), time_diff);
        }
    }
    
    response["body"] = body;

    // 发送响应
    Json::FastWriter writer;
    std::string jsonString = writer.write(response);
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Response: %s", jsonString.c_str());
    sendRequestData(jsonString);
}

// ****************** event是unbind_notify_voice **************************
void RobdogCenter::handleUnbindNotifyVoice(const Json::Value &jBody) {
    // 处理机器人平台发来的解绑请求
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "unbind_notify_voice");
    
    homi_speech_interface::srv::SIGCData::Request req;
    
    // 将发送的数据设置为固定的字符串 "unbind_notify_voice"
    req.data = "unbind_notify_voice";
    
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Send req to andlinkscript, req is %s", req.data.c_str());

    // 创建客户端并同步调用
    auto future = app_client->async_send_request(std::make_shared<homi_speech_interface::srv::SIGCData::Request>(req));
    
    // 等待并获取响应
    if (future.wait_for(std::chrono::seconds(1)) == std::future_status::ready) {
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Response from APPserver");
    } else {
        RCLCPP_ERROR(rclcpp::get_logger("robdog_control"), "Failed to call APPservice Service_demo");
    }
    std::this_thread::sleep_for(std::chrono::seconds(2)); 
    std::thread t_audio(&RobdogCtrlNode::playAudio,node_,node_->getResourcePath("bind_network/reset.wav"));
    t_audio.detach();
}

// ****************** event是navigation_notify **************************
void RobdogCenter::handleNavigationNotify(const Json::Value &jBody) {
    // 处理导航通知的逻辑（重定位请求）
    int type = jBody["type"].asInt();
    if (type == 0) {
        Json::Value modifiedBody = jBody;   // jBody本身是不能修改的
        
        Json::Value pointsArray = Json::Value(Json::arrayValue);
        pointsArray.append(-1);
        modifiedBody["points"] = pointsArray;
        Json::StreamWriterBuilder writerBuilder;
        std::string navCtrlMsgs = Json::writeString(writerBuilder, modifiedBody);
        std_msgs::msg::String nav_ctrl_msg;
        nav_ctrl_msg.data = navCtrlMsgs;
        actionPlanningMove_pub->publish(nav_ctrl_msg);

        //WebSocket传输给感知主机
        Json::Value reqValue;
        reqValue["client_type"] = CLIENT_LAUNCHER;
        reqValue["target_client"] = CLIENT_NVIDIA;
        reqValue["action"] = "navigation_control";
        reqValue["params"] = modifiedBody;
        SendtoNvOrin(reqValue.toStyledString().c_str(), nConnectIndex_);

    }
}
// void RobdogCenter::handleMapPointMarking(const Json::Value &jBody) {
//     // 处理标记点位逻辑
//     // 建图完成后，在每次标记点位的过程中，算法会实时消除动态障碍物，并在用户完成点位标记后，同步更新地图。
//     // 需要客户端在用户开始标记点位、结束标记点位时通知平台、本体算法，并且在标点结束时更新当前地图缓存
//     RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "handleMapPointMarking");
//     int action = jBody["action"].asInt();
//     Json::Value reqValue;
//     reqValue["action"] = action == 1 ? 20 : 21;
//     reqValue["url"] = jBody["url"].asString();
//     reqValue["mapId"] = jBody["mapId"].asString();
//     Json::StreamWriterBuilder writerBuilder;
//     std::string navCtrlMsgs = Json::writeString(writerBuilder, reqValue);
//     std_msgs::msg::String nav_ctrl_msg;
//     nav_ctrl_msg.data = navCtrlMsgs;
//     RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "[PUB] %s",navCtrlMsgs.c_str());
//     actionPlanningMove_pub->publish(nav_ctrl_msg);
// }

// // 处理亲密陪伴指令，切换模式或设置安静状态
void RobdogCenter::handleUserInteraction(const Json::Value &jBody)
{
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "handleUserInteraction");
    if (jBody["interMode"].asInt() == 2)
    {
        // 亲密模式关闭
        inactivity_mode_ = false;
        inactivity_timer_->cancel();
        sleep_timer_->reset();
        quiet_for_three_hours_ = true; // 设置三小时安静模式
        last_active_time_ = node_->now(); // 记录当前时间为最近一次用户询问的时间
    }
    else if(jBody["interMode"].asInt() == 1)
    {
        // 亲密模式开启
        inactivity_mode_ = true;
        sleep_timer_->cancel();
        inactivity_timer_->reset();
        asked_in_last_hour_ = false;
    }
    else{
        RCLCPP_WARN(rclcpp::get_logger("robdog_control"),"%d-------------Invalid interMode value: %d", __LINE__,jBody["interMode"].asInt());

    }
}

void RobdogCenter::handleFinishTask(const Json::Value &jBody){
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "handleFinishTask");
    if (jBody["finishTask"].asString() == "mapTask"){
        RobotState::getInstance().setCurrentState(RobotStateEnum::NORMAL); 
        // 结束建图
        Json::Value reqValue;
        Json::Value modifiedJBody;
        reqValue["client_type"] = CLIENT_LAUNCHER;
        reqValue["target_client"] = CLIENT_NVIDIA;
        reqValue["action"] = "mapping_control";
        modifiedJBody["action"] = 2;
        modifiedJBody["mapId"] = RobotState::getInstance().getMapId();
        reqValue["params"] = modifiedJBody;
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"),"The message of finish_map: %s", reqValue.toStyledString().c_str());
        SendtoNvOrin(reqValue.toStyledString().c_str(), nConnectIndex_);
    }
    else{
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "handleFinishTask: %s", jBody["body"]["finishtask"].asString().c_str());
    }
}

void RobdogCenter::handleVoiceResponseNluRaw(const Json::Value &jBody) {
    static const std::unordered_map<std::string, std::string> emotionVideos = {
        {"Sadness", "video/Sadness.mp4"},
        {"Happiness", "video/Happiness.mp4"},
        {"Anger", "video/Anger.mp4"},
        {"Praise", "video/Praise.mp4"},
        {"Criticism", "video/Criticism.mp4"},
        {"Neutrality", "video/Neutrality.mp4"},

    };
    auto it = emotionVideos.find(jBody["emotionTag"].asString());
    if (it != emotionVideos.end()) {
        // ExpressionChange::getInstance().async_callback_work(it->second,1); // 调用对应的处理函数
        ExpressionChange::getInstance().async_callback_work(node_->getResourcePath(it->second),1);
    }
}

void RobdogCenter::handleFollowMeStatus(const Json::Value &inValue) {
    Json::FastWriter fastWriter;
    std::string jsonStr = fastWriter.write(inValue);
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "handle FollowMe Status InValue:%s ",jsonStr.c_str());
    Json::Value response = inValue;
    Json::Value newBody(Json::objectValue);
    const Json::Value &jBody = inValue["body"];
    if (jBody.isMember("items") && jBody["items"].isArray()) {
        const Json::Value& items = jBody["items"];
        for (const auto& item : items) {
            if (item.asString() == "followMe") {
                RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Found 'followMe' in items.");
                newBody["items"]["followMe"]["status"] = RobotState::getInstance().getFollowMeStatus();
                RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "RobotState::getInstance().getFollowMeStatus is %s",RobotState::getInstance().getFollowMeStatus().c_str());
                response["body"] = newBody;
            }
            else if (item.asString() == "emergencyStop"){
                RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Found 'emergencyStop' in items.");
                newBody["items"]["emergencyStop"]["status"] = (RobotInfoMgr::getInstance().getRobotStatus()==ROBDOG_STATUS_LOSS_CTRL)?"on":"off";
                RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "RobotInfoMgr::getInstance().getRobotStatus() is %d",RobotInfoMgr::getInstance().getRobotStatus());
                response["body"] = newBody;
            }
        }
    } else {
        RCLCPP_ERROR(rclcpp::get_logger("robdog_control"),"'items' is not a valid array or is missing." );
    }
    std::string jsonString = fastWriter.write(response);
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Res to FollowMe Status platform is %s",jsonString.c_str());
    sendRequestData(jsonString);
}

void RobdogCenter::handleBindStatusQuery()
{
    Json::Value response;
    response["deviceId"] = RobotState::getInstance().getDeviceId();
    response["domain"] = "ROBOT_BUSINESS_DEVICE";
    response["event"] = "bind_status_query";
    response["eventId"] = "bind_status_query"+to_string(base::homiUtils::getCurrentTimeStamp());
    response["seq"] = to_string(base::homiUtils::getCurrentTimeStamp());
    response["response"] = "false";

    Json::Value body={};

    response["body"];
    response["body"].append(Json::objectValue);

    Json::FastWriter writer;
    std::string jsonString = writer.write(response);

    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "handleBindStatusRequest cmd : %s", jsonString.c_str());

    sendRequestData(jsonString);
}

void RobdogCenter::handleBindStatusResponse(const Json::Value &jBody)
{
    Json::FastWriter fastWriter;
    std::string jsonStr = fastWriter.write(jBody);
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "handleBindStatusResponse InValue:%s ",jsonStr.c_str());
    if (!jBody["status"].isNull()) {
        int bindstatus = jBody["status"].asInt();
        setBindStatus(bindstatus);
        
        // 如果是解绑状态(0)，调用handleUnbindNotify处理解绑
        if (bindstatus == 0) {
            RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "检测到解绑状态，调用handleUnbindNotify");
            handleUnbindNotify(jBody);
        }
    }
}

void RobdogCenter::actionNavigationResponse(const string msg3)
{
        // ------------- 上报导航路径信息 ----------------------
    Json::Value response;
    response["deviceId"] = RobotState::getInstance().getDeviceId();
    response["domain"] = "DEVICE_TRIP";
    response["event"] = "navigation_response";
    response["eventId"] = getEventId();
    response["seq"] = to_string(base::homiUtils::getCurrentTimeStamp());
    response["response"] = "false";

// TODO 相关数据需要更新
    Json::Value body;
    body["tripId"] = (Json::Int64)getTripId();
    body["checkResult"] = 1;
    body["distance"] = -1;
    body["duration"] = -1;
    body["power"] = 80;
    body["estimatedCostPower"] = -1;
    body["maxDistance"] = -1;


    string msg2 =removeEscapeCharacters(msg3);
    string msg = msg2.substr(1, msg2.length() - 2);

    RCLCPP_INFO(rclcpp::get_logger("actionNavigationResponse"), "actionPlanningMove2:%s", msg.c_str());
    Json::Reader reader;
    Json::Value value;
    if (false == reader.parse(msg, value)) {
        RCLCPP_ERROR(rclcpp::get_logger("actionNavigationResponse"), "reader.parse failed: %s", reader.getFormattedErrorMessages().c_str());
        return;
    }

    Json::StreamWriterBuilder writer;
    std::string unescapedMsg = Json::writeString(writer, value["12"]);

    RCLCPP_INFO(rclcpp::get_logger("actionNavigationResponse"), "Unescaped JSON: %s", unescapedMsg.c_str());

    if (!value["12"].isNull()) {
        Json::Value params = value["12"];
        RCLCPP_INFO(rclcpp::get_logger("NvidiaCtrlNode"), "params[12]:%s", params.toStyledString().c_str());
        Json::Value points = params["list"];
        replaceKeyInJsonList(points, "latitude", "lat");
        replaceKeyInJsonList(points, "longitude", "lon");
        body["navigationPath"] = points;
    }
    response["body"] = body;

    Json::FastWriter writer2;
    std::string jsonString = writer2.write(response);

    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "point actionNavigationResponse cmd : %s", jsonString.c_str());

    sendRequestData(jsonString);
}


void RobdogCenter::handleDeviceSettingQuery()
{
    Json::Value response;
    response["deviceId"] = RobotState::getInstance().getDeviceId();
    response["domain"] = "DEVICE_PROPERTIES";
    response["event"] = "device_settings_read";
    response["eventId"] = "device_settings_read"+to_string(base::homiUtils::getCurrentTimeStamp());
    response["seq"] = to_string(base::homiUtils::getCurrentTimeStamp());
    response["response"] = "false";

    Json::Value settings(Json::arrayValue);
    settings.append("volume");
    settings.append("mode");
    settings.append("flashlight");
    settings.append("emergencyContacts");
    settings.append("familyMembers");
    settings.append("uwbTag");

    Json::Value body;
    body["settings"] = settings;
    response["body"] = body;

    Json::FastWriter writer;
    std::string jsonString = writer.write(response);

    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "handleDeviceSettingQuery cmd : %s", jsonString.c_str());

    sendRequestData(jsonString);
}

void RobdogCenter::handleDeviceSettingResponse(const Json::Value &jBody)
{
    Json::FastWriter fastWriter;
    std::string jsonStr = fastWriter.write(jBody);
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "handleDeviceSettingResponse jBody:%s ",jsonStr.c_str());
    if (!jBody["settings"].isNull() && jBody["settings"].isObject()) {
        const Json::Value settings = jBody["settings"];
        if (!settings["flashlight"].isNull() && settings["flashlight"].isObject()) {
            std::string flashlightStatus = settings["flashlight"]["status"].asString();
            int flashlightBrightness = settings["flashlight"]["brightness"].asInt();
            RobotState::getInstance().setFlashStatus(flashlightStatus);
            RobotState::getInstance().setFlashBrightness(flashlightBrightness);
            RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "RobotState::getInstance().setFlashStatus is %s",RobotState::getInstance().getFlashStatus().c_str());
            RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "RobotState::getInstance().setFlashBrightness is %d", RobotState::getInstance().getFlashBrightness());
        }

        if (!settings["volume"].isNull()) {
            int volume = settings["volume"].asInt();
            RobotState::getInstance().setVolume(volume);
            RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "RobotState::getInstance().setVolume is %d", RobotState::getInstance().getVolume());
        }

        if (!settings["mode"].isNull()) {
            int mode = settings["mode"].asInt();
            RobotState::getInstance().setRobdogStatus(mode);
            RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "RobotState::getInstance().setRobdogStatus is %d", RobotState::getInstance().getRobdogStatus());
        }

        // 解析紧急联系人
        if (settings.isMember("emergencyContacts") && settings["emergencyContacts"].isArray()) {
            std::vector<EmergencyContact> emergencyContacts;
            for (const auto& contact : settings["emergencyContacts"]) {
                EmergencyContact ec;
                ec.nickName = contact["nickName"].asString();
                ec.phone = contact["phone"].asString();
                emergencyContacts.push_back(ec);
            }
            RobotState::getInstance().setEmergencyContacts(emergencyContacts);
        }

        // 解析家庭成员
        if (settings.isMember("familyMembers") && settings["familyMembers"].isArray()) {
            std::vector<FamilyMember> familyMembers;
            for (const auto& member : settings["familyMembers"]) {
            FamilyMember fm;
            fm.nickName = member["nickName"].asString();
            fm.roleName = member["roleName"].asString() == "null" ? "" : member["roleName"].asString();
            fm.birthday = member["birthday"].asString();
            fm.phone = member["phone"].asString();
            if(member.isMember("sex")) {
                fm.sex = member["sex"].asInt();
            } else {
                fm.sex = 0; 
            }
            fm.picture = member["picture"].asString();
            familyMembers.push_back(fm);
            }
            RobotState::getInstance().setFamilyMembers(familyMembers);
        }
        if (!settings["uwbTag"].isNull()) {
            std::string uwbTag = settings["uwbTag"].asString();
            RobotState::getInstance().setUwbTag(uwbTag);
            RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "RobotState::getInstance().setUwbTag is %s", RobotState::getInstance().getUwbTag().c_str());
        }

    }    

}

void RobdogCenter::handleMapReposition(const Json::Value &inValue){
    std::string PathEventId = inValue["eventId"].asString();
    RobotState::getInstance().setPathEventId(PathEventId);
    Json::Value jBody = inValue["body"];
    std::string mapId = jBody["mapId"].asString();
    std::string current_mapid = RobotState::getInstance().getMapId();
    if(mapId != current_mapid){
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "%d----handleMapReposition mapId %s is not the same as current mapId %s", __LINE__,mapId.c_str(), current_mapid.c_str());
        RobotState::getInstance().setMapId(mapId);
        update_map_points_path();
        RobotState::getInstance().saveConfig();
    }
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "handleMapReposition mapId:%s",mapId.c_str());
    repositioning = true;
    // checkNvidiaServiceStatus(true,mapId);//导航没开则先打开导航
    // std::thread t_audio(&RobdogCtrlNode::playAudio,node_,node_->getResourcePath("audio/nav/reposition_failed.wav"));
    // t_audio.detach();
    sendMapRepositionResponse(NAVIGATION_CODE_NO_AUTOREPOSITION, Json::Value()); // 传递空值
    repositioning = false;

}

// 地图坐标转像素坐标
std::array<double, 3> RobdogCenter::mapToPixel(double map_x, double map_y, double yaw,
                                               double origin_x, double origin_y, double resolution, int img_height) {
    // 计算像素坐标
    double pixel_x = (map_x - origin_x) / resolution;
    double pixel_y_flip = (map_y - origin_y) / resolution;
    // 反转y轴
    double pixel_y = img_height - pixel_y_flip - 1;
    // yaw（弧度）转角度
    double angle_deg = yaw ;
    return {pixel_x, pixel_y, angle_deg};
}

// 像素坐标转地图坐标
std::array<double, 3> RobdogCenter::pixelToMap(double pixel_x, double pixel_y, double angle_deg,
                                 double origin_x, double origin_y, double resolution, int img_height) {
    double pixel_y_flip = img_height - pixel_y - 1;
    double map_x = origin_x + pixel_x * resolution;
    double map_y = origin_y + pixel_y_flip * resolution;
    double yaw = angle_deg * M_PI / 180.0;
    return {map_x, map_y, yaw};
}

bool RobdogCenter::loadMapConfig(const std::string& filePath, MapConfig& config) {
    std::ifstream fin(filePath);
    if (!fin.is_open()) {
        std::cerr << "无法打开文件: " << filePath << std::endl;
        return false;
    }

    // 读取宽高
    fin >> config.img_width >> config.img_height;

    // 读取分辨率
    fin >> config.resolution;

    // 读取原点坐标 x 和 y
    fin >> config.origin_x >> config.origin_y;

    fin.close();
    return true;
}


void RobdogCenter::sendPoint(double x, double y, double angle) {
    // 构造 JSON 消息
    Json::Value json_data;
    json_data["pix_x"] = x;
    json_data["pix_y"] = y;
    json_data["angle"] = angle;

    // 转换为字符串
    Json::FastWriter writer;
    std::string message_str = writer.write(json_data);

    // 发送消息
    std_msgs::msg::String message;
    message.data = message_str;
    point_transform_publisher_->publish(message);
}

void RobdogCenter::pointTransformResultCallback(const std_msgs::msg::String::SharedPtr msg) {
    try {
        // 解析 JSON 数据
        Json::Reader reader;
        Json::Value value;
        std::string json_str = msg->data;
        if (!reader.parse(json_str, value)) {
            RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Failed to parse JSON: %s", reader.getFormattedErrorMessages().c_str());
            return;
        }
        double x = value["x"].asDouble();
        double y = value["y"].asDouble();
        double angle = value["angle"].asDouble(); 
        RobotState::getInstance().setMoveTaskPose(value); // 更新当前机器人位姿
        // 打印结果
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Received transformed point: x=%.3f, y=%.3f, angle=%.6f", x, y, angle);
    } catch (const std::exception& e) {
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Failed to parse JSON: %s", e.what());
    }
}

void RobdogCenter::RepositioningTimeout() {
    if (repositioning || repositioning_manual) {
        RCLCPP_WARN(rclcpp::get_logger("robdog_control"), "map_reposition timeout.");
        repositioning_timer_->cancel();
        int taskStatusCode = NAVIGATION_CODE_TIME_OUT;
        std::string taskStatusMsg = getDescription(taskStatusCode);
        Json::Value msgValue;
        msgValue["code"] = taskStatusCode;
        msgValue["msg"] = taskStatusMsg;
        sendMapRepositionResponse(taskStatusCode,msgValue);
        sendCommandToUSLAM("localization/stop");
        repositioning = false;
        repositioning_manual = false;
    }
}

void RobdogCenter::handleMapRepositionManual(const Json::Value &inValue) { 
    // 重定位定时器,15秒超时
    repositioning_timer_ = node_->create_wall_timer(std::chrono::seconds(15), 
        std::bind(&RobdogCenter::RepositioningTimeout, this));
    std::string PathEventId = inValue["eventId"].asString();
    RobotState::getInstance().setPathEventId(PathEventId);
    Json::Value jBody = inValue["body"];
    Json::Value point = jBody["point"];
    std::string mapId = jBody["mapId"].asString();
    std::string current_mapid = RobotState::getInstance().getMapId();
    if(mapId != current_mapid){
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "%d----handleMapRepositionManual mapId %s is not the same as current mapId %s", __LINE__,mapId.c_str(), current_mapid.c_str());
        RobotState::getInstance().setMapId(mapId);
        update_map_points_path();
        RobotState::getInstance().saveConfig();
    }
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "handleMapRepositionManual mapId:%s",mapId.c_str());
    repositioning_manual = true;
    double x = point["x"].asDouble();
    double y = point["y"].asDouble();
    double angle = point["angle"].asDouble(); // 角度，单位为度
    sendPoint(x, y, angle); // 发送点位到地图转换节点
    // 等待转换结果
    std::this_thread::sleep_for(std::chrono::milliseconds(500)); // 等待500毫秒，确保点位转换完成
    Json::Value currentPose = RobotState::getInstance().getMoveTaskPose();
    double map_x = currentPose["x"].asDouble();
    double map_y = currentPose["y"].asDouble();
    double yaw = currentPose["angle"].asDouble();
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "current pos x: %f, y: %f, theta: %f", map_x, map_y, yaw);


    // MapConfig config;
    // if (loadMapConfig("/root/tjc_test/map_server_ros2/scripts/tmp/unitree_files/map_display.txt", config)) {
    //     RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Width: %d, Height: %d", config.img_width, config.img_height);
    //     RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Resolution: %f", config.resolution);
    //     RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Origin X: %f, Origin Y: %f", config.origin_x, config.origin_y);
    // }
    // auto result = pixelToMap(x, y, angle, config.origin_x, config.origin_y, config.resolution, config.img_height);
    // double map_x = result[0], map_y = result[1], yaw = result[2];

    std::ostringstream oss;
    oss << "localization/set_initial_pose/" << map_x << "/" << map_y << "/" << yaw;
    sendCommandToUSLAM(oss.str());
    std::this_thread::sleep_for(std::chrono::milliseconds(200)); // 等待200毫秒，确保指令发送完成
    sendCommandToUSLAM("localization/start");
}
// ---------------------------------------------- 具体的函数实现 ------------------------------------------------
// 和定时器相关的操作

void RobdogCenter::setTotalCount(int count) {
    total_count_ = count;
    send_count_ = 0; // 重置已发送次数
}

void RobdogCenter::triggerTimerCallback() {
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Timer Start");
    timer_robMove->reset();
    timerCallback();
}

void RobdogCenter::timerCallback() {
    if (send_count_ < total_count_) {
        // publishVelocity(current_twist_msg_);
        publishVelocity(std::make_shared<geometry_msgs::msg::Twist>(current_twist_msg_));

        ++send_count_; // 增加了定时器次数
        // RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Pub times: %d, Total Count_: %d", send_count_, total_count_);
    } else {
        // 完成发送后停止定时器
        // timer_robMove.stop();
        timer_robMove->cancel();
        send_count_ = 0;
    }
}

void RobdogCenter::heartbeatTimerCallback() {
    if ((base::homiUtils::getCurrentTimeStamp() - currentTimeStramp_) > WEBSOCKET_CON_TIMEOUT) {
        bConnected_ = false;
        std::cerr << "websocket reconnect" ;
        WS_Connect(strConnectUrl_.c_str());
    }
}

int RobdogCenter::readBindStatusFromConfig() {
    const std::string configPath = "/etc/cmcc_robot/andlinkSdk.conf";
    std::ifstream configFile(configPath);
    
    if (!configFile.is_open()) {
        RCLCPP_WARN(rclcpp::get_logger("RobdogCenter"), "无法打开配置文件: %s", configPath.c_str());
        return -1;
    }

    std::string line;
    while (std::getline(configFile, line)) {
        // 忽略注释和空行
        if (line.empty() || line[0] == '#') {
            continue;
        }

        // 查找userBind配置项
        if (line.find("userBind") != std::string::npos) {
            std::string value = line.substr(line.find("=") + 1);
            value = trim(value);
            
            if (value == "1") {
                RCLCPP_INFO(rclcpp::get_logger("RobdogCenter"), "从配置文件读取到绑定状态: 已绑定");
                return 1;
            } else if (value == "0") {
                RCLCPP_INFO(rclcpp::get_logger("RobdogCenter"), "从配置文件读取到绑定状态: 未绑定");
                return 0;
            }
        }
    }

    RCLCPP_WARN(rclcpp::get_logger("RobdogCenter"), "配置文件中未找到userBind配置项");
    return 0;
}

std::string RobdogCenter::trim(const std::string& str) {
    size_t first = str.find_first_not_of(" \t\n\r");
    if (first == std::string::npos) {
        return "";
    }
    size_t last = str.find_last_not_of(" \t\n\r");
    return str.substr(first, (last - first + 1));
}

void RobdogCenter::internetTimerCallback() {
    // 发送网络状态请求 - 无论绑定状态如何，都需要定时执行
    Json::FastWriter writer;
    Json::Value requestJs;
    requestJs["command"]="getNetworkStatus";
    std::string jsonString = writer.write(requestJs);
    auto reqMsg = std::make_shared<homi_speech_interface::srv::NetCtrl::Request>();
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Request to net_ctrl_srv is %s", jsonString.c_str());
    reqMsg->data = jsonString;
    auto ret = net_client->wait_for_service(std::chrono::milliseconds(500));
    if(ret==false) {
        RCLCPP_WARN(rclcpp::get_logger("robdog_control"), "Failed to waitForExistence service assistant");
        return;
    }
    auto result = net_client->async_send_request(reqMsg, std::bind(&RobdogCenter::net_srv_callback, this, std::placeholders::_1));

    
    // processNetworkStatusData();
    // 注意: 处理逻辑已移至 processNetworkStatusData 函数，将在 net_srv_callback 中调用
}
void RobdogCenter::wakeupCallback(const std::shared_ptr<homi_speech_interface::msg::Wakeup> msg){
    RCLCPP_INFO(rclcpp::get_logger("RobdogCenter"),"Received msg form wakeup node: event:%s,angle:%d", msg->ivw_word.c_str(),msg->angle);
    is_interrupted_ = true;  
    cv_.notify_all();  
}
void RobdogCenter::startSequence() {
    stopSequence();  
    if (sequence_thread_.joinable()) {
        sequence_thread_.join();
    }
    is_interrupted_ = false;  
    sequence_thread_ = std::thread(&RobdogCenter::sequenceExecutor, this); 
}
void RobdogCenter::stopSequence() {
    is_interrupted_ = true;  
    cv_.notify_all();  
}
void RobdogCenter::sequenceExecutor() {
    RCLCPP_INFO(rclcpp::get_logger("RobdogCenter"), "Sequence started.");
    for (size_t i = 0; i < mapping_table_.size(); ++i) {
        if (is_interrupted_) {
            RCLCPP_WARN(rclcpp::get_logger("RobdogCenter"), "Sequence interrupted at step %zu.", i);
            break;
        }
        const auto& entry = mapping_table_[i];
        ExpressionChange::getInstance().async_callback_work(node_->getResourcePath(entry.video_path), 1);
        node_->handleLightControl(entry.light_cmd, entry.light_param);
        if (!entry.action.empty()) {
            node_->handleInteractionAction(entry.action);
        }
        // sendStringToBrocast(entry.audio_path);
        node_->sendAudio(node_->getResourcePath(entry.audio_path));
        RCLCPP_INFO(rclcpp::get_logger("RobdogCenter"), "Sequence play audio %s.",entry.audio_path.c_str());
        std::unique_lock<std::mutex> lock(cv_mutex_);
        bool wait_interrupted = cv_.wait_for(
            lock, 
            std::chrono::seconds(entry.interval_seconds), 
            [&]() { return is_interrupted_.load(); }  
        );
        if (wait_interrupted) {
            RCLCPP_WARN(rclcpp::get_logger("RobdogCenter"), "Wait for next step but interrupted.");
            is_interrupted_ = false; 
            int result = std::system("pkill -f aplay");
            if (result != 0) {
                RCLCPP_WARN(rclcpp::get_logger("RobdogCenter"), "Pkill aplay failed.");
            }
            break;  
        }
        RCLCPP_INFO(rclcpp::get_logger("RobdogCenter"), "Going to exe next sequence.");
    }
    node_->handleLightControl(DEEP_CMD_LIGHT_04, 0);
    RCLCPP_INFO(rclcpp::get_logger("RobdogCenter"), "Sequence finished or interrupted,Restore white light");
}

// 处理网络状态数据
void RobdogCenter::processNetworkStatusData() {
    static int empty_count = 0;
    static std::string last_internet_status = "";
    static int last_bind_status = -1;
    static std::chrono::steady_clock::time_point last_audio_time = std::chrono::steady_clock::now();
    static std::chrono::steady_clock::time_point last_QR_time = std::chrono::steady_clock::now();
    static std::chrono::steady_clock::time_point last_network_error_audio_time = std::chrono::steady_clock::now();
    static bool first_bind_stand_triggered = false;  // 新增：标记是否已经触发过首次绑定站立
    std::string current_internet_status = "false";
    std::string video_path;
    
    // 检查绑定状态
    static int bindStatus = -1;

    bool status_changed = false;
    bool QR_play = false;

    // 解析 JSON
    Json::Reader reader;
    Json::Value value;
    if (!reader.parse(g_netctrl_ret, value) || value.isNull()) {
        RCLCPP_ERROR(rclcpp::get_logger("RobdogCenter"), "解析 JSON 失败: %s", g_netctrl_ret.c_str());
        if (bindStatus == -1) {
            bindStatus = readBindStatusFromConfig();
        }
        return;
    }
        
    
    // 处理绑定状态
    if (value.isMember("userBind") && !value["userBind"].isNull()) {
        std::string userBind = value["userBind"].asString();
        int new_bind_status = (userBind == "true") ? 1 : 0;
        if (new_bind_status != last_bind_status) {  // 只在状态真正变化时更新
            status_changed = true;
            RCLCPP_INFO(rclcpp::get_logger("RobdogCenter"), "从网络响应中获取新的绑定状态: %s", userBind.c_str());
            if(new_bind_status != 1){
                node_->handleLightControl(DEEP_CMD_LIGHT_07, 0);
            }
        }
        bindStatus = new_bind_status;
    }
    // }
    
    // 处理网络状态
    if (value.isMember("isInternetConnect") && !value["isInternetConnect"].isNull()) {
        current_internet_status = value["isInternetConnect"].asString();
    }
    // 绑定状态发生变化时更新表情和播放声音
    RCLCPP_INFO(rclcpp::get_logger("RobdogCenter"), "bindStatus'%d  last_bind_status:%d",bindStatus,last_bind_status);
    if (bindStatus != last_bind_status) {
        std::string audio_path;
        last_internet_status="";
        if (bindStatus == 0) {
            // 变为未绑定状态
            if (current_internet_status == "true") {
                video_path = node_->getResourcePath(dynamic_nobind_video_path);
                dynamic_QRcode();
                // 粉灯呼吸
                node_->handleLightControl(DEEP_CMD_LIGHT_PINK_BREATHING, 0);
            } else {
                video_path = node_->getResourcePath(static_nobind_video_path);
                // 黄灯呼吸
                node_->handleLightControl(DEEP_CMD_LIGHT_YELLOW_BREATHING, 0);
            }
            QR_play = true;
            audio_path = node_->getResourcePath("audio/device_not_bound.wav");
            RCLCPP_INFO(rclcpp::get_logger("RobdogCenter"), "绑定状态变为未绑定，更新表情并播放提示音");
            
            // 播放状态变化提示音
            RCLCPP_INFO(rclcpp::get_logger("RobdogCenter"), "播放绑定状态变化提示音: %s", audio_path.c_str());
            std::thread t_audio(&RobdogCtrlNode::playAudio, node_, audio_path);
            t_audio.detach();

            node_->GetDown();
        } else {
            // 变为已绑定状态
            video_path = node_->getResourcePath(resource_default_video_path);
            bool is_unbind_to_bind = (last_bind_status == 0);
            // 首次绑定成功时触发站立动作
            // if (!first_bind_stand_triggered) {
                RCLCPP_INFO(rclcpp::get_logger("RobdogCenter"), "首次绑定成功，触发站立动作");
                node_->StandUp();
                std::thread check_stand_status([this,is_unbind_to_bind]() {
                    // 等待5秒
                    std::this_thread::sleep_for(std::chrono::seconds(5));
                    
                                    // 检查站立状态
                int status = 0;
                if(RobotInfoMgr::getInstance().getRobotStatus() == ROBDOG_STATUS_FORCE_CTRL_LEVEL_LOWSPPEED_GAIT){
                    status = 0;  // 站立
                } else if(RobotInfoMgr::getInstance().getRobotStatus() == ROBDOG_STATUS_GETDOWN){
                    status = 1;  // 趴下 
                } else if(RobotInfoMgr::getInstance().getRobotStatus() == ROBDOG_STATUS_SITDOWN){
                    status = 2;  // 坐下
                }
                
                RCLCPP_INFO(rclcpp::get_logger("RobdogCenter"), "5秒后检查站立状态: %d (0-站立, 1-趴下, 2-坐下)", status);
                
                std::string audio_path;
                if (status == 0) { // 站立成功
                    RCLCPP_INFO(rclcpp::get_logger("RobdogCenter"), "站立成功，播放欢迎语音");
                    if (is_unbind_to_bind) {
                        RCLCPP_INFO(rclcpp::get_logger("RobdogCenter"), "Never bound to bind changes, execute the welcome sequence");
                        startSequence();
                    } else {
                        RCLCPP_INFO(rclcpp::get_logger("RobdogCenter"), "The binding state changes but does not start from the binding, and the welcome sequence is not executed");
                    }
                    // audio_path = node_->getResourcePath("audio/welcome_message.wav");
                    // std::thread t_audio(&RobdogCtrlNode::playAudio, node_, audio_path);
                    // t_audio.detach();
                } else { // 趴下或坐下
                    RCLCPP_INFO(rclcpp::get_logger("RobdogCenter"), "站立失败，播放提示语音并再次尝试站立");
                    audio_path = node_->getResourcePath("audio/stand_failed.wav");
                    std::thread t_audio(&RobdogCtrlNode::playAudio, node_, audio_path);
                    t_audio.detach();
                    node_->StandUp(); // 再次尝试站立
                }
                });
                check_stand_status.detach();  // 分离线程，让它在后台运行
                first_bind_stand_triggered = true;  // 标记已触发
            // }
        }
        
        // 更新表情
        ExpressionChange::getInstance().async_callback_work(video_path, 0);
        
        last_bind_status = bindStatus;
        // if (bindStatus != 0) {
        //     last_internet_status = "";  // 重置网络状态，以便触发网络状态检查
        // } else {
        //     return;  // 如果变为未绑定状态，直接返回，不检查网络状态
        // }
    }
    
    // 检查是否需要播放未绑定提示音（每3分钟播放一次）
    if (bindStatus == 0) {
        auto current_time = std::chrono::steady_clock::now();
        auto elapsed_time = std::chrono::duration_cast<std::chrono::minutes>(current_time - last_audio_time).count();
        if (elapsed_time >= 3) {
            // 播放未绑定提示音
            std::string audio_path = node_->getResourcePath("audio/device_not_bound.wav");
            RCLCPP_INFO(rclcpp::get_logger("RobdogCenter"), "定期播放未绑定提示音: %s", audio_path.c_str());
            std::thread t_audio(&RobdogCtrlNode::playAudio, node_, audio_path);
            t_audio.detach();
            last_audio_time = current_time;
        }

        auto elapsed_qr_time = std::chrono::duration_cast<std::chrono::minutes>(current_time - last_QR_time).count();
        // 未绑定5分钟更新动态二维码
        if ((QR_play == false) && (current_internet_status == "true") &&(elapsed_qr_time >= 5)) {                
            video_path = node_->getResourcePath(dynamic_nobind_video_path);
            dynamic_QRcode();
            // 更新表情
            ExpressionChange::getInstance().async_callback_work(video_path, 0);            
            last_QR_time = current_time;
        }
        return;  
    }
    
    // 只有在已绑定状态下才检查网络状态
    if (bindStatus == 1) {
        // 检查 isInternetConnect 字段
        if (value.isMember("isInternetConnect") && !value["isInternetConnect"].isNull()) {
            
            // 网络异常时每3分钟播报一次
            if (current_internet_status != "true") {
                auto current_time = std::chrono::steady_clock::now();
                auto elapsed_time = std::chrono::duration_cast<std::chrono::minutes>(current_time - last_network_error_audio_time).count();
                if (elapsed_time >= 3) {
                    std::string audio_path = node_->getResourcePath("audio/network_disconnected.wav");
                    RCLCPP_INFO(rclcpp::get_logger("RobdogCenter"), "定期播放断网提示音: %s", audio_path.c_str());
                    std::thread t_audio(&RobdogCtrlNode::playAudio, node_, audio_path);
                    t_audio.detach();
                    last_network_error_audio_time = current_time;
                }
            }
            
            // 只在网络状态发生变化时更新表情
            if (current_internet_status != last_internet_status) {
                std::string audio_path;
                
                if (current_internet_status == "true") {
                    video_path = node_->getResourcePath(resource_default_video_path);
                    audio_path = node_->getResourcePath("audio/network_connected.wav");
                    RCLCPP_INFO(rclcpp::get_logger("RobdogCenter"), "网络连接恢复，播放提示音");

                    // 白灯常亮
                    node_->handleLightControl(DEEP_CMD_LIGHT_04, 0);

                } else {
                    video_path = node_->getResourcePath(resource_nonet_video_path);
                    audio_path = node_->getResourcePath("audio/network_disconnected.wav");
                    RCLCPP_INFO(rclcpp::get_logger("RobdogCenter"), "网络连接断开，播放提示音");

                    // 黄灯常亮 【网络断开】
                    node_->handleLightControl(DEEP_CMD_LIGHT_07, 0);
                }
                
                RCLCPP_INFO(rclcpp::get_logger("RobdogCenter"), "网络状态变化，更新表情, isInternetConnect: %s, video_path: %s", 
                    current_internet_status.c_str(), video_path.c_str());
                ExpressionChange::getInstance().async_callback_work(video_path, 0);
                
                // 播放网络状态变化提示音
                std::thread t_audio(&RobdogCtrlNode::playAudio, node_, audio_path);
                t_audio.detach();
                
                last_internet_status = current_internet_status;
            }
        }
    }
    
    // 处理初始化状态
    // int init_finish_status = getInitFinishStatus();
    // if (init_finish_status == 0) {
    //     setInitFinishStatus(1);
    //     RCLCPP_INFO(rclcpp::get_logger("RobdogCenter"), "初始化完成，bindStatus: %d", bindStatus);
    // }
}



void RobdogCenter::timerRobotPoseCallback() {
    Json::Value body = RobotState::getInstance().getCurRobotPose();
    if (body.isNull()) {
        // RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "timerRobotPoseCallback: no position"); //【发的太频繁先注释】
        return;
    }
    // RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "timerRobotPoseCallback: has position");
    
    // ------------- 上报点位信息 ----------------------
    Json::Value response;
    response["deviceId"] = RobotState::getInstance().getDeviceId();
    response["domain"] = "DEVICE_INTERACTION";
    response["event"] = "point_report";
    response["eventId"] = "robdog_plat_" + to_string(base::homiUtils::getCurrentTimeStamp());
    response["seq"] = 0;

    Json::Value currentPoint;
    currentPoint["x"] = body["x"].asDouble();
    currentPoint["y"] = body["y"].asDouble();
    currentPoint["angle"] = body["angle"].asDouble();
    currentPoint["pointStatus"] = body["pointStatus"].asInt(); 
    response["body"] = currentPoint;

    Json::FastWriter writer;
    std::string jsonString = writer.write(response);

    if(RobotState::getInstance().getCurrentState() == RobotStateEnum::NAVIGATION){//改成仅导航任务中打印点位信息，避免太过频繁打印
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "point report cmd : %s", jsonString.c_str());
    }

    // 调用服务并处理响应【给平台上报点位信息】
    sendRequestData(jsonString);   
}

// 路径上报
void RobdogCenter::timerRobotPathCallback() {
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), " --------------------- Start TimerRobotPathCallback ---------------------");
    Json::Value body = RobotState::getInstance().getCurRobotPose();
    Json::Value path = RobotState::getInstance().getMoveTaskPath();
    if (body.isNull() || path.isNull()) {
        return;
    }
    // ------------- 上报路径信息 ----------------------
    // 创建输出 JSON 对象
    Json::Value pathBody;
    Json::Value outputRoot;
    outputRoot["deviceId"] = RobotState::getInstance().getDeviceId();
    outputRoot["domain"] = "DEVICE_INTERACTION";
    outputRoot["event"] = "move_path_report";
    outputRoot["eventId"] = RobotState::getInstance().getPathEventId(); // "robdog_plat_" + to_string(base::homiUtils::getCurrentTimeStamp());
    std::time_t now = std::time(nullptr);
    outputRoot["seq"] = std::to_string(now);
    outputRoot["response"] = "false";
    pathBody["batchId"] = RobotState::getInstance().getbatchId();

    if(at_target_){ // 导航任务已经完成，但是没有收到status==2，以导航状态为依据再发一次
        pathBody["status"] = 2;
        outputRoot["body"] = pathBody; 
    }
    else{
        // 当前点 (currentPoint)
        pathBody["status"] = 1;
        Json::Value currentPoint;
        if(!body["x"].isNull() && !body["y"].isNull() && !body["angle"].isNull()){
            currentPoint["x"] = body["x"].asDouble();
            currentPoint["y"] = body["y"].asDouble();
            currentPoint["angle"] = body["angle"].asDouble();
        }
        pathBody["currentPoint"] = currentPoint;

        // 移动路径 (path) 
        Json::Value pathArray(Json::arrayValue);
        if (!path.isNull() && path.isArray()) {
            for (const auto& pathPoint : path) {
                Json::Value pathItem;
                if (pathPoint.isMember("path_x") && pathPoint.isMember("path_y") && pathPoint.isMember("path_angle")) {
                    pathItem["x"] = pathPoint["path_x"].asDouble();
                    pathItem["y"] = pathPoint["path_y"].asDouble();
                    pathItem["angle"] = pathPoint["path_angle"].asDouble();
                    pathArray.append(pathItem);
                }
            }
        }
        pathBody["path"] = pathArray;
        outputRoot["body"] = pathBody;
    }
    
    Json::FastWriter writer_1;
    std::string jsonString_1 = writer_1.write(outputRoot);
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "path report cmd : %s", jsonString_1.c_str());

    // 调用服务并处理响应
    sendRequestData(jsonString_1);
    

    if(RobotState::getInstance().getCurrentState() != RobotStateEnum::NAVIGATION) robPathStatusTimer_->cancel();  
}

// ******************************** 和设备信息相关的 *************************************************************
void RobdogCenter::deepStatusCallback(const homi_speech_interface::msg::ProprietySet::SharedPtr msg) {
    // RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Received msg form robdog_ctrl Node.cmd is :%d,value is:%d", msg->cmd, msg->value);
    switch (msg->cmd) {
        case POWER_LEVEL_FROM_NODE:
            RobotState::getInstance().setBatteryLevel(msg->value);
            RobotState::getInstance().setBatteryChargeStatus(msg->value);
            break;
        case WIFI_NAME_FROM_NODE:
            RobotState::getInstance().setWifiName(msg->exmsg);
            break;
        default:
            break;
    }
    RobotState::getInstance().saveConfig();
    return;
}

void RobdogCenter::utStatusCallback() 
{
    unitree_go::msg::dds_::SportModeState_ utSportState;
    unitree_go::msg::dds_::LowState_ utLowState;

    RobotInfoMgr::getInstance().utGetHighState(utSportState);
    RobotInfoMgr::getInstance().utGetLowState(utLowState);

    RobotState::getInstance().setBatteryLevel(utLowState.bms_state().soc());

    //云深处定义：0未在充电，1充电中 2 充满
    //宇树定义：正代表充电，负代表放电，与云深处定义不同，需转化
    if (utLowState.bms_state().current() > 0)
    {
        RobotState::getInstance().setBatteryChargeStatus(1);
    }
    else
    {
        RobotState::getInstance().setBatteryChargeStatus(0);
    }
    //RobotState::getInstance().setWifiName(msg->exmsg);

    RobotState::getInstance().saveConfig();
    
    return;
}

std::string RobdogCenter::get_connect_info_request(const Json::Value &inValue) {
    Json::Value response = inValue;
    Json::Value body;
    Json::Value data;
    response["event"] = "connect_info_response";
    data["status"] = RobotState::getInstance().getUserConnectStatus();
    data["phone"] = RobotState::getInstance().getUserPhoneNumber();
    data["lastConnectTs"] = Json::Int64(RobotState::getInstance().getTimeStamp());
    // RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "RobotState::getInstance().getTimeStamp=%lld", RobotState::getInstance().getTimeStamp());
    body["data"] = data;
    body["code"] = 0;
    body["msg"] = "";
    response["body"] = body;
    // 将 Json::Value 转换成字符串
    Json::FastWriter writer;
    std::string jsonString = writer.write(response);
    return jsonString;
}

std::string RobdogCenter::get_robot_properties(const Json::Value &inValue) {
    // 将整个 JSON 对象转换为字符串
    Json::FastWriter fastWriter;
    std::string jsonStr = fastWriter.write(inValue);
    // 打印输出 JSON 字符串
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "InValue:%s ",jsonStr.c_str());
    Json::Value response = inValue;
    const Json::Value &properties = inValue["body"]["properties"];
    Json::Value newBody(Json::objectValue);

    // 使用 promise 和 future 来同步异步数据
    std::promise<Json::Value> networkStatusPromise;
    auto networkStatusFuture = networkStatusPromise.get_future();

    for (const auto &property : properties) {
        std::string propertyName = property.asString();

        if (propertyName == "networkStatus") {
             // 异步获取网络状态
        // Json::FastWriter writer;
        // Json::Value requestJs;
        // requestJs["command"] = "getNetworkStatus";
        // std::string jsonString = writer.write(requestJs);
        // auto reqMsg = std::make_shared<homi_speech_interface::srv::NetCtrl::Request>();
        // reqMsg->data = jsonString;

        // auto ret = net_client->wait_for_service(std::chrono::seconds(1));
        // if (!ret) {
        //     RCLCPP_WARN(rclcpp::get_logger("robdog_control"), "Failed to waitForExistence service assistant");
        //     networkStatusPromise.set_value(Json::Value()); // 设置空值
        // } else {
        //     // auto result = net_client->async_send_request(reqMsg, std::bind(&RobdogCenter::net_srv_callback, this, std::placeholders::_1));
        //     auto result = net_client->async_send_request(reqMsg, [this, &networkStatusPromise](rclcpp::Client<homi_speech_interface::srv::NetCtrl>::SharedFuture response) {
        //         Json::Reader reader;
        //         Json::Value value;
        //         if (reader.parse(response.get()->result, value)) {
        //             networkStatusPromise.set_value(value); // 设置解析后的值
        //         } else {
        //             networkStatusPromise.set_value(Json::Value()); // 设置空值
        //         }
        //     });
        // }
        // Json::Value networkStatus = networkStatusFuture.get(); // 获取异步数据
        // if (!networkStatus.isNull()) {
        //     newBody["properties"]["networkStatus"] = networkStatus;
        // } else {
        //     newBody["properties"]["networkStatus"]["wifiState"] = "on";
        //     newBody["properties"]["networkStatus"]["mobileDataState"] = "on";
        //     // newBody["properties"]["networkStatus"]["wifiName"] = "robot_wifi";
        //     newBody["properties"]["networkStatus"]["isWifiConnect"] = true;
        // }
        Json::Reader reader;
        Json::Value value;
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"),"g_netctrl_ret is %s",g_netctrl_ret.c_str());
        reader.parse(g_netctrl_ret, value);
        if (!value.isNull()) {
            newBody["properties"]["networkStatus"] = value;
        } else {
            newBody["properties"]["networkStatus"]["wifiState"] = "off";
            newBody["properties"]["networkStatus"]["mobileDataState"] = "off";
            // newBody["properties"]["networkStatus"]["wifiName"] = "robot_wifi";
            newBody["properties"]["networkStatus"]["isWifiConnect"] = "false";
        }
    } else if (propertyName == "battery") {
            // newBody["properties"][propertyName]["power"] = RobotState::getInstance().getBatteryLevel();
            // newBody["properties"][propertyName]["status"] = RobotState::getInstance().getBatteryChargeStatus();
             Json::Value batteryStatus(Json::objectValue);
            batteryStatus["power"] = RobotInfoMgr::getInstance().getBatteryLevel();
            batteryStatus["status"] = (RobotInfoMgr::getInstance().getIsCharging())?1:0;
            newBody["properties"][propertyName] = batteryStatus;
        } else if (propertyName == "intelligent") {
            newBody["properties"][propertyName] = RobotState::getInstance().getIntelligentSwitch();
        } else if (propertyName == "flashlight") {
            newBody["properties"][propertyName]["status"] = RobotState::getInstance().getFlashStatus();
            newBody["properties"][propertyName]["brightness"] = RobotState::getInstance().getFlashBrightness();
        } else if (propertyName == "volume") {
            newBody["properties"][propertyName] = RobotState::getInstance().getVolume();
        } else if (propertyName == "connect_info") {
            newBody["properties"][propertyName]["changeType"] = RobotState::getInstance().getUserConnectStatus();
            newBody["properties"][propertyName]["phone"] = RobotState::getInstance().getUserPhoneNumber();
        } else if (propertyName == "posture") {
            Json::Value posture(Json::objectValue); //状态，0站立，1趴 2 坐
            int status = 0;
            if(RobotInfoMgr::getInstance().getRobotStatus() == ROBDOG_STATUS_FORCE_CTRL_LEVEL_LOWSPPEED_GAIT){
                status = 0;  // 站立
            } else if(RobotInfoMgr::getInstance().getRobotStatus() == ROBDOG_STATUS_GETDOWN){
                status = 1;  // 趴下 
            } else if(RobotInfoMgr::getInstance().getRobotStatus() == ROBDOG_STATUS_SITDOWN){
                status = 2;  // 坐下
            }
            posture["status"] = status;
            RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "posture send to platform is %d",status);
            newBody["properties"][propertyName] = posture;
        } else if (propertyName == "followMe") {
            newBody["properties"][propertyName]["status"] = RobotState::getInstance().getFollowMeStatus();
        } else if (propertyName == "amapLocation"){
            newBody["properties"][propertyName]["lon"] = rtkMsg.longitude;
            newBody["properties"][propertyName]["lat"] = rtkMsg.latitude;
        }
        else if (propertyName == "temperature") 
        {
            char msg[128] = "";
			std::string CPUTempStatus = RobotInfoMgr::getInstance().getCPUTempStatus();
			std::string TempStatus = RobotInfoMgr::getInstance().getTempStatus();

			if (0 != strcmp("Normal", CPUTempStatus.c_str()))
        	{
	            newBody["properties"][propertyName]["status"] = CPUTempStatus;
	            if (0 == strcmp("TooHigh", CPUTempStatus.c_str()))
	            {
	                snprintf(msg, sizeof(msg), "机器狗CPU温度过高，已停止运行!请等温度恢复正常后，再尝试作业。");
	            }
				else if (0 == strcmp("TooLow", CPUTempStatus.c_str()))
				{
					snprintf(msg, sizeof(msg), "机器狗CPU温度过低，已停止运行!请等温度恢复正常后，再尝试作业。");
				}
        	}
			else if (0 != strcmp("Normal", TempStatus.c_str()))
			{
	            newBody["properties"][propertyName]["status"] = TempStatus;
				if (0 == strcmp("TooHigh", TempStatus.c_str()))
	            {
	                snprintf(msg, sizeof(msg), "机器狗关节温度过高，已停止运行!请等温度恢复正常后，再尝试作业。");
	            }
				else if (0 == strcmp("TooLow", TempStatus.c_str()))
				{
					snprintf(msg, sizeof(msg), "机器狗关节温度过低，已停止运行!请等温度恢复正常后，再尝试作业。");
				}
			}
            else
            {
                newBody["properties"][propertyName]["status"] = "Normal";
            }

            newBody["properties"][propertyName]["msg"] = msg;
        }
		else if (propertyName == "speed") 
		{
			newBody["properties"][propertyName]["value"] = RobotInfoMgr::getInstance().getSpeedLevel();
		}
    }
    response["body"] = newBody;
    // 将 Json::Value 转换成字符串
    Json::FastWriter writer;
    std::string jsonString = writer.write(response);
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Request to platform is %s",jsonString.c_str());
    return jsonString;
}

std::string RobdogCenter::get_hardware_state(const Json::Value &inValue) {
    // 将整个 JSON 对象转换为字符串
    Json::FastWriter fastWriter;
    std::string jsonStr = fastWriter.write(inValue);
    // 打印输出 JSON 字符串
    RCLCPP_DEBUG(rclcpp::get_logger("robdog_control"), "InValue:%s ",jsonStr.c_str());
    Json::Value response = inValue;
    const Json::Value &hardwareList = inValue["body"]["hardwareList"];
    Json::Value newBody(Json::objectValue);

    // 使用 promise 和 future 来同步异步数据
    std::promise<Json::Value> networkStatusPromise;
    auto networkStatusFuture = networkStatusPromise.get_future();

    for (const auto &property : hardwareList) {
        std::string propertyName = property.asString();

        if (propertyName == "realSense"){
#ifndef UNITREE
            newBody[propertyName]["stateCode"] = RobotState::getInstance().getRealSenseState();
#else
            newBody[propertyName]["stateCode"] = 2;
#endif
            continue;
        } else if (propertyName == "lidar"){
            newBody[propertyName]["stateCode"] = RobotState::getInstance().getLidarState();
#ifdef UNITREE
            newBody[propertyName]["dirtyPercent"] = RobotState::getInstance().getLidarDirtyPercent();
#endif
            continue;
        } else if (propertyName == "battery"){
            newBody["battery"]["power"] = RobotInfoMgr::getInstance().getBatteryLevel();
            newBody["battery"]["status"] = (RobotInfoMgr::getInstance().getIsCharging())?1:0;
        } else if (propertyName == "networkStatus"){
            getNetworkInfo(newBody);
        } else if (propertyName == "temperature"){
            newBody["temerature"] = RobotState::getInstance().getBoardTempStatus();
        } else if (propertyName == "hardwareusage"){
            newBody["temerature"] = RobotState::getInstance().getBoardTempStatus();
        } else if (propertyName == "processstatus"){
            newBody["process"] = RobotState::getInstance().getProcessStatus();
        } else if (propertyName == "audiodevicestatus"){
            newBody["AudioDevice"]["stateCode"] = RobotState::getInstance().getAudioStatus();
        } else if (propertyName == "uwb"){
            newBody["uwb"]["statusCode"] = RobotState::getInstance().getUWBStatus();
        } else if (propertyName == "bluetooth"){
            newBody["bluetooth"]["status"] = RobotState::getInstance().getBluetoothStatus();
        } else if (propertyName == "rtk"){
            newBody["rtk"]["stateCode"] = RobotState::getInstance().getRTKState();
        } else if (propertyName == "mic") {
            newBody["mic"]["stateCode"] = RobotState::getInstance().getMicStatus();
        } else if (propertyName == "camera") {
            newBody["camera"]["forehead"]["stateCode"] = RobotState::getInstance().getForeheadCameraStatus();
            newBody["camera"]["bowtie"]["stateCode"] = RobotState::getInstance().getTieCameraStatus();
        } else if (propertyName == "hardwareStatus") {
            auto jointTemp = RobotInfoMgr::getInstance().getRobotTemperature();
            std::string jointPropertyName = "";
            // Joint temperature
            for(int i = 0;i< MAX_JOINT_NUM;i++) {
                jointPropertyName = jointNames[i];
                newBody[propertyName][jointPropertyName]["temperature"] = (double)jointTemp[i];
                newBody[propertyName][jointPropertyName]["stateCode"] = (double)jointTemp[i]>TEMP_THREADHOLD ? 1 : 0;
            }
        }
    }
    response["response"] = false;
    response["body"] = newBody;
    // 将 Json::Value 转换成字符串
    Json::FastWriter writer;
    std::string jsonString = writer.write(response);
    RCLCPP_DEBUG(rclcpp::get_logger("robdog_control"), "Request to platform is %s",jsonString.c_str());
    return jsonString;
}

void RobdogCenter::getNetworkInfo(Json::Value& body) {

    Json::Reader reader;
    Json::Value value;
    reader.parse(g_netctrl_ret, value);
    if (value.isNull())
    {
        body["networkStatus"]["wifiState"] = Json::Value();
        body["networkStatus"]["mobileDataState"] = Json::Value();
        body["networkStatus"]["wifiName"] = Json::Value();
        body["networkStatus"]["isWifiConnect"] = Json::Value();
    }
    else {
        body["networkStatus"]["wifiState"] = value["wifiState"];
        body["networkStatus"]["mobileDataState"] = value["mobileDataState"];
        body["networkStatus"]["wifiName"] = value["wifiName"];
        body["networkStatus"]["isWifiConnect"] = value["isWifiConnect"];
    }
}

void RobdogCenter::setProperties(const Json::Value &request) {
    const Json::Value &properties = request["body"]["properties"];
    if (properties.isMember("flashlight")) {
        const Json::Value &flahlight = properties["flashlight"];
        if (flahlight.isMember("status"))
            RobotState::getInstance().setFlashStatus(flahlight["status"].asString());
        if (flahlight.isMember("brightness"))
            RobotState::getInstance().setFlashBrightness(flahlight["brightness"].asInt());
        // int valueStatus = (RobotState::getInstance().getFlashStatus() == "on") ? 1 : 0;
        // publishStatusCtrl(DEEP_CMD_FLASHLIGHT, valueStatus, RobotState::getInstance().getFlashBrightness());
        //改为topic发布通知：
        if (RobotState::getInstance().getFlashStatus() == "on"){

            auto message = std_msgs::msg::String();
            message.data = "light_open";
            flashlight_control_pub_->publish(message);

        }
        if (RobotState::getInstance().getFlashStatus() == "off"){

            auto message = std_msgs::msg::String();
            message.data = "light_close";
            flashlight_control_pub_->publish(message);

        }


    }
    if (properties.isMember("volume") || properties.isMember("changeVolume")) {
        int volume = -1;
        if(properties.isMember("changeVolume")) {
            int oldVolume = RobotState::getInstance().getVolume();
            volume = properties["changeVolume"].asInt() + oldVolume;
            if(volume > 100) {
                volume = 100;
            } else if(volume < 0) {
                volume = 0;
            }
            if(volume == oldVolume) {
                return;
            }
            RobotState::getInstance().setVolume(volume);
        } else {
            volume = properties["volume"].asInt();
            RobotState::getInstance().setVolume(volume);
        }
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Volume set to %d", volume);
        if (volume < 0 || volume > 100) {
            RCLCPP_ERROR(rclcpp::get_logger("robdog_control"), "Volume must be between 0 and 100.");
            return;
        }
        // FILE* fp;
        // char buffer[50]={0};
        // fp = popen(R"(aplay -l | grep "USB Audio Device" -A 2 | grep "card" | awk '{print $2}' | tr -d ':')", "r");
        // if(!fp) {
        //     RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "ThirdpartyAudioDevice Search audio device failed");
        //     return;
        // }
        // if (fgets(buffer, sizeof(buffer), fp) == nullptr) {
        //     RCLCPP_ERROR(rclcpp::get_logger("robdog_control"), "No audio device found.");
        // }
        // if (pclose(fp) == -1) {
        //     RCLCPP_ERROR(rclcpp::get_logger("robdog_control"), "Error closing the pipe.");
        // }
        // std::string cardNumber(buffer);
        // cardNumber.erase(std::remove(cardNumber.begin(), cardNumber.end(), '\n'), cardNumber.end());
        // cardNumber.erase(std::remove(cardNumber.begin(), cardNumber.end(), ' '), cardNumber.end());
        // std::string command = "amixer -c   " + (cardNumber) + "   sset PCM " + std::to_string(volume) + "%";
        // 宇树狗默认声卡为0,并且音量调节为 50% ~ 100% ，所以需要映射
        // 将音量设置为其自身的一半再加50
        volume = 60 + (volume * 2 / 5);
        // 构建系统命令以设置PCM音量
        std::string command = "amixer -c 0 sset PCM " + std::to_string(volume) + "%";
        // 执行系统命令
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "CMD: %s ", command.c_str());
        int result = std::system(command.c_str());
        // 检查系统命令是否执行成功
        if (result != 0) {
            // 如果命令返回非零值，记录错误信息
            RCLCPP_ERROR(rclcpp::get_logger("robdog_control"), "Error setting PCM volume. Command returned: %d", result);
        }
    }
    if (properties.isMember("networkStatus")) {
        const Json::Value &networkStatus = properties["networkStatus"];
        Json::FastWriter writer;
        Json::Value requestJs;
        requestJs["command"] = "setNetworkStatus";
        requestJs["networkStatus"] = networkStatus;

        std::string jsonString = writer.write(requestJs);
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Request to net_ctrl_srv is %s", jsonString.c_str());
        auto reqMsg= std::make_shared<homi_speech_interface::srv::NetCtrl::Request>();
        reqMsg->data = jsonString;

        auto ret = net_client->wait_for_service(std::chrono::seconds(1));
        if(ret==false)
        {
            RCLCPP_WARN(rclcpp::get_logger("robdog_control"),"Failed to waitForExistence service assistant");
        }
        auto result = net_client->async_send_request(reqMsg, std::bind(&RobdogCenter::net_srv_callback, this, std::placeholders::_1));   
        
        if (networkStatus.isMember("wifiState")){
            RobotState::getInstance().setWifiSwitch(networkStatus["wifiState"].asString());
        }
        if (networkStatus.isMember("mobileDataState")){

            RobotState::getInstance().setMobileDataSwitch(networkStatus["mobileDataState"].asString());
        }
        int value = (RobotState::getInstance().getWifiSwitch() == "on") ? 1 : 0;
        int exvalue=(RobotState::getInstance().getMobileDataSwitch() == "on") ? 1 : 0;
    }
    if (properties.isMember("intelligent")) { // 储存平台写入的多地形自适应状态
        // RobotState::getInstance().setIntelligentSwitch(properties["intelligent"].asString());  // 改到在发布UDP消息的位置保存到xml
        int value = (properties["intelligent"].asString() == "on") ? 1 : 0;
        // publishStatusCtrl(DEEP_CMD_AI_MOTION, value, 0);
        if(value && RobotInfoMgr::getInstance().getRobotStatus()==ROBDOG_STATUS_FORCE_CTRL_LEVEL_LOWSPPEED_GAIT){ // 做一个状态判断，必须在站立的情况下才能发送开启AI模式
            // 向机器狗发送开启多地形自适应的指令（越障+RL）
            homi_speech_interface::msg::RobdogAction msg;// 创建消息
            msg.actiontype = "gaitControl";
            msg.actionargument = "obstacleCross";
            publishAction(std::make_shared<homi_speech_interface::msg::RobdogAction>(msg));
        }
        else{
            // 向机器狗发送关闭多地形自适应的指令
            homi_speech_interface::msg::RobdogAction msg;// 创建消息
            msg.actiontype = "gaitControl";
            msg.actionargument = "exit";
            publishAction(std::make_shared<homi_speech_interface::msg::RobdogAction>(msg));
        }
    }
    if (properties.isMember("rtkAccount")) {
        std::string rtkAccount = properties["rtkAccount"].asString();
        std::string rtkPassword = properties["rtkPass"].asString();

        //publish
        std::cout << "rtkAccount: " << rtkAccount.c_str() << "rtkPassword:" <<  rtkPassword.c_str() << std::endl;
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "rtkAccount: %s", rtkAccount.c_str());
        auto requestSetRtkInfo =
            std::make_shared<homi_speech_interface::srv::NtripAccount_Request>();
        requestSetRtkInfo->user = rtkAccount.c_str();
        requestSetRtkInfo->pwd = rtkPassword.c_str();

        ntrip_account_client_->async_send_request(requestSetRtkInfo);
    }
    RobotState::getInstance().saveConfig();
}

// 发送建图初始化响应
void RobdogCenter::sendMapCompleteResponse(int code,long mapId) {
    Json::Value response;
    response["deviceId"] = RobotState::getInstance().getDeviceId();
    response["domain"] = "APP_DEVICE_INTERACTION";
    response["event"] = "device_map_save_response";
    response["eventId"] = getEventId();
    response["requestId"] = "requestId"+to_string(base::homiUtils::getCurrentTimeStamp());
    response["seq"] = to_string(base::homiUtils::getCurrentTimeStamp());

    Json::Value body;
    body["code"] = code;
    body["mapId"] = Json::Int64(mapId);
    body["msg"] = getDescription(code);
    response["body"] = body;

    Json::FastWriter writer;
    std::string jsonString = writer.write(response);
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "device_map_save_response: %s", jsonString.c_str());
    sendRequestData(jsonString);
    // sendNavigationAction(1, RobotState::getInstance().getMapId());//建图结束开启导航
}

void RobdogCenter::sendMapRepositionResponse(int code,const Json::Value &jBody) {
    Json::Value response;
    response["deviceId"] = RobotState::getInstance().getDeviceId();
    response["domain"] = "APP_DEVICE_INTERACTION";
    if(repositioning){
        response["event"] = "map_reposition";
    }
    else if(repositioning_manual){
        response["event"] = "map_reposition_manual";
    }
    response["eventId"] = RobotState::getInstance().getPathEventId();
    response["requestId"] = "requestId"+to_string(base::homiUtils::getCurrentTimeStamp());
    response["seq"] = to_string(base::homiUtils::getCurrentTimeStamp());
    response["response"] = "false";
    
    Json::Value newBody;
    // Json::FastWriter writer1;
    // std::string jsonString1 = writer1.write(jBody);
    // RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "MapReposition msgs!!!!: %s", jsonString1.c_str());


    if(code == NAVIGATION_CODE_RELOCALIZATION_FINISHED){
        code = 0;//如果重定位成功，响应code为0
    }
    newBody["code"] = code;
    newBody["msg"] = getDescription(code);
    if (!jBody["currentPoint"].isNull()) {
        newBody["currentPoint"] = jBody["currentPoint"];
    } 
    else{
        newBody["currentPoint"] = RobotState::getInstance().getCurRobotPose();
    }
    newBody["mapId"] = RobotState::getInstance().getMapId();
    response["body"] = newBody;

    Json::FastWriter writer;
    std::string jsonString = writer.write(response);
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "sendMapRepositionResponse: %s", jsonString.c_str());
    sendRequestData(jsonString);
}

void RobdogCenter::deviceAlarmReport(int code) {
    // ---------------------------------- 告警上报 ------------------------------------------
    Json::Value alarmReport;
    Json::Value alarmBody;
    alarmReport["deviceId"] = RobotState::getInstance().getDeviceId();
    alarmReport["domain"] = "DEVICE_ALARM";
    alarmReport["event"] = "device_alarm_report";
    alarmReport["eventId"] = "robdog_alarm_" + to_string(base::homiUtils::getCurrentTimeStamp());
    alarmReport["requestId"] = "requestId" + to_string(base::homiUtils::getCurrentTimeStamp());
    alarmReport["seq"] = std::to_string(base::homiUtils::getCurrentTimeStamp());
    
    // alarmBody["notifystrategy"] = Json::arrayValue;
    // Json::Value notifyRole1;
    // notifyRole1["notifyRole"] = 1;
    // notifyRole1["notifyway"] = Json::arrayValue;
    // // notifyRole1["notifyway"].append(1);
    // notifyRole1["notifyway"].append(3);
    // alarmBody["notifystrategy"].append(notifyRole1);

    //平台需求，暂时只上报异常情况，1100-1205为建图异常，2100-2208为导航异常
    //设置告警类别、告警模块、告警级别和告警描述
      // 定义正则表达式，匹配以21或22开头的整数
    std::regex pattern(R"(^(221|222)\d*$)");
    if (1100 <= code && code <= 1205)
    {
        alarmBody["alarmType"] = "建图告警"; 
        alarmBody["launcherModel"] = "map_status";
        // code = code + base_map;
        alarmBody["alarmCode"] = code;
        alarmBody["alarmName"] = getCodeName(code); 
        alarmBody["alarmLevel"] = 4;
        alarmBody["alarmDesc"] = getDescription(code); 
    }
    else if (std::regex_match(std::to_string(code), pattern))
    {
        // code = code + base_navigation;
        auto now = std::chrono::steady_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::seconds>(now - lastCancelMovementTime).count();

        // 打印时间间隔
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Duration since last cancel movement: %ld seconds", duration);
        if (duration >= 1 && RobotState::getInstance().getCurrentState() == RobotStateEnum::NAVIGATION){
            handleCancelMovement();
            lastCancelMovementTime = now; // 更新上次调用时间
            std::thread t_audio(&RobdogCtrlNode::playAudio,node_,node_->getResourcePath("audio/nav/unreachable.wav"));
            t_audio.detach();
            RobotState::getInstance().setCurrentState(RobotStateEnum::NORMAL); // 取消导航状态
            robPathStatusTimer_->cancel(); 
            RobotState::getInstance().setMoveTaskPath(Json::Value()); // 清空路径
        }
        else return;
        
        alarmBody["alarmType"] = "导航告警"; 
        alarmBody["launcherModel"] = "navigation_status"; 
        alarmBody["alarmCode"] = code;
        alarmBody["alarmName"] = getCodeName(code); 
        alarmBody["alarmLevel"] = 4;
        alarmBody["alarmDesc"] = getDescription(code); 
    }
    else
    {
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "code = %d,code is not in the range",code);
        return;
    }

    alarmReport["body"] = alarmBody;
    Json::FastWriter writer;
    std::string alarmJsonString = writer.write(alarmReport);
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "alarmJsonString : %s", alarmJsonString.c_str());
    sendRequestData(alarmJsonString);
}

void RobdogCenter::processRepositioningResult(int taskStatusCode, const Json::Value &jBody) {
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Starting processRepositioningResult at %ld", base::homiUtils::getCurrentTimeStamp());
    // 使用 unique_lock 锁定 resultMutex
    std::unique_lock<std::mutex> lock(resultMutex);
    
    if ((repositioning || repositioning_manual) && taskStatusCode == NAVIGATION_CODE_RELOCALIZATION_FINISHED) {
        repositioningResult.store(true); // 设置重定位成功
        std::thread t_audio(&RobdogCtrlNode::playAudio,node_,node_->getResourcePath("audio/nav/reposition_success.wav"));
        t_audio.detach();
        sendMapRepositionResponse(taskStatusCode,jBody);
        repositioning = false;
        repositioning_manual = false;
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "repositioning is true ");
    } else if ((repositioning || repositioning_manual) && 
               (taskStatusCode == NAVIGATION_CODE_INVALID_COMMAND_OUTSIDE_NAVIGATION || 
                taskStatusCode == NAVIGATION_CODE_MAP_ID_MISMATCH || 
                taskStatusCode == NAVIGATION_CODE_COMMAND_FAILED || 
                taskStatusCode == NAVIGATION_CODE_RELOCALIZATION_ERROR_LARGE||
                taskStatusCode == NAVIGATION_CODE_TIME_OUT)) {
        std::thread t_audio(&RobdogCtrlNode::playAudio,node_,node_->getResourcePath("audio/nav/reposition_failed.wav"));
        t_audio.detach();
        repositioningResult.store(false); // 设置重定位失败
        sendMapRepositionResponse(taskStatusCode,jBody);
        sendCommandToUSLAM("localization/stop");
        repositioning = false;
        repositioning_manual = false;
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "repositioning is false ");
    }

    resultCV.notify_all(); // 通知等待的线程
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "ending processRepositioningResult at %ld", base::homiUtils::getCurrentTimeStamp());
}

// ************************************************ 和感知主机相关的操作(导航) ************************************************
//处理webSocket消息（按照topic名称处理）
void RobdogCenter::parseWsActionMsg(Json::Value& value) {
    string action = value["action"].asString();
    //感知主机下发的机器人移动的角速度和线速度
    if (action == "motionArcWithObstacles") {
        Json::Value params = value["params"];
        double lineSpeed = params["lineSpeed"].asDouble();
        double angularSpeed = params["angularSpeed"].asDouble();
        geometry_msgs::msg::Twist twist;
        twist.linear.x = lineSpeed;
        twist.angular.z = angularSpeed;
        publishVelocity(std::make_shared<geometry_msgs::msg::Twist>(twist));
    }
    else if (action == "navigation_position") { // 感知主机需要上报给平台的导航路径信息
        Json::Value params = value["params"];
        if (params.isMember("mode")) {
            if (params["mode"].isInt()) {
                counterFreq++;
                if (counterFreq >= RAW_FREQUENCY||params["status"].asInt()==FINISH){
                    handleStripPathReport(params);
                    counterFreq=0;
                }
                return;
            }
        }
        if (!params["x"].isNull()) {
            Navigation_node = true; // 有路径上报，说明当前导航节点是开启的
            // RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Navigation_node is active!!!!!!!!");
        }
        RobotState::getInstance().setCurRobotPose(params); // 里面就包含了点位和路径
        // 接受感知主机上报的导航位置以及路径规划信息
        
    }
    else if (action == "navigation_path") { // 感知主机需要上报给平台的导航路径信息

        Json::Value params = value["params"];
        if (!params.isMember("path")) {
            RCLCPP_ERROR(rclcpp::get_logger("robdog_control"), "No path data received");
            return;
        }
        Json::Value path = params["path"];
        if (path.isNull() || !path.isArray()) {
            RCLCPP_ERROR(rclcpp::get_logger("robdog_control"), "Invalid path data received");
            return;
        }
        RobotState::getInstance().setMoveTaskPath(path); // 里面就包含了点位和路径
        
    }
    else if (action == "playTts"){
        std::string textPlay= value["params"]["text"].asString();
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "send String To Brocast,content is %s",textPlay.c_str());   
        sendStringToBrocast(textPlay);
    }
    else if (action == "console_status") {
        int consoleStatusCode = value["params"]["code"].asInt();
        if(consoleStatusCode == 100 && !navHeartbeat){
            std::string current_mapid = RobotState::getInstance().getMapId();
            if (current_mapid != "100") {
                RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "%d---current_mapid: %s", __LINE__, current_mapid.c_str());
                // checkNvidiaServiceStatus(true,current_mapid);
                navHeartbeat = true;
            }
        }
    }
    else if (action == "task_status") {   // 和导航任务状态相关的部分
        Json::Value params = value["params"];
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "params: %s", Json::FastWriter().write(params).c_str());

        if (!params.isMember("msg") || !params["msg"].isObject()) {
            RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Invalid params format");
            return;
        }

        int type = params["type"].asInt();
        Json::Value msgValue = params["msg"];
        std::string uid = msgValue.isMember("uid") ? msgValue["uid"].asString() : "";
        std::string taskStatusMsg = msgValue.isMember("msg") ? msgValue["msg"].asString() : "";
        int taskStatusCode = params["code"].asInt();
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "task_status code = %d", taskStatusCode);

        handleTaskStatusCode(taskStatusCode, msgValue);
    }
    else if (action == "pathPlanning")
    {
        Json::StreamWriterBuilder writer;
        std::string strParams = Json::writeString(writer, value["params"]);
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "pathPlanning: %s", strParams.c_str());
        actionNavigationResponse(strParams);
    }
    else if(action == "robotPose")
    {
        actionRobdogPose(value["params"]);
    }
}

void RobdogCenter::handleTaskStatusCode(int taskStatusCode, const Json::Value& msgValue) {
    if (11000 <= taskStatusCode && taskStatusCode <= 11205) {
        deviceAlarmReport(taskStatusCode);
        // taskStatusCode += base_map;
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "%d------------MAP_taskStatusCode = %d", __LINE__,taskStatusCode);
        RobotStateEnum currentState = RobotState::getInstance().getCurrentState();
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "CurrentState: %d", static_cast<int>(currentState));
        if(RobotState::getInstance().getCurrentState() == RobotStateEnum::MAPPING){
            handleMapCompleteStatus(taskStatusCode, msgValue);
            RobotState::getInstance().setCurrentState(RobotStateEnum::NORMAL); 
        }
        if(RobotState::getInstance().getCurrentState() == RobotStateEnum::MAP_INITIALIZING && taskStatusCode != MAP_CODE_CANCELLED && taskStatusCode != MAP_CODE_STOPPED){
            handleMapInitialStatus(taskStatusCode, msgValue);
        }
        
    } else if (22000 <= taskStatusCode && taskStatusCode <= 22209) {
        deviceAlarmReport(taskStatusCode);
        // taskStatusCode += base_navigation;
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "%d------------NAV_taskStatusCode = %d", __LINE__,taskStatusCode);
        // handleNavBeforeRepositioning(taskStatusCode, msgValue);//宇树不做重定位前检查导航是否打开与开错的情况
        handleNavigationStatus(taskStatusCode,msgValue);
    } else if (55000 <= taskStatusCode && taskStatusCode <= 55211) {
        // taskStatusCode += base_charging;
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "%d------------CHARGE_taskStatusCode = %d", __LINE__,taskStatusCode);
        if(isChargeMarking){
            handleChargeMarkStatus(taskStatusCode, msgValue);
        }
        else if(is_charging){
            handleChargeActionStatus(taskStatusCode, msgValue);
        }
    } else {
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "taskStatusCode = %d, not in the range", taskStatusCode);
    }
}

void RobdogCenter::handleMapInitialStatus(int taskStatusCode, const Json::Value& msgValue) {
    auto duration = std::chrono::steady_clock::now() - response_start_time;
    auto duration_ms = std::chrono::duration_cast<std::chrono::milliseconds>(duration);

    // 打印调试信息
    RCLCPP_INFO(
        rclcpp::get_logger("robdog_control"), 
        "Duration: %ld ms, waiting_for_response: %s", 
        duration_ms.count(), 
        waiting_for_response ? "true" : "false"
    );
    if (waiting_for_response && duration <= timeout_duration) {
        if (taskStatusCode == MAP_CODE_STARTED) {
            sendMapInitialResponse(0, "");
            RobotState::getInstance().setCurrentState(RobotStateEnum::MAPPING);
        } else if (isInitializationFailure(taskStatusCode)) {
            sendMapInitialResponse(taskStatusCode, getDescription(taskStatusCode));
            RobotState::getInstance().setCurrentState(RobotStateEnum::NORMAL);
        }
        waiting_for_response = false;
    }
}

bool RobdogCenter::isInitializationFailure(int taskStatusCode) {
    return taskStatusCode == ERROR_CODE_MAP_UPLOAD_FAILED ||
        taskStatusCode == ERROR_CODE_NEW_MAP_COMMAND_DURING_BUILD ||
        taskStatusCode == ERROR_CODE_NAVIGATION_COMMAND_DURING_BUILD ||
        taskStatusCode == ERROR_CODE_NODE_CRASHED;
}

void RobdogCenter::handleMapCompleteStatus(int taskStatusCode, const Json::Value& msgValue) {
    if (msgValue.isMember("mapId")) {
        std::string mapIdStr = msgValue["mapId"].asString();
        long mapId = -1; // 设置一个默认值，表示无效的 mapId
        if (std::regex_match(mapIdStr, std::regex("^[0-9]+$"))) {
            mapId = std::stoll(mapIdStr);
            RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Converted mapId: %ld", mapId);
        } else {
            RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Invalid mapId format: %s", mapIdStr.c_str());
        }
        if (taskStatusCode == MAP_CODE_STOPPED) {
            taskStatusCode = 0;
            if (mapId != -1) { // 确保 mapId 是有效的
                // sendCommandToUSLAM("common/set_map_id/" + mapIdStr);
                sendMapCompleteResponse(taskStatusCode, mapId);
                // std::this_thread::sleep_for(std::chrono::milliseconds(100));
                // sendCommandToUSLAM("common/get_map_id");
            }
        } else if (taskStatusCode == ERROR_CODE_MAP_SAVE_FAILED || taskStatusCode == ERROR_CODE_MAP_ID_MISMATCH || taskStatusCode == ERROR_CODE_MAP_UPLOAD_FAILED) {
            sendMapCompleteResponse(taskStatusCode, mapId);           
        }
    } else {
        RCLCPP_WARN(rclcpp::get_logger("robdog_control"), "Missing mapId in MapCompleteStatus");
    }
}

bool RobdogCenter::isChargeMarkFailure(int taskStatusCode) {
    return taskStatusCode == CHARGING_ERROR_INVALID_COMMAND_FORMAT ||
        taskStatusCode == CHARGING_ERROR_NAVIGATION_NODE_NOT_STARTED ||
        taskStatusCode == CHARGING_ERROR_POINT_FUNCTION_FAILED ||
        taskStatusCode == CHARGING_ERROR_POINT_ENTRY_FAILED_NO_QRCODE ||
        taskStatusCode == CHARGING_ERROR_POINT_ENTRY_FAILED_ALGORITHM_ERROR ||
        taskStatusCode == CHARGING_ERROR_CHARGING_FAILED_ALIGNMENT_TIMEOUT ||
        taskStatusCode == CHARGING_ERROR_CHARGING_FUNCTION_FAILED_NO_POLE;
}

void RobdogCenter::handleChargeMarkStatus(int taskStatusCode, const Json::Value& msgValue) {
    if (taskStatusCode == CHARGING_CODE_POINT_ENTRY_COMPLETED) {
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "handleChargeMarkStatus");
        Json::Value point = RobotState::getInstance().getCurRobotPose();
        Json::Value arrayPoint(Json::arrayValue);
        arrayPoint.append(point);

        Json::FastWriter writer;
        std::string jsonStr = writer.write(arrayPoint);
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "ChargeMark point is %s", jsonStr.c_str());

        sendChargeMarkResponse(0, "");
        updateMapPoints("batteryCharging", arrayPoint);
        isChargeMarking = false;
    } else if (isChargeMarkFailure(taskStatusCode)) {
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "ChargeMarkFailure");
        sendChargeMarkResponse(1, getDescription(taskStatusCode));
        isChargeMarking = false;
    }
}

void RobdogCenter::handleChargeActionStatus(int taskStatusCode, const Json::Value& msgValue) {
    // if(taskStatusCode == NAVIGATION_CODE_NAVIGATION_TASK_COMPLETED){//已到达导航点
    //     RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "arrived at charging navigation point");
    //     std::string current_mapid = RobotState::getInstance().getMapId();
    //     int actionInt = 3;
    //     std::string BatteryChargingPoint = ReadMapCfg::getInstance().getBatteryChargingPoints();
    //     Json::Reader reader;
    //     Json::Value point;
    //     if (!reader.parse(BatteryChargingPoint, point)) {
    //         RCLCPP_ERROR(rclcpp::get_logger("robdog_control"), "Failed to parse JSON: %s", BatteryChargingPoint.c_str());
    //         return;
    //     }
    //     sendChargeAction(actionInt,current_mapid,point);
    // } else 
    if(taskStatusCode == CHARGING_CODE_CHARGING_COMPLETED) {
        sendChargeMarkResponse(0, "");
    } else if (isChargeMarkFailure(taskStatusCode)) {
        sendChargeMarkResponse(1, getDescription(taskStatusCode));
        is_charging = false;
    }
}

void RobdogCenter::handleNavBeforeRepositioning(int taskStatusCode, const Json::Value& msgValue) {
    if (repositioning) {
        if (taskStatusCode == NAVIGATION_CODE_NODE_STOPPED) {//重定位先关闭不一致的导航节点，关闭成功后开启当前mapid导航
            RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Opening navigation with New mapId: %s", RobotState::getInstance().getMapId().c_str());
            sendNavigationAction(1, RobotState::getInstance().getMapId());
        } else if (taskStatusCode == NAVIGATION_CODE_NODE_STARTED) {//重定位之前开启导航成功后下发重定位
            RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Navigation node started, sending navigation action 11 with Map ID: %s", RobotState::getInstance().getMapId().c_str());
            sendNavigationAction(11, RobotState::getInstance().getMapId());
        } else if (isRepositioningFailure(taskStatusCode)) {//重定位之前关闭导航失败结束重定位状态，repositioning置为false
            RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Closing navigation failed before map reposition.");
            sendMapRepositionResponse(taskStatusCode,msgValue);
            repositioning = false;
        }
    }
}

bool RobdogCenter::isRepositioningFailure(int taskStatusCode) {
    return taskStatusCode == NAVIGATION_CODE_NEW_NAVIGATION_COMMAND_DURING_NAVIGATION ||
        taskStatusCode == NAVIGATION_CODE_NEW_NAVIGATION_COMMAND_DURING_MAPPING ||
        taskStatusCode == NAVIGATION_CODE_NODE_CRASHED;
}

//导航相关状态处理与上报
void RobdogCenter::handleNavigationStatus(int taskStatusCode,const Json::Value& jBody) {
    int status = mapCodeToStatus(taskStatusCode);
    if (status <= NAVIGATION_STATUS_UNREACHABLE_DURING_MOVEMENT) {
        sendNavigationReport(status);
        if (status == 1) {//导航完成
            handleNavigationTaskCompletion();
        }
    }

    if (taskStatusCode == NAVIGATION_CODE_SINGLE_POINT_NAVIGATION_COMPLETED) {
        atSinglePoint = true;
    }
    if (taskStatusCode == NAVIGATION_CODE_NAVIGATION_TASK_CANCELED) {//导航取消
        RobotState::getInstance().setCurrentState(RobotStateEnum::NORMAL); 
    }
    if (taskStatusCode == NAVIGATION_CODE_NODE_STARTED) {
        Navigation_node = true;
    }
    if (taskStatusCode == NAVIGATION_CODE_NODE_STOPPED) {
        Navigation_node = false;
    }
    if (isRepositioningResult(taskStatusCode)) {
        repositioning_timer_->cancel();
        processRepositioningResult(taskStatusCode, jBody);
    }
}

void RobdogCenter::sendNavigationReport(int status) {
    Json::Value response;
    Json::Value body;
    body["status"] = status;
    body["batchId"] = RobotState::getInstance().getbatchId();
    response["deviceId"] = RobotState::getInstance().getDeviceId();
    response["domain"] = "DEVICE_INTERACTION";
    response["event"] = "navigation_report";
    response["eventId"] = RobotState::getInstance().getPathEventId().empty() ?
                        "robdog_plat_" + std::to_string(base::homiUtils::getCurrentTimeStamp()) :
                        RobotState::getInstance().getPathEventId();
    response["seq"] = Json::Int64(base::homiUtils::getCurrentTimeStamp());
    response["body"] = body;
    Json::FastWriter writer;
    std::string navigationReportString = writer.write(response);
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "navigation_report : %s", navigationReportString.c_str());
    sendRequestData(navigationReportString);
}

void RobdogCenter::handleNavigationTaskCompletion() {
    expresstion_count++;
    if (expresstion_count == 8) {
        expresstion_count = 0;
    }
    at_target_ = true;
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "at_target_: %d", at_target_);
    RobotState::getInstance().setCurrentState(RobotStateEnum::NORMAL);
    sendCommandToUSLAM("navigation/stop");
}

bool RobdogCenter::isRepositioningResult(int taskStatusCode) {
    return taskStatusCode == NAVIGATION_CODE_RELOCALIZATION_FINISHED ||
        taskStatusCode == NAVIGATION_CODE_INVALID_COMMAND_OUTSIDE_NAVIGATION ||
        taskStatusCode == NAVIGATION_CODE_MAP_ID_MISMATCH ||
        taskStatusCode == NAVIGATION_CODE_COMMAND_FAILED ||
        taskStatusCode == NAVIGATION_CODE_RELOCALIZATION_ERROR_LARGE;
}

// 导航任务模块
void RobdogCenter::moveToTarget(const homi_speech_interface::msg::SIGCEvent::SharedPtr msg) {
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Prepare sent msg to nvidia.");

    std::string navCtrlMsgs = msg->event; // JSON字段
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "The msg will be sent to nvidia:%s ", navCtrlMsgs.c_str());

    Json::Reader reader;
    Json::Value value;
    if (!reader.parse(navCtrlMsgs, value)) {
        RCLCPP_ERROR(rclcpp::get_logger("robdog_control"), "Failed to parse JSON: %s", navCtrlMsgs.c_str());
        return;
    }
    sendCommandToUSLAM("navigation/start");

    nav_wait_start_time_ = std::chrono::steady_clock::now();
        nav_wait_timer_ = node_->create_wall_timer(
        std::chrono::milliseconds(100),
        [this, value]() {
            auto duration = std::chrono::steady_clock::now() - nav_wait_start_time_;
            if (Navigation_node) {
                const Json::Value& points = value["points"];
                if (!points.empty()) {
                    const auto& point = points[0];
                    double x = point["x"].asDouble();
                    double y = point["y"].asDouble();
                    double angle = point["angle"].asDouble();
                    sendPoint(x, y, angle);
                }
                // 只创建一次 move_point_timer_，并立即 cancel nav_wait_timer_
                nav_wait_timer_->cancel();
                move_point_timer_ = node_->create_wall_timer(
                    std::chrono::milliseconds(500),
                    [this]() {
                        Json::Value currentPose = RobotState::getInstance().getMoveTaskPose();
                        double map_x = currentPose["x"].asDouble();
                        double map_y = currentPose["y"].asDouble();
                        double yaw = currentPose["angle"].asDouble();
                        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "target x: %f, y: %f, theta: %f", map_x, map_y, yaw);
                        std::ostringstream oss;
                        oss << "navigation/set_goal_pose/" << map_x << "/" << map_y << "/" << yaw;
                        sendCommandToUSLAM(oss.str());
                        move_point_timer_->cancel();
                    });
            } else if (duration > nav_wait_timeout_) {
                nav_wait_timer_->cancel();
                RCLCPP_WARN(rclcpp::get_logger("robdog_control"), "Timeout waiting for Navigation_node to become true");
            }
        }
    );
}

// 园区巡逻
void RobdogCenter::moveToPatrolPoint(const homi_speech_interface::msg::SIGCEvent::SharedPtr msg) {
        moveToTarget(msg);
        // 创建一个定时器来检查目标状态
        std::string jsonStr = msg->event;
        Json::Reader reader;
        Json::Value jBody;
        if (reader.parse(jsonStr, jBody)) {
        // 解析成功，可以访问 jBody 中的数据
        // std::cout << "解析成功,jBody: " << jBody.toStyledString() << std::endl;
        auto callback = std::bind(&RobdogCenter::checkPatrolStatus, this, jBody);
        robPatrolStatusTimer_ = node_->create_wall_timer(std::chrono::seconds(1), [callback]() { callback(); });
    } else {
        // 解析失败，输出错误信息
        std::cerr << "解析JSON字符串失败: " << reader.getFormattedErrorMessages() << std::endl;
    } 
}

//充电
void RobdogCenter::moveToBatteryPoint(const homi_speech_interface::msg::SIGCEvent::SharedPtr msg) {
        homi_speech_interface::msg::RobdogAction zzmsg;
        zzmsg.actiontype = "NavCtrl";
        zzmsg.actionargument = "AutoMode";
        publishAction(std::make_shared<homi_speech_interface::msg::RobdogAction>(zzmsg));

        moveToTarget(msg);
        // 创建一个定时器来检查目标状态
        std::string jsonStr = msg->event;
        Json::Reader reader;
        Json::Value jBody;
        if (reader.parse(jsonStr, jBody)) {
        // 解析成功，可以访问 jBody 中的数据
        // std::cout << "解析成功,jBody: " << jBody.toStyledString() << std::endl;
        auto callback = std::bind(&RobdogCenter::checkBatteryStatus, this, jBody);
        robPatrolStatusTimer_ = node_->create_wall_timer(std::chrono::seconds(1), [callback]() { callback(); });
    } else {
        // 解析失败，输出错误信息
        std::cerr << "解析JSON字符串失败: " << reader.getFormattedErrorMessages() << std::endl;
    } 
}

void RobdogCenter::moveToHomePoint(const homi_speech_interface::msg::SIGCEvent::SharedPtr msg) {
    moveToTarget(msg);
    // 创建一个定时器来检查目标状态
    std::string jsonStr = msg->event;
    Json::Reader reader;
    Json::Value jBody;
    if (reader.parse(jsonStr, jBody)) {
    // 解析成功，可以访问 jBody 中的数据
    // std::cout << "解析成功,jBody: " << jBody.toStyledString() << std::endl;
    auto callback = std::bind(&RobdogCenter::checkGoHomeStatus, this, jBody);
    robPatrolStatusTimer_ = node_->create_wall_timer(std::chrono::seconds(1), [callback]() { callback(); });
    } else {
        // 解析失败，输出错误信息
        std::cerr << "解析JSON字符串失败: " << reader.getFormattedErrorMessages() << std::endl;
    } 
}

void RobdogCenter::moveTodeliverCakePoint(const homi_speech_interface::msg::SIGCEvent::SharedPtr msg) {
    moveToTarget(msg);
    // 创建一个定时器来检查目标状态
    std::string jsonStr = msg->event;
    Json::Reader reader;
    Json::Value jBody;
    if (reader.parse(jsonStr, jBody)) {
    // 解析成功，可以访问 jBody 中的数据
    // std::cout << "解析成功,jBody: " << jBody.toStyledString() << std::endl;
    auto callback = std::bind(&RobdogCenter::checkDeliverCakeStatus, this, jBody);
    robPatrolStatusTimer_ = node_->create_wall_timer(std::chrono::seconds(1), [callback]() { callback(); });
    } else {
        // 解析失败，输出错误信息
        std::cerr << "解析JSON字符串失败: " << reader.getFormattedErrorMessages() << std::endl;
    } 
}

void RobdogCenter::handleCancelMovement() {
    // 处理取消移动任务
    // ------------------------ WebSocket传输给感知主机 ------------------------
    sendCommandToUSLAM("navigation/stop");
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"),"the message of handleCancelMovement: %s", "navigation/stop");
}

// void RobdogCenter::navPositionCallback(const geometry_msgs::msg::Pose::SharedPtr pos) {
//     // std::string strMsg = msg->data;
//     Json::Value value;
//     value["x"] = pos->position.x;
//     value["y"] = pos->position.y;
//     // tf2::Quaternion quaternion;
//     // tf2::convert(pos.orientation, quaternion);
//     // 使用四元数构造函数来创建tf2::Quaternion
//     tf2::Quaternion quaternion(pos->orientation.x, pos->orientation.y,
//                                pos->orientation.z, pos->orientation.w);
//     double roll, pitch, yaw;
//     tf2::Matrix3x3(quaternion).getRPY(roll, pitch, yaw);
//     yaw = yaw * 180.0 / M_PI;
//     value["angle"] = yaw;
    
//     // RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Received robot position %s",value.toStyledString().c_str());

//     RobotState::getInstance().setCurRobotPose(value);
// }
// void RobdogCenter::navPositionCallback(const nav_msgs::msg::Odometry::SharedPtr odom) {
//     Json::Value value;
//     value["x"] = odom->pose.pose.position.x;
//     value["y"] = odom->pose.pose.position.y;
//     tf2::Quaternion quaternion(
//         odom->pose.pose.orientation.x,
//         odom->pose.pose.orientation.y,
//         odom->pose.pose.orientation.z,
//         odom->pose.pose.orientation.w
//     );
//     double roll, pitch, yaw;
//     tf2::Matrix3x3(quaternion).getRPY(roll, pitch, yaw);
//     yaw = yaw * 180.0 / M_PI;
//     value["angle"] = yaw;
//     RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Received robot position %s",value.toStyledString().c_str());

//     MapConfig config;
//     if (loadMapConfig("/root/tjc_test/map_server_ros2/scripts/tmp/unitree_files/map_display.txt", config)) {
//         // RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Width: %d, Height: %d", config.img_width, config.img_height);
//         // RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Resolution: %f", config.resolution);
//         // RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Origin X: %f, Origin Y: %f", config.origin_x, config.origin_y);
//     }
    
//     auto result = RobdogCenter::mapToPixel(value["x"].asDouble(), value["y"].asDouble(), yaw, config.origin_x,  config.origin_y, config.resolution, config.img_height);
//     double pixel_x = result[0], pixel_y = result[1], angle_deg = result[2];
//     RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Map pixel: (%f, %f), Angle: %f", pixel_x, pixel_y, angle_deg);
//     value["x"] = pixel_x;
//     value["y"] = pixel_y;
//     value["angle"] = angle_deg;
//     RobotState::getInstance().setCurRobotPose(value);
// }

int RobdogCenter::mapCodeToStatus(int code) {
    // 根据 code 映射到对应的 task_status
    switch (code) {
        case NAVIGATION_CODE_NAVIGATION_TASK_COMPLETED:
            return NAVIGATION_STATUS_MOVE_TASK_FINISHED;  // 导航任务完成
        case NAVIGATION_CODE_TARGET_UNREACHABLE:
            return NAVIGATION_STATUS_POINT_UNREACHABLE;  // 目标点不可达
        // case NAVIGATION_CODE_RELOCALIZATION_ERROR_LARGE: // 2207 重定位误差大
        //     return NAVIGATION_STATUS_POINT_UNREACHABLE;  // 目标点不可达
        case NAVIGATION_CODE_CANNOT_GO:
            return NAVIGATION_STATUS_CANNOTGO;  // 无法出发
        case NAVIGATION_CODE_NAVIGATION_TASK_CANCELED:
            return NAVIGATION_STATUS_MOVEMENT_CANCEL_SUCCESS;  // 导航任务已取消
        case NAVIGATION_CODE_SINGLE_POINT_NAVIGATION_COMPLETED:
            return NAVIGATION_STATUS_SINGLE_POINT_NAVIGATION_COMPLETED; // 单点导航任务已完成
        case NAVIGATION_CODE_TASK_FAILED:
            return NAVIGATION_STATUS_UNREACHABLE_DURING_MOVEMENT; // 任务失败
        default:
             // 处理未预期的代码值
            RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "unknown code is %d ",code);
            // return NAVIGATION_STATUS_POINT_UNREACHABLE;  // 其他情况默认为目标点不可达
            return NAVIGATION_STATUS_OUT_OF_RANGE; // 其他情况，默认不可达会导致APP端一直报错
    }
}

std::string RobdogCenter::getCodeName(int code) {
    auto it = navigationCodeDescriptions.find(code);
    if (it != navigationCodeDescriptions.end()) {
        return std::get<0>(it->second);
    }
    return "未知状态码";
}

std::string RobdogCenter::getDescription(int code) {
    auto it = navigationCodeDescriptions.find(code);
    if (it != navigationCodeDescriptions.end()) {
        return std::get<1>(it->second);
    }
    return "未知状态码";
}

// 0-停止跟随 1-开启跟随 2-开启UWB跟随
void RobdogCenter::actionFollow(int status) {
    string strAction = 0 == status ? "endFollow"
                     : 1 == status ? "startFollow"
                     : 2 == status ? "startUwbSummon" : "endFollow";
    Json::Value value;
    Json::Value params;
    value["client_type"] = CLIENT_LAUNCHER;
    value["target_client"] = CLIENT_NVIDIA;
    value["action"] = strAction;
    // SendtoNvOrin(value.toStyledString().c_str(), nConnectIndex_);
    auto msg = std::make_shared<std_msgs::msg::String>();
    msg->data = value.toStyledString();
    try {
        follow_control_pub_->publish(*msg);
        RCLCPP_DEBUG(rclcpp::get_logger("FollowMe"), 
                    "Published follow control: %s", value.toStyledString().c_str());
    } catch (const std::exception& e) {
        RCLCPP_ERROR(rclcpp::get_logger("FollowMe"),
                    "Failed to publish follow control: %s", e.what());
    }
}

// ----------------------------------------- 和定点移动有关的 ----------------------------------------------
bool RobdogCenter::isAtTarget(const homi_speech_interface::msg::SIGCEvent::SharedPtr msg) {
    std::string strMsg = msg->event; // JSON字段
    Json::Reader reader;
    Json::Value value;
    reader.parse(strMsg, value);
    Json::Value jBody = value["body"];
    // 获取当前机器人位置
    Json::Value currentPose = RobotState::getInstance().getCurRobotPose();
    double curX = currentPose["x"].asDouble();
    double curY = currentPose["y"].asDouble();
    double curAngle = currentPose["angle"].asDouble();
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "current pos x: %f, y: %f, theta: %f", curX, curY, curAngle);

    // 目标位置
    double targetX = 0.0;
    double targetY = 0.0;
    double targetTheta = 0.0;

    if (!jBody["points"].isNull()){
        Json::Value arrayPoints = jBody["points"];
        if (arrayPoints.isArray()) {
            for (auto it : arrayPoints) {
                Json::Value jPoint = it;
                targetX = jPoint["x"].asDouble();
                targetY = jPoint["y"].asDouble();
                targetTheta = jPoint["angle"].asDouble();
            }
        }
    }
    // 智能播报给的location
    else if (!jBody["remindLocation"].isNull()){
        Json::Value jPoint = jBody["remindLocation"];
        targetX = jPoint["x"].asDouble();
        targetY = jPoint["y"].asDouble();
        targetTheta = jPoint["angle"].asDouble();
    }
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "target pos x: %f, y: %f, theta: %f", targetX, targetY, targetTheta);
    
    // RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "planning move pos x: %f, y: %f, theta: %f", targetX, targetY, targetTheta);
    // 比较当前位置与目标位置
    return (fabs(targetX - curX) < 0.01 && fabs(targetY - curY) < 0.01 &&
            fabs(targetTheta - curAngle) < 0.01);
}

void RobdogCenter::playAudio(const std::string &filePath) {
    if(node_) {
        node_->playAudio(filePath);
    }
}

void RobdogCenter::checkTargetStatus() {
    // 如果到达目标
    if (at_target_) {
        std::thread t_audio(&RobdogCtrlNode::playAudio,node_,node_->getResourcePath("audio/nav/到位打开氛围灯.wav"));
        t_audio.detach();
        // 调用iot控制打开氛围灯
        iot_control_client_ = node_->create_client<homi_speech_interface::srv::IotControl>(
            "/homi_speech/speech_iot_control_service");
        auto request_iot_control = std::make_shared<homi_speech_interface::srv::IotControl::Request>();
        auto ret = iot_control_client_->wait_for_service(std::chrono::seconds(1));
        if(ret==false)
        {
            RCLCPP_WARN(rclcpp::get_logger("robdog_control"),"Failed to waitForExistence  Iot control service assistant");
        }
        request_iot_control->param = "{\"outletStatus\":1}";
        auto result = iot_control_client_->async_send_request(request_iot_control);   


        // 调用语音助手拍照
        take_photo_client_ = node_->create_client<homi_speech_interface::srv::AssistantTakePhoto>(
            "/homi_speech/helper_assistant_takephoto_service");
        auto request_take_photo = std::make_shared<homi_speech_interface::srv::AssistantTakePhoto::Request>();
        auto ret_1 = take_photo_client_->wait_for_service(std::chrono::seconds(1));
        if(ret_1==false)
        {
            RCLCPP_WARN(rclcpp::get_logger("robdog_control"),"Failed to waitForExistence take photo service assistant");
        }
        auto result_1 = take_photo_client_->async_send_request(request_take_photo);  

        at_target_ = false;
        // 停止定时器
        robMoveStatusTimer_->cancel(); 
    }
}

// 园区巡逻相关
void RobdogCenter::checkPatrolStatus(const Json::Value &jBody) {
    homi_speech_interface::msg::RobdogAction msg;
    if (atSinglePoint) {
        if (jBody.isMember("points")) {
        const Json::Value& points = jBody["points"];
            for (const auto& point : points) {
                if (point.isMember("uid") && point["uid"].asString() == uid) {
                    if (point.isMember("actions")) {
                        const Json::Value& actions = point["actions"];
                        for (const auto& action : actions) {
                            int actionValue = action["action"].asInt();
                            std::string angle = action["angle"].asString();
                            if (actionValue >= 1 && actionValue <= static_cast<int>(actionVector.size())) {
                                Json::Value reqValue;
                                reqValue["client_type"] = CLIENT_LAUNCHER;
                                reqValue["target_client"] = CLIENT_NVIDIA;         
                                reqValue["action"] = "navigation_control";

                                // 暂停导航
                                Json::Value additionalParams;
                                additionalParams["action"] = 13; // 暂停导航
                                additionalParams["mapId"] = RobotState::getInstance().getMapId(); //  导航功能使用的地图
                                additionalParams["batchId"] = RobotState::getInstance().getbatchId(); // "1234"; // 多点导航任务的id(就是batchid)
                                reqValue["params"] = additionalParams;
                                // RCLCPP_INFO(rclcpp::get_logger("robdog_control"),"The message of checkPatrolStatus: %s", reqValue.toStyledString().c_str());
                                SendtoNvOrin(reqValue.toStyledString().c_str(), nConnectIndex_);

                                // 先旋转
                                msg.actiontype = "rotateCtl";
                                msg.actionargument = angle;
                                std::cout << "angle: " << msg.actionargument << std::endl;
                                publishAction(std::make_shared<homi_speech_interface::msg::RobdogAction>(msg));
                                // 暂停时间stayDuration，等待动作完成
                                int stayDuration = point["stayDuration"].asInt();
                                std::this_thread::sleep_for(std::chrono::seconds(stayDuration));
                                // 再执行动作
                                msg.actiontype = "motorSkill";
                                msg.actionargument = actionVector[actionValue - 1];
                                std::cout << "Action: " << msg.actionargument << std::endl;
                                publishAction(std::make_shared<homi_speech_interface::msg::RobdogAction>(msg));
                                // 暂停时间stayDuration，等待动作完成
                                std::this_thread::sleep_for(std::chrono::seconds(stayDuration));
                                // 继续导航
                                additionalParams["action"] = 14; 
                                reqValue["params"] = additionalParams;
                                // RCLCPP_INFO(rclcpp::get_logger("robdog_control"),"The message of checkPatrolStatus: %s", reqValue.toStyledString().c_str());
                                SendtoNvOrin(reqValue.toStyledString().c_str(), nConnectIndex_);
                            } else {
                                msg.actionargument = "unknown";
                            }
                        }
                    }
                }
            }
        }
        atSinglePoint = false;
       
    }
     // 如果整个导航任务完成，则取消定时器
    if(at_target_){
        // 停止定时器
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "导航任务完成，取消定时器");
        robPatrolStatusTimer_->cancel(); 
        at_target_ = false; // 一次导航任务完成
    }
}

// 充电
void RobdogCenter::checkBatteryStatus(const Json::Value &jBody) {
    if(at_target_){
        //已到达充电导航点，下发开始充电指令
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "arrived at target, start charging");
        // Json::Value points;
        // std::string ChargeNavPoints = ReadMapCfg::getInstance().getChargeNavPoints();
        // Json::Reader reader;
        // reader.parse(ChargeNavPoints, points);
        // std::string current_mapid = RobotState::getInstance().getMapId();
        // sendChargeAction(3,current_mapid,points);
        sendCommandToUSLAM("autocharge/start");
        robPatrolStatusTimer_->cancel(); 
        at_target_ = false; 
    }
} 

void runCommand(const std::string& command) {  
    system(command.c_str());  
} 

void playAudioAsync(RobdogCenter* thiz, const std::string audiofile) {  
    thiz->playAudio(audiofile);
} 

void RobdogCenter::checkGoHomeStatus(const Json::Value &jBody) {
    if(at_target_){
        // 切表情
        std::string video_path=RobotState::getInstance().getResourcePath("video/眨眼+开心+星星眼.mp4");
        ExpressionChange::getInstance().async_callback_work(video_path,1);
        std::this_thread::sleep_for(std::chrono::seconds(3));

        // // 做动作
        // homi_speech_interface::msg::RobdogAction msg;
        // msg.actiontype = "motorSkill";
        // msg.actionargument = "twistBody";
        // std::cout << "Action: " << msg.actionargument << std::endl;
        // publishAction(std::make_shared<homi_speech_interface::msg::RobdogAction>(msg));

        // 播放音频
        std::string file_path = node_->getResourcePath("audio/nav/gohome.wav");
        std::string command = "aplay \"" + file_path + "\"";
        //std::thread t(runCommand, command); 
        std::thread t(playAudioAsync,this,file_path);
		t.detach(); 

        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "GoHome任务完成，取消定时器");
        robPatrolStatusTimer_->cancel(); 
        at_target_ = false; 
    }
}

void RobdogCenter::checkDeliverCakeStatus(const Json::Value &jBody) {
    if(at_target_){
        homi_speech_interface::msg::RobdogAction msg;
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "DeliverCake任务完成，取消定时器");
        robPatrolStatusTimer_->cancel(); 
        at_target_ = false; 
        // msg.actiontype = "motorSkill";
        // msg.actionargument = "greeting";
        // std::cout << "Action: " << msg.actionargument << std::endl;
        // publishAction(std::make_shared<homi_speech_interface::msg::RobdogAction>(msg));
        // std::string file_path = "/home/<USER>/resource/audio/gohome.wav";
        // playAudio(file_path);
        // // 暂停时间stayDuration，等待动作完成
        // std::this_thread::sleep_for(std::chrono::seconds(15));

        // msg.actiontype = "motorSkill";
        // msg.actionargument = "twistBody";
        // std::cout << "Action: " << msg.actionargument << std::endl;
        // publishAction(std::make_shared<homi_speech_interface::msg::RobdogAction>(msg));
        // std::string file_path = "/home/<USER>/resource/audio/sofa.wav";
        // playAudio(file_path);
        // // 暂停时间stayDuration，等待动作完成
        // std::this_thread::sleep_for(std::chrono::seconds(15));
        
    }
}

 void RobdogCenter::moveToTargetAndPlayAudio(const homi_speech_interface::msg::SIGCEvent::SharedPtr msg) {
    // 如果已在指定地点，则直接播放音频
    if (isAtTarget(msg)) {
        std::thread t_audio(&RobdogCtrlNode::playAudio,node_,node_->getResourcePath("audio/nav/1312直接打开氛围灯.wav"));
        t_audio.detach();

        
        // rclcpp::TimerBase::SharedPtr robMoveStatusTimer_;

        iot_control_client_ = node_->create_client<homi_speech_interface::srv::IotControl>("/homi_speech/speech_iot_control_service");
        take_photo_client_ = node_->create_client<homi_speech_interface::srv::AssistantTakePhoto>("/homi_speech/helper_assistant_takephoto_service");


        // 调用iot控制打开氛围灯
        auto request = std::make_shared<homi_speech_interface::srv::IotControl::Request>();
        auto ret = iot_control_client_->wait_for_service(std::chrono::seconds(1));
        if(ret==false)
        {
            RCLCPP_WARN(rclcpp::get_logger("robdog_control"),"Failed to waitForExistence service assistant");
        }
        request->param = "{\"outletStatus\":1}";
        auto result = iot_control_client_->async_send_request(request);   

        // 第四步，调用语音助手
        auto request_1 = std::make_shared<homi_speech_interface::srv::AssistantTakePhoto::Request>();
        auto ret_1 = take_photo_client_->wait_for_service(std::chrono::seconds(1));
        if(ret_1==false)
        {
            RCLCPP_WARN(rclcpp::get_logger("robdog_control"),"Failed to waitForExistence service assistant");
        }
        auto result_1 = take_photo_client_->async_send_request(request_1);   

    } else {
        moveToTarget(msg);
        std::thread t_audio(&RobdogCtrlNode::playAudio,node_,node_->getResourcePath("audio/nav/去玄关.wav"));
        t_audio.detach();
        // 创建一个定时器来检查目标状态
        robMoveStatusTimer_ = node_->create_wall_timer(std::chrono::seconds(1), std::bind(&RobdogCenter::checkTargetStatus, this));
    }
}

// 和拍照有关
 void RobdogCenter::callHelperPhoto() {
    rclcpp::Client<homi_speech_interface::srv::AssistantTakePhoto>::SharedPtr assistant_take_photo_client_;
    assistant_take_photo_client_ = node_->create_client<homi_speech_interface::srv::AssistantTakePhoto>("/homi_speech/helper_assistant_takephoto_service");
    if (!assistant_take_photo_client_->wait_for_service(std::chrono::seconds(1))) {
        RCLCPP_ERROR(rclcpp::get_logger("robdog_control"), "Service not available after waiting");
        return;
    }

    auto request = std::make_shared<homi_speech_interface::srv::AssistantTakePhoto::Request>();
    // 这里可以根据需要设置请求参数
    auto ret = assistant_take_photo_client_->wait_for_service(std::chrono::seconds(1));
    if(ret==false)
    {
        RCLCPP_WARN(rclcpp::get_logger("robdog_control"),"Failed to waitForExistence service assistant");
    }
    auto result = assistant_take_photo_client_->async_send_request(request, std::bind(&RobdogCenter::takephoto_srv_callback, this, std::placeholders::_1));   
}

void RobdogCenter::takePhotoService() {
    rclcpp::Publisher<std_msgs::msg::String>::SharedPtr pub_;
    pub_ = node_->create_publisher<std_msgs::msg::String>("/take_photo", 10);
    std_msgs::msg::String msg;
    msg.data = "take_photo";
    pub_->publish(msg);

    // 输出日志
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Published message: [%s]", msg.data.c_str());

    /*
    // 如果需要打开氛围灯，可以在这里添加相关代码
    if (!iot_control_client_->wait_for_service(std::chrono::seconds(1))) {
        RCLCPP_ERROR(rclcpp::get_logger("robdog_control"), "IotControl service not available");
        return;
    }
    
    auto request1 = std::make_shared<homi_speech_interface::srv::IotControl::Request>();
    request1->param = "{\"outletStatus\":0}";

    auto future1 = iot_control_client_->async_send_request(request1);
    if (rclcpp::spin_until_future_complete(node_->get_node_base_interface(), future1) == rclcpp::FutureReturnCode::SUCCESS) {
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "IotControl response: %d", future1.get()->errorCode);
    } else {
        RCLCPP_ERROR(rclcpp::get_logger("robdog_control"), "Failed to call IotControl service");
    }
    */
}


// ************************************************* 智能播报相关 ***************************************************************
 void RobdogCenter::moveToTargetAndBrocast(const homi_speech_interface::msg::SIGCEvent::SharedPtr msg) {
    // 如果已在指定地点，则直接开始播报
    if (isAtTarget(msg)) {
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Start broadcasting!!");
        timer_brocast->cancel();
        brocast_send_count_ = 0;
        timer_brocast->reset();
        SendBrocastCallback();
    } else {
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Start MovetoTarget!!");
        // 创建一个用于发送给感知主机的msg（用于多点导航）
        auto msg_to_nvidia =  std::make_shared<homi_speech_interface::msg::SIGCEvent>();
        std::string data_string = msg->event;
        Json::Value inputRoot = parseJson(data_string);
        Json::Value outputRoot;

        // 添加 points 数组
        Json::Value pointsArray(Json::arrayValue);
        Json::Value point;
        
        // 这里获取 "remindLocation" 中的数据，并转换到 points 数组
        Json::Value remindLocation = inputRoot["body"]["remindLocation"];
        point["x"] = remindLocation["x"].asDouble();
        point["y"] = remindLocation["y"].asDouble();
        point["angle"] = remindLocation["angle"].asDouble();
        point["option"] = "even_low_speed";  // 根据需求填写
        point["uid"] = "1";  // 这里可以根据实际情况调整
        
        pointsArray.append(point);
        outputRoot["points"] = pointsArray;

        // 把json转为string
        Json::StreamWriterBuilder writer;
        std::string jsonString = Json::writeString(writer, outputRoot);
        msg_to_nvidia->event = jsonString;  
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "The message of moveToTarget is: %s", jsonString.c_str());

        moveToTarget(msg_to_nvidia);
        // 创建一个定时器来检查目标状态
        robMoveStatusTimer_brocast = node_->create_wall_timer(std::chrono::seconds(1), std::bind(&RobdogCenter::checkTargetStatus_brocast, this));
    }
}

void RobdogCenter::checkTargetStatus_brocast() {
    // 如果到达目标
    if (at_target_) {
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Arrive target point and start broadcasting!!");
        timer_brocast->cancel();
        brocast_send_count_ = 0;
        timer_brocast->reset();
        SendBrocastCallback();

        at_target_ = false; // 一次导航任务完成
        // 停止定时器
        robMoveStatusTimer_brocast->cancel(); 
    }
    // 如果导航任务取消了就关闭检查的定时器
    else if(RobotState::getInstance().getCurrentState() != RobotStateEnum::NAVIGATION){
        robMoveStatusTimer_brocast->cancel(); 
    }
}

// 接受语音助手上报的信息（是否终止）
void RobdogCenter::BrocastIfAbortCallBack(const homi_speech_interface::msg::AssistantEvent::SharedPtr msg) {
    std::string sectionId_past = RobotState::getInstance().getSectionId(); // 语音助手之前上报的sectionId
    std::string sectionId_cur = msg->section_id;
    std::string aborting = msg->description;
    // RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "aborting: %s, sectionId_past: %s, sectionId_cur: %s",
    // aborting.c_str(), sectionId_past.c_str(), sectionId_cur.c_str());
    // if(sectionId_past == sectionId_cur)
    // RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "6666666666666666666666666666666666666666");
    if (sectionId_past == sectionId_cur && aborting == "UserAbort") {   // 被语音词唤醒
        RobotBroadcastStatusToPlat(0); // 被打断
        timer_brocast->cancel();
        brocast_send_count_ = 0;
        // RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "interrupted by the wake word!!!");
    }
}

// 向平台上传智能播报状态，被唤醒词打断了需上报
void RobdogCenter::RobotBroadcastStatusToPlat(int status) {
    // 构建状态报告的 JSON
    Json::Value response;

    response["deviceId"] = RobotState::getInstance().getDeviceId();
    response["domain"] = "ROBOT_BUSINESS_DEVICE";        // 域
    response["event"] = "broadcast_report";              // 事件类型
    response["eventId"] = "111111111";                   // 事件 ID
    response["seq"] = std::to_string(base::homiUtils::getCurrentTimeStamp()); // 时间戳作为序列号

    // 将状态写入 JSON
    response["body"]["status"] = status; // 状态字段：0-打断，1-正常运行
    long id = RobotState::getInstance().getRemindId();
    response["body"]["remindId"] = Json::Int64(id); // 设备 ID【直接读取】

    // 将 JSON 转换为字符串
    Json::StreamWriterBuilder writerBuilder;
    std::string jsonString = Json::writeString(writerBuilder, response);

    // 打印信息
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Send res to plat, res is %s", jsonString.c_str());

    // 调用服务并处理响应
    sendRequestData(jsonString);  
}

// 定时发送播报文本
void RobdogCenter::SendBrocastCallback() {
    // RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Brocast times: %d, Total Count_: %d", brocast_send_count_, brocast_total_count_);
    if (brocast_send_count_ < brocast_total_count_) { // 人为定义发送次数
        sendStringToBrocast(brocast_text); // 向语音助手发送播报文本
        // RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Start Brocasting: %s.", brocast_text);
        ++brocast_send_count_; // 增加了定时器次数
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Start Brocasting: %s, Pub times: %d, Total Count_: %d", brocast_text, brocast_send_count_, brocast_total_count_);
    } else {
        // 完成发送后停止定时器
        timer_brocast->cancel();
        brocast_send_count_ = 0;
    }
}

// 调用服务向语音助手发送智能播报文本（收到的sectionId要保存起来）
void RobdogCenter::sendStringToBrocast(const std::string &message) {
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Sending to brocast: %s", message.c_str()); // 播报文本
   
    // 创建请求消息
    auto request = std::make_shared<homi_speech_interface::srv::AssistantSpeechText::Request>();
    request->msg = message;

    // 调用服务并处理响应
    if (!brocast_client->wait_for_service(std::chrono::seconds(1))) {
        RCLCPP_ERROR(rclcpp::get_logger("robdog_control"), "Service not available after waiting");
        return;
    }

    auto ret = brocast_client->wait_for_service(std::chrono::seconds(1));
    if(ret==false)
    {
        RCLCPP_WARN(rclcpp::get_logger("robdog_control"),"Failed to waitForExistence service assistant");
    }
    auto result = brocast_client->async_send_request(request, std::bind(&RobdogCenter::brocast_srv_callback, this, std::placeholders::_1));   

}

// void RobdogCenter::checkNvidia_srv_status(bool openNavigationIfClosed, std::string mapId) {
//     RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Starting checkNvidia_srv_status at %ld", base::homiUtils::getCurrentTimeStamp());
//     RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "unitreeMappingStatus %d unitreeNavigationStatus %d ",unitreeMappingStatus,unitreeNavigationStatus);
//     // 提取各个算法的状态信息
//     bool mapping_active = unitreeMappingStatus;
//     bool navigation_active = unitreeNavigationStatus;
//     // bool charge_active = "";
//     std::string current_mapid = unitreeUsingMapId;

//     // 并行记录日志
//     RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "MappingAct: %d, NavigationAct: %d, mapId:%s",mapping_active,navigation_active,current_mapid.c_str());

//     // NORMAL&MAPPING状态：如果导航开着需要关闭，如果导航关闭不需要做任何处理；重定位指令：如果导航开着需要判断 mapId 与下发的 mapId 是否一致，如果一致不需要任何处理，如果不一致需要先关闭原来的导航，再开启 mapId 的导航，再下发重定位。
//     if (navigation_active) {
//         Navigation_node = true;
//         if (openNavigationIfClosed) {
//             // 收到重定位指令
//             if (current_mapid != mapId) {
//                 RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Navigation is active with different Map ID, closing navigation with Map ID: %s", current_mapid.c_str());
//                 // sendNavigationAction(0, current_mapid);
//             } else {
//                 RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Navigation is active with the same Map ID, no action needed.");
//                 // sendNavigationAction(11, mapId);//导航已经开了，直接发送重定位指令
//                 sendCommandToUSLAM("localization/set_initial_pose/0/0/0"); 
//             }
//         } else {
//             // NORMAL&MAPPING状态，直接关闭
//             RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Navigation is active, closing navigation with Map ID: %s", current_mapid.c_str());
//             sendCommandToUSLAM("localization/stop");
//             // sendCommandToUSLAM("navigation/stop");//暂不确定是哪一个
//         }
//     } else if (openNavigationIfClosed) {
//         RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Navigation is not active, opening navigation with Plat_Map ID: %s", mapId.c_str());
//         sendNavigationAction(1, mapId);
//     } 
//     else {
//         Navigation_node = false;
//         RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "navigation service is not running");
//     }
//     if (mapping_active) {
//         RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Mapping is active, closing Mapping with Map ID: %s", current_mapid.c_str());
//         sendMapAction("cancel");
//     }
// }
void RobdogCenter::checkNvidia_srv_status(bool openNavigationIfClosed, std::string mapId) {
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Starting checkNvidia_srv_status at %ld", base::homiUtils::getCurrentTimeStamp());
    // 提取各个算法的状态信息
    bool mapping_active = (unitreeMappingStatus == 1);
    bool navigation_active = (unitreeNavigationStatus == 1);
    // bool charge_active = "";
    std::string current_mapid = unitreeUsingMapId;

    // 并行记录日志
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "MappingAct: %d, NavigationAct: %d, mapId:%s",mapping_active,navigation_active,current_mapid.c_str());

    // NORMAL&MAPPING状态：如果导航开着需要关闭，如果导航关闭不需要做任何处理；重定位指令：如果导航开着需要判断 mapId 与下发的 mapId 是否一致，如果一致不需要任何处理，如果不一致需要先关闭原来的导航，再开启 mapId 的导航，再下发重定位。
    
        // Navigation_node = false;
}

// 调用服务查询算法服务状态
void RobdogCenter::checkNvidiaServiceStatus(bool openNavigationIfClosed,std::string mapId) {
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Starting checkNvidiaServiceStatus at %ld", base::homiUtils::getCurrentTimeStamp());

    unitreeMappingStatus = -1;
    unitreeNavigationStatus = -1;
    unitreeLocalizationStatus = -1;
    unitreeUsingMapId = "";
    std_msgs::msg::String msg;

    const std::vector<std::string> commands = {
        "mapping/stop",
        "navigation/stop",
        "localization/stop"
    };
    for (const auto& cmd : commands) {
        sendCommandToUSLAM(cmd);
        std::this_thread::sleep_for(std::chrono::milliseconds(50));  // 等待 2 秒
    }

    const std::chrono::seconds total_timeout(3);
    const std::chrono::milliseconds sleep_time(100);
    auto start_time = std::chrono::steady_clock::now();
    // while (rclcpp::ok()) {
    //     if (std::chrono::duration_cast<std::chrono::seconds>(std::chrono::steady_clock::now() - start_time) >= total_timeout) {
    //         RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Timeout waiting for status updates.");
    //         break;
    //     }
    //     RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "unitreeMappingStatus  %d, unitreeNavigationStatus %d, unitreeLocalizationStatus %d, unitreeUsingMapId %s",unitreeMappingStatus,unitreeNavigationStatus,unitreeLocalizationStatus,unitreeUsingMapId.c_str());
    //     if (unitreeMappingStatus != -1 && unitreeNavigationStatus != -1 && unitreeLocalizationStatus != -1 && unitreeUsingMapId != "") {
    //         break;
    //     }
	// std::this_thread::sleep_for(sleep_time);
    // }

    checkNvidia_srv_status(openNavigationIfClosed, mapId);
}

// ****************************** 和平台消息有关的处理 ******************************************
// ****************** event是robot_action但是没有actiontype **************************
void RobdogCenter::handleTakePhotos() {
    ReadMapCfg::getInstance().loadConfig(map_points_path);
    homi_speech_interface::msg::SIGCEvent photo_msg;
    photo_msg.event = ReadMapCfg::getInstance().getPhonePoints();
    if(photo_msg.event.empty()) {
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "get PHOTO_POINT error");
        return;
    } else {
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "get PHOTO_POINT, %s", photo_msg.event.c_str());
    }
    moveToTargetAndPlayAudio(std::make_shared<homi_speech_interface::msg::SIGCEvent>(photo_msg));
}

void RobdogCenter::handleDeliverExpress() {
    // 取快递
    ReadMapCfg::getInstance().loadConfig(map_points_path);
    std::thread t_audio(&RobdogCtrlNode::playAudio,node_,node_->getResourcePath("audio/nav/出发去快递站.wav"));
    t_audio.detach();
    homi_speech_interface::msg::SIGCEvent press_msg;

    press_msg.event = ReadMapCfg::getInstance().getPressPoints();
    if (press_msg.event.empty()) {
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "get PRESS_POINT error");
        return;
    } else {
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "get PRESS_POINT, %s", press_msg.event.c_str());
    }
    moveToTarget(std::make_shared<homi_speech_interface::msg::SIGCEvent>(press_msg));  
}

void RobdogCenter::handleFetchExpress() {
    // 取快递
    ReadMapCfg::getInstance().loadConfig(map_points_path);
    std::thread t_audio(&RobdogCtrlNode::playAudio,node_,node_->getResourcePath("audio/nav/fetchExpress.wav"));
    t_audio.detach();
    homi_speech_interface::msg::SIGCEvent press_msg;
    press_msg.event = ReadMapCfg::getInstance().getPressPoints();
    if (press_msg.event.empty()) {
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "get PRESS_POINT error");
    } else {
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "get PRESS_POINT, %s", press_msg.event.c_str());
    }
    moveToTarget(std::make_shared<homi_speech_interface::msg::SIGCEvent>(press_msg));

}

void RobdogCenter::handleReportFamilyMovePoint() {
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "----------------------- handleReportFamilyMovePoint ----------------------- ");
    robPathStatusTimer_ = node_->create_wall_timer(std::chrono::seconds(1), 
            std::bind(&RobdogCenter::timerRobotPathCallback, this)); // 一边上报路径信息
    // 一边执行定点移动任务【】
    readMappingPoints();
    homi_speech_interface::msg::SIGCEvent familyMove_msg;
    familyMove_msg.event = ReadMapCfg::getInstance().getFamilyMovePoints();
    if(familyMove_msg.event.empty()) {
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "get PRESS_POINT error");
    } else {
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "get familyMove_POINT, %s", familyMove_msg.event.c_str());
    }
    moveToPatrolPoint(std::make_shared<homi_speech_interface::msg::SIGCEvent>(familyMove_msg));
}

void RobdogCenter::handleBatteryChargingPoint() {
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "----------------------- handleBatteryChargingPoint ----------------------- ");
    robPathStatusTimer_ = node_->create_wall_timer(std::chrono::seconds(1), 
            std::bind(&RobdogCenter::timerRobotPathCallback, this)); // 一边上报路径信息
    readMappingPoints();
    homi_speech_interface::msg::SIGCEvent batteryCharging_msg;
    batteryCharging_msg.event = ReadMapCfg::getInstance().getBatteryChargingPoints();
    if(batteryCharging_msg.event.empty()) {
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "get PRESS_POINT error");
    } else {
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "get batteryCharging_POINT, %s", batteryCharging_msg.event.c_str());
    }
    moveToBatteryPoint(std::make_shared<homi_speech_interface::msg::SIGCEvent>(batteryCharging_msg));
}
void RobdogCenter::handleParkPatrol() {
    // 处理巡逻任务
    readMappingPoints();
    homi_speech_interface::msg::SIGCEvent patrol_msg;
    patrol_msg.event = ReadMapCfg::getInstance().getPatrolPoints();
    if(patrol_msg.event.empty()) {
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "get PRESS_POINT error");
    } else {
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "get PATROL_POINT, %s", patrol_msg.event.c_str());
    }
    // homi_speech_interface::msg::SIGCEvent patrol_msg;
    // Json::StreamWriterBuilder writerBuilder;
    // std::string moveMsgs = Json::writeString(writerBuilder, jBody);
    // patrol_msg.event = moveMsgs;
    moveToPatrolPoint(std::make_shared<homi_speech_interface::msg::SIGCEvent>(patrol_msg));
}

void RobdogCenter::handlegoHome() {
    // 处理巡逻任务
    readMappingPoints();
    homi_speech_interface::msg::SIGCEvent goHome_msg;
    goHome_msg.event = ReadMapCfg::getInstance().getGoHomePoints();
    if(goHome_msg.event.empty()) {
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "get goHome_point error");
    } else {
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "get goHome_point, %s", goHome_msg.event.c_str());
    }
    // homi_speech_interface::msg::SIGCEvent goHome_msg;
    // Json::StreamWriterBuilder writerBuilder;
    // std::string moveMsgs = Json::writeString(writerBuilder, jBody);
    // goHome_msg.event = moveMsgs;
    moveToHomePoint(std::make_shared<homi_speech_interface::msg::SIGCEvent>(goHome_msg));
}

void RobdogCenter::handledeliverCake() {
    // 处理巡逻任务
    readMappingPoints();
    homi_speech_interface::msg::SIGCEvent deliverCake_msg;
    deliverCake_msg.event = ReadMapCfg::getInstance().getDeliverCakePoints();
    if(deliverCake_msg.event.empty()) {
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "get deliverCake_point error");
    } else {
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "get deliverCake_POINT, %s", deliverCake_msg.event.c_str());
    }
    // homi_speech_interface::msg::SIGCEvent deliverCake_msg;
    // Json::StreamWriterBuilder writerBuilder;
    // std::string moveMsgs = Json::writeString(writerBuilder, jBody);
    // deliverCake_msg.event = moveMsgs;
    moveTodeliverCakePoint(std::make_shared<homi_speech_interface::msg::SIGCEvent>(deliverCake_msg));
 }

// ****************** event是robot_action但是有actiontype **************************
void RobdogCenter::handleFollowMe(const Json::Value &jBody) {
    string actionArgument = "";
    const std::vector<std::string> MOVEMENT_COMMANDS = {"comeClose", "goFar", "goLeft", "goRight", "comeHere"};
    if (!jBody["actionArguement"].isNull()) {
        actionArgument = jBody["actionArguement"].asString();
    } else if (!jBody["actionArgument"].isNull()) {
        actionArgument = jBody["actionArgument"].asString();
    }
    if(RobotState::getInstance().getFollowMeStatus()=="on"&&
        std::find(MOVEMENT_COMMANDS.begin(), MOVEMENT_COMMANDS.end(), actionArgument) != MOVEMENT_COMMANDS.end())
    {
        auto message = std_msgs::msg::String();
        message.data = actionArgument;
        adjust_distance_publisher_->publish(message);
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Sent movement command '%s' to /adjust_distance topic", actionArgument.c_str());
    }
    else if (RobotState::getInstance().getFollowMeStatus()=="off"&&
        std::find(MOVEMENT_COMMANDS.begin(), MOVEMENT_COMMANDS.end(), actionArgument) != MOVEMENT_COMMANDS.end()){
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "The robot dog is on a fixed-point summoning mission.");
        homi_speech_interface::msg::RobdogAction msg;
        msg.actiontype = "followMe";
        msg.actionargument = "comeHere";
        publishAction(std::make_shared<homi_speech_interface::msg::RobdogAction>(msg));
        actionFollow(2);
    }
    // 开启跟随
    if (actionArgument == "on") {
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Robot is following the user.");
        homi_speech_interface::msg::RobdogAction msg;
        msg.actiontype = "followMe";
        msg.actionargument = "on";
        publishAction(std::make_shared<homi_speech_interface::msg::RobdogAction>(msg));
        actionFollow(1);
        RobotState::getInstance().setFollowMeStatus("on");
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "RobotState::getInstance().getFollowMeStatus is %s",RobotState::getInstance().getFollowMeStatus().c_str());
        RobotState::getInstance().setCurrentState(RobotStateEnum::FOLLOWME); 
    }
    // 关闭跟随
    else if (actionArgument == "off") {
        RCLCPP_INFO_STREAM(rclcpp::get_logger("robdog_control"), "Robot is not following the user.");
        actionFollow(0);
        RobotState::getInstance().setFollowMeStatus("off");
        RobotState::getInstance().setCurrentState(RobotStateEnum::NORMAL); 
    } 
    if(actionArgument =="on"||actionArgument =="off"){
        Json::Value response;
        response["deviceId"] = RobotState::getInstance().getDeviceId();
        response["domain"] = "BUSINESS_REPORT";
        response["event"] = "data_report";
        response["eventId"] = to_string(std::chrono::duration_cast<std::chrono::seconds>(std::chrono::system_clock::now().time_since_epoch()).count());
        response["seq"] = to_string(std::chrono::duration_cast<std::chrono::seconds>(std::chrono::system_clock::now().time_since_epoch()).count());
        response["body"]["type"] = "followMe";
        response["body"]["data"]["status"]=RobotState::getInstance().getFollowMeStatus();
        response["body"]["data"]["code"]=0;
        response["body"]["data"]["isFirstTime"]=false;
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "RobotState::getInstance().getFollowMeStatus is %s",RobotState::getInstance().getFollowMeStatus().c_str());
        Json::FastWriter writer;
        std::string jsonString = writer.write(response);
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"),"Follow me report msg is : %s", jsonString.c_str());
        sendRequestData(jsonString);
    }
}

// 处理手势识别任务【目前平台只能开启手势识别】
void RobdogCenter::handleGestRec(const Json::Value &jBody) {
    // if(!jBody["gestRec"].isNull()){
    //     bool gestRec = jBody["gestRec"].asBool();
    //     if(gestRec) {
    //         RobdogHandPosCtrl::getInstance().handPosStart();
    //     }
    // }
    string actionArgument = "";
    if (!jBody["actionArguement"].isNull()) {
        actionArgument = jBody["actionArguement"].asString();
    } else if (!jBody["actionArgument"].isNull()) {
        actionArgument = jBody["actionArgument"].asString();
    }
    // 开启手势识别
    if (actionArgument == "on") {
        RobdogHandPosCtrl::getInstance().handPosStart();
    }
    // 关闭手势识别
    else if (actionArgument == "off") {
        RobdogHandPosCtrl::getInstance().handPosCtrl("stop"); // 关闭手势识别算法
    } 
}
void RobdogCenter::handleSportMode(const Json::Value &jBody) {
    try {
        std::string actionArgument;
        if (!jBody["actionArguement"].isNull()) {
            actionArgument = jBody["actionArguement"].asString();
        } else if (!jBody["actionArgument"].isNull()) {
            actionArgument = jBody["actionArgument"].asString();
        }
        auto it = sportModeMap.find(actionArgument);
        if (it != sportModeMap.end()) {
            // publishStatusCtrl(DEEP_CMD_ACTION,sportModeMap[actionArgument],0);
            // 发送给控制节点
            homi_speech_interface::msg::RobdogAction msg;
            msg.actiontype = "sportMode";
            msg.actionargument = actionArgument;
            // publishAction(msg);
            publishAction(std::make_shared<homi_speech_interface::msg::RobdogAction>(msg));

        } else
        throw std::invalid_argument("Unknown actionArgument name: " + actionArgument);
    } catch (const std::exception &e) {
        std::cerr << e.what() << '\n';
    }
}

void RobdogCenter::handleMotorSkill(const Json::Value &jBody) {
    try {
        std::string actionArgument;
        // 提取动作参数
        if (!jBody["actionArguement"].isNull()) {
            actionArgument = jBody["actionArguement"].asString();
        } else if (!jBody["actionArgument"].isNull()) {
            actionArgument = jBody["actionArgument"].asString();
        }
        // 查找并发布动作
        auto it = actionMap.find(actionArgument);
        if (it != actionMap.end()) {
            // 创建消息
            homi_speech_interface::msg::RobdogAction msg;
            msg.actiontype = "motorSkill";
            msg.actionargument = actionArgument; //it->second;

            // 直接在这里执行动作的相应表情和语音
            // if(actionArgument == "standUp"){
            //     ExpressionChange::getInstance().async_callback_work("/home/<USER>/resource/vedio/touch/",1);
            // }
            // else if(actionArgument == "twistBody"){
            //     ExpressionChange::getInstance().async_callback_work("/home/<USER>/resource/vedio/twist/",1);
            // }
            // else if(actionArgument == "greeting"){
            //     ExpressionChange::getInstance().async_callback_work("/home/<USER>/resource/vedio/hello/",1);
            // }
            // else if(actionArgument == "sitDown"){
            //     ExpressionChange::getInstance().async_callback_work("/home/<USER>/resource/左看上看眨眼.mp4",1);
            // }

            // publishAction(msg);
            if (actionArgument == "standUp"&&RobotInfoMgr::getInstance().getRobotStatus() == ROBDOG_STATUS_FORCE_CTRL_LEVEL_LOWSPPEED_GAIT) 
                return;
            if (actionArgument == "getDown"&&RobotInfoMgr::getInstance().getRobotStatus() == ROBDOG_STATUS_GETDOWN) 
                return;
            publishAction(std::make_shared<homi_speech_interface::msg::RobdogAction>(msg));

            // RCLCPP_INFO_STREAM(rclcpp::get_logger("robdog_control"), "Recv " << actionArgument << ".");
        } else {
            RCLCPP_INFO_STREAM(rclcpp::get_logger("robdog_control"), "Unhandled action argument: " << actionArgument);
        }
    } catch (const std::exception& e) {
        std::cerr << "Error handling motor skill action: " << e.what() << '\n';
    }
}

void RobdogCenter::handleRLSkill(const Json::Value &jBody) {
    try {
        std::string actionArgument;
        // 提取动作参数
        if (!jBody["actionArgument"].isNull()) {
            actionArgument = jBody["actionArgument"].asString();
        } else if (!jBody["actionArguement"].isNull()) {
            actionArgument = jBody["actionArguement"].asString();
        }

        // 定义可处理的动作
        std::unordered_set<std::string> validActions = {
            "obstacleCross",
            "flatGround",
            "exit"
        };

        // 创建消息
        homi_speech_interface::msg::RobdogAction msg;
        msg.actiontype = "gaitControl";

        // 检查动作参数并发布
        if (validActions.find(actionArgument) != validActions.end()) {
            msg.actionargument = actionArgument;
            // publishAction(msg);
            publishAction(std::make_shared<homi_speech_interface::msg::RobdogAction>(msg));

        }
    } catch (const std::exception& e) {
        std::cerr << "Error handling gait control action: " << e.what() << '\n';
    }
}

void RobdogCenter::handleAcccompany(const Json::Value &jBody) {
    std::string actionArgument;
    if (!jBody["actionArgument"].isNull()) {
        actionArgument = jBody["actionArgument"].asString();
    } else if (!jBody["actionArguement"].isNull()) {
        actionArgument = jBody["actionArguement"].asString();
    } else {
        RCLCPP_WARN(rclcpp::get_logger("robdog_center_mgr"), "Neither actionArgument nor actionArguement found in the input");
        return;
    }
    RCLCPP_INFO(rclcpp::get_logger("robdog_center_mgr"), "Received action argument: %s", actionArgument.c_str());
    if (actionArgument == "on") {
        // 设置3小时内的不再主动求陪伴状态
        quiet_for_three_hours_ = true;
        auto now = std::chrono::system_clock::now();
        auto threeHoursLater = now + std::chrono::hours(3);
        quiet_for_three_hours_until_ = std::chrono::system_clock::to_time_t(threeHoursLater);

        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "User does not want to be accompanied for the next 3 hours.");
    } else if (actionArgument == "off") {
        // 用户需要陪伴的逻辑
        quiet_for_three_hours_ = false;
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "User wants to be accompanied.");
    } else {
        RCLCPP_WARN(rclcpp::get_logger("robdog_control"), "Invalid actionArgument received.");
    }
}


int RobdogCenter::checkTripReady(){
    // 如224001: 电量不足  224002：设备当前位置与导航时不一致 ...
    return 1;
}




//  ************************* 
void RobdogCenter::checkStatusWatchdog() {
    if (watchDogMonitor) {
        // 检查是否超过2秒未收到消息
        // if ((ros::Time::now() - lastMoveMessageTime).toSec() > 2.0) {
        //     ROS_WARN("No move message for more than two seconds. Stop Move.");
        if ((node_->now() - lastMoveMessageTime).seconds() > 2.0) {
            RCLCPP_WARN(rclcpp::get_logger("robdog_control"), "No move message for more than two seconds. Stop Move.");
            watchDogMonitor = false; // 停止监测

            current_continue_msg_.event = "stopActionDelay";
            continueMoveCmd_pub->publish(current_continue_msg_);

            RCLCPP_WARN(rclcpp::get_logger("robdog_control"), "publish stopActionDelay.");
        }
    }
}

//  ************************* 移动方式（步进还是持续移动） ************************* 
void RobdogCenter::Proceationtype(int actiontype, int step) {
    // 步进【基本上是语音发送的指令，直接把twist传到ctrl执行即可】
    if (actiontype == 2) {
        // RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Published velocity: linear.x = %f, linear.y = %f, angular.z =
        // %f, angular.x = %f, angular.y = %f, angular.z = %f",
        // current_twist_msg_.linear.x, current_twist_msg_.linear.y,
        // current_twist_msg_.linear.z, current_twist_msg_.angular.x,
        // current_twist_msg_.angular.y, current_twist_msg_.angular.z);
        // RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "count = %d", step);
        
        sleepForDuration(); // 暂停2秒 resting_time
        // 方式一：直接发送速度【配置在qt里面的方法】
        int count = std::abs(step); // 假设肯定不会发送0值
        setTotalCount(count);
        triggerTimerCallback();

        // 方式二：利用云深处新提供的接口【移动固定距离经常不灵，而且向左右移动固定距离是小碎步，不是明显的一步】
        // current_continue_msg_.event = "stepMode";
        // continueMoveCmd_pub.publish(current_continue_msg_);
    }
    // 连续移动【摇杆的指令，要通过轴指令控制狗】
    else if (actiontype == 1) {
        // 把current_continue_msg_传给控制节点
        watchDogMonitor = true;
        // lastMoveMessageTime = ros::Time::now();
        lastMoveMessageTime = node_->now();
        continueMoveCmd_pub->publish(current_continue_msg_);
    } else {
        if (current_continue_msg_.event == "robot_view")
        {
            node_->setRobotView(0, 0, 0);
        }
        else if (current_continue_msg_.event == "robot_move")
        {
            node_->setRobotMove(0, 0, 0);
        }

        current_continue_msg_.event = "stopAction";
        continueMoveCmd_pub->publish(current_continue_msg_);
    }
}

// ****************** event是data_update **************************
void RobdogCenter::update_emergency_contacts(int updateType,const std::string& entityId, const Json::Value& emergencyContact) {
    std::unique_ptr<SQLiteDB> db=std::make_unique<SQLiteDB>("robdog.db",rclcpp::get_logger("robdog_control"));
    if (!db->open()) {
        RCLCPP_ERROR(rclcpp::get_logger("robdog_control"),"robdog.db open or create failed");
        return ;
    }
    switch (updateType)
    {
    case 1:{
        db->createTable("emergency_contacts", "id TEXT PRIMARY KEY, nickName TEXT, phone TEXT");
        std::map<std::string, std::string> values={{"id", entityId},{"nickName", emergencyContact["nickName"].asString()},{"phone", emergencyContact["phone"].asString()}};
        if (db->insert("emergency_contacts", values)) {
            RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Emergency contact created successfully." );
        } else {
            RCLCPP_ERROR(rclcpp::get_logger("robdog_control"),"Failed to create emergency contact." );
        }
        break;
    }
    case 2:{
        std::string condition = "id = '" + entityId + "'";
        std::map<std::string, std::string> values = {{"nickName", emergencyContact["nickName"].asString()},{"phone", emergencyContact["phone"].asString()}};
        if (db->update("emergency_contacts", values, condition)) {
            RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Emergency contact update successfully." );
        } else {
            RCLCPP_ERROR(rclcpp::get_logger("robdog_control"),"Failed to update emergency contact." );
        }
        break;
    }
    case 3:{
        std::string condition = "id = '" + entityId + "'";
        if (db->remove("emergency_contacts", condition)) {
            RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Emergency contact delete successfully." );
        } else {
            RCLCPP_ERROR(rclcpp::get_logger("robdog_control"),"Failed to delete emergency contact." );
        }        
        break;
    }
    default:
        break;
    }
    db->close();
}

// 一些发布函数
//  ************************* 发给机器狗控制节点 ************************* 
void RobdogCenter::publishAction(const homi_speech_interface::msg::RobdogAction::SharedPtr robdogAction) {
    actionCmd_pub->publish(*robdogAction);
    // RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Published ActionType: %s, ActionArgument: %s",robdogAction.actiontype.c_str(),robdogAction.actionargument.c_str());
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Published ActionType: %s, ActionArgument: %s",
                    robdogAction->actiontype.c_str(),
                    robdogAction->actionargument.c_str());
}

void RobdogCenter::publishVelocity(const geometry_msgs::msg::Twist::SharedPtr velocity) {
    velCmd_pub->publish(*velocity);
    // RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Published velocity: linear.x = %f, linear.y = %f, angular.z = "
    //         "%f, angular.x = %f, angular.y = %f, angular.z = %f",velocity.linear.x, velocity.linear.y, velocity.linear.z,velocity.angular.x, velocity.angular.y, velocity.angular.z);
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), 
                    "Published velocity:\n"
                    "Linear Velocity: x = %f, y = %f, z = %f\n"
                    "Angular Velocity: x = %f, y = %f, z = %f",
                    velocity->linear.x, velocity->linear.y, velocity->linear.z,
                    velocity->angular.x, velocity->angular.y, velocity->angular.z);
}

void RobdogCenter::publishStatusCtrl(int cmd, int value, int exvalue) {
    homi_speech_interface::msg::ProprietySet msg;
    msg.cmd = cmd;
    msg.value = value;
    msg.exvalue = exvalue;
    status_pub_->publish(msg);
    // RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "cmd [0x%x] value[%d] exvalu[%d]", cmd, value, exvalue);
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "cmd [0x%x] value[%d] exvalue[%d]", cmd, value, exvalue);
}

// 持续发送（向APP）发送设备信息
void RobdogCenter::publishProperties2APP() {
    homi_speech_interface::msg::ProperToApp resMsg;
    Json::Value response(Json::objectValue);
    Json::Value newBody(Json::objectValue);
    newBody["properties"]["networkStatus"]["wifiState"] = RobotState::getInstance().getWifiSwitch();
    newBody["properties"]["networkStatus"]["mobileDataState"] = RobotState::getInstance().getMobileDataSwitch();
    newBody["properties"]["networkStatus"]["wifiName"] = RobotState::getInstance().getWifiName();
    newBody["properties"]["networkStatus"]["isWifiConnect"] = (RobotState::getInstance().getWifiSwitch() == "on");
    newBody["properties"]["battery"]["power"] = RobotInfoMgr::getInstance().getBatteryLevel();
    newBody["properties"]["battery"]["status"] = RobotState::getInstance().getBatteryChargeStatus();
    newBody["properties"]["connect_info"]["changeType"] = RobotState::getInstance().getUserConnectStatus();
    newBody["properties"]["connect_info"]["phone"] = RobotState::getInstance().getUserPhoneNumber();

    // geometry_msgs::Pose pos = RobotState::getInstance().getCurRobotPose();

    //将 geometry_msgs::Quaternion 转换为 tf::Quaternion
    // tf::Quaternion quat;
    // tf::quaternionMsgToTF(pos.orientation, quat);
    // 从四元数计算出欧拉角 (roll, pitch, yaw)
    // tf::Matrix3x3 mat(quat);
    // double roll, pitch, yaw;
    // mat.getRPY(roll, pitch, yaw);
    // newBody["properties"]["robot_pos"]["x"] = pos.position.x;
    // newBody["properties"]["robot_pos"]["y"] = pos.position.y;
    // newBody["properties"]["robot_pos"]["angle"] = (yaw * 180.0 / M_PI);

    response["body"] = newBody;
    // 将 Json::Value 转换成字符串
    Json::FastWriter writer;
    std::string jsonString = writer.write(response);
    resMsg.device_prope = jsonString;
    prope2app_pub->publish(resMsg);
}


//  ****************** 异步回调函数 ****************** 

// 创建接收到服务器(平台的交互)回复的异步回调函数
void RobdogCenter::plat_srv_callback(rclcpp::Client<homi_speech_interface::srv::SIGCData>::SharedFuture response) // (std::shared_ptr<homi_speech_interface::srv::SIGCData::Request> response) 
{
    // 使用response的get()获取
    auto response_value = response.get();
    int errorCode = response_value->error_code;
    if (errorCode!=0)
        RCLCPP_WARN(rclcpp::get_logger("robdog_control"),"service assistant return error code %d",errorCode);
}

void RobdogCenter::takephoto_srv_callback(rclcpp::Client<homi_speech_interface::srv::AssistantTakePhoto>::SharedFuture response)
{
    // 使用response的get()获取
    auto response_value = response.get();
    int errorCode = response_value->error_code;
    RCLCPP_WARN(rclcpp::get_logger("robdog_control"),"service assistant return error code %d",errorCode);
}

void RobdogCenter::net_srv_callback(rclcpp::Client<homi_speech_interface::srv::NetCtrl>::SharedFuture response)
{
    // 使用response的get()获取
    auto response_value = response.get();
    int errorCode = response_value->error_code;
    g_netctrl_ret=response_value->result.c_str();
    RCLCPP_WARN(rclcpp::get_logger("robdog_control"),"service assistant return error code %d",errorCode);
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Response result from net_ctrl_srv: %s", response_value->result.c_str());
    processNetworkStatusData();
}

void RobdogCenter::brocast_srv_callback(rclcpp::Client<homi_speech_interface::srv::AssistantSpeechText>::SharedFuture response)
{
    // 使用response的get()获取
    auto response_value = response.get();
    std::string sectionId  = response_value->section_id;
    RobotState::getInstance().setSectionId(sectionId);
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Received sectionId from server: %s", sectionId.c_str());

}

std::string RobdogCenter::getStatusDescription(const std::string& input) {
    static const std::unordered_map<std::string, std::string> statusMap = buildStatusMapping();

    auto it = statusMap.find(input);
    if (it != statusMap.end()) {
        return it->second;  // 返回对应的中文描述
    } else {
        return "未知状态";  // 如果未找到，返回默认值
    }
}

int RobdogCenter::getCodeForStatus(const std::string& status) {
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "getCodeForStatus: %s", status.c_str());
    auto it = statusToCodesMap.find(status);
    if (it != statusToCodesMap.end() && !it->second.empty()) {
        return it->second[0];  // 返回第一个匹配的 code
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "getCodeForStatus: %d", it->second[0]);
    }
    else {
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "STATUS NOT IN RANGE: %s", status.c_str());
        return -1;
    }
}

void RobdogCenter::uslamServerLogCallback(const std_msgs::msg::String::SharedPtr msg) {
    std::string statusStr = msg->data;
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Received server log: %s", statusStr.c_str());

    int status;
    std::string mapId;

    // Common 获取地图ID处理
   if (statusStr.find("common/get_map_id/map_id/") != std::string::npos) {
        std::string prefix = "common/get_map_id/map_id/";
        std::string mapId = statusStr.substr(prefix.size());
        unitreeUsingMapId = mapId;
        RobotState::getInstance().setMapId(unitreeUsingMapId);
        update_map_points_path();
        RobotState::getInstance().saveConfig();
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Current map ID updated: %s", mapId.c_str());
    }

    if (sscanf(statusStr.c_str(), "mapping/get_status/status/%d", &status) == 1) {
            unitreeMappingStatus = status;
            RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Mapping status updated: %d", status);
    } else if (sscanf(statusStr.c_str(), "localization/get_status/status/%d", &status) == 1) {
            unitreeLocalizationStatus = status;
            RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Localization status updated: %d", status);
    } else if (sscanf(statusStr.c_str(), "navigation/get_status/status/%d", &status) == 1) {
            unitreeNavigationStatus = status;
            RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Navigation status updated: %d", status);
    }

    Json::Value params;
    Json::Value msgValue;
    int taskStatusCode = getCodeForStatus(statusStr);
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Status code: %d", taskStatusCode);
    std::string statusDescription = getStatusDescription(statusStr);
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Status code: %d, Description: %s", taskStatusCode, statusDescription.c_str());
    std::string current_mapid = RobotState::getInstance().getMapId();
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Current map ID: %s", current_mapid.c_str());
    if (taskStatusCode != -1) { 
        params["code"] = taskStatusCode;
        msgValue["mapId"] = current_mapid;
        msgValue["msg"] = statusDescription;
        params["msg"] = msgValue;
        handleTaskStatusCode(taskStatusCode, msgValue);
    }
}

void RobdogCenter::devAlarmReportCallback(const std_msgs::msg::String::SharedPtr msg) {
    std::unordered_map<int, int> mapping = {{4101, 2},{4102, 3},{4106, 4}};
    Json::Value response;
    response["deviceId"] = RobotState::getInstance().getDeviceId();
    response["domain"] = "DEVICE_ALARM";
    response["event"] = "device_alarm_report";
    response["eventId"] = "robdog_plat_" + to_string(std::chrono::duration_cast<std::chrono::seconds>(std::chrono::system_clock::now().time_since_epoch()).count());
    response["seq"] = to_string(std::chrono::duration_cast<std::chrono::seconds>(std::chrono::system_clock::now().time_since_epoch()).count());
    Json::Value body;
    Json::Reader reader;

    std::string strMsg = msg->data;
    if (!reader.parse(strMsg, body)) 
        RCLCPP_WARN(rclcpp::get_logger("robdog_control"),"Can not parse warning msg");
    auto isFirstTime=false;
    if (body["alarmCode"].asInt()==14101||body["alarmCode"].asInt()==14102){
        body["alarmCode"]=body["alarmCode"].asInt()-10000;
        isFirstTime=true;
    }
    if (body["alarmCode"].asInt()==4106)
        isFirstTime=true;

    response["body"] = body;
    Json::FastWriter writer;
    std::string jsonString = writer.write(response);
    sendRequestData(jsonString);
    if (body["alarmCode"].asInt()<=4106&&body["alarmCode"].asInt()>=4101){
        RobotState::getInstance().setFollowMeStatus("off");
        RobotState::getInstance().setCurrentState(RobotStateEnum::NORMAL); 
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"),"report follow me off status : %d", body["alarmCode"].asInt());
        Json::Value response;
        response["deviceId"] = RobotState::getInstance().getDeviceId();
        response["domain"] = "BUSINESS_REPORT";
        response["event"] = "data_report";
        response["eventId"] = to_string(std::chrono::duration_cast<std::chrono::seconds>(std::chrono::system_clock::now().time_since_epoch()).count());
        response["seq"] = to_string(std::chrono::duration_cast<std::chrono::seconds>(std::chrono::system_clock::now().time_since_epoch()).count());
        response["body"]["type"] = "followMe";
        response["body"]["data"]["status"]=RobotState::getInstance().getFollowMeStatus();
        int mappedValue = -1;
        if (mapping.find(body["alarmCode"].asInt()) != mapping.end()) 
            mappedValue = mapping[body["alarmCode"].asInt()];
        response["body"]["data"]["code"]= mappedValue;
        response["body"]["data"]["isFirstTime"]=isFirstTime;
        Json::FastWriter writer;
        std::string jsonString = writer.write(response);
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"),"Follow me  warning msg is : %s", jsonString.c_str());
        sendRequestData(jsonString);
    }
}
void RobdogCenter::handleDataReportCtl(const Json::Value &inValue){
    try {
        if (inValue["body"]["actionType"].asString() == "start") {
            const std::string device_id = inValue["deviceId"].asString();
            active_types_.clear();
            for (const auto& type : inValue["body"]["dataTypeList"]) {
                active_types_.insert(type.asString());
            }

            status_.rtk.last = -1;    // 设置为非法值确保首次上报
            status_.rtk.current = status_.rtk.current; // 保持当前实际值
            status_.indoor.last = -1; // indoorSignal同样设置为非法值确保首次上报
            status_.indoor.current = status_.indoor.current; // 保持当前实际值
            status_.temperature.last = ""; // 空值强制触发温度上报

            status_.temperature.last =RobotInfoMgr::getInstance().getTempStatus();
            if (!pos_tem_rep_timer_) {
                pos_tem_rep_timer_ = node_->create_wall_timer(
                2s, 
                [this, device_id]() { check_pos_and_temp_status(device_id); }
                );
                check_pos_and_temp_status(device_id);//fisrt check;
                RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "position and temerature  status monitor START");
            }
        } else if (inValue["body"]["actionType"].asString()== "stop") {
            if (pos_tem_rep_timer_) {
                pos_tem_rep_timer_->cancel();
                pos_tem_rep_timer_.reset();
                RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "position and temerature status monitor STOP");
            }
            active_types_.clear();
        }
    } catch (const Json::LogicError &e) {
        RCLCPP_ERROR(rclcpp::get_logger("robdog_control"), "handleDataReportCtl Logic error: %s", e.what());
    } catch (const Json::RuntimeError &e) {
        RCLCPP_ERROR(rclcpp::get_logger("robdog_control"), "handleDataReportCtl Runtime error: %s", e.what());
    }
}

void RobdogCenter::check_pos_and_temp_status(const std::string& device_id)
{
    if (active_types_.empty()) return;
    Json::Value report_body;
    bool has_changes = false;
    if (active_types_.count("positioningSignal")) {
        const bool last_positive = (status_.rtk.last > 0);
        const bool curr_positive = (status_.rtk.current > 0);
        RCLCPP_INFO_THROTTLE(node_->get_logger(),*node_->get_clock(),5000,"status_.rtk.last is %d,status_.rtk.current is %d,last_positive is %d,curr_positive is %d",status_.rtk.last,status_.rtk.current,last_positive,curr_positive);        
        if (last_positive != curr_positive || status_.rtk.last == -2) {
            report_body["positioningSignal"]["status"] = status_.rtk.current > 0 ? 1 : 0;
            status_.rtk.last = status_.rtk.current;
            has_changes = true;
        }
    }
    if (active_types_.count("temperature")) {
        status_.temperature.current = RobotInfoMgr::getInstance().getTempStatus();
        RCLCPP_INFO_THROTTLE(node_->get_logger(),*node_->get_clock(),3000,"CURRENT RobotInfoMgr::getInstance().getTempStatus() is %s",status_.temperature.current.c_str());
        if (status_.temperature.current != status_.temperature.last) {
            report_body["temperature"]["status"] = status_.temperature.current;
            status_.temperature.last = status_.temperature.current;
            has_changes = true; 
        }
    }
    if (active_types_.count("indoorSignal")) {
        status_.indoor.current = repositioningResult.load();
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "CURRENT repositioningResult is %d",status_.indoor.current);
        const bool last_positive = (status_.indoor.last > 0);
        const bool curr_positive = (status_.indoor.current > 0);
        if (last_positive != curr_positive || status_.indoor.last == -1) {
            report_body["indoorSignal"]["status"] = status_.indoor.current ;
            status_.indoor.last = status_.indoor.current;
            has_changes = true;
        }
    }
    if (has_changes) {
        Json::Value report;
        report["body"]=report_body;
        report["eventId"] = to_string(std::chrono::duration_cast<std::chrono::seconds>(std::chrono::system_clock::now().time_since_epoch()).count());
        report["seq"] = to_string(std::chrono::duration_cast<std::chrono::seconds>(std::chrono::system_clock::now().time_since_epoch()).count());
        report["deviceId"]=device_id;
        report["event"]="data_report_v2";
        report["domain"]="BUSINESS_REPORT";
        report["response"]=false;
        Json::FastWriter writer;
        std::string jsonString = writer.write(report);
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "handleDataReportCtl the status of temp or position has changed.Report msg:%s",jsonString.c_str());
        sendRequestData(jsonString);
    }
}

void RobdogCenter::ntripExpireDayCallback(const std_msgs::msg::String::SharedPtr msg) {
    if (!msg) {
        return;
    }
    std::string rtkAccountExipreDay_tmp = msg->data;
    if (rtkAccountExipreDay_tmp != rtkAccountExipreDay) {
        rtkAccountExipreDay = rtkAccountExipreDay_tmp;
        rtkAccountStatusReport = true;
    }
}

void RobdogCenter::ntripStatusCallback(const std_msgs::msg::Int64::SharedPtr msg) {
    if (!msg) {
        return;
    }
    int64_t rtkAccountStatus_tmp = msg->data;
    if (rtkAccountStatus_tmp != rtkAccountStatus) {
        rtkAccountStatus = rtkAccountStatus_tmp;
        rtkAccountStatusReport = true;
    }
}

void RobdogCenter::ntripAccountCallback(const std_msgs::msg::String::SharedPtr msg) {
    if (!msg) {
        return;
    }
    std::string data = msg->data;

    // 检查是否无账号
    if (data == "No Account Configured") {
        if(rtkAccount != "" || rtkPass != "") {
            rtkAccount = "";
            rtkPass = "";
            rtkAccountStatusReport = true;
        }
        return;
    }
    std::regex pattern(R"(User:\s*(\w+)\s*\|\s*Pwd:\s*(\w+))");
    std::smatch matches;

    if (std::regex_search(data, matches, pattern) && matches.size() == 3) {
        std::string rtkAccount_tmp = matches[1].str();
        std::string rtkPass_tmp = matches[2].str();
        if(rtkAccount != rtkAccount_tmp) {
            rtkAccount = rtkAccount_tmp;
            rtkAccountStatusReport = true;
        }
        if(rtkPass != rtkPass_tmp) {
            rtkPass = rtkPass_tmp;
            rtkAccountStatusReport = true;
        }
    } else {
        return;
    }
}

void RobdogCenter::rtkAccountReportTimerCallback() {
    Json::Value properties;

    if(rtkAccountStatusReport == true) {
        handleRtkAccountRead(properties);
    }
}

// ****************** WiFi相关处理方法 **************************

// 处理WiFi列表查询 ：平台请求 → ROS2服务调用 → 接收响应 → 转换格式 → 发送给平台
void RobdogCenter::handleWifiListQuery(const Json::Value &inValue, const Json::Value &jBody) {
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "handleWifiListQuery called");

    try {
        // 检查节点指针是否有效
        if (!node_) {
            RCLCPP_ERROR(rclcpp::get_logger("robdog_control"), "Node pointer is null");
            sendWifiResponse(inValue, WifiQueryStatus::FAILED);
            return;
        }

        // 检查网络服务客户端是否可用
        if (!net_client) {
            RCLCPP_ERROR(rclcpp::get_logger("robdog_control"), "Network client is null");
            sendWifiResponse(inValue, WifiQueryStatus::FAILED);
            return;
        }

        // 等待服务可用
        if (!net_client->wait_for_service(std::chrono::seconds(2))) {
            RCLCPP_ERROR(rclcpp::get_logger("robdog_control"), "Network service not available");
            sendWifiResponse(inValue, WifiQueryStatus::FAILED);
            return;
        }

        // 创建服务请求
        auto request = std::make_shared<homi_speech_interface::srv::NetCtrl::Request>();
        request->data = "{\"command\": \"scanWifiNetworks\"}";

        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Calling network service for WiFi scan");

        // 使用异步调用和回调函数的方式，参考其他代码实现
        auto future = net_client->async_send_request(request,
            [this, inValue](rclcpp::Client<homi_speech_interface::srv::NetCtrl>::SharedFuture response) {
                try {
                    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Network service response received");
                    
                    auto serviceResponse = response.get();
                    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Network service result: %s", serviceResponse->result.c_str());
                    

                    // 转换服务响应为平台格式并发送
                    Json::Value platformResponse = convertServiceResponseToPlatformFormat(inValue, serviceResponse->result);
                    Json::StreamWriterBuilder writerBuilder;
                    std::string responseStr = Json::writeString(writerBuilder, platformResponse);
                    
                    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Platform response converted, sending to platform...");
                    sendRequestData(responseStr);

                    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "WiFi list query completed successfully");

                } catch (const std::exception& e) {
                    RCLCPP_ERROR(rclcpp::get_logger("robdog_control"), "Error in WiFi service callback: %s", e.what());
                    sendWifiResponse(inValue, WifiQueryStatus::FAILED);
                }
            });

    } catch (const std::exception& e) {
        RCLCPP_ERROR(rclcpp::get_logger("robdog_control"), "Error in handleWifiListQuery: %s", e.what());
        sendWifiResponse(inValue, WifiQueryStatus::FAILED);
    }
}

// 数据转换方法：将ROS2服务响应转换为平台格式
Json::Value RobdogCenter::convertServiceResponseToPlatformFormat(const Json::Value &inValue, const std::string &serviceResponse) {
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Converting service response to platform format");
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Raw service response: %s", serviceResponse.c_str());

    // 构建基础响应结构
    Json::Value response;
    response["deviceId"] = inValue["deviceId"];
    response["domain"] = "DEVICE_PROPERTIES";
    response["event"] = "wifi_list_query";
    response["eventId"] = inValue["eventId"];
    response["seq"] = std::to_string(std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::system_clock::now().time_since_epoch()).count());

    // 解析服务响应
    Json::Reader reader;
    Json::Value serviceResult;
    if (!reader.parse(serviceResponse, serviceResult)) {
        RCLCPP_ERROR(rclcpp::get_logger("robdog_control"), "Failed to parse service response JSON");
        response["body"]["queryStatus"] = 2; // 查询失败
        response["body"]["wifiList"] = Json::Value(Json::arrayValue);
        return response;
    }

    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Service response JSON parsed successfully");

    // 根据服务响应状态进行转换
    std::string status = serviceResult.get("status", "").asString();
    int code = serviceResult.get("code", 500).asInt();
    std::string message = serviceResult.get("message", "").asString();
    bool cached = serviceResult.get("cached", false).asBool();
    
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Service status: %s, code: %d, message: %s, cached: %s", 
                status.c_str(), code, message.c_str(), cached ? "true" : "false");

    if (status == "scanning") {
        // 扫描中状态（首次启动扫描或扫描进行中）
        response["body"]["queryStatus"] = 0; // 查询中
        response["body"]["wifiList"] = Json::Value(Json::arrayValue);
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Service response: scanning (code: %d) - %s", code, message.c_str());
    } else if (status == "completed") {
        // 扫描完成状态
        response["body"]["queryStatus"] = 1; // 查询成功
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Service response: completed (cached: %s), processing networks...", 
                    cached ? "true" : "false");

        // 转换networks数组为wifiList
        Json::Value wifiList(Json::arrayValue);
        if (serviceResult.isMember("networks") && serviceResult["networks"].isArray()) {
            const Json::Value& networks = serviceResult["networks"];
            int totalCount = serviceResult.get("count", 0).asInt();
            RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Found %d networks in service response (total count: %d)", 
                        networks.size(), totalCount);

            for (const auto& network : networks) {
                if (network.isMember("ssid") && network.isMember("security")) {
                    Json::Value wifiInfo;
                    wifiInfo["wifiName"] = network["ssid"].asString();

                    // 直接使用signal_level作为信号强度
                    int signal = network["signal"].asInt();
                    int signalStrength = 0;
                    
                    // 检查是否存在signal_level字段
                    if (network.isMember("signal_level")) {
                        signalStrength = network["signal_level"].asInt();
                        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Using direct signal_level: %d", signalStrength);
                    } else {
                        // 兼容旧版本：如果没有signal_level字段，保留原有转换逻辑
                        signalStrength = -120 + (signal * 110 / 100); // 将0-100%转换为-120到-10dBm
                        if (signalStrength > -10) signalStrength = -10;
                        if (signalStrength < -120) signalStrength = -120;
                        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Converted signal %d%% to signalStrength: %d dBm", signal, signalStrength);
                    }
                    wifiInfo["signalStrength"] = signalStrength;

                    // 判断加密状态
                    std::string security = network["security"].asString();
                    int encryptState = 0; // 默认未加密
                    if (!security.empty() && security != "none" && security != "--") {
                        encryptState = 1; // 已加密
                    }
                    wifiInfo["encryptState"] = encryptState;

                    // 记录BSSID信息（如果存在）
                    if (network.isMember("bssid")) {
                        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Processing WiFi: %s (BSSID: %s), signal: %d%%, security: %s, encrypted: %d", 
                                    network["ssid"].asString().c_str(), network["bssid"].asString().c_str(), 
                                    signal, security.c_str(), encryptState);
                    } else {
                        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Processing WiFi: %s, signal: %d%%, security: %s, encrypted: %d", 
                                    network["ssid"].asString().c_str(), signal, security.c_str(), encryptState);
                    }

                    // 根据协议要求：本体过滤未加密的WiFi，只返回加密的WiFi
                    if (encryptState == 1) {
                        wifiList.append(wifiInfo);
                    }
                }
            }
        }

        response["body"]["wifiList"] = wifiList;
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Service response: completed, found %u encrypted networks (cached: %s)", 
                    wifiList.size(), cached ? "true" : "false");
    } else if (status == "error") {
        // 错误状态
        response["body"]["queryStatus"] = 2; // 查询失败
        response["body"]["wifiList"] = Json::Value(Json::arrayValue);
        std::string errorMsg = serviceResult.get("error", "").asString();
        RCLCPP_ERROR(rclcpp::get_logger("robdog_control"), "Service response: error (code: %d) - %s", code, errorMsg.c_str());
    } else {
        // 未知状态
        response["body"]["queryStatus"] = 2; // 查询失败
        response["body"]["wifiList"] = Json::Value(Json::arrayValue);
        RCLCPP_ERROR(rclcpp::get_logger("robdog_control"), "Service response: unknown status '%s' (code: %d)", status.c_str(), code);
    }

    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "convertServiceResponseToPlatformFormat completed");
    return response;
}

// WiFi响应封装统一函数
void RobdogCenter::sendWifiResponse(const Json::Value &inValue, WifiQueryStatus queryStatus, const Json::Value &wifiList) {
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "sendWifiResponse called - Status: %d, WiFi count: %d", 
                static_cast<int>(queryStatus), wifiList.size());
    
    Json::Value response;
    response["deviceId"] = inValue["deviceId"];
    response["domain"] = "DEVICE_PROPERTIES";
    response["event"] = "wifi_list_query";
    response["eventId"] = inValue["eventId"];
    response["seq"] = std::to_string(std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::system_clock::now().time_since_epoch()).count());

    // 严格按照平台响应格式
    response["body"]["queryStatus"] = static_cast<int>(queryStatus);
    response["body"]["wifiList"] = wifiList;

    Json::StreamWriterBuilder writerBuilder;
    std::string responseStr = Json::writeString(writerBuilder, response);
    
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "sendWifiResponse - Sending response: %s", responseStr.c_str());
    
    sendRequestData(responseStr);
    
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "sendWifiResponse completed");
}





// 处理WiFi设置
void RobdogCenter::handleWifiSet(const Json::Value &inValue, const Json::Value &jBody) {
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "handleWifiSet called");

    try {
        // 获取WiFi参数（使用标准字段名）
        std::string wifiName = jBody["wifiName"].asString();
        std::string wifiPassword = jBody["wifiPassword"].asString();

        if (wifiName.empty()) {
            RCLCPP_ERROR(rclcpp::get_logger("robdog_control"), "WiFi name cannot be empty");
            return;
        }

        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Attempting to connect to WiFi: %s", wifiName.c_str());
        // 注意：密码不记录到日志中以保护隐私

        // 构建JSON命令字符串
        Json::Value commandJson;
        commandJson["command"] = "connectWifi";
        commandJson["ssid"] = wifiName;
        if (!wifiPassword.empty()) {
            commandJson["password"] = wifiPassword;
        }

        Json::StreamWriterBuilder writerBuilder;
        std::string commandStr = Json::writeString(writerBuilder, commandJson);

        // 创建服务请求
        auto request = std::make_shared<homi_speech_interface::srv::NetCtrl::Request>();
        request->data = commandStr;

        // 设置WiFi连接状态为连接中
        wifiSetResult.store(WifiSetResult::CONNECTING);
        targetWifiSSID = wifiName;
        wifiConnectStartTime = std::chrono::steady_clock::now();
        
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Calling network service for WiFi connection: %s", wifiName.c_str());

        // 使用异步调用和回调函数的方式
        auto future = net_client->async_send_request(request,
            [this, inValue, wifiName](rclcpp::Client<homi_speech_interface::srv::NetCtrl>::SharedFuture response) {
                try {
                    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Network service response received for WiFi connection");
                    
                    auto serviceResponse = response.get();
                    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Network service result for WiFi connection: %s", serviceResponse->result.c_str());

                    // 解析服务响应并更新WiFi连接状态
                    Json::Value responseJson;
                    Json::Reader reader;
                    if (reader.parse(serviceResponse->result, responseJson)) {
                        if (responseJson.isMember("success") && responseJson["success"].asBool()) {
                            wifiSetResult.store(WifiSetResult::CONNECT_SUCCESS);
                            RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "WiFi connection successful");
                        } else if (responseJson.isMember("error")) {
                            std::string error = responseJson["error"].asString();
                            if (error.find("password") != std::string::npos || error.find("authentication") != std::string::npos) {
                                wifiSetResult.store(WifiSetResult::CONNECT_PASSWORD_ERROR);
                                RCLCPP_ERROR(rclcpp::get_logger("robdog_control"), "WiFi connection failed: password error");
                            } else {
                                wifiSetResult.store(WifiSetResult::CONNECT_TIMEOUT);
                                RCLCPP_ERROR(rclcpp::get_logger("robdog_control"), "WiFi connection failed: timeout or other error");
                            }
                        } else {
                            wifiSetResult.store(WifiSetResult::CONNECT_TIMEOUT);
                            RCLCPP_ERROR(rclcpp::get_logger("robdog_control"), "WiFi connection failed: unknown error");
                        }
                    } else {
                        wifiSetResult.store(WifiSetResult::CONNECT_TIMEOUT);
                        RCLCPP_ERROR(rclcpp::get_logger("robdog_control"), "Failed to parse WiFi connection response");
                    }

                    // // 转换服务响应为平台格式并发送
                    // Json::Value platformResponse = convertServiceResponseToPlatformFormat(inValue, serviceResponse->result);
                    // Json::StreamWriterBuilder writerBuilder;
                    // std::string responseStr = Json::writeString(writerBuilder, platformResponse);
                    
                    // // 发送响应到平台
                    // sendRequestData(responseStr);
                    
                } catch (const std::exception& e) {
                    RCLCPP_ERROR(rclcpp::get_logger("robdog_control"), "Error in WiFi connection callback: %s", e.what());
                    wifiSetResult.store(WifiSetResult::CONNECT_TIMEOUT);
                }
            });

        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "WiFi connection request sent to network service for: %s", wifiName.c_str());

    } catch (const std::exception& e) {
        RCLCPP_ERROR(rclcpp::get_logger("robdog_control"), "Error in handleWifiSet: %s", e.what());
    }
}

// 处理WiFi设置结果查询
void RobdogCenter::handleWifiSetResultQuery(const Json::Value &inValue, const Json::Value &jBody) {
    RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "handleWifiSetResultQuery called");

    try {
        // 获取当前WiFi连接状态
        WifiSetResult currentResult = wifiSetResult.load();
        int wifiSetResultValue = static_cast<int>(currentResult);
        
        RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "Current WiFi connection status: %d", wifiSetResultValue);

        // 检查连接超时（如果状态为连接中且超过30秒）
        if (currentResult == WifiSetResult::CONNECTING) {
            auto now = std::chrono::steady_clock::now();
            auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(now - wifiConnectStartTime).count();
            
            if (elapsed > 30) { // 30秒超时
                wifiSetResult.store(WifiSetResult::CONNECT_TIMEOUT);
                currentResult = WifiSetResult::CONNECT_TIMEOUT;
                wifiSetResultValue = static_cast<int>(currentResult);
                RCLCPP_WARN(rclcpp::get_logger("robdog_control"), "WiFi connection timeout after %ld seconds", elapsed);
            }
        }

        // 构建标准响应消息
        Json::Value response;
        response["deviceId"] = inValue["deviceId"];
        response["domain"] = "DEVICE_PROPERTIES";
        response["event"] = "wifi_set_result_query";
        response["eventId"] = inValue["eventId"];
        response["seq"] = std::to_string(std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::system_clock::now().time_since_epoch()).count());
        response["body"]["wifiSetResult"] = wifiSetResultValue;

        // 发送响应
        Json::StreamWriterBuilder writerBuilder;
        std::string responseStr = Json::writeString(writerBuilder, response);
        sendRequestData(responseStr);

        // RCLCPP_INFO(rclcpp::get_logger("robdog_control"), "WiFi set result query completed, result: %d", wifiSetResult);

    } catch (const std::exception& e) {
        RCLCPP_ERROR(rclcpp::get_logger("robdog_control"), "Error in handleWifiSetResultQuery: %s", e.what());

        // 发送错误响应（连接失败）
        Json::Value errorResponse;
        errorResponse["deviceId"] = inValue["deviceId"];
        errorResponse["domain"] = "DEVICE_PROPERTIES";
        errorResponse["event"] = "wifi_set_result_query";
        errorResponse["eventId"] = inValue["eventId"];
        errorResponse["seq"] = std::to_string(std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::system_clock::now().time_since_epoch()).count());
        errorResponse["body"]["wifiSetResult"] = 2; // 连接失败（超时）

        Json::StreamWriterBuilder writerBuilder;
        std::string errorStr = Json::writeString(writerBuilder, errorResponse);
        sendRequestData(errorStr);;
    }
}

