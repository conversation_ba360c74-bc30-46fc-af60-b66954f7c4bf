#ifndef ANDLINK_NODE_HPP
#define ANDLINK_NODE_HPP

#include "rclcpp/rclcpp.hpp"
#include "std_msgs/msg/byte_multi_array.hpp"
#include "std_msgs/msg/string.hpp"
#include "andlink_adapt.h"
#include <homi_speech_interface/msg/sigc_event.hpp>

class AndlinkNode : public rclcpp::Node
{
public:
    AndlinkNode();
    int andlink_main();
    int demo_startAndlinkSdk(CFG_NET_MODE_e mode, char *ifname);
    void publish_ble_cmd_broadcast_message();
    void publish_ble_cmd_unbroadcast_message();
    void publish_ble_cmd_unbind_message();
    void publish_network_connecting_start_message();
    void publish_network_connecting_end_message();
private:
    void handle_andlink_cmd_message(const std_msgs::msg::ByteMultiArray::SharedPtr msg);
    void handle_ble_byte_stream_message(const std_msgs::msg::ByteMultiArray::SharedPtr msg);
    void handle_andlink_userkey_message(const std_msgs::msg::String::SharedPtr msg);
    void handle_timer_callback(); // 新增定时器回调函数声明
    void log_timer_callback();
    void publish_byte_stream_message();
    std::string to_hex(uint8_t byte);
    void publish_json_message();

    rclcpp::Subscription<std_msgs::msg::ByteMultiArray>::SharedPtr byte_stream_subscription_;
    rclcpp::Subscription<std_msgs::msg::String>::SharedPtr andlink_userkey_subscription_;
    rclcpp::Subscription<std_msgs::msg::ByteMultiArray>::SharedPtr andlink_cmd_subscription_;
    rclcpp::Publisher<std_msgs::msg::ByteMultiArray>::SharedPtr ble_cmd_publisher_;
    rclcpp::Publisher<std_msgs::msg::String>::SharedPtr andlink_network_publisher_; 
    rclcpp::Publisher<homi_speech_interface::msg::SIGCEvent>::SharedPtr homi_player_publisher_;       
    rclcpp::TimerBase::SharedPtr timer_; // 定时器成员变量
    rclcpp::TimerBase::SharedPtr log_timer_;
    rclcpp::Publisher<std_msgs::msg::ByteMultiArray>::SharedPtr byte_stream_publisher_;
    rclcpp::TimerBase::SharedPtr byte_stream_timer_;
    rclcpp::Subscription<std_msgs::msg::String>::SharedPtr char_subscription_;
    rclcpp::Publisher<std_msgs::msg::String>::SharedPtr json_publisher_;
    rclcpp::TimerBase::SharedPtr json_timer_;
};
int andlink_dn_send_cmd_callback(RESP_MODE_e mode, dn_dev_ctrl_frame_t *ctrlFrame, char *eventType, char *respData, unsigned int respBufSize);

#endif // ANDLINK_NODE_HPP
