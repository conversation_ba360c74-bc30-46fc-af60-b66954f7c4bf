#include <cstring>
#include <regex>
#include <netinet/in.h>
#include <csignal> // 添加头文件
#include "rclcpp/rclcpp.hpp"
#include <string>
#include "std_msgs/msg/string.hpp"
#include "std_msgs/msg/byte_multi_array.hpp" // 添加头文件
#include "andlink_node.hpp"
#include <fstream>
#include <stdio.h>
#include <stdlib.h>
#include <dirent.h>
#include <unistd.h>
#include <ctype.h>
#include <signal.h>
#include <algorithm>
#include <cctype>
#include <vector>
#include <sstream>
#include <homi_speech_interface/msg/sigc_event.hpp>
#define WPA_CONF_FILE "/etc/wpa_supplicant/wpa_supplicant-wlan0.conf"
// 简化宏定义
#define LOG_INFO(format, ...) log_printf("INFO", format, ##__VA_ARGS__)
#define LOG_WARN(format, ...) log_printf("WARN", format, ##__VA_ARGS__)
#define LOG_ERROR(format, ...) log_printf("ERROR", format, ##__VA_ARGS__)
#define LOG_DEBUG(format, ...) log_printf("DEBUG", format, ##__VA_ARGS__)

#include <chrono>
#include <cstdio>
#include <iomanip>
#include <sstream>
#include <string>

#include <array>
#include <memory>
#include <cstring> // for strstr

// 获取带毫秒的当前时间字符串
std::string get_current_time_string() {
    auto now = std::chrono::system_clock::now();
    auto now_time = std::chrono::system_clock::to_time_t(now);
    auto now_ms = std::chrono::duration_cast<std::chrono::milliseconds>(
                    now.time_since_epoch()) % 1000;
    
    std::stringstream ss;
    ss << "[" << std::put_time(std::localtime(&now_time), "%Y-%m-%d %H:%M:%S")
       << "." << std::setfill('0') << std::setw(3) << now_ms.count() << "]";
    return ss.str();
}

// 带时间戳的日志函数
void log_printf(const char* prefix, const char* format, ...) {
    // 获取当前时间
    std::string time_str = get_current_time_string();
    
    // 构造完整格式
    char full_format[512];
    snprintf(full_format, sizeof(full_format), "%s [%s] %s\n", time_str.c_str(), prefix, format);
    
    // 打印日志
    va_list args;
    va_start(args, format);
    vprintf(full_format, args);
    va_end(args);
}

// static int s_quit = 0;
int dn_send_cmd_status = 0;
char* g_json_str = nullptr; 
char g_ifname[16]= "";
char g_p2p_ifname[16]= "";

struct DeviceInfo {
    char ip[INET_ADDRSTRLEN];
    char brdAddr[INET_ADDRSTRLEN];
};

namespace {

bool is_hex_digit(char c) {
    return (c >= '0' && c <= '9') || 
           (c >= 'a' && c <= 'f') || 
           (c >= 'A' && c <= 'F');
}

// 增强的SSID解码函数，处理各种转义序列
std::string decode_ssid(const std::string& input) {
    std::string result;
    size_t i = 0;
    size_t len = input.size();

    while (i < len) {
        // 处理 \xXX 转义序列（中文等非ASCII字符）
        if (i + 3 < len && input[i] == '\\' && input[i+1] == 'x' &&
            is_hex_digit(input[i+2]) && is_hex_digit(input[i+3])) {
            
            int hex_value;
            std::stringstream ss;
            ss << std::hex << input.substr(i+2, 2);
            ss >> hex_value;
            
            result += static_cast<char>(hex_value);
            i += 4;
        }
        // 处理其他转义序列（如\t, \n, \\ 等）
        else if (i + 1 < len && input[i] == '\\') {
            switch (input[i+1]) {
                case '\\': result += '\\'; break;
                case '"':  result += '"';  break;
                case '\'': result += '\''; break;
                case 't':  result += '\t'; break;
                case 'n':  result += '\n'; break;
                case 'r':  result += '\r'; break;
                default:   // 未知转义序列，保留原样
                    result += '\\';
                    result += input[i+1];
                    break;
            }
            i += 2;
        }
        // 普通字符
        else {
            result += input[i];
            i++;
        }
    }
    return result;
}

// 安全提取SSID行
std::string extract_ssid_line(const std::string& line) {
    size_t pos = line.find("SSID: ");
    if (pos == std::string::npos) {
        return "";
    }
    
    std::string ssid_line = line.substr(pos + 6); // "SSID: " 长度为6
    
    // 移除行尾的换行符和空白字符
    size_t endpos = ssid_line.find_last_not_of(" \r\n\t");
    if (endpos != std::string::npos) {
        ssid_line = ssid_line.substr(0, endpos + 1);
    } else {
        ssid_line.clear();
    }
    
    return ssid_line;
}

} // 匿名命名空间

bool isWlan0ConnectedToSSID(const std::string& ssid) {
    std::string cmd = "iw dev " + std::string(g_ifname) + " link";
    LOG_DEBUG("Executing command: %s", cmd.c_str());
    
    std::array<char, 512> buffer; // 使用更大的缓冲区处理长SSID
    std::unique_ptr<FILE, decltype(&pclose)> pipe(popen(cmd.c_str(), "r"), pclose);
    
    if (!pipe) {
        LOG_ERROR("Failed to execute iw command");
        return false;
    }

    bool is_connected = false;
    bool found_not_connected = false;
    std::string raw_ssid = "";

    while (fgets(buffer.data(), buffer.size(), pipe.get()) != nullptr) {
        std::string line(buffer.data());
        
        // 检查是否包含 "Not connected" 信息
        if (line.find("Not connected") != std::string::npos) {
            found_not_connected = true;
        }
        
        // 查找 SSID 行
        if (line.find("SSID: ") != std::string::npos) {
            LOG_DEBUG("Found SSID field in command output");
            is_connected = true;
            
            raw_ssid = extract_ssid_line(line);
            if (raw_ssid.empty()) {
                LOG_WARN("SSID field found but could not extract value");
                continue;
            }
            
            // 处理转义序列
            std::string actual_ssid = decode_ssid(raw_ssid);
            
            LOG_DEBUG("Raw SSID from command: '%s'", raw_ssid.c_str());
            LOG_DEBUG("Decoded SSID: '%s'", actual_ssid.c_str());
            LOG_DEBUG("Target SSID: '%s'", ssid.c_str());
            
            // 比较处理后的SSID
            if (actual_ssid == ssid) {
                LOG_INFO("Already connected to same SSID '%s'", ssid.c_str());
                return true;
            }
            
            LOG_WARN("Connected to different SSID '%s' (expected '%s')", 
                   actual_ssid.c_str(), ssid.c_str());
            return false;
        }
    }

    if (found_not_connected) {
        LOG_INFO("Interface %s is not connected to any network", g_ifname);
    } else if (is_connected) {
        LOG_INFO("Interface %s is connected but no SSID information found", g_ifname);
    } else {
        LOG_INFO("Interface %s status unknown from command output", g_ifname);
    }
    
    return false;
}

bool scanForExactSSID(const std::string &ssid) {
    LOG_DEBUG("Starting scan for exact SSID: '%s'", ssid.c_str());
    
    FILE *fp = popen("nmcli device wifi list --rescan yes", "r");
    if (!fp) {
        LOG_ERROR("Failed to execute nmcli scan command.");
        return false;
    }
    LOG_DEBUG("Scan command executed successfully");

    char buffer[512];
    std::regex ssid_regex(R"((?:\*|\s)\s+([^\s]+(?:\s[^\s]+)*)\s+Infra)");
    int line_num = 0;
    int match_count = 0;

    while (fgets(buffer, sizeof(buffer), fp) != nullptr) {
        line_num++;
        std::string line(buffer);
        LOG_DEBUG("Processing line %d (raw): %s", line_num, line.c_str());

        std::smatch match;
        if (std::regex_search(line, match, ssid_regex)) {
            match_count++;
            std::string current_ssid = match[1].str();
            // 去除首尾空格
            auto start = current_ssid.find_first_not_of(" ");
            auto end = current_ssid.find_last_not_of(" ");
            current_ssid = (start == std::string::npos) ? "" : current_ssid.substr(start, end - start + 1);
            
            LOG_DEBUG("[MATCH %d] Detected SSID: '%s' (Raw capture: '%s')", 
                     match_count, current_ssid.c_str(), match[1].str().c_str());

            if (current_ssid == ssid) {
                LOG_INFO("Exact match found on line %d!", line_num);
                pclose(fp);
                return true;
            }
        }
    }

    LOG_INFO("Scan completed. Total lines: %d, Total matches: %d. Target SSID not found.", 
             line_num, match_count);
    pclose(fp);
    return false;
}

void reset_network_manager() {
    std::string wifi_cmd;
    int result = 0;
    // 2. 删除所有 NetworkManager 配置并重启服务
    LOG_INFO("Removing all NetworkManager configurations...");
    wifi_cmd = "sudo rm -rf /etc/NetworkManager/system-connections/*";
    result = system(wifi_cmd.c_str());

    if (result == -1) {
        LOG_ERROR("Failed to remove NetworkManager configurations");
    } else {
        int exit_status = WEXITSTATUS(result);
        if (exit_status != 0) {
            LOG_ERROR("Remove command exited with status: %d", exit_status);
        } else {
            LOG_DEBUG("NetworkManager configurations removed successfully");
        }
    }

    LOG_INFO("Restarting NetworkManager service...");
    wifi_cmd = "sudo systemctl restart NetworkManager.service";
    result = system(wifi_cmd.c_str());

    if (result == -1) {
        LOG_ERROR("Failed to restart NetworkManager service");
    } else {
        int exit_status = WEXITSTATUS(result);
        if (exit_status != 0) {
            LOG_ERROR("Restart command exited with status: %d", exit_status);
        } else {
            LOG_DEBUG("NetworkManager service restarted successfully");
        }
    }
    
    // 3. 等待服务完全启动
    sleep(4);
}

int get_user_bind() {
    const std::string config_path = "/etc/cmcc_robot/andlinkSdk.conf";  // 配置文件路径
    
    try {
        // 1. 检查配置文件是否存在
        std::ifstream file(config_path);
        if (!file.is_open()) {
            LOG_ERROR("Configuration file %s does not exist.", config_path.c_str());
            return 0;
        }
        
        // 2. 逐行读取配置文件
        std::string line;
        while (std::getline(file, line)) {
            // 跳过空行和注释行
            if (line.empty() || line[0] == '#') continue;
            
            // 查找 userBind 配置项
            if (line.find("userBind") != std::string::npos) {
                // 提取键值对
                size_t eq_pos = line.find('=');
                if (eq_pos != std::string::npos) {
                    // 提取值部分
                    std::string value = line.substr(eq_pos + 1);
                    
                    // 清除值两端的空白字符
                    size_t start = value.find_first_not_of(" \t");
                    if (start != std::string::npos) {
                        size_t end = value.find_last_not_of(" \t");
                        value = value.substr(start, end - start + 1);
                    }
                    
                    LOG_INFO("Found userBind value: %s", value.c_str());
                    
                    // 如果值为 "1" 返回 1，否则返回 0
                    return (value == "1") ? 1 : 0;
                }
            }
        }
        
        // 3. 如果未找到 userBind 配置项
        LOG_ERROR("userBind configuration not found");
        return 0;
        
    } catch (...) {
        // 捕获所有异常并返回 0
        LOG_ERROR("Exception occurred while reading userBind configuration");
        return 0;
    }
}

bool write_wpa_conf_file(const wifi_cfg_info_t* wificfg) {
    std::ofstream fd(WPA_CONF_FILE);
    if (!fd.is_open()) {
        LOG_ERROR("Failed to create/open file: %s", WPA_CONF_FILE);
        return false;
    }

    try {
        // 写入文件头和使用通用配置
        fd << "# Auto-generated wpa_supplicant configuration\n"
           << "ctrl_interface=/var/run/wpa_supplicant\n"
           << "ap_scan=1\n"
           << "update_config=1\n"
           << "country=CN\n\n";

        // 验证必要参数
        if (!wificfg->ssid || !wificfg->password || 
            !wificfg->ssid[0] || !wificfg->password[0]) {
            throw std::invalid_argument("Invalid SSID or password");
        }

        // 构建网络配置块
        std::string network_block = "network={\n";
        network_block += "    ssid=\"" + std::string(wificfg->ssid) + "\"\n";
        
        // 添加 BSSID（如果存在且有效）
        if (wificfg->mac && wificfg->mac[0]) {
            network_block += "    bssid=" + std::string(wificfg->mac) + "\n";
        }
        
        // 添加 password 和扫描设置
        network_block += "    psk=\"" + std::string(wificfg->password) + "\"\n";
        network_block += "    scan_ssid=1\n";
        
        // 添加 key_mgmt=WPA-PSK 设置（解决兼容性问题）
        network_block += "    key_mgmt=WPA-PSK\n";  // 明确指定密钥管理方式
        
        network_block += "}\n";

        // 写入网络配置块
        fd << network_block;
        
        // 添加调试信息
        LOG_DEBUG("Generated wpa_supplicant configuration:\n%s", network_block.c_str());
        
        return true;
    } 
    catch (const std::exception& e) {
        LOG_ERROR("Configuration error: %s", e.what());
        // 删除部分写入的文件
        std::remove(WPA_CONF_FILE);
        return false;
    }
}

int get_wpa_supplicant_pid() {
    const char* TARGET_NAME = "wpa_supplicant";
    // 必须匹配的参数
    std::vector<std::string> REQUIRED_ARGS = {
        "-i", "wlan0",
        "-c", "/etc/wpa_supplicant/wpa_supplicant-wlan0.conf"
    };
    int target_pid = 0;
    int killed_processes = 0;

    LOG_DEBUG("开始搜索目标 wpa_supplicant 进程...");
    
    std::string debug_msg = "目标参数要求: ";
    for (const auto& arg : REQUIRED_ARGS) {
        debug_msg += arg + " ";
    }
    LOG_DEBUG("%s", debug_msg.c_str());

    DIR *dir = opendir("/proc");
    if (!dir) {
        LOG_ERROR("无法打开 /proc 目录");
        return 0;
    }

    struct dirent *entry;
    while ((entry = readdir(dir))) {
        if (entry->d_type != DT_DIR) continue;
        
        char *endptr;
        long pid = strtol(entry->d_name, &endptr, 10);
        if (*endptr != '\0') continue;
        
        char cmdline_path[256];
        snprintf(cmdline_path, sizeof(cmdline_path), "/proc/%ld/cmdline", pid);
        
        FILE *cmdline_file = fopen(cmdline_path, "r");
        if (!cmdline_file) continue;
        
        char cmdline[1024] = {0};
        if (fgets(cmdline, sizeof(cmdline) - 1, cmdline_file)) {
            // 改进的参数解析逻辑
            std::vector<std::string> args;
            char* p = cmdline;
            while (*p != '\0') {
                args.push_back(p);
                p += strlen(p) + 1;
            }
            
            // 检查程序名是否匹配
            bool is_wpa = false;
            for (const auto& arg : args) {
                if (arg.find(TARGET_NAME) != std::string::npos) {
                    is_wpa = true;
                    break;
                }
            }
            
            if (!is_wpa) {
                fclose(cmdline_file);
                continue;
            }
            
            LOG_DEBUG("找到 wpa_supplicant 进程 PID: %ld", pid);
            
            debug_msg = "完整命令行: ";
            for (const auto& arg : args) {
                debug_msg += arg + " ";
            }
            LOG_DEBUG("%s", debug_msg.c_str());
            
            // 检查是否包含所有必需参数
            bool is_target = true;
            for (const auto& req_arg : REQUIRED_ARGS) {
                bool found = false;
                for (const auto& arg : args) {
                    if (arg == req_arg) {
                        found = true;
                        break;
                    }
                }
                if (!found) {
                    is_target = false;
                    LOG_DEBUG("缺失参数: %s", req_arg.c_str());
                    break;
                }
            }
            
            // 检查是否包含后台参数 (-B)
            bool has_B_option = false;
            for (const auto& arg : args) {
                if (arg == "-B") {
                    has_B_option = true;
                    break;
                }
            }
            
            // 关键修改：只有配置文件路径匹配才是目标进程
            if (is_target) {
                LOG_INFO("找到目标进程 PID: %ld (参数完全匹配)", pid);
                target_pid = (int)pid;
            } 
            // 关键修改：移除 has_B_option 分支
            else {
                LOG_WARN("杀死非目标进程 PID: %ld (参数不匹配)", pid);
                if (kill(pid, SIGTERM) == 0) {
                    LOG_INFO("已发送 SIGTERM 到 PID: %ld", pid);
                    killed_processes++;
                } else {
                    LOG_ERROR("杀死进程失败: %s", strerror(errno));
                }
            }
        }
        fclose(cmdline_file);
    }
    closedir(dir);
    
    if (target_pid == 0) {
        if (killed_processes > 0) {
            LOG_INFO("已杀死 %d 个不匹配的 wpa_supplicant 进程", killed_processes);
            sleep(5);  // 确保进程完全终止
        } else {
            LOG_INFO("未找到运行的目标 wpa_supplicant 进程");
        }
    } else {
        LOG_INFO("返回目标 PID: %d", target_pid);
    }
    
    return target_pid;
}

int andlink_ctrl_wifi_callback(WIFI_CTRL_OPT_e opt, wifi_cfg_info_t *wificfg, char *outMsg, unsigned int msgBufSize)
{   
    LOG_DEBUG("enter andlink_ctrl_wifi_callback");

    if (opt == 1) {

        // 禁止连接移动闪连
        if (strcmp(wificfg->ssid, "CMCC-GUIDE-LINK") == 0) {
            LOG_DEBUG("连接移动闪连, 禁止");
            return -1;
        }

        // 检查 wlan0 是否已连接到目标 WiFi
        if (isWlan0ConnectedToSSID(wificfg->ssid)) {
            LOG_DEBUG("wlan0 已经连接到 WiFi: %s", wificfg->ssid);
            return 0;
        }

        LOG_DEBUG("wlan0 去连接 WiFi: %s", wificfg->ssid);
        dn_send_cmd_status = 6;
        sleep(2);
        std::string wifi_cmd;
        int result = 0;

        // 1. 断开 wlan0 连接
        wifi_cmd = "wpa_cli -i wlan0 disconnect";
        LOG_DEBUG("Executing command: %s", wifi_cmd.c_str());
        result = system(wifi_cmd.c_str());

        if (result == -1) {
            LOG_ERROR("Failed to execute disconnect command.");
        } else {
            int exit_status = WEXITSTATUS(result);
            if (exit_status != 0) {
                LOG_ERROR("Disconnect command exited with status: %d", exit_status);
            } else {
                LOG_DEBUG("Command executed disconnect successfully.");
            }
        }
        sleep(1);

        // 2. 重写wpa配置文件
        write_wpa_conf_file(wificfg);
        /*
        int user_bind = get_user_bind();
        if (user_bind == 0) {
            LOG_DEBUG("当前是绑定配网操作, 清除并重启网络服务");
            reset_network_manager();
        } else {
            LOG_DEBUG("当前设备已绑定, 是恢复网络操作, 不需要清除重启网络服务");
        }
        */
        
        // 3.判断是否后台有进程，没有重启wpa_supplicant
        if (get_wpa_supplicant_pid() == 0) {
            LOG_DEBUG("后台没有wpa_supplicant, 启动wpa_supplicant.");
            wifi_cmd = "wpa_supplicant -i wlan0 -B -c /etc/wpa_supplicant/wpa_supplicant-wlan0.conf";
            LOG_DEBUG("Executing command: %s", wifi_cmd.c_str());
            result = system(wifi_cmd.c_str());
        } else {
            LOG_DEBUG("后台已经有wpa_supplicant, 执行wpa_cli -i wlan0 reconfigure.");
            wifi_cmd = "wpa_cli -i wlan0 reconfigure"; 
            LOG_DEBUG("Executing command: %s", wifi_cmd.c_str());
            result = system(wifi_cmd.c_str());
                
            wifi_cmd = "wpa_cli -i wlan0 reconnect";
            LOG_DEBUG("Executing command: %s", wifi_cmd.c_str()); 
            result = system(wifi_cmd.c_str());            
        }
        sleep(5);
        
        // 4. 开始连接
        int retry_count = 0;
        int max_retries = 20;
        bool connect = false;
        char ipAddr[40] = { 0 };
        char brdAddr[40] = { 0 };
        
        if ((!isWlan0ConnectedToSSID(wificfg->ssid)) || (0 != check_device_ip_info(g_ifname, ipAddr, brdAddr))) {
            while (retry_count < max_retries) {
                /*
                wifi_cmd = "wpa_cli -i wlan0 reconnect";
                LOG_DEBUG("Connecting for exact SSID: %s (attempt %d/%d)", wificfg->ssid, retry_count+1, max_retries); 
                LOG_DEBUG("Executing command: %s", wifi_cmd.c_str());
                result = system(wifi_cmd.c_str());
                */
                sleep(2);
                if (result == -1) {
                    LOG_ERROR("Failed to execute connect command.");
                } else {
                    // 验证是否真正连接成功
                    if (isWlan0ConnectedToSSID(wificfg->ssid)) {
                        LOG_DEBUG("Wifi成功连上 %s.", wificfg->ssid);
                        if (0 == check_device_ip_info(g_ifname, ipAddr, brdAddr)) {
                            LOG_DEBUG("成功获取到ip %s, ipAddr %s.", ipAddr, brdAddr);
                            connect = true;
                            demo_set_device_ipaddr_for_callback(ipAddr, brdAddr);
                            break;
                        } else {
                            LOG_DEBUG("还未获取到ip."); 
                        }
                    } else {
                        LOG_ERROR("Wifi还未连上 %s.", wificfg->ssid);
                    }
                }
                retry_count++;
            }
            
            if (!connect) {
                LOG_ERROR("Failed after %d attempts", max_retries);
                dn_send_cmd_status = 7;
                char empty_ip[] = "";
                demo_set_device_ipaddr_for_callback(empty_ip, empty_ip);
                return -1;
            }
        }
            
        sleep(1);
        // === 修改 network.conf ===
        // 该部分代码已被注释，保留原样不修改

        // === 修改完成 ===

        // 获取当前路由表信息
        FILE *fp = popen("route -n", "r");
        if (!fp) {
            LOG_ERROR("Failed to run route command.");
            dn_send_cmd_status = 7;
            return -1;
        }

        char buffer[1024];
        std::string route_output;
        std::string gateway;
        while (fgets(buffer, sizeof(buffer), fp) != nullptr) {
            route_output += buffer;

            // 提取 wlan0 的网关地址
            if (strstr(buffer, g_ifname) && strstr(buffer, "UG")) {
                std::istringstream iss(buffer);
                std::string destination, gw, genmask, flags, iface;
                iss >> destination >> gw >> genmask >> flags >> iface;
                gateway = gw;
            }
        }
        pclose(fp);
        
        wifi_cmd = std::string("systemctl restart systemd-resolved");
        result = system(wifi_cmd.c_str());
        
        // 打印初次查询的路由表信息
        LOG_DEBUG("Initial Routing Table: \n%s", route_output.c_str());

        // 检查并调整 wlan0 的 metric 值
        /*
        if (!gateway.empty()) {
            std::string delete_cmd = "ip route del default via " + gateway + " dev wlan0";
            std::string add_cmd = "ip route add default via " + gateway + " dev wlan0 metric 60";
            system(delete_cmd.c_str());
            result = system(add_cmd.c_str());

            if (result == -1) {
                LOG_ERROR("Failed to modify wlan0 metric.");
            } 
        } else {
            LOG_ERROR("Failed to find wlan0 gateway in routing table.");
            return -1;
        }
        */
        
        // 发送调试数据到服务器
        // 该部分代码已被注释，保留原样不修改
        
        dn_send_cmd_status = 7;
        return 0;
    } else {
        LOG_DEBUG("opt!=1, do not try reconnect wifi");
        dn_send_cmd_status = 7;
        return 0;
    }
}

int andlink_get_device_ipaddr_callback(char *outip, char *outbrdAddr)
{
    DeviceInfo s_deviceInfo = {"", ""};
    int ret = -1;

    LOG_DEBUG("enter ysc_get_device_ipaddr_callback");
    

    // 如果 IP 和广播地址还未获取，使用 getifaddrs 获取
    if (strlen(s_deviceInfo.ip) == 0 || strlen(s_deviceInfo.brdAddr) == 0) {
        struct ifaddrs *interfaces = nullptr;
        struct ifaddrs *tempAddr = nullptr;

        // 获取本机的所有网络接口信息
        if (getifaddrs(&interfaces) == 0) {
            tempAddr = interfaces;

            while (tempAddr != nullptr) {
                if (tempAddr->ifa_addr && tempAddr->ifa_addr->sa_family == AF_INET) { // 只处理IPv4地址
                    // 匹配特定网口，例如 "wlan0"
                    if (strcmp(tempAddr->ifa_name, g_ifname) == 0) {
                        // 获取 IP 地址
                        struct sockaddr_in *sockaddr_ipv4 = (struct sockaddr_in *)tempAddr->ifa_addr;
                        inet_ntop(AF_INET, &sockaddr_ipv4->sin_addr, outip, INET_ADDRSTRLEN);

                        // 获取广播地址
                        if (tempAddr->ifa_broadaddr) {
                            struct sockaddr_in *sockaddr_brd = (struct sockaddr_in *)tempAddr->ifa_broadaddr;
                            inet_ntop(AF_INET, &sockaddr_brd->sin_addr, outbrdAddr, INET_ADDRSTRLEN);
                            LOG_DEBUG("Found IP address: %s, Broadcast address: %s", outip, outbrdAddr);
                        } else {
                            LOG_DEBUG("Found IP address: %s (no broadcast address)", outip);
                        }
                        ret = 0;
                        break; // 找到指定接口后退出循环
                    }
                }
                tempAddr = tempAddr->ifa_next;
            }
        } else {
            LOG_ERROR("Failed to get network interfaces information");
        }

        // 释放内存
        if (interfaces != nullptr) {
            freeifaddrs(interfaces);
        }
    } else {
        LOG_DEBUG("Using cached IP and broadcast address");
    }

    // 返回 IP 地址
    if (outip) {
        if (strlen(s_deviceInfo.ip)) {
            memcpy(outip, s_deviceInfo.ip, sizeof(s_deviceInfo.ip) - 1);
            LOG_DEBUG("Returning cached IP address: %s", outip);
            ret = 0;
        }
    }

    // 返回广播地址
    if (outbrdAddr) {
        if (strlen(s_deviceInfo.brdAddr)) {
            memcpy(outbrdAddr, s_deviceInfo.brdAddr, sizeof(s_deviceInfo.brdAddr) - 1);
            LOG_DEBUG("Returning cached broadcast address: %s", outbrdAddr);
            ret = 0;
        }
    }

    if (ret != 0) {
        LOG_ERROR("Failed to retrieve IP or broadcast address");
    }
    ret = 1;
    return ret;
}

void wait_for_subscriber(
    rclcpp::Publisher<std_msgs::msg::ByteMultiArray>::SharedPtr publisher,
    int timeout_seconds = 15)
{
    auto start = std::chrono::steady_clock::now();
    
    while (rclcpp::ok() && 
          (publisher->get_subscription_count() == 0) &&
          (std::chrono::steady_clock::now() - start < std::chrono::seconds(timeout_seconds)))
    {
        LOG_INFO("等待订阅者连接...");
        std::this_thread::sleep_for(std::chrono::milliseconds(500)); // 每500ms检查一次
    }
    
    if (publisher->get_subscription_count() > 0) {
        LOG_INFO("订阅者已连接");
    } else {
        LOG_WARN("订阅者已超时 (等待时间: %d 秒)", timeout_seconds);
    }
}
void AndlinkNode::handle_andlink_userkey_message(const std_msgs::msg::String::SharedPtr msg) {
    // 从msg中提取字符串（std_msgs::msg::String的成员是`data`）
    std::string userkey = msg->data;    
    // 打印或处理字符串
    RCLCPP_INFO(this->get_logger(), "Received userkey: %s", userkey.c_str());
    // setScanCodeBindConfigInfo(userkey, NULL, NULL);
    adlDeviceBinding(const_cast<char*>(userkey.c_str()), NULL);
}

AndlinkNode::AndlinkNode() : Node("andlink_node")
{
    RCLCPP_INFO(this->get_logger(), "Hello, andlink Node!");

    andlink_main();

    byte_stream_subscription_ = this->create_subscription<std_msgs::msg::ByteMultiArray>(
        "ble_byte_stream", 10,
        std::bind(&AndlinkNode::handle_ble_byte_stream_message, this, std::placeholders::_1));

    andlink_userkey_subscription_ = this->create_subscription<std_msgs::msg::String>(
        "andlink_userkey",  // 
        10,                 // 队列长度
        std::bind(&AndlinkNode::handle_andlink_userkey_message, this, std::placeholders::_1)
    );

    andlink_cmd_subscription_ = this->create_subscription<std_msgs::msg::ByteMultiArray>(
        "andlink_cmd", 10,
        std::bind(&AndlinkNode::handle_andlink_cmd_message, this, std::placeholders::_1));

    ble_cmd_publisher_ = this->create_publisher<std_msgs::msg::ByteMultiArray>("ble_cmd", 10);
    wait_for_subscriber(ble_cmd_publisher_);
    andlink_network_publisher_ = this->create_publisher<std_msgs::msg::String>("andlink_network", 10);
    homi_player_publisher_ = this->create_publisher<homi_speech_interface::msg::SIGCEvent>("/homi_speech/sigc_event_topic", 10);
    // 初始化1秒定时器
    timer_ = this->create_wall_timer(
        std::chrono::seconds(1),
        std::bind(&AndlinkNode::handle_timer_callback, this));

    // 新增定时器
    // log_timer_ = this->create_wall_timer(
    //     std::chrono::seconds(2),
    //     std::bind(&AndlinkNode::log_timer_callback, this));

    // 新增字节流发布器
    // byte_stream_publisher_ = this->create_publisher<std_msgs::msg::ByteMultiArray>("ble_byte_stream", 10);
    // byte_stream_timer_ = this->create_wall_timer(
    //     std::chrono::seconds(10),
    //     std::bind(&AndlinkNode::publish_byte_stream_message, this));

    // 新增发布器
    // json_publisher_ = this->create_publisher<std_msgs::msg::String>("json_topic", 10);

    // // 新增定时器，用于定期发送 JSON 格式字符串
    // json_timer_ = this->create_wall_timer(
    //     std::chrono::seconds(5),
    //     std::bind(&AndlinkNode::publish_json_message, this));
}

void AndlinkNode::handle_timer_callback()
{
    if(dn_send_cmd_status == 1)
    {
        dn_send_cmd_status = 0;
        RCLCPP_INFO(this->get_logger(), "Timer triggered dn_send_cmd_status == 1.");
        publish_ble_cmd_unbind_message();
    }
    else if(dn_send_cmd_status == 2)
    {
        dn_send_cmd_status = 0;
        RCLCPP_INFO(this->get_logger(), "Timer triggered dn_send_cmd_status == 2.");
        publish_ble_cmd_broadcast_message();
    }
    else if(dn_send_cmd_status == 3)
    {
        dn_send_cmd_status = 0;
        RCLCPP_INFO(this->get_logger(), "Timer triggered dn_send_cmd_status == 3.");
        publish_ble_cmd_unbroadcast_message();
    }
    else if(dn_send_cmd_status == 4)
    {
        dn_send_cmd_status = 0;
        RCLCPP_INFO(this->get_logger(), "Timer triggered dn_send_cmd_status == 4.");
        // publish_ble_cmd_unbroadcast_message();
    }
    else if(dn_send_cmd_status == 6)
    {
        dn_send_cmd_status = 0;
        RCLCPP_INFO(this->get_logger(), "Andlink开始联网, dn_send_cmd_status == 6.");
        publish_network_connecting_start_message();
    }
    else if(dn_send_cmd_status == 7)
    {
        dn_send_cmd_status = 0;
        RCLCPP_INFO(this->get_logger(), "Andlink结束联网, dn_send_cmd_status == 6.");
        publish_network_connecting_end_message();
    }
    else if(dn_send_cmd_status == 8)
    {
        dn_send_cmd_status = 0;
        RCLCPP_INFO(this->get_logger(), "收到绘本管控指令, dn_send_cmd_status == 8.");
        if (g_json_str) {
            auto msg = homi_speech_interface::msg::SIGCEvent();
            msg.event = g_json_str;
            homi_player_publisher_->publish(msg);
            free(g_json_str);
            g_json_str = nullptr;
        }
        
    }
    // else
    // {
    //     RCLCPP_INFO(this->get_logger(), "Timer triggered dn_send_cmd_status == 1.");
    // }
    // 在此处添加定时器触发时的处理逻辑
}

int AndlinkNode::andlink_main()
{
    CFG_NET_MODE_e mode = NETWOKR_MODE_BT;

    this->declare_parameter<std::string>("wifi_connect_interface", "wlan0");
    std::string  wifi_connect_interface = this->get_parameter("wifi_connect_interface").as_string();
    RCLCPP_INFO(rclcpp::get_logger("andlink_node"), "wifi_connect_interface: %s", wifi_connect_interface.c_str());

    this->declare_parameter<std::string>("p2p_connect_interface", "p2p0");
    std::string  p2p_connect_interface = this->get_parameter("p2p_connect_interface").as_string();
    RCLCPP_INFO(rclcpp::get_logger("andlink_node"), "p2p_connect_interface: %s", p2p_connect_interface.c_str());


    strcpy(g_ifname,wifi_connect_interface.c_str());
    strcpy(g_p2p_ifname,p2p_connect_interface.c_str());




    /************ 2.加载Andlink SDK ************/
    if (0 != demo_startAndlinkSdk(mode, (char *)g_ifname))
    {
        DEMO_DBG_PRINT("demo_startAndlinkSdk failed\r\n");
    }
    return 0;
}
int AndlinkNode::demo_startAndlinkSdk(CFG_NET_MODE_e mode, char *ifname)
{
    // 1.设置设备andlink基本信息
    static adl_dev_attr_t devAttr;
    devAttr.cfgNetMode = NETWOKR_MODE_BT;
    devAttr.deviceVendor = const_cast<char*>("");
    devAttr.deviceType = const_cast<char*>("");
    devAttr.id = const_cast<char*>("");
    devAttr.andlinkToken = const_cast<char*>("");
    devAttr.productToken = const_cast<char*>("");
    devAttr.firmWareVersion = const_cast<char*>("");
    devAttr.softWareVersion = const_cast<char*>("");
    devAttr.cfgPath = const_cast<char*>("");

    // 恢复厂商设备配置文件,一旦恢复失败,重新构造
    if (0 != recoverFacDevinfoCfg(&devAttr))
    {
        DEMO_DBG_PRINT("recoverFacDevinfoCfg failed\n");
        buildFacDevinfoCfgFile(ifname, &devAttr);
    }
    devAttr.version = ADL_EXPORT_API_VERSION;
    devAttr.cfgNetMode = mode;

    RCLCPP_INFO(this->get_logger(), "[enter]demo_startAndlinkSdk,cfgNetMode =%d,ifname =%s\n", devAttr.cfgNetMode, ifname);

    // 2.设置andlink回调接口
    static adl_dev_callback_t devCbs;
    devCbs.version = ADL_EXPORT_API_VERSION;
    devCbs.scan_wifi_callback = demo_scan_wifi_callback;
    // devCbs.ctrl_wifi_callback = demo_ctrl_wifi_callback;
    devCbs.ctrl_wifi_callback = andlink_ctrl_wifi_callback;
    devCbs.set_andlink_status_callback = demo_set_andlink_status_callback;
    devCbs.set_extfunc_notify_callback = NULL;
    devCbs.dn_send_cmd_callback = demo_dn_send_cmd_callback;
    devCbs.dev_paramsSync_callback = demo_dev_paramsSync_callback;
    devCbs.download_upgrade_version_callback = NULL;
    // devCbs.download_upgrade_version_callback = demo_download_upgrade_version_callback;
    devCbs.upgrade_version_callback = demo_upgrade_version_callback;
    // devCbs.get_device_ipaddr = demo_get_device_ipaddr_callback;
    devCbs.get_device_ipaddr = andlink_get_device_ipaddr_callback;
    devCbs.reset_device_Ipaddr = demo_reset_device_Ipaddr_callback;
    devCbs.getCfg_callback = NULL;
    devCbs.setCfg_callback = NULL;
    devCbs.get_dmInfo_callback = demo_get_dmInfo_callback;
    devCbs.get_extInfo_callback = demo_get_extInfo_callback;
    devCbs.set_voice_notify_callback = NULL;
    devCbs.ble_start_server_callback = demo_ble_start_server_callback;
    devCbs.ble_stop_server_callback = demo_ble_stop_server_callback;
    devCbs.ble_send_callback = demo_ble_send_callback;
    devCbs.ctrl_scanCode_callback = NULL;

    // 3.检查设备是否联网
    char ipAddr[40] = { 0 };
    char brdAddr[40] = { 0 };
    if (0 != check_device_ip_info(ifname, ipAddr, brdAddr))
    {
        RCLCPP_INFO(this->get_logger(),"device has NOT been connected to the network!!!\n");
        char empty_ip[] = "";
        demo_set_device_ipaddr_for_callback(empty_ip, empty_ip);
    }
    else
    {
        RCLCPP_INFO(this->get_logger(),"device has been connected to the network!\n");
        demo_set_device_ipaddr_for_callback(ipAddr, brdAddr);
    }

    RCLCPP_INFO(this->get_logger(),"andlink_init========start");
    RCLCPP_INFO(this->get_logger(),"##############################################################################");
    RCLCPP_INFO(this->get_logger(),"andlinksdk demo   version:%s", adlDemoVersionGet());
    RCLCPP_INFO(this->get_logger(),"andlinksdk lib    version:%s", getAndlinkVersion());
    RCLCPP_INFO(this->get_logger(),"andlinksdk DM lib version:%s", getAndlinkVersion());
    RCLCPP_INFO(this->get_logger(),"##############################################################################");

    // 4.设置特殊的启动选项
    // 4.1设置andlink单个日志文件阈值;若不设置,默认单个日志文件最大512KB,即andlink诊断功能的日志文件占用空间最大为512KB*2;
    // setAndlinkLogMaxSize(0x80000);

    // 4.2设置andlink 日志文件存储路径;若不设置,默认是存储在/tmp/andlink目录下.
    // setAndlinkLogFilePath("/tmp/andlink1");

    // 4.4若是扫码绑定的设备,可以关闭APP发现服务;
    disableAdlFunc(ADL_APP_SEARCH_SERVICE);

    // 4.5若存在离线解绑后,需要立马绑定的场景时,可以禁止自动默认用户注册功能;
    disableAdlFunc(ADL_OFFLINE_UNBIND_AUTO_REBOOTSTRAP);

    // 4.6若是可视门锁、守护台灯等设备,使用扫码配网,可以调用下列接口禁止闪联配网和手工SoftAP或BLE配网
    disableAdlFunc(ADL_FLASHLINK_CONFIG_NET);
    // disableAdlFunc(ADL_MANUAL_CONFIG_NET);

    // 4.7业务连接优先使能,若是泛摄像头品类需要使能此接口
    // enableAdlFunc(ADL_BUSINESS_CONN_FIRST)

    // 4.8设置特定地域服务器地址,比如设置非大陆Andlink服务地址,测试环境地址等;
    // 若产品需要销往非大陆地区,需要调用此接口设置非大陆地区的Andlink服务器地址.
    // setAndlinkServAddress("http://************:8085");

    // 4.9设置OTA 文件下载路径及文件名称;设置OTA下载策略(1分片下载;0整包下载)
    // setOtaStoragePathAndFilename("/tmp", "ota.zip");
    // setOtaDownloadPolicy(1, 2*1024*1024);

    // 4.10设置诊断服务器地址,SDK使用者一般无需关心
    // setAndlinkDgsServUrl("http://************:8085/dev/anddgs/chkdgs");

    /*
    5.启动andlink SDK(立即返回,内部不会阻塞)
    注意:sdk不会申请内存存储devAttr和devCbs,只会保存其指针;
    因此入参所需要的空间需要调用者申请且SDK运行期间不能释放这两个指针;假设用户需要调用andlink_destroy使SDK消亡时,需要释放这两个指针;
    */
    return andlink_init(&devAttr, &devCbs);
}

// private:
void AndlinkNode::handle_andlink_cmd_message(const std_msgs::msg::ByteMultiArray::SharedPtr msg)
{
    // int ret = 0;
    // 获取数据指针
    uint8_t* data_ptr = msg->data.data();
    size_t data_size = msg->data.size();
    RCLCPP_INFO(this->get_logger(), "Received byte stream of size: %zu", data_size);

    if(data_size >= 4)
    {
        if((data_ptr[0] == 0x55) && (data_ptr[1] == 0x55) && (data_ptr[2] == 0x00) )
        {
            if(data_ptr[3] == 0x02)
            {
                // 使用子线程调用 devReset
                std::thread([this]() {
                    devReset();
                    // RCLCPP_INFO(this->get_logger(), "devReset : %d", ret);
                }).detach();
            }
        }
    }

    // 打印数据
    for (size_t i = 0; i < data_size; ++i) {
        printf("%02X ", data_ptr[i]);
    }
    printf("\n");
}

void AndlinkNode::handle_ble_byte_stream_message(const std_msgs::msg::ByteMultiArray::SharedPtr msg)
{
    // int ret = 0;

    // 获取数据指针
    uint8_t* data_ptr = msg->data.data();
    size_t data_size = msg->data.size();
    RCLCPP_INFO(this->get_logger(), "Received byte stream of size: %zu", data_size);

    std::ostringstream oss;
    for (size_t i = 0; i < data_size; ++i) {
        oss << "0x" << std::hex << std::uppercase << static_cast<int>(data_ptr[i]) << " ";
    }
    RCLCPP_INFO(this->get_logger(), "Byte stream content: %s", oss.str().c_str());

    adlBleRecvHandler(data_ptr, data_size);
}

void AndlinkNode::log_timer_callback()
{
    RCLCPP_INFO(this->get_logger(), "Log timer triggered every 2 seconds.");
}

// void AndlinkNode::publish_byte_stream_message()
// {
//     auto message = std_msgs::msg::ByteMultiArray();
//     // 填充字节流数据
//     message.data = {0x01, 0x02, 0x03, 0x04, 0x05};
//     RCLCPP_INFO(this->get_logger(), "Publishing byte stream of size: %zu", message.data.size());
    
//     // 打印字节流内容
//     std::string byte_stream;
//     for (const auto &byte : message.data) {
//         byte_stream += "0x" + to_hex(byte) + " ";
//     }
//     RCLCPP_INFO(this->get_logger(), "Byte stream content: %s", byte_stream.c_str());

//     byte_stream_publisher_->publish(message);
// }


void AndlinkNode::publish_ble_cmd_broadcast_message()
{
    auto message = std_msgs::msg::ByteMultiArray();
    // 填充字节流数据
    message.data = {0x55, 0x55, 0x00, 0x04};
    RCLCPP_INFO(this->get_logger(), "publish_ble_cmd_broadcast_message of size: %zu", message.data.size());
    
    // 打印字节流内容
    std::string byte_stream;
    for (const auto &byte : message.data) {
        byte_stream += "0x" + to_hex(byte) + " ";
    }
    RCLCPP_INFO(this->get_logger(), "Byte stream content: %s", byte_stream.c_str());

    ble_cmd_publisher_->publish(message);
}

void AndlinkNode::publish_ble_cmd_unbroadcast_message()
{
    auto message = std_msgs::msg::ByteMultiArray();
    // 填充字节流数据
    message.data = {0x55, 0x55, 0x00, 0x05};
    RCLCPP_INFO(this->get_logger(), "publish_ble_cmd_unbroadcast_message of size: %zu", message.data.size());
    
    // 打印字节流内容
    std::string byte_stream;
    for (const auto &byte : message.data) {
        byte_stream += "0x" + to_hex(byte) + " ";
    }
    RCLCPP_INFO(this->get_logger(), "Byte stream content: %s", byte_stream.c_str());

    ble_cmd_publisher_->publish(message);
}

void AndlinkNode::publish_ble_cmd_unbind_message()
{
    auto message = std_msgs::msg::ByteMultiArray();
    // 填充字节流数据
    message.data = {0x55, 0x55, 0x00, 0x03};
    RCLCPP_INFO(this->get_logger(), "publish_ble_cmd_unbind_message of size: %zu", message.data.size());
    
    // 打印字节流内容
    std::string byte_stream;
    for (const auto &byte : message.data) {
        byte_stream += "0x" + to_hex(byte) + " ";
    }
    RCLCPP_INFO(this->get_logger(), "Byte stream content: %s", byte_stream.c_str());

    ble_cmd_publisher_->publish(message);
}

void AndlinkNode::publish_network_connecting_start_message()
{
    auto message = std_msgs::msg::String();
    message.data = "notify_userbind_start";
    andlink_network_publisher_->publish(message);
    RCLCPP_INFO(this->get_logger(), "发送给网络服务消息 notification: '%s'", message.data.c_str());
}

void AndlinkNode::publish_network_connecting_end_message()
{
    auto message = std_msgs::msg::String();
    message.data = "notify_userbind_end";
    andlink_network_publisher_->publish(message);
    RCLCPP_INFO(this->get_logger(), "发送给网络服务消息 notification: '%s'", message.data.c_str());
}

std::string AndlinkNode::to_hex(uint8_t byte)
{
    std::ostringstream oss;
    oss << std::hex << std::uppercase << static_cast<int>(byte);
    return oss.str();
}

// 新增方法：发布 JSON 格式字符串
void AndlinkNode::publish_json_message()
{
    auto message = std_msgs::msg::String();
    message.data = R"({"key1": "value1", "key2": 123, "key3": true})"; // 示例 JSON 数据
    RCLCPP_INFO(this->get_logger(), "Publishing JSON: '%s'", message.data.c_str());
    json_publisher_->publish(message);
}

