import rclpy
import struct
import os
from rclpy.node import Node
from std_msgs.msg import ByteMultiArray
from std_msgs.msg import String
from .ble_manager import Ble_manager
from .ble_data_process import DataProcessor
from homi_speech_interface.msg  import SIGCEvent
from .read import DeviceConfig
from homi_speech_interface.srv import SIGCData
import time
import threading
import configparser
import subprocess
import re

class ByteMultiArrayPublisher(Node):
    def __init__(self):
        super().__init__('ble_node')
        # 初始化完成后自动生成配置文件
        self.generate_fac_devinfo() 
        self.get_logger().info("Initializing ByteMultiArrayPublisher...")
        self.ble_manager = None
        self.andlink_reset = None
        self.wifi_info = {'connected': False, 'ssid': None, 'password': None}
        self.ble_stream_publisher_ = self.create_publisher(ByteMultiArray, 'ble_byte_stream', 10)
        self.andlink_cmd_publisher_ = self.create_publisher(ByteMultiArray, 'andlink_cmd', 10)
        
        self.andlink_network_publisher = self.create_publisher(String, 'andlink_network', 10)
        self.app_publisher = self.create_publisher(SIGCEvent, '/homi_speech/sigc_event_topic_APP', 10)

        self.robot_control_service = self.create_service(SIGCData, '/homi_speech/sigc_data_service_APP', self.handle_robot_control_service_request)

        self.subscription = self.create_subscription(
            ByteMultiArray,
            'ble_cmd',
            self.listener_ble_cmd_callback,
            10
        )
        self.subscription  # 防止垃圾回收
        self.get_logger().info('Subscriber Node has been started.')

        self.timer_period = 65.0  # 定时器周期（秒）
        self.timer = None
        self.timer_running = False
        self.declare_parameter('wifi_connect_interface', "wlan0")
        self.wifi_interface = self.get_parameter('wifi_connect_interface').value

        self.declare_parameter('mobile_connect_interface', "eth2")
        self.mobile_interface = self.get_parameter('mobile_connect_interface').value
        # self.start_ble_bind_timer()
        # self.start_ble_bind_timer()

        self.user_bind_timer = self.create_timer(5.0, self.update_advertisement)  # 每5秒更新一次广播
        self.get_logger().info("ByteMultiArrayPublisher initialized successfully.")
        self.config_path = '/etc/cmcc_robot/andlinkSdk.conf'  # 配置文件路径
        self.last_user_bind = self.get_user_bind()   # 保存上一次的 user_bind 状态

    def get_user_bind(self):
        """获取 user_bind 的值"""
        try:
            if os.path.exists(self.config_path):
                self.get_logger().info(f"Reading configuration from {self.config_path}...")
                device_config = DeviceConfig(self.config_path)
                user_bind = device_config.get_config_value('userBind')
                self.get_logger().info(f"Current userBind value: {user_bind}")
                # 如果 user_bind 不等于 '1'，返回 '0'
                return user_bind if user_bind == '1' else '0'
            else:
                self.get_logger().warning(f"Configuration file {self.config_path} does not exist.")
                return '0'
        except Exception as e:
            self.get_logger().error(f"Error occurred while getting userBind: {e}")
            return '0'

    def process_reset_command(self, action: str = "remove_new"):
        """处理重置命令的逻辑"""
        self.get_logger().info('发送复位信号, self.andlink_reset = False.')
        self.andlink_reset = False
        self.ble_manager.update_advertisement(True)
        #self.ble_manager.disconnect_wifi(self.wifi_interface)
        # self.ble_manager.disable_network_interface(self.mobile_interface)
        self.ble_manager.remove_andlinkSdkConfig_file(action)
        time.sleep(2)
        self.publish_andlink_cmd_reset()

    def listener_ble_cmd_callback(self, msg):
        self.get_logger().info(f"Received BLE command: {list(msg.data)}")
        self.get_logger().info(f'Received ByteMultiArray: {list(msg.data)}')
        if list(msg.data)[0] == b'U' and list(msg.data)[1] == b'U' and list(msg.data)[2] == b'\x00':
            self.get_logger().info("Valid BLE command received.")
            if list(msg.data)[3] == b'\x09':
                self.get_logger().info("Reset command detected. Executing reset sequence.")
                self.process_reset_command("remove_new")
            elif list(msg.data)[3] == b'\x05':
                self.get_logger().info('收到andlink unbroadcast消息, 关闭广播01, 改为广播03, self.andlink_reset = False.')
                self.andlink_reset = False
                self.ble_manager.update_advertisement(True)
            elif list(msg.data)[3] == b'\x04':
                self.get_logger().info("Advertisement enable command detected.")
                self.get_logger().info('成功收到andlink的复位信号回复, self.andlink_reset = True.')
                self.andlink_reset = True
                self.ble_manager.update_advertisement(True)


    def publish_byte_stream(self, publisher, stream_data):
        self.get_logger().info(f"Publishing byte stream: {list(stream_data)}")
        self.get_logger().info(f'Preparing to publish byte stream: {list(stream_data)}')
        msg = ByteMultiArray()
        msg.data = [bytes([b]) for b in stream_data]
        publisher.publish(msg)
        self.get_logger().info(f'Published byte stream: {list(msg.data)}')
        self.get_logger().info("Byte stream published successfully.")
    

    def publish_andlink_cmd_reset(self):
        self.get_logger().info("Publishing andlink reset command.")
        stream_data = bytes([0x55,0x55,0x00,0x02])
        self.publish_byte_stream(self.andlink_cmd_publisher_,stream_data)
        self.get_logger().info("Andlink reset command published.")

    def publish_app_cmd(self,stream_data):
        self.sigcevent_msg=SIGCEvent()
        self.sigcevent_msg.event = stream_data
        self.app_publisher.publish(self.sigcevent_msg)

    def publish_internet_status(self, is_connected):
        msg = String()
        msg.data = f'{{"isInternetConnect": "{str(is_connected).lower()}"}}'
        self.internet_status_publisher_.publish(msg)
        self.get_logger().info(f'Published internet status: {msg.data}')

    def set_ble_manager(self,ble_manager):
        self.ble_manager = ble_manager

    def reconnect_to_wifi(self, max_retries=3):
        """尝试重新连接到之前保存的Wi-Fi网络，最多重试3次"""
        if not self.wifi_info or not self.wifi_info['connected'] or not self.wifi_info['ssid']:
            self.get_logger().warning("没有有效的Wi-Fi信息可用于重新连接")
            return False
        
        ssid = self.wifi_info['ssid']
        password = self.wifi_info['password']
        
        self.get_logger().info(f"尝试重新连接到Wi-Fi: {ssid}，最多重试 {max_retries} 次")
        self.send_userbind_notification("start")
        try:
            # 1. 先断开当前连接
            disconnect_cmd = "sudo nmcli device disconnect wlan0"
            self.get_logger().info(f"断开当前Wi-Fi连接: {disconnect_cmd}")
            disconnect_result = subprocess.run(
                disconnect_cmd,
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                timeout=10
            )
            
            if disconnect_result.returncode != 0:
                self.get_logger().warning(f"断开连接失败: {disconnect_result.stderr}")
            else:
                self.get_logger().info("成功断开当前Wi-Fi连接")
            
            # 等待1秒让设备重置
            time.sleep(1)
        except Exception as e:
            self.get_logger().warning(f"断开连接时发生错误: {str(e)}")
            # 继续尝试连接，即使断开失败

        for attempt in range(1, max_retries + 1):
            self.get_logger().info(f"尝试 #{attempt}/{max_retries}: 连接到 {ssid}")
            
            try:
                # 2. 尝试连接新网络
                connect_cmd = f"sudo nmcli dev wifi connect '{ssid}'"
                if password:
                    connect_cmd += f" password '{password}'"
                
                self.get_logger().info(f"执行连接命令: {connect_cmd}")
                connect_result = subprocess.run(
                    connect_cmd, 
                    shell=True, 
                    stdout=subprocess.PIPE, 
                    stderr=subprocess.PIPE,
                    text=True,
                    timeout=30
                )

                if connect_result.stdout:
                    self.get_logger().info(f"连接命令输出: {connect_result.stdout.strip()}")

                # 3. 检查连接结果
                if connect_result.returncode == 0:
                    self.get_logger().info(f"成功重新连接到Wi-Fi: {ssid}")
                    self.send_userbind_notification("end")
                    return True
                else:
                    self.get_logger().warning(f"连接尝试 #{attempt} 失败: {connect_result.stderr}")
                    
                    # 等待一段时间后重试（如果还有尝试机会）
                    if attempt < max_retries:
                        retry_delay = 2  # 2秒后重试
                        self.get_logger().info(f"等待 {retry_delay} 秒后重试...")
                        time.sleep(retry_delay)
                    
            except subprocess.TimeoutExpired as e:
                self.get_logger().warning(f"连接尝试 #{attempt} 超时: {str(e)}")
                if attempt < max_retries:
                    self.get_logger().info("等待5秒后重试...")
                    time.sleep(5)
            except Exception as e:
                self.get_logger().error(f"连接尝试 #{attempt} 发生意外错误: {str(e)}")
                if attempt < max_retries:
                    self.get_logger().info("等待5秒后重试...")
                    time.sleep(5)
        
        self.get_logger().error(f"所有 {max_retries} 次连接尝试均失败")
        self.send_userbind_notification("end")
        return False

    def timer_ble_bind_callback(self):
        self.get_logger().info("BLE bind timer triggered.")
        self.get_logger().info('Timer triggered for BLE bind.')
        self.ble_manager.disconnect_all_devices()
        user_bind = self.get_user_bind()
        if user_bind is None or user_bind != '1':
            self.get_logger().info('[绑定失败]user_bind不为1，未绑成功，同时计时器到达时间周期，重置绑定流程.')
            #self.send_userbind_notification("end")
            #self.reconnect_to_wifi(max_retries=3)
            self.process_reset_command("remove_new")
        self.stop_ble_bind_timer()
        self.get_logger().info('关闭定时器.') 
        self.get_logger().info("BLE bind timer callback completed.")

    def stop_ble_bind_timer(self):
        self.get_logger().info('Attempting to stop BLE bind timer.')
        if self.timer is not None:
            self.timer.cancel()
            self.get_logger().info('Timer has been stopped.')
        self.timer_running = False

    def get_wifi_info(self, interface='wlan0'):
        """
        获取指定无线接口的连接信息
        :param interface: 无线接口名称 (默认为wlan0)
        :return: 包含连接状态、SSID和密码的字典
        """
        self.get_logger().info("[WIFI_INFO] Entering get_wifi_info")
        #self.send_userbind_notification("start")
        result = {
            'connected': False,
            'ssid': None,
            'password': None
        }
        
        # 1. 检查连接状态并获取SSID
        try:
            cmd = f"iw dev {interface} link"
            output = subprocess.check_output(cmd, shell=True, stderr=subprocess.STDOUT, text=True)
            
            # 检查是否未连接
            if "Not connected" in output:
                self.get_logger().info(f"[WIFI_INFO] Interface {interface} is not connected")
                self.wifi_info = result
                return
                
            # 查找SSID行
            ssid_match = re.search(r'SSID: (.+)', output)
            if ssid_match:
                ssid = ssid_match.group(1).strip()
                result['connected'] = True
                result['ssid'] = ssid
                self.get_logger().info(f"[WIFI_INFO] Connected to SSID: {ssid}") 
                
                # 2. 尝试获取密码
                try:
                    # 使用nmcli获取密码
                    cmd = f"sudo nmcli -s --show-secrets -g 802-11-wireless-security.psk connection show '{ssid}'"
                    password = subprocess.check_output(cmd, shell=True, text=True).strip()
                    
                    if password:
                        result['password'] = password
                        self.get_logger().info(f"[WIFI_INFO][SUCCESS] Retrieved password for {ssid}")
                    else:
                        self.get_logger().info(f"[WIFI_INFO][WARNING] Password not found for {ssid}")
                        
                except subprocess.CalledProcessError:
                    self.get_logger().info(f"[WIFI_INFO][ERROR] Failed to retrieve password for {ssid}")
                    
            else:
                self.get_logger().info(f"[WIFI_INFO][WARNING] Connected but no SSID information found for {interface}")
                
        except subprocess.CalledProcessError as e:
            if "No such device" in e.output:
                self.get_logger().info(f"[WIFI_INFO][ERROR] Interface {interface} does not exist")
            elif "Operation not supported" in e.output:
                self.get_logger().info(f"[WIFI_INFO][ERROR] Wireless operation not supported on {interface}")
            else:
                self.get_logger().info(f"[WIFI_INFO][ERROR] Command failed: {e.output}")
        
        self.wifi_info = result
        return

    def start_ble_bind_timer(self):
        self.get_logger().info('[绑定开始]Attempting to start BLE bind timer.')
        #wifi_thread = threading.Thread(target=self.get_wifi_info, args=('wlan0',))
        #wifi_thread.daemon = True
        #wifi_thread.start()
        if not self.is_timer_stopped():
            self.stop_ble_bind_timer()
        self.timer = self.create_timer(self.timer_period, self.timer_ble_bind_callback)
        self.timer_running = True
        self.get_logger().info('Timer has been started.')

    def is_timer_stopped(self):
        return not self.timer_running

    def handle_robot_control_service_request(self, req, response):
        self.get_logger().info(f"Handling robot control service request: {req.data}")
        self.get_logger().info(f"Received service request: {req.data}")
        user_bind = self.get_user_bind()  # 调用独立函数获取 user_bind
        self.get_logger().info(f"Handling robot control service request. Current userBind: {user_bind}")
        if req.data == "unbind_notify":
            andlink_oldconf = os.path.exists("/etc/cmcc_robot/andlinkSdk_old.conf")
            if user_bind == '0' and andlink_oldconf == False:
                self.get_logger().info(f"user_bind=False, andlink_oldconf=False, 当前就是APP解绑状态,不用重复处理APP解绑")
                response.error_code = 1
                return response
        if req.data == "unbind_notify" or req.data == "unbind_notify_voice":
            self.get_logger().info("Unbind notify request detected. Executing unbind sequence.")
            self.ble_manager.disconnect_all_devices()
            self.stop_ble_bind_timer()
            time.sleep(5)
            if user_bind == '1':
                if req.data == "unbind_notify_voice":
                    self.get_logger().info("音解绑. UserBind is still '1'. move andlinkSdk.conf to andlinkSdk_old.conf. Executing reset command.")
                    self.process_reset_command("move")
                if req.data == "unbind_notify":
                    self.get_logger().info("App解绑. UserBind is still '1'. remove andlinkSdk.conf and andlinkSdk_old.conf. Executing reset command.")
                    self.process_reset_command("remove_all")
            elif req.data == "unbind_notify":
                self.get_logger().info("App解绑. UserBind is not '1'. Only remove andlinkSdk_old.conf. Executing reset command.")
                self.process_reset_command("remove_old")
            elif req.data == "unbind_notify_voice":
                self.get_logger().info("语音解绑. UserBind is not '1'. Only Executing reset command.")
                self.process_reset_command("remove_new")
            response.error_code = 0
            return response
        else:
            self.get_logger().warning(f"Unhandled service request: {req.data}")
            response.error_code = 1
            return response
    def execute_shell_command(self,command):
        try:
            result = subprocess.run(command, capture_output=True, text=True, check=True)
            return result.stdout
        except subprocess.CalledProcessError as e:
            return f"Error: {e.stderr}"

    def send_userbind_notification(self, status):
        msg = String()
        if status == "start":
            msg.data = "notify_userbind_start"
        elif status == "end":
            msg.data = "notify_userbind_end"
        else:
            self.get_logger().warn(f"未知的通知状态: {status}")
            return
            
        self.andlink_network_publisher.publish(msg)
        self.get_logger().info(f"已发送通知Network服务: {msg.data}")

    def update_advertisement(self):
        self.get_logger().info("Starting advertisement update process...")
        try:
            user_bind = self.get_user_bind()  # 调用独立函数获取 user_bind

            # 检查 user_bind 是否与上一次状态不一致且不等于 '1'
            if user_bind != self.last_user_bind:
                self.get_logger().info(f"userBind has changed from {self.last_user_bind} to {user_bind}.")
                if user_bind == '1' and self.last_user_bind != '1':
                    command = ['rm', '/etc/cmcc_robot/andlinkSdk_old.conf']
                    self.execute_shell_command(command)
                    # self.send_userbind_notification("end")
                    self.get_logger().info("绑定成功，Removed andlinkSdk_old.conf")
                if user_bind != '1':
                    self.get_logger().info("userBind is not '1'. Executing reset command.")
                    # self.process_reset_command()
                else:
                    self.ble_manager.enable_network_interface(self.mobile_interface)

            # 更新 last_user_bind
            self.last_user_bind = user_bind

            if user_bind == '1':
                self.get_logger().info("userBind is '1'. Disabling advertisement.")
                self.ble_manager.update_advertisement(False)
            else:
                self.get_logger().info("userBind is not '1'. Checking timer status...")
                if self.is_timer_stopped():
                    self.get_logger().info("Timer is stopped. Proceeding with advertisement update.")
                    self.get_logger().info("Enabling advertisement.")
                    self.ble_manager.update_advertisement(True)
                else:
                    self.get_logger().info("Timer is running. Skipping advertisement update.")
        except Exception as e:
            self.get_logger().error(f"Error occurred during advertisement update: {e}")
        self.get_logger().info("Advertisement update process completed.")

    def generate_fac_devinfo(self):
        """生成/更新facDevinfo.conf"""
        input_ini_path = "/etc/cmcc_robot/cmcc_dev.ini"
        output_conf_path = "/etc/cmcc_robot/facDevinfo.conf"

        try:
            # 读取原始配置文件（逻辑保持不变）
            config = configparser.ConfigParser()
            config.read(input_ini_path)
            required_keys = ['devType', 'devSn', 'devCmei', 'OVDLoginPassword']
            if not all(config['factory'].get(key) for key in required_keys):
                raise ValueError("cmcc_dev.ini缺少必要字段")
            
            current_values = {
                'deviceType': config['factory']['devType'],
                'id': config['factory']['devSn'],
                'sn': config['factory']['devSn'],
                'cmei': config['factory']['devCmei'],
                'license': config['factory']['OVDLoginPassword']
            }

            need_update = False
            if os.path.exists(output_conf_path):
                device_config2 = DeviceConfig(output_conf_path)                
                # 检查字段一致性
                for key in ['deviceType', 'id', 'sn', 'cmei', 'license']:
                    if device_config2.get_config_value(key) != current_values[key]:
                        self.get_logger().warning(f"字段 {key} 不一致，触发更新")
                        need_update = True
                        break
            else:
                need_update = True

            if not need_update:
                self.get_logger().info(f"{output_conf_path} 已存在且字段一致，无需操作")
                return

            # 构建新配置内容
            conf_content = f"""deviceType={current_values['deviceType']}
productToken=iov0DF2snz711HMK
andlinkToken=RhcTU7W6Pgd86uZC
deviceVendor=test
id={current_values['id']}
firmWareVersion=v1.0
softWareVersion=v2.0
cfgPath=/etc/cmcc_robot
mac=000C2999D25D
wlanMac=FF0822A08E00
sn={current_values['sn']}
cmei={current_values['cmei']}
license={current_values['license']}
otaPolicy=0
otaFragSize=2097152
otaFilePath=/tmp
otaFileName=ota.zip
"""

            # 强制写入新文件（覆盖模式）
            os.makedirs(os.path.dirname(output_conf_path), exist_ok=True)
            with open(output_conf_path, 'w') as f:
                f.write(conf_content)
            self.get_logger().info(f"配置文件已重建: {output_conf_path}")

        except PermissionError as e:
            self.get_logger().error(f"权限不足: {str(e)}")
        except Exception as e:
            self.get_logger().error(f"操作失败: {str(e)}")

def mac_to_bytes(mac):
    try:
        if len(mac) != 12:
            raise ValueError("MAC address should be exactly 12 characters long")
        return bytes(int(mac[i:i+2], 16) for i in range(0, len(mac), 2))
    except ValueError as e:
        raise ValueError(f"Invalid MAC address format: {mac}. Error: {e}")


def main(args=None):
    rclpy.init(args=args)
    node = ByteMultiArrayPublisher()
    node.get_logger().info("Node initialized successfully.")

    config_path = '/etc/cmcc_robot/andlinkSdk.conf'
    device_config = DeviceConfig(config_path)
    user_bind = device_config.get_config_value('userBind')
    usrkey = device_config.get_config_value('usrkey')

    config2_path = '/etc/cmcc_robot/facDevinfo.conf'
    device_config2 = DeviceConfig(config2_path)
    device_id = device_config2.get_config_value('id')
    mac_name = device_config2.get_config_value('mac')
    device_type = device_config2.get_config_value('deviceType')
    deviceId = 'CMCC-'+device_type+'-'+device_config2.get_config_value('sn')
    if user_bind is not None:
        node.get_logger().info("userBind:"+user_bind)
    else:
        node.get_logger().info("userBind:None")
    deviceId1=device_config.get_config_value('deviceId')
    if deviceId1 is not None:
        node.get_logger().info("deviceId1:"+deviceId1)
    if deviceId is not None:
        node.get_logger().info("deviceId_fa:"+deviceId)
    mac_bytes = mac_to_bytes(mac_name)
    node.get_logger().info(f'MAC bytes: {list(mac_bytes)}')

    manuf_code, = struct.unpack('>H', mac_bytes[1::-1])
    manuf_data = list(mac_bytes[2:])
    if user_bind is not None and user_bind == '1' and usrkey is not None:
        manuf_data.append(0x02)
        node.get_logger().debug("manuf_data.append(0x02)")
    else:
        manuf_data.append(0x01)
        node.get_logger().debug("manuf_data.append(0x01)")
    ble_adv_localname = 'CMB'+device_type+'-'+device_id[-4:]

    data_processor_t=DataProcessor(node,ble_adv_localname,manuf_code,manuf_data,deviceId,user_bind)

    ble_t = Ble_manager(node, data_processor_t, True)
    if user_bind is None or user_bind != '1':
        if user_bind is not None:
            ble_t.remove_andlinkSdkConfig_file()
        #ble_t.disconnect_wifi(node.wifi_interface)
        # time.sleep(2)
        # node.publish_andlink_cmd_reset()
    node.set_ble_manager(ble_t)
    ble_t.custom_advertisement_register()
    time.sleep(1)
    ble_t.custom_app_register()
    time.sleep(1)
    ble_thread = threading.Thread(target=ble_t.ble_manager_start,args=())
    ble_thread.daemon = True
    ble_thread.start()


    listener_thread = threading.Thread(target=ble_t.start_bluetooth_listener)
    listener_thread.daemon = True
    listener_thread.start()

    try:
        rclpy.spin(node)
    except KeyboardInterrupt:
        node.get_logger().info('Node stopped by user.')
    finally:
        node.get_logger().info('Shutting down node.')
        node.stop_timer()  # 关闭定时器
        node.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
