#ifndef __VS_DEFINE_H__
#define __VS_DEFINE_H__

// voice suite(VS) sdk header file

#define SERVER_IP "127.0.0.1"
#define SERVER_PORT (9090)
//----------------------------------------------
//消息类型
typedef enum {
	MSG_TYPE_NR_VTN = 0x0a,		//通话降噪音频（双向说话,voice talk）
	MSG_TYPE_ORIGIN = 0x0b,		//原始音频
	MSG_TYPE_NR_VAD = 0x0c,		//交互降噪音频(单向语音数据发送到大模型）
	MSG_TYPE_WAKEUP_EVENT = 0x0d,	//唤醒事件

	MSG_TYPE_CMD_SET_NR_TYPE = 0x10,		//设置降噪类型
	MSG_TYPE_CMD_SET_WAKEUP_WORD = 0x11,	//设置唤醒词资源
	MSG_TYPE_CMD_SET_OTA = 0x12,			//ota控制
	MSG_TYPE_CMD_GET_VERSION = 0x13,		//查询版本号

	MSG_TYPE_ACK_SET_NR_TYPE = 0x20,		//降噪音频类型切换反馈
	MSG_TYPE_ACK_SET_WAKEUP_WORD = 0x21,	//设置唤醒词资源设置反馈
	MSG_TYPE_ACK_OTA_PKT_CHECK = 0x22,		//ota数据包校验反馈
	MSG_TYPE_ACK_GET_VERSION = 0x23,		//查询版本号反馈

	//---------------------------------
	//自定义唤醒词
	MSG_TYPE_CMD_SET_DIY_WAKEUP_WORD = 0x14,	//设置自定义唤醒词（废弃）
	MSG_TYPE_CMD_GET_DIY_WAKEUP_WORD = 0x15,	//获取自定义唤醒词（废弃）

	MSG_TYPE_ACK_SET_DIY_WAKEUP_WORD = 0x24,	//设置自定义唤醒词反馈（废弃）
	MSG_TYPE_ACK_GET_DIY_WAKEUP_WORD = 0x25,	//获取自定义唤醒词反馈（废弃）

	// 唤醒词（深定制、浅定制、自定义）
	MSG_TYPE_CMD_OP_WAKEUP_WORD = 0x30,		//唤醒词相关操作
	//---------------------------------
}Ifly_Msg_Type_e;

//错误码
typedef enum {
	ERR_OK,				//成功
	ERR_CB_NULL,
	ERR_INIT_ALREADY,
	ERR_INVALID_PARAM,
	ERR_TIMEOUT,
	ERR_AUTH_ERR,		//鉴权失败
	ERR_NETWORK_NORMAL,	//网络异常
}VS_ERR_e;

//音频降噪模式
typedef enum {
	NR_MODE_NONE = 0,	//不使用
	NR_MODE_INTERACT,	//交互降噪
	NR_MODE_CALL,		//通话降噪
	NR_MODE_MAX,		//不使用
}VS_NR_Mode_e;

int32_t nrModeToInt32(VS_NR_Mode_e mode);
VS_NR_Mode_e int32ToNrMode(int32_t mode);
const char* int32ToChar(int32_t mode);

//VAD状态（仅交互音频使用）
typedef enum {
	VAD_STATUS_MUTE = 0,//静音片段
	VAD_STATUS_START,	//开始说话
	VAD_STATUS_KEEP,	//持续说话
	VAD_STATUS_END,		//结束说话
}VS_VAD_Status_e;

//唤醒事件数据结构体
typedef struct {
	char ivwWord[64];	//唤醒词
	int angle;			//声源定位角度
	char *extraInfo;	//扩展信息
}Event_wakeup_word_st;

//--------------------------------------
//#define SOLUTION_DWW_0224
#ifdef SOLUTION_DWW_0224
#define DWW_WORDS_SIZE_MAX (3 * 20) //限制20个汉字

//方案0224
//自定义唤醒词操作指令
typedef enum {
	DWW_OP_MIN = 0,
	DWW_OP_ADD,		//增
	DWW_OP_DELETE,	//删
	DWW_OP_MODIFY,	//改
	DWW_OP_QUERY,	//查
	DWW_OP_DELETE_F,	//强删
	DWW_OP_MODIFY_F,	//强改
	DWW_OP_MAX,
}Ifly_Dww_Op_e;

//自定义唤醒词数据结构
//typedef struct {
//	int op;			//操作，见 Ifly_Dww_Op_e
//	int id;			//暂时不用，置0
//	int size;		//自定义唤醒词数据大小
//	char words[DWW_WORDS_SIZE_MAX]; //自定义唤醒词
//}Ifly_Diy_Wakeup_word_st;

//增加：words_old为空，words_new非空
//删除：words_old非空，words_new为空
//修改：words_old非空，words_new非空
//查询：words_old为空，words_new为空
typedef struct {
	int op;	//动作，见 Ifly_Dww_Op_e，用于提高运行速度，须和其他成员取值匹配
	char words_old[DWW_WORDS_SIZE_MAX]; //原先的自定义唤醒词，如无，则置空（增加、查询时）
	char words_new[DWW_WORDS_SIZE_MAX]; //新的自定义唤醒词，如无，则置空（删除、查询时）
}Ifly_Diy_Wakeup_word_st;
#endif

//--------------------------------------
////命令词事件数据结构体
//typedef struct {
//	char keyWord[64];	//关键词
//	char *extraInfo;	//扩展信息
//}Event_key_word_st;

////降噪模式切换事件数据结构体
//typedef struct {
//	int mode_last; //之前降噪模式
//	int mode_cur; //当前降噪模式
//}Event_mode_st;
//不提供，只提供切换成功与否结果，当前模式由自己记录

//自定义唤醒词更新模式
typedef enum {
	UPDATE_WAKEUP_WORD_MODE_APPEND = 0x00, //追加
	UPDATE_WAKEUP_WORD_MODE_REPALCE = 0x01, //替换
}Ifly_Update_Wakeup_Word_Mode_e;

//设置成功与否的结果反馈
typedef enum {
	ACK_OK = 0x01,	//成功
	ACK_FAIL,		//失败
}Ifly_Ack_e;

//结果通知事件数据结构体
typedef struct {
	int ack; //结果通知（0 成功，-1 失败）：协议修改，见Ifly_Ack_e
}Event_ret_st;

//事件类型
typedef enum {
	EVENT_TYPE_NONE,			//不使用
	EVENT_WAKEUP_WORD,			//唤醒词事件
	EVENT_KEY_WORD,				//命令词事件
	EVENT_NR_MODE_SET_RET,		//降噪模式设置结果
	EVENT_WAKEUP_WORD_UPDATE_RET,	//唤醒词更新结果
	EVENT_SET_DIY_WAKEUP_WORD_RET,	//设置自定义唤醒词结果
	EVENT_GET_DIY_WAKEUP_WORD_RET,	//获取自定义唤醒词结果
	EVENT_WW_RET,				//唤醒词操作返回结果json格式
	EVENT_OTA_RET,				//ota升级结果
	EVENT_AUTH_RET,				//鉴权结果，见 VS_ERR_e 和 Ifly_Auth_Ret_e
	EVENT_MIC_STATUS,			//0正常，1异常
}IFLY_EVENT_e;

//鉴权相关
typedef enum {
	AUTH_RET_INIT = -1, //鉴权初始态，未开始鉴权
	AUTH_RET_OK = 0, //鉴权成功

	AUTH_RET_PARAM_MISS = 1001,
	AUTH_RET_OBU_IN = 1002,
	AUTH_RET_INVALID_ENGINE = 1003,

	AUTH_RET_HD_PARAM_MISS = 2000,
	AUTH_RET_HD_PARAM_MISS2 = 2001,

	AUTH_RET_OBU_BLACKLIST = 3001,
	AUTH_RET_OBU_LIMIT = 3002,
	AUTH_RET_OBU_BIND_SN_ERR = 3003,
	AUTH_RET_OBU_BIND_SN_UNIQUE_ERR = 3004,

	AUTH_RET_OBU_DECRYPT_ERR = 3007,
	AUTH_RET_FAIL = 3008,

	AUTH_RET_MAX,
}Ifly_Auth_Ret_e;

#endif
