#ifndef __XUNFEI_AUDIO_NODE_H__
#define __XUNFEI_AUDIO_NODE_H__

#include "rclcpp/rclcpp.hpp"
#include "homi_speech_interface/msg/pcm_stream.hpp"
#include "homi_speech_interface/msg/wakeup.hpp"
#include "homi_speech_interface/srv/set_nr_mode.hpp"
#include "homi_speech_interface/srv/set_wake_event.hpp"
#include "homi_speech_interface/srv/set_diy_word.hpp"
#include "homi_speech_interface/srv/ble_headset.hpp"
#include <stdint.h>
#include <unistd.h>
#include <string.h>
#include <memory>
#include <jsoncpp/json/json.h>
#include <std_msgs/msg/string.hpp>
#include <std_msgs/msg/int8.hpp>

extern "C" {
#include "vs_define.h"
#include "vs_api.h"
}

#define LOGNAME "audio_recorder"

class XunfeiAudioNode : public rclcpp::Node
{
private:
    static int vs_voice_nr_data(unsigned char *data, int size, int mode, VS_VAD_Status_e vad_status);
    static int vs_voice_origin_data(unsigned char *data, int size, int mode);
    static int vs_event_cb(IFLY_EVENT_e event_type, void *data);
    bool query_ww();
    bool setNrMode(
        const std::shared_ptr<homi_speech_interface::srv::SetNrMode::Request> req,
        std::shared_ptr<homi_speech_interface::srv::SetNrMode::Response> resp);
    bool setWakeEvent(
        const std::shared_ptr<homi_speech_interface::srv::SetWakeEvent::Request> req,
        std::shared_ptr<homi_speech_interface::srv::SetWakeEvent::Response> resp);
    bool setDiyEvent(
        const std::shared_ptr<homi_speech_interface::srv::SetDiyWord::Request> req,
        std::shared_ptr<homi_speech_interface::srv::SetDiyWord::Response> resp);
    bool setBleHeadset(
        const std::shared_ptr<homi_speech_interface::srv::BleHeadset::Request> req,
        std::shared_ptr<homi_speech_interface::srv::BleHeadset::Response> resp);

    static void deviceAlarmHandle();
    static void clearAlarmHandle();

    VS_CallBackFunList VS_CB;
    static bool isWaking, aiuiMic;
    std::thread queryThread;
    bool queried;
    std::string enableWw;
    std::string diyWw;

protected:
    static rclcpp::Publisher<homi_speech_interface::msg::PCMStream>::SharedPtr pushStream_;     //声明降噪音频流发布者
    static rclcpp::Publisher<homi_speech_interface::msg::PCMStream>::SharedPtr pushOrigin_;     //声明降噪音频流发布者
    static rclcpp::Publisher<homi_speech_interface::msg::Wakeup>::SharedPtr pushWakeup_;        //声明降噪音频流发布者
    rclcpp::Service<homi_speech_interface::srv::SetNrMode>::SharedPtr modeServer_;              //声明降噪模式服务
    rclcpp::Service<homi_speech_interface::srv::SetWakeEvent>::SharedPtr wakeServer_;           //声明唤醒开关服务
    rclcpp::Service<homi_speech_interface::srv::SetDiyWord>::SharedPtr diyServer_;              //声明自定义唤醒词服务
    rclcpp::Service<homi_speech_interface::srv::BleHeadset>::SharedPtr bleServer_;              //声明蓝牙耳机服务
    static rclcpp::Publisher<std_msgs::msg::String>::SharedPtr deviceAlarmPub_;
    static rclcpp::Publisher<std_msgs::msg::Int8>::SharedPtr vadPub_;
public:
    XunfeiAudioNode(std::string name);
    ~XunfeiAudioNode();
    bool init();
};


#endif
