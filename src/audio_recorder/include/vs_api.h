#ifndef __VS_API_H__
#define __VS_API_H__

// voice suite(VS) sdk header file

typedef struct
{
	/**
	 * 音频降噪数据
	 * @param data	音频数据
	 * @param size	音频数据长度
	 * @param mode	音频降噪模式，详见VS_NR_Mode_e
	 * @param vad_status	vad状态，详见VS_VAD_Status_e
	 */
	int (*vs_voice_nr_data)(unsigned char *data, int size, int mode, VS_VAD_Status_e vad_status);

	/**
	* 仅当配置lineout=false时，该回调有数据
	 * 原始数据
	 * @param data	音频数据
	 * @param size	音频数据长度
	 */
	int (*vs_voice_origin_data)(unsigned char *data, int size, int mode);

	/**
	 * 事件回调
	 * @param event_type	事件类型
	 * @param data	事件数据（需根据事件类型转换给不同的数据结构）
	 */
	int (*vs_event_cb)(IFLY_EVENT_e event_type, void *data);

}VS_CallBackFunList;

//鉴权接口
//VS_init里已包含鉴权，该接口用于在sdk内部鉴权失败时，用户主动调用
int VS_auth(void);

//获取sdk版本号，同步接口
char *VS_get_version(void);

//设置sdk日志等级(0 trace, 1 debug, 2 info, 3 warn, 4 error)
void VS_set_log_level(int level);

//sdk初始化
int VS_init(VS_CallBackFunList cb);

//sdk逆初始化
int VS_unInit(void);

//设置降噪模式
int VS_set_nr_mode(VS_NR_Mode_e mode);

//获取当前降噪模式，同步接口
VS_NR_Mode_e VS_get_nr_mode(void);

//-----------------------------------
//唤醒词分三类：深定制、浅定制、自定义
//同步接口方案
int VS_ww_query_deep_sync(char *ww, int *size);		//查询深定制唤醒词
int VS_ww_query_shallow_sync(char *ww, int *size);	//查询浅定制唤醒词
int VS_ww_query_diy_sync(char *ww, int *size);		//查询自定义唤醒词
int VS_ww_query_enable_sync(char *ww, int *size);	//查询使能唤醒词
int VS_ww_query_all_sync(char *ww, int *size);		//查询所有唤醒词

int VS_ww_disable_word_sync(const char *word);	//禁用指定唤醒词
int VS_ww_enable_word_sync(const char *word);	//使能指定唤醒词

int VS_ww_dww_add_sync(const char *word);		//添加自定义唤醒词
int VS_ww_dww_delete_sync(const char *word);	//删除自定义唤醒词（自定义唤醒词槽里不存在待删除的唤醒词，即视为成功）
int VS_ww_dww_query_sync(char *ww, int *size);	//查询自定义唤醒词，同接口VS_ww_query_diy

//-----------------------------------
//更新唤醒词资源（自定义唤醒词使用）
//mode: 0 追加，1 替换
int VS_update_wakeup_word(char *data, int size, int mode);

//ota升级
int VS_ota(char *data, int size);

#endif

