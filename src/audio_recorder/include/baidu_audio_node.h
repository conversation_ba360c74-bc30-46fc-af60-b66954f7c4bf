#ifndef __BAIDU_AUDIO_NODE_H__
#define __BAIDU_AUDIO_NODE_H__

#include "rclcpp/rclcpp.hpp"
#include "homi_speech_interface/msg/pcm_stream.hpp"
#include "homi_speech_interface/msg/wakeup.hpp"
#include "homi_speech_interface/srv/set_wake_event.hpp"
#include <stdint.h>
#include <unistd.h>
#include <string.h>
#include <memory>

extern "C" {
#include "duerwen_api.h"
}

#define LOGNAME "audio_recorder"

class BaiduAudioNode : public rclcpp::Node
{
private:
    static int voice_nr_data(unsigned char *data, int size);
    static int voice_origin_data(unsigned char *data, int size);
    static int event_cb(EVENT_e event_type, void *data);
    bool setWakeEvent(
        const std::shared_ptr<homi_speech_interface::srv::SetWakeEvent::Request> req,
        std::shared_ptr<homi_speech_interface::srv::SetWakeEvent::Response> resp);

    CallBackFunList VS_CB;
    static bool isWaking;
protected:
    static rclcpp::Publisher<homi_speech_interface::msg::PCMStream>::SharedPtr pushStream_;     //声明降噪音频流发布者
    static rclcpp::Publisher<homi_speech_interface::msg::PCMStream>::SharedPtr pushOrigin_;     //声明降噪音频流发布者
    static rclcpp::Publisher<homi_speech_interface::msg::Wakeup>::SharedPtr pushWakeup_;        //声明降噪音频流发布者
    rclcpp::Service<homi_speech_interface::srv::SetWakeEvent>::SharedPtr wakeServer_;           //声明唤醒开关服务
public:
    BaiduAudioNode(std::string name);
    ~BaiduAudioNode();
    bool init();
};


#endif
