#ifdef XUNFEI
#include "xunfei_audio_node.h"
#else
#include "baidu_audio_node.h"
#endif
int main(int argc, char *argv[])
{
    char tmp[256];
    getcwd(tmp, 256);
    RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "Current working directory: %s\n", tmp);
    rclcpp::init(argc, argv);
#ifdef XUNFEI
    auto node = std::make_shared<XunfeiAudioNode>("audio_recorder_node");
#else
    auto node = std::make_shared<BaiduAudioNode>("audio_recorder_node");
#endif
    if (!node->init()) return 1;
    rclcpp::spin(node);
    rclcpp::shutdown();
    return 0;
}
