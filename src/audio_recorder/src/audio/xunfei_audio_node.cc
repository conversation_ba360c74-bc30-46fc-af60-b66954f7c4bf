#include "xunfei_audio_node.h"
#define HW_VOICE_BOARD_MIC_NO_DEVICE 1010030101
#define HW_VOICE_BOARD_MIC_FOUND 1010030100

bool parseJson(const char* j<PERSON><PERSON><PERSON>, Json::Value& data) {
    if (!jsonChar) {
        RCLCPP_ERROR(rclcpp::get_logger(LOGNAME), "json<PERSON>har is null.");
        return false;
    }
    Json::CharReaderBuilder readerBuilder;
    std::string errs;
    const std::unique_ptr<Json::CharReader> reader(readerBuilder.newCharReader());
    if (!reader->parse(jsonChar, jsonChar + strlen(jsonChar), &data, &errs)) {
        RCLCPP_ERROR(rclcpp::get_logger(LOGNAME), "Parse JSON failed: %s.", errs.c_str());
        return false;
    }
    return true;
}

XunfeiAudioNode::XunfeiAudioNode(std::string name) : Node(name), queried(false), enableWw(""), diyWw(""),
    modeServer_(nullptr), wakeServer_(nullptr), diyServer_(nullptr)
{
	VS_CB.vs_voice_nr_data = vs_voice_nr_data;
    VS_CB.vs_voice_origin_data = vs_voice_origin_data;
	VS_CB.vs_event_cb = vs_event_cb;
    
    pushStream_ = this->create_publisher<homi_speech_interface::msg::PCMStream>("pcm_stream", 10);
    pushOrigin_ = this->create_publisher<homi_speech_interface::msg::PCMStream>("pcm_origin", 10);
    pushWakeup_ = this->create_publisher<homi_speech_interface::msg::Wakeup>("wakeup_event", 10);
    bleServer_ = this->create_service<homi_speech_interface::srv::BleHeadset>("ble_headset_service",
        std::bind(&XunfeiAudioNode::setBleHeadset, this, std::placeholders::_1, std::placeholders::_2));
    deviceAlarmPub_ = this->create_publisher<std_msgs::msg::String>("/device_alarm_report", 10);
    vadPub_ = this->create_publisher<std_msgs::msg::Int8>("vad_status", 5);
}

XunfeiAudioNode::~XunfeiAudioNode()
{
    if (queryThread.joinable()) {
        queryThread.join();
    }

    while(VS_unInit() != 0) {
        RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "======================= failed to uninit xunfei sdk =======================");
        //SDK反初始化失败，等待一段时间重试
        usleep(1000 * 1000 * 10);
    }
    RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "======================= uninit finished =======================");
}

bool XunfeiAudioNode::init()
{
    queryThread = std::thread([this]() {
        while(!this->query_ww()) {
            std::this_thread::sleep_for(std::chrono::seconds(1));
        }
    });

    while(VS_init(VS_CB) != 0) {
        RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "======================= failed to init xunfei sdk =======================");
        //SDK初始化失败，等待一段时间重试
        usleep(1000 * 1000* 10);
    }
    
    VS_set_nr_mode(NR_MODE_INTERACT);
    RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "======================= init finished =======================");

    modeServer_ = this->create_service<homi_speech_interface::srv::SetNrMode>("set_nr_mode_service",
        std::bind(&XunfeiAudioNode::setNrMode, this, std::placeholders::_1, std::placeholders::_2));
    wakeServer_ = this->create_service<homi_speech_interface::srv::SetWakeEvent>("set_wake_event_service",
        std::bind(&XunfeiAudioNode::setWakeEvent, this, std::placeholders::_1, std::placeholders::_2));
    diyServer_ = this->create_service<homi_speech_interface::srv::SetDiyWord>("diy_wakeup_service",
        std::bind(&XunfeiAudioNode::setDiyEvent, this, std::placeholders::_1, std::placeholders::_2));
    
    return true;
}

bool XunfeiAudioNode::query_ww()
{
    char buf[1024] = {0};
    int size = sizeof(buf);
    int ret = VS_ww_query_all_sync(buf, &size);
    if (ret != 0) {
        return false;
    }
    RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "%s", buf);
    Json::Value temp_json;
    if(parseJson(buf, temp_json)) {
        if (temp_json["enable"].size() != 0) {
            enableWw = temp_json["enable"][0].asString();
        } else {
            enableWw = "";
        }
        if (temp_json["diy"].size() != 0) {
            diyWw = temp_json["diy"][0].asString();
        } else {
            diyWw = "";
        }
    } else {
        return false;
    }

    queried = true;
    RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "XunfeiAudioNode::query_ww enable wakeup word %s, diy wakeup word %s.", enableWw.c_str(), diyWw.c_str());
    return true;
}

bool XunfeiAudioNode::setNrMode(
    const std::shared_ptr<homi_speech_interface::srv::SetNrMode::Request> req,
    std::shared_ptr<homi_speech_interface::srv::SetNrMode::Response> resp)
{
    VS_NR_Mode_e mode = int32ToNrMode(req->vs_nr_mode);
    if(mode == NR_MODE_NONE || mode == NR_MODE_INTERACT || mode == NR_MODE_CALL || mode == NR_MODE_MAX) {
        VS_set_nr_mode(mode);
        // 事件循环机制，无法立刻获取切换后的模式，需要等待事件响应回调
        // 默认事件执行成功
        resp->vs_nr_mode = req->vs_nr_mode;
        RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "XunfeiAudioNode::setNrMode successed change nr mode to %s", int32ToChar(resp->vs_nr_mode));
        return true;
    } else {
        resp->vs_nr_mode = nrModeToInt32(VS_get_nr_mode());
        RCLCPP_ERROR(rclcpp::get_logger(LOGNAME), "XunfeiAudioNode::setNrMode no such nr mode, current mode is %s", int32ToChar(resp->vs_nr_mode));
        return false;
    }
}

bool XunfeiAudioNode::setWakeEvent(
    const std::shared_ptr<homi_speech_interface::srv::SetWakeEvent::Request> req,
    std::shared_ptr<homi_speech_interface::srv::SetWakeEvent::Response> resp)
{
    isWaking = req->target;
    resp->current = isWaking;

    if(resp->current) {
        RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "XunfeiAudioNode::setWakeEvent wakeup event is running.");
    } else {
        RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "XunfeiAudioNode::setWakeEvent wakeup event is abort.");
    }

    return true;
}

bool XunfeiAudioNode::setDiyEvent(
    const std::shared_ptr<homi_speech_interface::srv::SetDiyWord::Request> req,
    std::shared_ptr<homi_speech_interface::srv::SetDiyWord::Response> resp)
{
    if (queried) {
        if (enableWw != req->wakeup_word) {
            int ret = 0;
            if (diyWw != req->wakeup_word) {
                if (diyWw != "") {
                    ret = VS_ww_dww_delete_sync(diyWw.c_str());
                    if (ret != 0) {
                        resp->status = 3;
                        resp->enable_ww = enableWw;
                        RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "XunfeiAudioNode::setDiyEvent delete diy wakeup word %s failed.", diyWw.c_str());
                        return false;
                    }
                    diyWw = "";
                }

                ret = VS_ww_dww_add_sync(req->wakeup_word.c_str());
                if (ret != 0) {
                    resp->status = 4;
                    resp->enable_ww = enableWw;
                    RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "XunfeiAudioNode::setDiyEvent add diy wakeup word %s failed.", req->wakeup_word.c_str());
                    return false;
                }
                diyWw = req->wakeup_word;
            }
            
            if (enableWw != "") {
                ret = VS_ww_disable_word_sync(enableWw.c_str());
                if (ret != 0) {
                    resp->status = 2;
                    resp->enable_ww = enableWw;
                    RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "XunfeiAudioNode::setDiyEvent disable wakeup word %s failed.", enableWw.c_str());
                    return false;
                }
                enableWw = "";
            }
            
            ret = VS_ww_enable_word_sync(req->wakeup_word.c_str());
            if (ret != 0) {
                resp->status = 5;
                resp->enable_ww = enableWw;
                RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "XunfeiAudioNode::setDiyEvent enable wakeup word %s failed.", req->wakeup_word.c_str());
                return false;
            }
            enableWw = req->wakeup_word;
        }

        RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "XunfeiAudioNode::setDiyEvent enable wakeup word %s, diy wakeup word %s.", enableWw.c_str(), diyWw.c_str());
        resp->status = 0;
        resp->enable_ww = enableWw;
        return true;
    } else {
        RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "XunfeiAudioNode::setDiyEvent wait query finish.");
        resp->status = 1;
        resp->enable_ww = enableWw;
        return true;
    }
}

bool XunfeiAudioNode::setBleHeadset(
    const std::shared_ptr<homi_speech_interface::srv::BleHeadset::Request> req,
    std::shared_ptr<homi_speech_interface::srv::BleHeadset::Response> resp)
{
    aiuiMic = !req->ble;
    resp->success = true;
    return true;
}

int XunfeiAudioNode::vs_voice_nr_data(unsigned char *data, int size, int mode, VS_VAD_Status_e vad_status)
{
    // RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "======================= voice receviced =======================");
    (void)mode;
    if (aiuiMic) {
        auto now = std::chrono::system_clock::now();
        auto ts = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()).count();
        homi_speech_interface::msg::PCMStream msg;
        msg.ts = ts;
        msg.data = std::vector<unsigned char>(data, data+size);
        pushStream_->publish(msg);
        std_msgs::msg::Int8 vad_msg;
        vad_msg.data = (int8_t)vad_status;
        vadPub_->publish(vad_msg);
    }
    return 0;
}

int XunfeiAudioNode::vs_voice_origin_data(unsigned char *data, int size, int mode)
{
    // RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "======================= origin voice receviced =======================");
    (void)mode;
    auto now = std::chrono::system_clock::now();
    auto ts = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()).count();
    homi_speech_interface::msg::PCMStream msg;
    msg.ts = ts;
    msg.data = std::vector<unsigned char>(data, data+size);
    pushOrigin_->publish(msg);

    return 0;
}

int XunfeiAudioNode::vs_event_cb(IFLY_EVENT_e event_type, void *data)
{
    // RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "======================= event receviced =======================");
    int ret = 0;

	if(data == NULL)
	{
		return -1;
	}

	switch(event_type)
	{
	case EVENT_WAKEUP_WORD:
		{
            if(isWaking) {
                Event_wakeup_word_st *temp = (Event_wakeup_word_st *)data;
                RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "EVENT: type[%d] word[%s] angel[%d] info[%s]", event_type, temp->ivwWord, temp->angle, temp->extraInfo);

                homi_speech_interface::msg::Wakeup msg;
                msg.ivw_word = temp->ivwWord;
                msg.angle = temp->angle;
                if(temp->extraInfo) {
                    msg.extra_info = temp->extraInfo;
                } else {
                    msg.extra_info = "";
                }
                pushWakeup_->publish(msg);
            }

			break;
		}
	case EVENT_NR_MODE_SET_RET:
		{
			Event_ret_st *temp = (Event_ret_st *)data;
			printf("EVENT: type[%d] ret[%d]", event_type, temp->ack);

			break;
		}
	case EVENT_OTA_RET:
		{
			Event_ret_st *temp = (Event_ret_st *)data;
			printf("EVENT: type[%d] ret[%d]", event_type, temp->ack);

			break;
		}
	case EVENT_WAKEUP_WORD_UPDATE_RET:
		{
			Event_ret_st *temp = (Event_ret_st *)data;
			printf("EVENT: type[%d] ret[%d]", event_type, temp->ack);

			break;
		}
    case EVENT_MIC_STATUS:
		{
			int mic_status = *(int *)data;
            RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "DEMO: EVENT: type[%d] ret[%d]", event_type, mic_status);
            
            if (mic_status == 0) {
                clearAlarmHandle();
            } else {
                deviceAlarmHandle();
            }
			break;
		}
	default:
		{
			printf("EVENT: unsupport type[%d]", event_type);
			ret = -2;
			break;
		}
	}

	return ret;
}

void XunfeiAudioNode::deviceAlarmHandle() {
    Json::Value body;
    body["alarmCode"] = HW_VOICE_BOARD_MIC_NO_DEVICE;
    body["alarmName"] = "NoMicrophone";
    body["alarmLevel"] = 2;
    body["alarmType"] = "NoMicrophone";
    body["alarmDesc"] = "There is no microphone";
    body["launcherModel"] = "Audio";
    body["data"] = Json::nullValue;
    Json::StreamWriterBuilder writer;
    writer["commentStyle"] = "None";
    writer["indentation"] = "";
    std_msgs::msg::String bodyStr;
    bodyStr.data=Json::writeString(writer, body);

    deviceAlarmPub_->publish(bodyStr);
    RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "XunfeiAudioNode::deviceAlarmHandle send alarm %d", HW_VOICE_BOARD_MIC_NO_DEVICE);

    return;
}

void XunfeiAudioNode::clearAlarmHandle()
{
    Json::Value body;
    body["alarmCode"] = HW_VOICE_BOARD_MIC_FOUND;
    body["alarmName"] = "NoMicrophoneClear";
    body["alarmLevel"] = 2;
    body["alarmType"] = "NoMicrophoneClear";
    body["alarmDesc"] = "Clear no microphone alarm";
    body["launcherModel"] = "Audio";
    body["data"] = Json::nullValue;
    Json::StreamWriterBuilder writer;
    writer["commentStyle"] = "None";
    writer["indentation"] = "";
    std_msgs::msg::String bodyStr;
    bodyStr.data=Json::writeString(writer, body);

    deviceAlarmPub_->publish(bodyStr);
    RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "XunfeiAudioNode::clearAlarmHandle send alarm %d", HW_VOICE_BOARD_MIC_FOUND);

    return;
}

bool XunfeiAudioNode::isWaking = true;

bool XunfeiAudioNode::aiuiMic = true;

rclcpp::Publisher<homi_speech_interface::msg::PCMStream>::SharedPtr  XunfeiAudioNode::pushStream_ = nullptr ;

rclcpp::Publisher<homi_speech_interface::msg::PCMStream>::SharedPtr  XunfeiAudioNode::pushOrigin_ = nullptr ;

rclcpp::Publisher<homi_speech_interface::msg::Wakeup>::SharedPtr  XunfeiAudioNode::pushWakeup_ = nullptr ;

rclcpp::Publisher<std_msgs::msg::String>::SharedPtr  XunfeiAudioNode::deviceAlarmPub_ = nullptr ;

rclcpp::Publisher<std_msgs::msg::Int8>::SharedPtr  XunfeiAudioNode::vadPub_ = nullptr ;
