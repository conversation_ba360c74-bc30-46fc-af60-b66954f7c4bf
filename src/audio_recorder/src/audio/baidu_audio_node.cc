#include "baidu_audio_node.h"


BaiduAudioNode::BaiduAudioNode(std::string name) : Node(name)
{
	VS_CB.voice_nr_data = voice_nr_data;
    VS_CB.voice_origin_data = voice_origin_data;
	VS_CB.event_cb = event_cb;
    
    pushStream_ = this->create_publisher<homi_speech_interface::msg::PCMStream>("pcm_stream", 10);
    pushOrigin_ = this->create_publisher<homi_speech_interface::msg::PCMStream>("pcm_origin", 10);
    pushWakeup_ = this->create_publisher<homi_speech_interface::msg::Wakeup>("wakeup_event", 10);
    wakeServer_ = this->create_service<homi_speech_interface::srv::SetWakeEvent>("set_wake_event_service",
        std::bind(&BaiduAudioNode::setWakeEvent, this, std::placeholders::_1, std::placeholders::_2));
}

BaiduAudioNode::~BaiduAudioNode()
{
    while(duwen_unInit() != 0) {
        RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "======================= failed to uninit baidu sdk =======================\n");
        //SDK反初始化失败，等待一段时间重试
        usleep(1000 * 1000 * 10);
    }
    RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "======================= uninit finished =======================\n");
}

bool BaiduAudioNode::init()
{
    while(duwen_init(VS_CB) < 0) {
        RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "======================= failed to init baidu sdk =======================\n");
        //SDK初始化失败，等待一段时间重试
        usleep(1000 * 1000* 10);
    }
    RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "======================= init finished =======================\n");

    return true;
}

bool BaiduAudioNode::setWakeEvent(
    const std::shared_ptr<homi_speech_interface::srv::SetWakeEvent::Request> req,
    std::shared_ptr<homi_speech_interface::srv::SetWakeEvent::Response> resp)
{
    isWaking = req->target;
    resp->current = isWaking;

    if(resp->current) {
        RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "Wakeup event is running.\n");
    } else {
        RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "Wakeup event is abort.\n");
    }

    return true;
}

int BaiduAudioNode::voice_nr_data(unsigned char *data, int size)
{
    // RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "======================= voice receviced =======================\n");
    auto now = std::chrono::system_clock::now();
    auto ts = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()).count();
    homi_speech_interface::msg::PCMStream msg;
    msg.ts = ts;
    msg.data = std::vector<unsigned char>(data, data+size);
    pushStream_->publish(msg);

    return 0;
}

int BaiduAudioNode::voice_origin_data(unsigned char *data, int size)
{
    // RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "======================= origin voice receviced =======================\n");
    auto now = std::chrono::system_clock::now();
    auto ts = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()).count();
    homi_speech_interface::msg::PCMStream msg;
    msg.ts = ts;
    msg.data = std::vector<unsigned char>(data, data+size);
    pushOrigin_->publish(msg);

    return 0;
}

int BaiduAudioNode::event_cb(EVENT_e event_type, void *data)
{
    // RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "======================= event receviced =======================\n");
    int ret = 0;

	if(data == NULL)
	{
        RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "EVENT: type[%d] data is NULL\n", event_type);
		return -1;
	}

	switch(event_type)
	{
	case EVENT_WAKEUP_WORD:
		{
            // Event_key_word_st *temp = (Event_key_word_st *)data;
			//	printf("DEMO: EVENT: type[%d] word[%s] info[%s]\n", event_type, temp->keyWord, temp->extraInfo);
            if(isWaking) {
                Event_wakeup_word_st *temp = (Event_wakeup_word_st *)data;
                RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "EVENT: type[%d] word[%s] angel[%d] info[%s]\n", event_type, temp->ivwWord, temp->angle, temp->extraInfo);

                homi_speech_interface::msg::Wakeup msg;
                msg.ivw_word = temp->ivwWord;
                msg.angle = temp->angle;
                if(temp->extraInfo) {
                    msg.extra_info = temp->extraInfo;
                } else {
                    msg.extra_info = "";
                }
                pushWakeup_->publish(msg);
            }

			break;
        }
	default:
		{
            RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "DEMO: EVENT: unsupport type[%d]\n", event_type);
			ret = -2;
			break;
		}
	}

	return ret;
}

bool BaiduAudioNode::isWaking = true;

rclcpp::Publisher<homi_speech_interface::msg::PCMStream>::SharedPtr  BaiduAudioNode::pushStream_ = nullptr ;

rclcpp::Publisher<homi_speech_interface::msg::PCMStream>::SharedPtr  BaiduAudioNode::pushOrigin_ = nullptr ;

rclcpp::Publisher<homi_speech_interface::msg::Wakeup>::SharedPtr  BaiduAudioNode::pushWakeup_ = nullptr ;
