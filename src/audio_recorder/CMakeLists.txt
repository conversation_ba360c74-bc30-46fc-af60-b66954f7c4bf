cmake_minimum_required(VERSION 3.5)
project(audio_recorder)

if("${CMAKE_BUILD_TYPE}" STREQUAL "Release")
  set(CMAKE_C_FLAGS_RELEASE "${CMAKE_C_FLAGS_RELEASE} -s")
  set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} -s")
endif()

set(COMPANY "xunfei")

# Default to C99
if(NOT CMAKE_C_STANDARD)
  set(CMAKE_C_STANDARD 99)
endif()

# Default to C++14
if(NOT CMAKE_CXX_STANDARD)
  set(CMAKE_CXX_STANDARD 14)
endif()

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# find dependencies
find_package(ament_cmake REQUIRED)
find_package(rclcpp REQUIRED)
find_package(homi_speech_interface REQUIRED)
find_package(std_msgs REQUIRED)

include_directories(
  ${ament_cmake_INCLUDE_DIRS}
  ${CMAKE_CURRENT_SOURCE_DIR}/include
  ${homi_speech_interface_INCLUDE_DIRS}
)

if(CMAKE_SYSTEM_PROCESSOR MATCHES "x86_64|i386")
    message(STATUS "Detected x86 architecture")

    link_directories(${CMAKE_CURRENT_SOURCE_DIR}/lib/x86_64/${COMPANY}/)

    if(COMPANY MATCHES "xunfei")
      add_executable(${PROJECT_NAME}_node
        src/main.cc
        src/audio/${COMPANY}_audio_node.cc
        src/audio/vs_define_trans.cc)

      ament_target_dependencies(${PROJECT_NAME}_node rclcpp homi_speech_interface std_msgs)
      # 添加 x86 相关的依赖库
      target_compile_definitions(${PROJECT_NAME}_node PUBLIC XUNFEI)
      target_link_libraries(${PROJECT_NAME}_node
        ${ament_cmake_LIBRARIES}
        vs
        cjson
        pthread
        ssl
        curl
        crypto
        jsoncpp
      )
    elseif(COMPANY MATCHES "baidu")
      add_executable(${PROJECT_NAME}_node
        src/main.cc
        src/audio/${COMPANY}_audio_node.cc)

      ament_target_dependencies(${PROJECT_NAME}_node rclcpp homi_speech_interface std_msgs)
      # 添加 x86 相关的依赖库
      target_link_libraries(${PROJECT_NAME}_node
        ${ament_cmake_LIBRARIES}
        duerwen_wakeup
        asound
        pthread
        jsoncpp
      )
    endif()
elseif(CMAKE_SYSTEM_PROCESSOR MATCHES "aarch64")
    message(STATUS "Detected ARM architecture")

    link_directories(${CMAKE_CURRENT_SOURCE_DIR}/lib/aarch64/${COMPANY}/)

    if(COMPANY MATCHES "xunfei")
      add_executable(${PROJECT_NAME}_node
        src/main.cc
        src/audio/${COMPANY}_audio_node.cc
        src/audio/vs_define_trans.cc)

      ament_target_dependencies(${PROJECT_NAME}_node rclcpp homi_speech_interface std_msgs)
      # 添加 arm 相关的依赖库
      target_compile_definitions(${PROJECT_NAME}_node PUBLIC XUNFEI)
      target_link_libraries(${PROJECT_NAME}_node
        ${ament_cmake_LIBRARIES}
        vs
        cjson
        pthread
        ssl
        curl
        crypto
        jsoncpp
      )
    elseif(COMPANY MATCHES "baidu")
      add_executable(${PROJECT_NAME}_node
        src/main.cc
        src/audio/${COMPANY}_audio_node.cc)

      ament_target_dependencies(${PROJECT_NAME}_node rclcpp homi_speech_interface std_msgs)
      # 添加 arm 相关的依赖库
      target_link_libraries(${PROJECT_NAME}_node
        ${ament_cmake_LIBRARIES}
        duerwen_wakeup
        asound
        pthread
        jsoncpp
      )
    endif()
endif()


install(TARGETS ${PROJECT_NAME}_node
DESTINATION lib/${PROJECT_NAME}
)
install(DIRECTORY launch
DESTINATION share/${PROJECT_NAME}
)

if(BUILD_TESTING)
  find_package(ament_lint_auto REQUIRED)
  # the following line skips the linter which checks for copyrights
  # uncomment the line when a copyright and license is not present in all source files
  #set(ament_cmake_copyright_FOUND TRUE)
  # the following line skips cpplint (only works in a git repo)
  # uncomment the line when this package is not in a git repo
  #set(ament_cmake_cpplint_FOUND TRUE)
  ament_lint_auto_find_test_dependencies()
endif()

ament_package()
