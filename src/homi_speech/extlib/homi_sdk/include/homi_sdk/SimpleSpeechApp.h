#ifndef __SRC_HOMI_INCLUDE_HOMI_SIMPLESPEECHAPP_H_
#define __SRC_HOMI_INCLUDE_HOMI_SIMPLESPEECHAPP_H_
#include "AudioStream.h"
#include "AudioCode.h"
#include <functional>
namespace homi::app 
{
    enum class SimpleSpeechAppEvent
    {
        Idle = 0,
        Running = 1,
        AsrResponsed = 2,
        AsrTextout = 3,
        AsrEnd = 4,
        Aborting = 5,
        OfflineInstructionMatched = 6,
        DisplayError = 7,
        UserAbort = 8,
        End
    };

    struct SimpleSpeechAppEventHelper {
        static const char * to_string(SimpleSpeechAppEvent event) {
            switch (event) {
                case SimpleSpeechAppEvent::Idle:          return "Idle";
                case SimpleSpeechAppEvent::Running:       return "Running";
                case SimpleSpeechAppEvent::AsrResponsed:  return "AsrResponsed";
                case SimpleSpeechAppEvent::AsrTextout:    return "AsrTextout";
                case SimpleSpeechAppEvent::AsrEnd:        return "AsrEnd";
                case SimpleSpeechAppEvent::Aborting:      return "Aborting";
                case SimpleSpeechAppEvent::OfflineInstructionMatched:    return "OfflineInstructionMatched";
                case SimpleSpeechAppEvent::DisplayError:             return "DisplayError";
                case SimpleSpeechAppEvent::UserAbort:             return "UserAbort";
                case SimpleSpeechAppEvent::End:           return "End";
                default:                                  return "Unknown";
            }
        }
    };

    enum class SimpleSpeechAppErrorCode
    {
        Success = 0,
        Failure = 1,
        EAgain = 2,
        End
    };
    enum class ResourceType
    {
        Wakeup = 0,
        NetworkError=1,
        DeviceInputError=2,
        offlineInstruction = 3,
        Inquiry = 4,
        TimeOut = 5,
        offlineInstructionError = 6,
        End
    };
    enum class ModeType
    {
        Normal = 0,
        Inquiry = 1,
        End
    };
    class IStreamsFactory
    {
    public:
        virtual std::shared_ptr<IAudioStreamInput> createAudioStreamInput(const AudioConfig &) = 0;
        virtual std::shared_ptr<IAudioStreamOutput> createAudioStreamOutput(const AudioConfig &) = 0;
        virtual std::shared_ptr<IAudioStreamInput> createResource(const ResourceType,const int subType=0) = 0;
        virtual std::shared_ptr<IAudioStreamOutput> createOfflineInstruction(const std::string &eventId,const std::function<void(const std::string & eventId, const int ResSubType, const std::string & instruction)> &callback) = 0;
        virtual std::shared_ptr<IAudioDecode> createAudioDecode(const AudioCodeConfig &config) =0;
        virtual std::shared_ptr<IAudioEncode> createAudioEncode(const AudioCodeConfig &config) =0;
        virtual ~IStreamsFactory() = default;
    };
    class ISimpleSpeechApp
    {
    public:
        virtual SimpleSpeechAppErrorCode runInquiry(std::string &sectionId, const std::string &inquirytext,const int inquiryResSubType
            ,const std::string &inquiryOpt="{}"
            ,const std::chrono::milliseconds &wait=std::chrono::milliseconds(2000)
            ,const std::chrono::milliseconds &round1Wheelwait=std::chrono::milliseconds(5000)
            ,const std::chrono::milliseconds &multipleWheelwait=std::chrono::milliseconds(5000)) = 0;
        virtual SimpleSpeechAppErrorCode runNormal(std::string &sectionId, const std::chrono::milliseconds &wait=std::chrono::milliseconds(2000)
            ,const std::chrono::milliseconds &round1Wheelwait=std::chrono::milliseconds(5000)
            ,const std::chrono::milliseconds &multipleWheelwait=std::chrono::milliseconds(5000)) = 0;
        virtual SimpleSpeechAppErrorCode stop(const bool notifyUserAbort,const std::chrono::milliseconds &wait=std::chrono::milliseconds(2000)) = 0;
        virtual ~ISimpleSpeechApp() = default;
        virtual void update(const std::string& extConfig) = 0;
    };
    std::shared_ptr<ISimpleSpeechApp> SimpleSpeechAppFactory(const std::shared_ptr<IStreamsFactory> &,const std::function<void (const SimpleSpeechAppEvent,const std::string &,const std::string &)> &);
}

#endif  // __SRC_HOMI_INCLUDE_HOMI_SIMPLESPEECHAPP_H_
