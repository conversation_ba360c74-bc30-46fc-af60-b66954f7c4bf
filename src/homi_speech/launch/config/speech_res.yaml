speech_resourse_file_config: '{
                                "Wakeup":[
                                    "$(find-pkg-share homi_speech)/launch/res/wakeup01.wav"
                                    ,"$(find-pkg-share homi_speech)/launch/res/wakeup02.wav"   
                                    ,"$(find-pkg-share homi_speech)/launch/res/wakeup03.wav"                
                                ]
                                ,"NetworkError":[
                                    "$(find-pkg-share homi_speech)/launch/res/noresponse01.wav"    
                                    ,"$(find-pkg-share homi_speech)/launch/res/noresponse02.wav"               
                                ]
                                ,"DeviceInputError":[
                                    "$(find-pkg-share homi_speech)/launch/res/noresponse01.wav"    
                                    ,"$(find-pkg-share homi_speech)/launch/res/noresponse02.wav"                
                                ]
                                ,"offlineInstructionError":[
                                    "$(find-pkg-share homi_speech)/launch/res/noresponse01.wav"    
                                    ,"$(find-pkg-share homi_speech)/launch/res/noresponse02.wav"               
                                ]
                                ,"TimeOut":[   
                                    "$(find-pkg-share homi_speech)/launch/res/silence.wav"                 
                                ]
                                ,"Inquiry":[
                                    "$(find-pkg-share homi_speech)/launch/res/takephoto_isready.wav"                
                                ]
                                ,"offlineInstruction":[
                                    "$(find-pkg-share homi_speech)/launch/res/instruction_ok.wav"
                                    ,"$(find-pkg-share homi_speech)/launch/res/instruction_express_delivery.wav"
                                    ,"$(find-pkg-share homi_speech)/launch/res/instruction_takephoto.wav"
                                    ,"$(find-pkg-share homi_speech)/launch/res/instruction_fetch_express.wav"               
                                ]
                            }'