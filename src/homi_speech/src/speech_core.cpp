#include <rclcpp/rclcpp.hpp>
#include <std_msgs/msg/string.hpp>
#include <std_msgs/msg/int8.hpp>
#include <homi_speech_interface/msg/pcm_stream.hpp>
#include <homi_speech_interface/msg/sigc_event.hpp>
#include <homi_speech_interface/srv/sigc_data.hpp>
#include <homi_speech_interface/srv/assistant_ctrl.hpp>
#include <homi_speech_interface/msg/assistant_event.hpp>
#include <homi_speech_interface/srv/iot_control.hpp>
#include <homi_speech_interface/srv/upload_image_url.hpp>
#include <homi_speech/def.h>
#include <homi_speech/AlsaHelper.h>
#include <homi_speech/WavHelper.h>
#include <homi_sdk/OpenAPI.h>
#include <homi_speech/json.hpp>
#include <homi_sdk/InnerAPI.h>
#include <homi_sdk/AudioStream.h>
#include <homi_sdk/SimpleSpeechApp.h>
#include <homi_sdk/ThreadPool.h>
#include <fstream>
#include <queue>
#include <mutex>
#include <condition_variable>
#include <string>
#include <chrono>
#include <homi_speech/OfflineAsrEngine.h>
#include <homi_speech/opusEngine.h>
#include <fstream>
#include <iostream>
#include <curl/curl.h>
#include <homi_speech_interface/msg/task_status.hpp>
#include "taskStatusContainer.h"
#include "ament_index_cpp/get_package_share_directory.hpp"
namespace happ =  homi::app;
std::shared_ptr<homi::app::IOpusEngine> g_opusEngine;
std::shared_ptr<homi::app::IOffineAsrEngine> g_offlineAsrEngine;

struct UploadImageUrl
{
    std::mutex _mutex;
    std::condition_variable _cv;
    std::string _url;
};
bool checkFolderExists(const std::string& filepath) {
    size_t found = filepath.find_last_of("/\\");
    if (found == std::string::npos) {
        return false;  // 没有找到路径分隔符
    }
    std::string folder = filepath.substr(0, found);
    std::ifstream file(folder);
    return file.good();
}


class INotifyInputStream
{
public:
    virtual void notifyInputStream(const std::shared_ptr<happ::AudioFrame> &frame) = 0;
};
class InputStreamPublisher
{
public:
    void registerSubscriber(const std::weak_ptr<INotifyInputStream> & subscriber)
    {
        std::lock_guard<std::mutex> lock(_mutex);
        _subscribersTmp.push_back(subscriber);
    }
    void notifySubscribers(const std::shared_ptr<happ::AudioFrame> &frame)
    {
        {
            std::lock_guard<std::mutex> lock(_mutex);
            _subscribers.insert(_subscribers.end(),_subscribersTmp.begin(),_subscribersTmp.end());
            _subscribersTmp.clear();
        }
        _subscribers.remove_if([frame](auto& callback){
            if (auto ptr = callback.lock())
            {
                ptr->notifyInputStream(frame);
                return false;
            }
            return true;
        });
    }
private:
    std::list<std::weak_ptr<INotifyInputStream>> _subscribers;
    std::list<std::weak_ptr<INotifyInputStream>> _subscribersTmp;
    std::mutex _mutex;
} g_NetStatusListener;

class TimeoutChecker {
    public:
        // 构造函数，接收一个超时时间（秒为单位）
        TimeoutChecker(int timeoutSeconds) {
            // 记录超时的时间点
            timeoutPoint = std::chrono::system_clock::now() + std::chrono::seconds(timeoutSeconds);
        }
    
        // 检查是否超时
        bool isTimeout() const {
            return std::chrono::system_clock::now() >= timeoutPoint;
    }
    
private:
    std::chrono::time_point<std::chrono::system_clock> timeoutPoint; // 超时的时间点
};
    
class InquiryCheck {
    public:
        InquiryCheck() : _cnt(0) {}
    
        // Check if the conditions are met
        bool check() {
            std::lock_guard<std::mutex> lock(_mutex);
            ++_cnt;
            if (_cnt == 2 && _timeoutChecker) {
                return true;
            }
            // Simplified check for timeout or null _timeoutChecker
            return _timeoutChecker && !_timeoutChecker->isTimeout();
        }
    
        // Set the timeout checker with a timeout value
        void set() {
            std::lock_guard<std::mutex> lock(_mutex);
            _timeoutChecker = std::make_shared<TimeoutChecker>(2);
        }
    
        // Clear the timeout checker and reset the counter
        void clear() {
            std::lock_guard<std::mutex> lock(_mutex);
            _timeoutChecker.reset();
            _cnt = 0;
        }
    
    private:
        std::shared_ptr<TimeoutChecker> _timeoutChecker;
        std::mutex _mutex;
        int _cnt;
};

// 唤醒超时未说话检测
class WakeupTips {
    public:
        WakeupTips() : _timerRunning(false), _cancelFlag(false) {}
        
        ~WakeupTips() {
            Clear();
            // 确保线程已结束
            JoinThreadIfNeeded();
        }
        
        void Set() {
            {
                RCLCPP_INFO(rclcpp::get_logger("speech_core"), "WakeupTips Set called");
                // 检查并处理已存在的线程
                if (_timerRunning) {
                    RCLCPP_WARN(rclcpp::get_logger("speech_core"), "WakeupTips Timer is already running. Cancelling previous timer");
                    {
                        std::lock_guard<std::mutex> lock(_mutex);
                        _cancelFlag = true;
                        _condition.notify_one();
                    }
                    JoinThreadIfNeeded();
                }
                _timerRunning = true;
                _cancelFlag = false;
            }
            {
                std::unique_lock<std::mutex> lock(_mutex);
                if (_timerThread.joinable())
                {
                    RCLCPP_WARN(rclcpp::get_logger("speech_core"), "WakeupTips Timer is running ignore..");
                    return;
                }
                _continueFlag.store(false,std::memory_order_release);
                _timerThread = std::thread([this]() {
                    RCLCPP_INFO(rclcpp::get_logger("speech_core"), "WakeupTips Timer thread enter");
                    auto now = std::chrono::steady_clock::now();
                    auto timeoutTime = now + std::chrono::seconds(5);
                    _continueFlag.store(false,std::memory_order_release);
                    {
                        std::unique_lock<std::mutex> lock(_mutex);
                        while (!_cancelFlag && 
                               _condition.wait_until(lock, timeoutTime) != std::cv_status::timeout) {
                            RCLCPP_INFO(rclcpp::get_logger("speech_core"), "WakeupTips abort\n");
                        }
                    }
                    if (!_cancelFlag && !_continueFlag.load(std::memory_order_acquire)) {
                        playTips();
                    } else {
                        RCLCPP_INFO(rclcpp::get_logger("speech_core"), "WakeupTips set canceled");
                    }
                    {
                        // 更新状态前加锁
                        std::lock_guard<std::mutex> lock(_mutex);
                        _timerRunning = false;
                    }
                    RCLCPP_INFO(rclcpp::get_logger("speech_core"), "WakeupTips Timer thread end !!");
                });
            }
        }
        
        void Clear() {
            {
                std::lock_guard<std::mutex> lock(_mutex);
                if (_timerRunning) {
                    RCLCPP_INFO(rclcpp::get_logger("speech_core"), "WakeupTips Clear");
                    _cancelFlag = true;
                    _condition.notify_one();
                }
                }
                // 等待线程结束
                JoinThreadIfNeeded();
        }
        
        void UpdateVadStatus(int8_t vad) {
            static int8_t s_last_vad = 0;
            if (vad == 1 || vad == 3 ) {
                RCLCPP_INFO(rclcpp::get_logger("speech_core"), "WakeupTips UpdateVadStatus %u", vad);
            }
            if (vad == 1 && s_last_vad != 1) {
                Clear();
            }
            if (vad == 2 && !_continueFlag) {
                RCLCPP_WARN(rclcpp::get_logger("speech_core"), "WakeupTips UpdateVadStatus %u set continue", vad);
                _continueFlag.store(true,std::memory_order_release);
            }
            s_last_vad = vad;
        }
    
    private:
        // 辅助函数：安全地等待线程结束
        void JoinThreadIfNeeded() {
            std::thread tempThread;
            {
                std::lock_guard<std::mutex> lock(_mutex);
                if (_timerThread.joinable()) {
                    tempThread = std::move(_timerThread);
                }
            }
            if (tempThread.joinable()) {
                tempThread.join();
            }
        }
        
        void playTips() {
            RCLCPP_INFO(rclcpp::get_logger("speech_core"), "WakeupTips play tips");
            auto now = time(NULL);
            std::string package_share_directory = ament_index_cpp::get_package_share_directory("homi_speech");
            std::string filePath = package_share_directory;
            if(now/2) {
                filePath = package_share_directory + "/launch/res/wakeup_idel1.wav";
            } else {
                filePath = package_share_directory + "/launch/res/wakeup_idel2.wav";
            }
            std::string command_3 = "aplay  \"" + filePath + "\"";
            RCLCPP_INFO(rclcpp::get_logger("speech_core"), "Going to exe command: %s", command_3.c_str());
            std::system(command_3.c_str());
        }
        
        mutable std::mutex _mutex;
        std::condition_variable _condition;
        std::thread _timerThread;
        std::atomic<bool> _timerRunning;
        std::atomic<bool> _cancelFlag;
        std::atomic<bool> _continueFlag;
    };
// Global instance
//WakeupTips g_wakeupTips;

class VoicePrint : public INotifyInputStream
{
public:
    VoicePrint(const std::string &fileName,const std::string &shellPath):m_fileName(fileName),m_shellPath(shellPath)
    {
        
    }
    virtual void notifyInputStream(const std::shared_ptr<happ::AudioFrame> &frame) override
    {
        if(frame&&frame->data.size()>0)
        {
            std::lock_guard<std::mutex> lock(m_mutex);
            try
            {
                if(outFile.is_open()&&m_timeoutChecker)
                {
                    if(!m_timeoutChecker->isTimeout())
                        outFile.write((char*)frame->data.data(),frame->data.size());
                    else
                    {
                        RCLCPP_INFO(rclcpp::get_logger("speech_core"),"notifyInputStream: timeout ");
                        outFile.close();
                        if(m_timeoutChecker) m_timeoutChecker.reset();
                        isCancel = true;
                    }
                        
                }
            }
            catch(const std::exception& e)
            {
                if(outFile.is_open())outFile.close();
                if(m_timeoutChecker) m_timeoutChecker.reset();
                RCLCPP_ERROR(rclcpp::get_logger("speech_core"),"VoicePrint::%s",e.what());
            }
        }
    }
    int start(const std::string &url,int timeout)
    {
        RCLCPP_INFO(rclcpp::get_logger("speech_core"),"VoicePrint::start (url:%s,timeout:%d)",url.c_str(),timeout);
        std::lock_guard<std::mutex> lock(m_mutex);
        if(timeout<0)timeout = -timeout;
        if(timeout>60*10)timeout = 60*10;
        try
        {
            if(outFile.is_open())outFile.close();
            outFile.open(m_fileName,std::ios::binary | std::ios::out | std::ios::trunc);
            if(outFile.fail()) return -1;
            m_timeoutChecker = std::make_shared<TimeoutChecker>(timeout);
            m_url = url;
            isCancel = false;
        }
        catch(const std::exception& e)
        {
            if(outFile.is_open())outFile.close();
            return -1;
        }
        return 0;
    }
    int cancel()
    {
        RCLCPP_INFO(rclcpp::get_logger("speech_core"),"VoicePrint::cancel");
        std::lock_guard<std::mutex> lock(m_mutex);
        if(outFile.is_open())outFile.close();
        if(m_timeoutChecker) m_timeoutChecker.reset();
        isCancel = true;
        return 0;
    }
    int finish()
    {
        RCLCPP_INFO(rclcpp::get_logger("speech_core"),"VoicePrint::finish");
        {
            std::lock_guard<std::mutex> lock(m_mutex);
            if(outFile.is_open())outFile.close();
            if(m_timeoutChecker) m_timeoutChecker.reset();
            if(isCancel)return -1;
        }
        return _uploadFile(m_shellPath,m_fileName,m_url);
    }
    bool isBusy()
    {
        std::lock_guard<std::mutex> lock(m_mutex);
        if(isCancel)return false;
        if(!m_timeoutChecker)return false;
        return !m_timeoutChecker->isTimeout();
    }
private:

    int _uploadFile(const std::string m_shellPath,const std::string &fileName,const std::string &url)
    {
        const std::string cmd = m_shellPath + " \"" + url + "\" " + fileName + " & ";
        RCLCPP_INFO(rclcpp::get_logger("speech_core"),"VoicePrint::_uploadFile %s",cmd.c_str());
        return std::system(cmd.c_str());
    }
    
    std::mutex m_mutex;
    const std::string m_fileName;
    const std::string m_shellPath;
    std::string m_url;
    std::ofstream outFile;
    std::shared_ptr<TimeoutChecker> m_timeoutChecker;
    bool isCancel;

};

const char * homiAppResourceTypehelperToString(homi::app::ResourceType type) {
            switch (type) {
                case homi::app::ResourceType::Wakeup:          return "Wakeup";
                case homi::app::ResourceType::NetworkError:       return "NetworkError";
                case homi::app::ResourceType::DeviceInputError:  return "DeviceInputError";
                case homi::app::ResourceType::offlineInstruction:    return "offlineInstruction";
                case homi::app::ResourceType::Inquiry:        return "Inquiry";
                case homi::app::ResourceType::TimeOut:      return "TimeOut";
                case homi::app::ResourceType::offlineInstructionError:    return "offlineInstructionError";
                case homi::app::ResourceType::End:    return "End";
                default:                                  return "Unknown";
            }
}
static nlohmann::json g_ResourceFiles;
class ResStream : public homi::app::IAudioStreamInput
{
public:
    ResStream(happ::ResourceType rt,int subType)
    {
        s_count.fetch_add(1);
        _rt = rt;
        _subType = subType;
    }
    bool onInit()
    {
        std::string filePath;
        if(_subType<0) return false;
        try
        {
            auto key = g_ResourceFiles[homiAppResourceTypehelperToString(_rt)];
            RCLCPP_WARN(rclcpp::get_logger("speech_core"),"ResStream onInit type: %s",homiAppResourceTypehelperToString(_rt));
            if((int)(key.size())<_subType) return false;
            auto index = _subType==0?rand()%key.size():_subType-1;
            filePath = key[index].get<std::string>();
        }
        catch(const std::exception& e)
        {
            RCLCPP_WARN(rclcpp::get_logger("speech_core"),"parse speech resource file error:%s",e.what());
        }
        if(_wf.openFile(filePath)<0)
        {
            RCLCPP_WARN(rclcpp::get_logger("speech_core"),"failed to open file %s", filePath.c_str());
            return false;
        }        
        _config.sampleRate = _wf.getRate();
        _config.channelCount = _wf.getChannels();
        _config.format = _wf.getFormat();
        if(_config.format==happ::SampleFormat::End)
        {
            RCLCPP_WARN(rclcpp::get_logger("speech_core"),"unknown sample format %d", (int)_wf.getFormat());
            return false;
        }
        return true;
    }
    virtual happ::StreamErrorCode read(std::shared_ptr<happ::AudioFrame> &InDataPtr,const std::chrono::milliseconds &wait) override
    {
        HOMI_SPEECH_UNUSED(wait);
        InDataPtr = std::make_shared<happ::AudioFrame>();
        InDataPtr->config = _config;
        InDataPtr->data = _wf.readData(-1,1024);
        InDataPtr->ts = std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch());
        if(InDataPtr->data.size() == 0) return happ::StreamErrorCode::EEOF;
        return happ::StreamErrorCode::Success;
    }
    virtual happ::StreamErrorCode getAudioConfig(happ::AudioConfig & config) override
    {
        config = _config;
        return happ::StreamErrorCode::Success;
    }
    virtual happ::StreamErrorCode close(const std::chrono::milliseconds &wait) override
    {
        HOMI_SPEECH_UNUSED(wait);
        return happ::StreamErrorCode::Success;
    }
    virtual ~ResStream() override
    {
        _wf.close();
        auto k = s_count.fetch_sub(1);
        RCLCPP_INFO(rclcpp::get_logger("speech_core"),"~ResStream %d",k-1);
    };
private:
    static std::atomic<int> s_count;
    happ::AudioConfig _config;
    homi_speech::WavHelperFromFile _wf;
    happ::ResourceType _rt;
    int _subType;
};
std::atomic<int> ResStream::s_count{0};
class InStream : public std::enable_shared_from_this<InStream>
    , public homi::app::IAudioStreamInput ,public INotifyInputStream
{
public:
    InStream(const happ::AudioConfig &config):_config(config)
    {
        s_count.fetch_add(1);  
        _inputClosed = false;  
        _isRead = false;    
    }
    bool onInit()
    {
        RCLCPP_INFO(rclcpp::get_logger("speech_core"),"InStream onInit() called k= %d",s_count.load());
        auto ptr = std::static_pointer_cast<INotifyInputStream>(shared_from_this());
        if(ptr==nullptr) 
        {
            RCLCPP_WARN(rclcpp::get_logger("speech_core"),"onInit std::static_pointer_cast<INotifyInputStream>(shared_from_this())==nullptr");
            return false;
        }
        g_NetStatusListener.registerSubscriber(ptr);
        return true;
    }
    virtual void notifyInputStream(const std::shared_ptr<happ::AudioFrame> &frame) override
    {
        std::unique_lock<std::mutex> lock(_mutex);
        if(_inputClosed) {
            RCLCPP_WARN(rclcpp::get_logger("speech_core"),"notifyInputStream but cloded");
            return;
        }
        _frames.push(frame);
        if(_frames.size()>20) _frames.pop();
        _cv.notify_one();
    }
    void readSyn()
    {
        if(!_isRead)
        {
            _isRead = true;
            _firstReadTS=std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch());
        }
        while(_frames.size() > 0)
        {
            auto frame = _frames.front();
            if(frame->ts > _firstReadTS) 
            {
                break;
            }
            _frames.pop();
        }
    }
    virtual happ::StreamErrorCode read(std::shared_ptr<happ::AudioFrame> &InDataPtr,const std::chrono::milliseconds &wait) override
    {
        auto endTime = std::chrono::system_clock::now();
        if(wait>=std::chrono::milliseconds::max()/2) endTime = std::chrono::system_clock::time_point::max();
        else endTime += wait;
        std::unique_lock<std::mutex> lock(_mutex);
        while(endTime > std::chrono::system_clock::now())
        {
            _cv.wait_until(lock,endTime,[this](){return _inputClosed || _frames.size()>0;});
            if(_inputClosed) return happ::StreamErrorCode::Failure;
            readSyn();
            if(_frames.size()>0)
            {
                InDataPtr = _frames.front();
                _frames.pop();
                return happ::StreamErrorCode::Success;
            }
        }
        return happ::StreamErrorCode::EAgain;
    }
    virtual happ::StreamErrorCode getAudioConfig(happ::AudioConfig & config) override
    {
        config = _config;
        return happ::StreamErrorCode::Success;
    }
    virtual happ::StreamErrorCode close(const std::chrono::milliseconds &wait) override
    {
        HOMI_SPEECH_UNUSED(wait);
        std::unique_lock<std::mutex> lock(_mutex);
        _inputClosed = true;
        _cv.notify_all();
        return happ::StreamErrorCode::Success;
    }
    virtual ~InStream() override
    {
        {
            std::unique_lock<std::mutex> lock(_mutex);
            _inputClosed = true;
            _cv.notify_all();
        }
        auto k = s_count.fetch_sub(1);
        RCLCPP_INFO(rclcpp::get_logger("speech_core"),"InStream::~InStream %d",k-1);
    };
private:
    static std::atomic<int> s_count;
    const happ::AudioConfig _config;
    bool _inputClosed;
    bool _isRead;
    std::chrono::milliseconds _firstReadTS;
    std::mutex _mutex;
    std::condition_variable _cv;
    std::queue<std::shared_ptr<happ::AudioFrame>> _frames;
};
std::atomic<int> InStream::s_count{0};
class OutStream : public happ::IAudioStreamOutput
{
public:
    OutStream(const happ::AudioConfig &config):_config(config)
    {
        s_count.fetch_add(1);
    }
    std::string getPlaybackDeviceName()
    {
        return "default";
    }
    bool onInit()
    {
        auto name = getPlaybackDeviceName();
        if(name.empty())
        {
            RCLCPP_WARN(rclcpp::get_logger("speech_core"),"getPlaybackDeviceName fail");
            return false;
        }
        try
        {
            RCLCPP_INFO(rclcpp::get_logger("speech_core"),"playbackDeviceName=%s",name.c_str());
            playback.openAndConfig(name.c_str(),"S16_LE",_config.channelCount,_config.sampleRate);
        }
        catch(const std::exception& e)
        {
            RCLCPP_WARN(rclcpp::get_logger("speech_core"),"%s",e.what());
            return false;
        }
        return true;
        
    }
    virtual happ::StreamErrorCode write(std::shared_ptr<happ::AudioFrame> OutDataPtr,const std::chrono::milliseconds &wait) override
    {       
        HOMI_SPEECH_UNUSED(wait); 
        try
        {
            playback.writei((const unsigned char *)OutDataPtr->data.data(), OutDataPtr->data.size());
        }
        catch(const std::exception& e)
        {
            RCLCPP_WARN(rclcpp::get_logger("speech_core"),"%s",e.what());
            return happ::StreamErrorCode::Failure;
        }
        return happ::StreamErrorCode::Success;
    }
    virtual happ::StreamErrorCode getAudioConfig(happ::AudioConfig &config) override
    {
        config = _config;
        return happ::StreamErrorCode::Success;
    }
    virtual happ::StreamErrorCode close(bool flush,const std::chrono::milliseconds &wait) override
    {
        HOMI_SPEECH_UNUSED(wait);
        try
        {
            if(flush)
            {
                playback.drain();
            }
            playback.close();
            
        }
        catch(const std::exception& e)
        {
            RCLCPP_WARN(rclcpp::get_logger("speech_core"),"%s",e.what());
            return happ::StreamErrorCode::Failure;
        }
        return happ::StreamErrorCode::Success;
    }
    virtual ~OutStream() override
    {
        playback.close();
        auto k = s_count.fetch_sub(1);
        RCLCPP_INFO(rclcpp::get_logger("speech_core"),"OutStream::~OutStream %d",k-1);
    };
private:
    static std::atomic<int> s_count;
    const happ::AudioConfig _config;
    homi_speech::AlsaHelperPlayback playback;
};
std::atomic<int> OutStream::s_count{0};


class StreamFactory : public happ::IStreamsFactory
{
public:
    virtual std::shared_ptr<happ::IAudioStreamInput> createAudioStreamInput(const happ::AudioConfig & config) override
    {
        auto ptr = std::make_shared<InStream>(config);
        if(ptr->onInit())
            return ptr;
        else
            return nullptr;
    }
    virtual std::shared_ptr<happ::IAudioStreamOutput> createAudioStreamOutput(const happ::AudioConfig & config) override
    {
        auto ptr = std::make_shared<OutStream>(config);
        if(ptr->onInit())
            return ptr;
        else
            return nullptr;
    }
    virtual std::shared_ptr<happ::IAudioStreamInput> createResource(const happ::ResourceType rt,const int subType) override
    {
        auto ptr = std::make_shared<ResStream>(rt,subType);
        if(ptr->onInit())
            return ptr;
        else
            return nullptr;
    }
    virtual std::shared_ptr<happ::IAudioStreamOutput> createOfflineInstruction(const std::string &eventId,const std::function<void(const std::string & eventId, const int ResSubType, const std::string & instruction)> &callback) override
    {
        if(g_offlineAsrEngine==nullptr) return nullptr;
        return g_offlineAsrEngine->createAudioStreamOutput(eventId,callback);
    }
    virtual std::shared_ptr<happ::IAudioDecode> createAudioDecode(const happ::AudioCodeConfig &config) override
    {
        RCLCPP_WARN(rclcpp::get_logger("speech_core"),"createAudioDecode");
        if(g_opusEngine==nullptr)
        {
            RCLCPP_WARN(rclcpp::get_logger("speech_core"),"createAudioDecode:g_opusEngine==nullptr");
            return nullptr;
        } 
        return g_opusEngine->createAudioDecode(config);
    }
    virtual std::shared_ptr<happ::IAudioEncode> createAudioEncode(const happ::AudioCodeConfig &config) override
    {
        RCLCPP_WARN(rclcpp::get_logger("speech_core"),"createAudioEncode");
        if(g_opusEngine==nullptr)
        {
            RCLCPP_WARN(rclcpp::get_logger("speech_core"),"createAudioEncode:g_opusEngine==nullptr");
            return nullptr;
        } 
        return g_opusEngine->createAudioEncode(config);
    }
    virtual ~StreamFactory() override
    {}
};

class SpeechCore : public rclcpp::Node {
    public: enum State {
        idel = 0,
        wakeup,
        listening,
        responsing,
        error
    };
    public:
    SpeechCore() : Node("speech_core", rclcpp::NodeOptions().automatically_declare_parameters_from_overrides(true)) {
        //g_speech_core_node = this;
    }
        
    ~SpeechCore() {
        shutdown();
    }

    void run() {
        // 启动语音应用
        // 进入ROS循环
        rclcpp::spin(shared_from_this());
    }
    
    void shutdown() {
        // 停止语音应用
        // 关闭ROS
        rclcpp::shutdown();
    }

    int initialize() {
        RCLCPP_INFO(rclcpp::get_logger("speech_core"),"initialize");
        
        // 获取参数
        getParameters();
        regeisterUserEvents();
        createRosInterfaces();
        
        std::shared_ptr<homi::framework::ThreadPool> threadPoolPtr = std::make_shared<homi::framework::ThreadPool>(1);
        if(threadPoolPtr==nullptr)
        {
            RCLCPP_ERROR(rclcpp::get_logger("speech_core"),"create thread pool failed");
            return -1;
        }
        g_speechApp = happ::SimpleSpeechAppFactory(std::make_shared<StreamFactory>()
        ,[this,threadPoolPtr](const happ::SimpleSpeechAppEvent event,const std::string & msg,const std::string & eventId){
        RCLCPP_WARN(rclcpp::get_logger("speech_core"),"event: %s, msg: %s, id: %s",happ::SimpleSpeechAppEventHelper::to_string(event),msg.c_str(),eventId.c_str());
        homi_speech_interface::msg::AssistantEvent assistantEvent;
        static int s_seq = 0;
        assistantEvent.status = (int)event;
        assistantEvent.description = happ::SimpleSpeechAppEventHelper::to_string(event);
        assistantEvent.msg = msg;
        assistantEvent.section_id = eventId;
        {
            std::lock_guard<std::mutex> lock(pub_mutex_);
            assistantEventPub_->publish(assistantEvent);
        }
        if(event==happ::SimpleSpeechAppEvent::OfflineInstructionMatched)
        {
            RCLCPP_WARN(rclcpp::get_logger("speech_core"),"OfflineInstructionMatched: %s",msg.c_str());
            homi_speech_interface::msg::SIGCEvent sigcEvent;
            try
            {
                auto json = nlohmann::json::parse(msg);
                json["deviceId"] = myDeviceId;
                json["eventId"] = eventId;
                json["seq"] = s_seq++;
                sigcEvent.event = json.dump();
            }
            catch(const std::exception& e)
            {
                RCLCPP_WARN(rclcpp::get_logger("speech_core"),"OfflineInstructionMatched %s",e.what());
            }
            {
                std::lock_guard<std::mutex> lock(pub_mutex_);
                sigcEventPub_->publish(sigcEvent);
            }
        }else if(event==happ::SimpleSpeechAppEvent::Running)
        {
            RCLCPP_INFO(rclcpp::get_logger("speech_core"),"Running");
            if(inquiryCheck_.check()){
                RCLCPP_INFO(rclcpp::get_logger("speech_core"),"Running,but inqury is set");
                return ;
            }
            inquiryCheck_.clear();
            wakeupTips_.Set();

            std::thread([this]() {
                // 休眠指定时间
                std::this_thread::sleep_for(std::chrono::duration<double>(2));
                // 确保在ROS节点线程中执行回调
                changeExpression(SpeechCore::listening);
            }).detach();
        }else if (event==happ::SimpleSpeechAppEvent::Idle)
        {
            RCLCPP_INFO(rclcpp::get_logger("speech_core"),"Idle");
            if(inquiryCheck_.check())
            {
                RCLCPP_INFO(rclcpp::get_logger("speech_core"),"Running,but inqury is set");
                inquiryCheck_.clear();
                return ;
            }
            inquiryCheck_.clear();
            wakeupTips_.Clear();
            changeExpression(SpeechCore::idel);
        }
        else{}
    });
    if(g_speechApp==nullptr)
    {
        RCLCPP_ERROR(rclcpp::get_logger("speech_core"),"SimpleSpeechAppFactory fail");
        return -1;
    }
        return 0;
    }

    private:


    void changeExpression(SpeechCore::State st) {
        std_msgs::msg::String msg;
        switch (st)
        {
        case idel:
            msg.data = displayAssistantIdleCMD;
            break;
        case wakeup:
            msg.data = displayAssistantWakeupCMD;
            break;
        case listening:
            msg.data = displayListeningCMD;
            break;
        case responsing:
            msg.data = displayResponseCMD;
            break;
        default:
            msg.data = displayAssistantIdleCMD;
            break;
        }
        RCLCPP_INFO(rclcpp::get_logger("speech_core"),"PUB \n%s\n",msg.data.c_str());
        if(expressEventPub_) {
            std::lock_guard<std::mutex> lock(pub_mutex_);
            expressEventPub_->publish(msg);
        }
    }

    int getParameters() {
        std::string speechResourceFiles;
        if(!get_parameter(PARAM_SPEECH_RESSOURCE_FILE_CONFIG, speechResourceFiles)){
            RCLCPP_ERROR(rclcpp::get_logger("speech_core"),"getparam (%s) fail",PARAM_SPEECH_RESSOURCE_FILE_CONFIG);
        }
        try{
            g_ResourceFiles=nlohmann::json::parse(speechResourceFiles);
        }
        catch(const std::exception& e){
            RCLCPP_ERROR(rclcpp::get_logger("speech_core"),"parse (%s) fail:%s",PARAM_SPEECH_RESSOURCE_FILE_CONFIG,e.what() );
            return -1;
        }
        std::string aiserJson=R"({})";;
        if(!get_parameter(PARAM_OFFLINE_ASR_ENGINE_CONFIG,aiserJson)){
            RCLCPP_ERROR(rclcpp::get_logger("speech_core"),"getparam (%s) fail",PARAM_OFFLINE_ASR_ENGINE_CONFIG);
        }
        std::string offlineParseRule=R"({})";
        if(!get_parameter(PARAM_OFFLINE_PARSE_RULE_CONFIG,offlineParseRule)){
            RCLCPP_ERROR(rclcpp::get_logger("speech_core"),"getparam (%s) fail",PARAM_OFFLINE_PARSE_RULE_CONFIG);
        } 
        RCLCPP_INFO(rclcpp::get_logger("speech_core"),"get offline asr config:\n%s \nrule=%s",aiserJson.c_str(),offlineParseRule.c_str());

        if(!get_parameter(PARAM_SPEECH_SDK_INITPARAM, initParam)) {
            RCLCPP_ERROR(rclcpp::get_logger("speech_core"),"getparam (%s) fail",PARAM_SPEECH_SDK_INITPARAM);
        }
        std::string sTmp;
        if(get_parameter(PARAM_SPEECH_SDK_LOGFILE, sTmp)){
            if(checkFolderExists(sTmp))
            {
                logfile = sTmp;
            }
        }
        sTmp.clear();
        if(get_parameter(PARAM_SPPECH_SDK_LOGLEVEL, sTmp)){
            if (sTmp=="info"){
                loglevel = homi::LogLevel::LOG_LEVEL_INFO;
            }
        }
        RCLCPP_INFO(rclcpp::get_logger("speech_core"),"OpenAPI init param:\n%s\nloglevel=%d\nlogfile=%s",initParam.c_str(),(int)loglevel,logfile.c_str());
        try{
            auto json = nlohmann::json::parse(initParam);
            myDeviceId = json["config"]["deviceId"].get<std::string>();
            RCLCPP_INFO(rclcpp::get_logger("speech_core"),"deviceId=%s",myDeviceId.c_str());
        }
        catch(const std::exception& e) {
            RCLCPP_ERROR(rclcpp::get_logger("speech_core"),"parse (myDeviceId) fail:%s",e.what() );
        }
        std::string voicePrintPCMFile;
        std::string voicePrintShFile;
        if(!get_parameter(PARAM_SPEECH_VOICE_PRINT_PCM_FILE,voicePrintPCMFile)
            ||!get_parameter(PARAM_SPEECH_VOICE_PRINT_SH_FILE,voicePrintShFile))
        {
            RCLCPP_ERROR(rclcpp::get_logger("speech_core"),"getparam (%s , %s) fail",PARAM_SPEECH_VOICE_PRINT_PCM_FILE,PARAM_SPEECH_VOICE_PRINT_SH_FILE);
        }
        voicePrintPtr_ = std::make_shared<VoicePrint>(voicePrintPCMFile,voicePrintShFile);
        g_NetStatusListener.registerSubscriber(std::static_pointer_cast<INotifyInputStream>(voicePrintPtr_));
        
        auto h = homi::getOpenAPI();
        int ret; 
        ret = h->Init(initParam,loglevel,logfile);
        if(ret<0){
            RCLCPP_ERROR(rclcpp::get_logger("speech_core"),"OpenAPI init fail");
            return -1;
        }
        g_offlineAsrEngine = homi::app::createOfflineAsrEngine(aiserJson,offlineParseRule);
        if(g_offlineAsrEngine==nullptr) {
            RCLCPP_ERROR(rclcpp::get_logger("speech_core"),"createOfflineAsrEngine fail");
            return -1;
        }
        g_opusEngine = homi::app::createOpusEngine();
        if(g_opusEngine==nullptr) {
            RCLCPP_WARN(rclcpp::get_logger("speech_core"),"createOpusEngine fail");
        }
    
        return 0;
    }

    int regeisterUserEvents() {
        auto ret = homi::inner::regeisterUserEvent("ROBOT_BUSINESS_DEVICE","voiceprint_record",[this](const std::string &json){
            RCLCPP_INFO(rclcpp::get_logger("speech_core"),"voiceprint_record : %s",json.c_str());
            if(!voicePrintPtr_){
                RCLCPP_WARN(rclcpp::get_logger("speech_core"),"voiceprint_record:voicePrintPtr is null");
                return ;
            }
            try
            {
                auto obj = nlohmann::json::parse(json);
                auto action = obj["action"].get<std::string>();
                if(action=="start")
                {
                    auto url = obj["url"].get<std::string>();
                    auto to = obj["time"].get<int>();
                    auto ret = voicePrintPtr_->start(url,to);
                    if(ret<0)
                    {
                        RCLCPP_WARN(rclcpp::get_logger("speech_core"),"voiceprint_record:start ret=%d",ret);
                        return ;
                    }
                    if(g_speechApp) g_speechApp->stop(false,std::chrono::milliseconds(100));
                }else if(action=="end")
                {
                    auto ret = voicePrintPtr_->finish();
                    RCLCPP_WARN(rclcpp::get_logger("speech_core"),"voiceprint_record:finish ret=%d",ret);
                }else if(action=="exit")
                {
                    auto ret = voicePrintPtr_->cancel();
                    RCLCPP_WARN(rclcpp::get_logger("speech_core"),"voiceprint_record:cancel ret=%d",ret);
                }
            }
            catch(const std::exception& e)
            {
                RCLCPP_WARN(rclcpp::get_logger("speech_core"),"voiceprint_record:%s",e.what());
            }
            
        });
        ret = homi::inner::regeisterUserEvent("DEVICE_ABILITY","photo_upload",[this](const std::string &json){
            RCLCPP_INFO(rclcpp::get_logger("speech_core"),"upload json=%s",json.c_str());
            try
            {
                auto obj = nlohmann::json::parse(json);
                std::unique_lock<std::mutex> lock(g_UploadImageUrlOp._mutex);
                g_UploadImageUrlOp._url = obj["url"];
                g_UploadImageUrlOp._cv.notify_one();
            }
            catch(const std::exception& e)
            {
                RCLCPP_WARN(rclcpp::get_logger("speech_core"),"%s",e.what());
            }
        });
        if(ret<0)
        {
            RCLCPP_ERROR(rclcpp::get_logger("speech_core"),"regeisterUserEvent fail");
            return -1;
        }
        homi::inner::registerSIGCNetWorkStatus([this](const int status){
            RCLCPP_INFO(rclcpp::get_logger("speech_core"),"network status:%d",status);
            std_msgs::msg::String msg;
            if(status==0)
            {
                msg.data = R"({"netstatus":1})";
            }
            else
            {
                msg.data = R"({"netstatus":0})";
            }
            {
                std::lock_guard<std::mutex> lock(pub_mutex_);
                connectStatusEventPub_->publish(msg);
            }
        });
        homi::inner::registerDefaultEvent([this](const std::string &json){
            RCLCPP_INFO(rclcpp::get_logger("speech_core"),"inner event: %s",json.c_str());
            homi_speech_interface::msg::SIGCEvent sigcEvent;
            sigcEvent.event = json;
            sigcEventPub_->publish(sigcEvent);
        });
        return 0;
    }

    // 创建ROS接口
    int createRosInterfaces() {
        if(!get_parameter(PARAM_SPEECH_DISPLAY_WAKEUP_CMD, displayAssistantWakeupCMD)
        ||!get_parameter(PARAM_SPEECH_DISPLAY_IDLE_CMD, displayAssistantIdleCMD)
        ||!get_parameter(PARAM_SPEECH_DISPLAY_LISTENING_CMD, displayListeningCMD)
        ||!get_parameter(PARAM_SPEECH_DISPLAY_IDLE_CMD, displayAssistantIdleCMD)
        ||!get_parameter(PARAM_EXPRESS_TOPIC, expressTopic)
        ||!get_parameter(PARAM_SIGC_CONNECT_STATUS_TOPIC, connectStatusTopic)){
            RCLCPP_ERROR(rclcpp::get_logger("speech_core"),"getparam (%s , %s ,%s ,%s) fail",PARAM_SPEECH_DISPLAY_WAKEUP_CMD,PARAM_SPEECH_DISPLAY_IDLE_CMD,PARAM_EXPRESS_TOPIC,PARAM_SIGC_CONNECT_STATUS_TOPIC);
            return -1;
        }
        // 创建订阅者
        std::string pcmStreamTopic,vadStatusTopic;
        if(!get_parameter(PARAM_PCM_STREAM_TOPIC,pcmStreamTopic)){
            RCLCPP_ERROR(rclcpp::get_logger("speech_core"),"getparam (%s) fail",PARAM_PCM_STREAM_TOPIC);
        }
        if(!get_parameter(PARAM_VAD_STATUS_TOPIC,vadStatusTopic)){
            RCLCPP_ERROR(rclcpp::get_logger("speech_core"),"getparam (%s) fail",PARAM_VAD_STATUS_TOPIC);
        }
        pcmStreamSub_ = create_subscription<homi_speech_interface::msg::PCMStream>(pcmStreamTopic,50,std::bind(&SpeechCore::pcmStreamCallback, this, std::placeholders::_1));
        vadStatusSub_ = create_subscription<std_msgs::msg::Int8>(vadStatusTopic,5,std::bind(&SpeechCore::vadStatusCallback,this,std::placeholders::_1));
        taskStatusSub_ = create_subscription<homi_speech_interface::msg::TaskStatus>(PARAM_TASK_STATUS_TOPIC, 10, std::bind(&SpeechCore::taskStatusCallback, this, std::placeholders::_1));
        
        // 创建服务
        sigcDataService_ = create_service<homi_speech_interface::srv::SIGCData>(PARAM_SPEECH_SIGC_DATA_SERVICE, std::bind(&SpeechCore::SGICDataCallback, this, std::placeholders::_1, std::placeholders::_2));
        assistantService_ = create_service<homi_speech_interface::srv::AssistantCtrl>(
            PARAM_SPEECH_ASSISTANT_CTRL_SERVICE, std::bind(&SpeechCore::AssistantCtrlCallback, this, std::placeholders::_1, std::placeholders::_2));
        
        uploadImageUrlService_ = create_service<homi_speech_interface::srv::UploadImageUrl>(
            PARAM_SPEECH_UPLOAD_IMAGE_URL_SERVICE, std::bind(&SpeechCore::UploadImageUrlCallback, this, std::placeholders::_1, std::placeholders::_2));
        
        iotControlService_ = create_service<homi_speech_interface::srv::IotControl>(
            PARAM_SPEECH_IOT_CONTROL_SERVICE, std::bind(&SpeechCore::IotControlCallback, this, std::placeholders::_1, std::placeholders::_2));

        // 创建发布者
        sigcEventPub_ = create_publisher<homi_speech_interface::msg::SIGCEvent>(PARAM_SPEECH_SIGC_EVENT_TOPIC, 10);
        expressEventPub_ = create_publisher<std_msgs::msg::String>(expressTopic, 10);
        connectStatusEventPub_ = create_publisher<std_msgs::msg::String>(connectStatusTopic, 10);
        assistantEventPub_ = create_publisher<homi_speech_interface::msg::AssistantEvent>(PARAM_SPEECH_ASSISTANT_STATUS_TOPIC, 10);
    
        auto tAssistantEvent = create_publisher<homi_speech_interface::msg::AssistantEvent>(PARAM_SPEECH_ASSISTANT_STATUS_TOPIC,10);
       return 0;
    }

        /**
     * @brief PCM流回调函数
     */
    void pcmStreamCallback(const homi_speech_interface::msg::PCMStream::ConstSharedPtr ptr) {
        if (ptr == nullptr) return;
        auto frame = std::make_shared<happ::AudioFrame>();
        if (frame == nullptr) return;
        happ::AudioConfig config{happ::SampleFormat::PCM_S16LE, 16000, 1};
        frame->config = config;
        frame->data = ptr->data;
        frame->ts = std::chrono::milliseconds(ptr->ts);
        g_NetStatusListener.notifySubscribers(frame);
    }

    /**
     * @brief VAD状态回调函数
     */
    void vadStatusCallback(const std::shared_ptr<const std_msgs::msg::Int8> ptr) {
        if (ptr == nullptr) return;
        int8_t vad = ptr->data;
        wakeupTips_.UpdateVadStatus(vad);
    }

    /**
     * @brief 任务状态回调函数
     */
    void taskStatusCallback(const homi_speech_interface::msg::TaskStatus::ConstSharedPtr ptr) {
        if (ptr == nullptr) return;
        RCLCPP_INFO(get_logger(), "taskStatusCallback task(%s), status=(%d)", ptr->name, ptr->status);
        if (ptr->status == 1) {
            taskStatusContainer_.set(ptr->name, 0);
        } else {
            taskStatusContainer_.del(ptr->name);
        }
        if (g_speechApp) {
            g_speechApp->update(taskStatusContainer_.get());
        }
    }

     /**
     * @brief SGIC数据服务回调
     */
    void SGICDataCallback(const std::shared_ptr<homi_speech_interface::srv::SIGCData::Request> req,
        std::shared_ptr<homi_speech_interface::srv::SIGCData::Response> res) {
        RCLCPP_INFO(get_logger(), "SGICDataCallback %s", req->data.c_str());
        auto ret = homi::inner::sendEventOOB(std::make_shared<std::string>(req->data), false, 2000);
        if (ret < 0) {
            RCLCPP_WARN(get_logger(), "SGICDataCallback ret=%d", ret);
            res->error_code = -1;
        } else {
            res->error_code = 0;
        }
    }

    /**
    * @brief 上传图片URL服务回调
    */
    void UploadImageUrlCallback(const std::shared_ptr<homi_speech_interface::srv::UploadImageUrl::Request> req,
                std::shared_ptr<homi_speech_interface::srv::UploadImageUrl::Response> res) {
        RCLCPP_INFO(get_logger(), "UploadImageUrl file_size = %d", req->file_size);
        try {
            nlohmann::json obj;
            obj["skillName"] = "photo_upload";
            obj["argumentV2"]["fileSize"] = req->file_size;
            auto ret = homi::inner::sendEvent("SKILL_EXECUTE", "skill_photo_upload", true, obj);
            if (ret < 0) {
            res->error_code = -1;
            return;
            }
        } catch (const std::exception& e) {
            RCLCPP_WARN(get_logger(), "%s", e.what());
        }
        {
            std::unique_lock<std::mutex> lock(g_UploadImageUrlOp._mutex);
            g_UploadImageUrlOp._url.clear();
            g_UploadImageUrlOp._cv.wait_for(lock, std::chrono::milliseconds(10000), 
                                [this]() { return !g_UploadImageUrlOp._url.empty(); });
            if (g_UploadImageUrlOp._url.empty()) {
                res->error_code = -2;
            } else {
                res->url = g_UploadImageUrlOp._url;
                res->error_code = 0;
            }
        }
    }

    /**
    * @brief IoT控制服务回调
    */
    void IotControlCallback(const std::shared_ptr<homi_speech_interface::srv::IotControl::Request> req,
            std::shared_ptr<homi_speech_interface::srv::IotControl::Response> res) {
    nlohmann::json obj;
    // nlohmann::json controlParams;
    try {    
        obj["skillName"] = "iot_control";
        nlohmann::json argumentV2;
        argumentV2["targetDeviceId"] = "CMCC-591022-34A6EF828EA8";
        nlohmann::json parsedJson = nlohmann::json::parse(req->param);
        int outletStatusValue = parsedJson.value("outletStatus", 1);
        std::vector<std::unordered_map<std::string, std::string>> controlParams;
        controlParams.push_back({{"name", "outletStatus"}, {"value", std::to_string(outletStatusValue)}});
        argumentV2["controlParams"] = controlParams;
        obj["argumentV2"] = argumentV2;
        RCLCPP_INFO(get_logger(), "IotControlCallback:%s", obj.dump().c_str());
        } catch (const std::exception& e) {
        RCLCPP_WARN(get_logger(), "%s", e.what());
        }

        auto ret = homi::inner::sendEvent("SKILL_EXECUTE", "skill_iot_control", true, obj);
        if (ret < 0) {
            res->error_code = -1;
        } else {
            res->error_code = 0;
        }
    }

/**
* @brief 助手控制服务回调
*/
void AssistantCtrlCallback(const std::shared_ptr<homi_speech_interface::srv::AssistantCtrl::Request> req,
            std::shared_ptr<homi_speech_interface::srv::AssistantCtrl::Response> res) {
    RCLCPP_INFO(get_logger(), 
        "Assistant isNormal(%d) start(%d) stop(%d) startWaitMS(%d) stopWaitMS(%d) "
        "inquiryText(%s) inquirySubType(%d) inquiryTTS(%s) mutipleWheels(%d) notifyUserAbort(%d)",
    req->is_normal, req->start, req->stop, req->start_wait_ms, req->stop_wait_ms,
    req->inquiry_text.c_str(), req->inquiry_sub_type, req->inquiry_tts.c_str(),
    req->mutiple_wheels, req->notify_user_abort);

    if (voicePrintPtr_ && voicePrintPtr_->isBusy()) {
        RCLCPP_INFO(get_logger(), "AssistantCtrlCallback is busy");
        res->error_code = -1;
        res->event_id = "";
        return;
    }

    std::string eventId;
    res->error_code = assistantCtrl(eventId, req->is_normal, req->mutiple_wheels, req->notify_user_abort,
                        req->inquiry_text, req->inquiry_sub_type, req->inquiry_tts,
                        req->start, std::chrono::milliseconds(req->start_wait_ms),
                        req->stop, std::chrono::milliseconds(req->stop_wait_ms));
    res->event_id = eventId;
}

/**
* @brief 助手控制功能
*/
int assistantCtrl(std::string &eventId, bool isNormal, bool multipleWheel, bool notifyUserAbort,
   const std::string &inquiryText, const int inquirySubType, const std::string &inquiryTTS,
   bool run, const std::chrono::milliseconds& runWait,
   bool stop, const std::chrono::milliseconds& stopWait) {
    int waitrun;
    std::chrono::milliseconds wait1{10000}, wait2{0};
    happ::SimpleSpeechAppErrorCode error;

    if (get_parameter("speech_round1wait_ms", waitrun)) {
        wait1 = std::chrono::milliseconds(waitrun);
    }

    if (multipleWheel) {
        if (get_parameter("speech_roundnwait_ms", waitrun)) {
        wait2 = std::chrono::milliseconds(waitrun);
    } else {
        wait2 = std::chrono::milliseconds(10000);
    }
    }

    if (stop) {
        if (!g_speechApp) return -1;
        error = g_speechApp->stop(notifyUserAbort, stopWait);
        if (error != happ::SimpleSpeechAppErrorCode::Success) {
            if (error == happ::SimpleSpeechAppErrorCode::EAgain) {
                return 1;
            } else {
                return -1;
            }
        }
    } 

    if (run) {
    if (!g_speechApp) return -2;
    if (isNormal) {
        changeExpression(SpeechCore::wakeup);
        inquiryCheck_.clear();
        error = g_speechApp->runNormal(eventId, std::chrono::milliseconds(runWait), wait1, wait2);
    } else {
        RCLCPP_INFO(get_logger(), "runInquiry inquiryText: %s", inquiryText.c_str());
        RCLCPP_INFO(get_logger(), "runInquiry ttsSession: %s", inquiryTTS.c_str());
        RCLCPP_INFO(get_logger(), "runInquiry inquirySubType: %d runWait: %ld", 
                inquirySubType, runWait.count());
        auto inquiryTTS2 = inquiryTTS;
        try {
            auto tmp = nlohmann::json::parse(inquiryTTS2);
        } catch (const std::exception& e) {
            RCLCPP_INFO(get_logger(), "runInquiry ttsSession json error: %s", e.what());
            inquiryTTS2 = "{}";
        }
        if (inquiryText.empty()) {
            RCLCPP_ERROR(get_logger(), "runInquiry but inquiryText is null !!!");
            return 0;
        }
        inquiryCheck_.set();
        error = g_speechApp->runInquiry(eventId, inquiryText, inquirySubType, inquiryTTS2, 
                            std::chrono::milliseconds(runWait), wait1, wait2);
    }

    if (error != happ::SimpleSpeechAppErrorCode::Success) {
        if (error == happ::SimpleSpeechAppErrorCode::EAgain) {
            return 2;
        } else {
            return -2;
        }
    }
    } 

    return 0;  
}

private:
    
    std::shared_ptr<homi::app::ISimpleSpeechApp> g_speechApp; 

    std::string initParam;
    std::string myDeviceId;
    std::string logfile="";
    homi::LogLevel loglevel=homi::LogLevel::LOG_LEVEL_DEBUG;
    std::string playbackShPath;
    std::string playbackShParam;
    std::string displayAssistantWakeupCMD;
    std::string displayAssistantIdleCMD;
    std::string displayListeningCMD;
    std::string displayResponseCMD;
    std::string expressTopic;
    std::string connectStatusTopic;

    WakeupTips wakeupTips_;
    std::shared_ptr<VoicePrint> voicePrintPtr_;
    HomiTaskStatusContainer taskStatusContainer_;
    UploadImageUrl g_UploadImageUrlOp;
    InquiryCheck inquiryCheck_;

    std::mutex pub_mutex_;

    // 私有成员变量：ROS订阅者、服务和发布者
    rclcpp::Subscription<homi_speech_interface::msg::PCMStream>::SharedPtr pcmStreamSub_;
    rclcpp::Subscription<std_msgs::msg::Int8>::SharedPtr vadStatusSub_;
    rclcpp::Subscription<homi_speech_interface::msg::TaskStatus>::SharedPtr taskStatusSub_;
    
    rclcpp::Service<homi_speech_interface::srv::SIGCData>::SharedPtr sigcDataService_;
    rclcpp::Service<homi_speech_interface::srv::AssistantCtrl>::SharedPtr assistantService_;
    rclcpp::Service<homi_speech_interface::srv::UploadImageUrl>::SharedPtr uploadImageUrlService_;
    rclcpp::Service<homi_speech_interface::srv::IotControl>::SharedPtr iotControlService_;
    

    rclcpp::Publisher<homi_speech_interface::msg::SIGCEvent>::SharedPtr sigcEventPub_;
    rclcpp::Publisher<std_msgs::msg::String>::SharedPtr expressEventPub_;
    rclcpp::Publisher<std_msgs::msg::String>::SharedPtr connectStatusEventPub_;
    rclcpp::Publisher<homi_speech_interface::msg::AssistantEvent>::SharedPtr assistantEventPub_;
    
};

/**
 * @brief 主函数
 */
int main(int argc, char *argv[]) {
    setlocale(LC_ALL, "");
    try {
        // 初始化ROS
        rclcpp::init(argc, argv);    
        RCLCPP_INFO(rclcpp::get_logger("speech_core"), "Starting speech core node...");

        // 创建语音核心实例
        auto speechCore = std::make_shared<SpeechCore>();
        
        // 初始化
        if (speechCore->initialize() != 0) {
            RCLCPP_ERROR(rclcpp::get_logger("speech_core"), "Failed to initialize speech core");
            return -1;
        }
        
        // 运行
        RCLCPP_INFO(rclcpp::get_logger("speech_core"), "Speech core node is running...");
        speechCore->run();
        
        // 正常退出
        RCLCPP_INFO(rclcpp::get_logger("speech_core"), "Speech core node is shutting down...");
    } catch (const std::exception& e) {
        RCLCPP_FATAL(rclcpp::get_logger("speech_core"), "Fatal error: %s", e.what());
        return -1;
    } catch (...) {
        RCLCPP_FATAL(rclcpp::get_logger("speech_core"), "Unknown fatal error occurred");
        return -1;
    }
    
    return 0;
}