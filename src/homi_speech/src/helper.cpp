#include <rclcpp/rclcpp.hpp>
#include <homi_speech/def.h>
#include <homi_speech_interface/srv/assistant_abort.hpp>
#include <homi_speech_interface/srv/assistant_ctrl.hpp>
#include <homi_speech_interface/srv/assistant_take_photo.hpp>
//#include <homi_speech_interface/AssistantQuiet.h>
#include <homi_speech_interface/srv/assistant_speech_text.hpp>
#include <homi_speech_interface/msg/wakeup.hpp>
#include <atomic>
#include <chrono>
rclcpp::Node::SharedPtr g_helper_node;
rclcpp::CallbackGroup::SharedPtr g_cb_greoup;
static const std::shared_ptr<homi_speech_interface::srv::AssistantCtrl::Response> AssistantCallService(std::shared_ptr<homi_speech_interface::srv::AssistantCtrl::Request> req)
{
    RCLCPP_WARN(rclcpp::get_logger("helper"),"AssistantCallService"); 
    auto assistantClient = g_helper_node->create_client<homi_speech_interface::srv::AssistantCtrl>(PARAM_SPEECH_ASSISTANT_CTRL_SERVICE,rmw_qos_profile_services_default,g_cb_greoup);
    auto ret = assistantClient->wait_for_service(std::chrono::seconds(1));
    if(ret==false)
    {
        RCLCPP_WARN(rclcpp::get_logger("helper"),"Failed to waitForExistence service assistant");
        return nullptr;
    }
    auto result = assistantClient->async_send_request(req);
    auto status = result.wait_for(std::chrono::seconds(2));
    if(status == std::future_status::ready)
    {
        auto response = result.get();
        int errorCode = response->error_code;
        RCLCPP_WARN(rclcpp::get_logger("helper"),"service assistant return error code %d",errorCode);
        return response; 
    }
    else
    {
        RCLCPP_WARN(rclcpp::get_logger("helper"),"Failed to call service assistant");
        return nullptr;        
    }
}
static void AssistantTakePhotoCallback(const std::shared_ptr<homi_speech_interface::srv::AssistantTakePhoto::Request> req,
                             std::shared_ptr<homi_speech_interface::srv::AssistantTakePhoto::Response> res)
{
    RCLCPP_WARN(rclcpp::get_logger("helper"),"AssistantTakePhotoCallback"); 
    HOMI_SPEECH_UNUSED(req);
    auto srvReq = std::make_shared<homi_speech_interface::srv::AssistantCtrl::Request>();
    srvReq->is_normal = false;
    srvReq->start = true;
    srvReq->stop = true;
    srvReq->start_wait_ms = 5000;
    srvReq->stop_wait_ms = 5000;
    srvReq->mutiple_wheels = true;
    srvReq->inquiry_text = "准备好拍照了吗";
    srvReq->inquiry_sub_type = 0;
    srvReq->notify_user_abort = false;
    auto response = AssistantCallService(srvReq);
    if(response)
    {
        res->error_code = response->error_code;
    }
    else
    {
        res->error_code = -20;
    }
}
static void AssistantAbortCallback(const std::shared_ptr<homi_speech_interface::srv::AssistantAbort::Request> req,
                             std::shared_ptr<homi_speech_interface::srv::AssistantAbort::Response> res)
{   
    RCLCPP_WARN(rclcpp::get_logger("helper"),"AssistantAbortCallback");   
    HOMI_SPEECH_UNUSED(req); 
    auto srvReq = std::make_shared<homi_speech_interface::srv::AssistantCtrl::Request>();
    srvReq->is_normal = false;
    srvReq->start = false;
    srvReq->stop = true;
    srvReq->start_wait_ms = 5000;
    srvReq->stop_wait_ms = 5000;
    srvReq->mutiple_wheels = false;
    srvReq->inquiry_text = "";
    srvReq->inquiry_sub_type = -1;
    srvReq->notify_user_abort = false;
    auto response = AssistantCallService(srvReq);
    if(response)
    {
        res->error_code = response->error_code;
    }
    else
    {
        res->error_code = -20;
    }
}

static void AssistantSpeechTextCallback(const std::shared_ptr<homi_speech_interface::srv::AssistantSpeechText::Request> req,
                             std::shared_ptr<homi_speech_interface::srv::AssistantSpeechText::Response> res)
{
    RCLCPP_WARN(rclcpp::get_logger("helper"),"AssistantSpeechTextCallback");    
    auto srvReq = std::make_shared<homi_speech_interface::srv::AssistantCtrl::Request>();
    srvReq->is_normal = false;
    srvReq->start = true;
    srvReq->stop = true;
    srvReq->start_wait_ms = 5000;
    srvReq->stop_wait_ms = 5000;
    srvReq->mutiple_wheels = false;
    srvReq->inquiry_text = req->msg;
    srvReq->inquiry_sub_type = -1;
    srvReq->inquiry_tts = req->tts_session;
    srvReq->notify_user_abort = false;
    auto response = AssistantCallService(srvReq);
    if(response)
    {
        res->error_code = response->error_code;
        res->section_id = response->event_id;
    }
    else
    {
        res->error_code = -20;
    }
}

static void AssistantEndTextCallback(const std::shared_ptr<homi_speech_interface::srv::AssistantSpeechText::Request> req,
                             std::shared_ptr<homi_speech_interface::srv::AssistantSpeechText::Response> res)
{
    RCLCPP_WARN(rclcpp::get_logger("helper"),"AssistantEndTextCallback");    
    auto srvReq = std::make_shared<homi_speech_interface::srv::AssistantCtrl::Request>();
    srvReq->is_normal = false;
    srvReq->start = true;
    srvReq->stop = true;
    srvReq->start_wait_ms = 5000;
    srvReq->stop_wait_ms = 5000;
    srvReq->mutiple_wheels = true;
    srvReq->inquiry_text = req->msg;
    srvReq->inquiry_sub_type = -1;
    srvReq->notify_user_abort = false;
    auto response = AssistantCallService(srvReq);
    if(response)
    {
        res->error_code = response->error_code;
        res->section_id = response->event_id;
    }
    else
    {
        res->error_code = -20;
    }
}

static void WakeupCallback(const homi_speech_interface::msg::Wakeup::ConstSharedPtr msg)
{
    RCLCPP_WARN(rclcpp::get_logger("helper"),"WakeupCallback: %s",msg->ivw_word.c_str());
    auto srvReq = std::make_shared<homi_speech_interface::srv::AssistantCtrl::Request>();
    srvReq->is_normal = true;
    srvReq->start = true;
    srvReq->stop = true;
    srvReq->start_wait_ms = 5000;
    srvReq->stop_wait_ms = 5000;
    srvReq->mutiple_wheels = true;
    srvReq->inquiry_text = "";
    srvReq->inquiry_sub_type = -1;
    srvReq->notify_user_abort = true;
    AssistantCallService(srvReq);
}


int main(int argc, char *argv[])
{
    setlocale(LC_ALL, "");
    rclcpp::init(argc, argv);
    g_helper_node = rclcpp::Node::make_shared("helper",rclcpp::NodeOptions().automatically_declare_parameters_from_overrides(true));
    RCLCPP_INFO(rclcpp::get_logger("helper"),"helper start up");
    std::string assistantTopic;
    if(!g_helper_node->get_parameter(PARAM_PCM_STREAM_WAKEUP_TOPIC, assistantTopic))
    {
        RCLCPP_ERROR(rclcpp::get_logger("helper"),"Failed to get param assistantTopic");
        return -1;
    }
    g_cb_greoup = g_helper_node->create_callback_group(rclcpp::CallbackGroupType::Reentrant);
    auto tWakeup = g_helper_node->create_subscription<homi_speech_interface::msg::Wakeup>(assistantTopic,10,&WakeupCallback);
    auto sAssistantAbort = g_helper_node->create_service<homi_speech_interface::srv::AssistantAbort>(PARAM_HELPER_ASSISTANT_ABORT_SERVICE, &AssistantAbortCallback,rmw_qos_profile_services_default,g_cb_greoup);
    auto sAssistantTakePhoto = g_helper_node->create_service<homi_speech_interface::srv::AssistantTakePhoto>(PARAM_HELPER_ASSISTANT_TAKEPHOTO_SERVICE, &AssistantTakePhotoCallback,rmw_qos_profile_services_default,g_cb_greoup);
    auto sAssistantSpeechText = g_helper_node->create_service<homi_speech_interface::srv::AssistantSpeechText>(PARAM_HELPER_ASSISTANT_SPEECH_TEXT_SERVICE, &AssistantSpeechTextCallback,rmw_qos_profile_services_default,g_cb_greoup);
    auto sAssistantEndText = g_helper_node->create_service<homi_speech_interface::srv::AssistantSpeechText>(PARAM_HELPER_ASSISTANT_END_TEXT_SERVICE, &AssistantEndTextCallback,rmw_qos_profile_services_default,g_cb_greoup);
    long long lcount = 0;
    auto timer = g_helper_node->create_wall_timer(std::chrono::seconds(5),[&lcount](){
        lcount++;
        RCLCPP_INFO(rclcpp::get_logger("helper"),"idle ,run count =%lld s ...",(lcount*5));
    });
    rclcpp::executors::MultiThreadedExecutor executors;
    executors.add_node(g_helper_node);
    executors.spin();
    // rclcpp::spin(g_helper_node);
    return 0;
}
