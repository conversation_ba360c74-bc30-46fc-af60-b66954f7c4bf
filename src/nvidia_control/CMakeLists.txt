cmake_minimum_required(VERSION 3.5)
project(nvidia_control)

set(CMAKE_BUILD_TYPE Debug)

if("${CMAKE_BUILD_TYPE}" STREQUAL "Release")
  set(CMAKE_C_FLAGS_RELEASE "${CMAKE_C_FLAGS_RELEASE} -s")
  set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} -s")
endif()

# Default to C99
if(NOT CMAKE_C_STANDARD)
  set(CMAKE_C_STANDARD 99)
endif()

# Default to C++14
if(NOT CMAKE_CXX_STANDARD)
  set(CMAKE_CXX_STANDARD 14)
endif()

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# find dependencies
find_package(ament_cmake REQUIRED)
find_package(rclcpp REQUIRED)
find_package(std_msgs REQUIRED)
find_package(geometry_msgs REQUIRED)
find_package(sensor_msgs REQUIRED)


get_filename_component(SRC_DIR ${CMAKE_CURRENT_SOURCE_DIR} DIRECTORY)
get_filename_component(MAIN_DIR ${SRC_DIR} DIRECTORY)
message(MAIN_DIR:${MAIN_DIR})

include_directories(
 ${CMAKE_CURRENT_SOURCE_DIR}
 ${CMAKE_CURRENT_BINARY_DIR}
 ${CMAKE_CURRENT_SOURCE_DIR}/include
 ${CMAKE_CURRENT_SOURCE_DIR}/src
 ${OpenCV_INCLUDE_DIRS}
 ${MAIN_DIR}/include
)

if(${CMAKE_HOST_UNIX})
    message("当前系统为 Linux 架构")
    # Linux 平台特定处理逻辑
    if(CMAKE_HOST_SYSTEM_PROCESSOR MATCHES "x86_64")
        message("当前系统为 x86-64/AMD64 架构")
        if(CMAKE_CXX_FLAGS MATCHES "-m32")
            message("m32")
            find_library(WEBSOCKET_LIB WebSocket PATHS ${MAIN_DIR}/lib/x86_64 NO_DEFAULT_PATH)
        else()
            message("x64")
            find_library(WEBSOCKET_LIB WebSocket PATHS ${MAIN_DIR}/lib/x86_64 NO_DEFAULT_PATH)
        endif()
    elseif(CMAKE_HOST_SYSTEM_PROCESSOR MATCHES "aarch64")
        message("aarch64")
        find_library(WEBSOCKET_LIB WebSocket PATHS ${MAIN_DIR}/lib/aarch64 NO_DEFAULT_PATH)
    endif()
elseif (${CMAKE_SYSTEM} MATCHES "FreeBSD|OpenBSD")
    # FreeBSD 或 OpenBSD 平台特定处理逻辑
endif()
if(NOT WEBSOCKET_LIB)
  message(FATAL_ERROR "Could not find libWebSocket.so")
endif()

# 添加动态库
add_library(WebSocket SHARED IMPORTED)
set_target_properties(WebSocket PROPERTIES
  IMPORTED_LOCATION ${WEBSOCKET_LIB}
)

# 安装动态库
if(NOT CMAKE_CUSTOM_LIB_INSTALL)
install(FILES ${WEBSOCKET_LIB} DESTINATION lib/${PROJECT_NAME})
else()
install(FILES ${WEBSOCKET_LIB} DESTINATION ${CMAKE_CUSTOM_LIB_INSTALL})
endif()

############################nvidia_control#####################################
add_executable(nvidia_control
  src/nvidia_control.cpp
  src/nvidia_control/nvidia_control_node.cpp
)

ament_target_dependencies(nvidia_control rclcpp std_msgs geometry_msgs sensor_msgs)

target_compile_features(nvidia_control PUBLIC c_std_99 cxx_std_17) 

target_link_libraries(nvidia_control 
  jsoncpp
  ${WEBSOCKET_LIB}
  pthread
)

set_target_properties(nvidia_control PROPERTIES
  INSTALL_RPATH "${CMAKE_INSTALL_PREFIX}/lib/${PROJECT_NAME}"
)

install(TARGETS nvidia_control DESTINATION lib/${PROJECT_NAME})

############################homi_ws_server#####################################
add_executable(homi_ws_server
  src/homi_ws_server/homi_ws_server_node.cpp
  src/homi_ws_server/homi_ws_server_node.h
  src/homi_ws_server.cpp
)

ament_target_dependencies(homi_ws_server rclcpp std_msgs geometry_msgs sensor_msgs)

target_compile_features(homi_ws_server PUBLIC c_std_99 cxx_std_17) 

target_link_libraries(homi_ws_server 
  jsoncpp
  ${WEBSOCKET_LIB}
  pthread
)
set_target_properties(homi_ws_server PROPERTIES
  INSTALL_RPATH "${CMAKE_INSTALL_PREFIX}/lib/${PROJECT_NAME}"
)
install(TARGETS homi_ws_server DESTINATION lib/${PROJECT_NAME})



if(BUILD_TESTING)
  find_package(ament_lint_auto REQUIRED)
  # the following line skips the linter which checks for copyrights
  # uncomment the line when a copyright and license is not present in all source files
  #set(ament_cmake_copyright_FOUND TRUE)
  # the following line skips cpplint (only works in a git repo)
  # uncomment the line when this package is not in a git repo
  #set(ament_cmake_cpplint_FOUND TRUE)
  ament_lint_auto_find_test_dependencies()
endif()
ament_package()
