//
/*****websocket通讯服务端节点******/
/**********默认端口为19002********/
//
#include "homi_ws_server/homi_ws_server_node.h"
#include "rclcpp/rclcpp.hpp"
#include <iostream>
#include <chrono>
#include <string>
#include <map>

using namespace std;

void signal_handler(int signal) {
    switch (signal) {
        case SIGTSTP:
            std::cout << "Caught Ctrl+Z!" << std::endl;
            // 可以在这里执行一些操作，比如保存状态或暂停程序
            break;
        default:
            std::cout << "Caught " << signal << std::endl;
            // 处理其他信号
            break;
    }
}

int main(int argc, char **argv)
{
    // 注册信号处理函数
    struct sigaction sa_ctrl_c;
    //ctrl + c
    sa_ctrl_c.sa_handler = &signal_handler;
    sigemptyset(&sa_ctrl_c.sa_mask);
    sa_ctrl_c.sa_flags = 0;
    if (sigaction(SIGINT, &sa_ctrl_c, NULL) == -1) {
        std::cerr << "Failed to register signal handler" << std::endl;
        return 1;
    }

    rclcpp::init(argc, argv);
    auto node = std::make_shared<HomiWebSocketServerNode>();
    node->init();
    RCLCPP_INFO(rclcpp::get_logger("HomiWebSocketServerNode"), "ws server init");
    rclcpp::spin(node);
    rclcpp::shutdown();
    return 0;
}