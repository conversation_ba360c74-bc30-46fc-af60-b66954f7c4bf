//
/*****websocket通讯服务端节点******/
/**********默认端口为19002********/
//

#include "nvidia_control/nvidia_control_node.h"
#include <iostream>
#include <chrono>
#include <string>
#include <csignal>  
#include <signal.h>

using namespace std;

// 信号处理函数
void signal_handler(int signal) {
    switch (signal) {
        case SIGINT:
            std::cout << "Caught Ctrl+C (SIGINT)!" << std::endl;
            //stopRequested = true;
            //rclcpp::shutdown();   // 否则程序结束无法坐下
            break;
        case SIGTSTP:
            std::cout << "Caught Ctrl+Z!" << std::endl;
            //pauseRequested = !pauseRequested; // 切换暂停状态
            break;
        default:
            std::cout << "Caught " << signal << std::endl;
            // 处理其他信号
            break;
    }
}

//local_port = 43894"
//remote_port = 43893
//remote_ip = "*************"

int main(int argc, char **argv)
{
    //  ------------------ 注册信号处理函数 ------------------ 
    struct sigaction sa_ctrl_c;
    sa_ctrl_c.sa_handler = &signal_handler;
    sigemptyset(&sa_ctrl_c.sa_mask);
    sa_ctrl_c.sa_flags = 0;
    if (sigaction(SIGINT, &sa_ctrl_c, NULL) == -1) {
        std::cerr << "Failed to register SIGINT handler" << std::endl;
        return 1;
    }
    struct sigaction sa_tstp;
    sa_tstp.sa_handler = &signal_handler;
    sigemptyset(&sa_tstp.sa_mask);
    sa_tstp.sa_flags = 0;
    if (sigaction(SIGTSTP, &sa_tstp, NULL) == -1) {
        std::cerr << "Failed to register SIGTSTP handler" << std::endl;
        return 1;
    }
    // ROS2代码：
    rclcpp::init(argc, argv);
    // 创建一个节点
    std::shared_ptr<NvidiaCtrlNode> udp_ctrl = std::make_shared<NvidiaCtrlNode>();
    // 等待一段时间确保 UDP 通信初始化完成
    rclcpp::sleep_for(std::chrono::milliseconds(200));
    // ros::spin();
    // 进入 ROS 2 事件循环
    rclcpp::spin(udp_ctrl);
    // 请求停止
    //stopRequested = true;
    rclcpp::shutdown();
    
    return 0;
}
