//
/*****websocket通讯服务端节点******/
/**********默认端口为19002********/
//
#pragma once
#include "rclcpp/rclcpp.hpp"
#include <iostream>
#include <std_msgs/msg/string.hpp>
#include <geometry_msgs/msg/twist.hpp>
#include <chrono>
#include <string>
#include <map>
#include <jsoncpp/json/json.h>

#include "libWebSocket.h"
#include <homi_com/singleton.hpp>


class WSSeverMgr: public base::singleton<WSSeverMgr>  {
public:
    WSSeverMgr() {};
    ~WSSeverMgr() {};
    void UpdateClientInfo(int index, string strClientType);
    void SendClientWsMsg(string strMsgs);
    //更新心跳
    void UpdateHeartBeatMsg();
    void SetClock(rclcpp::Clock::SharedPtr clock) { 
        m_clock = clock; 
    }

public:
    std::map<std::string, int> m_mpCleintIndex;
private:
    rclcpp::Clock::SharedPtr m_clock;
};


class HomiWebSocketServerNode : public rclcpp::Node
{

public:
    HomiWebSocketServerNode();
    ~HomiWebSocketServerNode();
    HomiWebSocketServerNode* getNode();

    void init();


    void parseWsMsgCallback(const std::string& msg, int index);

    void timerHeadBeart();
    rclcpp::Logger get_logger() const { return Node::get_logger(); }
    rclcpp::Clock::SharedPtr get_clock() { return Node::get_clock(); }

private: 
    //心跳节点
    rclcpp::TimerBase::SharedPtr headBeartTimer_ = nullptr;
};
