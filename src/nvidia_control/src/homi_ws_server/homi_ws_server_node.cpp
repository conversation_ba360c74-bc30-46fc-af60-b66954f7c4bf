
#include "homi_ws_server_node.h"
#include <xiaoli_com/xiaoli_pub_def.h>

void WSSeverMgr::UpdateClientInfo(int index, std::string strClientType) {
    m_mpCleintIndex[strClientType] = index;
    RCLCPP_DEBUG(rclcpp::get_logger("WSSeverMgr"), "update client info: %s, %d", strClientType.c_str(), index);
}


void WSSeverMgr::SendClientWsMsg(std::string strMsgs) {
    Json::Reader reader;
    Json::Value value;  
    if(!reader.parse(strMsgs, value)){
        RCLCPP_ERROR(rclcpp::get_logger("WSSeverMgr"), "no client_type: %s", strMsgs.c_str());
        return;
    }
    string strClientType = CLIENT_LAUNCHER;
    if (value["client_type"].isNull()) {
        return;
    }
    strClientType = value["client_type"].asString();
    int index = 0;
    auto it = m_mpCleintIndex.find(strClientType);
    if (it == m_mpCleintIndex.end()){
        return;
    }
    index = it->second;
    if (value["action"].asString()!="uwb_data")
    // {
        RCLCPP_INFO_THROTTLE(rclcpp::get_logger("WSSeverMgr"),*m_clock,5000," index:%d  send msg to %s: %s",index,strClientType.c_str(),strMsgs.c_str());
    // }
    WS::WS_Send(strMsgs.c_str(), index);
    // std::this_thread::sleep_for(std::chrono::milliseconds(20));
}

void WSSeverMgr::UpdateHeartBeatMsg() {
    for(auto it : m_mpCleintIndex){
        Json::Value json;
        json["client_type"] = it.first;
        json["headbeart"] = "keep_alive";
        WS::WS_Send(json.toStyledString().c_str(), it.second);
        RCLCPP_INFO(rclcpp::get_logger("WSSeverMgr"), json.toStyledString().c_str());
        // std::this_thread::sleep_for(std::chrono::milliseconds(20));
    }
}

void notifyWsMsgCallback(void *handle, const char *msg, int index) {
    HomiWebSocketServerNode* pThis = (HomiWebSocketServerNode*)(handle);
    if (!pThis){
        return;
    }
    pThis->parseWsMsgCallback(msg, index);
}

HomiWebSocketServerNode::HomiWebSocketServerNode() : Node("homi_ws_server") {
    WSSeverMgr::getInstance().SetClock(this->get_clock());
}

HomiWebSocketServerNode::~HomiWebSocketServerNode(){
    WS::WS_UnInit();
    RCLCPP_INFO(rclcpp::get_logger("HomiWebSocketServerNode"), "destroy");
}

void HomiWebSocketServerNode::init(){
    this->declare_parameter<int>("ws_connect_port", WEBSOCKET_SERVER_PORT); 
    int port = this->get_parameter("ws_connect_port").as_int();
    //WS服务启动
    WS::WS_Init(WS::EN_WS_SEVER, port);
    //设置接受msg的回调函数
    WS::WS_SetMsgCallback(notifyWsMsgCallback, this);
    RCLCPP_INFO(rclcpp::get_logger("HomiWebSocketServerNode"), "ws_connect_port: %d", port);
        
    // 创建一个10s定时器的主控的心跳
    headBeartTimer_ = this->create_wall_timer(std::chrono::seconds(10),
        std::bind(&HomiWebSocketServerNode::timerHeadBeart, this));
}


void HomiWebSocketServerNode::timerHeadBeart(){
    WSSeverMgr::getInstance().UpdateHeartBeatMsg();
}

void HomiWebSocketServerNode::parseWsMsgCallback(const std::string& msg, int index){
    Json::Reader reader;
    Json::Value value;
    if(false == reader.parse(msg, value)) {
        return;
    }
    if (value["action"].asString()!="uwb_data" && value["action"].asString()!="uwb_data_array" )
        //RCLCPP_INFO(rclcpp::get_logger("WSSeverMgr"), "parseWsMsgCallback: %s", msg.c_str());
        RCLCPP_INFO_THROTTLE(this->get_logger(),*this->get_clock(),5000,"parseWsMsgCallback: %s", msg.c_str());
    if(value["client_type"].isNull()) { 
        return;
    }
    if(value["action"] == "pathPlanning")
    {
        value["target_client"] = CLIENT_NVIDIA;
    }
    std::string strClientType = value["client_type"].asString();
    WSSeverMgr::getInstance().UpdateClientInfo(index, strClientType);
    if(value["target_client"].isNull()) {
        RCLCPP_INFO(rclcpp::get_logger("WSSeverMgr"), "no target client: %s", msg.c_str());
        return;
    }
    value["client_type"] = value["target_client"].asString();
    std::thread sendThread([value]() {
        WSSeverMgr::getInstance().SendClientWsMsg(value.toStyledString().c_str());
    });

    sendThread.detach();
}

HomiWebSocketServerNode* HomiWebSocketServerNode::getNode(){
    return this;
}
