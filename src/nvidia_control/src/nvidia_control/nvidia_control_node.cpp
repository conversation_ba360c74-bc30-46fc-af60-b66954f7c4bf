#include <iomanip>

#include "nvidia_control_node.h"
#include "geometry_msgs/msg/quaternion.hpp"
#include "tf2/LinearMath/Quaternion.h"
#include "tf2/LinearMath/Matrix3x3.h"
#include "tf2_geometry_msgs/tf2_geometry_msgs.h" 
#include "xiaoli_com/xiaoli_pub_def.h"
#include "homi_com/homi_utils.hpp"

using namespace WS;

unsigned long currentTimeStramp_ = base::homiUtils::getCurrentTimeStamp();
bool bConnected_ = false;

int nConnectIndex_ = 0;

std::string removeEscapeCharacters(const std::string& input) {
    std::string result;
    for (size_t i = 0; i < input.length(); ++i) {
        if (input[i] == '\\') {
            // Skip the escape character and handle the next character
            ++i;
            if (i < input.length()) {
                switch (input[i]) {
                    case 'n':  // Newline
                    case 't':  // Tab
                    case 'r':  // Carriage return
                    case 'b':  // Backspace
                    case 'f':  // Form feed
                    case 'a':  // Alert (bell)
                    case '\\': // Backslash
                    case '"':  // Double quote
                    case '\'': // Single quote
                        // Do nothing, skip these characters
                        result += input[i];
                        break;
                    default:
                        // If it's an unknown escape sequence, add the backslash and the character
                        result += '\\';
                        result += input[i];
                        break;
                }
            }
        } else {
            result += input[i];
        }
    }
    return result;
}

std::string exec(const char* cmd) {
    char buffer[128];
    std::string result = "";
    FILE* pipe = popen(cmd, "r");
    if (!pipe) {
        throw std::runtime_error("popen() failed!");
    }
    try {
        while (fgets(buffer, sizeof(buffer), pipe) != nullptr) {
            result += buffer;
        }
    } catch (...) {
        pclose(pipe);
        throw;
    }
    pclose(pipe);
    return result;
}

std::vector<std::pair<float, float>> parseCoordinates(const std::string& input) {
    std::vector<std::pair<float, float>> coordinates;
    std::stringstream ss(input);
    std::string item;

    while (std::getline(ss, item, ';')) {
        std::stringstream ssItem(item);
        std::string latStr, lonStr;
        if (std::getline(ssItem, latStr, ',') && std::getline(ssItem, lonStr)) {
            float lat = std::stof(latStr);
            float lon = std::stof(lonStr);
            coordinates.emplace_back(lon, lat); // 注意顺序：经度在前，纬度在后
        }
    }

    return coordinates;
}

Json::Value convertToJSON(const std::vector<std::pair<float, float>>& coordinates) {
    Json::Value jsonArray(Json::arrayValue);
    for (const auto& coord : coordinates) {
        Json::Value coordJson;
        coordJson["longitude"] = coord.first;
        coordJson["latitude"] = coord.second;
        jsonArray.append(coordJson);
    }
    return jsonArray;
}

// 递归函数，用于遍历 JSON 值并修改键名
void modifyKeys(Json::Value& value) {
    if (value.isObject()) {
        Json::Value newObj;
        for (Json::Value::iterator it = value.begin(); it != value.end(); ++it) {
            std::string key = it.key().asString();
            if (key == "lat") {
                key = "latitude";
            }
            else if (key == "lon") {
                key = "longitude";
            }
            Json::Value& child = *it;
            modifyKeys(child);
            newObj[key] = child;
        }
        value = newObj;
    } else if (value.isArray()) {
        for (Json::Value::ArrayIndex i = 0; i < value.size(); ++i) {
            Json::Value& child = value[i];
            modifyKeys(child);
        }
    }
}


Json::Value mergeJsonArrays(const Json::Value& array1, const Json::Value& array2) {
    Json::Value mergedArray(Json::arrayValue);
    if (array1.isArray()) {
        for (const auto& item : array1) {
            mergedArray.append(item);
        }
    }
    if (array2.isArray()) {
        for (const auto& item : array2) {
            mergedArray.append(item);
        }
    }
    return mergedArray;
}

void NvidiaCtrlNode::notifyWsMsgCallback(void *handle, const char *msg, int index) {
    nConnectIndex_ = index;
    currentTimeStramp_ = base::homiUtils::getCurrentTimeStamp();
    Json::Reader reader;
    Json::Value value;
    NvidiaCtrlNode *parent = (NvidiaCtrlNode *)handle;
    if(nullptr == parent) {
        RCLCPP_ERROR(rclcpp::get_logger("NvidiaCtrlNode"), "NvidiaCtrlNode *parent is NULL");
        return;
    }
    if (false == reader.parse(msg, value)) {
        RCLCPP_ERROR(rclcpp::get_logger("NvidiaCtrlNode"), "reader.parse failed");
        return;
    }
    if (value["action"].asString()!="uwb_data")
        RCLCPP_INFO_THROTTLE(parent->get_logger(),*parent->get_clock(),2000,"[NvidiaCtrlNode] notifyWsMsgCallback: %s", msg);
        // RCLCPP_INFO(rclcpp::get_logger("NvidiaCtrlNode"), "notifyWsMsgCallback: %s", msg);
    if (!value["type"].isNull()) {
        string strType = value["type"].asString();
        if ("connect_success" == strType) {
            bConnected_ = true;
            std::this_thread::sleep_for(std::chrono::milliseconds(20));
            Json::Value reqValue;
            reqValue["client_type"] = CLIENT_NVIDIA;
            reqValue["action"] = "success";
            WS_Send(reqValue.toStyledString().c_str(), nConnectIndex_);
        } 
    }
    if (value["client_type"].isNull()) {
        return;
    }
    string strClientType = value["client_type"].asString();
    if(CLIENT_NVIDIA != strClientType) {
        return;
    }
    if (value["action"].isNull()) {
        return;
    }
    string action = value["action"].asString();
    // RCLCPP_INFO(rclcpp::get_logger("NvidiaCtrlNode"), "action is : %s", action.c_str());
    Json::StreamWriterBuilder writer;
    std::string strParams = Json::writeString(writer, value["params"]);
    // RCLCPP_INFO(rclcpp::get_logger("NvidiaCtrlNode"), "going to parent publishLauncherMsg: %s", msg);

     if (action == "navigation_control") {
        parent->actionPlanningMove(strParams);
    }
    else if (action == "mapping_control") {
        parent->actionMappingControl(strParams);
    }
    else if (action == "charge_control") {
        parent->actionChargeControl(strParams);
    }
    else if(action == "ramble_control") {
        parent->actionRambleControl(strParams);
    }
    else if (action == "targetPose") {
        parent->actionTargetPose(strParams);
    }
    else if (action == "uwb_data") {
        parent->actionUwbData(strParams);
    }
    else if (action == "uwb_data_array") {
        parent->actionUwbDataArray(strParams);
    }

    else if (action == "virtual_wall_control") {
        parent->virtualWall(strParams);
    }
    else if (action == "pathPlanning") {
        parent->actionPlanningMove2(strParams);
    }
    else if (action == "tripStart"){
        RCLCPP_INFO(rclcpp::get_logger("NvidiaCtrlNode"), "tripStart:%s", strParams.c_str());
        parent->actionTripStart(strParams);
    }
    else if (action == "tripStop"){
        RCLCPP_INFO(rclcpp::get_logger("NvidiaCtrlNode"), "tripStop:%s", strParams.c_str());
        parent->actionTripStop(strParams);
    }
    else if (action == "tripPause"){
        RCLCPP_INFO(rclcpp::get_logger("NvidiaCtrlNode"), "tripPause:%s", strParams.c_str());
        parent->actionTripPause(strParams);
    }
    else {
        //未解析的默认传给跟随相关逻辑，
        //有velocity wakeup startFollow endFollow startUwbFollow endUwbFollow等
        parent->publishLauncherMsg(msg);
    }
}

NvidiaCtrlNode::NvidiaCtrlNode() : Node("nvidia_control_node") {
    this->declare_parameter<int>("ws_connect_port", WEBSOCKET_SERVER_PORT); 
    connectPort_ = this->get_parameter("ws_connect_port").as_int();  
    RCLCPP_INFO(rclcpp::get_logger("NvidiaCtrlNode"), "ws_connect_port: %d", connectPort_);
    this->declare_parameter<string>("ws_connect_url", WEBSOCKET_SERVER_URL); 
    strConnectUrl_ = this->get_parameter("ws_connect_url").as_string();  
    RCLCPP_INFO(rclcpp::get_logger("NvidiaCtrlNode"), "ws_connect_url: %s", strConnectUrl_.c_str());

    this->declare_parameter<int>("demo_mode", 0);
    int demo_mode = this->get_parameter("demo_mode").as_int();
    setDemoMode(demo_mode);
    RCLCPP_INFO(rclcpp::get_logger("NvidiaCtrlNode"), "demo_mode: %d", demo_mode);


    // WS服务启动
    WS_Init(EN_WS_ClIENT, connectPort_);
    //设置接受msg的回调函数
    WS_SetMsgCallback(notifyWsMsgCallback, this);
    WS_Connect(strConnectUrl_.c_str());


    //RTK数据订阅
    navSatFix_sub = create_subscription<sensor_msgs::msg::NavSatFix>("/fix", 10, 
        std::bind(&NvidiaCtrlNode::recNavSatFixCallback, this, std::placeholders::_1));

    /*******************************跟随相关**************************************************/
 
    pub_ = this->create_publisher<std_msgs::msg::String>("/follow_me/from_control", 10);

    sub_ = this->create_subscription<std_msgs::msg::String>("/follow_me/from_rcs", 10, 
        std::bind(&NvidiaCtrlNode::recRcsMsgCallback, this, std::placeholders::_1));

    pub_uwb_data_ = this->create_publisher<geometry_msgs::msg::Twist>("uwb_data", 10);
    pub_uwb_data_array_ = this->create_publisher<geometry_msgs::msg::Twist>("/uwb_triple/raw_data", 10);


    /*******************************导航相关**************************************************/
    timer_ = this->create_wall_timer(std::chrono::seconds(10), 
        std::bind(&NvidiaCtrlNode::timerCallback, this));

    if (getDemoMode() == 1) {
        demo_timer_ = this->create_wall_timer(std::chrono::seconds(1), 
            std::bind(&NvidiaCtrlNode::demoTimerCallback, this));
    }

    navPosition_sub_ = this->create_subscription<std_msgs::msg::String>("/navigation_position", 
        10, std::bind(&NvidiaCtrlNode::navPositionCallback, this, std::placeholders::_1));

    navPath_sub_ = this->create_subscription<std_msgs::msg::String>("/navigation_path", 
        10, std::bind(&NvidiaCtrlNode::navPathCallback, this, std::placeholders::_1));

    navStatus_sub_ = this->create_subscription<std_msgs::msg::String>("/task_status", 
        10, std::bind(&NvidiaCtrlNode::navStatusCallback, this, std::placeholders::_1));
    
    navHeart_sub_ = this->create_subscription<std_msgs::msg::String>("/console_status", 
        10, std::bind(&NvidiaCtrlNode::navHeartCallback, this, std::placeholders::_1));

    actionPlanningMove_pub = this->create_publisher<std_msgs::msg::String>("/navigation_control", 10);

    mappingControl_pub = this->create_publisher<std_msgs::msg::String>("/mapping_control", 10);

    chargeControl_pub = this->create_publisher<std_msgs::msg::String>("/charge_control", 10);

    publishVirtualWall = this->create_publisher<std_msgs::msg::String>("/virtual_wall_control", 10);

    rambleControl_pub = this->create_publisher<std_msgs::msg::String>("/ramble_control", 10);

    /***************************************************************************************** */
}

NvidiaCtrlNode::~NvidiaCtrlNode() {
}

void NvidiaCtrlNode::demoNavPositionCallback() {
    Json::Value value;
    value["client_type"] = CLIENT_NVIDIA;
    value["target_client"] = CLIENT_LAUNCHER;
    value["action"] = "navigation_position";
    Json::Value params;
    params["latitude"] = 30.295719146728516;
    params["longitude"] = 120.0228271484375;
    params["demo"] = "on";
    value["params"] = params;
    WS_Send(value.toStyledString().c_str(), nConnectIndex_);
    RCLCPP_INFO(rclcpp::get_logger("WSSeverMgr"), "demoNavPositionCallback: %s", value.toStyledString().c_str());
}

void NvidiaCtrlNode::demoRecNavSatFixCallback() {
    Json::Value value;
    value["client_type"] = CLIENT_NVIDIA;
    value["action"] = "robotPose";

    Json::Value params;
    params["latitude"] = 30.295719146728516;
    params["longitude"] = 120.0228271484375;
    params["demo"] = "on";
    value["params"] = params;
    value["target_client"] = CLIENT_LAUNCHER;
    WS_Send(value.toStyledString().c_str(), nConnectIndex_);
    RCLCPP_INFO(rclcpp::get_logger("demoRecNavSatFixCallback"), "send msg: %s", value.toStyledString().c_str());
}

void NvidiaCtrlNode::demoTimerCallback() {
    static int count = 0;
    if (count % 3 == 0) {
        int tripStatus = getTripStatus();
        if(tripStatus == GOING ||  tripStatus == PAUSE || tripStatus == CANCLE ){
            demoNavPositionCallback();
        }
        if(tripStatus == CANCLE){
            tripStatus = STOP;
        }
        demoRecNavSatFixCallback();
    }
    count++;
}

void NvidiaCtrlNode::timerCallback() {
    /***********webSocket的超时处理逻辑**********/
    if ((base::homiUtils::getCurrentTimeStamp() - currentTimeStramp_) > WEBSOCKET_CON_TIMEOUT) {
        bConnected_ = false;
        RCLCPP_INFO(rclcpp::get_logger("NvidiaCtrlNode"), "websocket reconnect: %ld, %ld", 
            base::homiUtils::getCurrentTimeStamp(), currentTimeStramp_);
        WS_Connect(strConnectUrl_.c_str());
    }
    /********************************************/
}

void NvidiaCtrlNode::actionTripStart(const string msg) {

    Json::Reader reader;
    Json::Value value;
    if (false == reader.parse(msg, value)) {
        RCLCPP_ERROR(rclcpp::get_logger("NvidiaCtrlNode"), "reader.parse failed: %s", reader.getFormattedErrorMessages().c_str());
        return;
    }
    Json::Value msg_value;
    msg_value["action"] = 1;
    msg_value["mode"] = 1;
    msg_value["batchId"] = value["tripId"].asInt64();

    Json::Value points = value["navigationPath"];
    modifyKeys(points);
    msg_value["points"] = points;

    std_msgs::msg::String nav_ctrl_msg;

    nav_ctrl_msg.data = msg_value.toStyledString();
    RCLCPP_INFO(rclcpp::get_logger("NvidiaCtrlNode"), "nav_ctrl_msg.data:%s", nav_ctrl_msg.data.c_str());
    actionPlanningMove_pub->publish(nav_ctrl_msg);

    setTripStatus(GOING);
}



void NvidiaCtrlNode::actionTripStop(const string msg) {
    Json::Reader reader;
    Json::Value value;
    if (false == reader.parse(msg, value)) {
        RCLCPP_ERROR(rclcpp::get_logger("NvidiaCtrlNode"), "reader.parse failed: %s", reader.getFormattedErrorMessages().c_str());
        return;
    }
    Json::Value msg_value;
    msg_value["action"] = 0;
    msg_value["mode"] = 1;
    msg_value["batchId"] = value["tripId"].asInt64();

    std_msgs::msg::String nav_ctrl_msg;

    nav_ctrl_msg.data = msg_value.toStyledString();
    RCLCPP_INFO(rclcpp::get_logger("NvidiaCtrlNode"), "nav_ctrl_msg.data:%s", nav_ctrl_msg.data.c_str());
    actionPlanningMove_pub->publish(nav_ctrl_msg);
    setTripStatus(CANCLE);
}

void NvidiaCtrlNode::actionTripPause(const string msg) {
        Json::Reader reader;
    Json::Value value;
    if (false == reader.parse(msg, value)) {
        RCLCPP_ERROR(rclcpp::get_logger("NvidiaCtrlNode"), "reader.parse failed: %s", reader.getFormattedErrorMessages().c_str());
        return;
    }
    Json::Value msg_value;
    msg_value["action"] = 13;
    msg_value["mode"] = 1;
    msg_value["batchId"] = value["tripId"].asInt64();

    std_msgs::msg::String nav_ctrl_msg;

    nav_ctrl_msg.data = msg_value.toStyledString();
    RCLCPP_INFO(rclcpp::get_logger("NvidiaCtrlNode"), "nav_ctrl_msg.data:%s", nav_ctrl_msg.data.c_str());
    actionPlanningMove_pub->publish(nav_ctrl_msg);
    setTripStatus(PAUSE);
}

void NvidiaCtrlNode::actionPlanningMove2(string msg3) {

    string msg2 =removeEscapeCharacters(msg3);
    string msg = msg2.substr(1, msg2.length() - 2);

    RCLCPP_INFO(rclcpp::get_logger("NvidiaCtrlNode"), "actionPlanningMove2:%s", msg.c_str());
    Json::Reader reader;
    Json::Value value;
    if (false == reader.parse(msg, value)) {
        RCLCPP_ERROR(rclcpp::get_logger("NvidiaCtrlNode"), "reader.parse failed: %s", reader.getFormattedErrorMessages().c_str());
        return;
    }

    RCLCPP_INFO(rclcpp::get_logger("NvidiaCtrlNode"), "value: %s", value["12"]["allLength"].toStyledString().c_str());

    Json::StreamWriterBuilder writer;
    std::string unescapedMsg = Json::writeString(writer, value["12"]);

    RCLCPP_INFO(rclcpp::get_logger("NvidiaCtrlNode"), "Unescaped JSON: %s", unescapedMsg.c_str());

    Json::Value msg_value;
    msg_value["action"] = 1;
    msg_value["mode"] = 1;
    msg_value["batchId"] = 1;

    if (!value["12"].isNull()) {
        Json::Value params = value["12"];
        RCLCPP_INFO(rclcpp::get_logger("NvidiaCtrlNode"), "params[12]:%s", params.toStyledString().c_str());
        Json::Value points = params["list"];
        msg_value["points"] = points;
    }

    std_msgs::msg::String nav_ctrl_msg;

    nav_ctrl_msg.data = msg_value.toStyledString();
    RCLCPP_INFO(rclcpp::get_logger("NvidiaCtrlNode"), "nav_ctrl_msg.data:%s", nav_ctrl_msg.data.c_str());
    actionPlanningMove_pub->publish(nav_ctrl_msg);
}

void NvidiaCtrlNode::actionPlanningMove(const string msg) {
    std_msgs::msg::String nav_ctrl_msg;
    nav_ctrl_msg.data = msg;
    actionPlanningMove_pub->publish(nav_ctrl_msg);
}

void NvidiaCtrlNode::virtualWall(const string msg) {
    std_msgs::msg::String nav_ctrl_msg;
    nav_ctrl_msg.data = msg;
    publishVirtualWall->publish(nav_ctrl_msg);
}

void NvidiaCtrlNode::actionMappingControl(const string msg) {
    std_msgs::msg::String map_ctrl_msg;
    map_ctrl_msg.data = msg;
    mappingControl_pub->publish(map_ctrl_msg);
}

void NvidiaCtrlNode::actionChargeControl(const string msg) {
    std_msgs::msg::String charge_ctrl_msg;
    charge_ctrl_msg.data = msg;
    chargeControl_pub->publish(charge_ctrl_msg);
}

void NvidiaCtrlNode::actionRambleControl(const string msg) {
    std_msgs::msg::String ramble_ctrl_msg;
    ramble_ctrl_msg.data = msg;
    rambleControl_pub->publish(ramble_ctrl_msg);
}
void NvidiaCtrlNode::actionTargetPose(const string msg) {
    Json::Reader reader;
    Json::Value jParaValue;
    reader.parse(msg, jParaValue);
    if (jParaValue.isNull()) {
        RCLCPP_ERROR(rclcpp::get_logger("actionTargetPose"), "json parse error");
        return;
    }
    Json::Value jMsgValue;
    jMsgValue["client_type"] = CLIENT_NVIDIA;
    jMsgValue["target_client"] = CLIENT_ANDROID;
    jMsgValue["action"] = "targetPose";

    jMsgValue["params"] = jParaValue;

    WS_Send(jMsgValue.toStyledString().c_str(), nConnectIndex_);
    RCLCPP_INFO(rclcpp::get_logger("actionTargetPose"), "send msg: %s", jMsgValue.toStyledString().c_str());
}

void NvidiaCtrlNode::actionUwbData(const string msg) {
    Json::Reader reader;
    Json::Value params;  
    if(false == reader.parse(msg, params)) {
        return;
    }
    float distance = params["distance"].asFloat();
    if(distance > 10) {
        return;
    }
    float angle = params["angle"].asFloat();
    geometry_msgs::msg::Twist twist;
    twist.linear.z = distance;
    twist.angular.z = angle;
    if(pub_uwb_data_) { 
        pub_uwb_data_->publish(twist);
    }
}

void NvidiaCtrlNode::actionUwbDataArray(const string msg) {
    Json::Reader reader;
    Json::Value params;  
    geometry_msgs::msg::Twist twist;
    if(false == reader.parse(msg, params)) {
        return;
    }
    float distance = 0.0;
    float angle = 0.0;
    Json::Value valueLeft = params["left"];
    distance = valueLeft["distance"].asFloat();
    angle = valueLeft["angle"].asFloat();
    twist.linear.z = distance;
    twist.angular.z = angle;
    twist.angular.x = 0;
    if(pub_uwb_data_array_) { 
        pub_uwb_data_array_->publish(twist);
    }

    Json::Value valueRight = params["right"];
    distance = valueRight["distance"].asFloat();
    angle = valueRight["angle"].asFloat();
    twist.linear.z = distance;
    twist.angular.z = angle;
    twist.angular.x = 1;
    if(pub_uwb_data_array_) { 
        pub_uwb_data_array_->publish(twist);
    }

    Json::Value valueFront = params["front"];
    distance = valueFront["distance"].asFloat();
    angle = valueFront["angle"].asFloat();
    twist.linear.z = distance;
    twist.angular.z = angle;
    twist.angular.x = 2;
    if(pub_uwb_data_array_) { 
        pub_uwb_data_array_->publish(twist);
    }
}



void NvidiaCtrlNode::navPositionCallback(const std_msgs::msg::String::SharedPtr msg) {
    // RCLCPP_INFO(rclcpp::get_logger("WSSeverMgr"), "Received msg form nav: %s", msg->data.c_str());
    std::string strMsg = msg->data;
    Json::Reader reader;
    Json::Value value;
    reader.parse(strMsg, value);
    if (value.isNull()) {
        RCLCPP_INFO(rclcpp::get_logger("WSSeverMgr"), "json parse error");
        return;
    }
    Json::Value jMsgValue;
    jMsgValue["client_type"] = CLIENT_NVIDIA;
    jMsgValue["target_client"] = CLIENT_LAUNCHER;
    jMsgValue["action"] = "navigation_position";
    jMsgValue["params"] = value; 
    WS_Send(jMsgValue.toStyledString().c_str(), nConnectIndex_);
}

void NvidiaCtrlNode::navPathCallback(const std_msgs::msg::String::SharedPtr msg) {
    // RCLCPP_INFO(rclcpp::get_logger("WSSeverMgr"), "Received msg form nav: %s", msg->data.c_str());
    std::string strMsg = msg->data;
    Json::Reader reader;
    Json::Value value;
    reader.parse(strMsg, value);
    if (value.isNull()) {
        RCLCPP_INFO(this->get_logger(), "json parse error");
        return;
    }
    Json::Value jMsgValue;
    jMsgValue["client_type"] = CLIENT_NVIDIA;
    jMsgValue["target_client"] = CLIENT_LAUNCHER;
    jMsgValue["action"] = "navigation_path";
    jMsgValue["params"] = value; 
    WS_Send(jMsgValue.toStyledString().c_str(), nConnectIndex_);
}
void NvidiaCtrlNode::navStatusCallback(const std_msgs::msg::String::SharedPtr msg) {
    // RCLCPP_INFO(rclcpp::get_logger("WSSeverMgr"), "Received msg form nav: %s", msg->data.c_str());
    std::string strMsg = msg->data;
    Json::Reader reader;
    Json::Value value;
    reader.parse(strMsg, value);
    if (value.isNull()) {
        RCLCPP_INFO(this->get_logger(), "json parse error");
        return;
    }
    Json::Value jMsgValue;
    jMsgValue["client_type"] = CLIENT_NVIDIA;
    jMsgValue["target_client"] = CLIENT_LAUNCHER;
    jMsgValue["action"] = "task_status";
    jMsgValue["params"] = value; 
    WS_Send(jMsgValue.toStyledString().c_str(), nConnectIndex_);
}

void NvidiaCtrlNode::navHeartCallback(const std_msgs::msg::String::SharedPtr msg) {
    // RCLCPP_INFO(rclcpp::get_logger("WSSeverMgr"), "Received msg form nav: %s", msg->data.c_str());
    std::string strMsg = msg->data;
    Json::Reader reader;
    Json::Value value;
    reader.parse(strMsg, value);
    if (value.isNull()) {
        RCLCPP_INFO(this->get_logger(), "json parse error");
        return;
    }
    Json::Value jMsgValue;
    jMsgValue["client_type"] = CLIENT_NVIDIA;
    jMsgValue["target_client"] = CLIENT_LAUNCHER;
    jMsgValue["action"] = "console_status";
    jMsgValue["params"] = value; 
    WS_Send(jMsgValue.toStyledString().c_str(), nConnectIndex_);
}
void NvidiaCtrlNode::recNavSatFixCallback(const sensor_msgs::msg::NavSatFix::SharedPtr msg) {
    Json::Value value;
    value["client_type"] = CLIENT_NVIDIA;
    // value["target_client"] = CLIENT_ANDROID;
    value["action"] = "robotPose";

    Json::Value params;
    float latitude = msg->latitude;
    float longitude = msg->longitude;
    params["latitude"] = latitude;
    params["longitude"] = longitude;
    params["status"]=msg->status.status;
    setLatitude(latitude);
    setLongitude(longitude);
    value["params"] = params;
    // WS_Send(value.toStyledString().c_str(), nConnectIndex_);
    value["target_client"] = CLIENT_LAUNCHER;
    WS_Send(value.toStyledString().c_str(), nConnectIndex_);
}


void NvidiaCtrlNode::getGaoDeNavigation(float originLatitude, float originLongitude, float destinationLatitude, float destinationLongitude) {


    std::ostringstream oss;
    std::string command = "curl -s \"";
    std::string key="2448c1137ca178909153ddef36576838\"";

    std::string apiURL="https://restapi.amap.com/v3/direction/walking?origin=";
    // =${ORIGIN_LONGITUDE},${ORIGIN_LATITUDE}&destination=${DESTINATION_LONGITUDE},${DESTINATION_LATITUDE}&key=${KEY}"

    oss << std::fixed << std::setprecision(15);
    oss  << command << apiURL  << originLatitude << "," << originLongitude << "&destination=" << destinationLatitude << "," << destinationLongitude << "&key=" << key;
    std::string cmd = oss.str();

    std::string result = exec(cmd.c_str());

    RCLCPP_INFO(rclcpp::get_logger("NvidiaCtrlNode"), "actionPlanningMove2:%s", cmd.c_str());
    RCLCPP_INFO(rclcpp::get_logger("NvidiaCtrlNode"), "actionPlanningMove2:%s", result.c_str());

    Json::Reader reader;
    Json::Value value;
    reader.parse(result, value);
    if (value.isNull()) {
        RCLCPP_INFO(this->get_logger(), "json parse error");
        return;
    }
    if (!value["route"].isNull()) {
        if(!value["route"]["paths"].isNull()) {
            Json::Value paths = value["route"]["paths"];
            if (!paths[0].isNull()) {
                Json::Value path = paths[0];
                if (!path["steps"].isNull()) {
                    Json::Value steps = path["steps"];
                    Json::Value all_json_array(Json::arrayValue);
                    for (int i = 0; i < steps.size(); i++) {
                        Json::Value step = steps[i];
                        if (!step["polyline"].isNull()) {
                            std::vector<std::pair<float, float>> coordinates = parseCoordinates(step["polyline"].asString());
                            Json::Value jsonArray = convertToJSON(coordinates);
                            all_json_array = mergeJsonArrays(all_json_array, jsonArray);
                        }
                    }
                    // Json::StreamWriterBuilder writer;
                    // writer["indentation"] = "    ";
                    // std::string output = Json::writeString(writer, all_json_array);
                    // RCLCPP_INFO(rclcpp::get_logger("NvidiaCtrlNode"), "output: %s", output.c_str());
                    Json::Value msg_value;
                    msg_value["action"] = 1;
                    msg_value["mode"] = 1;
                    msg_value["batchId"] = 1;
                    msg_value["points"] = all_json_array;

                    std_msgs::msg::String nav_ctrl_msg;

                    nav_ctrl_msg.data = msg_value.toStyledString();
                    RCLCPP_INFO(rclcpp::get_logger("NvidiaCtrlNode"), "nav_ctrl_msg.data:%s", nav_ctrl_msg.data.c_str());
                    actionPlanningMove_pub->publish(nav_ctrl_msg);
                }
            }
        }
    }





    // std::string result = oss.str();

    // string msg2 =removeEscapeCharacters(msg3);
    // string msg = msg2.substr(1, msg2.length() - 2);

    // RCLCPP_INFO(rclcpp::get_logger("NvidiaCtrlNode"), "actionPlanningMove2:%s", msg.c_str());
    // Json::Reader reader;
    // Json::Value value;
    // if (false == reader.parse(msg, value)) {
    //     RCLCPP_ERROR(rclcpp::get_logger("NvidiaCtrlNode"), "reader.parse failed: %s", reader.getFormattedErrorMessages().c_str());
    //     return;
    // }

    // RCLCPP_INFO(rclcpp::get_logger("NvidiaCtrlNode"), "value: %s", value["12"]["allLength"].toStyledString().c_str());

    // Json::StreamWriterBuilder writer;
    // std::string unescapedMsg = Json::writeString(writer, value["12"]);

    // RCLCPP_INFO(rclcpp::get_logger("NvidiaCtrlNode"), "Unescaped JSON: %s", unescapedMsg.c_str());

    // Json::Value msg_value;
    // msg_value["action"] = 1;
    // msg_value["mode"] = 1;
    // msg_value["batchId"] = 1;

    // if (!value["12"].isNull()) {
    //     Json::Value params = value["12"];
    //     RCLCPP_INFO(rclcpp::get_logger("NvidiaCtrlNode"), "params[12]:%s", params.toStyledString().c_str());
    //     Json::Value points = params["list"];
    //     msg_value["points"] = points;
    // }

    // std_msgs::msg::String nav_ctrl_msg;

    // nav_ctrl_msg.data = msg_value.toStyledString();
    // RCLCPP_INFO(rclcpp::get_logger("NvidiaCtrlNode"), "nav_ctrl_msg.data:%s", nav_ctrl_msg.data.c_str());
    // actionPlanningMove_pub->publish(nav_ctrl_msg);
}


void NvidiaCtrlNode::recRcsMsgCallback(const std_msgs::msg::String::SharedPtr msg) {
    std::string strSendMsg = msg->data;
    Json::Reader reader;
    Json::Value value;  
    if(false == reader.parse(strSendMsg, value)) {
        return;
    }
    //收到的follow_rcs跟随相关命令，直接传给讯飞控制板的websockect
    value["client_type"] = CLIENT_NVIDIA;
    value["target_client"] = CLIENT_LAUNCHER;
    WS_Send(value.toStyledString().c_str(), nConnectIndex_);
}

void NvidiaCtrlNode::publishLauncherMsg(const std::string& strMsg){
    Json::Reader reader;
    Json::Value value;  
    if(false == reader.parse(strMsg, value)) {
        return;
    }
    RCLCPP_INFO(rclcpp::get_logger("NvidiaCtrlNode"), "publishLauncherMsg: %s", strMsg.c_str());
    std::string strClientType = CLIENT_LAUNCHER;
    if(!value["client_type"].isNull()) {
        strClientType = value["client_type"].asString();
        RCLCPP_INFO(rclcpp::get_logger("NvidiaCtrlNode"), "client_type is : %s", strClientType.c_str());
    }
    if(CLIENT_NVIDIA == strClientType) {
        if (pub_) {
            std_msgs::msg::String strMsgs_;
            strMsgs_.data = strMsg;
            pub_->publish(strMsgs_);
            RCLCPP_INFO(rclcpp::get_logger("WSSeverMgr"), "send msg: %s", strMsg.c_str());
        }
    }
}