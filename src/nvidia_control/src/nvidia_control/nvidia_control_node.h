
#include <rclcpp/rclcpp.hpp>
#include <iostream>
#include "std_msgs/msg/string.hpp"
#include "geometry_msgs/msg/twist.hpp"
#include "geometry_msgs/msg/pose.hpp"
#include "sensor_msgs/msg/nav_sat_fix.hpp"

#include <chrono>
#include <string>
#include <map>

#include "libWebSocket.h"
#include <jsoncpp/json/json.h>

#define GOING    1
#define STOP     2
#define PAUSE    3
#define CANCLE   4

class NvidiaCtrlNode: public rclcpp::Node {
public:
    NvidiaCtrlNode();
    ~NvidiaCtrlNode();
    static void notifyWsMsgCallback(void *handle, const char *msg, int index) ;
    void recNavSatFixCallback(const sensor_msgs::msg::NavSatFix::SharedPtr msg);
    void recRcsMsgCallback(const std_msgs::msg::String::SharedPtr msg);
    void publishLauncherMsg(const std::string& strMsg);

    void demoNavPositionCallback();
    void demoRecNavSatFixCallback();
    void demoTimerCallback();

    void timerCallback();
    void navPositionCallback(const std_msgs::msg::String::SharedPtr msg);
    void navStatusCallback(const std_msgs::msg::String::SharedPtr msg);
    void navHeartCallback(const std_msgs::msg::String::SharedPtr msg);
    void navPathCallback(const std_msgs::msg::String::SharedPtr msg);
    
    void actionTripStart(const string msg);
    void actionTripStop(const string msg);
    void actionTripPause(const string msg);
    void actionPlanningMove(const string msg);
    void actionPlanningMove2(const string msg);
    void actionMappingControl(const string msg);
    void actionChargeControl(const string msg);
    void actionRambleControl(const string msg);
    void actionTargetPose(const string msg);

    void actionUwbData(const string msg);
    void actionUwbDataArray(const string msg);

    void virtualWall(const string msg);

    float getLatitude() {
        return originLatitude_;
    };
    void setLatitude(float latitude) {
        originLatitude_ = latitude;
    };
    float getLongitude() {
        return originLongitude_;
    };
    void setLongitude(float longitude) {
        originLongitude_ = longitude;
    };
    void getGaoDeNavigation(float ORIGIN_LONGITUDE, float ORIGIN_LATITUDE, float DESTINATION_LONGITUDE, float DESTINATION_LATITUDE);

    int getDemoMode() {
        return demo_mode_;
    };
    void setDemoMode(int mode) {
        demo_mode_ = mode;
    }

    void setTripStatus(int status) { tripStatus_ = status; };

    int getTripStatus() { return tripStatus_; };
    rclcpp::Logger get_logger() const { return Node::get_logger(); }
    rclcpp::Clock::SharedPtr get_clock() { return Node::get_clock(); }

private: 
    rclcpp::TimerBase::SharedPtr timer_;
    rclcpp::TimerBase::SharedPtr demo_timer_;
    string strConnectUrl_ = "";
    int connectPort_ = 0;

    float originLatitude_ = 0.0;
    float originLongitude_ = 0.0;

    int demo_mode_ = 0;

    int tripStatus_ = 0;


    //RTK数据订阅
    rclcpp::Subscription<sensor_msgs::msg::NavSatFix>::SharedPtr navSatFix_sub = nullptr;
    rclcpp::Subscription<sensor_msgs::msg::NavSatFix>::SharedPtr navSatFix_sub2 = nullptr;

    // 从感知主机订阅者机器人实时位置
    rclcpp::Subscription<std_msgs::msg::String>::SharedPtr navPosition_sub_ = nullptr;
    rclcpp::Subscription<std_msgs::msg::String>::SharedPtr navPath_sub_ = nullptr;
    // 地图更新给感知主机
    rclcpp::Subscription<std_msgs::msg::String>::SharedPtr navStatus_sub_ = nullptr;
    rclcpp::Subscription<std_msgs::msg::String>::SharedPtr navHeart_sub_ = nullptr; //监听档心跳
    // 发布机器狗固定点位坐标到感知主机
    rclcpp::Publisher<std_msgs::msg::String>::SharedPtr actionPlanningMove_pub = nullptr;
    // 地图更新给感知主机
    rclcpp::Publisher<std_msgs::msg::String>::SharedPtr mappingControl_pub = nullptr;
    rclcpp::Publisher<std_msgs::msg::String>::SharedPtr publishVirtualWall = nullptr;
    //室内漫步接口
    rclcpp::Publisher<std_msgs::msg::String>::SharedPtr rambleControl_pub = nullptr;
    //充电接口
    rclcpp::Publisher<std_msgs::msg::String>::SharedPtr chargeControl_pub = nullptr;
    /*****TODO 后续这些topic都改为webSocket传输******/
    //和launcher的交互（机器人应用本体端）
    rclcpp::Publisher<std_msgs::msg::String>::SharedPtr pub_ = nullptr;
    rclcpp::Subscription<std_msgs::msg::String>::SharedPtr sub_= nullptr;
    //和部署uwb硬件模块的控制器的交互
    rclcpp::Publisher<geometry_msgs::msg::Twist>::SharedPtr pub_uwb_data_ = nullptr;
    rclcpp::Publisher<geometry_msgs::msg::Twist>::SharedPtr pub_uwb_data_array_ = nullptr;

};
