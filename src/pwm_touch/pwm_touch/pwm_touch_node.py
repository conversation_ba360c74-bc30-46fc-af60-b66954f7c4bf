import math
import os
import select
import enum
import rclpy
from rclpy.node import Node
from std_msgs.msg import String
from std_srvs.srv import SetBool, Trigger
from rcl_interfaces.srv import SetParameters
from rcl_interfaces.msg import SetParametersResult, Parameter, ParameterValue, ParameterType
import json
import time
import threading

# LED模式枚举
class LedMode(enum.Enum):
    STATIC = "static"          # 静态颜色模式
    BREATH = "breath"          # 呼吸灯模式
    FLOW = "flow"             # 流水灯模式
    OFF = "off"               # 关闭模式
    BLINK = "blink"           # 闪烁模式
    RAINBOW = "rainbow"        # 彩虹模式
    GRADIENT = "gradient"      # 渐变模式

class Color:
    def __init__(self, r=0, g=0, b=0):
        self.r = r
        self.g = g
        self.b = b

    @staticmethod
    def from_hex(hex_color):
        """从十六进制颜色代码创建Color对象"""
        hex_color = hex_color.lstrip('#')
        return Color(
            int(hex_color[0:2], 16),
            int(hex_color[2:4], 16),
            int(hex_color[4:6], 16)
        )

    def to_duty_cycle(self, max_duty):
        """将RGB值（0-255）转换为PWM占空比"""
        return (
            int(self.r / 255 * max_duty),
            int(self.g / 255 * max_duty),
            int(self.b / 255 * max_duty)
        )

class ColorEffect:
    def __init__(self, start_color, end_color=None, speed=1.0):
        self.start_color = start_color
        self.end_color = end_color if end_color else start_color
        self.speed = speed
        self.phase = 0.0

    def get_current_color(self, progress):
        """根据进度（0-1）计算当前颜色"""
        r = self.start_color.r + (self.end_color.r - self.start_color.r) * progress
        g = self.start_color.g + (self.end_color.g - self.start_color.g) * progress
        b = self.start_color.b + (self.end_color.b - self.start_color.b) * progress
        return Color(int(r), int(g), int(b))

# 新增：PwmChannel类
class PwmChannel:
    def __init__(self, pwmchip, channel, period, duty, polarity="normal"):
        self.pwmchip = pwmchip
        self.channel = channel
        self.period = period
        self.duty = duty
        self.polarity = polarity

    def configure(self):
        self._configure_via_sysfs(self.period, self.duty, self.polarity)

    def set_duty(self, duty):
        self.duty = duty
        self._configure_via_sysfs(self.period, self.duty, self.polarity)

    def set_duty_cycle(self, value, pwmchip='pwmchip0', channel='0'):
        pwm_path = f"/sys/class/pwm/{pwmchip}/pwm{channel}/duty_cycle"
        duty = max(0, min(value, self.period))        
        with open(pwm_path, 'w') as f:
            f.write(str(duty))

    def disable(self):
        """禁用PWM通道"""
        try:
            pwm_path = f"/sys/class/pwm/{self.pwmchip}/pwm{self.channel}"
            # 首先禁用PWM
            with open(f"{pwm_path}/enable", 'w') as f:
                f.write("0")
            # 然后取消导出
            unexport_path = f"/sys/class/pwm/{self.pwmchip}/unexport"
            with open(unexport_path, 'w') as f:
                f.write(str(self.channel))
        except Exception as e:
            raise Exception(f"禁用PWM通道失败: {str(e)}")

    def cleanup(self):
        """清理PWM资源"""
        try:
            self.disable()
        except Exception as e:
            print(f"清理PWM资源时出错: {str(e)}")

    def _configure_via_sysfs(self, period, duty_cycle, polarity):
        pwm_path = f"/sys/class/pwm/{self.pwmchip}/pwm{self.channel}"
        export_path = f"/sys/class/pwm/{self.pwmchip}/export"
        if not os.path.exists(pwm_path):
            with open(export_path, 'w') as f:
                f.write(str(self.channel))
        with open(f"{pwm_path}/period", 'w') as f:
            f.write(str(period))
        with open(f"{pwm_path}/duty_cycle", 'w') as f:
            f.write(str(duty_cycle))
        with open(f"{pwm_path}/polarity", 'w') as f:
            f.write(polarity)
        with open(f"{pwm_path}/enable", 'w') as f:
            f.write("1")

# 新增：TouchInput类（原GpioInput）
class TouchInput:
    """触摸输入控制类"""
    def __init__(self, node):
        self.node = node
        self.gpio_pins = []
        self.fds = {}
        self.epoll = select.epoll()
        self.is_running = True
        self.last_touch_time = {}  # 用于防抖
        self.debounce_time = 0.5   # 防抖时间（秒）改为500毫秒
        
        # 新增：边缘检测相关
        self.last_gpio_states = {}  # 记录每个引脚的上次状态
        self.rising_edge_detected = {}  # 记录是否检测到上升沿
        self.falling_edge_detected = {}  # 记录是否检测到下降沿

        # 高电平时间过滤相关
        self.rising_edge_time = {}  # 记录上升沿触发时间
        self.min_high_duration = 0.1  # 最小高电平持续时间（50ms）
        self.pending_rising_edges = {}  # 待确认的上升沿事件
        self.rising_edge_timers = {}  # 上升沿确认定时器
        
        self.node.get_logger().info("触摸输入控制器初始化完成")
        
    def setup(self, gpio_pins):
        """设置GPIO引脚"""
        self.gpio_pins = gpio_pins
        self.node.get_logger().info(f"开始设置GPIO引脚: {self.gpio_pins}")
        self._setup_gpios()
        
    def _setup_gpios(self):
        """初始化GPIO配置"""
        try:
            for pin in self.gpio_pins:
                gpio_path = f"/sys/class/gpio/gpio{pin}"
                # 如果GPIO已经导出，先取消导出
                if os.path.exists(gpio_path):
                    self.node.get_logger().debug(f"GPIO {pin} 已存在，准备重新导出")
                    with open("/sys/class/gpio/unexport", "w") as f:
                        f.write(str(pin))
                
                # 导出GPIO
                self.node.get_logger().debug(f"导出 GPIO {pin}")
                with open("/sys/class/gpio/export", "w") as f:
                    f.write(str(pin))
                
                # 配置为输入模式
                self.node.get_logger().debug(f"设置 GPIO {pin} 为输入模式")
                with open(f"{gpio_path}/direction", "w") as f:
                    f.write("in")
                
                # 配置边缘触发
                self.node.get_logger().debug(f"设置 GPIO {pin} 为双边沿触发")
                with open(f"{gpio_path}/edge", "w") as f:
                    f.write("both")
                
                # 打开文件描述符
                fd = os.open(f"{gpio_path}/value", os.O_RDONLY | os.O_NONBLOCK)
                self.epoll.register(fd, select.EPOLLPRI)
                self.fds[pin] = fd
                self.last_touch_time[pin] = 0
                
                # 初始化边缘检测
                self.last_gpio_states[pin] = False
                self.rising_edge_detected[pin] = False
                self.falling_edge_detected[pin] = False

                # 初始化高电平时间过滤
                self.rising_edge_time[pin] = 0
                self.pending_rising_edges[pin] = False
                self.rising_edge_timers[pin] = None
                
                # 初始读取
                os.lseek(fd, 0, os.SEEK_SET)
                os.read(fd, 1)
                
            self.node.get_logger().info(f"GPIO引脚初始化成功: {self.gpio_pins}")
        except Exception as e:
            self.node.get_logger().error(f"GPIO初始化失败: {str(e)}")
            self.cleanup()
            raise

    def read_status(self):
        """读取所有GPIO状态"""
        status = {}
        try:
            for pin, fd in self.fds.items():
                os.lseek(fd, 0, os.SEEK_SET)
                value = os.read(fd, 1).decode().strip()
                status[pin] = (value == '1')
                self.node.get_logger().debug(f"GPIO {pin} 状态: {value}")
        except Exception as e:
            self.node.get_logger().error(f"读取GPIO状态失败: {str(e)}")
        return status

    def poll_events(self, timeout=-1):
        """轮询GPIO事件"""
        try:
            return self.epoll.poll(timeout)
        except Exception as e:
            self.node.get_logger().error(f"轮询GPIO事件失败: {str(e)}")
            return []

    def check_edge(self, pin, current_value):
        """检查是否检测到边缘变化，使用定时器机制进行高电平时间过滤"""
        last_state = self.last_gpio_states.get(pin, False)
        current_time = time.time()
        edge_type = None

        # 检测上升沿：从低电平变为高电平
        if not last_state and current_value:
            # 记录上升沿时间
            self.rising_edge_time[pin] = current_time
            self.pending_rising_edges[pin] = True
            self.node.get_logger().debug(f"检测到GPIO {pin} 上升沿，启动定时器验证")

            # 取消之前的定时器（如果存在）
            if self.rising_edge_timers[pin] is not None:
                self.rising_edge_timers[pin].cancel()

            # 创建定时器，在最小持续时间后检查上升沿是否仍然有效
            self.rising_edge_timers[pin] = threading.Timer(
                self.min_high_duration,
                self._check_rising_edge_validity,
                args=[pin]
            )
            self.rising_edge_timers[pin].start()

        # 检测下降沿：从高电平变为低电平
        elif last_state and not current_value:
            # 取消定时器（如果存在）
            if self.rising_edge_timers[pin] is not None:
                self.rising_edge_timers[pin].cancel()
                self.rising_edge_timers[pin] = None

            # 检查是否有待确认的上升沿需要处理
            if self.pending_rising_edges.get(pin, False):
                high_duration = current_time - self.rising_edge_time.get(pin, 0)

                if high_duration >= self.min_high_duration:
                    # 高电平持续时间足够，确认上升沿有效
                    self.rising_edge_detected[pin] = True
                    self.node.get_logger().info(f"GPIO {pin} 上升沿有效 (高电平持续 {high_duration:.3f}s)")
                    edge_type = "rising"
                else:
                    # 高电平持续时间太短，过滤掉这次上升沿
                    self.node.get_logger().debug(f"GPIO {pin} 上升沿被过滤 (高电平仅持续 {high_duration:.3f}s < {self.min_high_duration}s)")

                # 清除待确认状态
                self.pending_rising_edges[pin] = False

            # 如果没有处理上升沿，则处理下降沿
            if edge_type != "rising":
                self.falling_edge_detected[pin] = True
                self.node.get_logger().info(f"检测到GPIO {pin} 下降沿")
                edge_type = "falling"

        # 更新上次状态
        self.last_gpio_states[pin] = current_value
        return edge_type

    def has_rising_edge(self, pin):
        """检查指定引脚是否有上升沿"""
        return self.rising_edge_detected.get(pin, False)

    def has_falling_edge(self, pin):
        """检查指定引脚是否有下降沿"""
        return self.falling_edge_detected.get(pin, False)

    def clear_rising_edge(self, pin):
        """清除指定引脚的上升沿标志"""
        self.rising_edge_detected[pin] = False

    def clear_falling_edge(self, pin):
        """清除指定引脚的下降沿标志"""
        self.falling_edge_detected[pin] = False

    def set_min_high_duration(self, duration):
        """设置最小高电平持续时间（秒）"""
        if duration > 0:
            self.min_high_duration = duration
            self.node.get_logger().info(f"设置最小高电平持续时间为: {duration}s")
        else:
            self.node.get_logger().warn(f"无效的最小高电平持续时间: {duration}，保持当前值: {self.min_high_duration}s")

    def get_min_high_duration(self):
        """获取当前最小高电平持续时间"""
        return self.min_high_duration

    def _check_rising_edge_validity(self, pin):
        """定时器回调：检查上升沿是否仍然有效"""
        try:
            # 检查引脚是否仍在等待确认状态
            if not self.pending_rising_edges.get(pin, False):
                return

            # 读取当前GPIO状态
            fd = self.fds.get(pin)
            if fd is None:
                return

            try:
                os.lseek(fd, 0, os.SEEK_SET)
                value = os.read(fd, 1).decode().strip()
                current_value = (value == '1')

                # 如果仍然是高电平，确认上升沿有效
                if current_value:
                    current_time = time.time()
                    high_duration = current_time - self.rising_edge_time.get(pin, 0)

                    self.rising_edge_detected[pin] = True
                    self.pending_rising_edges[pin] = False
                    self.rising_edge_timers[pin] = None

                    self.node.get_logger().info(f"GPIO {pin} 上升沿有效 (定时器确认，高电平持续 {high_duration:.3f}s)")

                    # 通知主节点有新的上升沿事件
                    if hasattr(self.node, '_handle_timer_rising_edge'):
                        self.node._handle_timer_rising_edge(pin)
                else:
                    # 已经变为低电平，清除待确认状态
                    self.pending_rising_edges[pin] = False
                    self.rising_edge_timers[pin] = None
                    self.node.get_logger().debug(f"GPIO {pin} 定时器检查时已变为低电平")

            except Exception as e:
                self.node.get_logger().error(f"定时器读取GPIO {pin} 失败: {e}")

        except Exception as e:
            self.node.get_logger().error(f"定时器回调异常 GPIO {pin}: {e}")

    def cleanup(self):
        """清理资源"""
        try:
            self.is_running = False

            # 取消所有定时器
            for pin, timer in self.rising_edge_timers.items():
                if timer is not None:
                    timer.cancel()
            self.rising_edge_timers.clear()

            self.epoll.close()

            # 关闭文件描述符
            for fd in self.fds.values():
                try:
                    os.close(fd)
                except Exception as e:
                    self.node.get_logger().warn(f"关闭文件描述符失败: {str(e)}")
            
            # 取消导出GPIO
            for pin in self.gpio_pins:
                try:
                    self.node.get_logger().debug(f"取消导出 GPIO {pin}")
                    with open("/sys/class/gpio/unexport", "w") as f:
                        f.write(str(pin))
                except Exception as e:
                    self.node.get_logger().warn(f"取消导出GPIO {pin} 失败: {str(e)}")
            
            self.node.get_logger().info("GPIO资源清理完成")
        except Exception as e:
            self.node.get_logger().error(f"清理GPIO资源时发生错误: {str(e)}")

    def is_debounced(self, pin):
        """检查是否在防抖时间内"""
        current_time = time.time()
        if current_time - self.last_touch_time.get(pin, 0) > self.debounce_time:
            self.last_touch_time[pin] = current_time
            return False
        return True

# 新增：BreathLedChannel（呼吸灯）和FlowLedChannel（流水灯）
class BreathLedChannel(PwmChannel):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.breath_phase = 0.0

    def update_breath(self):
        min_duty = 0
        max_duty = self.period
        self.breath_phase += 0.04
        if self.breath_phase > 2 * math.pi:
            self.breath_phase -= 2 * math.pi
        ratio = (math.sin(self.breath_phase) + 1) / 2
        duty = int(min_duty + (max_duty - min_duty) * ratio)
        self.set_duty(duty)
        return duty

class FlowLedChannel(PwmChannel):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.state = False

    def update_flow(self, on):
        if on:
            self.set_duty(self.period)
        else:
            self.set_duty(0)
        self.state = on

class LightController:
    """LED灯光控制器类"""
    def __init__(self, node, pwm_channels):
        self.node = node
        self.pwm_channels = pwm_channels
        self.current_effect = None
        self.effect_timer = None

    def update_mode(self, mode, color_hex, secondary_color_hex=None):
        """更新LED模式和颜色"""
        self.node.get_logger().info(f"更新灯光模式: {mode}, 主要颜色: {color_hex}, 次要颜色: {secondary_color_hex}")
        
        if self.effect_timer:
            self.effect_timer.cancel()
            self.effect_timer = None

        try:
            color = Color.from_hex(color_hex)
            secondary_color = Color.from_hex(secondary_color_hex) if secondary_color_hex else None
            
            if mode == LedMode.STATIC.value:
                self._set_color(color)
                self.node.get_logger().info(f"设置静态颜色: {color_hex}")
            
            elif mode == LedMode.BREATH.value:
                self.current_effect = ColorEffect(
                    Color(0, 0, 0), color,
                    self.node.get_parameter('effect_speed').get_parameter_value().double_value
                )
                # 修改呼吸效果的速度计算
                breath_period = 0.05 / self.node.get_parameter('effect_speed').get_parameter_value().double_value
                self.effect_timer = self.node.create_timer(breath_period, self._breath_callback)
                self.node.get_logger().info(f"启动呼吸灯效果，周期: {breath_period}秒")
            
            elif mode == LedMode.RAINBOW.value:
                self.current_effect = ColorEffect(color, None)
                # 修改彩虹效果的速度计算
                rainbow_period = 0.05 / self.node.get_parameter('effect_speed').get_parameter_value().double_value
                self.effect_timer = self.node.create_timer(rainbow_period, self._rainbow_callback)
                self.node.get_logger().info(f"启动彩虹效果，周期: {rainbow_period}秒")
            
            elif mode == LedMode.GRADIENT.value:
                if not secondary_color:
                    secondary_color = Color.from_hex('#000000')
                self.current_effect = ColorEffect(
                    color, secondary_color,
                    self.node.get_parameter('effect_speed').get_parameter_value().double_value
                )
                # 修改渐变效果的速度计算
                gradient_period = 0.05 / self.node.get_parameter('effect_speed').get_parameter_value().double_value
                self.effect_timer = self.node.create_timer(gradient_period, self._gradient_callback)
                self.node.get_logger().info(f"启动渐变效果，周期: {gradient_period}秒")
            
            elif mode == LedMode.FLOW.value:
                self.current_effect = ColorEffect(color, None)
                # 修改流水灯效果的速度计算 - 直接切换颜色，不需要中间关闭状态，并加快默认速度
                flow_period = 0.1 / self.node.get_parameter('effect_speed').get_parameter_value().double_value
                self.effect_timer = self.node.create_timer(flow_period, self._flow_callback)
                self.node.get_logger().info(f"启动流水灯效果，周期: {flow_period}秒")
            
            elif mode == LedMode.BLINK.value:
                self.current_effect = ColorEffect(color, None)
                # 闪烁效果的速度计算
                blink_period = 0.5 / (2.0 * self.node.get_parameter('effect_speed').get_parameter_value().double_value)
                self.effect_timer = self.node.create_timer(blink_period, self._blink_callback)
                self.node.get_logger().info(f"启动闪烁效果，周期: {blink_period}秒")
            
            elif mode == LedMode.OFF.value:
                self._set_color(Color(0, 0, 0))
                self.node.get_logger().info("关闭所有LED")
            
        except Exception as e:
            self.node.get_logger().error(f"更新灯光模式失败: {str(e)}")

    def _set_color(self, color):
        """设置RGB颜色"""
        try:
            brightness = self.node.get_parameter('brightness').get_parameter_value().double_value
            r, g, b = color.to_duty_cycle(self.pwm_channels[0].period)
            
            # 应用亮度
            r = int(r * brightness)
            g = int(g * brightness)
            b = int(b * brightness)
            
            self.pwm_channels[0].set_duty(r)
            self.pwm_channels[1].set_duty(g)
            self.pwm_channels[2].set_duty(b)
            
            self.node.get_logger().debug(f"设置PWM占空比 - R: {r}, G: {g}, B: {b}, 亮度: {brightness}")
        except Exception as e:
            self.node.get_logger().error(f"设置颜色失败: {str(e)}")

    def _breath_callback(self):
        """呼吸效果回调"""
        speed = self.current_effect.speed
        self.current_effect.phase += 0.1 * speed  # 根据速度调整相位变化
        progress = (math.sin(self.current_effect.phase) + 1) / 2
        color = self.current_effect.get_current_color(progress)
        self._set_color(color)

    def _rainbow_callback(self):
        """彩虹效果回调"""
        speed = self.current_effect.speed
        self.current_effect.phase += 0.1 * speed  # 根据速度调整相位变化
        hue = (self.current_effect.phase % (2 * math.pi)) / (2 * math.pi)
        
        # HSV转RGB
        h = hue * 6
        i = int(h)
        f = h - i
        if i & 1:
            f = 1 - f
        
        m = 0
        v = 255
        a = int(v * f)
        
        if i == 0:
            self._set_color(Color(v, a, m))
        elif i == 1:
            self._set_color(Color(a, v, m))
        elif i == 2:
            self._set_color(Color(m, v, a))
        elif i == 3:
            self._set_color(Color(m, a, v))
        elif i == 4:
            self._set_color(Color(a, m, v))
        else:
            self._set_color(Color(v, m, a))

    def _gradient_callback(self):
        """渐变效果回调"""
        speed = self.current_effect.speed
        self.current_effect.phase += 0.1 * speed  # 根据速度调整相位变化
        progress = (math.sin(self.current_effect.phase) + 1) / 2
        color = self.current_effect.get_current_color(progress)
        self._set_color(color)

    def _flow_callback(self):
        """流水灯效果回调 - 七彩变化（红橙黄绿青蓝紫）"""
        # 如果没有颜色索引或过渡状态，初始化
        if not hasattr(self.current_effect, 'color_index'):
            self.current_effect.color_index = 0
            self.current_effect.is_transition = False
            self.current_effect.last_color = None
            
        # 七彩颜色列表（红橙黄绿青蓝紫）
        rainbow_colors = [
            Color(255, 0, 0),     # 红色
            Color(255, 165, 0),   # 橙色
            Color(255, 255, 0),   # 黄色
            Color(0, 255, 0),     # 绿色
            Color(0, 255, 255),   # 青色
            Color(0, 0, 255),     # 蓝色
            Color(128, 0, 128)    # 紫色
        ]
        
        # 直接设置当前颜色，不关闭LED
        current_color = rainbow_colors[self.current_effect.color_index]
        self._set_color(current_color)
        self.current_effect.last_color = current_color
            
        # 更新颜色索引，循环遍历七种颜色
        self.current_effect.color_index = (self.current_effect.color_index + 1) % len(rainbow_colors)
        
        self.node.get_logger().debug(f"流水灯切换颜色: 索引 {self.current_effect.color_index}")

    def _blink_callback(self):
        """闪烁效果回调"""
        self.current_effect.phase = not self.current_effect.phase
        if self.current_effect.phase:
            self._set_color(self.current_effect.start_color)
        else:
            self._set_color(Color(0, 0, 0))

    def handle_parameter_update(self, params, update_mode=False):
        """处理参数更新
        
        Args:
            params: 要更新的参数列表
            update_mode: 是否更新灯光效果，只有在更新mode参数时为True
        """
        mode = None
        color = None
        secondary_color = None
        
        self.node.get_logger().info("开始处理参数更新请求")
        
        for param in params:
            self.node.get_logger().info(f"处理参数: {param.name} = {param.value}")
            
            if param.name == 'mode':
                mode = param.value
                self.node.get_logger().info(f"设置新的LED模式: {mode}")
            elif param.name == 'color':
                color = param.value
                self.node.get_logger().info(f"设置新的主要颜色: {color}")
            elif param.name == 'secondary_color':
                secondary_color = param.value
                self.node.get_logger().info(f"设置新的次要颜色: {secondary_color}")
            elif param.name == 'effect_speed':
                if not isinstance(param.value, (int, float)):
                    raise TypeError(f"effect_speed参数必须是数值类型")
                if float(param.value) <= 0 or float(param.value) > 20.0:
                    raise ValueError(f"effect_speed必须大于0且不超过20")
                # 如果当前是动态效果模式，立即更新速度
                current_mode = self.node.get_parameter('mode').get_parameter_value().string_value
                if current_mode in [LedMode.BLINK.value, LedMode.BREATH.value, 
                                  LedMode.FLOW.value, LedMode.RAINBOW.value, 
                                  LedMode.GRADIENT.value]:
                    self.update_mode(current_mode, 
                                   self.node.get_parameter('color').get_parameter_value().string_value,
                                   self.node.get_parameter('secondary_color').get_parameter_value().string_value)
                self.node.get_logger().info(f"effect_speed参数已更新为: {param.value}")
            elif param.name == 'brightness':
                self.node.get_logger().info(f"设置新的亮度: {param.value}")
        
        current_mode = self.node.get_parameter('mode').get_parameter_value().string_value
        current_color = self.node.get_parameter('color').get_parameter_value().string_value
        
        try:
            if update_mode:
                self.node.get_logger().info(f"当前模式: {current_mode}, 当前颜色: {current_color}")
                self.node.get_logger().info(f"即将更新为 - 模式: {mode or current_mode}, 颜色: {color or current_color}")
                
                self.update_mode(
                    mode or current_mode,
                    color or current_color,
                    secondary_color
                )
                self.node.get_logger().info("灯光效果更新成功")
            else:
                self.node.get_logger().info("仅保存参数，不更新灯光效果")
                
        except Exception as e:
            self.node.get_logger().error(f"更新时发生错误: {str(e)}")
            raise

    def cleanup(self):
        """清理LED控制器资源"""
        self.node.get_logger().info("开始清理LED控制器资源")
        try:
            # 取消定时器
            if self.effect_timer:
                self.effect_timer.cancel()
                self.effect_timer = None
            
            # 关闭所有LED（设置占空比为0）
            self._set_color(Color(0, 0, 0))
            
            # 清理所有PWM通道
            for channel in self.pwm_channels:
                try:
                    channel.cleanup()
                    self.node.get_logger().info(f"已清理PWM通道: {channel.pwmchip}:pwm{channel.channel}")
                except Exception as e:
                    self.node.get_logger().error(f"清理PWM通道 {channel.pwmchip}:pwm{channel.channel} 时出错: {str(e)}")
            
            self.node.get_logger().info("LED控制器资源清理完成")
        except Exception as e:
            self.node.get_logger().error(f"清理LED控制器资源时出错: {str(e)}")

class FanController:
    """风扇控制器类"""
    def __init__(self, node, pwm_channel):
        self.node = node
        self.pwm_channel = pwm_channel

    def update_speed(self, speed):
        """更新风扇转速"""
        try:
            self._set_speed(speed)
        except Exception as e:
            self.node.get_logger().error(f"设置风扇转速失败: {str(e)}")

    def _set_speed(self, speed):
        """设置风扇转速"""
        try:
            fanSpeed = int(speed * self.pwm_channel.period * 0.01)
            self.pwm_channel.set_duty_cycle(fanSpeed, self.pwmchip, self.channel)
            self.pwm_channel.duty = fanSpeed
            self.node.get_logger().info(f"设置PWM占空比 - 风扇转速: {fanSpeed}")
        except Exception as e:
            self.node.get_logger().error(f"设置PWM占空比失败: {str(e)}")

    def handle_parameter_update(self, params):
        """处理参数更新

        Args:
            params: 要更新的参数列表
        """
        self.node.get_logger().info("开始处理参数更新请求")

        for param in params:
            self.node.get_logger().info(f"处理参数: {param.name} = {param.value}")

            if param.name == 'fan_speed':
                try:
                    self.node.get_logger().info(f"当前风扇转速: {self.pwm_channel.duty}")
                    self.update_speed(param.value)
                    self.node.get_logger().info("风扇转速更新成功")

                except Exception as e:
                    self.node.get_logger().error(f"更新时发生错误: {str(e)}")
                    raise

class PwmTouchNode(Node):
    def __init__(self):
        super().__init__('pwm_touch_node')
        self.get_logger().info("初始化PWM触摸节点")
        
        # 基本参数
        self.declare_parameter('pwm_gpio_map', 'pwmchip6:0,pwmchip2:0,pwmchip3:0')
        self.declare_parameter('light_pwm', 'pwmchip0:0')  # 照明灯通道配置
        self.declare_parameter('fan_pwm', 'pwmchip5:0')  # 风扇通道配置
        self.declare_parameter('fan_period', 40000)      # 风扇周期配置
        self.declare_parameter('fan_speed', 20000)       # 风扇转速50%
        self.declare_parameter('touch_gpio_pins', [36, 38, 39])
        self.declare_parameter('pwm_period', 10000)

        # 触摸检测参数
        self.declare_parameter('min_high_duration', 0.1)  # 最小高电平持续时间（秒）
        
        # LED参数 - 默认设置为静态白灯
        self.declare_parameter('mode', 'static')
        self.declare_parameter('color', '#FFFFFF')  # 白色
        self.declare_parameter('secondary_color', '#FFFFFF')
        self.declare_parameter('effect_speed', 1.0)
        self.declare_parameter('brightness', 1.0)
        self.get_logger().info("参数初始化完成")

        # 状态跟踪变量
        self.last_overall_touch_status = False  # 上次的整体触摸状态
        self.current_overall_status = False     # 当前整体触摸状态

        # 初始化硬件
        self._init_hardware()
        
        # 创建灯光控制器和触摸输入
        self.light_controller = LightController(self, self.pwm_channels)
        self.touch_input = TouchInput(self)

        # 创建风扇控制器
        self.fan_controller = FanController(self, self.fan_pwm)

        # 设置触摸GPIO
        gpio_pins = self.get_parameter('touch_gpio_pins').get_parameter_value().integer_array_value
        self.touch_input.setup(gpio_pins)

        # 设置最小高电平持续时间
        min_high_duration = self.get_parameter('min_high_duration').get_parameter_value().double_value
        self.touch_input.set_min_high_duration(min_high_duration)
        
        # 创建发布器
        self.touch_publisher = self.create_publisher(String, 'touch_status', 10)
        
        # 添加参数回调
        self.add_on_set_parameters_callback(self.parameters_callback)
        
        # 创建照明灯订阅者
        self.light_subscription = self.create_subscription(
            String,
            'light_control',
            self.light_control_callback,
            10
        )
        
        # 初始化LED为静态白灯
        self.light_controller.update_mode(
            'static',
            '#FFFFFF',
            '#FFFFFF'
        )
        
        self.get_logger().info("PWM触摸节点初始化完成,LED设置为静态白灯")
    
    def compute_overall_status(self, status_dict):
        """计算整体触摸状态:只要有一个引脚为True,整体就为True;全为False时整体为False"""
        return any(status_dict.values())
    
    def update_touch_status(self, new_status):
        """更新触摸状态，只在状态变化时发布"""
        # 计算新的整体状态
        new_overall_status = self.compute_overall_status(new_status)
        
        # 检查状态是否发生变化
        status_changed = False
        
        # 状态变化判断
        if self.last_overall_touch_status != new_overall_status:
            status_changed = True
            
            # 记录状态变化
            if not self.last_overall_touch_status and new_overall_status:
                self.get_logger().info("检测到触摸事件（从false到true）")
            elif self.last_overall_touch_status and not new_overall_status:
                self.get_logger().info("检测到触摸释放（从true到false）")
            elif new_overall_status:
                self.get_logger().info("检测到持续触摸状态")
            else:
                self.get_logger().info("检测到持续释放状态")
                
            # 更新上次状态
            self.last_overall_touch_status = new_overall_status
            
        # 更新当前状态
        self.current_overall_status = new_overall_status
        
        return status_changed, new_overall_status

    def _init_hardware(self):
        """初始化PWM硬件"""
        try:
            pwm_gpio_map_str = self.get_parameter('pwm_gpio_map').get_parameter_value().string_value
            self.pwm_period = self.get_parameter('pwm_period').get_parameter_value().integer_value
            
            self.get_logger().info(f"PWM配置 - RGB映射: {pwm_gpio_map_str}, 周期: {self.pwm_period}")
            
            # 初始化RGB LED通道
            self.pwm_channels = []
            for item in pwm_gpio_map_str.split(','):
                pwmchip, channel = item.split(':')
                self.pwm_channels.append(PwmChannel(
                    pwmchip.strip(), int(channel.strip()),
                    self.pwm_period, 0
                ))
                self.get_logger().debug(f"创建RGB PWM通道: {pwmchip}:{channel}")
            
            # 初始化照明灯通道
            light_pwm_config = self.get_parameter('light_pwm').get_parameter_value().string_value
            light_chip, light_channel = light_pwm_config.split(':')
            self.light_pwm = PwmChannel(
                light_chip.strip(), int(light_channel.strip()),
                self.pwm_period, 0
            )
            self.get_logger().debug(f"创建照明灯PWM通道: {light_chip}:{light_channel}")

            # 初始化风扇通道
            fan_pwm_config = self.get_parameter('fan_pwm').value
            fan_period = self.get_parameter('fan_period').value
            fan_speed = self.get_parameter('fan_speed').value
            fan_chip, fan_channel = fan_pwm_config.split(':')
            self.fan_pwm = PwmChannel(
                fan_chip.strip(), int(fan_channel.strip()),
                fan_period, fan_speed
            )
            self.get_logger().debug(f"创建风扇PWM通道: {fan_chip}:{fan_channel}")

            # 配置所有通道
            for ch in self.pwm_channels + [self.light_pwm, self.fan_pwm]:
                ch.configure()
                self.get_logger().debug(f"配置PWM通道: {ch.pwmchip}:pwm{ch.channel}")
            
            # 初始关闭照明灯
            self.light_pwm.set_duty_cycle(0)
            
            self.get_logger().info("PWM硬件初始化完成")
        except Exception as e:
            self.get_logger().error(f"PWM硬件初始化失败: {str(e)}")
            raise

    def light_control_callback(self, msg):
        """照明灯控制回调函数"""
        try:
            command = msg.data
            self.get_logger().info(f"收到照明灯控制命令: {command}")
            
            if command == "light_open":
                # 设置占空比为最大占空比的80%
                # duty = int(0.8 * self.light_pwm.period)
                # self.light_pwm.set_duty(duty)
                duty=5000
                self.light_pwm.set_duty_cycle(5000)
                self.get_logger().info(f"打开照明灯，占空比设置为: {duty} (周期={self.light_pwm.period})")
                
            elif command == "light_close":
                self.light_pwm.set_duty(0)
                self.get_logger().info("关闭照明灯")
                
            else:
                self.get_logger().warn(f"未知的照明灯命令: {command}")
                
        except Exception as e:
            self.get_logger().error(f"处理照明灯命令失败: {str(e)}")

    def parameters_callback(self, params):
        """参数更新回调函数"""
        result = SetParametersResult()
        result.successful = True
        try:
            self.get_logger().info(f"收到参数更新请求: {[p.name for p in params]}")
            
            # 先验证所有参数
            for param in params:
                self.get_logger().info(f"验证参数: {param.name} = {param.value}")
                
                if param.name == 'mode':
                    if not isinstance(param.value, str):
                        raise TypeError(f"mode参数必须是字符串类型")
                    if param.value not in [mode.value for mode in LedMode]:
                        raise ValueError(f"不支持的LED模式: {param.value}")
                    # mode参数更新时，更新灯光效果
                    self.light_controller.handle_parameter_update(params, update_mode=True)
                    self.get_logger().info("mode参数更新，已更新灯光效果")
                        
                elif param.name == 'color':
                    if not isinstance(param.value, str):
                        raise TypeError(f"color参数必须是字符串类型")
                    if not param.value.startswith('#') or len(param.value) != 7:
                        raise ValueError(f"无效的颜色格式: {param.value}")
                    # 只保存参数
                    self.light_controller.handle_parameter_update(params, update_mode=False)
                    self.get_logger().info("color参数已保存")
                        
                elif param.name == 'secondary_color':
                    if not isinstance(param.value, str):
                        raise TypeError(f"secondary_color参数必须是字符串类型")
                    if not param.value.startswith('#') or len(param.value) != 7:
                        raise ValueError(f"无效的次要颜色格式: {param.value}")
                    # 只保存参数
                    self.light_controller.handle_parameter_update(params, update_mode=False)
                    self.get_logger().info("secondary_color参数已保存")
                        
                elif param.name == 'effect_speed':
                    if not isinstance(param.value, (int, float)):
                        raise TypeError(f"effect_speed参数必须是数值类型")
                    if float(param.value) <= 0 or float(param.value) > 20.0:
                        raise ValueError(f"effect_speed必须大于0且不超过20")
                    # 如果当前是动态效果模式，立即更新速度
                    current_mode = self.get_parameter('mode').get_parameter_value().string_value
                    if current_mode in [LedMode.BLINK.value, LedMode.BREATH.value, 
                                      LedMode.FLOW.value, LedMode.RAINBOW.value, 
                                      LedMode.GRADIENT.value]:
                        self.light_controller.handle_parameter_update(params, update_mode=True)
                    else:
                        self.light_controller.handle_parameter_update(params, update_mode=False)
                    self.get_logger().info(f"effect_speed参数已更新为: {param.value}")
                        
                elif param.name == 'brightness':
                    if not isinstance(param.value, (int, float)):
                        raise TypeError(f"brightness参数必须是数值类型")
                    if not 0 <= float(param.value) <= 1:
                        raise ValueError(f"brightness必须在0到1之间")
                    # 只保存参数
                    self.light_controller.handle_parameter_update(params, update_mode=False)
                    self.get_logger().info("brightness参数已保存")

                elif param.name == 'min_high_duration':
                    if not isinstance(param.value, (int, float)):
                        raise TypeError(f"min_high_duration参数必须是数值类型")
                    if float(param.value) <= 0 or float(param.value) > 1.0:
                        raise ValueError(f"min_high_duration必须大于0且不超过1秒")
                    # 更新触摸输入的最小高电平持续时间
                    self.touch_input.set_min_high_duration(float(param.value))
                    self.get_logger().info(f"min_high_duration参数已更新为: {param.value}s")
                elif param.name == 'fan_speed':
                    if not isinstance(param.value, (int, float)):
                        raise TypeError(f"fan_speed参数必须是数值类型")
                    if float(param.value) < 0 or float(param.value) > 100:
                        raise ValueError(f"fan_speed必须在0到100之间")
                    self.fan_controller.handle_parameter_update(params)
                    self.get_logger().info(f"风扇速度参数已更新")
        except Exception as e:
            result.successful = False
            result.reason = str(e)
            self.get_logger().error(f"参数更新失败: {str(e)}")
            self.get_logger().error(f"错误类型: {type(e).__name__}")
            import traceback
            self.get_logger().error(f"错误堆栈: {traceback.format_exc()}")
        
        return result

    def publish_rising_edge_message(self, pin):
        """发布上升沿检测消息"""
        try:
            # 读取所有GPIO状态
            status = self.touch_input.read_status()
            
            # 创建触摸事件数据
            touch_data = {}
            for p, value in status.items():
                touch_data[str(p)] = bool(value)
            
            # 添加上升沿事件信息
            current_time = time.time()
            data = {
                "command": "touch_rising_edge",
                "data": touch_data,
                "rising_edge_pin": pin,
                "timestamp": int(self.get_clock().now().seconds_nanoseconds()[0]),
                "event_time": current_time
            }
            
            msg = String()
            msg.data = json.dumps(data)
            self.touch_publisher.publish(msg)
            self.get_logger().info(f"发布上升沿消息 - GPIO {pin}: {msg.data}")
            
            return True
        except Exception as e:
            self.get_logger().error(f"发布上升沿消息失败: {str(e)}")
            return False

    def publish_touch_status(self, status):
        """发布触摸状态消息"""
        try:
            # 直接创建正确格式的JSON数据
            touch_data = {}
            for pin, value in status.items():
                # 使用JSON原生布尔值而非字符串
                touch_data[str(pin)] = bool(value)
            
            data = {
                "command": "touch_status",
                "data": touch_data,
                "timestamp": int(self.get_clock().now().seconds_nanoseconds()[0])
            }
            
            msg = String()
            msg.data = json.dumps(data)
            self.touch_publisher.publish(msg)
            self.get_logger().info(f"发布触摸状态: {msg.data}")
            
            return True
        except Exception as e:
            self.get_logger().error(f"发布触摸状态失败: {str(e)}")
            return False

    def check_touch_status(self):
        """检查并处理触摸状态（保留原有接口兼容性）"""
        try:
            # 读取所有GPIO状态
            status = self.touch_input.read_status()
            
            # 计算新的整体状态并检查状态变化
            status_changed, _ = self.update_touch_status(status)
            
            # 只有在状态变化时才发布消息
            if status_changed:
                self.publish_touch_status(status)
            
            return status
        except Exception as e:
            self.get_logger().error(f"检查触摸状态失败: {str(e)}")
            return {}

    def monitor_touch(self):
        """监控触摸事件"""
        self.get_logger().info("开始监控触摸事件")
        try:
            while rclpy.ok() and self.touch_input.is_running:
                # 等待GPIO事件，使用定时器机制处理高电平持续时间
                events = self.touch_input.poll_events(timeout=-1)  # 阻塞等待事件

                # 处理GPIO事件
                for fd, event in events:
                    pin = next(pin for pin, pin_fd in self.touch_input.fds.items() if pin_fd == fd)

                    # 读取当前GPIO值
                    os.lseek(fd, 0, os.SEEK_SET)
                    value = os.read(fd, 1).decode().strip()
                    current_value = (value == '1')

                    # 检查是否检测到边缘变化
                    edge_type = self.touch_input.check_edge(pin, current_value)
                    if edge_type:
                        self.get_logger().info(f"检测到GPIO {pin} {edge_type}沿，准备发布消息")
                        # 根据边缘类型发布相应的消息
                        if edge_type == "rising":
                            self.publish_rising_edge_message(pin)
                            # 清除上升沿标志，避免重复处理
                            self.touch_input.clear_rising_edge(pin)
                        elif edge_type == "falling":
                            self.publish_falling_edge_message(pin)
                            # 清除下降沿标志，避免重复处理
                            self.touch_input.clear_falling_edge(pin)

        except Exception as e:
            self.get_logger().error(f"触摸监控异常: {str(e)}")
        finally:
            self.touch_input.cleanup()
            self.get_logger().info("触摸监控结束")

    def _handle_timer_rising_edge(self, pin):
        """处理定时器确认的上升沿事件"""
        try:
            self.get_logger().info(f"定时器确认的GPIO {pin} 上升沿，准备发布消息")
            self.publish_rising_edge_message(pin)
            # 清除上升沿标志，避免重复处理
            self.touch_input.clear_rising_edge(pin)
        except Exception as e:
            self.get_logger().error(f"处理定时器上升沿失败 GPIO {pin}: {e}")

    def publish_falling_edge_message(self, pin):
        """发布下降沿检测消息"""
        try:
            # 读取所有GPIO状态
            status = self.touch_input.read_status()
            
            # 创建触摸事件数据
            touch_data = {}
            for p, value in status.items():
                touch_data[str(p)] = bool(value)
            
            # 添加下降沿事件信息
            current_time = time.time()
            data = {
                "command": "touch_falling_edge",
                "data": touch_data,
                "falling_edge_pin": pin,
                "timestamp": int(self.get_clock().now().seconds_nanoseconds()[0]),
                "event_time": current_time
            }
            
            msg = String()
            msg.data = json.dumps(data)
            self.touch_publisher.publish(msg)
            self.get_logger().info(f"发布下降沿消息 - GPIO {pin}: {msg.data}")
            
            return True
        except Exception as e:
            self.get_logger().error(f"发布下降沿消息失败: {str(e)}")
            return False

    def cleanup(self):
        """节点清理"""
        try:
            self.get_logger().info("清理节点资源...")
            # 关闭照明灯
            self.light_pwm.set_duty(0)
            # 恢复风扇为默认转速
            self.fan_pwm.set_duty_cycle(node.get_parameter('fan_speed').value)
            # 关闭LED
            self.light_controller.update_mode('off', '#000000')
            # 清理硬件资源
            self.light_controller.cleanup()
            self.light_pwm.cleanup()
            self.fan_pwm.cleanup()
            self.touch_input.cleanup()
            self.get_logger().info("所有资源清理完成")
        except Exception as e:
            self.get_logger().error(f"清理资源时出错: {str(e)}")

def main(args=None):
    rclpy.init(args=args)
    node = PwmTouchNode()
    
    # 创建触摸监控线程
    import threading
    touch_thread = threading.Thread(target=node.monitor_touch, daemon=True)
    touch_thread.start()
    
    try:
        rclpy.spin(node)
    except KeyboardInterrupt:
        node.get_logger().info("收到中断信号，准备关闭节点")
    finally:
        node.get_logger().info("正在关闭灯光...")
        try:
            # 关闭照明灯
            node.light_pwm.set_duty(0)
            # 恢复风扇为默认转速
            node.fan_pwm.set_duty_cycle(node.get_parameter('fan_speed').value)
            # 将灯光设置为关闭模式
            node.light_controller.update_mode('off', '#000000')
            node.get_logger().info("所有灯光已关闭")
        except Exception as e:
            node.get_logger().error(f"关闭灯光时出错: {str(e)}")

        node.touch_input.is_running = False
        touch_thread.join(timeout=1.0)
        # 清理节点资源
        node.cleanup()
        node.destroy_node()
        rclpy.shutdown()
        node.get_logger().info("节点已安全关闭")

if __name__ == '__main__':
    main()