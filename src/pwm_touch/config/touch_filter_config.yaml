# PWM触摸节点配置文件
# 包含高电平时间过滤参数

pwm_touch_node:
  ros__parameters:
    # 基本PWM配置
    pwm_gpio_map: "pwmchip6:0,pwmchip2:0,pwmchip3:0"  # RGB LED通道
    light_pwm: "pwmchip0:0"                            # 照明灯通道
    touch_gpio_pins: [36, 38, 39]                      # 触摸GPIO引脚
    pwm_period: 10000                                   # PWM周期（纳秒）
    fan_pwm: "pwmchip5:0"                               # 风扇通道
    fan_period: 40000                                   # 风扇周期
    fan_speed: 20000                                    # 风扇转速
    
    # LED效果配置
    mode: "static"                                      # LED模式
    color: "#FFFFFF"                                    # 主要颜色（白色）
    secondary_color: "#FFFFFF"                          # 次要颜色
    effect_speed: 1.0                                   # 效果速度
    brightness: 1.0                                     # 亮度（0-1）
    
    # 触摸检测过滤配置
    min_high_duration: 0.05                            # 最小高电平持续时间（秒）
                                                        # 默认50ms，可根据需要调整：
                                                        # - 0.02 (20ms): 较敏感，适合快速点击
                                                        # - 0.05 (50ms): 标准设置，过滤大部分噪声
                                                        # - 0.1 (100ms): 较严格，只检测明确的触摸
                                                        # - 0.2 (200ms): 很严格，只检测长按

# 不同应用场景的推荐配置：

# 1. 高敏感度配置（适合需要快速响应的场景）
# min_high_duration: 0.02

# 2. 标准配置（推荐，平衡敏感度和抗干扰）
# min_high_duration: 0.05

# 3. 抗干扰配置（适合噪声较多的环境）
# min_high_duration: 0.1

# 4. 长按检测配置（只检测明确的长按操作）
# min_high_duration: 0.2
