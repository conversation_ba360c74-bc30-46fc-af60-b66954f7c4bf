#ifndef __CMCC_RTC_NODE_H__
#define __CMCC_RTC_NODE_H__

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <iostream>
#include <unistd.h>
#include <pthread.h>
#include <sys/time.h>
#include <time.h>
#include <unordered_map>
#include <sys/prctl.h>
#include "cmcc_rtc_api.h"
#include <rclcpp/rclcpp.hpp>
#include "homi_speech_interface/msg/pcm_stream.hpp"
#include "homi_speech_interface/srv/assistant_abort.hpp"
#include "homi_speech_interface/srv/set_nr_mode.hpp"
#include "homi_speech_interface/srv/set_wake_event.hpp"
#include "homi_speech_interface/srv/force_idr.hpp"
#include "homi_speech_interface/srv/get_video_stream.hpp"
#include "homi_speech_interface/srv/end_video_stream.hpp"
#include "homi_speech_interface/srv/get_pcm_player.hpp"
#include "homi_speech_interface/srv/end_pcm_player.hpp"
#include "homi_speech_interface/srv/rtc_emotion_change.hpp"
#include "homi_speech_interface/srv/phone_call.hpp"
#include "homi_speech_interface/srv/play_wav.hpp"
#include "video_source.h"
#include "shmaudio.h"

extern "C" {
#include "vs_define.h"
}

#define LOGNAME "cmcc_rtc"

#define SDK_INCLUDE_THIRD_PARTY_DEVICE
#define ENABLE_AUDIO_CODEC
#define SERVICE_CALL

class CmccRtcNode : public rclcpp::Node
{
public:
    CmccRtcNode(std::string name);
    ~CmccRtcNode();
    void init();
    bool getIsRunning();

private:
    bool getRtcConfig();
    bool phoneCall(const std::shared_ptr<homi_speech_interface::srv::PhoneCall::Request> req,
                   std::shared_ptr<homi_speech_interface::srv::PhoneCall::Response> resp);
    void pcmCallback(const homi_speech_interface::msg::PCMStream::SharedPtr msg);
    void audioTimerCallback();

    int _writeShmId;
    int _writeIsAvaliableShmId;
    audio_buffer* _writeShared;
    is_avalibale* _writeIsAvaliableShared;

    int _readShmId;
    int _readIsAvaliableShmId;
    audio_buffer* _readShared;
    is_avalibale* _readIsAvaliableShared;

    std::unordered_map<std::string, cmcc_rtc_opt_> stringsToEnum = {
        {"CMCC_OPT_AUTO_ANSWER_FLAG", CMCC_OPT_AUTO_ANSWER_FLAG},
        {"CMCC_OPT_ENABLE_H265", CMCC_OPT_ENABLE_H265},
        {"CMCC_OPT_HANGUP_WITH_REASON", CMCC_OPT_HANGUP_WITH_REASON},
        {"CMCC_OPT_ENABLE_OPUS_NEGOTIATE", CMCC_OPT_ENABLE_OPUS_NEGOTIATE},
        {"CMCC_OPT_VIDEO_RESOLUTION", CMCC_OPT_VIDEO_RESOLUTION},
        {"CMCC_OPT_GOVERNMENT_ENTERPRISE", CMCC_OPT_GOVERNMENT_ENTERPRISE},
        {"CMCC_OPT_THREAD_MODE_LOOP", CMCC_OPT_THREAD_MODE_LOOP},
        {"CMCC_OPT_VIDEO_FEC", CMCC_OPT_VIDEO_FEC},
        {"CMCC_OPT_PORTRAIT", CMCC_OPT_PORTRAIT},
        {"CMCC_OPT_ENABLE_DTMF_TONE", CMCC_OPT_ENABLE_DTMF_TONE},
        {"CMCC_OPT_REMOTE_CONTROL_ID", CMCC_OPT_REMOTE_CONTROL_ID},
        {"CMCC_OPT_ENABLE_CODEC_AND_RESOLUTION_CALLBACK", CMCC_OPT_ENABLE_CODEC_AND_RESOLUTION_CALLBACK},
        {"CMCC_OPT_RECV_VIDEO", CMCC_OPT_RECV_VIDEO},
        {"CMCC_OPT_DISENABLE_CONTACTS", CMCC_OPT_DISENABLE_CONTACTS},
        {"CMCC_OPT_FLASH_DIR", CMCC_OPT_FLASH_DIR}
    };
    
#ifdef XUNFEI
    static void setNrMode(VS_NR_Mode_e mode);
#endif
    static void setWakeup(bool wakeup);
    static void abortSpeech();
#ifdef VSNODE
    static void forceIDR(std::string resolution);
    static void getVidoeStream(std::string resolution, bool response=false);
    static void endVideoStream(std::string resolution);
    static void changeEmotion(std::string filename, int cnt);
    static void playWav(std::string filename);
    static void getAudioPlayer();
    static void endAudioPlayer();
#endif

    static int g_call_session, g_calling, g_login_success, g_enable_video, g_call_type;
    static bool g_video_resolution_changed;
    static std::string resolution;
    static std::shared_ptr<VideoSourceNode> vs_node;

#ifdef SDK_INCLUDE_THIRD_PARTY_DEVICE
    static FILE* g_audio_pcm_fp_input;
    static FILE* g_audio_pcm_fp_output;
#endif

#ifndef ENABLE_AUDIO_CODEC
    static int g_enable_audio;
    static FILE* g_audio_fp;
    static uint64_t get_cur_timestamp_us();
    static void create_send_audio_thread();
#endif

    static void VideoVoipBufferCallback(uint8_t *data, unsigned long length, int width, int height);

#ifdef ENABLE_MULTI_STREAMS
    static void VideoCameraBufferCallback(uint8_t *data, unsigned long length, int width, int height);
#endif

    static void create_send_video_thread();

#ifndef ENABLE_AUDIO_CODEC
    static void __on_recv_audio_packet(char *packet ,int size);
#endif

    static void __on_login_success(const char *user);
    static void __on_login_failed(const char *user, int err_code, const char *reason);
    static void __on_recv_call(int session, const char *from, const char *displayname, const char *to, cmcc_call_type_t call_type, const char *json_call_control);
    static void __on_recv_ring(int session, const char *from, const char *displayname, const char *to, int early_media);
    static void __on_recv_answer(int session, const char *from, const char *displayname, const char *to, cmcc_call_type_t call_type);
    static void __on_recv_hangup(int session, int err_code, const char *reason, cmcc_call_type_t call_type);
    static void __on_recv_notify(cmcc_notify_t notify_type, const char *content);
    static void __on_recv_keyframe_request();
    static void __on_recv_dtmf(int value);

#ifdef SDK_INCLUDE_THIRD_PARTY_DEVICE
    static void __on_recv_audio_pcm_data(char *pcm_data, int size);
    static void __on_send_audio_pcm_data(char *pcm_data, int size);
#endif

protected:
    bool is_running_;
    bool dev_;
    int gst_debug_level_;
    std::string video_namespace_;
    cmcc_rtc_device_info_t device_info_;
    cmcc_rtc_log_config_t log_config_;
    cmcc_rtc_login_params_t login_params_;
    cmcc_rtc_event_handler_t event_handler;
    std::unordered_map<cmcc_rtc_opt_, const char*> rtc_opts_;

    rclcpp::Service<homi_speech_interface::srv::PhoneCall>::SharedPtr callServer_;
    rclcpp::Subscription<homi_speech_interface::msg::PCMStream>::SharedPtr audioSuber_;
    rclcpp::Publisher<homi_speech_interface::msg::PCMStream>::SharedPtr audioPuber_;
    rclcpp::TimerBase::SharedPtr audioTimer_;
#ifdef XUNFEI
    static rclcpp::Client<homi_speech_interface::srv::SetNrMode>::SharedPtr modeClient_;
#endif
    static rclcpp::Client<homi_speech_interface::srv::SetWakeEvent>::SharedPtr wakeClient_;
    static rclcpp::Client<homi_speech_interface::srv::AssistantAbort>::SharedPtr abortClient_;
#ifdef VSNODE
    static bool forceIDR_;
    static rclcpp::Client<homi_speech_interface::srv::ForceIDR>::SharedPtr forceClient_;
    static rclcpp::Client<homi_speech_interface::srv::GetVideoStream>::SharedPtr getClient_;
    static rclcpp::Client<homi_speech_interface::srv::EndVideoStream>::SharedPtr endClient_;
#endif
    static rclcpp::Client<homi_speech_interface::srv::RtcEmotionChange>::SharedPtr emotionClient_;
    static rclcpp::Client<homi_speech_interface::srv::PlayWav>::SharedPtr wavClient_;
    static rclcpp::Client<homi_speech_interface::srv::GetPcmPlayer>::SharedPtr getAudioCLient_;
    static rclcpp::Client<homi_speech_interface::srv::EndPcmPlayer>::SharedPtr endAudioClient_;
};

#endif
