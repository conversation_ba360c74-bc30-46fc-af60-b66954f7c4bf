#ifndef __VIDEO_SOURCE_NODE_H__
#define __VIDEO_SOURCE_NODE_H__
#include <gst/gst.h>
#include "rclcpp/rclcpp.hpp"

// #define VSSOURCE
#define VSNODE
#define VIDEOLOG "rtc_video_source"

#ifdef UNITREE
    #define SOCKET_SD "/tmp/neck_sd"
    #define SOCKET_HD  "/tmp/neck_hd"
    #define SOCKET_FHD  "/tmp/neck_fhd"
#else
    #define SOCKET_SD "/tmp/foo_sd"
    #define SOCKET_HD  "/tmp/foo_hd"
    #define SOCKET_FHD  "/tmp/foo_fhd"
#endif

typedef std::function<void(uint8_t *, unsigned long, int, int)> BufferCallback;
typedef std::function<void(std::string)> EndCallback;

class VideoSourceNode
{
public:
    VideoSourceNode();
    ~VideoSourceNode();
    bool init(int gst_debug_level);
    void fini();
    bool get(std::string resolution);
    bool start();
    bool stop();
    void SetVoipBufferCallback(BufferCallback callback);
    void GetWidthAndHeight(int &width, int &height);
    
    BufferCallback mVoipBufferCallback;
    EndCallback mEndCallback;
private:
    bool create();
    bool destroy();

    std::string resolution_;
    GstElement *mPipeline_, *mSrc_, *mSink_;
    GstElement *shmsrc1_, *shmsrc2_, *shmsrc3_;
};

#endif