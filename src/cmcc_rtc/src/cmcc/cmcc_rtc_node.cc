#include "cmcc_rtc_node.h"
#include "alsa_helper.h"
#include "file_wrapper.h"
#include <sys/ipc.h>
#include <sys/shm.h>
#include <errno.h>
#include <thread>

int CmccRtcNode::g_call_session                   = 0;
int CmccRtcNode::g_calling                        = 0;
int CmccRtcNode::g_login_success                  = 0;
int CmccRtcNode::g_enable_video                   = 0;
int CmccRtcNode::g_call_type                      = CMCC_CALL_TYPE_IMS_1V1_VIDEO;
std::string CmccRtcNode::resolution               = "";
bool CmccRtcNode::g_video_resolution_changed      = 0;
std::shared_ptr<VideoSourceNode> CmccRtcNode::vs_node = nullptr;

#ifdef SDK_INCLUDE_THIRD_PARTY_DEVICE
FILE* CmccRtcNode::g_audio_pcm_fp_input           = NULL;
FILE* CmccRtcNode::g_audio_pcm_fp_output          = NULL;
#endif

#ifndef ENABLE_AUDIO_CODEC
int CmccRtcNode::g_enable_audio                   = 0;
FILE* CmccRtcNode::g_audio_fp                     = NULL;

uint64_t CmccRtcNode::get_cur_timestamp_us()
{
    return 0;
}

void CmccRtcNode::create_send_audio_thread()
{
    RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "================ create_send_audio_thread ================\n");
    pthread_t tid;
    pthread_attr_t attr;
    pthread_attr_init(&attr);
    pthread_attr_setstacksize(&attr, 1024*128);
    pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);
    tid = pthread_create(&tid, &attr, &send_audio_thread, NULL);
    pthread_attr_destroy(&attr);
}
#endif

#ifdef XUNFEI
rclcpp::Client<homi_speech_interface::srv::SetNrMode>::SharedPtr CmccRtcNode::modeClient_ = nullptr;
#endif
rclcpp::Client<homi_speech_interface::srv::SetWakeEvent>::SharedPtr CmccRtcNode::wakeClient_ = nullptr;
rclcpp::Client<homi_speech_interface::srv::AssistantAbort>::SharedPtr CmccRtcNode::abortClient_ = nullptr;

#ifdef VSNODE
bool CmccRtcNode::forceIDR_ = false;
rclcpp::Client<homi_speech_interface::srv::ForceIDR>::SharedPtr CmccRtcNode::forceClient_ = nullptr;
rclcpp::Client<homi_speech_interface::srv::GetVideoStream>::SharedPtr CmccRtcNode::getClient_ = nullptr;
rclcpp::Client<homi_speech_interface::srv::EndVideoStream>::SharedPtr CmccRtcNode::endClient_ = nullptr;
#endif

rclcpp::Client<homi_speech_interface::srv::RtcEmotionChange>::SharedPtr CmccRtcNode::emotionClient_ = nullptr;
rclcpp::Client<homi_speech_interface::srv::PlayWav>::SharedPtr CmccRtcNode::wavClient_ = nullptr;
rclcpp::Client<homi_speech_interface::srv::GetPcmPlayer>::SharedPtr CmccRtcNode::getAudioCLient_ = nullptr;
rclcpp::Client<homi_speech_interface::srv::EndPcmPlayer>::SharedPtr CmccRtcNode::endAudioClient_ = nullptr;

CmccRtcNode::CmccRtcNode(std::string name)
    : Node(name, rclcpp::NodeOptions().automatically_declare_parameters_from_overrides(true)), _writeShmId(0), _writeShared(NULL), _readShmId(0), _readShared(NULL)
{
    is_running_ = this->getRtcConfig();
    if(!is_running_) return;
    event_handler.on_login_success          = __on_login_success;
    event_handler.on_login_failed           = __on_login_failed;
    event_handler.on_recv_call              = __on_recv_call,
    event_handler.on_recv_ring              = __on_recv_ring,
    event_handler.on_recv_answer            = __on_recv_answer,
    event_handler.on_recv_hangup            = __on_recv_hangup,
    event_handler.on_recv_notify            = __on_recv_notify,
    event_handler.on_recv_keyframe_request  = __on_recv_keyframe_request,
    event_handler.on_recv_dtmf              = __on_recv_dtmf,
#ifdef SDK_INCLUDE_THIRD_PARTY_DEVICE
    event_handler.on_recv_audio_pcm_data    = __on_recv_audio_pcm_data,
    event_handler.on_send_audio_pcm_data    = __on_send_audio_pcm_data,
#else
    event_handler.on_recv_audio_pcm_data    = NULL,
    event_handler.on_send_audio_pcm_data    = NULL,
#endif
#ifndef ENABLE_AUDIO_CODEC
    event_handler.on_recv_audio_packet      = __on_recv_audio_packet,
#else
    event_handler.on_recv_audio_packet      = NULL,
#endif

    vs_node = std::make_shared<VideoSourceNode>();
    vs_node->init(gst_debug_level_);
    vs_node->SetVoipBufferCallback(
        std::bind(VideoVoipBufferCallback, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3, std::placeholders::_4));
#ifdef ENABLE_MULTI_STREAMS
    vs_node->SetCameraBufferCallback(
        std::bind(VideoCameraBufferCallback, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3, std::placeholders::_4));
#endif

    int cnt = 0;
#ifdef XUNFEI
    modeClient_ = this->create_client<homi_speech_interface::srv::SetNrMode>("/audio_recorder/set_nr_mode_service");
    while (!modeClient_->wait_for_service(std::chrono::seconds(1))) {
      if (!rclcpp::ok()) {
        RCLCPP_ERROR(rclcpp::get_logger(LOGNAME), "Interrupted while waiting for service: /audio_recorder/set_nr_mode_service.");
        is_running_ = false;
        return;
      }
      RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "Waiting for service: /audio_recorder/set_nr_mode_service.");
      if(++cnt > 10) {
        RCLCPP_ERROR(rclcpp::get_logger(LOGNAME), "Waiting for service more then 10, no audio stream available.");
        is_running_ = false;
        return;
      }
    }
    RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "Service: /audio_recorder/set_nr_mode_service is ready.");
#endif
    wakeClient_ = this->create_client<homi_speech_interface::srv::SetWakeEvent>("/audio_recorder/set_wake_event_service");
    while (!wakeClient_->wait_for_service(std::chrono::seconds(1))) {
      if (!rclcpp::ok()) {
        RCLCPP_ERROR(rclcpp::get_logger(LOGNAME), "Interrupted while waiting for service: /audio_recorder/set_wake_event_service.");
        is_running_ = false;
        return;
      }
      RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "Waiting for service: /audio_recorder/set_wake_event_service.");
      if(++cnt > 10) {
        RCLCPP_ERROR(rclcpp::get_logger(LOGNAME), "Waiting for service more then 10, no audio stream available.");
        is_running_ = false;
        return;
      }
    }
    RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "Service: /audio_recorder/set_wake_event_service is ready.");

    abortClient_ = this->create_client<homi_speech_interface::srv::AssistantAbort>("/homi_speech/helper_assistant_abort_service");
    while (!abortClient_->wait_for_service(std::chrono::seconds(1))) {
      if (!rclcpp::ok()) {
        RCLCPP_ERROR(rclcpp::get_logger(LOGNAME), "Interrupted while waiting for service: /homi_speech/helper_assistant_abort_service.");
        is_running_ = false;
        return;
      }
      RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "Waiting for service: /homi_speech/helper_assistant_abort_service.");
      if(++cnt > 10) {
        RCLCPP_ERROR(rclcpp::get_logger(LOGNAME), "Waiting for service more then 10, no audio stream available.");
        is_running_ = false;
        return;
      }
    }
    RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "Service: /homi_speech/helper_assistant_abort_service is ready.");

#ifdef VSNODE
    if (forceIDR_) {
        cnt = 0;
        forceClient_ = this->create_client<homi_speech_interface::srv::ForceIDR>(video_namespace_ + "/force_idr_service");
        while (!forceClient_->wait_for_service(std::chrono::seconds(1))) {
            if (!rclcpp::ok()) {
            RCLCPP_ERROR(rclcpp::get_logger(LOGNAME), "Interrupted while waiting for service: " + video_namespace_ + "/force_idr_service.");
            is_running_ = false;
            return;
            }
            RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "Waiting for service: " + video_namespace_ + "/force_idr_service.");
            if(++cnt > 10) {
            RCLCPP_ERROR(rclcpp::get_logger(LOGNAME), "Waiting for service more then 10, no video stream available.");
            is_running_ = false;
            return;
            }
        }
        RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "Service: " + video_namespace_ + "/force_idr_service is ready.");
    }

    getClient_ = this->create_client<homi_speech_interface::srv::GetVideoStream>(video_namespace_ + "/get_video_service");
    while (!getClient_->wait_for_service(std::chrono::seconds(1))) {
      if (!rclcpp::ok()) {
        RCLCPP_ERROR(rclcpp::get_logger(LOGNAME), "Interrupted while waiting for service: " + video_namespace_ + "/get_video_service.");
        is_running_ = false;
        return;
      }
      RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "Waiting for service: " + video_namespace_ + "/get_video_service.");
      if(++cnt > 10) {
        RCLCPP_ERROR(rclcpp::get_logger(LOGNAME), "Waiting for service more then 10, no video stream available.");
        is_running_ = false;
        return;
      }
    }
    RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "Service: " + video_namespace_ + "/get_video_service is ready.");

    endClient_ = this->create_client<homi_speech_interface::srv::EndVideoStream>(video_namespace_ + "/end_video_service");
    while (!endClient_->wait_for_service(std::chrono::seconds(1))) {
      if (!rclcpp::ok()) {
        RCLCPP_ERROR(rclcpp::get_logger(LOGNAME), "Interrupted while waiting for service: " + video_namespace_ + "/end_video_service.");
        is_running_ = false;
        return;
      }
      RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "Waiting for service: " + video_namespace_ + "/end_video_service.");
      if(++cnt > 10) {
        RCLCPP_ERROR(rclcpp::get_logger(LOGNAME), "Waiting for service more then 10, no video stream available.");
        is_running_ = false;
        return;
      }
    }
    RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "Service: " + video_namespace_ + "/end_video_service is ready.");
#endif

    emotionClient_ = this->create_client<homi_speech_interface::srv::RtcEmotionChange>("/audio_player/change_emotion");
    while (!emotionClient_->wait_for_service(std::chrono::seconds(1))) {
        if (!rclcpp::ok()) {
            RCLCPP_ERROR(rclcpp::get_logger(LOGNAME), "Interrupted while waiting for service: /audio_player/change_emotion.");
            is_running_ = false;
            return;
        }
        RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "Waiting for service: /audio_player/change_emotion.");
        if(++cnt > 10) {
            RCLCPP_ERROR(rclcpp::get_logger(LOGNAME), "Waiting for service more then 10, no audio_player service.");
            is_running_ = false;
            return;
        }
    }
    RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "Service: /audio_player/change_emotion is ready.");

    wavClient_ = this->create_client<homi_speech_interface::srv::PlayWav>("/audio_player/play_wav");
    while (!wavClient_->wait_for_service(std::chrono::seconds(1))) {
        if (!rclcpp::ok()) {
            RCLCPP_ERROR(rclcpp::get_logger(LOGNAME), "Interrupted while waiting for service: /audio_player/play_wav.");
            is_running_ = false;
            return;
        }
        RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "Waiting for service: /audio_player/play_wav.");
        if(++cnt > 10) {
            RCLCPP_ERROR(rclcpp::get_logger(LOGNAME), "Waiting for service more then 10, no audio_player service.");
            is_running_ = false;
            return;
        }
    }
    RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "Service: /audio_player/play_wav is ready.");

    getAudioCLient_ = this->create_client<homi_speech_interface::srv::GetPcmPlayer>("/audio_player/get_pcm_player");
    while (!getAudioCLient_->wait_for_service(std::chrono::seconds(1))) {
        if (!rclcpp::ok()) {
            RCLCPP_ERROR(rclcpp::get_logger(LOGNAME), "Interrupted while waiting for service: /audio_player/get_pcm_player.");
            is_running_ = false;
            return;
        }
        RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "Waiting for service: /audio_player/get_pcm_player.");
        if(++cnt > 10) {
            RCLCPP_ERROR(rclcpp::get_logger(LOGNAME), "Waiting for service more then 10, no audio_player service.");
            is_running_ = false;
            return;
        }
    }
    RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "Service: /audio_player/get_pcm_player is ready.");

    endAudioClient_ = this->create_client<homi_speech_interface::srv::EndPcmPlayer>("/audio_player/end_pcm_player");
    while (!endAudioClient_->wait_for_service(std::chrono::seconds(1))) {
        if (!rclcpp::ok()) {
            RCLCPP_ERROR(rclcpp::get_logger(LOGNAME), "Interrupted while waiting for service: /audio_player/end_pcm_player.");
            is_running_ = false;
            return;
        }
        RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "Waiting for service: /audio_player/end_pcm_player.");
        if(++cnt > 10) {
            RCLCPP_ERROR(rclcpp::get_logger(LOGNAME), "Waiting for service more then 10, no audio_player service.");
            is_running_ = false;
            return;
        }
    }
    RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "Service: /audio_player/end_pcm_player is ready.");
}

CmccRtcNode::~CmccRtcNode()
{
    cmcc_rtc_fini();
    vs_node->fini();
}

void CmccRtcNode::init()
{
    cmcc_rtc_init(&device_info_, &log_config_, &event_handler);

    for(std::unordered_map<cmcc_rtc_opt_, const char*>::iterator it=rtc_opts_.begin(); it!=rtc_opts_.end(); it++) {
        cmcc_rtc_setopt(it->first, it->second);
    }

    if(dev_) {
        RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "======================= rtc running in dev =======================\n");
        cmcc_rtc_setopt(cmcc_rtc_opt_(1000), "dev");
    }

    while(cmcc_rtc_login(&login_params_) != 0) {
        RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "======================= failed to invoke cmcc_rtc_login interface =======================\n");
        usleep(1000 * 1000* 10);
    }

    _writeIsAvaliableShmId = shmget((key_t)2886, sizeof(is_avalibale), 0);
    while(_writeIsAvaliableShmId == -1) {
        _writeIsAvaliableShmId = shmget((key_t)2886, sizeof(is_avalibale), 0);
    }
    _writeIsAvaliableShared = (is_avalibale*)shmat(_writeIsAvaliableShmId, NULL, 0);
    while(_writeIsAvaliableShared == (is_avalibale*)-1) {
        _writeIsAvaliableShared = (is_avalibale*)shmat(_writeIsAvaliableShmId, NULL, 0);
    }
    
    _readIsAvaliableShmId = shmget((key_t)4395, sizeof(is_avalibale), 0);
    while(_readIsAvaliableShmId == -1) {
        _readIsAvaliableShmId = shmget((key_t)4395, sizeof(is_avalibale), 0);
    }
    _readIsAvaliableShared = (is_avalibale*)shmat(_readIsAvaliableShmId, NULL, 0);
    while(_readIsAvaliableShared == (is_avalibale*)-1) {
        _readIsAvaliableShared = (is_avalibale*)shmat(_readIsAvaliableShmId, NULL, 0);
    }
    
    callServer_ = this->create_service<homi_speech_interface::srv::PhoneCall>("phone_call_service",
        std::bind(&CmccRtcNode::phoneCall, this, std::placeholders::_1, std::placeholders::_2));
    audioSuber_ = this->create_subscription<homi_speech_interface::msg::PCMStream>("/audio_recorder/pcm_stream", 10,
        std::bind(&CmccRtcNode::pcmCallback, this, std::placeholders::_1));
    audioPuber_ = this->create_publisher<homi_speech_interface::msg::PCMStream>("pcm_call", 10);
    audioTimer_ = this->create_wall_timer(std::chrono::milliseconds(10), std::bind(&CmccRtcNode::audioTimerCallback, this));
}

bool CmccRtcNode::getRtcConfig()
{
    std::string os_version, image_version, hardware_version, device_model, sn, cmei, mac, chip_model, chip_uuid, chip_brand;
    if (!this->get_parameter("device_info.os_version", os_version)
        || !this->get_parameter("device_info.image_version", image_version)
        || !this->get_parameter("device_info.hardware_version", hardware_version)
        || !this->get_parameter("device_info.device_model", device_model)
        || !this->get_parameter("device_info.sn", sn)
        || !this->get_parameter("device_info.cmei", cmei)
        || !this->get_parameter("device_info.mac", mac)
        || !this->get_parameter("device_info.chip_model", chip_model)
        || !this->get_parameter("device_info.chip_uuid", chip_uuid)
        || !this->get_parameter("device_info.chip_brand", chip_brand)) {
        RCLCPP_ERROR(rclcpp::get_logger(LOGNAME), "CmccRtcNode::getRtcConfig get device_info failed");
        return false;
    } else {
        device_info_.os_version = strdup(os_version.c_str());
        device_info_.image_version = strdup(image_version.c_str());
        device_info_.hardware_version = strdup(hardware_version.c_str());
        device_info_.device_model = strdup(device_model.c_str());
        device_info_.sn = strdup(sn.c_str());
        device_info_.cmei = strdup(cmei.c_str());
        device_info_.mac = strdup(mac.c_str());
        device_info_.chip_model = strdup(chip_model.c_str());
        device_info_.chip_uuid = strdup(chip_uuid.c_str());
        device_info_.chip_brand = strdup(chip_brand.c_str());
    }

    int log_size, console_log_enable;
    std::string log_path;
    if (!this->get_parameter("log_config.log_size", log_size)
        || !this->get_parameter("log_config.console_log_enable", console_log_enable)
        || !this->get_parameter("log_config.log_path", log_path)) {
        RCLCPP_ERROR(rclcpp::get_logger(LOGNAME), "CmccRtcNode::getRtcConfig get log_config failed");
        return false;
    } else {
        log_config_.log_size = log_size;
        log_config_.console_log_enable = console_log_enable;
        log_config_.log_path = strdup(log_path.c_str());
    }

    std::string app_key, app_secret, device_id;
    if (!this->get_parameter("login_params.app_key", app_key)
        || !this->get_parameter("login_params.app_secret", app_secret)
        || !this->get_parameter("login_params.device_id", device_id)) {
        RCLCPP_ERROR(rclcpp::get_logger(LOGNAME), "CmccRtcNode::getRtcConfig get login_params failed");
        return false;
    } else {
        login_params_.app_key = strdup(app_key.c_str());
        login_params_.app_secret = strdup(app_secret.c_str());
        login_params_.device_id = strdup(device_id.c_str());
    }

    auto parameter_names = this->list_parameters({"rtc_opts"}, 0);
    for (std::string name : parameter_names.names) {
        std::string value;
        if (!this->get_parameter(name, value)) {
            RCLCPP_ERROR(rclcpp::get_logger(LOGNAME), "CmccRtcNode::getRtcConfig get %s failed", name);
            return false;
        }
        rtc_opts_[stringsToEnum[name.substr(strlen("rtc_opts."))]] = strdup(value.c_str());
    }

    dev_ = false;
    if (!this->get_parameter("dev", dev_)) {
        RCLCPP_ERROR(rclcpp::get_logger(LOGNAME), "CmccRtcNode::getRtcConfig get dev failed");
        return false;
    }

    if (!this->get_parameter("gst_debug_level", gst_debug_level_)) {
        RCLCPP_ERROR(rclcpp::get_logger(LOGNAME), "CmccRtcNode::getRtcConfig get gst_debug_level failed");
        return false;
    }

    if (!this->get_parameter("video_namespace", video_namespace_)) {
        RCLCPP_ERROR(rclcpp::get_logger(LOGNAME), "CmccRtcNode::getRtcConfig get namespace failed");
        return false;
    }

#ifdef VSNODE
    if (!this->get_parameter("forceIDR", forceIDR_)) {
        RCLCPP_ERROR(rclcpp::get_logger(LOGNAME), "CmccRtcNode::getRtcConfig get forceIDR failed");
        return false;
    }
#endif

    return true;
}

bool CmccRtcNode::getIsRunning()
{
    return is_running_;
}

bool CmccRtcNode::phoneCall(const std::shared_ptr<homi_speech_interface::srv::PhoneCall::Request> req,
    std::shared_ptr<homi_speech_interface::srv::PhoneCall::Response> resp)
{
    if (req->type == "pickup") {
        int ret = 0;
        if (1 == g_calling) {
            ret = cmcc_rtc_pickup(g_call_session);
#ifndef ENABLE_AUDIO_CODEC
            g_enable_audio = 1;
            create_send_audio_thread();
#endif
            if (CMCC_CALL_TYPE_IMS_1V1_VIDEO == g_call_type && 0 == ret) {
                g_enable_video = 1;
                create_send_video_thread();
            }
        } else {
            RCLCPP_ERROR(rclcpp::get_logger(LOGNAME), "============ CmccRtcNode::phoneCall pickup error ============\n");
            resp->status = 1;
            return false;
        }
    } else if (req->type == "hangup") {
        if (1 == g_calling) {
            cmcc_rtc_hangup(g_call_session);
            g_calling = 0;
#ifndef ENABLE_AUDIO_CODEC
            g_enable_audio = 0;
#endif
            g_enable_video = 0;

            if(vs_node) {
                vs_node->stop();
            }
#ifdef VSNODE
            CmccRtcNode::endVideoStream(resolution);
#endif
            CmccRtcNode::endAudioPlayer();
            CmccRtcNode::changeEmotion("default_wake.mp4", 0);
#ifdef XUNFEI
            CmccRtcNode::setNrMode(NR_MODE_INTERACT);
#endif
            // CmccRtcNode::setWakeup(true);
        } else {
            RCLCPP_ERROR(rclcpp::get_logger(LOGNAME), "============ CmccRtcNode::phoneCall hangup error ============\n");
            resp->status = 2;
            return false;
        }
#ifdef SDK_INCLUDE_THIRD_PARTY_DEVICE
        if (NULL != g_audio_pcm_fp_input) {
            fclose(g_audio_pcm_fp_input);
            g_audio_pcm_fp_input = NULL;
        }
                
        if (NULL != g_audio_pcm_fp_output) {
            fclose(g_audio_pcm_fp_output);
            g_audio_pcm_fp_output = NULL;
        }
#endif
    } else if (req->type == "callout") {
        if (1 == g_login_success) {
            if (0 == g_calling) {
                if (req->phone_number.length() == 0) {
                    RCLCPP_ERROR(rclcpp::get_logger(LOGNAME), "============ CmccRtcNode::phoneCall expected number ============\n");
                    resp->status = 3;
                    return false;
                } else {
                    // CmccRtcNode::abortSpeech();
                    // CmccRtcNode::setWakeup(false);
#ifdef XUNFEI
                    CmccRtcNode::setNrMode(NR_MODE_CALL);
#endif
                    CmccRtcNode::getAudioPlayer();
                    RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "============ CmccRtcNode::phoneCall cmcc_rtc_callout ============\n");
                    cmcc_rtc_callout(req->phone_number.c_str(),CMCC_CALL_TYPE_IMS_1V1_VIDEO);
                    g_calling = 1;
                    CmccRtcNode::changeEmotion("step1_call.mp4", 0);
                }
            } else {
                RCLCPP_ERROR(rclcpp::get_logger(LOGNAME), "============ CmccRtcNode::phoneCall be calling ============\n");
                resp->status = 4;
                return false;
            }

        } else {
            RCLCPP_ERROR(rclcpp::get_logger(LOGNAME), "============ login failed ============\n");
            resp->status = 5;
            return false;
        }
    } else {
        RCLCPP_ERROR(rclcpp::get_logger(LOGNAME), "CmccRtcNode::phoneCall unknown type %s", req->type.c_str());
        resp->status = 6;
        return false;
    }

    resp->status = 0;
    return true;
}

void CmccRtcNode::pcmCallback(const homi_speech_interface::msg::PCMStream::SharedPtr msg)
{
#ifdef SDK_INCLUDE_THIRD_PARTY_DEVICE
    if(_writeIsAvaliableShared->avaliable) {
        if(_writeShared==NULL) {
            _writeShmId = shmget((key_t)2887, sizeof(audio_buffer), 0);
            if(_writeShmId == -1) {
                RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "======================= connect pcmCallback share memory failed, code: %d =======================\n", errno);
                return;
            }
            _writeShared = (audio_buffer*)shmat(_writeShmId, NULL, 0);
            if(_writeShared == (audio_buffer*)-1) {
                RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "======================= connect pcmCallback share memory failed, code: %d =======================\n", errno);
                _writeShared = NULL;
                return;
            }
        }
        size_t curlen = M_TOTSIZE - (_writeShared->m_Writeindex + M_TOTSIZE - _writeShared->m_Readindex) % M_TOTSIZE;
        if(msg->data.size() >= curlen){   
            RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "======================= pcmCallback share memory not enough =======================\n");                                               //当前的缓冲区仅剩一帧可以写，此时要等待读取，暂时抛弃一部分数据
            return;
        }
        if((_writeShared->m_Writeindex + msg->data.size()) > M_TOTSIZE)
        {
            int overlen = _writeShared->m_Writeindex + msg->data.size() - M_TOTSIZE;
            int curlen = msg->data.size() - overlen;
            memcpy(_writeShared->m_Audio + _writeShared->m_Writeindex, &msg->data[0], curlen);
            memcpy(_writeShared->m_Audio, &msg->data[0] + curlen, overlen);
            _writeShared->m_Writeindex = (_writeShared->m_Writeindex + msg->data.size()) % M_TOTSIZE;
        } else {
            memcpy(_writeShared->m_Audio + _writeShared->m_Writeindex, &msg->data[0], msg->data.size());
            _writeShared->m_Writeindex += msg->data.size();
        }
    } else {
        if(_writeShared)
        {
            shmdt(_writeShared);
            _writeShared = NULL;
        }
    }
#else
    if(g_calling == 1) {}
#endif
}

void CmccRtcNode::audioTimerCallback()
{
#ifdef SDK_INCLUDE_THIRD_PARTY_DEVICE
    if (_readIsAvaliableShared->avaliable) {
        if(_readShared==NULL) {
            _readShmId = shmget((key_t)4396, sizeof(audio_buffer), 0);
            if(_readShmId == -1) {
                RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "======================= connect audioTimerCallback share memory failed, code: %d =======================\n", errno);
                return;
            }
            _readShared = (audio_buffer*)shmat(_readShmId, NULL, 0);
            if(_readShared == (audio_buffer*)-1) {
                RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "======================= connect audioTimerCallback share memory failed, code: %d =======================\n", errno);
                _readShared = NULL;
                return;
            }
        }
        int curlen = (_readShared->m_Writeindex + M_TOTSIZE - _readShared->m_Readindex) % M_TOTSIZE;
        if(320 < curlen) {
            homi_speech_interface::msg::PCMStream msg;
            if ((_readShared->m_Readindex + 320) > M_TOTSIZE) {
                int overlen = _readShared->m_Readindex + 320 - M_TOTSIZE;
                int curlen = 320 - overlen;
                msg.data = std::vector<unsigned char>(_readShared->m_Audio + _readShared->m_Readindex, _readShared->m_Audio + _readShared->m_Readindex + curlen);
                msg.data.insert(msg.data.end(), _readShared->m_Audio, _readShared->m_Audio + overlen);
                _readShared->m_Readindex = (_readShared->m_Readindex + 320) % M_TOTSIZE;
            } else {
                msg.data = std::vector<unsigned char>(_readShared->m_Audio + _readShared->m_Readindex, _readShared->m_Audio + _readShared->m_Readindex+320);
                _readShared->m_Readindex += 320;
            }
            auto now = std::chrono::system_clock::now();
            auto ts = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()).count();
            msg.ts = ts;
            audioPuber_->publish(msg);
        } else {
            RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "======================= audioTimerCallback share memory data is insufficient =======================\n");
            return;
        }
    } else {
        if(_readShared)
        {
            shmdt(_readShared);
            _readShared = NULL;
        }
    }
#else
    if(g_calling == 1) {}
#endif
}

#ifdef XUNFEI
void CmccRtcNode::setNrMode(VS_NR_Mode_e mode)
{
    auto nrReq = std::make_shared<homi_speech_interface::srv::SetNrMode_Request>();
    nrReq->vs_nr_mode = nrModeToInt32(mode);

    modeClient_->async_send_request(nrReq);
}
#endif

void CmccRtcNode::setWakeup(bool wakeup)
{
    auto wakeReq = std::make_shared<homi_speech_interface::srv::SetWakeEvent_Request>();
    wakeReq->target = wakeup;

    wakeClient_->async_send_request(wakeReq);
}

void CmccRtcNode::abortSpeech()
{
    auto abortReq = std::make_shared<homi_speech_interface::srv::AssistantAbort_Request>();
    
    abortClient_->async_send_request(abortReq);
}

#ifdef VSNODE
void CmccRtcNode::forceIDR(std::string resolution)
{
    if (forceIDR_) {
        if(resolution!="sd" && resolution!="hd" && resolution!="fhd") {
            RCLCPP_ERROR(rclcpp::get_logger(LOGNAME), "CmccRtcNode::forceIDR: Invalid resolution: %s, effective resolution: sd, hd, fhd.", resolution.c_str());
            return;
        }

        auto forceReq = std::make_shared<homi_speech_interface::srv::ForceIDR_Request>();
        forceReq->resolution = resolution;

        forceClient_->async_send_request(forceReq);
    }
}

void CmccRtcNode::getVidoeStream(std::string resolution, bool response)
{
    if(resolution!="sd" && resolution!="hd" && resolution!="fhd") {
        RCLCPP_ERROR(rclcpp::get_logger(LOGNAME), "CmccRtcNode::getVidoeStream: Invalid resolution: %s, effective resolution: sd, hd, fhd.", resolution.c_str());
        return;
    }

    auto getReq = std::make_shared<homi_speech_interface::srv::GetVideoStream_Request>();
    getReq->resolution = resolution;

    if (response) {
        getClient_->async_send_request(getReq, [resolution](rclcpp::Client<homi_speech_interface::srv::GetVideoStream>::SharedFuture resp_future) {
            auto resp = resp_future.get();
            if (resp->success) {
                vs_node->get(resolution);
            } else {
                RCLCPP_ERROR(rclcpp::get_logger(LOGNAME), "CmccRtcNode::getVidoeStream: Get %s stream failed.", resolution.c_str());
            }
        });
    } else {
        getClient_->async_send_request(getReq);
    }
}

void CmccRtcNode::endVideoStream(std::string resolution)
{
    if(resolution!="sd" && resolution!="hd" && resolution!="fhd") {
        RCLCPP_ERROR(rclcpp::get_logger(LOGNAME), "CmccRtcNode::endVideoStream: Invalid resolution: %s, effective resolution: sd, hd, fhd.", resolution.c_str());
        return;
    }

    auto endReq = std::make_shared<homi_speech_interface::srv::EndVideoStream_Request>();
    endReq->resolution = resolution;

    endClient_->async_send_request(endReq);
}
#endif

void CmccRtcNode::changeEmotion(std::string filename, int cnt)
{
    auto changeReq = std::make_shared<homi_speech_interface::srv::RtcEmotionChange_Request>();
    changeReq->str = filename;
    changeReq->cnt = cnt;

    emotionClient_->async_send_request(changeReq);
}

void CmccRtcNode::playWav(std::string filename)
{
    auto playReq = std::make_shared<homi_speech_interface::srv::PlayWav_Request>();
    playReq->str = filename;

    wavClient_->async_send_request(playReq);
}

void CmccRtcNode::getAudioPlayer()
{
    auto getReq = std::make_shared<homi_speech_interface::srv::GetPcmPlayer_Request>();

    getAudioCLient_->async_send_request(getReq);
}

void CmccRtcNode::endAudioPlayer()
{
    auto endReq = std::make_shared<homi_speech_interface::srv::EndPcmPlayer_Request>();

    endAudioClient_->async_send_request(endReq);
}

void CmccRtcNode::VideoVoipBufferCallback(uint8_t *data, unsigned long length, int width, int height) {
    (void)width;
    (void)height;
    cmcc_rtc_send_video(g_call_session, reinterpret_cast<char *>(data), length);
}

#ifdef ENABLE_MULTI_STREAMS
void CmccRtcNode::VideoCameraBufferCallback(uint8_t *data, unsigned long length, int width, int height) {
}
#endif
void CmccRtcNode::create_send_video_thread()
{
    RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "================ create_send_video_thread ================\n");
    if (vs_node) {
        vs_node->start();
    }
}

#ifndef ENABLE_AUDIO_CODEC
void CmccRtcNode::__on_recv_audio_packet(char *packet ,int size)
{
    if (g_audio_fp) {
        size_t num_bytes = fwrite(packet, 1, size, g_audio_fp);
        fflush(g_audio_fp);
    }
}
#endif

void CmccRtcNode::__on_login_success(const char *user)
{
    (void)user;
    RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "================ __on_login_success ================\n");
    g_login_success = 1;
}

void CmccRtcNode::__on_login_failed(const char *user, int err_code, const char *reason)
{
    (void)user;
    (void)err_code;
    (void)reason;
    RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "================ __on_login_failed ================\n");
    g_login_success = 0;
}

void CmccRtcNode::__on_recv_call(int session, const char *from, const char *displayname, const char *to, cmcc_call_type_t call_type, const char *json_call_control)
{
    (void)displayname;
    (void)to;
    int ret = 0;
    g_calling = 1;
    RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "================ __on_recv_call(%d) json_call_control(%s)================\n",call_type, json_call_control);
    const char *nick_name = cmcc_rtc_get_nickname_by_number(from);
    RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "from number:%s,nickname:%s\n",from, nick_name);
    RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "================================================\n");
    g_call_type = call_type;

    // CmccRtcNode::abortSpeech();
    // CmccRtcNode::setWakeup(false);
#ifdef XUNFEI
    CmccRtcNode::setNrMode(NR_MODE_CALL);
#endif
    CmccRtcNode::getAudioPlayer();

    try {
        if (NULL != strstr(json_call_control, "answer_auto")) {
            if (NULL != strstr(json_call_control, "app_broadcast")) {
                RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "__on_recv_call: app_broadcast answer_auto");
            }else if (NULL != strstr(json_call_control, "app_talk")) {
                RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "__on_recv_call: app_talk answer_auto");
            }

            cmcc_rtc_setopt(CMCC_OPT_AUTO_ANSWER_FLAG,"1");
            ret = cmcc_rtc_pickup(session);

#ifndef ENABLE_AUDIO_CODEC
            g_enable_audio = 1;
            create_send_audio_thread();
#endif
            if (CMCC_CALL_TYPE_IMS_1V1_VIDEO == g_call_type && 0 == ret) {
                g_enable_video = 1;
                create_send_video_thread();
            }
        } else {
            if(NULL != strstr(json_call_control, "called")) {
                CmccRtcNode::changeEmotion("step1_call.mp4", 0);
                CmccRtcNode::playWav("ring.wav");
            }

            cmcc_rtc_setopt(CMCC_OPT_AUTO_ANSWER_FLAG,"1");
            ret = cmcc_rtc_pickup(session);

            CmccRtcNode::changeEmotion("step2_call_wake.mp4", 0);

#ifndef ENABLE_AUDIO_CODEC
            g_enable_audio = 1;
            create_send_audio_thread();
#endif
            if (CMCC_CALL_TYPE_IMS_1V1_VIDEO == g_call_type && 0 == ret) {
                g_enable_video = 1;
                create_send_video_thread();
            }
        }
    } catch (const std::exception& e) {
        RCLCPP_ERROR(rclcpp::get_logger(LOGNAME), "CmccRtcNode::__on_recv_call wakeCallback error: %s", e.what());
    }
}

void CmccRtcNode::__on_recv_ring(int session, const char *from, const char *displayname, const char *to, int early_media)
{
    (void)session;
    (void)from;
    (void)displayname;
    (void)to;
    (void)early_media;
#ifndef ENABLE_AUDIO_CODEC
    if (1 == early_media) {
        g_enable_audio = 1;
        create_send_audio_thread();
    }
#endif
    RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "================ __on_recv_ring ================\n");
}

void CmccRtcNode::__on_recv_answer(int session, const char *from, const char *displayname, const char *to, cmcc_call_type_t call_type)
{
    (void)session;
    (void)from;
    (void)displayname;
    (void)to;
    CmccRtcNode::changeEmotion("step2_call_wake.mp4", 0);

    RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "================ __on_recv_answer calltype[%d] ================\n",call_type);

#ifndef ENABLE_AUDIO_CODEC
    if (0 == g_enable_audio) {
        g_enable_audio = 1;
        create_send_audio_thread();
    }
#endif
    g_calling = 1;
    if (CMCC_CALL_TYPE_IMS_1V1_VIDEO == call_type) {
        g_enable_video = 1;
        create_send_video_thread();
    }
}

void CmccRtcNode::__on_recv_hangup(int session, int err_code, const char *reason, cmcc_call_type_t call_type)
{
    (void)session;
    (void)err_code;
    (void)reason;
    (void)call_type;
    RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "================ __on_recv_hangup ================\n");
    g_calling = 0;
#ifndef ENABLE_AUDIO_CODEC
    g_enable_audio = 0;
#endif
    g_enable_video = 0;

    if(vs_node) {
        vs_node->stop();
    }
#ifdef VSNODE
    CmccRtcNode::endVideoStream(resolution);
#endif
#ifdef SDK_INCLUDE_THIRD_PARTY_DEVICE
        if ( NULL != g_audio_pcm_fp_input) {
            fclose(g_audio_pcm_fp_input);
            g_audio_pcm_fp_input = NULL;
        }
        
        if (NULL != g_audio_pcm_fp_output) {
            fclose(g_audio_pcm_fp_output);
            g_audio_pcm_fp_output = NULL;
        }
#endif

    CmccRtcNode::endAudioPlayer();
    CmccRtcNode::changeEmotion("default_wake.mp4", 0);
    
#ifdef XUNFEI
    CmccRtcNode::setNrMode(NR_MODE_INTERACT);
#endif
    // CmccRtcNode::setWakeup(true);
}

void CmccRtcNode::__on_recv_notify(cmcc_notify_t notify_type, const char *content)
{
    RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "================ __on_recv_notify ================\n"); 
    RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "type:%d, content:%s\n", notify_type, content);  
    if (CMCC_NOTIFY_VIDEO_CODEC == notify_type) {
        if (NULL != strstr(content,"H265")) {
            RCLCPP_ERROR(rclcpp::get_logger(LOGNAME), "================ __on_recv_notify unsupported H265 ================\n");
        }
    } else if (CMCC_NOTIFY_VIDEO_RESOLUTION == notify_type) {
        if (NULL != strstr(content,"quality=")){
            if (NULL != strstr(content,"quality=sd")) {
                if(vs_node) {
                    resolution = "hd";
#ifdef VSNODE
                    CmccRtcNode::getVidoeStream(resolution, true);
#endif
                }
            }else if( NULL != strstr(content,"quality=hd")){
                if(vs_node) {
                    resolution = "hd";
#ifdef VSNODE
                    CmccRtcNode::getVidoeStream(resolution, true);
#endif
                }
            }else if(NULL != strstr(content,"quality=fhd")){
                if(vs_node) {
                    resolution = "fhd";
#ifdef VSNODE
                    CmccRtcNode::getVidoeStream(resolution, true);
#endif
                }
            }
        }else{
            if(NULL != strstr(content,"640") || NULL != strstr(content,"360")){
                if(vs_node) {
                    resolution = "hd";
#ifdef VSNODE
                    CmccRtcNode::getVidoeStream(resolution, true);
#endif
                }
            }else if(NULL != strstr(content,"1280") || NULL != strstr(content,"720")) {
                if(vs_node) {
                    resolution = "hd";
#ifdef VSNODE
                    CmccRtcNode::getVidoeStream(resolution, true);
#endif
                }
            }else if(NULL != strstr(content,"1920") || NULL != strstr(content,"1080")){
                if(vs_node) {
                    resolution = "fhd";
#ifdef VSNODE
                    CmccRtcNode::getVidoeStream(resolution, true);
#endif
                }
            }else{
                if(vs_node) {
                    resolution = "hd";
#ifdef VSNODE
                    CmccRtcNode::getVidoeStream(resolution, true);
#endif
                }
            }
        }
        if (g_enable_video) {
            g_video_resolution_changed =1;
        }
    }else if(CMCC_NOTIFY_VIDEO_CODEC_AND_RESOLUTION == notify_type){
        RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "======CMCC_NOTIFY_VIDEO_CODEC_AND_RESOLUTION=========\n\n");
        if (NULL != strstr(content,"quality=")){
            if (NULL != strstr(content,"quality=sd")) {
                if(vs_node) {
                    resolution = "hd";
#ifdef VSNODE
                    CmccRtcNode::getVidoeStream(resolution, true);
#endif
                }
            }else if( NULL != strstr(content,"quality=hd")){
                if(vs_node) {
                    resolution = "hd";
#ifdef VSNODE
                    CmccRtcNode::getVidoeStream(resolution, true);
#endif
                }
            }else if(NULL != strstr(content,"quality=fhd")){
                if(vs_node) {
                    resolution = "fhd";
#ifdef VSNODE
                    CmccRtcNode::getVidoeStream(resolution, true);
#endif
                }
            }
        }else{
            if(NULL != strstr(content,"640") || NULL != strstr(content,"360")){
                if(vs_node) {
                    resolution = "hd";
#ifdef VSNODE
                    CmccRtcNode::getVidoeStream(resolution, true);
#endif
                }
            }else if(NULL != strstr(content,"1280") || NULL != strstr(content,"720")) {
                if(vs_node) {
                    resolution = "hd";
#ifdef VSNODE
                    CmccRtcNode::getVidoeStream(resolution, true);
#endif
                }
            }else if(NULL != strstr(content,"1920") || NULL != strstr(content,"1080")){
                if(vs_node) {
                    resolution = "fhd";
#ifdef VSNODE
                    CmccRtcNode::getVidoeStream(resolution, true);
#endif
                }
            }else{
                if(vs_node) {
                    resolution = "hd";
#ifdef VSNODE
                    CmccRtcNode::getVidoeStream(resolution, true);
#endif
                }
            }
        }
        if (g_enable_video) {
            g_video_resolution_changed =1;
        }
    }else if(CMCC_NOTIFY_REMOTE_CONTROL == notify_type) {
        RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "======CMCC_NOTIFY_REMOTE_CONTROL=========\n\n");
    }
}

void CmccRtcNode::__on_recv_keyframe_request()
{
    RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "================ __on_recv_keyframe_request ================\n");
#ifdef VSNODE
    CmccRtcNode::forceIDR(resolution);
#endif
}

void CmccRtcNode::__on_recv_dtmf(int value)
{
    RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "================ __on_recv_dtmf ================\n");
    RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "value:%d\n",value);
}

#ifdef SDK_INCLUDE_THIRD_PARTY_DEVICE
void CmccRtcNode::__on_recv_audio_pcm_data(char *pcm_data, int size)
{
    if (NULL == g_audio_pcm_fp_output) {
        RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "__on_recv_audio_pcm_data, data size=%d\n", size);
        g_audio_pcm_fp_output = fopen("output16000.pcm", "w");
        if (!g_audio_pcm_fp_output) {
            RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "failed to open output16000 file!!!\n");
            return;
        }
    }
    fwrite(pcm_data, 1, size, g_audio_pcm_fp_output);
}

void CmccRtcNode::__on_send_audio_pcm_data(char *pcm_data, int size)
{
    RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "__on_send_audio_pcm_data, test, data size=%d\n", size);
    if (NULL == g_audio_pcm_fp_input) {
        RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "__on_send_audio_pcm_data, data size=%d\n", size);
        g_audio_pcm_fp_input = fopen("input16000.pcm", "r");
        if (!g_audio_pcm_fp_input) {
            RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "failed to open input16000 file!!!\n");
            return;
        }
    }
    int read_data_len = fread(pcm_data, 1, size, g_audio_pcm_fp_input);
    if (read_data_len <= 0) {
        RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "__on_send_audio_pcm_data,fseek!!!\n");
        fseek(g_audio_pcm_fp_input, 0, SEEK_SET);
    }
}
#endif
