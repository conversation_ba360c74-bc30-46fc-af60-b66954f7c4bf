#include "video_source.h"

static GstFlowReturn cb_call_sink_new_sample(GstElement *appsink,
                                           gpointer user_data) {
    VideoSourceNode *video_source_node =
            reinterpret_cast<VideoSourceNode *>(user_data);

    GstSample *sample = NULL;
    GstBuffer *buffer = NULL;
    GstMapInfo map;
    GstFlowReturn ret = GST_FLOW_OK;
    int sample_width = 0;
    int sample_height = 0;

    // equals to gst_app_sink_pull_sample (GST_APP_SINK_CAST (appsink), sample);
    g_signal_emit_by_name(appsink, "pull-sample", &sample, &ret);
    if (ret != GST_FLOW_OK) {
        RCLCPP_ERROR(rclcpp::get_logger(VIDEOLOG),"can't pull GstSample.");
        goto exit;
    }

    if (sample) {
        buffer = gst_sample_get_buffer(sample);
        if (buffer == NULL) {
            RCLCPP_ERROR(rclcpp::get_logger(VIDEOLOG),"get buffer is null");
            goto exit;
        }

        gst_buffer_map(buffer, &map, GST_MAP_READ);

        // appsink product queue produce
        // init a cv::Mat with gst buffer address: deep copy
        if (map.data == NULL) {
            RCLCPP_ERROR(rclcpp::get_logger(VIDEOLOG),"appsink buffer data empty");
            return GST_FLOW_NOT_LINKED;
        }
        if (video_source_node->mVoipBufferCallback) {
            video_source_node->GetWidthAndHeight(sample_width, sample_height);
            video_source_node->mVoipBufferCallback(map.data, map.size,
                                                sample_width, sample_height);
        }
    }

exit:
    if (buffer) {
        gst_buffer_unmap(buffer, &map);
    }
    if (sample) {
        gst_sample_unref(sample);
    }
    return GST_FLOW_OK;
}

VideoSourceNode::VideoSourceNode() : resolution_(""), mPipeline_(nullptr), mSrc_(nullptr), mSink_(nullptr), shmsrc1_(nullptr), shmsrc2_(nullptr), shmsrc3_(nullptr) {}

VideoSourceNode::~VideoSourceNode() {}

bool VideoSourceNode::init(int gst_debug_level)
{
    gst_init(nullptr, nullptr);

    // 设置日志等级
    gst_debug_set_threshold_for_name("*", (GstDebugLevel)gst_debug_level);
    gst_debug_set_threshold_for_name("pipeline", (GstDebugLevel)gst_debug_level);

    this->mPipeline_ = gst_pipeline_new("rtc-pipeline");

    this->shmsrc1_ = gst_element_factory_make("shmsrc", "shmsrc1");
    g_object_set(G_OBJECT(this->shmsrc1_), "socket-path", SOCKET_FHD, nullptr);
    this->shmsrc2_ = gst_element_factory_make("shmsrc", "shmsrc2");
    g_object_set(G_OBJECT(this->shmsrc2_), "socket-path", SOCKET_HD, nullptr);
    this->shmsrc3_ = gst_element_factory_make("shmsrc", "shmsrc3");
    g_object_set(G_OBJECT(this->shmsrc3_), "socket-path", SOCKET_SD, nullptr);

    this->mSink_ = gst_element_factory_make("appsink", "appsink");
    g_object_set(this->mSink_, "emit-signals", TRUE, NULL);
    g_signal_connect(this->mSink_, "new-sample", G_CALLBACK(cb_call_sink_new_sample),
        reinterpret_cast<void *>(this));
    
    this->resolution_ = "hd";
    this->mSrc_ = this->shmsrc2_;

    gst_bin_add_many(GST_BIN(this->mPipeline_), this->mSrc_, this->mSink_, nullptr);
    if(!gst_element_link_many(this->mSrc_, this->mSink_, nullptr)) {
        RCLCPP_ERROR(rclcpp::get_logger(VIDEOLOG), "VideoSourceNode::create: Failed to link elements");
        return false;
    }

    gst_pipeline_set_auto_flush_bus(GST_PIPELINE(this->mPipeline_), true);
    GstStateChangeReturn ret = gst_element_set_state(this->mPipeline_, GST_STATE_READY);
    if (ret == GST_STATE_CHANGE_FAILURE) {
        RCLCPP_ERROR(rclcpp::get_logger(VIDEOLOG), "VideoSourceNode::create: Unable to set the pipeline to the ready state.");
        gst_object_unref(this->mPipeline_);
        return false;
    }

    return true;
}

void VideoSourceNode::fini()
{
    this->resolution_ = "";
    this->mSrc_ = nullptr;
    if (this->mPipeline_) {
        gst_bin_add_many(GST_BIN(this->mPipeline_), this->shmsrc1_, this->shmsrc2_, this->shmsrc3_, nullptr);
        gst_element_set_state(this->mPipeline_, GST_STATE_NULL);
        gst_object_unref(this->mPipeline_);
    }
}

// TODO: 状态转换失败重试3次
bool VideoSourceNode::get(std::string resolution)
{
    if(this->resolution_ != resolution) {
        if(resolution!="sd" && resolution!="hd" && resolution!="fhd") {
            RCLCPP_ERROR(rclcpp::get_logger(VIDEOLOG), "CmccRtcNode::getVidoeStream: Invalid resolution: %s, effective resolution: sd, hd, fhd.", resolution.c_str());
            return false;
        }
        
        this->destroy();

        if (resolution == "fhd") {
            this->resolution_ = resolution;
            this->mSrc_ = this->shmsrc1_;
        } else if (resolution == "hd") {
            this->resolution_ = resolution;
            this->mSrc_ = this->shmsrc2_;
        } else if (resolution == "sd") {
            this->resolution_ = resolution;
            this->mSrc_ = this->shmsrc3_;
        }
        return this->create();
    }
    return true;
}

bool VideoSourceNode::start()
{
    int cnt = 0;

    while (cnt++ < 3) {
        GstStateChangeReturn mRet = gst_element_set_state(mPipeline_, GST_STATE_PLAYING);
        if (mRet == GST_STATE_CHANGE_SUCCESS) return true;
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }

    RCLCPP_ERROR(rclcpp::get_logger(VIDEOLOG), "VideoSourceNode::start: Unable to set the pipeline to the playing state (GST_STATE_PLAYING).");
    return false;
}

bool VideoSourceNode::stop()
{
    GstState state, pending;
    if (GST_STATE_CHANGE_ASYNC == gst_element_get_state(this->mPipeline_, &state,
                                                        &pending,
                                                        5 * GST_SECOND / 1000)) {
        RCLCPP_ERROR(rclcpp::get_logger(VIDEOLOG), "VideoSourceNode::stop: Failed to get state of pipeline");
        return false;
    }

    if (state == GST_STATE_PAUSED) {
        return true;
    } else if (state == GST_STATE_PLAYING) {
        gst_element_set_state(this->mPipeline_, GST_STATE_PAUSED);
        return true;
    } else {
        RCLCPP_ERROR(rclcpp::get_logger(VIDEOLOG), "VideoSourceNode::stop: Invalid state of pipeline(%d)", GST_STATE_CHANGE_ASYNC);
        return false;
    }
}

bool VideoSourceNode::create()
{
    gst_bin_add(GST_BIN(this->mPipeline_), this->mSrc_);
    if(!gst_element_link_many(this->mSrc_, this->mSink_, nullptr)) {
        RCLCPP_ERROR(rclcpp::get_logger(VIDEOLOG), "VideoSourceNode::create: Failed to link elements");
        return false;
    }
    gst_pipeline_set_auto_flush_bus(GST_PIPELINE(this->mPipeline_), true);
    GstStateChangeReturn ret = gst_element_set_state(this->mPipeline_, GST_STATE_PAUSED);
    if (ret == GST_STATE_CHANGE_FAILURE) {
        RCLCPP_ERROR(rclcpp::get_logger(VIDEOLOG), "VideoSourceNode::create: Unable to set the pipeline to the paused state.");
        gst_object_unref(this->mPipeline_);
        return false;
    }
    return true;
}

bool VideoSourceNode::destroy()
{
    GstStateChangeReturn ret = gst_element_set_state(this->mPipeline_, GST_STATE_READY);
    if (ret == GST_STATE_CHANGE_FAILURE) {
        RCLCPP_ERROR(rclcpp::get_logger(VIDEOLOG), "VideoSourceNode::destroy: Unable to set the pipeline to the ready state.");
        gst_object_unref(this->mPipeline_);
        return false;
    }
    gst_element_unlink(this->mSrc_, this->mSink_);
    gst_bin_remove(GST_BIN(this->mPipeline_), this->mSrc_);
    return true;
}

void VideoSourceNode::SetVoipBufferCallback(BufferCallback callback)
{
    mVoipBufferCallback = std::move(callback);
}

void VideoSourceNode::GetWidthAndHeight(int &width, int &height)
{
    if(this->resolution_ == "sd") {
        width = 640;
        height = 480;
    } else if(this->resolution_ == "hd") {
        width = 1280;
        height = 720;
    } else if(this->resolution_ == "fhd") {
        width = 1920;
        height = 1080;
    }
}
