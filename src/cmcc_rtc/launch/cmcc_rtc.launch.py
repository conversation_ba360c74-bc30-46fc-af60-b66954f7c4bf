import os
from ament_index_python.packages import get_package_share_directory
from launch import LaunchDescription
from launch_ros.actions import Node


def generate_launch_description():
    config = os.path.join(
        get_package_share_directory("cmcc_rtc"),
        "config",
        "param.yaml"
    )

    return LaunchDescription([
        Node(
            package="cmcc_rtc",
            namespace="cmcc_rtc",
            name="cmcc_rtc_node",
            executable="cmcc_rtc_node",
            parameters=[
                config,
                {"video_namespace": "/video_gst_nv",
                 "forceIDR": False}
            ]
        )
    ])