#include <cstdio>

#include "geometry_msgs/msg/quaternion.hpp"
#include "nav_msgs/srv/get_map.hpp"
#include "rclcpp/rclcpp.hpp"
#include "tf2/LinearMath/Matrix3x3.h"
#include <opencv2/opencv.hpp>
#include <vector>

using namespace std;

class MapGenerator : public rclcpp::Node
{
public:
  MapGenerator(const std::string &mapname, const std::string &topicname, int threshold_occupied, int threshold_free)
      : rclcpp::Node("map_saver"), mapname_(mapname), saved_map_(false), threshold_occupied_(threshold_occupied), threshold_free_(threshold_free)
  {
    RCLCPP_INFO(this->get_logger(), "Waiting for the map");
    map_sub_ = this->create_subscription<nav_msgs::msg::OccupancyGrid>(topicname, rclcpp::QoS(rclcpp::KeepLast(1)).best_effort(),
                                                                       [this](const nav_msgs::msg::OccupancyGrid::SharedPtr msg)
                                                                       { mapCallback(msg); });
  }

  void mapCallback(const nav_msgs::msg::OccupancyGrid::SharedPtr &map)
  {
    RCLCPP_INFO(this->get_logger(), "Received a %d X %d map @ %.3f m/pix", map->info.width, map->info.height, map->info.resolution);

    std::string mapdatafile = mapname_ + ".pgm";
    RCLCPP_INFO(this->get_logger(), "Writing map occupancy data to %s", mapdatafile.c_str());
    FILE *out = fopen(mapdatafile.c_str(), "w");
    if (!out)
    {
      RCLCPP_ERROR(this->get_logger(), "Couldn't save map file to %s", mapdatafile.c_str());
      return;
    }

    fprintf(out, "P5\n# CREATOR: map_saver.cpp %.3f m/pix\n%d %d\n255\n", map->info.resolution, map->info.width, map->info.height);

    //    ROS_INFO("TEST1\n");
    cv::Mat map_mat = cv::Mat::zeros(map->info.height, map->info.width, CV_8UC1);

    for (unsigned int y = 0; y < map->info.height; y++)
    {
      for (unsigned int x = 0; x < map->info.width; x++)
      {
        unsigned int i = x + (map->info.height - y - 1) * map->info.width;
        if (static_cast<uint8_t>(map->data[i]) > 127)
        {
          fputc(254, out);
        }
        else if (static_cast<uint8_t>(map->data[i]) < 127)
        {
          fputc(000, out);
        }
        else
        {
          fputc(205, out);
        }
      }
    }

    fclose(out);

    std::string mapmetadatafile = mapname_ + ".yaml";
    RCLCPP_INFO(this->get_logger(), "Writing map occupancy data to %s", mapmetadatafile.c_str());
    FILE *yaml = fopen(mapmetadatafile.c_str(), "w");

    geometry_msgs::msg::Quaternion orientation = map->info.origin.orientation;
    tf2::Matrix3x3 mat(tf2::Quaternion(orientation.x, orientation.y, orientation.z, orientation.w));
    double yaw, pitch, roll;
    mat.getEulerYPR(yaw, pitch, roll);

    fprintf(yaml, "image: %s\nresolution: %f\norigin: [%f, %f, 0.00]\nnegate: 0\noccupied_thresh: 0.65\nfree_thresh: 0.196\n\n", mapdatafile.c_str(), map->info.resolution, map->info.origin.position.x,
            map->info.origin.position.y);

    fclose(yaml);

    //    ROS_INFO("Done\n");
    saved_map_ = true;
  }

  std::string mapname_;
  rclcpp::Subscription<nav_msgs::msg::OccupancyGrid>::SharedPtr map_sub_;
  bool saved_map_;
  int threshold_occupied_;
  int threshold_free_;
};

#define USAGE        \
  "Usage: \n"        \
  "  map_saver -h\n" \
  "  map_saver [--occ <threshold_occupied>] [--free <threshold_free>] [-f <mapname>] [-t <topicname>] [ROS remapping args]"

int main(int argc, char **argv)
{
  std::string mapname = "map";
  std::string topicname = "map";
  int threshold_occupied = 65;
  int threshold_free = 25;

  for (int i = 1; i < argc; i++)
  {
    if (!strcmp(argv[i], "-h"))
    {
      puts(USAGE);
      return 0;
    }
    else if (!strcmp(argv[i], "-f"))
    {
      if (++i < argc)
        mapname = argv[i];
      else
      {
        puts(USAGE);
        return 1;
      }
    }
    else if (!strcmp(argv[i], "-t"))
    {
      if (++i < argc)
        topicname = argv[i];
      else
      {
        puts(USAGE);
        return 1;
      }
    }
    else if (!strcmp(argv[i], "--occ"))
    {
      if (++i < argc)
      {
        threshold_occupied = std::atoi(argv[i]);
        if (threshold_occupied < 1 || threshold_occupied > 100)
        {
          RCLCPP_ERROR(rclcpp::get_logger("map_saver"), "threshold_occupied must be between 1 and 100");
          return 1;
        }
      }
      else
      {
        puts(USAGE);
        return 1;
      }
    }
    else if (!strcmp(argv[i], "--free"))
    {
      if (++i < argc)
      {
        threshold_free = std::atoi(argv[i]);
        if (threshold_free < 0 || threshold_free > 100)
        {
          RCLCPP_ERROR(rclcpp::get_logger("map_saver"), "threshold_free must be between 0 and 100");
          return 1;
        }
      }
      else
      {
        puts(USAGE);
        return 1;
      }
    }
    else
    {
      puts(USAGE);
      return 1;
    }
  }

  if (threshold_occupied <= threshold_free)
  {
    RCLCPP_ERROR(rclcpp::get_logger("map_saver"), "threshold_free must be smaller than threshold_occupied");
    return 1;
  }

  rclcpp::init(argc, argv);
  auto mg = std::make_shared<MapGenerator>(mapname, topicname, threshold_occupied, threshold_free);
  rclcpp::Rate rate(10);
  while (!mg->saved_map_ && rclcpp::ok())
  {
    rclcpp::spin_some(mg);
    rate.sleep();
  }

  return 0;
}
