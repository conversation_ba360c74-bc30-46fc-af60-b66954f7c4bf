cmake_minimum_required(VERSION 3.5)
project(audio_player)

if("${CMAKE_BUILD_TYPE}" STREQUAL "Release")
  set(CMAKE_C_FLAGS_RELEASE "${CMAKE_C_FLAGS_RELEASE} -s")
  set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} -s")
endif()

# Default to C99
if(NOT CMAKE_C_STANDARD)
  set(CMAKE_C_STANDARD 99)
endif()

# Default to C++14
if(NOT CMAKE_CXX_STANDARD)
  set(CMAKE_CXX_STANDARD 14)
endif()

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# find dependencies
find_package(ament_cmake REQUIRED)
find_package(rclcpp REQUIRED)
find_package(homi_speech_interface REQUIRED)
find_package(ALSA REQUIRED)

include_directories(
# include
  ${ament_cmake_INCLUDE_DIRS}
  ${CMAKE_CURRENT_SOURCE_DIR}/include
  ${homi_speech_interface_INCLUDE_DIRS}
)

add_executable(${PROJECT_NAME}_node
       src/audio_player.cc
       src/alsa_controller.cc
       src/alsa_player.cc
)

ament_target_dependencies(${PROJECT_NAME}_node rclcpp homi_speech_interface)

target_link_libraries(${PROJECT_NAME}_node
      ${ament_cmake_LIBRARIES}
      ${ALSA_LIBRARIES}
      pthread
      jsoncpp
)

set(CMAKE_CUSTOM_PRODUCT_NO "1_0" CACHE STRING "Custom product number (e.g., 1_0. 1_1, 2_0)")
if("${CMAKE_CUSTOM_PRODUCT_NO}" STREQUAL "1_0")
  target_compile_definitions(${PROJECT_NAME}_node PUBLIC YSC1_0)
elseif("${CMAKE_CUSTOM_PRODUCT_NO}" STREQUAL "1_1")
  target_compile_definitions(${PROJECT_NAME}_node PUBLIC YSC1_1)
elseif("${CMAKE_CUSTOM_PRODUCT_NO}" STREQUAL "2_0")
  target_compile_definitions(${PROJECT_NAME}_node PUBLIC UNITREE)
endif()

install(TARGETS ${PROJECT_NAME}_node
  DESTINATION lib/${PROJECT_NAME}
)
install(DIRECTORY config launch resource script
  DESTINATION share/${PROJECT_NAME}
)

if(BUILD_TESTING)
  find_package(ament_lint_auto REQUIRED)
  # the following line skips the linter which checks for copyrights
  # uncomment the line when a copyright and license is not present in all source files
  #set(ament_cmake_copyright_FOUND TRUE)
  # the following line skips cpplint (only works in a git repo)
  # uncomment the line when this package is not in a git repo
  #set(ament_cmake_cpplint_FOUND TRUE)
  ament_lint_auto_find_test_dependencies()
endif()

ament_package()
