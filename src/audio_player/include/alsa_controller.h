#ifndef __ALSA_CONTROLLER_H__
#define __ALSA_CONTROLLER_H__
#include <rclcpp/rclcpp.hpp>
#include <jsoncpp/json/json.h>
#include <fstream>
#include "alsa_player.h"
#include "std_msgs/msg/string.hpp"
#include "homi_speech_interface/msg/pcm_stream.hpp"
#include "homi_speech_interface/srv/rtc_emotion_change.hpp"
#include "homi_speech_interface/srv/get_pcm_player.hpp"
#include "homi_speech_interface/srv/end_pcm_player.hpp"
#include "homi_speech_interface/srv/play_wav.hpp"
#include "homi_speech_interface/srv/get_player_status.hpp"

#define LOGNAME "audio_player"

struct WaveHeader {
    char riff[4];           // "RIFF"
    int32_t length;         // Length of the file excluding the first 8 bytes
    char file_type[4];      // "WAVE"
    char fmt[4];            // "fmt "
    int32_t fmt_length;     // Length of the format data
    short audio_format;     // PCM = 1
    short channels;         // Number of channels (1 for mono, 2 for stereo)
    int32_t sample_rate;    // Sample rate in Hz
    int32_t byte_rate;      // Sample rate * channels * bits per sample / 8
    short sample_alignment; // channels * bits per sample / 8
    short bits_per_sample;  // Bits per sample
    char data[4];           // "data"
    int32_t data_length;    // Length of the data section
};

class AlsaController : public rclcpp::Node
{
public:
    AlsaController(std::string name);
    ~AlsaController();
    
    bool init();
    bool uninit();
    std::string getPlayoutDeviceName();

private:
    void audioCallback (const homi_speech_interface::msg::PCMStream::SharedPtr msg);
    bool changeEmotion (
        const std::shared_ptr<homi_speech_interface::srv::RtcEmotionChange::Request> req,
        std::shared_ptr<homi_speech_interface::srv::RtcEmotionChange::Response> resp);
    bool getPcmPlayer (
        const std::shared_ptr<homi_speech_interface::srv::GetPcmPlayer::Request> req,
        std::shared_ptr<homi_speech_interface::srv::GetPcmPlayer::Response> resp);
    bool endPcmPlayer (
        const std::shared_ptr<homi_speech_interface::srv::EndPcmPlayer::Request> req,
        std::shared_ptr<homi_speech_interface::srv::EndPcmPlayer::Response> resp);
    bool playWav (
        const std::shared_ptr<homi_speech_interface::srv::PlayWav::Request> req,
        std::shared_ptr<homi_speech_interface::srv::PlayWav::Response> resp);
    bool getPlayerStatus (
        const std::shared_ptr<homi_speech_interface::srv::GetPlayerStatus::Request> req,
        std::shared_ptr<homi_speech_interface::srv::GetPlayerStatus::Response> resp);

    bool pcmIsPlaying;
    AlsaPlayer* _player;
    audio_buffer* _audioBuffer;
    std::string device, format, resPath;
    int channels, rate, bufferFrame, bufferSize;
    Json::FastWriter  json2str;      //Json转String
    
protected:
    rclcpp::Publisher<std_msgs::msg::String>::SharedPtr emotionPuber_;                              //声明表情切换发布者
    rclcpp::Subscription<homi_speech_interface::msg::PCMStream>::SharedPtr pcmSuber_;               //声明pcm音频订阅者
    rclcpp::Service<homi_speech_interface::srv::RtcEmotionChange>::SharedPtr emotionServer_;        //声明表情切换服务
    rclcpp::Service<homi_speech_interface::srv::GetPcmPlayer>::SharedPtr getPcmServer_;             //声明获取pcm播放权限服务
    rclcpp::Service<homi_speech_interface::srv::EndPcmPlayer>::SharedPtr endPcmServer_;             //声明释放pcm播放权限服务
    rclcpp::Service<homi_speech_interface::srv::PlayWav>::SharedPtr playWavServer_;                 //声明wav文件播放服务
    rclcpp::Service<homi_speech_interface::srv::GetPlayerStatus>::SharedPtr getStatusServer_;       //声明获取播放器状态服务    
};

#endif
