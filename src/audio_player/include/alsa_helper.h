#ifndef __ALSA_HELPER_H__
#define __ALSA_HELPER_H__
#include <rclcpp/rclcpp.hpp>
#include <stdint.h>
#include <alsa/asoundlib.h>
#include <sys/ipc.h>
#include <sys/shm.h>
#include <errno.h>
#include <string.h>

#define LOG "alsa_helper"

enum class AlsaHelperErrorCode : int 
{
    SUCCEED=0,
    IS_PREEMT=-1,
    INVAILD=-2,
    IO_ERROR=-3,

};
class AlsaHelperConfig
{
private:
    uint32_t _rateNear;
    uint32_t _bufferTime;
    snd_pcm_uframes_t _bufferSize;
    uint32_t _periodTime;
    snd_pcm_uframes_t _periodSize;
    snd_pcm_format_t _format;
    uint32_t _bitsPerFrame;
    uint32_t _channels;
    uint32_t _periodBytes;   
public:
    AlsaHelperConfig()
    {
        clear();
    }
    void clear()
    {
        _rateNear = 0;
        _bufferTime = 0;
        _bufferSize = 0;
        _periodTime = 0;
        _format = SND_PCM_FORMAT_UNKNOWN;
        _bitsPerFrame = 0;
        _channels = 0;
        _periodBytes = 0;
    }
    std::string toString()
    {
        return  "_rateNear: "+std::to_string(_rateNear)+
                " , _bufferTime: "+std::to_string(_bufferTime)+
                " , _bufferSize: "+std::to_string(_bufferSize)+
                " , _periodTime: "+std::to_string(_periodTime)+
                " , _periodSize: "+std::to_string(_periodSize)+
                " , _bitsPerFrame: "+std::to_string(_bitsPerFrame)+
                " , _channels: "+std::to_string(_channels)+
                " , _periodBytes: "+std::to_string(_periodBytes)+
                " , _format: "+std::string(snd_pcm_format_name(_format));
    }
    uint32_t getRateNear()
    {
        return _rateNear;
    }
    uint32_t getBufferTime()
    {
        return _bufferTime;
    }
    snd_pcm_uframes_t getBufferSize()
    {
        return _bufferSize;
    }
    uint32_t getPeriodTime()
    {
        return _periodTime;
    }
    snd_pcm_uframes_t getPeriodSize()
    {
        return _periodSize;
    }
    snd_pcm_format_t getFormat()
    {
        return _format;
    }
    uint32_t getBitPerFrame()
    {
        return _bitsPerFrame;
    }
    uint32_t getChannels()
    {
        return _channels;
    }
    uint32_t getPeriodBytes()
    {
        return _periodBytes;
    }
    snd_pcm_uframes_t sizeToFrame(size_t size)
    {
        return (size*8)/_bitsPerFrame;
    }
    int config(snd_pcm_t *pcmHandle,const char *format,const uint32_t channels,const uint32_t rate,
        const uint32_t nearBufferTime = 1000000,
        const snd_pcm_uframes_t startDelay = 200000,
        const snd_pcm_uframes_t stopDelay = 900000)
    {
        int err = 0;
        snd_pcm_uframes_t avail_min = 0;
        snd_pcm_uframes_t start_threshold = 0;
        snd_pcm_uframes_t stop_threshold = 0;
        if(pcmHandle==nullptr)
        {
            RCLCPP_ERROR(rclcpp::get_logger(LOG), "pcmHandle is nullptr");
            return static_cast<int>(AlsaHelperErrorCode::INVAILD);
        }
        /*硬件参数*/
        snd_pcm_hw_params_t *params;
        /*软件参数*/
        snd_pcm_sw_params_t *swparams;

        snd_pcm_hw_params_alloca(&params);
        snd_pcm_sw_params_alloca(&swparams);

        /*设置所有配置，相当与初始化硬件配置变量*/
        err = snd_pcm_hw_params_any(pcmHandle, params);
        if (err < 0) {
            RCLCPP_ERROR(rclcpp::get_logger(LOG), "set hw_params any fail,%s",snd_strerror(err));
            return static_cast<int>(AlsaHelperErrorCode::IO_ERROR);
        }
        /*下面都是设置硬件相关参数*/
        /*设置访问方式这里使用直接写入交错数据，即使用readi/writei*/
        err = snd_pcm_hw_params_set_access(pcmHandle, params,
                        SND_PCM_ACCESS_RW_INTERLEAVED);
        if (err < 0) {
            RCLCPP_ERROR(rclcpp::get_logger(LOG), "set access type fail,%s",snd_strerror(err));
            return static_cast<int>(AlsaHelperErrorCode::IO_ERROR);
        }
        _format = snd_pcm_format_value(format);
        if(_format==SND_PCM_FORMAT_UNKNOWN)
        {
            RCLCPP_ERROR(rclcpp::get_logger(LOG), "format is unknown");
            return static_cast<int>(AlsaHelperErrorCode::INVAILD);
        }
        err = snd_pcm_hw_params_set_format(pcmHandle, params, _format);
        if (err < 0) {
            RCLCPP_ERROR(rclcpp::get_logger(LOG), "set format fail,%s",snd_strerror(err));
            return static_cast<int>(AlsaHelperErrorCode::IO_ERROR);
        }
        _channels = channels;
        err = snd_pcm_hw_params_set_channels(pcmHandle, params, _channels);
        if (err < 0) {
            RCLCPP_ERROR(rclcpp::get_logger(LOG), "set channel fail,%s",snd_strerror(err));
            return static_cast<int>(AlsaHelperErrorCode::INVAILD);
        }
        /*set nearest rate, return the rate in ainfo->rate*/
        _rateNear = rate;
        err = snd_pcm_hw_params_set_rate_near(pcmHandle, params, &_rateNear, NULL);
        if (err < 0) {
            RCLCPP_ERROR(rclcpp::get_logger(LOG), "set rate near fail,%s",snd_strerror(err));
            return static_cast<int>(AlsaHelperErrorCode::IO_ERROR);
        }

        if (_rateNear > rate * 1.05 || _rateNear < rate * 0.95) {
            RCLCPP_ERROR(rclcpp::get_logger(LOG), "rate is not near: ,%u",_rateNear);
            return static_cast<int>(AlsaHelperErrorCode::IO_ERROR);
        }
        /*获取驱动的最大缓存时间参数, 驱动设置的128k, 就是dma ringbuffer的size*/
        err = snd_pcm_hw_params_get_buffer_time_max(params,
                    &_bufferTime, NULL);
        if (err < 0) {
            RCLCPP_ERROR(rclcpp::get_logger(LOG), "get buffer time max fail,%s",snd_strerror(err));
            return static_cast<int>(AlsaHelperErrorCode::IO_ERROR);
        }
        /*当输入参数 16k 1 channel 16bit时，buffer time 为4.096s*/
        if (_bufferTime > nearBufferTime)
            _bufferTime = nearBufferTime;
        /*读写单元长度 10ms*/
        _periodTime = 10000;

        err = snd_pcm_hw_params_set_period_time_near(pcmHandle, params,
                        &_periodTime, NULL);
        if (err < 0) {
            RCLCPP_ERROR(rclcpp::get_logger(LOG), "set period time near fail,%s",snd_strerror(err));
            return static_cast<int>(AlsaHelperErrorCode::IO_ERROR);
        }

        err = snd_pcm_hw_params_set_buffer_time_near(pcmHandle, params,
                        &_bufferTime, NULL);
        if (err < 0) {
            RCLCPP_ERROR(rclcpp::get_logger(LOG), "set buffer time near fail,%s",snd_strerror(err));
            return static_cast<int>(AlsaHelperErrorCode::IO_ERROR);
        }
        /*设置硬件参数，在这个末尾会将codec的音频路径中的功能单元上电*/
        err = snd_pcm_hw_params(pcmHandle, params);
        if (err < 0) {
            RCLCPP_ERROR(rclcpp::get_logger(LOG), "fail to install hw params,%s",snd_strerror(err));
            return static_cast<int>(AlsaHelperErrorCode::IO_ERROR);
        }
        /*以frame为单位*/
        snd_pcm_hw_params_get_period_size(params, &_periodSize, NULL);
        snd_pcm_hw_params_get_buffer_size(params, &_bufferSize);
        /*下面是软件参数*/
        /*获取软件参数*/
        snd_pcm_sw_params_current(pcmHandle, swparams);
        avail_min = _periodSize;
        /*最小可用数据帧数, 当buffer里边的数据小于这个值时，读写操作会阻塞等待*/
        err = snd_pcm_sw_params_set_avail_min(pcmHandle, swparams, avail_min);
        if (err < 0) {
            RCLCPP_ERROR(rclcpp::get_logger(LOG), "set avail min,%s",snd_strerror(err));
            return static_cast<int>(AlsaHelperErrorCode::IO_ERROR);
        }
        /*数据启动门限，当输入的读写数据大于这个值时，读写操作才会启动*/
        start_threshold = (double)_rateNear * startDelay /nearBufferTime;
        err = snd_pcm_sw_params_set_start_threshold(pcmHandle, swparams, start_threshold);
        if (err < 0) {
            RCLCPP_ERROR(rclcpp::get_logger(LOG), "set start threshold fail,%s",snd_strerror(err));
            return static_cast<int>(AlsaHelperErrorCode::IO_ERROR);
        }
        /*停止门限，有效数据达到这个值时，说明处理过程出现问题，会报overrun*/
        stop_threshold = (double)_rateNear * stopDelay /nearBufferTime;
        err = snd_pcm_sw_params_set_stop_threshold(pcmHandle, swparams, stop_threshold);
        if (err < 0) {
            RCLCPP_ERROR(rclcpp::get_logger(LOG), "set stop threshold fail,%s",snd_strerror(err));
            return static_cast<int>(AlsaHelperErrorCode::IO_ERROR);
        }
        /*设置软件参数*/
        err = snd_pcm_sw_params(pcmHandle, swparams);
        if (err < 0 ) {
            RCLCPP_ERROR(rclcpp::get_logger(LOG), "fail to install sw params,%s",snd_strerror(err));
            return static_cast<int>(AlsaHelperErrorCode::IO_ERROR);
        }

        err = snd_pcm_format_physical_width(_format);
        if(err<0)
        {
            RCLCPP_ERROR(rclcpp::get_logger(LOG), "snd_pcm_format_physical_width is fail,%s",snd_strerror(err));
            return static_cast<int>(AlsaHelperErrorCode::IO_ERROR);               
        }
        _bitsPerFrame = err * _channels;
        _periodBytes = _periodSize * _bitsPerFrame / 8;
        return static_cast<int>(AlsaHelperErrorCode::SUCCEED);           
    }
};

inline int getCardId(const std::string &sh, const std::string &deviceName)
{
    // 构建调用shell脚本的命令
    std::string command = "bash " + sh + " \"" + deviceName + "\"";

    // 调用shell脚本并获取输出
    FILE* pipe = popen(command.c_str(), "r");
    if (!pipe) {
        return -1;
    }
    char buffer[128];
    std::string result = "";
    while (!feof(pipe)) {
        if (fgets(buffer, 128, pipe) != NULL)
            result += buffer;
    }
    pclose(pipe);

    // 去除输出中的换行符
    result.erase(result.find_last_not_of("\n") + 1);

    RCLCPP_INFO(rclcpp::get_logger(LOG), "command = %s \n getCardId = %s",command.c_str(), result.c_str());

    if(result.empty())
    {
        RCLCPP_INFO(rclcpp::get_logger(LOG), "result.empty()");
        return -1;
    }
    int cardId = -1;
    try
    {
        cardId = std::stoi(result);
    }
    catch(const std::exception& e)
    {
        cardId = -1;
        RCLCPP_INFO(rclcpp::get_logger(LOG), "string to int error %s",e.what());
    }
    return cardId;
}

#endif
