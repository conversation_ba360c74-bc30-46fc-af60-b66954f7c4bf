import os
from ament_index_python.packages import get_package_share_directory
from launch import LaunchDescription
from launch_ros.actions import Node


def generate_launch_description():
    config = os.path.join(
        get_package_share_directory("audio_player"),
        "config",
        "param.yaml"
    )

    script = os.path.join(
        get_package_share_directory("audio_player"),
        "script",
        "get_playback_card.sh"
    )

    res = os.path.join(
        get_package_share_directory("audio_player"),
        "resource"
    )

    return LaunchDescription([
        Node(
            package="audio_player",
            namespace="audio_player",
            name="audio_player_node",
            executable="audio_player_node",
            parameters=[
                config,
                {"alsa_playout_sh_name": script,
                 "res_path": res}
            ]
        )
    ])