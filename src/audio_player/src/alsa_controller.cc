#include "alsa_controller.h"

#define CHUNKSIZE 1024
AlsaController::AlsaController(std::string name) : Node(name, rclcpp::NodeOptions().automatically_declare_parameters_from_overrides(true)), pcmIsPlaying(false), _player(nullptr), _audioBuffer(nullptr)
{
    device = this->getPlayoutDeviceName();
    if (!this->get_parameter("alsa_playout_format", format) ||
        !this->get_parameter("res_path", resPath) ||
        !this->get_parameter("alsa_playout_channels", channels) ||
        !this->get_parameter("alsa_playout_rate", rate) ||
        !this->get_parameter("alsa_playout_bufferFrame", bufferFrame) ||
        !this->get_parameter("alsa_playout_bufferSize", bufferSize)) {
        RCLCPP_ERROR(rclcpp::get_logger(LOGNAME), "AlsaController::AlsaController getparam (alsa_playout_format, alsa_playout_channels, alsa_playout_rate, alsa_playout_bufferFrame, alsa_playout_bufferSize or alsa_input_topics) fail");
        return;
    }
    
    emotionPuber_ = this->create_publisher<std_msgs::msg::String>("/robdog_control/changeExpression", 10);
    pcmSuber_ = this->create_subscription<homi_speech_interface::msg::PCMStream>("/cmcc_rtc/pcm_call", 10,
        std::bind(&AlsaController::audioCallback, this, std::placeholders::_1));
    emotionServer_ = this->create_service<homi_speech_interface::srv::RtcEmotionChange>("change_emotion",
        std::bind(&AlsaController::changeEmotion, this, std::placeholders::_1, std::placeholders::_2));
    getPcmServer_ = this->create_service<homi_speech_interface::srv::GetPcmPlayer>("get_pcm_player",
        std::bind(&AlsaController::getPcmPlayer, this, std::placeholders::_1, std::placeholders::_2));
    endPcmServer_ = this->create_service<homi_speech_interface::srv::EndPcmPlayer>("end_pcm_player",
        std::bind(&AlsaController::endPcmPlayer, this, std::placeholders::_1, std::placeholders::_2));
    playWavServer_ = this->create_service<homi_speech_interface::srv::PlayWav>("play_wav",
        std::bind(&AlsaController::playWav, this, std::placeholders::_1, std::placeholders::_2));
    getStatusServer_ = this->create_service<homi_speech_interface::srv::GetPlayerStatus>("get_player_status",
        std::bind(&AlsaController::getPlayerStatus, this, std::placeholders::_1, std::placeholders::_2));
}

AlsaController::~AlsaController() {
    this->uninit();
}

bool AlsaController::init()
{
    pcmIsPlaying = false;

    _audioBuffer = new audio_buffer();
    _audioBuffer->m_Totsize = bufferSize;
    _audioBuffer->m_Writeindex = 0;
    _audioBuffer->m_Readindex = 0;
    _audioBuffer->m_Audio = new char[_audioBuffer->m_Totsize];
    memset(_audioBuffer->m_Audio, 0, _audioBuffer->m_Totsize);
    
    _player = new AlsaPlayer(device, format, channels, rate, bufferFrame, _audioBuffer);

    if (!_player->init()) return false;
    _player->start();
    return true;
}

bool AlsaController::uninit()
{
    // 确保释放 _audioBuffer 内部的 m_Audio 数组
    if (_audioBuffer != nullptr && _audioBuffer->m_Audio != nullptr) {
        delete[] _audioBuffer->m_Audio;
        _audioBuffer->m_Audio = nullptr; // 防止悬挂指针
    }

    // 释放 _audioBuffer 本身
    if (_audioBuffer != nullptr) {
        delete _audioBuffer;
        _audioBuffer = nullptr; // 防止悬挂指针
    }

    // AlsaPlayer 有一个析构函数来清理其资源
    if (_player != nullptr) {
        delete _player;
        _player = nullptr; // 防止悬挂指针
    }

    return true;
}

std::string AlsaController::getPlayoutDeviceName()
{
    std::string playoutShPath;
    std::vector<std::string> playoutShParam_names;
    if (!this->get_parameter("alsa_playout_sh_name", playoutShPath) ||
        !this->get_parameter("alsa_playout_sh_param.name", playoutShParam_names)) {
        RCLCPP_ERROR(rclcpp::get_logger(LOGNAME), "AlsaController::getPlayoutDeviceName getparam (alsa_playback_sh_name or alsa_playback_sh_param.name) fail");
        return "default";
    }
    int cardId = -1;
    for (auto nameTmp:playoutShParam_names) {
        RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "AlsaController::getPlayoutDeviceName playoutDeviceName=%s",nameTmp.c_str());
        if(nameTmp=="default")
        {
            return "default";
        }
        cardId = getCardId(playoutShPath, nameTmp);
        if(cardId>=0)
        {
            return ("plughw:" + std::to_string(cardId) + ",0");
        }
    }
    RCLCPP_ERROR(rclcpp::get_logger(LOGNAME), "AlsaController::getPlayoutDeviceName no mathch playbackDeviceName");
    return "default";
}

bool AlsaController::changeEmotion (
    const std::shared_ptr<homi_speech_interface::srv::RtcEmotionChange::Request> req,
    std::shared_ptr<homi_speech_interface::srv::RtcEmotionChange::Response> resp) {
    Json::Value jsonMsg;
#if defined(YSC1_0)
    jsonMsg["path"] = this->resPath + "/video/" + "ysc1_0/" + req->str;
#elif defined(YSC1_1)
    jsonMsg["path"] = this->resPath + "/video/" + "ysc1_1/" + req->str;
#elif defined(UNITREE)
    jsonMsg["path"] = this->resPath + "/video/" + "ysc1_1/" + req->str;
#else
    jsonMsg["path"] = this->resPath + "/video/" + "ysc1_0/" + req->str;
#endif
    jsonMsg["cnt"] = req->cnt;
    std_msgs::msg::String msg;
    msg.data = json2str.write(jsonMsg);
    emotionPuber_->publish(msg);

    resp->status = true;
    return true;
}

bool AlsaController::getPcmPlayer(
    const std::shared_ptr<homi_speech_interface::srv::GetPcmPlayer::Request> req,
    std::shared_ptr<homi_speech_interface::srv::GetPcmPlayer::Response> resp) {
    if (this->init()) {
        RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "AlsaController::getPcmPlayer init success");
        resp->status = true;
        return true;
    } else {
        RCLCPP_ERROR(rclcpp::get_logger(LOGNAME), "AlsaController::getPcmPlayer init fail");
        resp->status = false;
        return false;
    }
}

bool AlsaController::endPcmPlayer(
    const std::shared_ptr<homi_speech_interface::srv::EndPcmPlayer::Request> req,
    std::shared_ptr<homi_speech_interface::srv::EndPcmPlayer::Response> resp) {
    if (this->uninit()) {
        RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "AlsaController::getPcmPlayer uninit success");
        resp->status = true;
        return true;
    } else {
        RCLCPP_ERROR(rclcpp::get_logger(LOGNAME), "AlsaController::getPcmPlayer uninit fail");
        resp->status = false;
        return false;
    }
}

bool AlsaController::playWav(
    const std::shared_ptr<homi_speech_interface::srv::PlayWav::Request> req,
    std::shared_ptr<homi_speech_interface::srv::PlayWav::Response> resp) {
    if (_audioBuffer == nullptr || _player == nullptr) {
        RCLCPP_ERROR(rclcpp::get_logger(LOGNAME), "AlsaController::playWav use getPcmPlayer first");
        resp->status = false;
        return false;
    }
    std::string filename = resPath + "/audio/" + req->str;
    std::ifstream file(filename.c_str(), std::ios::binary);
    if(!file) {
        RCLCPP_ERROR(rclcpp::get_logger(LOGNAME), "AlsaController::playWav get param filename not a file path: %s", filename.c_str());
        resp->status = false;
        return false;
    }

    WaveHeader header;
    file.read(reinterpret_cast<char*>(&header), sizeof(WaveHeader));
    if (file.fail()) {
        RCLCPP_ERROR(rclcpp::get_logger(LOGNAME), "AlsaController::playWav failed to read the header");
        file.close();
        resp->status = false;
        return false;
    }
    
    if (strncmp(header.riff, "RIFF", 4) != 0 ||
        strncmp(header.file_type, "WAVE", 4) != 0 ||
        strncmp(header.fmt, "fmt ", 4) != 0 ||
        header.audio_format != 1 || // PCM
        strncmp(header.data, "data", 4) != 0) {
        RCLCPP_ERROR(rclcpp::get_logger(LOGNAME), "AlsaController::playWav invalid WAV file");
        file.close();
        resp->status = false;
        return false;
    }
    
    while(!pcmIsPlaying) {
        int curlen = _audioBuffer->m_Totsize - (_audioBuffer->m_Writeindex + _audioBuffer->m_Totsize - _audioBuffer->m_Readindex) % _audioBuffer->m_Totsize;  //当前缓冲区空余长度
        while(CHUNKSIZE >= curlen) {
            RCLCPP_ERROR(rclcpp::get_logger(LOGNAME), "AlsaController::playWav _audioBuffer is not enough");
            usleep(10000);     //等待10ms
            curlen = _audioBuffer->m_Totsize - (_audioBuffer->m_Writeindex + _audioBuffer->m_Totsize - _audioBuffer->m_Readindex) % _audioBuffer->m_Totsize;  //当前缓冲区空余长度
        }
        if((_audioBuffer->m_Writeindex + CHUNKSIZE) > _audioBuffer->m_Totsize) {
            int overlen = _audioBuffer->m_Writeindex + CHUNKSIZE - _audioBuffer->m_Totsize;                  //超出长度部分
            int curlen = CHUNKSIZE - overlen;                                                           //可容纳长度
            file.read(_audioBuffer->m_Audio + _audioBuffer->m_Writeindex, curlen);                      //写入缓冲区
            auto bytesRead = file.gcount();
            _audioBuffer->m_Writeindex = (_audioBuffer->m_Writeindex + bytesRead) % _audioBuffer->m_Totsize;          //取余 写指针位置
            if(file.eof()) {
                RCLCPP_ERROR(rclcpp::get_logger(LOGNAME), "AlsaController::playWav file ptr is eof");
                file.close();
                resp->status = true;
                return true;
            }
            if(file.fail()) {
                RCLCPP_ERROR(rclcpp::get_logger(LOGNAME), "AlsaController::playWav failed to read audio data");
                file.close();
                resp->status = false;
                return false;
            }
            file.read(_audioBuffer->m_Audio + _audioBuffer->m_Writeindex, overlen);                     //覆盖原来
            bytesRead = file.gcount();
            _audioBuffer->m_Writeindex = (_audioBuffer->m_Writeindex + bytesRead) % _audioBuffer->m_Totsize;          //取余 写指针位置
            if(file.eof()) {
                RCLCPP_ERROR(rclcpp::get_logger(LOGNAME), "AlsaController::playWav file ptr is eof");
                file.close();
                resp->status = true;
                return true;
            }
            if(file.fail()) {
                RCLCPP_ERROR(rclcpp::get_logger(LOGNAME), "AlsaController::playWav failed to read audio data");
                file.close();
                resp->status = false;
                return false;
            }
        } else {
            file.read(_audioBuffer->m_Audio + _audioBuffer->m_Writeindex, CHUNKSIZE);                   //写入缓冲区
            auto bytesRead = file.gcount();
            _audioBuffer->m_Writeindex += bytesRead;
            if(file.eof()) {
                file.close();
                resp->status = true;
                return true;
            }
            if(file.fail()) {
                RCLCPP_ERROR(rclcpp::get_logger(LOGNAME), "AlsaController::playWav failed to read audio data");
                file.close();
                resp->status = false;
                return false;
            }
        }
    }
    RCLCPP_ERROR(rclcpp::get_logger(LOGNAME), "AlsaController::playWav permissions changed, not all files written");
    resp->status = false;
    return false;
}

bool AlsaController::getPlayerStatus(
    const std::shared_ptr<homi_speech_interface::srv::GetPlayerStatus::Request> req,
    std::shared_ptr<homi_speech_interface::srv::GetPlayerStatus::Response> resp) {
    resp->status = _player->getPlayerStatus();
    return true;
}

void AlsaController::audioCallback(const homi_speech_interface::msg::PCMStream::SharedPtr msg)
{
    if (_audioBuffer == nullptr || _player == nullptr) {
        return;
    }
    pcmIsPlaying = true;
    int curlen = _audioBuffer->m_Totsize - (_audioBuffer->m_Writeindex + _audioBuffer->m_Totsize - _audioBuffer->m_Readindex) % _audioBuffer->m_Totsize;  //当前缓冲区空余长度
    if(msg->data.size() >= curlen){   
        RCLCPP_INFO(rclcpp::get_logger(LOGNAME), "AlsaController::audioCallback _audioBuffer not enough");                                               //当前的缓冲区仅剩一帧可以写，此时要等待读取，暂时抛弃一部分数据
        return;
    }
    if((_audioBuffer->m_Writeindex + msg->data.size()) > _audioBuffer->m_Totsize) {                               //大于总长度
        int overlen = _audioBuffer->m_Writeindex + msg->data.size() - _audioBuffer->m_Totsize;                    //超出长度部分
        int curlen = msg->data.size() - overlen;                                                    //可容纳长度
        memcpy(_audioBuffer->m_Audio + _audioBuffer->m_Writeindex, &msg->data[0], curlen);          //写入缓冲区	  
        memcpy(_audioBuffer->m_Audio, &msg->data[0] + curlen, overlen);                             //覆盖原来
        _audioBuffer->m_Writeindex = (_audioBuffer->m_Writeindex + msg->data.size()) % _audioBuffer->m_Totsize;   //取余 写指针位置
    } else {                                                                                        //长度不超出
        memcpy(_audioBuffer->m_Audio + _audioBuffer->m_Writeindex, &msg->data[0], msg->data.size());                       //写入缓冲区
        _audioBuffer->m_Writeindex += msg->data.size();
    }
}
