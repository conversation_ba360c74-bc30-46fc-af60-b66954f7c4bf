#include "alsa_player.h"

AlsaPlayer::AlsaPlayer(std::string device, std::string format, int channels, int rate, int bufferFrame, audio_buffer* audioBuffer)
    : _device(device), _format(format), _channels(channels), _rate(rate), _bufferFrame(bufferFrame), _audioBuffer(audioBuffer), _isPlay(false)
{
    _audioPlayerConfig = AlsaHelperConfig();
}

AlsaPlayer::~AlsaPlayer()
{
    this->uninit();
}

bool AlsaPlayer::init()
{
    if (!_audioBuffer)
    {
        RCLCPP_ERROR(rclcpp::get_logger(LOG), "AlsaPlayer get audio buffer is nullptr\n");
        return false;
    }

    _playoutBuffer = new int8_t[_bufferFrame]();
    if (!_playoutBuffer)
    {
        RCLCPP_ERROR(rclcpp::get_logger(LOG), "AlsaPlayer create playout buffer false\n");
        return false;
    }
    
    _err = snd_pcm_open(&_audioPlayer, _device.c_str(), SND_PCM_STREAM_PLAYBACK, 0);
    if(_err < 0)
    {
        RCLCPP_ERROR(rclcpp::get_logger(LOG), "AlsaPlayer open audio device error:%s\n", snd_strerror(_err));
        return false;
    }

    _err = _audioPlayerConfig.config(_audioPlayer, _format.c_str(), _channels, _rate);
    if(_err < 0)
    {
        RCLCPP_ERROR(rclcpp::get_logger(LOG), "AlsaPlayer set audio device params error:%s\n", snd_strerror(_err));
        return false;
    }

    _frameSize = _audioPlayerConfig.sizeToFrame(_bufferFrame);

    return true;
}

bool AlsaPlayer::uninit()
{
    // 如果播放线程还在运行，就终止它
    if (_playThread.joinable()) {
        _isPlay = false;
        _playThread.join();
    }

    // 关闭 PCM 设备
    if (_audioPlayer != NULL) {
        snd_pcm_drain(_audioPlayer);
        snd_pcm_close(_audioPlayer);
    }
    
    // 释放播放缓冲区
    if(_playoutBuffer) {
        delete [] _playoutBuffer;
    }

    return true;
}

void AlsaPlayer::start()
{
    if(!_playThread.joinable()) {
        _isPlay = true;
        _playThread = std::thread(&AlsaPlayer::playFunction, this);
    }
}

void AlsaPlayer::playFunction()
{
    snd_pcm_start(_audioPlayer);
    snd_pcm_writei(_audioPlayer, _playoutBuffer, _frameSize);
    while(_isPlay){
        int curlen = (_audioBuffer->m_Writeindex + _audioBuffer->m_Totsize - _audioBuffer->m_Readindex) % _audioBuffer->m_Totsize;  //当前缓冲区数据长度
        if(_bufferFrame < curlen) {
            if((_audioBuffer->m_Readindex + _bufferFrame) > _audioBuffer->m_Totsize) {                                //取数据大于长度
                int overlen =  _audioBuffer->m_Readindex + _bufferFrame - _audioBuffer->m_Totsize;	                //超出部分长度
                int curlen = _bufferFrame - overlen;						                            //可用长度
                memcpy(_playoutBuffer, _audioBuffer->m_Audio+_audioBuffer->m_Readindex, curlen);
                memcpy(_playoutBuffer+curlen, _audioBuffer->m_Audio,overlen);
                _audioBuffer->m_Readindex = (_audioBuffer->m_Readindex + _bufferFrame) % _audioBuffer->m_Totsize;       //取余 读指针位置
            }else {
                memcpy(_playoutBuffer, _audioBuffer->m_Audio+_audioBuffer->m_Readindex, _bufferFrame);
                _audioBuffer->m_Readindex += _bufferFrame;
            }
            
            _err = snd_pcm_writei(_audioPlayer, _playoutBuffer, _frameSize);
            if(_err == -EPIPE){
                snd_pcm_prepare(_audioPlayer);
                snd_pcm_writei(_audioPlayer, _playoutBuffer, _frameSize);
            } else if (_err < 0) {
                RCLCPP_ERROR(rclcpp::get_logger(LOG), "AlsaPlayer::playFunction playout error: %s\n", snd_strerror(_err));
            }
        } else {
            usleep(1000); // 等待1ms
            continue;
        }
    }
}

// 查询播放设备状态
std::string AlsaPlayer::getPlayerStatus()
{
    if (_audioPlayer == nullptr) {
        return "Device not initialized";
    }

    // 获取播放器状态
    snd_pcm_status_t *status;
    snd_pcm_status_alloca(&status);
    snd_pcm_status(_audioPlayer, status);
    snd_pcm_state_t state = snd_pcm_state(_audioPlayer);
    switch (state) {
        case SND_PCM_STATE_OPEN:
            return "Open";
        case SND_PCM_STATE_SETUP:
            return "Setup";
        case SND_PCM_STATE_PREPARED:
            return "Prepared";
        case SND_PCM_STATE_RUNNING:
            return "Running";
        case SND_PCM_STATE_XRUN:
            return "XRun(underrun)";
        case SND_PCM_STATE_DRAINING:
            return "Draining";
        case SND_PCM_STATE_PAUSED:
            return "Paused";
        case SND_PCM_STATE_SUSPENDED:
            return "Suspended";
        case SND_PCM_STATE_DISCONNECTED:
            return "Disconnected";
        default:
            return "Unknown";
    }
}
