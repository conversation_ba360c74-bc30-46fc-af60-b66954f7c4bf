import rclpy
import time
from rclpy.node import Node
from homi_speech_interface.srv import PlayFile

class AudioTest(Node):
    def __init__(self):
        super().__init__('AudioTest')
        self.client = self.create_client(PlayFile, '/homi_audio_player/play_file_service')
        while not self.client.wait_for_service(timeout_sec=1.0):
            self.get_logger().info('服务未就绪，等待中...')
        self.get_logger().info('AudioTest 节点已启动')

    def send_request(self, file):
        self.req = PlayFile.Request()
        self.req.url = file
        future = self.client.call_async(self.req)
        rclpy.spin_until_future_complete(self, future)
        return future.result()

    def run(self, count):
        file = "/home/<USER>/resource/internet/internet_imnormal.wav"
        for i in range(0,count):
            print("[%d] send request %s ..."%(i,file))
            rst =  self.send_request(file)
            print("[%d] error_code %d"%(i,rst.error_code))
            time.sleep(10)
        fileNotExist = "/home/<USER>/resource/internet/fake__.wav"
        print("test send bad request %s ..."%(fileNotExist))
        rst =  self.send_request(fileNotExist)
        print("test fake wave error_code %d"%(rst.error_code))
            

def main(args=None):
    rclpy.init(args=args)
    atest = AudioTest()
    time.sleep(5)
    atest.run(2)
    rclpy.spin(atest)
    atest.destroy_node()
    rclpy.shutdown()



if __name__ == '__main__':
    main()