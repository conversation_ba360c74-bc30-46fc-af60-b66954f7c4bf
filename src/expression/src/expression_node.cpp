#include "rclcpp/rclcpp.hpp"
#include "std_msgs/msg/string.hpp"
#include <cstdlib>
#include <iostream>
#include <memory>
#include <thread>
#include <chrono>
#include <sys/types.h>
#include <signal.h>
#include <stdexcept>
#include <sys/wait.h>  
#include <dirent.h>
#include <vector>
#include <ctime>
#include <algorithm>
#include <sys/stat.h>
#include <unistd.h>
#include <jsoncpp/json/json.h>
#include <regex>
#include <thread>
#include <chrono>
#include <boost/filesystem.hpp>
#include <ament_index_cpp/get_package_share_directory.hpp>

const std::string resource_startup_video_path="/resource/video/startup/startup.mp4";
const std::string resource_default_video_path="/resource/video/default/default.mp4";
const std::string express_topic_name = "/robdog_control/changeExpression";
boost::filesystem::path folder_path;

class ExpressionNode : public rclcpp::Node
{
public:
    ExpressionNode() : Node("expression_node"), mpv_process(-1), is_user_bound_(false)
    {
        
        startup_vedio_ = getResourcePath(resource_startup_video_path);  // 默认开机路径
        default_vedio_ = getResourcePath(resource_default_video_path);  // 默认视频路径
        subscription_ = this->create_subscription<std_msgs::msg::String>(
            express_topic_name, 10, std::bind(&ExpressionNode::topic_callback, this, std::placeholders::_1));
        
        // 订阅网络状态主题
        network_status_subscription_ = this->create_subscription<std_msgs::msg::String>(
            "/network_status", 10, std::bind(&ExpressionNode::network_status_callback, this, std::placeholders::_1));
        
        start_video_playback();
        timer_ = this->create_wall_timer(std::chrono::seconds(2),std::bind(&ExpressionNode::monitor_mpv_process, this));
        signal(SIGCHLD, &ExpressionNode::handle_sigchld);
    }

    ~ExpressionNode()
    {
        RCLCPP_INFO(this->get_logger(), "going to kill ID is %d.",mpv_process);
        if (mpv_process) {
            kill(mpv_process, SIGTERM);  
            wait_for_process_to_exit();
        }
    }
    void monitor_mpv_process()
    {
        if (mpv_process <= 0 || kill(mpv_process, 0) != 0) {
            RCLCPP_ERROR(this->get_logger(), "mpv process has died. Restarting...");
            start_video_playback();
        }
    }
    std::string getResourcePath(const std::string &file_name){
        static const std::string package_share_dir = ament_index_cpp::get_package_share_directory("expression");
        auto pathstr= package_share_dir + file_name;
        RCLCPP_INFO(this->get_logger(), "file dir is : %s", pathstr.c_str());
        return pathstr;
    }

private:
    static void handle_sigchld(int sig)
    {
        int saved_errno = errno;
        while(waitpid(-1, NULL, WNOHANG) > 0);
        errno = saved_errno;
    }
    long getVideoDurationInMilliseconds(const std::string& filePath) {
        if (filePath.empty()) {
            std::cout<<"File path cannot be empty."<<std::endl;
            throw std::invalid_argument("File path cannot be empty.");
        }
        std::string command = "ffmpeg -i \"" + filePath + "\" 2>&1 | grep \"Duration\"";
        std::array<char, 128> buffer;
        std::string result;
        FILE* pipe = popen(command.c_str(), "r");
        if (!pipe) {
            std::cout<<"popen() failed!"<<std::endl;
        }
        while (fgets(buffer.data(), buffer.size(), pipe) != nullptr) {
            result += buffer.data();
        }
        pclose(pipe);
        std::regex durationRegex(R"(Duration: (\d+):(\d+):(\d+)\.(\d+))");
        std::smatch match;
        if (std::regex_search(result, match, durationRegex)) {
            int hours = std::stoi(match[1]);
            int minutes = std::stoi(match[2]);
            int seconds = std::stoi(match[3]);
            int milliseconds = std::stoi(match[4]);
            long totalMilliseconds = (hours * 3600 + minutes * 60 + seconds) * 1000 + milliseconds * 10;
            return totalMilliseconds;
        } else {
            throw std::runtime_error("Could not extract duration from ffmpeg output.");
        }
    }

    void topic_callback(const std_msgs::msg::String::SharedPtr msg)
    {
        RCLCPP_INFO(this->get_logger(), "Received message : %s", (msg->data).c_str());
        
        // 检查用户绑定状态
        if (!is_user_bound_) {
            RCLCPP_WARN(this->get_logger(), "Device not bound, ignoring expression update request");
            return;
        }
        
        Json::Reader reader;
        Json::Value value;
        reader.parse(msg->data, value);
        if (value.isNull()) {
            RCLCPP_INFO(this->get_logger(), "json parse error");
            return;
        }
        std::string video_filename_ = value["path"].asString();
        int cnt = value["cnt"].asInt();
        RCLCPP_INFO(this->get_logger(), "Received new video filename: %s", video_filename_.c_str());
        RCLCPP_INFO(this->get_logger(), "Received new video cnt: %d", cnt);
        if (is_directory(video_filename_)) {
            std::string random_video = get_random_video_from_directory(video_filename_);
            if (!random_video.empty()) {
                video_filename_ = random_video;
            } else {
                RCLCPP_ERROR(this->get_logger(), "No video files found in directory: %s", video_filename_.c_str());
                return;
            }
        }
        if (cnt){
            auto duration = getVideoDurationInMilliseconds(video_filename_);
            RCLCPP_INFO(this->get_logger(), "getVideoDurationInMilliseconds: %lld", duration);
            load_new_video(video_filename_);
            RCLCPP_INFO(this->get_logger(), "load_new_video : %s", video_filename_.c_str());
            sleep(cnt*duration/1000);
            load_new_video(default_vedio_);
            RCLCPP_INFO(this->get_logger(), "load default vedio : %s", default_vedio_.c_str());
        }
        else
            load_new_video(video_filename_);
    }

    void network_status_callback(const std_msgs::msg::String::SharedPtr msg)
    {
        RCLCPP_INFO(this->get_logger(), "Received network status: %s", (msg->data).c_str());
        Json::Reader reader;
        Json::Value network_status;
        if (reader.parse(msg->data, network_status)) {
            // 解析网络状态信息
            std::string wifi_state = network_status.get("wifiState", "").asString();
            std::string mobile_state = network_status.get("mobileDataState", "").asString();
            std::string is_wifi_connect = network_status.get("isWifiConnect", "").asString();
            std::string is_internet_connect = network_status.get("isInternetConnect", "").asString();
            std::string user_bind = network_status.get("userBind", "").asString();
            
            // 更新绑定状态
            bool previous_bind_status = is_user_bound_;
            is_user_bound_ = (user_bind == "true");
            
            if (previous_bind_status != is_user_bound_) {
                RCLCPP_INFO(this->get_logger(), "User bind status changed: %s -> %s", 
                           previous_bind_status ? "true" : "false", 
                           is_user_bound_ ? "true" : "false");
            }
            
            RCLCPP_INFO(this->get_logger(), "Network Status - WiFi: %s, Mobile: %s, WiFi Connected: %s, Internet: %s, User Bind: %s", 
                        wifi_state.c_str(), mobile_state.c_str(), is_wifi_connect.c_str(), 
                        is_internet_connect.c_str(), user_bind.c_str());
        } else {
            RCLCPP_ERROR(this->get_logger(), "Failed to parse network status JSON");
        }
    }

    void start_video_playback()
    {
        try {
            setenv("DISPLAY", ":0", 1);
            mpv_process = fork();
            if (mpv_process == -1) {
                RCLCPP_INFO(this->get_logger(), "Failed to fork process");
                throw std::runtime_error("Failed to fork process");
            } else if (mpv_process == 0) {
                execl("/bin/sh", "sh", "-c",("mpv  -vo=drm  --hwdec=vaapi --cache=yes  --vf=fps=15    --loop-file=inf --fs --ontop --input-ipc-server=/tmp/mpv-socket \"" + startup_vedio_ + "\"").c_str(),nullptr);
                RCLCPP_ERROR(this->get_logger(),"Exec failed");
                exit(EXIT_FAILURE); 
            } else {
                RCLCPP_INFO(this->get_logger(), "Started mpv with PID: %d.", mpv_process);
                RCLCPP_INFO(this->get_logger(), "Started video playback with mpv.");
            }        
        } catch (const std::exception &e) {
            RCLCPP_ERROR(this->get_logger(), "Error starting video playback: %s", e.what());
        }
    }

    void load_new_video(const std::string &filename)
    {
        if (!boost::filesystem::exists(filename)) {
            RCLCPP_ERROR(this->get_logger(), "File not found: %s", filename.c_str());
            return;
        }
        try {
            std::string command = "echo '{\"command\": [\"loadfile\", \"" + filename + "\", \"replace\"]}' | socat - /tmp/mpv-socket";
            int result = std::system(command.c_str());

            if (result != 0) {
                throw std::runtime_error("Failed to send loadfile command to mpv");
            }

            RCLCPP_INFO(this->get_logger(), "Sent loadfile command to mpv to load %s", filename.c_str());
        } catch (const std::exception &e) {
            RCLCPP_ERROR(this->get_logger(), "Error loading new video: %s", e.what());
        }
    }

    void wait_for_process_to_exit()
    {
        int status;
        waitpid(mpv_process, &status, 0);
        if (WIFEXITED(status)) {
            RCLCPP_INFO(this->get_logger(), "mpv process terminated normally.");
        } else {
            RCLCPP_ERROR(this->get_logger(), "mpv process terminated abnormally.");
        }
    }
    bool is_directory(const std::string &path)
    {
        struct stat statbuf;
        if (stat(path.c_str(), &statbuf) != 0) {
            return false;
        }
        return S_ISDIR(statbuf.st_mode);
    }

    std::string get_random_video_from_directory(const std::string &directory)
    {
        DIR *dir = opendir(directory.c_str());
        if (dir == nullptr) {
            std::cerr << "Failed to open directory: " << directory << std::endl;
            return "";
        }
        struct DirectoryCloser {
            void operator()(DIR* dir) const {
                if (dir != nullptr) closedir(dir);
            }
        };
        std::unique_ptr<DIR, DirectoryCloser> safe_dir(dir);
        std::vector<std::string> video_files;
        struct dirent *entry;
        while ((entry = readdir(safe_dir.get())) != nullptr) {
            if (entry->d_name[0] == '.' && (entry->d_name[1] == '\0' || (entry->d_name[1] == '.' && entry->d_name[2] == '\0'))) {
                continue;
            }
            std::string full_path = directory + "/" + entry->d_name;
            struct stat file_info;
            if (stat(full_path.c_str(), &file_info) == 0 && S_ISREG(file_info.st_mode)) {
                video_files.push_back(full_path);
            }
        }
        if (video_files.empty()) {
            std::cerr << "No files found in directory: " << directory << std::endl;
            return "";
        }
        static bool seeded = false;
        if (!seeded) {
            srand(static_cast<unsigned>(time(nullptr)));
            seeded = true;
        }
        int random_index = rand() % video_files.size();
        std::cout << "Selected file: " << video_files[random_index] << std::endl;
        return video_files[random_index];
    }
    rclcpp::Subscription<std_msgs::msg::String>::SharedPtr subscription_;
    rclcpp::Subscription<std_msgs::msg::String>::SharedPtr network_status_subscription_;
    pid_t mpv_process;
    std::string startup_vedio_;
    std::string default_vedio_;
    rclcpp::TimerBase::SharedPtr timer_;
    bool is_user_bound_;  // 用户绑定状态
public:
    std::thread monitor_thread_;
    
};

int main(int argc, char *argv[])
{
    rclcpp::init(argc, argv);
    auto node = std::make_shared<ExpressionNode>();
    // node->monitor_thread_ = std::thread(&ExpressionNode::monitor_mpv_process, node.get());
    rclcpp::spin(node);
    // node->monitor_thread_.join();    
    rclcpp::shutdown();
    return 0;
}
