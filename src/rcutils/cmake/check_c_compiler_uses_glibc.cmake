# Copyright 2020 Open Source Robotics Foundation, Inc.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

#
# Checks if the C compiler uses glibc.
#
# @private
#
function(check_c_compiler_uses_glibc result_variable)
  include(CheckCSourceCompiles)

  set(GLIBC_TEST_CODE [====[
    #include <execinfo.h>

    int main() {
      void * buffer[1];
      int size = sizeof(buffer) / sizeof(void *);
      backtrace(&buffer, size);
      return 0;
    }
  ]====])

  check_c_source_compiles("${GLIBC_TEST_CODE}" ${result_variable})
  set(${result_variable} ${result_variable} PARENT_SCOPE)
endfunction()
