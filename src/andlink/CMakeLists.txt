cmake_minimum_required(VERSION 3.8)
project(andlink)

if("${CMAKE_BUILD_TYPE}" STREQUAL "Release")
  set(CMAKE_C_FLAGS_RELEASE "${CMAKE_C_FLAGS_RELEASE} -s")
  set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} -s")
endif()

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# find dependencies
find_package(ament_cmake REQUIRED)
find_package(rclcpp REQUIRED)
find_package(std_msgs REQUIRED) # 添加对 std_msgs 的依赖
find_package(homi_speech_interface REQUIRED)

if(BUILD_TESTING)
  find_package(ament_lint_auto REQUIRED)
  # the following line skips the linter which checks for copyrights
  # comment the line when a copyright and license is added to all source files
  set(ament_cmake_copyright_FOUND TRUE)
  # the following line skips cpplint (only works in a git repo)
  # comment the line when this package is in a git repo and when
  # a copyright and license is added to all source files
  set(ament_cmake_cpplint_FOUND TRUE)
  ament_lint_auto_find_test_dependencies()
endif()

# 添加 include/andlink 为头文件包含路径
include_directories(${CMAKE_SOURCE_DIR}/include/andlink)
include_directories(${CMAKE_SOURCE_DIR}/include)

# 添加 src/adapt 文件夹下的所有源文件
file(GLOB_RECURSE ADAPT_SOURCES src/adapt/*.c)

# 添加可执行文件
add_executable(andlink_node src/main.cpp src/andlink_node.cpp ${ADAPT_SOURCES})
ament_target_dependencies(andlink_node rclcpp std_msgs homi_speech_interface) # 添加 std_msgs homi_speech_interface 依赖

if(CMAKE_HOST_SYSTEM_PROCESSOR MATCHES "x86_64")
    message("x86-64")
    target_link_libraries(andlink_node pthread m dl rt  ${CMAKE_SOURCE_DIR}/lib/x86_64/libandlink.a) # 添加 libandlink.a 链接
elseif(CMAKE_HOST_SYSTEM_PROCESSOR MATCHES "aarch64")
    message("aarch64")
    target_link_libraries(andlink_node pthread m dl rt  ${CMAKE_SOURCE_DIR}/lib/aarch64/libandlink.a) # 添加 libandlink.a 链接
endif()
# 链接依赖库

# 设置编译选项
target_compile_options(andlink_node PRIVATE -Os -fPIC -Wno-system-headers -Wno-unused-result -rdynamic -funwind-tables -ffunction-sections -g -Wno-variadic-macros)

# 设置链接选项
# target_link_options(my_node PRIVATE -Wl,-rpath ${DEMO_ROOT_DIR}/lib)

# 安装可执行文件
install(TARGETS
  andlink_node
  DESTINATION lib/${PROJECT_NAME})

ament_package()
