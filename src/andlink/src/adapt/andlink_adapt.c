/*
 *
 * 文件名称：andlink_adapt.c
 * 说 明:厂商集成andlink SDK需要适配的通用接口信息,由厂商补充实现.
 * 凡是标注了TODO的的函数都需要厂商重写或修改.
 */

/*
===================================== 强烈注意 start =====================================
一、若要使用make,对demo程序进行二次编译,需要指定以下1个符号.
APP_USING_ANDLINK_CJSON             是否使用SDK封装的json库函数.
这个符号的含义可以搜索demo程序实例代码.

二、使用andlink SDK集成适配的过程中,有任何问题可以查阅以下文档:
2.1、Andlink SDK(Linux)接入指南
https://docs.qq.com/doc/DUERlaEJIS0RwYXNH

2.2、Andlink-SDK(linux)使用常见问题(FAQ)
https://docs.qq.com/doc/DUHBpUVljbXhteHJr

===================================== 强烈注意 end   =====================================
*/
#include "andlink_adapt.h"

// andlink demo 示例程序 版本号
#define ADL_DEMO_MAJOR "2.4"
#define ADL_DEMO_ST    ".0demo241128"

/*
注意：下列定义的宏中，
1.DEVICE_TYPE、PRODUCTION_TOKEN、ANDLINK_TOKEN、DEVICE_VENDOR_NAME 是产品ID及一型一密的一对令牌;
这些信息,在移动爱家生态平台上(https://open.home.10086.cn )创建产品后，会自动分配.

2.DEVICE_REAL_SN、DEVICE_REAL_CMEI、DEVICE_SEC_LICENSE 是三码一密信息;
这些信息,需要在移动爱家生态平台的串码管理模块进行申请.
*/

// 以下FAC_DEVICE_INFO中的信息是样例数据,厂商集成适配时,需要改为厂商设备自身的信息
#define FAC_DEVICE_INFO
#ifdef FAC_DEVICE_INFO
// 1.[创建产品后,移动爱家生态平台自动分配]产品类型信息
#define DEVICE_TYPE        "2320647"           // 产品类型ID;
#define PRODUCTION_TOKEN   "PXclNa5N6PmeVzZV" // 产品验证令牌;
#define ANDLINK_TOKEN      "reF7fz1mT585oHY5" // 平台验证令牌;
#define DEVICE_VENDOR_NAME "test"             // 厂商名称(厂商编码)

// 2.设备自身MAC信息
#define DEVICE_WLAN_MAC "FF0822A08E00" // 设备无线mac
#define DEVICE_REAL_MAC "000C2999D25D" // 设备机身铭牌mac

// 3.[须向移动爱家生态平台申请]设备三码一密信息
#define DEVICE_REAL_SN     "11830004229212345670000024"                                                // 设备sn
#define DEVICE_REAL_CMEI   "6212345670000024"                                                          // 设备cmei
#define DEVICE_SEC_LICENSE "yFwk7hwV" // 72位设备一机一密秘钥
// #define DEVICE_SEC_LICENSE "026bbb404a08" // 12位

// 设备自身唯一标识,建议填sn,最终andlink平台以此分配andlink体系的设备ID(deviceId),格式为CMCC-$DEVICE_TYPE-$FAC_DEVICE_ID
#define FAC_DEVICE_ID DEVICE_REAL_SN

// 4.设备版本信息
#define DEVICE_FMWARE_VER   "v1.0" // 设备固件版本号
#define DEVICE_SOFTWARE_VER "v2.0" // 设备软件版本号

#endif // FAC_DEVICE_INFO

// andlink相关的配置文件存储路径,注意需要断电不丢失
#define CFG_FILE_PATH "/etc/cmcc_robot"

// OTA 分片下载使能
#define OTA_FRAG_DOWNLOAD_ENABLE 0
// OTA每个分片的大小,单位字节
#define OTA_FRAG_SIZE (2 * 1024 * 1024)
// OTA文件存储路径
#define OTA_FILE_PATH "/tmp"
// OTA文件存储的文件名
#define OTA_FILE_NAME "ota.zip"

// 保存设备基本信息
typedef struct
{
    CFG_NET_MODE_e netMode;
    // 以下空间SDK调用者可以根据实际情况进行调整
    char deviceVendor[32];
    char deviceType[MAX_DEVICE_TYPE_LEN + 1];
    char id[MAX_SN_LEN + 1];
    char andlinkToken[MAX_ANDLINK_TOKEN_LEN + 1];
    char productToken[MAX_PRODUCT_TOKEN_LEN + 1];
    char firmWareVersion[64];
    char softWareVersion[64];
    char cfgPath[64];

    char ip[MAX_IPSTR_LEN + 1];
    char brdAddr[MAX_IPSTR_LEN + 1];
    char mac[MAX_MAC_LEN + 1];
    char wlanMac[MAX_MAC_LEN + 1];
    char sn[MAX_SN_LEN + 1];
    char cmei[MAX_CMEI_LEN + 1];
    char license[MAX_AUTH_KEY_LEN + 1];
    int andlinkState;
} DMEO_DEVICE_INFO_T;

// 设备基本信息
static DMEO_DEVICE_INFO_T s_deviceInfo = {
    .netMode = NETWOKR_MODE_WIFI,
    .deviceVendor = DEVICE_VENDOR_NAME,
    .deviceType = DEVICE_TYPE,
    .id = FAC_DEVICE_ID,
    .andlinkToken = ANDLINK_TOKEN,
    .productToken = PRODUCTION_TOKEN,
    .firmWareVersion = DEVICE_FMWARE_VER,
    .softWareVersion = DEVICE_SOFTWARE_VER,
    .cfgPath = CFG_FILE_PATH,
    .ip = "",
    .brdAddr = "",
    .mac = DEVICE_REAL_MAC,
    .wlanMac = DEVICE_WLAN_MAC,
    .sn = DEVICE_REAL_SN,
    .cmei = DEVICE_REAL_CMEI,
    .license = DEVICE_SEC_LICENSE
};

/************************************************************************
Description: 获取sdk demo的版本号和编译时间
Input: None
Output: None
Return: sdk demo程序的版本号及编译时间
Others:
************************************************************************/
char *adlDemoVersionGet()
{
    static char s_demoSWVersion[64] = { 0 };
    app_snprintf_s(s_demoSWVersion, sizeof(s_demoSWVersion) - 1, "%s%s; "
                                                                 "Compiled timestamp: %s %s",
                   ADL_DEMO_MAJOR, ADL_DEMO_ST, __DATE__, __TIME__);
    return s_demoSWVersion;
}

/************************************************************************
Description:    设置设备的自身标识
Input:          char *mac
Output:         None
Return:         成功:0, 失败:-1
Others:         IoT平台以此生成设备唯一标识,用于在APP展示
************************************************************************/
int demo_setDevUniqueId(char *id)
{
    DEMO_DBG_PRINT("dev set id[%s]\n", id);
    memset(s_deviceInfo.id, 0, sizeof(s_deviceInfo.id));
    if (app_strlen(id))
    {
        app_strncpy_s(s_deviceInfo.id, sizeof(s_deviceInfo.id) - 1, id, app_strlen(id));
    }
    return 0;
}

/************************************************************************
Description:    获取设备的自身标识
Others:
************************************************************************/
char *demo_getDevUniqueId()
{
    return s_deviceInfo.id;
}
/************************************************************************
Description:    获取设备的有线网卡mac地址
Input:          None
Output:         None
Return:         mac地址字符串
Others:
************************************************************************/
char *demo_getMac()
{
    return s_deviceInfo.mac;
}

/************************************************************************
Description:    获取设备的无线网卡mac地址
Input:          None
Output:         None
Return:         mac地址字符串
Others:
************************************************************************/
char *demo_getWlanMac()
{
    return s_deviceInfo.wlanMac;
}

/************************************************************************
Description:    获取设备的sn地址
Input:          None
Output:         None
Return:         sn地址字符串
Others:
************************************************************************/
char *demo_getDeviceSn()
{
    return s_deviceInfo.sn;
}

/************************************************************************
Description:    获取设备的cmei地址
Input:          None
Output:         None
Return:         cmei地址字符串
Others:
************************************************************************/
char *demo_getDeviceCmei()
{
    return s_deviceInfo.cmei;
}

/************************************************************************
Description: 获取一机一密的预置秘钥
Input: None
Output: None
Return: 预置秘钥字符串
Others:
************************************************************************/
char *demo_getDeviceLicense()
{
    return s_deviceInfo.license;
}

/************************************************************************
Description:    获取设备的厂商名称
Others:
************************************************************************/
char *demo_getDeviceVendor()
{
    return s_deviceInfo.deviceVendor;
}
/************************************************************************
Description:    获取设备的产品类型ID
Others:
************************************************************************/
char *demo_getDeviceType()
{
    return s_deviceInfo.deviceType;
}
/************************************************************************
Description:    获取设备的Andlink令牌
Others:
************************************************************************/
char *demo_getAndlinkToken()
{
    return s_deviceInfo.andlinkToken;
}
/************************************************************************
Description:    获取设备的产品令牌
Others:
************************************************************************/
char *demo_getProductToken()
{
    return s_deviceInfo.productToken;
}
/************************************************************************
Description:    获取设备的固件版本号
Others:
************************************************************************/
char *demo_getFirmWareVersion()
{
    return s_deviceInfo.firmWareVersion;
}
/************************************************************************
Description:    获取设备的软件版本号
Others:
************************************************************************/
char *demo_getSoftWareVersion()
{
    return s_deviceInfo.softWareVersion;
}
/************************************************************************
Description:    获取设备的andlink配置文件路径
Others:
************************************************************************/
char *demo_getAndlinkCfgPath()
{
    return s_deviceInfo.cfgPath;
}

/************************************************************************
Description: 获取andlink的接入状态
Input: None
Output: None
Return: 状态
Others:
************************************************************************/
int demo_get_andlink_state()
{
    return s_deviceInfo.andlinkState;
}

/************************************************************************
Description:    SDK实时通知设备当前的andlink连接状态,设备可以根据需要使用;
Input:          ADL_DEV_STATE_e state
Output:         None
Return:         成功:0, 失败:-1
Others:
************************************************************************/
int demo_set_andlink_status_callback(ADL_DEV_STATE_e state)
{
    DEMO_DBG_PRINT("[demo]set_andlink_status_callback,state=%d\r\n", state);

    s_deviceInfo.andlinkState = state;

    switch (demo_get_andlink_state())
    {
    case ADL_BOOTSTRAP:
        DEMO_DBG_PRINT("========demo andlink bootstrap!\r\n");
        break;

    case ADL_BOOTSTRAP_SUC:
        DEMO_DBG_PRINT("========demo andlink bootstrap SUC!\r\n");
        break;

    case ADL_BOOT:
        DEMO_DBG_PRINT("========demo andlink boot!\r\n");
        break;

    case ADL_BOOT_SUC:
        DEMO_DBG_PRINT("========demo andlink boot SUC!\r\n");
        break;

    case ADL_ONLINE:
        DEMO_DBG_PRINT("========demo andlink online!\r\n");
        break;

    case ADL_BOUND:
        DEMO_DBG_PRINT("========demo andlink bound!\r\n");
        break;
    default:
        break;
    }

    // TODO
    // 厂商根据需要使用
    return 0;
}

/************************************************************************
Description:    厂商调用设置IP地址和广播地址
Input:          char *ip, char *brdAddr
Output:         None
Return:         成功:0, 失败:-1
Others:
************************************************************************/
int demo_set_device_ipaddr_for_callback(char *ip, char *brdAddr)
{
    DEMO_DBG_PRINT("dev setDeviceIpAddr[%s, %s]\n", ip, brdAddr);

    memset(s_deviceInfo.ip, 0, sizeof(s_deviceInfo.ip) - 1);
    memset(s_deviceInfo.brdAddr, 0, sizeof(s_deviceInfo.brdAddr) - 1);

    if (ip && app_strlen(ip))
    {
        app_memcpy_s(s_deviceInfo.ip, sizeof(s_deviceInfo.ip) - 1, ip, app_strlen(ip));
    }

    if (brdAddr && app_strlen(brdAddr))
    {
        app_memcpy_s(s_deviceInfo.brdAddr, sizeof(s_deviceInfo.brdAddr) - 1, brdAddr, app_strlen(brdAddr));
    }

    return 0;
}

/************************************************************************
Description: SDK调用,获取设备IP信息
Input: char *outip, char *outbrdAddr
Output: char *outip, char *outbrdAddr
Return: 成功:0, 失败:-1
Others: SDK以能否获取到IP来判断设备是否已经联网,注意入参可能是NULL;即SDK会单独获取IP或广播地址
************************************************************************/
int demo_get_device_ipaddr_callback(char *outip, char *outbrdAddr)
{
    int ret = -1;
    if (outip)
    {
        if (app_strlen(s_deviceInfo.ip))
        {
            app_memcpy_s(outip, sizeof(s_deviceInfo.ip) - 1, s_deviceInfo.ip, app_strlen(s_deviceInfo.ip));
            ret = 0;
        }
    }

    if (outbrdAddr)
    {
        if (app_strlen(s_deviceInfo.brdAddr))
        {
            app_memcpy_s(outbrdAddr, sizeof(s_deviceInfo.brdAddr) - 1, s_deviceInfo.brdAddr, app_strlen(s_deviceInfo.brdAddr));
            ret = 0;
        }
    }
    return ret;
}

/************************************************************************
Description:    SDK调用,复位设备IP
Input:          None
Output:         None
Return:         成功:0, 失败:-1
Others:
************************************************************************/
int demo_reset_device_Ipaddr_callback()
{
    DEMO_DBG_PRINT("[demo]reset_device_Ipaddr\r\n");
    return demo_set_device_ipaddr_for_callback("", "");
}

/************************************************************************
Description:    SDK实时通知设备执行某些扩展功能;
Input:          ADL_DEV_EXTFUNC_NOTIFY_e type
Output:         None
Return:         成功:0, 失败:-1
Others:
************************************************************************/
int demo_set_extfunc_notify_callback(ADL_DEV_EXTFUNC_NOTIFY_e type)
{
    int ret = 0;
    switch (type)
    {
    case ADL_LOG_SYNC:
        // 进行日志同步,可选
        break;
    default:
        ret = -1;
        break;
    }
    return ret;
}
/************************************************************************
Description:    设备语音提示通知
Input:          ADL_DEV_VOICE_NOTIFY_e type
Output:         None
Return:         成功:0, 失败:-1
Others:         可选
************************************************************************/
int demo_set_voice_notify_callback(ADL_DEV_VOICE_NOTIFY_e type, char *msg)
{
    // TODO
    switch (type)
    {
    case ADL_NET_FLASHLINK_START_CONNECTING:
        break;

    case ADL_NET_FLASHLINK_GUIDE_AP_FAILED:
        break;

    case ADL_NET_FLASHLINK_GUIDE_AP_CONN_SUC:
        break;

    case ADL_NET_FLASHLINK_WORK_AP_CONN_FAILED:
        break;

    case ADL_NET_FLASHLINK_WORK_AP_CONN_SUC:
        // TODO
        break;
    default:
        break;
    }
    return 0;
}

/************************************************************************
Description: SDK调用,获取设备的扩展信息
Input:     char *childDeviceId, cJSON *root
Output:    cJSON *root
Return:    成功:0, 失败:-1
Others:    add 22.03.03 by wuhao  V1.6.1版本开始,用于替换getDevExtInfo接口.
childDevId为NULL表示父设备,否则表示子设备
************************************************************************/
int demo_get_extInfo_callback(char *childDeviceId, cJSON *root)
{
    if (NULL == root)
    {
        return -1;
    }

    if (childDeviceId)
    {
        // 查询子设备的扩展信息
    }
    else
    {
        // 查询设备的扩展信息
    }

    // 1.添加三码一密信息(cmei、mac、sn、一机一密密钥license)
    adl_cJSON_AddStringToObject(root, "cmei", demo_getDeviceCmei());       // 必填,设备唯一标识
    adl_cJSON_AddStringToObject(root, "mac", demo_getMac());               // 必填,设备机身铭牌MAC,全大写不带冒号;
    adl_cJSON_AddStringToObject(root, "wlanMac", demo_getWlanMac());       // 必填,设备无线网卡MAC,全大写不带冒号;不存在填"NONE"
    adl_cJSON_AddStringToObject(root, "sn", demo_getDeviceSn());           // 必填,设备真实SN
    adl_cJSON_AddStringToObject(root, "license", demo_getDeviceLicense()); // 必填,设备一机一密安全认证的预置密钥,即物料清单里的登录密码

    // 2.添加保留字段及设备生产日期信息及后续扩展字段
    cJSON *xdataObj = NULL;
    adl_cJSON_AddItemToObject(root, "XData", xdataObj = adl_cJSON_CreateObject());
    {
        adl_cJSON_AddStringToObject(xdataObj, "reserve", "");         // 可选,厂商特殊标记字段
        adl_cJSON_AddStringToObject(xdataObj, "manuDate", "2019-06"); // 必填,设备生产日期,格式为:年-月
    }
    return 0;
}

/************************************************************************
Description:    DM上报信息获取接口
Input:          char *childDeviceId, cJSON *root
Output:         cJSON *root
Return:         成功:0, 失败:-1
Others:         childDevId为NULL表示父设备,否则表示子设备
************************************************************************/
int demo_get_dmInfo_callback(char *childDeviceId, cJSON *root)
{
    if (NULL == root)
    {
        return -1;
    }

    if (childDeviceId)
    {
        // 查询子设备的DM信息
    }
    else
    {
        // 查询设备的DM信息
    }

    DEMO_DBG_PRINT("[demo]start demo_get_dmInfo_callback\r\n");

    // 1.添加cmei,真实mac,sn,操作系统信息
    adl_cJSON_AddStringToObject(root, "cmei", demo_getDeviceCmei()); // 必填,设备唯一标识
    adl_cJSON_AddStringToObject(root, "mac", demo_getMac());         // 必填,设备机身铭牌MAC,全大写不带冒号;
    adl_cJSON_AddStringToObject(root, "sn", demo_getDeviceSn());     // 必填,设备真实SN
    adl_cJSON_AddStringToObject(root, "OS", "arm-linux32 2.6.18");   // 必填,操作系统(包含版本号)
    // adl_cJSON_AddStringToObject(root, "stbId", "11110");        // 机顶盒ID，机顶盒设备必填

    // 2.添加DM设备信息
    adl_cJSON_AddStringToObject(root, "cpuModel", "Allwinner-A40i"); // 必填,不存在填NONE,处理器型号;update by 220909
    adl_cJSON_AddStringToObject(root, "romStorageSize", "128MB");    // 必填,不存在填NONE,设备的存储总容量（ROM）大小，与工信部登记终端ROM信息一致;update by 220909
    adl_cJSON_AddStringToObject(root, "ramStorageSize", "64MB");     // 必填,不存在填NONE,设备的内存总容量（RAM）大小，与工信部登记争端RAM信息一致;update by 220909

    // 必填,网络类型.规则:RJ45, Wi-Fi, 5G, 4G, 3G, NB, ZigBee等,若多种连接方式,使用/分隔,比如Wi-Fi/RJ45
    adl_cJSON_AddStringToObject(root, "networkType", "Wi-Fi");

    // 设备当前位置，前面表示经度、后面表示维度可选（支持定位设备必填）,第三个参数取值:1：GPS; 2：北斗; 4：伽利略; 8：格洛纳斯; 16:基站定位; 32：WiFi定位;  协同定位：GPS+北斗,值为1|2=3
    adl_cJSON_AddStringToObject(root, "locationInfo", "123.52958679200002,25.77701556036132,1"); // 必填,不存在填NONE;update by 220909

    adl_cJSON_AddStringToObject(root, "deviceVendor", DEVICE_VENDOR_NAME);    // 必填,设备制造商
    adl_cJSON_AddStringToObject(root, "deviceBrand", "WJA");                  // 必填,设备品牌
    adl_cJSON_AddStringToObject(root, "deviceModel", "wja001200");            // 必填,设备型号
    adl_cJSON_AddStringToObject(root, "wlanMac", demo_getWlanMac());          // 必填,设备无线网卡MAC,全大写不带冒号;不存在填NONE;update by 220909
    adl_cJSON_AddStringToObject(root, "bluetoothMacAddress", "665544332211"); // 蓝牙设备必填,蓝牙设备的 MAC地址

    // 必填,供电方式.规则:电池供电:battery; POE供电:POE; 市电:220V(110V); USB供电:USB; 其他方式:other
    adl_cJSON_AddStringToObject(root, "powerSupplyMode", "220V");

    // 必填,设备IP.规则:默认上报有线网卡的IP;单栈ipv6需要支持上报ipv6地址;对于双栈ipv4和ipv6同时存在的环境,上报由厂家任选一种;不存在填NONE
    adl_cJSON_AddStringToObject(root, "deviceIP", "*************");

    // 可选,Wi-Fi信号场强
    adl_cJSON_AddStringToObject(root, "wifiRssi", "-60");

    // 3.添加DM模组信息,根据实际情况可选
    // root对象中添加名为moduleInfo的一个数组
    cJSON *array_moduleInfo = NULL;
    adl_cJSON_AddItemToObject(root, "moduleInfo", array_moduleInfo = adl_cJSON_CreateArray());

    // 模组1:wifi模组信息
    cJSON *wifiModuleObj = NULL;
    {
        // array_moduleInfo数组内添加一个对象wifiModuleObj
        adl_cJSON_AddItemToArray(array_moduleInfo, wifiModuleObj = adl_cJSON_CreateObject());
        // wifiModuleObj对象内添加1个条目
        adl_cJSON_AddStringToObject(wifiModuleObj, "moduleType", "Wi-Fi");            // 模组类型, NB、Wi-Fi、Zigbee、Bluetooth、Thread、lora、ZWAVE等
        adl_cJSON_AddStringToObject(wifiModuleObj, "moduleVendor", "11110");          // 模组厂商名
        adl_cJSON_AddStringToObject(wifiModuleObj, "moduleBrand", "11110");           // 模组品牌名
        adl_cJSON_AddStringToObject(wifiModuleObj, "moduleModel", "11110");           // 模组型号
        adl_cJSON_AddStringToObject(wifiModuleObj, "ctaNetworkModel", "11110");       // CTA入网许可证型号,支持蜂窝入网的模组必填
        adl_cJSON_AddStringToObject(wifiModuleObj, "moduleFirmwareVersion", "11110"); // 模组固件版本
        adl_cJSON_AddStringToObject(wifiModuleObj, "moduleSystemVersion", "11110");   // 模组系统版本
        adl_cJSON_AddStringToObject(wifiModuleObj, "moduleImei", "11110");            // 模组IMEI
        adl_cJSON_AddStringToObject(wifiModuleObj, "moduleImsi", "11110");            // 模组IMSI
        adl_cJSON_AddStringToObject(wifiModuleObj, "moduleIccid", "11110");           // 模组ICCID号
        adl_cJSON_AddStringToObject(wifiModuleObj, "moduleCellid", "11110");          // 模组Cellid
        adl_cJSON_AddStringToObject(wifiModuleObj, "moduleLac", "11110");             // 模组Lac
        adl_cJSON_AddStringToObject(wifiModuleObj, "moduleBleMacAddress", "11110");   // 模组蓝牙MAC地址
        adl_cJSON_AddStringToObject(wifiModuleObj, "moduleMacAddress", "11110");      // 模组MAC地址
        adl_cJSON_AddStringToObject(wifiModuleObj, "moduleWlanMac", "11110");         // 模组采集的设备Wlan MAC地址
        adl_cJSON_AddStringToObject(wifiModuleObj, "moduleSn", "11110");              // 模组SN
    }

    // 模组2:蓝牙模组信息
    cJSON *btModuleObj = NULL;
    {
        // array_moduleInfo数组内添加一个对象wifiModuleObj
        adl_cJSON_AddItemToArray(array_moduleInfo, btModuleObj = adl_cJSON_CreateObject());
        // wifiModuleObj对象内添加1个条目
        adl_cJSON_AddStringToObject(btModuleObj, "moduleType", "Bluetooth");        // 模组类型, NB、WiFi、Zigbee、Bluetooth、Thread、lora、ZWAVE等
        adl_cJSON_AddStringToObject(btModuleObj, "moduleVendor", "11110");          // 模组厂商名
        adl_cJSON_AddStringToObject(btModuleObj, "moduleBrand", "11110");           // 模组品牌名
        adl_cJSON_AddStringToObject(btModuleObj, "moduleModel", "11110");           // 模组型号
        adl_cJSON_AddStringToObject(btModuleObj, "ctaNetworkModel", "11110");       // CTA入网许可证型号,支持蜂窝入网的模组必填
        adl_cJSON_AddStringToObject(btModuleObj, "moduleFirmwareVersion", "11110"); // 模组固件版本
        adl_cJSON_AddStringToObject(btModuleObj, "moduleSystemVersion", "11110");   // 模组系统版本
        adl_cJSON_AddStringToObject(btModuleObj, "moduleImei", "11110");            // 模组IMEI
        adl_cJSON_AddStringToObject(btModuleObj, "moduleImsi", "11110");            // 模组IMSI
        adl_cJSON_AddStringToObject(btModuleObj, "moduleIccid", "11110");           // 模组ICCID号
        adl_cJSON_AddStringToObject(btModuleObj, "moduleCellid", "11110");          // 模组Cellid
        adl_cJSON_AddStringToObject(btModuleObj, "moduleLac", "11110");             // 模组Lac
        adl_cJSON_AddStringToObject(btModuleObj, "moduleBleMacAddress", "11110");   // 模组蓝牙MAC地址
        adl_cJSON_AddStringToObject(btModuleObj, "moduleMacAddress", "11110");      // 模组MAC地址
        adl_cJSON_AddStringToObject(btModuleObj, "moduleWlanMac", "11110");         // 模组采集的设备Wlan MAC地址
        adl_cJSON_AddStringToObject(btModuleObj, "moduleSn", "11110");              // 模组SN
    }

    adl_cJSON_AddStringToObject(root, "additionalSlotImei", "11110");   // 可选,额外卡槽IMEI，模组出厂卡槽之外的卡槽信息
    adl_cJSON_AddStringToObject(root, "additionalSlotImsi", "11110");   // 可选,额外卡槽IMSI，模组出厂卡槽之外的卡槽信息
    adl_cJSON_AddStringToObject(root, "additionalSlotIccid", "11110");  // 可选,额外卡槽ICCID，模组出厂卡槽之外的卡槽信息
    adl_cJSON_AddStringToObject(root, "additionalSlotCellid", "11110"); // 可选,额外卡槽CellId，模组出厂卡槽之外的卡槽信息
    adl_cJSON_AddStringToObject(root, "additionalSlotLac", "11110");    // 可选, 额外卡槽Lac，模组出厂卡槽之外的卡槽信息

    // 4.添加版本信息
    // 4.1产品本身版本信息
    adl_cJSON_AddStringToObject(root, "firmwareVersion", DEVICE_FMWARE_VER);
    adl_cJSON_AddStringToObject(root, "softwareVersion", DEVICE_SOFTWARE_VER);

    // 4.2产品依赖的众多中移插件版本信息
    cJSON *andVersionObj = NULL;
    adl_cJSON_AddItemToObject(root, "cmccVersion", andVersionObj = adl_cJSON_CreateObject());
    // adl_cJSON_AddStringToObject(andVersionObj, "andimsVersion", "v1.0");              // 和家固话 SDK,若使用必填;
    // adl_cJSON_AddStringToObject(andVersionObj, "andnlpVersion", "v1.0");              // 和家语音交互 SDK,若使用必填;
    // adl_cJSON_AddStringToObject(andVersionObj, "anddotVersion", "v1.0");              // 和家安防 SDK,若使用必填;
    // adl_cJSON_AddStringToObject(andVersionObj, "andfacerecVersion", "v1.0");          // 和家人脸识别 SDK,若使用必填;

    // AHS中间件版本,必填; 组网设备:AOS-NET-1.0; 机顶盒:A0S-STB-1.0; 摄像头:AOS-IPC-Linux V1.0.0; 其他智能设备:AOS-DEV-1.0
    adl_cJSON_AddStringToObject(andVersionObj, "AHSVersion", "AOS-DEV-1.0");

    // 添加其他扩展字段
    cJSON *dmExt = NULL;
    adl_cJSON_AddItemToObject(root, "deviceManageExtInfo", dmExt = adl_cJSON_CreateObject()); // 必填

    return 0;
}

/************************************************************************
Description:    构造厂商设备信息配置文件,用于准备启动andlinkInit的入参信息
Input:          adl_dev_attr_t *outDevAttr
Output:         adl_dev_attr_t *outDevAttr
Return:         成功:0, 失败:-1
Others:         此接口只是为了调试demo方便,厂商可以不用
************************************************************************/
int buildFacDevinfoCfgFile(char *ifname, adl_dev_attr_t *outDevAttr)
{
    DEMO_DBG_PRINT("[enter]buildFacDevinfoCfgFile\n");
    char id[MAX_SN_LEN + 1] = { 0 };
    // 为加载andlink SDK准备入参
    outDevAttr->deviceType = DEVICE_TYPE;
    outDevAttr->productToken = PRODUCTION_TOKEN;
    outDevAttr->andlinkToken = ANDLINK_TOKEN;
    outDevAttr->deviceVendor = DEVICE_VENDOR_NAME;
    outDevAttr->id = FAC_DEVICE_ID;

    /*
    ########################@@@@ notice notice notice @@@@########################
    1.id是设备自身唯一标识,这里仅是示例,可以填mac或sn,建议使用sn
    */
    if (0 == check_device_mac_info(ifname, id) || 0 == check_device_mac_info(WIRED_IF_NAME, id))
    {
        demo_setDevUniqueId(id);
        outDevAttr->id = demo_getDevUniqueId();
    }

    outDevAttr->firmWareVersion = DEVICE_FMWARE_VER;   // 设备固件版本号
    outDevAttr->softWareVersion = DEVICE_SOFTWARE_VER; // 设备软件版本号
    outDevAttr->cfgPath = CFG_FILE_PATH;               // SDK配置文件存储位置

    // 构造配置文件
    char debugFileName[256] = { 0 };
    app_snprintf_s(debugFileName, sizeof(debugFileName) - 1, "%s/facDevinfo.conf", outDevAttr->cfgPath);

    FILE *fd = NULL;
    if (access(outDevAttr->cfgPath, W_OK) != 0)
    {
        if (mkdir(outDevAttr->cfgPath, 0777) < 0)
        {
            DEMO_DBG_PRINT("mkdir %s failed\n", outDevAttr->cfgPath);
            return -1;
        }
    }

    DEMO_DBG_PRINT("[fac]build devinfo cfg file=%s\n", debugFileName);

    // 不存在,则手动创建一个默认调试配置文件.
    if ((fd = fopen(debugFileName, "w+")) == NULL)
    {
        DEMO_DBG_PRINT("createFile %s failed\n", debugFileName);
        return -1;
    }

    // 构造默认调试配置文件
    if (fputs("#facturer dev configuration file\n", fd) < 0)
    {
        DEMO_DBG_PRINT_ERROR("fputs error1\n");
    }
    if (fputs("#[basic]#\n", fd) < 0)
    {
        DEMO_DBG_PRINT_ERROR("fputs error2\n");
    }

    char cfgStr[128];

    memset(cfgStr, 0, sizeof(cfgStr));
    app_snprintf_s(cfgStr, sizeof(cfgStr) - 1, "deviceType=%s\n", outDevAttr->deviceType);
    if (fputs(cfgStr, fd) < 0)
    {
        DEMO_DBG_PRINT_ERROR("fputs(%s) error\n", cfgStr);
    }

    memset(cfgStr, 0, sizeof(cfgStr));
    app_snprintf_s(cfgStr, sizeof(cfgStr) - 1, "productToken=%s\n", outDevAttr->productToken);
    if (fputs(cfgStr, fd) < 0)
    {
        DEMO_DBG_PRINT_ERROR("fputs(%s) error\n", cfgStr);
    }

    memset(cfgStr, 0, sizeof(cfgStr));
    app_snprintf_s(cfgStr, sizeof(cfgStr) - 1, "andlinkToken=%s\n", outDevAttr->andlinkToken);
    if (fputs(cfgStr, fd) < 0)
    {
        DEMO_DBG_PRINT_ERROR("fputs(%s) error\n", cfgStr);
    }

    memset(cfgStr, 0, sizeof(cfgStr));
    app_snprintf_s(cfgStr, sizeof(cfgStr) - 1, "deviceVendor=%s\n", outDevAttr->deviceVendor);
    if (fputs(cfgStr, fd) < 0)
    {
        DEMO_DBG_PRINT_ERROR("fputs(%s) error\n", cfgStr);
    }

    memset(cfgStr, 0, sizeof(cfgStr));
    app_snprintf_s(cfgStr, sizeof(cfgStr) - 1, "id=%s\n", outDevAttr->id);
    if (fputs(cfgStr, fd) < 0)
    {
        DEMO_DBG_PRINT_ERROR("fputs(%s) error\n", cfgStr);
    }

    memset(cfgStr, 0, sizeof(cfgStr));
    app_snprintf_s(cfgStr, sizeof(cfgStr) - 1, "firmWareVersion=%s\n", outDevAttr->firmWareVersion);
    if (fputs(cfgStr, fd) < 0)
    {
        DEMO_DBG_PRINT_ERROR("fputs(%s) error\n", cfgStr);
    }

    memset(cfgStr, 0, sizeof(cfgStr));
    app_snprintf_s(cfgStr, sizeof(cfgStr) - 1, "softWareVersion=%s\n", outDevAttr->softWareVersion);
    if (fputs(cfgStr, fd) < 0)
    {
        DEMO_DBG_PRINT_ERROR("fputs(%s) error\n", cfgStr);
    }

    memset(cfgStr, 0, sizeof(cfgStr));
    app_snprintf_s(cfgStr, sizeof(cfgStr) - 1, "cfgPath=%s\n", outDevAttr->cfgPath);
    if (fputs(cfgStr, fd) < 0)
    {
        DEMO_DBG_PRINT_ERROR("fputs(%s) error\n", cfgStr);
    }

    /*-----------------------start for sdk test, ignore the following code for user.-----------------------*/
    if (fputs("#[test]#\n", fd) < 0)
    {
        DEMO_DBG_PRINT_ERROR("fputs error3\n");
    }

    // 设置mac
    memset(cfgStr, 0, sizeof(cfgStr));
    app_snprintf_s(cfgStr, sizeof(cfgStr) - 1, "mac=%s\n", DEVICE_REAL_MAC);
    if (fputs(cfgStr, fd) < 0)
    {
        DEMO_DBG_PRINT_ERROR("fputs(%s) error\n", cfgStr);
    }

    // 设置wlanMac
    memset(cfgStr, 0, sizeof(cfgStr));
    app_snprintf_s(cfgStr, sizeof(cfgStr) - 1, "wlanMac=%s\n", DEVICE_WLAN_MAC);
    if (fputs(cfgStr, fd) < 0)
    {
        DEMO_DBG_PRINT_ERROR("fputs(%s) error\n", cfgStr);
    }

    // 设置sn
    memset(cfgStr, 0, sizeof(cfgStr));
    app_snprintf_s(cfgStr, sizeof(cfgStr) - 1, "sn=%s\n", DEVICE_REAL_SN);
    if (fputs(cfgStr, fd) < 0)
    {
        DEMO_DBG_PRINT_ERROR("fputs(%s) error\n", cfgStr);
    }

    // 设置cmei
    memset(cfgStr, 0, sizeof(cfgStr));
    app_snprintf_s(cfgStr, sizeof(cfgStr) - 1, "cmei=%s\n", DEVICE_REAL_CMEI);
    if (fputs(cfgStr, fd) < 0)
    {
        DEMO_DBG_PRINT_ERROR("fputs(%s) error\n", cfgStr);
    }

    // 设置license(一机一密的原始密钥)
    memset(cfgStr, 0, sizeof(cfgStr));
    app_snprintf_s(cfgStr, sizeof(cfgStr) - 1, "license=%s\n", DEVICE_SEC_LICENSE);
    if (fputs(cfgStr, fd) < 0)
    {
        DEMO_DBG_PRINT_ERROR("fputs(%s) error\n", cfgStr);
    }

    // 设置下载策略
    char mode = (char)OTA_FRAG_DOWNLOAD_ENABLE;
    {
        memset(cfgStr, 0, sizeof(cfgStr));
        app_snprintf_s(cfgStr, sizeof(cfgStr) - 1, "otaPolicy=%d\n", mode);
        if (fputs(cfgStr, fd) < 0)
        {
            DEMO_DBG_PRINT_ERROR("fputs(%s) error\n", cfgStr);
        }


        unsigned int oneFragSize = OTA_FRAG_SIZE;
        memset(cfgStr, 0, sizeof(cfgStr));
        app_snprintf_s(cfgStr, sizeof(cfgStr) - 1, "otaFragSize=%u\n", oneFragSize);
        if (fputs(cfgStr, fd) < 0)
        {
            DEMO_DBG_PRINT_ERROR("fputs(%s) error\n", cfgStr);
        }

        // setOtaDownloadPolicy(mode, oneFragSize);
    }

    // 设置下载的文件路径和文件名
    {
        char *filePath = OTA_FILE_PATH;
        memset(cfgStr, 0, sizeof(cfgStr));
        app_snprintf_s(cfgStr, sizeof(cfgStr) - 1, "otaFilePath=%s\n", filePath);
        if (fputs(cfgStr, fd) < 0)
        {
            DEMO_DBG_PRINT_ERROR("fputs(%s) error\n", cfgStr);
        }


        char *filename = OTA_FILE_NAME;
        memset(cfgStr, 0, sizeof(cfgStr));
        app_snprintf_s(cfgStr, sizeof(cfgStr) - 1, "otaFileName=%s\n", filename);
        if (fputs(cfgStr, fd) < 0)
        {
            DEMO_DBG_PRINT_ERROR("fputs(%s) error\n", cfgStr);
        }
        // setOtaStoragePathAndFilename(filePath, filename);
    }

    /*-----------------------end for sdk test-----------------------*/

    fclose(fd);

    DEMO_DBG_PRINT("[fac]build devinfo cfg file OK\n");

    return 0;
}

/************************************************************************
Description:    恢复厂商设备信息配置文件,用于准备启动andlinkInit的入参信息
Input:          adl_dev_attr_t *outDevAttr
Output:         adl_dev_attr_t *outDevAttr
Return:         成功:0, 失败:-1
Others:         此接口只是为了调试demo方便,厂商可以不用
************************************************************************/
int recoverFacDevinfoCfg(adl_dev_attr_t *outDevAttr)
{
    unsigned long lineLen = 0;
    char lineBuf[128] = { 0 };
    char debugFileName[256] = { 0 };
    app_snprintf_s(debugFileName, sizeof(debugFileName) - 1, "%s/facDevinfo.conf", CFG_FILE_PATH);

    DEMO_DBG_PRINT("[enter]recoverFacDevinfoCfg:%s\n", debugFileName);

    /*----读取andlink SDK启动必须的一些信息----*/
    // 读取产品ID
    memset(lineBuf, 0, sizeof(lineBuf));
    if (0 != readCfgItem(debugFileName, "deviceType", lineBuf, sizeof(lineBuf) - 1))
    {
        DEMO_DBG_PRINT("[fac]recover deviceType failed\n");
        goto FAILEXIT;
    }
    if ((lineLen = app_strlen(lineBuf)) > 0)
    {
        app_strncpy_s(s_deviceInfo.deviceType, sizeof(s_deviceInfo.deviceType), lineBuf, lineLen);
        outDevAttr->deviceType = demo_getDeviceType();
    }

    // 读取 productToken
    memset(lineBuf, 0, sizeof(lineBuf));
    if (0 != readCfgItem(debugFileName, "productToken", lineBuf, sizeof(lineBuf) - 1))
    {
        DEMO_DBG_PRINT("[fac]recover productToken failed\n");
        goto FAILEXIT;
    }
    if ((lineLen = app_strlen(lineBuf)) > 0)
    {
        app_strncpy_s(s_deviceInfo.productToken, sizeof(s_deviceInfo.productToken), lineBuf, lineLen);
        outDevAttr->productToken = demo_getProductToken();
        DEMO_DBG_PRINT("[fac]recover productToken(%s) OK\n", s_deviceInfo.productToken);
    }

    // 读取 andlinkToken
    memset(lineBuf, 0, sizeof(lineBuf));
    if (0 != readCfgItem(debugFileName, "andlinkToken", lineBuf, sizeof(lineBuf) - 1))
    {
        DEMO_DBG_PRINT("[fac]recover andlinkToken failed\n");
        goto FAILEXIT;
    }
    if ((lineLen = app_strlen(lineBuf)) > 0)
    {
        app_strncpy_s(s_deviceInfo.andlinkToken, sizeof(s_deviceInfo.andlinkToken), lineBuf, lineLen);
        outDevAttr->andlinkToken = demo_getAndlinkToken();
    }

    // 读取 deviceVendor
    memset(lineBuf, 0, sizeof(lineBuf));
    if (0 != readCfgItem(debugFileName, "deviceVendor", lineBuf, sizeof(lineBuf) - 1))
    {
        DEMO_DBG_PRINT("[fac]recover deviceVendor failed\n");
        goto FAILEXIT;
    }
    if ((lineLen = app_strlen(lineBuf)) > 0)
    {
        app_strncpy_s(s_deviceInfo.deviceVendor, sizeof(s_deviceInfo.deviceVendor), lineBuf, lineLen);
        outDevAttr->deviceVendor = demo_getDeviceVendor();
    }

    // 读取 id
    memset(lineBuf, 0, sizeof(lineBuf));
    if (0 != readCfgItem(debugFileName, "id", lineBuf, sizeof(lineBuf) - 1))
    {
        DEMO_DBG_PRINT("[fac]recover id failed\n");
        goto FAILEXIT;
    }
    if ((lineLen = app_strlen(lineBuf)) > 0)
    {
        app_strncpy_s(s_deviceInfo.id, sizeof(s_deviceInfo.id), lineBuf, lineLen);
        outDevAttr->id = demo_getDevUniqueId();
    }

    // 读取 firmWareVersion
    memset(lineBuf, 0, sizeof(lineBuf));
    if (0 != readCfgItem(debugFileName, "firmWareVersion", lineBuf, sizeof(lineBuf) - 1))
    {
        DEMO_DBG_PRINT("[fac]recover firmWareVersion failed\n");
        goto FAILEXIT;
    }
    if ((lineLen = app_strlen(lineBuf)) > 0)
    {
        app_strncpy_s(s_deviceInfo.firmWareVersion, sizeof(s_deviceInfo.firmWareVersion), lineBuf, lineLen);
        outDevAttr->firmWareVersion = demo_getFirmWareVersion();
    }

    // 读取 softWareVersion
    memset(lineBuf, 0, sizeof(lineBuf));
    if (0 != readCfgItem(debugFileName, "softWareVersion", lineBuf, sizeof(lineBuf) - 1))
    {
        DEMO_DBG_PRINT("[fac]recover softWareVersion failed\n");
        goto FAILEXIT;
    }
    if ((lineLen = app_strlen(lineBuf)) > 0)
    {
        app_strncpy_s(s_deviceInfo.softWareVersion, sizeof(s_deviceInfo.softWareVersion), lineBuf, lineLen);
        outDevAttr->softWareVersion = demo_getSoftWareVersion();
    }

    // 读取 cfgPath
    memset(lineBuf, 0, sizeof(lineBuf));
    if (0 != readCfgItem(debugFileName, "cfgPath", lineBuf, sizeof(lineBuf) - 1))
    {
        DEMO_DBG_PRINT("[fac]recover cfgPath failed\n");
        goto FAILEXIT;
    }
    if ((lineLen = app_strlen(lineBuf)) > 0)
    {
        app_strncpy_s(s_deviceInfo.cfgPath, sizeof(s_deviceInfo.cfgPath), lineBuf, lineLen);
        outDevAttr->cfgPath = demo_getAndlinkCfgPath();
    }

    /*----读取一些调试信息----*/
    // 读取 cmei
    memset(lineBuf, 0, sizeof(lineBuf));
    if (0 != readCfgItem(debugFileName, "cmei", lineBuf, sizeof(lineBuf) - 1))
    {
        DEMO_DBG_PRINT("[fac]recover cmei failed\n");
        goto FAILEXIT;
    }
    if ((lineLen = app_strlen(lineBuf)) > 0)
    {
        app_strncpy_s(s_deviceInfo.cmei, sizeof(s_deviceInfo.cmei), lineBuf, lineLen);
    }

    // 读取 sn
    memset(lineBuf, 0, sizeof(lineBuf));
    if (0 != readCfgItem(debugFileName, "sn", lineBuf, sizeof(lineBuf) - 1))
    {
        DEMO_DBG_PRINT_ERROR("[fac]recover sn failed\n");
        goto FAILEXIT;
    }
    if ((lineLen = app_strlen(lineBuf)) > 0)
    {
        app_strncpy_s(s_deviceInfo.sn, sizeof(s_deviceInfo.sn), lineBuf, lineLen);
        DEMO_DBG_PRINT("[fac]recover sn(%s) OK\n", s_deviceInfo.sn);
    }

    // 读取 license(一机一密的密钥)
    memset(lineBuf, 0, sizeof(lineBuf));
    if (0 == readCfgItem(debugFileName, "license", lineBuf, sizeof(lineBuf) - 1))
    {
        DEMO_DBG_PRINT("[fac]recover license OK\n");
        memset(s_deviceInfo.license, 0, sizeof(s_deviceInfo.license));
        if ((lineLen = app_strlen(lineBuf)) > 0)
        {
            app_strncpy_s(s_deviceInfo.license, sizeof(s_deviceInfo.license), lineBuf, lineLen);
        }
    }

    // 读取机身铭牌mac
    memset(lineBuf, 0, sizeof(lineBuf));
    if (0 != readCfgItem(debugFileName, "mac", lineBuf, sizeof(lineBuf) - 1))
    {
        DEMO_DBG_PRINT("[fac]recover mac failed\n");
    }
    if ((lineLen = app_strlen(lineBuf)) > 0)
    {
        app_memcpy_s(s_deviceInfo.mac, sizeof(s_deviceInfo.mac), lineBuf, lineLen);
    }

    // 读取 无线mac
    memset(lineBuf, 0, sizeof(lineBuf));
    if (0 != readCfgItem(debugFileName, "wlanMac", lineBuf, sizeof(lineBuf) - 1))
    {
        DEMO_DBG_PRINT("[fac]recover wlanMac failed\n");
    }
    if ((lineLen = app_strlen(lineBuf)) > 0)
    {
        app_memcpy_s(s_deviceInfo.wlanMac, sizeof(s_deviceInfo.wlanMac), lineBuf, lineLen);
    }

#if 1 // 根据配置文件设置OTA的下载策略
    // 读取 otaPolicy
    memset(lineBuf, 0, sizeof(lineBuf));
    if (0 != readCfgItem(debugFileName, "otaPolicy", lineBuf, sizeof(lineBuf) - 1))
    {
        DEMO_DBG_PRINT("[fac]recover otaPolicy failed\n");
    }

    char otaMode = 0;
    if ((lineLen = app_strlen(lineBuf)) > 0)
    {
        otaMode = (char)atoi(lineBuf);
    }

    // 读取 otaFragSize
    memset(lineBuf, 0, sizeof(lineBuf));
    if (0 != readCfgItem(debugFileName, "otaFragSize", lineBuf, sizeof(lineBuf) - 1))
    {
        DEMO_DBG_PRINT("[fac]recover otaFragSize failed\n");
    }

    unsigned int oneFragSize = 0;
    if ((lineLen = app_strlen(lineBuf)) > 0)
    {
        oneFragSize = (char)atoi(lineBuf);
    }
    // setOtaDownloadPolicy(otaMode, oneFragSize);
#endif

#if 1 // 根据配置文件设置OTA存储的路径及文件名
    // 读取 otaPolicy
    memset(lineBuf, 0, sizeof(lineBuf));
    if (0 != readCfgItem(debugFileName, "otaFilePath", lineBuf, sizeof(lineBuf) - 1))
    {
        DEMO_DBG_PRINT("[fac]recover otaPolicy failed\n");
    }

    char otaFilePath[128] = { 0 };
    if ((lineLen = app_strlen(lineBuf)) > 0)
    {
        app_strncpy_s(otaFilePath, sizeof(otaFilePath), lineBuf, lineLen);
    }

    // 读取 otaFragSize
    memset(lineBuf, 0, sizeof(lineBuf));
    if (0 != readCfgItem(debugFileName, "otaFileName", lineBuf, sizeof(lineBuf) - 1))
    {
        DEMO_DBG_PRINT("[fac]recover otaFragSize failed\n");
    }

    char otaFileName[128] = { 0 };
    if ((lineLen = app_strlen(lineBuf)) > 0)
    {
        app_strncpy_s(otaFileName, sizeof(otaFileName), lineBuf, lineLen);
    }
    // setOtaStoragePathAndFilename(otaFilePath, otaFileName);
#endif

    return 0;

FAILEXIT:
    return -1;
}

#if 1 // 厂商可选的配置文件读写接口;
// andlink 配置文件名
#define ANDLINK_CFG_FILENAME "/etc/cmcc_robot/andlinkSdk.conf"
/************************************************************************
Description:    SDK调用,读取配置项
Input:          char *item, char *value, int bufsize
Output:         None
Return:         成功:0, 失败:-1
Others:
************************************************************************/
int demo_getCfg_callback(char *item, char *outbuf, unsigned int bufsize)
{
    char *fileName = ANDLINK_CFG_FILENAME;

    FILE *fp = NULL;
    char lineContent[256] = { 0 };
    char *p = NULL;

    if ((NULL == fileName) || (NULL == item) || (NULL == outbuf))
    {
        DEMO_DBG_PRINT("func parameter NULL\n");
        return -1;
    }

    fp = fopen(fileName, "r");
    if (NULL == fp)
    {
        return -1;
    }

    while (NULL != fgets(lineContent, (int)sizeof(lineContent) - 1, fp))
    {
        // 过滤包含#的注释行
        if (0 == strncmp(lineContent, "#", app_strlen("#")))
        {
            continue;
        }

        p = strstr(lineContent, item);
        if (NULL != p && 0 == strncmp(lineContent, item, app_strlen(item)))
        {
            app_strncpy_s(outbuf, bufsize, p + app_strlen(item) + 1, app_strlen(p) - app_strlen(item) - 1);
            p = strchr(outbuf, '\n');
            if (p)
            {
                *p = 0;
            }
            goto SUC_EXIT;
        }
    }

    // FAIL_EXIT:
    fclose(fp);
    return -1;

SUC_EXIT:
    fclose(fp);
    return 0;
}

/************************************************************************
Description:    SDK调用,写配置项
Input:          char *item, char *value, int bufsize
Output:         None
Return:         成功:0, 失败:-1
Others:
************************************************************************/
int demo_setCfg_callback(char *item, char *value)
{
    const char *fileName = ANDLINK_CFG_FILENAME;

    FILE *fp = NULL;
    FILE *fp_tmp = NULL;
    char buf[256];
    char *p = NULL;
    unsigned long tmpFileNameLen = app_strlen(fileName);
    char tmpfile[tmpFileNameLen + 10];
    memset(tmpfile, 0, tmpFileNameLen + 1);

    app_snprintf_s(tmpfile, sizeof(tmpfile) - 1, "%s.tmp", fileName);
    int itemExist = 0;

    if ((NULL == item) || (NULL == value))
    {
        return -1;
    }

    fp = fopen(fileName, "r");

    fp_tmp = fopen(tmpfile, "w");
    if (NULL == fp_tmp)
    {
        if (fp)
        {
            fclose(fp);
            fp = NULL;
        }
        return -1;
    }

    if (NULL != fp)
    {
        while (NULL != fgets(buf, (int)sizeof(buf), fp))
        {
            p = strstr(buf, item);
            if (NULL != p && 0 == strncmp(buf, item, app_strlen(item)))
            {
                app_strncpy_s(p + app_strlen(item) + 1, sizeof(buf) - app_strlen(item) - 1, value, app_strlen(value));
                p += app_strlen(item) + 1 + app_strlen(value);
                if (p)
                {
                    *p = 0;
                }

                fprintf(fp_tmp, "%s\n", buf);
                itemExist = 1;
            }
            else
            {
                fprintf(fp_tmp, "%s", buf);
            }
        }

        if (fp)
        {
            fclose(fp);
            fp = NULL;
        }

        unlink(fileName);
    }

    if (0 == itemExist)
    {
        app_snprintf_s(buf, sizeof(buf) - 1, "%s=%s", item, value);
        fprintf(fp_tmp, "%s\n", buf);
    }

    if (fp_tmp)
    {
        fclose(fp_tmp);
        fp_tmp = NULL;
    }

    rename(tmpfile, fileName);
    unlink(tmpfile);
    return 0;
}
#endif