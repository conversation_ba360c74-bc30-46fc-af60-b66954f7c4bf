/*
 * 文件名称：wpa_supplicant_api.c
 * 说 明:对wpi_cli相关接口的封装,用于实现andlink_adapt.c 中wifi控制的相关接口,仅作为SDK使用者参考.
 * 完成日期：2024年4月22日
 */
#include "andlink_adapt.h"

// 复位wifi配置文件
#define WPA_CONF_CLEAR "ctrl_interface=/var/run/wpa_supplicant\nap_scan=1\nupdate_config=1\ncountry=CN\n\n"
// wifi配置文件
#define WPA_CONF_FILE "/etc/wpa_supplicant/wpa_supplicant.conf"
// wifi扫描结果文件
#define WPA_SCAN_RESULT "/tmp/andlink/wpa_cli_results"

// hostapd配置文件
#define HOSTAPD_CONF_FILE "/etc/hostapd.conf"
// udhcpd配置文件
#define UDHCPD_CONF_FILE "/etc/udhcpd.conf"

typedef enum
{
    WIFI_UNKNOWN = -1,
    WIFI_WPA,
    WIFI_WEP_HEX,
    WIFI_WEP_ASCII,
    WIFI_OPEN
} WIFI_Encryption_TYPE_e;

typedef enum
{
    WIFI_STOP,
    WIFI_AP,
    WIFI_STA,
    WIFI_CONNECTED,
    WIFI_FAILED
} WIFI_WORK_MODE_e;

typedef struct AP_INFO_T
{
    char bssid[20];
    int RSSI;
} AP_INFO_T;

// wifi状态
WIFI_WORK_MODE_e s_wifi_status = 0;

/************************************************************************
Description: 获取wifi的加密类型
Input: char *psk, char *securityMode
Output: None
Return: 加密模式
Others:
securityMode取值如下:
None
WEP-64
WEP-128
WPA-Personal
WPA2-Personal
MIXED-WPAPSK2
WPA3-SAE
MIXED-WPA2WPA3
************************************************************************/
static WIFI_Encryption_TYPE_e get_wifi_encryption_type(char *psk, char *securityMode)
{
    int pskLen = 0;
    if (NULL == securityMode || NULL == psk)
    {
        DEMO_DBG_PRINT_ERROR("func input error\n");
        return -1;
    }

    pskLen = (int)app_strlen(psk);
    if (strstr(securityMode, "WEP"))
    {
        if (5 == pskLen || 13 == pskLen || 16 == pskLen)
        {
            DEMO_DBG_PRINT("wifi encryption type is WEP ASCII, psklen =%d\n", pskLen);
            return WIFI_WEP_ASCII;
        }

        if (10 == pskLen || 26 == pskLen || 32 == pskLen)
        {
            DEMO_DBG_PRINT("wifi encryption type is WEP HEX, psklen =%d\n", pskLen);
            return WIFI_WEP_HEX;
        }
        return -1;
    }
    else if (strstr(securityMode, "WPA"))
    {
        DEMO_DBG_PRINT("wifi encryption type is WPA(%s), pskLen =%d\n", securityMode, pskLen);
        return WIFI_WPA;
    }
    else if (strstr(securityMode, "None") || 0 == pskLen)
    {
        DEMO_DBG_PRINT("wifi encryption type is OPEN(%s), pskLen =%d\n", securityMode, pskLen);
        return WIFI_OPEN;
    }
    else if (pskLen)
    {
        DEMO_DBG_PRINT("default wifi encryption type is WPA, pskLen =%d\n", pskLen);
        return WIFI_WPA;
    }
    else
    {
        DEMO_DBG_PRINT_ERROR("it's unknown wifi encryption type(%s), pskLen =%d\n", securityMode, pskLen);
        return -1;
    }
}

/************************************************************************
Description: 重写wifi配置文件
Input: char *ssid, char *psk, char *encrypt
Output: None
Return: 成功:0, 失败:-1
Others:
************************************************************************/
int write_wpa_conf_file(char *bssid, char *ssid, char *psk, char *encrypt)
{
    char *wpa_conf = WPA_CONF_FILE;
    int ret = -1;

    FILE *fd = NULL;

    if ((fd = fopen(wpa_conf, "w+")) == NULL)
    {
        DEMO_DBG_PRINT_ERROR("createFile %s failed\n", wpa_conf);
        return -1;
    }

    // 写配置文件
    if (fputs("#wifi wpa_supplicant.conf\n", fd) < 0)
    {
        DEMO_DBG_PRINT_ERROR("[write_wpa_conf_file]fputs fail-1\n");
    }
    if (fputs("ctrl_interface=/var/run/wpa_supplicant\n", fd) < 0)
    {
        DEMO_DBG_PRINT_ERROR("[write_wpa_conf_file]fputs fail-2\n");
        goto EXIT;
    }
    if (fputs("ap_scan=1\nupdate_config=1\ncountry=CN\n\n", fd) < 0)
    {
        DEMO_DBG_PRINT_ERROR("[write_wpa_conf_file]fputs fail-3\n");
        goto EXIT;
    }

    char network[128] = { 0 };
    WIFI_Encryption_TYPE_e encryptionType = 0;
    encryptionType = get_wifi_encryption_type(psk, encrypt);

    switch (encryptionType)
    {
    case WIFI_WPA:
        if (bssid && app_strlen(bssid))
        {
            // 连接指定bssid的热点
            app_snprintf_s(network, sizeof(network) - 1, "network={\nssid=\"%s\"\nbssid=%s\npsk=\"%s\"\nscan_ssid=1\n}", ssid, bssid, psk);
        }
        else
        {
            app_snprintf_s(network, sizeof(network) - 1, "network={\nssid=\"%s\"\npsk=\"%s\"\nscan_ssid=1\n}", ssid, psk);
        }
        break;

    case WIFI_WEP_HEX:
        memset(network, 0, sizeof(network));
        app_snprintf_s(network, sizeof(network) - 1, "network={\nssid=\"%s\"\nkey_mgmt=NONE\nwep_key0=%s\nwep_tx_keyidx=0\n}", ssid, psk);
        break;

    case WIFI_WEP_ASCII:
        memset(network, 0, sizeof(network));
        app_snprintf_s(network, sizeof(network) - 1, "network={\nssid=\"%s\"\nkey_mgmt=NONE\nwep_key0=\"%s\"\nwep_tx_keyidx=0\n}", ssid, psk);
        break;

    case WIFI_OPEN:
        /* 开放热点 */
        memset(network, 0, sizeof(network));
        app_snprintf_s(network, sizeof(network) - 1, "network={\nssid=\"%s\"\nkey_mgmt=NONE\nscan_ssid=1\n}", ssid);
        break;

    default:
        goto EXIT;
    }

    if (fputs(network, fd) < 0)
    {
        DEMO_DBG_PRINT_ERROR("[write_wpa_conf_file]fputs %s fail-1\n", network);
        goto EXIT;
    }
    ret = 0;
EXIT:
    fclose(fd);
    return ret;
}
/************************************************************************
Description: 复位wifi配置文件
Return: 成功:0, 失败:-1
Others:
************************************************************************/
static int reset_wpa_conf_file()
{
    char *wpa_conf_content = WPA_CONF_CLEAR;
    FILE *pFile = NULL;

    unsigned long fileSize = 0;
    if (0 == (fileSize = app_strlen(wpa_conf_content)))
    {
        return -1;
    }

    if (NULL == (pFile = fopen(WPA_CONF_FILE, "w")))
    {
        return -1;
    }

    unsigned long rc = fwrite(wpa_conf_content, 1, fileSize, pFile);
    if (rc != fileSize)
    {
        DEMO_DBG_PRINT("write wpa_conf info failed, rc =%lu\n", rc);
    }

    fclose(pFile);

    DEMO_DBG_PRINT("write wpa_conf info success, rc =%lu\n", rc);
    return 0;
}

/************************************************************************
Description: 插入AP时,从大到小进行排序
Input: AP_INFO_T APs[], int apNum, AP_INFO_T *oneAP
Output: AP_INFO_T APs[]
Return: None
Others:
************************************************************************/
void ap_insert_and_sort(AP_INFO_T APs[], int apNum, AP_INFO_T *oneAP)
{
    int i, j;

    if (NULL == oneAP)
    {
        return;
    }

    int new_RSSI = oneAP->RSSI;

    for (i = 0; i < apNum && APs[i].RSSI > new_RSSI; i++)
    {
        ;
    }

    for (j = apNum; j > i; j--)
    {
        APs[j] = APs[j - 1];
    }

    app_memcpy_s(&APs[i], sizeof(AP_INFO_T), oneAP, sizeof(AP_INFO_T));
}

/************************************************************************
Description: 实时检查网口MAC
Input: char *ifname
Output: None
Return: 成功:0, 失败:-1
Others:
************************************************************************/
int check_device_mac_info(char *ifname, char *outMac)
{
    char macAddress[20] = { 0 };
    if (NULL == ifname)
    {
        DEMO_DBG_PRINT_ERROR("ifname is NULL\n");
        return -1;
    }

    if (0 != get_device_mac_address(ifname, macAddress, (int)sizeof(macAddress) - 1))
    {
        DEMO_DBG_PRINT_ERROR("get mac address failed\n");
        return -1;
    }

    DEMO_DBG_PRINT("get mac address success[%u](%s)\n", app_strlen(macAddress), macAddress);

    char MAC[12 + 1] = { 0 };
    int k = 0, i = 0;
    for (i = 0; i < (int)app_strlen(macAddress); i++)
    {
        if ((macAddress[i] - ':') != 0)
        {
            MAC[k++] = (char)toupper(macAddress[i]);
        }
    }
    MAC[sizeof(MAC) - 1] = '\0';
    DEMO_DBG_PRINT("convert mac %s to MAC %s\n", macAddress, MAC);

    if (outMac)
    {
        app_memcpy_s(outMac, sizeof(MAC) - 1, MAC, app_strlen(MAC));
    }

    return 0;
}
/************************************************************************
Description: 实时检查网口IP信息
Input: char *ifname
Output: None
Return: 成功:0, 失败:-1
Others:
************************************************************************/
int check_device_ip_info(char *ifname, char *outIP, char *outBrdAddress)
{
    char ifAddress[INET_ADDRSTRLEN + 1] = { 0 };  // "***********";
    char brdAddress[INET_ADDRSTRLEN + 1] = { 0 }; // "*************";

    if (0 != if_get_ipv4(ifname, ifAddress, brdAddress, INET_ADDRSTRLEN))
    {
        DEMO_DBG_PRINT_ERROR("call if_get_ipv4 failed\n");
        return -1;
    }

    if (0 == app_strlen(ifAddress) || 0 == strncmp(ifAddress, "127.0.0.1", app_strlen("127.0.0.1")))
    {
        DEMO_DBG_PRINT_ERROR("get ip address invalid\n");
        return -1;
    }

    DEMO_DBG_PRINT("get ip Address success:%s\n", ifAddress);

    if (0 == app_strlen(brdAddress))
    {
        DEMO_DBG_PRINT_ERROR("get broadcast Address invalid\n");
        return -1;
    }

    DEMO_DBG_PRINT("get broadcast Address success:%s\n", brdAddress);

    if (outIP)
    {
        app_memcpy_s(outIP, sizeof(ifAddress) - 1, ifAddress, app_strlen(ifAddress));
    }

    if (outBrdAddress)
    {
        app_memcpy_s(outBrdAddress, sizeof(brdAddress) - 1, brdAddress, app_strlen(brdAddress));
    }

    return 0;
}

/************************************************************************
Description: 获取扫描周围热点的结果
Input: char *ssid, AP_INFO_T APs[], int maxApNum
Output: AP_INFO_T APs[]
Return: 热点个数
Others: wpa_cli -i wlan0 scan_result
************************************************************************/
int get_wpa_cli_result(char *ssid, AP_INFO_T APs[], int maxApNum)
{
    FILE *fstream = NULL;
    char cmd[256];
    AP_INFO_T oneAP;
    int apNum = 0;

    int subStringNum = 0;
    char expectedData[2][64] = { 0 };

    // 1.扫描热点
    memset(cmd, 0, sizeof(cmd));
    // wpa_cli -i wlan0 scan_result | grep -w 'CMCC-GUIDE-LINK' | awk '{print $1,$3}'
    // wpa_cli -i wlan0 scan_result | grep -wE 'CMCC-GUIDE-LINK|CMCC-SMARTAOSNET-LINK' | awk '{print $1,$3}'
    // app_snprintf_s(cmd, sizeof(cmd)-1, "wpa_cli -i %s scan_result | grep -w '%s' | awk '{print $1,$3}'", IF_NAME, ssid);

    app_snprintf_s(cmd, sizeof(cmd) - 1, "wpa_cli -i %s scan_result | grep -wE '%s' | awk '{print $1,$3}'", IF_NAME, ssid);
    DEMO_DBG_PRINT("[enter]get_wpa_cli_result,cmd=%s\n", cmd);

    if (NULL == (fstream = popen(cmd, "r")))
    {
        DEMO_DBG_PRINT_ERROR("wpa_cli scan failed: %s\n", strerror(errno));
        return -1;
    }

    // 2.解析扫描结果的每一行
    char tmp[512] = { 0 };
    unsigned int endCharacterIndex = 0;
    while (fgets(tmp, (int)sizeof(tmp), fstream) != NULL)
    {
        endCharacterIndex = app_strlen(tmp) - 1;
        if ((endCharacterIndex < sizeof(tmp)) && (tmp[endCharacterIndex] == '\n'))
        {
            tmp[endCharacterIndex] = '\0';
        }
        DEMO_DBG_PRINT("line[%d]:get return results: %s\n", apNum, tmp);
        subStringNum = extractSubstring(tmp, expectedData, sizeof(expectedData) / sizeof(expectedData[0]));
        if (subStringNum <= 0)
        {
            continue;
        }
        // 获取bssid
        memset(oneAP.bssid, 0, sizeof(oneAP.bssid));
        app_memcpy_s(oneAP.bssid, sizeof(oneAP.bssid) - 1, expectedData[0], app_strlen(expectedData[0]));
        // 获取RSSI
        oneAP.RSSI = atoi(expectedData[1]);

        ap_insert_and_sort(APs, apNum, &oneAP);
        apNum++;
        if (apNum >= maxApNum)
        {
            DEMO_DBG_PRINT("already got enough results\n");
            break;
        }

        memset(tmp, 0, sizeof(tmp));
    }

    pclose(fstream);

    return apNum;
}


int do_wpa_supplicant(char *ifname, char *opt)
{
    char cmd[256 + 1] = { 0 };

#if 0
    app_snprintf_s(cmd, sizeof(cmd)-1, "wpa_cli -i %s %s > /tmp/null 2>&1", ifname, opt);

    DEMO_DBG_PRINT("do_wpa_cli:%s\n",cmd);
    system(cmd);
#else
    char cmd_response[256] = { 0 };
    app_snprintf_s(cmd, sizeof(cmd) - 1, "wpa_supplicant -i %s %s", ifname, opt);
    shellExecuteCmdAndReadResultsByBlock(cmd, cmd_response, sizeof(cmd_response));
    sleep(3);
#endif
    return 0;
}


/************************************************************************
Description: wifi控制
Input:
Output: None
Return: 成功:0, 失败:-1
Others: wpa_cli -i wlan0 disconnect
************************************************************************/
int do_wpa_cli(char *ifname, char *opt)
{
    char cmd[256 + 1] = { 0 };

#if 0
    app_snprintf_s(cmd, sizeof(cmd)-1, "wpa_cli -i %s %s > /tmp/null 2>&1", ifname, opt);

    DEMO_DBG_PRINT("do_wpa_cli:%s\n",cmd);
    system(cmd);
#else
    char cmd_response[256] = { 0 };
    app_snprintf_s(cmd, sizeof(cmd) - 1, "wpa_cli -i %s %s", ifname, opt);
    shellExecuteCmdAndReadResultsByBlock(cmd, cmd_response, sizeof(cmd_response));
    sleep(3);
#endif
    return 0;
}

/************************************************************************
Description: 启动dhcp,获取IP地址
Input:
Output: None
Return: 成功:0, 失败:-1
Others: wpa_cli -i wlan0 disconnect
************************************************************************/
int do_udhcpc(char *ifname)
{
    char cmd[64] = { 0 };
#if 0
    app_snprintf_s(cmd, sizeof(cmd)-1, "udhcpc -b -i %s -q > /tmp/null 2>&1", ifname);
    DEMO_DBG_PRINT("do_udhcpc:%s\n",cmd);
    system(cmd);
#else
    char cmd_response[512] = { 0 };
    app_snprintf_s(cmd, sizeof(cmd) - 1, "udhcpc -n -t 5 -i %s", ifname);
    // app_snprintf_s(cmd, sizeof(cmd)-1, "udhcpc -i %s -q", ifname);
    shellExecuteCmdAndReadResultsByBlock(cmd, cmd_response, sizeof(cmd_response));
#endif
    return 0;
}

/************************************************************************
Description: 检查wifi状态是否连接
Input: char *ifname
Output: None
Return: 成功:0, 失败:-1
Others: wpa_cli -i wlan0 status
wpa_cli -i wlan0 status | grep -w wpa_state=COMPLETED
************************************************************************/
int do_wpa_cli_status_is_connected(char *ifname)
{
    char cmd[64] = { 0 };
    char respBuffer[256 + 1] = { 0 };
    char *status = "COMPLETED";
    app_snprintf_s(cmd, sizeof(cmd) - 1, "wpa_cli -i %s status | grep wpa_state=", ifname);

    int size = shellExecuteCmdAndReadResultsByBlock(cmd, respBuffer, sizeof(respBuffer) - 1);
    if (size <= 0)
    {
        DEMO_DBG_PRINT_ERROR("call shellExecuteCmdAndReadResultsByBlock size is 0\n");
        return -1;
    }

    if (NULL == strstr(respBuffer, status))
    {
        DEMO_DBG_PRINT_ERROR("call shellExecuteCmdAndReadResultsByBlock respBuffer(%s) is unexpected\n", respBuffer);
        return -1;
    }
    return 0;
}

/************************************************************************
Description: 扫描WiFi
Input: wifi_cfg_info_t *wificfg, cJSON *outApMsg
Output: cJSON *outApMsg
Return: 成功:0, 失败:-1
Others:
#搜索附近网络功能  no/ok
wpa_cli -p /etc/wifi/sockets/ -i wlan0 scan
#搜索附近网络,并列出结果
wpa_cli -i wlan0 scan
wpa_cli -i wlan0 scan_result | grep -wE 'CMCC-GUIDE-LINK|CMCC-SMARTAOSNET-LINK' | awk '{print $1,$3}'
wpa_cli -i wlan0 scan_result | grep -wE 'CMCC-GUIDE-LINK' | awk '{print $1,$3}'
wpa_cli -i wlan0 scan_result | grep -wE 'CMCC-SMARTAOSNET-LINK' | awk '{print $1,$3}'
强烈注意：
此接口示例代码,不支持扫描指定名称的隐藏热点,但是实际场景中引导热点是隐藏的,因此厂商需要实现接口支持扫描出周围指定名称的隐藏引导热点列表
************************************************************************/
int scan_wifi(wifi_cfg_info_t *wificfg, cJSON *outApMsg)
{
    int ret = -1;
    AP_INFO_T APs[8];
    memset(APs, 0, sizeof(APs));
    if (NULL == outApMsg || NULL == wificfg || 0 == app_strlen(wificfg->ssid))
    {
        return ret;
    }

    DEMO_DBG_PRINT("[enter]scan_wifi,bssid=%s, ssid=%s\n", wificfg->mac, wificfg->ssid);

    if (0 != write_wpa_conf_file(wificfg->mac, wificfg->ssid, wificfg->password, wificfg->encrypt))
    {
        DEMO_DBG_PRINT_ERROR("write wpa conf file failed\n");
        return -1;
    }

    do_wpa_cli(IF_NAME, "scan");

    int apNum = get_wpa_cli_result(wificfg->ssid, APs, (int)(sizeof(APs) / sizeof(APs[0])));
    if (apNum <= 0)
    {
        DEMO_DBG_PRINT_ERROR("call get_wpa_cli_result failed\n");
        return -1;
    }

    cJSON *root = outApMsg;
    cJSON *array_aps = NULL;
    adl_cJSON_AddItemToObject(root, "apList", array_aps = adl_cJSON_CreateArray());

    cJSON *apObj = NULL;
    int i = 0;
    for (i = 0; i < apNum; i++)
    {
        adl_cJSON_AddItemToArray(array_aps, apObj = adl_cJSON_CreateObject());

        adl_cJSON_AddStringToObject(apObj, "bssid", APs[i].bssid);
        adl_cJSON_AddStringToObject(apObj, "SSID", wificfg->ssid);
        adl_cJSON_AddNumberToObject(apObj, "RSSI", APs[i].RSSI);
    }

    if (i)
    {
        ret = 0;
    }

    return ret;
}
/************************************************************************
Description: 连接或断开WiFi
Input: WIFI_CTRL_OPT_e opt, wifi_cfg_info_t *wificfg, char *outIp, char *outBrdAddr
Output: char *outIp, char *outBrdAddr
Return: 成功:0, 失败:-1
Others:
************************************************************************/
int control_wifi_sta(WIFI_CTRL_OPT_e opt, wifi_cfg_info_t *wificfg, char *outIp, char *outBrdAddr)
{
    int ret = -1;
    char cmd[64] = { 0 };
    char cmd_response[256 + 1] = { 0 };
    DEMO_DBG_PRINT("wifi sta mode =%d(1 start,2 stop)\n", opt);

    if (WIFI_OPT_STA_STOP == opt)
    {
        s_wifi_status = WIFI_STOP;
        do_wpa_cli(IF_NAME, "disconnect");

        app_snprintf_s(cmd, sizeof(cmd) - 1, "ip addr flush dev %s", IF_NAME);
        DEMO_DBG_PRINT("flush:%s\n", cmd);
        system(cmd);

        reset_wpa_conf_file();
        return 0;
    }

    if (NULL == wificfg || NULL == outIp || NULL == outBrdAddr)
    {
        DEMO_DBG_PRINT_ERROR("func parameter is invalid\n");
        return -1;
    }

    DEMO_DBG_PRINT("[control_wifi_sta]bssid=%s, ssid=%s, password=%s, encrypt=%s, len=%d, channel=%u\n",
                   wificfg->mac, wificfg->ssid, wificfg->password, wificfg->encrypt, app_strlen(wificfg->encrypt), wificfg->channel);


    if (0 != write_wpa_conf_file(wificfg->mac, wificfg->ssid, wificfg->password, wificfg->encrypt))
    {
        DEMO_DBG_PRINT_ERROR("write wpa conf file failed\n");
        return -1;
    }

    s_wifi_status = WIFI_STA;

    // 没有网络,首次连接
    char ifAddress[40] = { 0 };
    if (0 != check_device_ip_info(IF_NAME, ifAddress, NULL))
    {
        int wpa_supplicant_pid = get_pid("wpa_supplicant");
        if (wpa_supplicant_pid == 0)
            do_wpa_supplicant(IF_NAME, "-B -c " WPA_CONF_FILE);
        else
        {
            do_wpa_cli(IF_NAME, "reconfigure");
            do_wpa_cli(IF_NAME, "reconnect");
        }
    }
    else
    {
        do_wpa_cli(IF_NAME, "reconfigure");
        do_wpa_cli(IF_NAME, "reconnect");
    }
    // else
    // {
    //     do_wpa_cli(IF_NAME, "reconnect");
    //     DEMO_DBG_PRINT("reconnect\n");
    // }

    // udhcpc network
    // int udhcpc_pid = get_pid("udhcpc");
    // if (udhcpc_pid != 0)
    // {
    //     memset(cmd, 0, sizeof(cmd));
    //     app_snprintf_s(cmd, sizeof(cmd) - 1, "kill %d", udhcpc_pid);
    //     shellExecuteCmdAndReadResultsByBlock(cmd, cmd_response, sizeof(cmd_response));
    // }
    // do_udhcpc(IF_NAME);

    s_wifi_status = WIFI_FAILED;
    int i = 0, sta_try_times = 15;
    for (i = 0; i < sta_try_times; i++)
    {
        sleep(1);

        if (0 != do_wpa_cli_status_is_connected(IF_NAME))
        {
            DEMO_DBG_PRINT_ERROR("check wifi status failed,now try times[%d/%d]\n", i + 1, sta_try_times);
            // continue;
        }

        if (0 != check_device_ip_info(IF_NAME, outIp, outBrdAddr))
        {
            DEMO_DBG_PRINT_ERROR("set andlink ip info failed,now try times[%d/%d]\n", i + 1, sta_try_times);
            continue;
        }
        DEMO_DBG_PRINT("set andlink ip info success\n");
        s_wifi_status = WIFI_CONNECTED;
        char cmd[64] = { 0 };
        app_snprintf_s(cmd, sizeof(cmd) - 1, "systemctl restart systemd-resolved.service");
        DEMO_DBG_PRINT("cmd:%s\n", cmd);
        system(cmd);
        ret = 0;
        break;
    }
    return ret;
}

/************************************************************************
Description: 重写hostapd配置文件
Input: char *ssid, char *psk, char *encrypt
Output: None
Return: 成功:0, 失败:-1
Others:
************************************************************************/
static int write_hostapd_conf_file(char *ssid, char *psk, char *encrypt)
{
    char *conf = HOSTAPD_CONF_FILE;
    int ret = -1;

    FILE *fd = NULL;

    if ((fd = fopen(conf, "w+")) == NULL)
    {
        DEMO_DBG_PRINT_ERROR("createFile %s failed\n", conf);
        return -1;
    }

    char item[128] = {0};
    // 写配置文件
    // 设置使用的网卡
    app_snprintf_s(item, sizeof(item) - 1, "interface=%s\n", IF_NAME);
    if (fputs(item, fd) < 0)
    {
        DEMO_DBG_PRINT_ERROR("fputs (%s) fail\n", item);
        goto EXIT;
    }
    // 设置热点名称
    app_snprintf_s(item, sizeof(item) - 1, "ssid=%s\n", ssid);
    if (fputs(item, fd) < 0)
    {
        DEMO_DBG_PRINT_ERROR("fputs (%s) fail\n", item);
        goto EXIT;
    }
    // 设置热点加密类型
    app_snprintf_s(item, sizeof(item) - 1, "wpa_key_mgmt=%s\n", encrypt);
    if (fputs(item, fd) < 0)
    {
        DEMO_DBG_PRINT_ERROR("fputs (%s) fail\n", item);
        goto EXIT;
    }
    // 设置热点密码
    if (app_strlen(psk))
    {
        app_snprintf_s(item, sizeof(item) - 1, "wpa_passphrase=%s\n", psk);
        if (fputs(item, fd) < 0)
        {
            DEMO_DBG_PRINT_ERROR("fputs (%s) fail\n", item);
            goto EXIT;
        }
    }
    ret = 0;
EXIT:
    fclose(fd);
    return ret;
}

/************************************************************************
Description: 重写udhcpd配置文件
Input: char *ssid, char *psk, char *encrypt
Output: None
Return: 成功:0, 失败:-1
Others:
************************************************************************/
static int write_udhcpd_conf_file()
{
    char *conf = UDHCPD_CONF_FILE;
    int ret = -1;

    FILE *fd = NULL;

    if ((fd = fopen(conf, "w+")) == NULL)
    {
        DEMO_DBG_PRINT_ERROR("createFile %s failed\n", conf);
        return -1;
    }

    char item[128] = { 0 };
    // 写配置文件
    // 设置使用的网卡
    app_snprintf_s(item, sizeof(item) - 1, "interface %s\n", IF_NAME);
    if (fputs(item, fd) < 0)
    {
        DEMO_DBG_PRINT_ERROR("fputs (%s) fail\n", item);
        goto EXIT;
    }
    // 设置IP池的范围
    app_snprintf_s(item, sizeof(item) - 1, "start %s\n", "***************");
    if (fputs(item, fd) < 0)
    {
        DEMO_DBG_PRINT_ERROR("fputs (%s) fail\n", item);
        goto EXIT;
    }
    app_snprintf_s(item, sizeof(item) - 1, "end %s\n", "***************");
    if (fputs(item, fd) < 0)
    {
        DEMO_DBG_PRINT_ERROR("fputs (%s) fail\n", item);
        goto EXIT;
    }

    // 设置路由地址
    app_snprintf_s(item, sizeof(item) - 1, "opt	router %s\n", AP_IP_ADDR);
    if (fputs(item, fd) < 0)
    {
        DEMO_DBG_PRINT_ERROR("fputs (%s) fail\n", item);
        goto EXIT;
    }
    ret = 0;
EXIT:
    fclose(fd);
    return ret;
}

/************************************************************************
Description: 开启或关闭AP
Input: WIFI_CTRL_OPT_e opt, wifi_cfg_info_t *wificfg
Output: outMsg
Return: 成功:0, 失败:-1
Others:
默认的softAP热点,要求如下:
热点名:CMQLINK-{$deviceType}-****,devieType 为设备在开发者门户申请的设备类型,****为设备产生的随机四位码，一般是MAC地址后缀;举例:CMQLINK-30135-8DFB
通道热点认证方式:广播 、开放式接入
通道存活周期:     15分钟,APP发现或触发快连则提前退出
通道网络配置:支持DHCP,设备地址为*************,dhcp分配的地址范围:AP_IP_POOL_BEGIN "***************" AP_IP_POOL_END "***************"
************************************************************************/
int control_wifi_ap(WIFI_CTRL_OPT_e opt, wifi_cfg_info_t *wificfg)
{
    DEMO_DBG_PRINT("wifi ap mode: %d(3 start,4 stop)\n", opt);
    DEMO_DBG_PRINT("wificfg->ssid     =%s\n", wificfg->ssid);
    DEMO_DBG_PRINT("wificfg->password =%s\n", wificfg->password);

    char cmd[256] = { 0 };
    static int s_apConfInit = 0;
    if (0 == s_apConfInit)
    {
        write_udhcpd_conf_file();
        write_hostapd_conf_file(wificfg->ssid, wificfg->password, wificfg->encrypt);
        s_apConfInit = 1;
    }   

    if (WIFI_OPT_AP_STOP == opt)
    {
        // 关闭 AP
        system("killall hostapd > /dev/null 2>&1");
        sleep(2);

        system("killall udhcpd > /dev/null 2>&1");
        sleep(2);
    }
    else
    {
        // 开启AP
        // 1.停止wpa_supplicant
        system("killall wpa_supplicant > /dev/null 2>&1");
        sleep(2);
        
        // 2.启用网卡
        app_snprintf_s(cmd, sizeof(cmd) - 1, "ifconfig %s up", IF_NAME);
        system(cmd);
        sleep(2);

        // 3.给网卡设置IP地址
        app_snprintf_s(cmd, sizeof(cmd) - 1, "ifconfig %s %s", IF_NAME, AP_IP_ADDR);
        system(cmd);

        // 4.启动DHCP服务
        app_snprintf_s(cmd, sizeof(cmd) - 1, "udhcpd %s %s", UDHCPD_CONF_FILE);
        system(cmd);
        sleep(2);

        // 5.启动热点
        app_snprintf_s(cmd, sizeof(cmd) - 1, "hostapd %s -B", HOSTAPD_CONF_FILE);
        system(cmd);
        sleep(2);
    }
    return 0;
}

/************************************************************************
Description: 获取当前连接热点的信息
Input: wifi_cfg_info_t *wificfg
Output: None
Return: 成功:0, 失败:-1
Others:
************************************************************************/
int get_wifi_info(wifi_cfg_info_t *wificfg)
{
    int ret = -1;
    char cmd[256] = { 0 };
    char res[4][128] = { 0 };
    char *response = res[0];
    char *key = "ssid";
    char *cmdFormat = "wpa_cli -i %s status | grep -wE ssid=* ";
    memset(cmd, 0, sizeof(cmd));

    app_snprintf_s(cmd, sizeof(cmd) - 1, cmdFormat, IF_NAME);
    int size = shellExecuteCmdAndReadResultsByRow(cmd, res, 1);

    if (size <= 0)
    {
        DEMO_DBG_PRINT_ERROR("call shellExecuteCmdAndReadResultsByBlock size is 0\n");
        return -1;
    }

    if (NULL == strstr(response, key))
    {
        DEMO_DBG_PRINT_ERROR("call shellExecuteCmdAndReadResultsByBlock respBuffer(%s) is unexpected\n", response);
        return -1;
    }

    char *p = NULL;

    p = strstr(response, key);

    if (p)
    {
        app_strncpy_s(wificfg->ssid, sizeof(wificfg->ssid) - 1, p + app_strlen(key) + 1, app_strlen(response) - app_strlen(key) - 1);

        if ((p = strchr(wificfg->ssid, ' ')))
        {
            *p = 0;
        }
        else if ((p = strchr(wificfg->ssid, '\n')))
        {
            *p = 0;
        }

        ret = 0;
    }

    return 0;
}
