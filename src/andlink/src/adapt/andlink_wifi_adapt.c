/*
 *
 * 文件名称：andlink_wifi_adapt.c
 * 说 明:针对WiFi设备厂商集成andlink SDK需要适配的接口信息,由厂商补充实现.
 *
 */
#include "andlink_adapt.h"
extern int dn_send_cmd_status;

/************************************************************************
Description:    SDK调用实现扫描特定SSID的热点,返回一个热点列表
Input:          demo_scan_wifi_callback
Output:         outMsg
Return:         成功:0, 失败:-1
Others:         wifi设备必须实现
************************************************************************/
int demo_scan_wifi_callback(wifi_cfg_info_t *wificfg, cJSON *outApMsg)
{
    int ret = -1;
    if (NULL == wificfg || NULL == outApMsg)
    {
        DEMO_DBG_PRINT_ERROR("input param null\r\n");
        return ret;
    }

    DEMO_DBG_PRINT("scan_wifi_sta_callback,ssid=%s,mac=%s.\r\n", wificfg->ssid, wificfg->mac);

    ret = scan_wifi(wificfg, outApMsg);

    return ret;
}


/************************************************************************
Description:    SDK调用实现wifi控制,包括STA模式连接热点,STA模式断开热点,开启一个AP,关闭一个AP
Input:          WIFI_CTRL_OPT_e opt, wifi_cfg_info_t *wificfg,int msgBufSize
Output:         outMsg
Return:         成功:0, 失败:-1
Others:         wifi设备必须实现

SoftAP启动说明:
热点名:CMQLINK-{$deviceType}-****,devieType 为设备在开发者门户申请的设备类型,****为设备产生的随机四位码，一般是MAC地址后缀
通道热点认证方式:广播 、开放式接入
通道存活周期:     15分钟,APP发现并返回配网信息或触发快连则提前退出
通道网络配置:支持DHCP,设备地址为*************

Wi-Fi加密模式(wificfg->encrypt)取值如下:
None
WEP-64
WEP-128
WPA-Personal
WPA2-Personal
MIXED-WPAPSK2
WPA3-SAE
MIXED-WPA2WPA3
************************************************************************/
int demo_ctrl_wifi_callback(WIFI_CTRL_OPT_e opt, wifi_cfg_info_t *wificfg, char *outMsg, unsigned int msgBufSize)
{
    (void)outMsg;
    (void)msgBufSize;
    int ret = -1;
    char devIP[46] = { 0 };
    char BdevIP[46] = { 0 };

    DEMO_DBG_PRINT("[demo]ctrl_wifi_sta_callback\r\n");

    switch (opt)
    {
    case WIFI_OPT_STA_START:
        // 连接指定名称和密码的热点
        DEMO_DBG_PRINT("[enter]wifista_handler start\n");
        // 1.1连接热点,并获取设备IP地址及广播地址
        if (0 != control_wifi_sta(WIFI_OPT_STA_START, wificfg, devIP, BdevIP))
        {
            DEMO_DBG_PRINT_ERROR("call control_wifi_sta failed\n");
            demo_set_device_ipaddr_for_callback("", "");
            break;
        }
        DEMO_DBG_PRINT("[leave]wifista_handler start OK,new addr devIP =%s,BdevIP =%s\n", devIP, BdevIP);
        // 1.2设置IP地址和广播地址,以便SDK回调get_device_ipaddr判断设备是否联网
        demo_set_device_ipaddr_for_callback(devIP, BdevIP);
        ret = 0;
        break;

    case WIFI_OPT_STA_STOP:
        (void)wificfg;
        // 断开连接的热点
        DEMO_DBG_PRINT("[enter]wifista_handler stop\n");
        if (0 != control_wifi_sta(WIFI_OPT_STA_STOP, NULL, NULL, NULL))
        {
            DEMO_DBG_PRINT_ERROR("call control_wifi_sta failed\n");
            break;
        }
        demo_set_device_ipaddr_for_callback("", "");
        DEMO_DBG_PRINT("[leave]wifista_handler stop OK\n");
        ret = 0;
        break;

    case WIFI_OPT_AP_START:
        // 启动默认的softAP热点
        DEMO_DBG_PRINT("[enter]wifiAP_handler start\n");
        // if (0 != control_wifi_ap(WIFI_OPT_AP_START, wificfg))
        // {
        //     DEMO_DBG_PRINT_ERROR("call control_wifi_ap start failed\n");
        //     break;
        // }
        ret = 0;
        break;

    case WIFI_OPT_AP_STOP:
        // 关闭默认的softAP热点
        DEMO_DBG_PRINT("[enter]wifiAP_handler stop\n");
        // if (0 != control_wifi_ap(WIFI_OPT_AP_STOP, wificfg))
        // {
        //     DEMO_DBG_PRINT_ERROR("call control_wifi_ap stop failed\n");
        //     break;
        // }
        ret = 0;
        break;

    case WIFI_OPT_NET_STORE:
        // 存储热点
        DEMO_DBG_PRINT("[enter]wifista_handler storage\n");
        ret = 0;
        break;

    case WIFI_OPT_NET_CLEAR:
        // 清除热点配置
        DEMO_DBG_PRINT("[enter]wifista_handler clear\n");
        ret = 0;
        break;

    case WIFI_OPT_NET_GET:
        // 获取热点信息
        DEMO_DBG_PRINT("[enter]wifista_handler get\n");
        if (0 != get_wifi_info(wificfg))
        {
            DEMO_DBG_PRINT_ERROR("call get_wifi_info failed\n");
            break;
        }
        ret = 0;
        break;
    }

    return ret;
}

/************************************************************************
Description:    扫码配网控制
Input:          demo_scan_wifi_callback
Output:         outMsg
Return:         成功:0, 失败:-1
Others:         wifi设备必须实现
************************************************************************/
int demo_ctrl_scanCode_callback(SCAN_CTRL_OPT_e opt)
{
    int ret = -1;
    switch (opt)
    {
    case SCAN_OPT_QRCODE_START:
        // TODO 开启扫码配网
        break;
    case SCAN_OPT_QRCODE_STOP:
        // TODO 关闭扫码配网
        break;
    default:
        break;
    }

    return ret;
}

/************************************************************************
Description: 通知设备启动BLE配网
Input: adl_ble_gatt *gattServer
Output: None
Return: 成功:0, 失败:-1
Others: wifi设备选用蓝牙配网必须实现
************************************************************************/
int demo_ble_start_server_callback(adl_ble_gatt *gattServer)
{
    int ret = 0;
    DEMO_DBG_PRINT("[enter]start gattServer\n");

    DEMO_DBG_PRINT("advDataLen =%u\n", gattServer->advDataLen);
    DEMO_DBG_PRINT("advData:");
    for (size_t i = 0; i < gattServer->advDataLen; i++)
    {
        printf("%02X ",gattServer->advData[i]);
    }
    printf("\n");
    
    DEMO_DBG_PRINT("localName                      =%s\n", gattServer->localName);
    DEMO_DBG_PRINT("serviceUUID                    =0x%x\n", gattServer->serviceUUID);
    DEMO_DBG_PRINT("characteristicUUID_Down_Write  =0x%x\n", gattServer->characteristicUUID_Down_Write);
    DEMO_DBG_PRINT("characteristicUUID_Up_Notify   =0x%x\n", gattServer->characteristicUUID_Up_Notify);
    DEMO_DBG_PRINT("characteristicUUID_Up_Indicate =0x%x\n", gattServer->characteristicUUID_Up_Indicate);
    DEMO_DBG_PRINT("duration =%u\n", gattServer->duration);
    DEMO_DBG_PRINT("timeout =%u\n", gattServer->timeout);
    dn_send_cmd_status = 2;
    // TODO

    return ret;
}

/************************************************************************
Description: 通知设备停止BLE配网
Input: None
Output: None
Return: 成功:0, 失败:-1
Others: wifi设备选用蓝牙配网必须实现
************************************************************************/
int demo_ble_stop_server_callback(void)
{
    int ret = 0;
    dn_send_cmd_status = 3;
    // TODO
    return ret;
}
/************************************************************************
Description: 通知设备发送BLE数据
Input: char *data, int datalen
Output: None
Return: 成功:0, 失败:-1
Others: wifi设备选用蓝牙配网必须实现
************************************************************************/
int demo_ble_send_callback(char *data, unsigned int datalen)
{
    int ret = -1;
    if (NULL == data)
    {
        return ret;
    }

    DEMO_DBG_PRINT("[enter]demo_ble_send_callback,datalen =%u\n", datalen);
    // TODO
    dn_send_cmd_status = 4;
    ret = 0;
    return ret;
}