/*
 *
 * 文件名称：andlink_control_adapt.c
 * 说 明:厂商集成andlink SDK 管控功能需要适配的接口信息,由厂商补充实现.
 *
 */
#include "andlink_adapt.h"
#include <cJSON.h>       // 标准cJSON头文件（需确保路径正确）
#include <string.h>      // 用于strncpy、strcmp等函数
#include <stdlib.h>      // 用于system、strtol等函数
#include <stdio.h>       // 用于snprintf、printf等函数

extern int dn_send_cmd_status;
extern char* g_json_str;
// #include "andlink_node.hpp"
// #include "andlink_node_wrapper.h"

// extern std::shared_ptr<AndlinkNode> andlink_node_;

// 功能参数索引-名称接口
// 静态字段（固定为示例值）
static const int g_playTime = 65;         // 播放时间（静态）
static const int g_volPCT = 20;           // 音量百分比（静态）
char *g_dnControlRespData = NULL;
typedef struct
{
    int funcIndex;
    char *function;
} FUNCTION_INDEX_MAP_T;

#define PYTHON_SCRIPT_PATH "/usr/bin/cmcc_robot/install/ble/lib/python3.8/site-packages/ble/mp3.py"

// 设备支持的下行管控功能列表
static FUNCTION_INDEX_MAP_T s_adlFunctionSets[] = {
    { 0, "Control" },
    { 1, "Unbind" },
    { 2, "SelfDetect" },
    { 3, "Reboot" },
    { 4, "Bind" },
    { 5, "Query" },
#if ANDLINK_DEBUG_ENABLE == 4
    { 6, "DebugControl" },
#endif
};

typedef struct device_control_model
{
    char code[16];
    char value[8];
} device_control_model_t;

// 设备最大支持的功能参数个数,可以根据设备的实际情况进行调整
#define MAX_FUNC_PARAMETR_NUM 4
// 样例,默认设备的功能参数是插座开关(outletStatus),开关状态为开(1)
static device_control_model_t s_control_model[MAX_FUNC_PARAMETR_NUM] = {{"outletStatus","1"}};

/************************************************************************
Description: 获取管控功能的索引
Input: char *function, int *outIndex
Output: int *outIndex
Return: 成功:0, 失败:-1
Others:
************************************************************************/
int checFunctionIndex(char *function, int *outIndex)
{
    int i = 0;
    if (NULL == function || 0 == app_strlen(function))
    {
        return -1;
    }

    char tmpFunc[16] = { 0 };
    app_strncpy_s(tmpFunc, sizeof(tmpFunc) - 1, function, app_strlen(function));
    // strtolower(tmpFunc);

    for (i = 0; i < (int)(sizeof(s_adlFunctionSets) / sizeof(FUNCTION_INDEX_MAP_T)); ++i)
    {
        if (0 == strcmp(s_adlFunctionSets[i].function, tmpFunc))
        {
            *outIndex = s_adlFunctionSets[i].funcIndex;
            return 0;
        }
    }

    return -1;
}

/************************************************************************
 * 封装函数：生成JSON字符串并发布到ROS2话题
 * @param chapterUrl  要填充到info.url的URL字符串
 * @return 成功:0, 失败:-1
 ************************************************************************/
int createAndPublishMediaPlayEvent(const char* chapterUrl)
{
    // 1. 生成当前时间戳（毫秒级）
    struct timeval tv;
    gettimeofday(&tv, NULL);
    long long seq = (long long)tv.tv_sec * 1000 + tv.tv_usec / 1000;
    char eventId[64] = {0};
    snprintf(eventId, sizeof(eventId), "speech_%lld", seq);
    DEMO_DBG_PRINT("Generated eventId: %s, seq: %lld\n", eventId, seq);

    // 2. 构造根JSON对象
    cJSON *root = cJSON_CreateObject();
    if (!root) {
        DEMO_DBG_PRINT_ERROR("Failed to create root JSON object\n");
        return -1;
    }

    // 3. 添加核心字段
    cJSON_AddStringToObject(root, "deviceId", "11200");
    cJSON_AddStringToObject(root, "domain", "DEVICE_ABILITY");
    cJSON_AddStringToObject(root, "event", "audio_media_play");
    cJSON_AddStringToObject(root, "eventId", eventId);
    cJSON_AddNumberToObject(root, "seq", seq);

    // 4. 构造body对象
    cJSON *body = cJSON_CreateObject();
    if (!body) {
        DEMO_DBG_PRINT_ERROR("Failed to create body object\n");
        cJSON_Delete(root);
        return -1;
    }
    
    // 5. 构造info对象（核心：包含chapterUrl）
    cJSON *info = cJSON_CreateObject();
    if (!info) {
        DEMO_DBG_PRINT_ERROR("Failed to create info object\n");
        cJSON_Delete(body);
        cJSON_Delete(root);
        return -1;
    }
    cJSON_AddStringToObject(info, "url", chapterUrl);
    
    // 6. 填充body字段
    cJSON_AddStringToObject(body, "subEvent", "media.play");
    cJSON_AddStringToObject(body, "playListUrl", "");
    cJSON_AddItemToObject(body, "info", info);
    cJSON_AddObjectToObject(body, "next");  // 空对象
    cJSON_AddBoolToObject(body, "infinity", cJSON_False);
    
    // 7. 将body添加到根对象
    cJSON_AddItemToObject(root, "body", body);

    // 8. 生成JSON字符串
    char *json_str = cJSON_PrintUnformatted(root);
    if (!json_str) {
        DEMO_DBG_PRINT_ERROR("Failed to generate JSON string\n");
        cJSON_Delete(root);
        return -1;
    }

    // 9. 安全赋值给全局变量
    char *new_copy = strdup(json_str); // 创建副本
    if (!new_copy) {
        DEMO_DBG_PRINT_ERROR("Memory allocation failed\n");
        free(json_str);
        cJSON_Delete(root);
        return -1;
    }
    
    // 释放旧的内存（如果存在）
    if (g_json_str) {
        free(g_json_str);
    }
    g_json_str = new_copy; // 更新全局变量
    
    // 10. 打印生成的JSON字符串
    char *formatted_str = cJSON_Print(root);
    if (formatted_str) {
        DEMO_DBG_PRINT("Generated JSON string:\n%s\n", formatted_str);
        free(formatted_str);
    } else {
        DEMO_DBG_PRINT("Generated JSON string: %s\n", g_json_str);
    }

    // 11. 释放资源和内存
    free(json_str);      // 释放临时JSON缓冲区
    cJSON_Delete(root);  // 释放cJSON对象
    
    return 0;
}

/************************************************************************
Description: 执行特定管控指令
Input: char *data
Output: NONE
Return: 成功:0, 失败:-1
Others:
************************************************************************/
int doControlCmd(char *data) {
    // 静态变量（保留上次调用的状态）
    static char g_chapterId[256] = {0};       // 从playResource提取的chapterId
    static int g_playStatus = 1;              // 从playStatus提取的status（初始值1）
    
    cJSON *root = NULL;
    char paramCode[16] = {0};
    char paramValue[1024] = {0};
    char cmdBuffer[1024] = {0};
    int hasValidPlayResource = 0;
    // 1. 打印原始数据
    if (data == NULL) {
        DEMO_DBG_PRINT_ERROR("doControlCmd: data is NULL!\n");
        return -1;
    }
    DEMO_DBG_PRINT("doControlCmd: received data = [%s]\n", data);

    // 2. 解析JSON根对象
    root = cJSON_Parse(data);
    if (root == NULL) {
        DEMO_DBG_PRINT_ERROR("[cJSON_Parse]Error: %s\n", cJSON_GetErrorPtr());
        return -1;
    }

    // 3. 获取"params"或"parameters"数组（兼容两种信令格式）
    cJSON *paramsArray = cJSON_GetObjectItem(root, "params");
    if (paramsArray == NULL || !cJSON_IsArray(paramsArray)) {
        paramsArray = cJSON_GetObjectItem(root, "parameters");
        if (paramsArray == NULL || !cJSON_IsArray(paramsArray)) {
            DEMO_DBG_PRINT_ERROR("Missing or invalid 'params'/'parameters' array\n");
            goto FAIL_EXIT;
        }
    }

    // 4. 遍历数组
    int paramsSize = cJSON_GetArraySize(paramsArray);
    for (int i = 0; i < paramsSize; i++) {
        cJSON *paramObj = cJSON_GetArrayItem(paramsArray, i);
        if (paramObj == NULL || !cJSON_IsObject(paramObj)) {
            DEMO_DBG_PRINT_ERROR("Invalid params element at index %d\n", i);
            continue;
        }

        // 解析paramCode和paramValue
        cJSON *paramCodeItem = cJSON_GetObjectItem(paramObj, "paramCode");
        cJSON *paramValueItem = cJSON_GetObjectItem(paramObj, "paramValue");
        if (paramCodeItem == NULL || !cJSON_IsString(paramCodeItem) || 
            paramValueItem == NULL || !cJSON_IsString(paramValueItem)) {
            DEMO_DBG_PRINT_ERROR("Missing or invalid 'paramCode'/'paramValue' at index %d\n", i);
            continue;
        }
        
        // 安全复制字符串
        strncpy(paramCode, paramCodeItem->valuestring, sizeof(paramCode) - 1);
        paramCode[sizeof(paramCode) - 1] = '\0';
        strncpy(paramValue, paramValueItem->valuestring, sizeof(paramValue) - 1);
        paramValue[sizeof(paramValue) - 1] = '\0';

        DEMO_DBG_PRINT("paramCode = %s, paramValue = %s\n", paramCode, paramValue);

        // 5. 处理playResource（提取chapterId）
        if (strcmp(paramCode, "playResource") == 0) {
            cJSON *playResourceObj = cJSON_Parse(paramValue);
            if (playResourceObj == NULL) {
                DEMO_DBG_PRINT_ERROR("Failed to parse 'playResource' value: %s\n", cJSON_GetErrorPtr());
                continue;
            }
            hasValidPlayResource = 1;
            // 提取chapterId
            cJSON *chapterIdItem = cJSON_GetObjectItem(playResourceObj, "chapterId");
            if (chapterIdItem != NULL && cJSON_IsString(chapterIdItem)) {
                strncpy(g_chapterId, chapterIdItem->valuestring, sizeof(g_chapterId) - 1);
                g_chapterId[sizeof(g_chapterId) - 1] = '\0';
                DEMO_DBG_PRINT("Obtained chapterId: %s\n", g_chapterId);
            } else {
                DEMO_DBG_PRINT_ERROR("'playResource' missing valid 'chapterId'\n");
            }

            // 处理chapterUrl
            cJSON *chapterUrlItem = cJSON_GetObjectItem(playResourceObj, "chapterUrl");
            if (chapterUrlItem != NULL && cJSON_IsString(chapterUrlItem)) {
                char chapterUrl[256] = {0};
                strncpy(chapterUrl, chapterUrlItem->valuestring, sizeof(chapterUrl) - 1);
                chapterUrl[sizeof(chapterUrl) - 1] = '\0';
                
                snprintf(cmdBuffer, sizeof(cmdBuffer), "python3 %s play \"%s\"", 
                         PYTHON_SCRIPT_PATH, chapterUrl);
                DEMO_DBG_PRINT("Executing play command: %s\n", cmdBuffer);
                
                int ret = system(cmdBuffer);
                if (ret != 0) {
                    DEMO_DBG_PRINT_ERROR("Play command failed (code: %d)\n", ret);
                } else {
                    DEMO_DBG_PRINT("Play command executed successfully\n");
                }
            } else {
                DEMO_DBG_PRINT_ERROR("'playResource' missing valid 'chapterUrl'\n");
            }

            cJSON_Delete(playResourceObj);
        }

        // 6. 处理playStatus（保存status）
        else if ((strcmp(paramCode, "playStatus") == 0) && (hasValidPlayResource == 0)) {
            char *endPtr = NULL;
            long playStatus = strtol(paramValue, &endPtr, 10);
            if (*endPtr != '\0' || playStatus < 0 || playStatus > 2) {
                DEMO_DBG_PRINT_ERROR("Invalid playStatus: %s (must be 0/1/2)\n", paramValue);
                continue;
            }

            // 保存status
            g_playStatus = (int)playStatus;
            DEMO_DBG_PRINT("Updated playStatus to: %d\n", g_playStatus);

            // 构建命令
            switch ((int)playStatus) {
                case 0: // 暂停
                    snprintf(cmdBuffer, sizeof(cmdBuffer), "python3 %s pause", PYTHON_SCRIPT_PATH); 
                    break;
                case 1: // 继续播放
                    snprintf(cmdBuffer, sizeof(cmdBuffer), "python3 %s resume", PYTHON_SCRIPT_PATH); 
                    break;
                case 2: // 停止播放
                    snprintf(cmdBuffer, sizeof(cmdBuffer), "python3 %s quit", PYTHON_SCRIPT_PATH); 
                    break;
                default:
                    DEMO_DBG_PRINT_ERROR("Unknown playStatus: %ld\n", playStatus);
                    continue;
            }
            
            DEMO_DBG_PRINT("Executing playStatus command: %s\n", cmdBuffer);
            int ret = system(cmdBuffer);
            if (ret != 0) {
                DEMO_DBG_PRINT_ERROR("playStatus command failed (code: %d)\n", ret);
            } else {
                DEMO_DBG_PRINT("playStatus command executed successfully\n");
            }
        }
    }

    // 新增：生成params2 JSON
    cJSON *params2Root = NULL;
    cJSON *paramsArray2 = NULL;
    cJSON *playInfoObj = NULL;
    cJSON *paramValueObj = NULL;
    char *paramValueStr = NULL;
    char *params2Str = NULL;
    
    // 创建整个params2 JSON结构
    params2Root = cJSON_CreateObject();
    if (params2Root == NULL) {
        DEMO_DBG_PRINT_ERROR("Failed to create params2Root\n");
        goto CLEANUP_PARAMS2;
    }
    
    paramsArray2 = cJSON_CreateArray();
    if (paramsArray2 == NULL) {
        DEMO_DBG_PRINT_ERROR("Failed to create paramsArray for params2\n");
        goto CLEANUP_PARAMS2;
    }
    
    playInfoObj = cJSON_CreateObject();
    if (playInfoObj == NULL) {
        DEMO_DBG_PRINT_ERROR("Failed to create playInfoObj for params2\n");
        goto CLEANUP_PARAMS2;
    }
    
    // 添加paramCode
    cJSON_AddStringToObject(playInfoObj, "paramCode", "playInfo");
    
    // 创建嵌套的paramValue对象
    paramValueObj = cJSON_CreateObject();
    if (paramValueObj == NULL) {
        DEMO_DBG_PRINT_ERROR("Failed to create paramValueObj for params2\n");
        goto CLEANUP_PARAMS2;
    }
    
    // 添加字段到paramValue对象
    cJSON_AddStringToObject(paramValueObj, "chapterId", g_chapterId);
    cJSON_AddNumberToObject(paramValueObj, "status", g_playStatus);
    cJSON_AddNumberToObject(paramValueObj, "playTime", 65);
    cJSON_AddNumberToObject(paramValueObj, "volPCT", 20);
    
    // 将嵌套对象转换为JSON字符串
    paramValueStr = cJSON_PrintUnformatted(paramValueObj);
    if (paramValueStr == NULL) {
        DEMO_DBG_PRINT_ERROR("Failed to print paramValueObj for params2\n");
        goto CLEANUP_PARAMS2;
    }
    
    // 添加paramValue字符串
    cJSON_AddStringToObject(playInfoObj, "paramValue", paramValueStr);
    
    // 将playInfo对象添加到数组
    cJSON_AddItemToArray(paramsArray2, playInfoObj);
    
    // 将数组添加到根对象
    cJSON_AddItemToObject(params2Root, "params", paramsArray2);
    
    // 打印生成的params2
    params2Str = cJSON_PrintUnformatted(params2Root);
    if (params2Str != NULL) {
        DEMO_DBG_PRINT("Generated params2: %s\n", params2Str);
       // 5.1 释放之前的全局变量内存（避免泄漏）
        if (g_dnControlRespData != NULL) {
            free(g_dnControlRespData);
            g_dnControlRespData = NULL;
        }
        // 5.2 复制新字符串到全局变量（strdup会分配新内存）
        g_dnControlRespData = strdup(params2Str);
        if (g_dnControlRespData == NULL) {
            DEMO_DBG_PRINT_ERROR("Failed to duplicate params2 string\n");
        }        
        free(params2Str);
        
        // TODO: 这里应该将params2发送给目标设备，而不仅仅是打印
        // 示例: sendToDevice(params2Str);
    }

CLEANUP_PARAMS2:
    // 释放所有资源
    if (paramValueStr) free(paramValueStr);
    if (paramValueObj) cJSON_Delete(paramValueObj);
    
    /* 注意：以下对象如果已添加到树中，会被params2Root一起删除，
       不需要单独删除，所以我们设置为NULL避免重复释放 */
    playInfoObj = NULL;    // 已添加到paramsArray2
    paramsArray2 = NULL;   // 已添加到params2Root
    
    if (params2Root) cJSON_Delete(params2Root);

    // 释放原JSON根对象
    cJSON_Delete(root);
    return 0;

FAIL_EXIT:
    if (root) cJSON_Delete(root);
    return -1;
}

/************************************************************************
Description: 执行下行控制命令的回应
Input: char *data
Output: NONE
Return: 成功:0, 失败:-1
Others:
************************************************************************/
static int doDnControlCmdResponse(RESP_MODE_e mode, dn_dev_ctrl_frame_t *ctrlFrame, char *eventType, char *respData, unsigned int respBufSize)
{
    int ret = 0;
    char *localEventType = "ParamChange";
    const char* mode_str[] = {"ASYNC", "SYNC"};
    const char* mode_desc = (mode >= 0 && mode < sizeof(mode_str)/sizeof(mode_str[0])) ? mode_str[mode] : "UNK";
    // 核心入口打印（覆盖所有传入参数）
    DEMO_DBG_PRINT("doDnControlCmdResponse: mode=%d(%s), ctrlFrame=%p, eventType=%p, respData=%p, respBufSize=%u\n",
                   mode, mode_desc, ctrlFrame, eventType, respData, respBufSize);
    // 进行回应
    switch (mode)
    {
    case ASYNC_MODE:
        // 异步响应
        if (NULL != respData && NULL != localEventType)
        {
            devDataReport(localEventType, ctrlFrame->seqId, 0, respData, app_strlen(respData));
        }
        break;

    case SYNC_MODE:
        // 同步响应
        if (NULL != eventType && NULL != localEventType)
        {
            app_strncpy_s(eventType, sizeof(ctrlFrame->function) - 1, localEventType, app_strlen(localEventType));
        }

        if (NULL != respData)
        {
            app_strncpy_s(respData, respBufSize, ctrlFrame->data, app_strlen(ctrlFrame->data));
            devDataReport(localEventType, ctrlFrame->seqId, 0, ctrlFrame->data, app_strlen(ctrlFrame->data));
            DEMO_DBG_PRINT("sync response=type:%s,data:%s,datalen=%u\n", eventType, respData, app_strlen(respData));
        }
        break;
    default:
        ret = -1;
        break;
    }
    return 0;
}
/************************************************************************
Description: 执行下行自检命令的回应
Input: char *data
Output: NONE
Return: 成功:0, 失败:-1
Others:
************************************************************************/
int doDnSelfDetectCmdResponse(RESP_MODE_e mode, dn_dev_ctrl_frame_t *ctrlFrame, char *eventType, char *respData, unsigned int respBufSize)
{
    int ret = 0;
    char localResp[256] = { 0 };
    char *format = "{\"cpuRate\":%d,\"ramRate\":%d,\"upLinkType\":\"%s\",\"rssi\":\"%s\"}";
    char *localEventType = "SelfDetect";
    app_snprintf_s(localResp, sizeof(localResp) - 1, format, 30, 30, "Wi-Fi", "-45");

    // 进行回应
    switch (mode)
    {
    case ASYNC_MODE:
        // 异步响应
        if (NULL != ctrlFrame->data && NULL != localEventType)
        {
            devDataReport(localEventType, ctrlFrame->seqId, 0, localResp, app_strlen(localResp));
        }
        break;

    case SYNC_MODE:
        // 同步响应
        if (NULL != eventType && NULL != localEventType)
        {
            app_strncpy_s(eventType, sizeof(ctrlFrame->function) - 1, localEventType, app_strlen(localEventType));
        }

        if (NULL != respData)
        {
            app_strncpy_s(respData, respBufSize, localResp, app_strlen(localResp));
            devDataReport(localEventType, ctrlFrame->seqId, 0, localResp, app_strlen(localResp));

            DEMO_DBG_PRINT("sync response=type:%s,data:%s,datalen=%u\n", eventType, respData, app_strlen(respData));
        }

        break;
    default:
        ret = -1;
        break;
    }
    return ret;
}
/************************************************************************
Description: 执行下行查询命令的回应
Input: char *data
Output: NONE
Return: 成功:0, 失败:-1
Others:
************************************************************************/
int doDnQueryCmdResponse(RESP_MODE_e mode, dn_dev_ctrl_frame_t *ctrlFrame, char *eventType, char *respData, unsigned int respBufSize)
{
    int ret = 0;
    char *localEventType = "ParamChange";

    cJSON *root = adl_cJSON_CreateObject();
    if (NULL == root)
    {
        return -1;
    }

    // root对象中添加名为data的一个数组
    cJSON *array_data = NULL;
    adl_cJSON_AddItemToObject(root, "params", array_data = adl_cJSON_CreateArray());

    cJSON *paramObj = NULL;
    int i = 0;
    int num = (int)(sizeof(s_control_model) / sizeof(s_control_model[0]));
    for (i = 0; i < num; ++i)
    {
        if (0 == app_strlen(s_control_model[i].code))
        {
            continue;
        }
        adl_cJSON_AddItemToArray(array_data, paramObj = adl_cJSON_CreateObject());
        adl_cJSON_AddStringToObject(paramObj, "paramCode", s_control_model[i].code);
        adl_cJSON_AddStringToObject(paramObj, "paramValue", s_control_model[i].value);
    }

    char *s_info = adl_cJSON_PrintUnformatted(root);
    DEMO_DBG_PRINT("doDnQueryCmdResponse s_info = %s\n", s_info);
    adl_cJSON_Delete(root);

    // 进行回应
    switch (mode)
    {
    case ASYNC_MODE:
        // 异步响应
        if (NULL != ctrlFrame->data && NULL != localEventType)
        {
            devDataReport(localEventType, ctrlFrame->seqId, 0, s_info, app_strlen(s_info));
        }
        break;

    case SYNC_MODE:
        // 同步响应
        if (NULL != eventType && NULL != localEventType)
        {
            app_strncpy_s(eventType, sizeof(ctrlFrame->function) - 1, localEventType, app_strlen(localEventType));
        }

        if (NULL != respData)
        {
            app_strncpy_s(respData, respBufSize, s_info, app_strlen(s_info));
            DEMO_DBG_PRINT("sync response=type:%s,data:%s,datalen=%u\n", eventType, respData, app_strlen(respData));
        }

        break;
    default:
        ret = -1;
        break;
    }

    free(s_info);
    return ret;
}
/************************************************************************
Description:    SDK调用实现对设备的下行管控;
Input:          RESP_MODE_e mode, dn_dev_ctrl_frame_t *ctrlFrame
Output:         char *eventType, char *respData
Return:         成功:0, 失败:-1
Others:         注意:若mode要求同步响应,才会使用两个输出参数
                响应方式分为:无需响应;同步响应;异步响应
ctrlFrame->data的格式如下:
                {
                    "params": [ {"paramCode": "PARAMCODE1","paramValue": "10"},// 单端口
                               {"paramCode": "PARAMCODE2","paramValue": "20"},
                               {"paramCode": "PARAMCODE3","paramIndex": "1","paramValue": "10"},// 多端口,比如插排,1号开关状态,2号开关状态;
                               {"paramCode": "PARAMCODE3","paramIndex": "2","paramValue": "10"},

                               ... ...
                              ]
                }

某个灯举例:
                {
                    "params": [ {"paramCode": "powerStatus","paramValue": "1"},  // 开灯
                               {"paramCode": "brightness","paramValue": "50"},   // 亮度50
                               ... ...
                              ]
                }

输出参数eventType,默认最大空间为MAX_FUNCSTR_LEN=32B;

************************************************************************/
int demo_dn_send_cmd_callback(RESP_MODE_e mode, dn_dev_ctrl_frame_t *ctrlFrame, char *eventType, char *respData, unsigned int respBufSize)
{
    DEMO_DBG_PRINT("[demo]dn_send_cmd_callback(%s),mode=%d(0:no; 1:async; 2:sync;)\r\n", ctrlFrame->function, mode);
    // TODO
    int funcIndex = 0;

    char *localEventType = NULL;
    char localResp[256] = { 0 };
    unsigned long respSize = respBufSize;

    // 处理管控指令
    if (0 != checFunctionIndex(ctrlFrame->function, &funcIndex))
    {
        goto FAIL_EXIT;
    }
    switch (funcIndex)
    {
    case 0: // Control
        DEMO_DBG_PRINT("andlink: Control\n");
        // 处理控制指令
        doControlCmd(ctrlFrame->data);

        doDnControlCmdResponse(ASYNC_MODE, ctrlFrame, eventType, g_dnControlRespData, respBufSize);
        break;

    case 5: // Query指令
        DEMO_DBG_PRINT("andlink: Query\n");
        // 处理查询指令
        doDnQueryCmdResponse(mode, ctrlFrame, eventType, respData, respBufSize);
        break;

    case 2: // SelfDetect//自检
        DEMO_DBG_PRINT("andlink: SelfDetect\n");
        // 处理自检指令 TODO
        doDnSelfDetectCmdResponse(mode, ctrlFrame, eventType, respData, respBufSize);
        break;

    case 3: // Reboot//重启,此接口最好必须实现,用于通过远程重启规避某些严重的现网问题.
        DEMO_DBG_PRINT("andlink: Reboot\n");
        // 指定设备重启
        break;

    case 1: // Unbind指令,设备解绑通知,设备根据需要决定是否使用
        DEMO_DBG_PRINT("andlink: Unbind\n");
        dn_send_cmd_status = 1;
        // void* nodePtr = createAndlinkNode();
        // if (nodePtr) {
        //     callAndlinkNodeDoSomething(nodePtr);
        //     // destroyAndlinkNode(nodePtr);
        // }
        break;

    case 4: // Bind指令,设备绑定通知,设备根据需要决定是否使用
        DEMO_DBG_PRINT("andlink: Bind\n");
        break;

#if ANDLINK_DEBUG_ENABLE == 4
    case 6: // DebugControl指令,用于SDK研发者调试,SDK使用者无需关心
        DEMO_DBG_PRINT("andlink: DebugControl\n");
        doDebugCmd(ctrlFrame->data);
        break;
#endif
    default:
        goto FAIL_EXIT;
    }
    return 0;
FAIL_EXIT:
    return -1;
}

// 测试上报功能参数1
int demoDevParamsReport()
{
    // 设备状态参数上报 参考代码
    char data[1024] = { 0 };
    // 举例1的data格式: 设备主动上报版本号的格式
    // char *format1 = "{\"params\": [{\"paramCode\": \"softVersion\",\"paramValue\": \"%s\"}]}";

    // 举例2的data格式: 单端口单属性的灯设备 上报状态开
    char *format2 = "{\"params\": [{\"paramCode\": \"outletStatus\",\"paramValue\": \"0\"}]}";

    // 举例3的data格式: 单端口的2个属性状态同时上报
    // char *format3 = "{\"params\": [ {\"paramCode\": \"powerStatus\",\"paramValue\": \"1\"},{\"paramCode\": \"brightness\",\"paramValue\": \"0\"} ]}";

    // 举例4的data格式: 多端口插排设备 上报状态; 上报第一个插孔状态;
    // char *format4 = "{\"params\": [{\"paramCode\": \"powerStatus\",\"paramIndex\": \"1\",\"paramValue\": \"1\"}]}";

    // 举例5的data格式: 多端口插排设备 上报状态; 上报第一个插孔状态和第二个插孔状态;
    // char *format5 = "{\"params\": [{\"paramCode\": \"powerStatus\",\"paramIndex\": \"1\",\"paramValue\": \"1\"},{\"paramCode\": \"powerStatus\",\"paramIndex\": \"2\",\"paramValue\": \"1\"}]}";

    // 举例6的data格式: 单端口设备的多个属性同时上报
    /*
    {"params":[
        {"paramCode":"softVersion","paramValue":"1.10.0009"},
        {"paramCode":"firmware","paramValue":"1.10.0009"},
        {"paramCode":"STATE","paramValue":"1"},
        {"paramCode":"brightness","paramValue":"0"},
        {"paramCode":"powerStatus","paramValue":"0"},
        {"paramCode":"lightMode","paramValue":"0"},
        {"paramCode":"countDown","paramValue":"0"},
        {"paramCode":"AISwitch","paramValue":"1"},
        {"paramCode":"delaySwitch","paramValue":"0"},
        {"paramCode":"DelaySwitch","paramValue":"0"},
        {"paramCode":"strongLightProtect","paramValue":"0"}
        ]}
    */

    /*
    // 测试环境:产测demo测试
    char *format6 = "{\"params\": [ {\"paramCode\": \"batteryCapacity\",\"paramValue\": \"95\"},\
    {\"paramCode\": \"indoorTemperature\",\"paramValue\": \"25.0\"},\
    {\"paramCode\": \"mode\",\"paramValue\": \"auto\"},{\"paramCode\": \"software\",\"paramValue\": \"A\"},\
    {\"paramCode\": \"switch\",\"paramValue\": \"0\"}]}";

    // 生产环境:产测demo测试
    char *format7 = "{\"params\" : [ {\"paramCode\" : \"boolTest\", \"paramValue\" : \"0\"},{\"paramCode\" : \"doubleTest\", \"paramValue\" : \"1.0\"}, {\"paramCode\" : \"enumTest\", \"paramValue\" : \"0\"},{\"paramCode\" : \"floatTest\", \"paramValue\" : \"1.1\"}, {\"paramCode\" : \"intTest\", \"paramValue\" : \"1\"},{\"paramCode\" : \"stringTest\", \"paramValue\" : \"a\"}, {\"paramCode\" : \"motionSwitch\", \"paramValue\" : \"0\"} ]}";

    char *format8 = "{ \"params\" : [ {\"paramCode\" : \"boolTest\",\"paramValue\" : \"1\"}, {\"paramCode\" : \"doubleTest\", \"paramValue\" : \"20\"}, \
    {\"paramCode\" : \"enumTest\", \"paramValue\" : \"2\"}, {\"paramCode\" : \"floatTest\", \"paramValue\" : \"1.1\"}, \
    {\"paramCode\" : \"intTest\", \"paramValue\" : \"1\"}, {\"paramCode\" : \"stringTest\", \"paramValue\" : \"a\"}, \
    {\"paramCode\" : \"motionSwitch\", \"paramValue\" : \"0\"} ] }";
    */
    char *format = NULL;
    format = format2;
    // app_snprintf_s(data, sizeof(data) - 1, format, "0");
    app_strncpy_s(data, sizeof(data) - 1, format, app_strlen(format));

#if 1
    // 上报1:设备主动上报
    if (0 == devDataReport("Inform", NULL, 0, data, app_strlen(data)))
    {
        DEMO_DBG_PRINT("devDataReport Suc,data =%s\n\n", data);
    }
#else
    // 上报2:测试数据上报成功率 for test
    int i, num = 1;
    for (i = 0; i < num; i++)
    {
        char test[16] = { 0 };
        app_snprintf_s(test, sizeof(test) - 1, "Vtest0.%d", i);
        app_snprintf_s(data, sizeof(data) - 1, format, test);

        // 上报2:设备因用户按键或下行管控触发的数据上报
        if (0 == devDataReport("ParamChange", NULL, 0, data, app_strlen(data)))
        {
            DEMO_DBG_PRINT("devDataReport Suc,data =%s\n\n", data);
        }
    }
#endif

// wifi 感知使能
#ifdef SDK_USING_DEVICE_FUNCS_ENABLE
    // for SOS报警器 21.10.22
    // 向局域网内移动生态的路由器 上报wifi感知功能开启;这里仅是一个样例,具体paramCode和paramValue 可以在连楹家庭智慧平台门户定义,并且在端到端进行约定.
    int wifiSensorEnable = 1;
    char *format11 = "{\"mac\":\"%s\",\"params\": [{\"paramCode\":\"wifiSensor\",\"paramValue\":\"%d\"}]}";
    memset(data, 0, sizeof(data));
    app_snprintf_s(data, sizeof(data) - 1, format11, DEVICE_WLAN_MAC, wifiSensorEnable);
    local_devDataReport(NULL, NULL, "Inform", NULL, data, app_strlen(data));
#endif

    return 0;
}

// 测试启动任务上报功能参数2
void *paramsSyncTaskHandler(void *arg)
{
    (void)arg;
    demoDevParamsReport();
    return (void *)0;
}

/************************************************************************
Description:    SDK调用,通知设备进行参数同步;
Input:          None
Output:         None
Return:         成功:0, 失败:-1
Others:         设备需要调用devDataReport上报一次设备属性信息
                sdk调用时机:设备MQTT上线成功;预绑定方案,设备被真实绑定时.
************************************************************************/
int demo_dev_paramsSync_callback()
{
    // 此接口中可以通过getDeviceInfoStr接口查看设备的真实绑定状态,1表示绑定.
    DEMO_DBG_PRINT("[demo]dev_paramsSync_callback,userBind =%s.\r\n", getAdlDeviceInfoStr(ADL_BIND_STATUS));
    // TODO

#if 1
    // 直接上报参数
    demoDevParamsReport();
#else
    // 测试,创建多个任务上报参数
    int i, num = 10;
    for (i = 0; i < num; ++i)
    {
        pthread_t tid;
        int ret = pthread_create(&tid, NULL, paramsSyncTaskHandler, NULL);
        if (0 == ret)
        {
            DEMO_DBG_PRINT("start paramsSyncTask[%d] OK\r\n", i);
            pthread_detach(tid);
        }
    }
#endif
    return 0;
}