/*
 *
 * 文件名称：andlink_app_test.c
 * 说 明:厂商集成andlink SDK时,对SDK的原子能力如何使用的示例接口或进行功能及稳定性测试的接口
 *
 */
#include "andlink_adapt.h"

#if ANDLINK_DEBUG_ENABLE != 0
/************************************************************************
Description:    获取andlink提供的connectToken接口
Input:          None
Output:         None
Return:         connectToken字符串
Others:         anlink注册成功后,才能获取到
************************************************************************/
void facGetConnectToken()
{
    char *connectToken = getAdlDeviceInfoStr(ADL_CONNECT_TOKEN);
    DEMO_DBG_PRINT("facGetConnectToken =%s\r\n", connectToken);
}

/************************************************************************
Description:    andlink SDK启动后,随机选择时间点,验证扫码绑定
Input:          int msBegin, int msEnd  单位毫秒
Output:         None
Return:         成功:0, 失败:-1
Others:         测试接口,SDK使用者无需关心
************************************************************************/
void test_randInputUserkey(int msBegin, int msEnd)
{
    int num = 10;
    for (int i = 0; i < num; i++)
    {
        int begin = msBegin;
        int end = msEnd;
        srand((unsigned int)time(NULL));

        int msNum = rand() % (end - begin + 1) + begin;

        int u_num = msNum * 1000; // us

        usleep(u_num);

        DEMO_DBG_PRINT("========demo scan code success,rand sleeped %d ms\r\n", msNum);

        sleep(1);
    }
}

/************************************************************************
Description:    andlink SDK启动后,随机选择时间点,验证复位流程
Input:          int msBegin, int msEnd  单位毫秒
Output:         None
Return:         成功:0, 失败:-1
Others:         测试接口,SDK使用者无需关心
************************************************************************/
void test_devReset(int msBegin, int msEnd)
{
    int begin = msBegin;
    int end = msEnd;
    srand((unsigned int)time(NULL));

    int msNum = rand() % (end - begin + 1) + begin;

    int u_num = msNum * 1000; // us

    usleep(u_num);

    DEMO_DBG_PRINT("========demo test devReset %d ms\r\n", msNum);

    devReset();
}

/************************************************************************
Description:    调试功能处理
Input:          char *paramIndex, char *paramCode, char *paramValue
Output:         None
Return:         成功:0, 失败:-1
************************************************************************/
static int degbugCmdProc(char *paramIndex, char *paramCode, char *paramValue)
{
    if ((NULL == paramIndex) || (NULL == paramCode) || (NULL == paramValue))
    {
        return -1;
    }

    DEMO_DBG_PRINT("degbugCmdProc[%s][%s][%s]\n", paramIndex, paramCode, paramValue);

    switch (atoi(paramIndex))
    {
    case 2000: // 强制进入手工配网
        if (0 == strcmp(paramCode, "manualCfgnet"))
        {
            DEMO_DBG_PRINT("startManualCfgNet\n");
#ifdef ANDLINK_CFG_NET_ENABLE
            startManualCfgNet();
#endif
        }
        break;

    case 2001: // 获取andlink绑定状态
        if (0 == strcmp(paramCode, "bindStatus"))
        {
            DEMO_DBG_PRINT("facGetAndlinkBindStatus =%s\r\n", getAdlDeviceInfoStr(ADL_BIND_STATUS));
        }
        break;

    case 2002: // 获取connectToken
        if (0 == strcmp(paramCode, "connectToken"))
        {
            DEMO_DBG_PRINT("facGetConnectToken =%s\r\n", getAdlDeviceInfoStr(ADL_CONNECT_TOKEN));
        }
        break;

    case 2003: // 执行子设备注册
        if (0 == strcmp(paramCode, "childBootstrap"))
        {
            char deviceToken[MAX_GW_TOKEN_LEN + 1];
            char dmToken[MAX_DM_TOKEN_LEN + 1];
            childDevBootstrap("110C29F9E488", "30135", "haNX13Bo0TPggyoI", deviceToken, dmToken);
        }
        break;

    case 2004: // 执行子设备上线
        if (0 == strcmp(paramCode, "childBoot"))
        {
            childDevBoot("110C29F9E488", "30135", "SV1.0", "FV1.0");
        }
        break;

    default:
        return -1;
    }
    return 0;
}

/************************************************************************
Description: 执行特定调试指令
Input: char *data
Output: NONE
Return: 成功:0, 失败:-1
Others:
************************************************************************/
int doDebugCmd(char *data)
{
    cJSON *root = NULL;
    char paramIndex[8] = { 0 };
    char paramCode[16] = { 0 };
    char paramValue[8] = { 0 };
    if (!(root = adl_cJSON_Parse(data)))
    {
        DEMO_DBG_PRINT_ERROR("[adl_cJSON_Parse]Error.[%s]\n", adl_cJSON_GetErrorPtr());
        return -1;
    }

    cJSON *arrayItem = NULL;
    if (!(arrayItem = adl_cJSON_GetObjectItem(root, "params")))
    {
        DEMO_DBG_PRINT_ERROR("[adl_cJSON_GetObjectItem]Error.[%s]\n", adl_cJSON_GetErrorPtr());
        goto FAIL_EXIT;
    }
    cJSON *paramCodeItem = NULL;
    int size = adl_cmhiJSON_GetArraySize(arrayItem);
    for (int i = 0; i < size; i++)
    {
        memset(paramIndex, 0, sizeof(paramIndex));
        memset(paramCode, 0, sizeof(paramCode));
        memset(paramValue, 0, sizeof(paramValue));
        paramCodeItem = adl_cmhiJSON_GetArrayItem(arrayItem, i);

        if (NULL == paramCodeItem)
        {
            continue;
        }

        if (0 != adl_cmhiJSON_GetValueString(paramCodeItem, "paramIndex", paramIndex, sizeof(paramIndex)))
        {
            DEMO_DBG_PRINT_ERROR("parse paramIndex failed\n");
            continue;
        }
        if (0 != adl_cmhiJSON_GetValueString(paramCodeItem, "paramValue", paramValue, sizeof(paramValue)))
        {
            DEMO_DBG_PRINT_ERROR("parse paramValue failed\n");
            continue;
        }
        if (0 != adl_cmhiJSON_GetValueString(paramCodeItem, "paramCode", paramCode, sizeof(paramCode)))
        {
            DEMO_DBG_PRINT_ERROR("parse paramCode failed\n");
            continue;
        }
        if (0 != adl_cmhiJSON_GetValueString(paramCodeItem, "paramValue", paramValue, sizeof(paramValue)))
        {
            DEMO_DBG_PRINT_ERROR("parse paramValue failed\n");
            continue;
        }
        DEMO_DBG_PRINT("paramIndex =%s, paramCode =%s, paramValue =%s\n", paramIndex, paramCode, paramValue);
        degbugCmdProc(paramIndex, paramCode, paramValue);
        break;
    }
    adl_cJSON_Delete(root);
    return 0;

FAIL_EXIT:
    adl_cJSON_Delete(root);
    return -1;
}
#endif // ANDLINK_DEBUG_ENABLE!=0

/************************************************************************
Description: 调试andlink demo程序
Input: int argc, char **argv
Output: None
Return: 成功:0, 失败:-1
Others: 测试接口,供SDK的开发者调试; SDK使用者无需关心
************************************************************************/
int debug_demo_main(int argc, char **argv)
{
    DEMO_DBG_PRINT("[enter]debug_demo_main,argc =%d,argv[0] =%s\r\n", argc, argv[0]);
    // sleep(2);

// 1.调试复位
#if ANDLINK_DEBUG_ENABLE == 1
    demo_main(argc, argv);
    // 1.1 andlink SDK启动后,取20-200ms的随机时刻,验证扫码绑定;
    // test_randInputUserkey(20, 200);

    // 1.2 andlink SDK启动后,取2s-5s的随机时刻,验证复位流程;
    test_devReset(5000, 10000);

// 2.dmalloc调试内存泄漏
#elif ANDLINK_DEBUG_ENABLE == 2
    void test_andlinkInit();
    void main_dmalloc_test();

    test_andlinkInit();
    main_dmalloc_test();

    while (1)
    {
        sleep(10);
    }

// 3.valgrind调试内存泄漏
#elif ANDLINK_DEBUG_ENABLE == 3
    void test_andlinkInit();
    test_andlinkInit();

// 4.通过调试控制命令,调用SDK的一系列能力,进行测试
#elif ANDLINK_DEBUG_ENABLE == 4
    if (0 == demo_main(argc, argv))
    {
        while (1)
        {
            sleep(10);
        }
    }
#else
#endif
    return 0;
}