/*
 * Copyright (c) 2019,中移(杭州)信息技术有限公司
 * All rights reserved.
 *
 * 文件名称：andlink_pub.h
 * 说 明：andlinkSDK 对外接口信息
 *        !!!强烈注意:此文件由SDK维护,厂商只可引用,不可修改.!!!
 * 初始版本：1.0
 * 作 者：罗武通
 * 完成日期：2019年4月29日
 *
 * 取代版本：1.4
 * 原作者 ：wuhao
 * 完成日期：2020年02月01日
 *
 * 取代版本：1.6
 * 原作者 ：wuhao
 * 完成日期：2023年07月31日
 *
 * 取代版本：1.6.5
 * 原作者 ：wuhao
 * 完成日期：2023年09月01日
 *
 * 取代版本：1.6.6
 * 原作者 ：wuhao
 * 完成日期：2023年10月20日
 *
 * 取代版本：1.6.7
 * 原作者 ：wuhao
 * 完成日期：2024年02月07日
 *
 * 取代版本：1.7.1
 * 原作者 ：wuhao
 * 完成日期：2024年05月17日
 * 接口修改:
 * 1.新增了BLE配网相关接口.
 * 2.新增了配网语音通知接口.
 *
 * 取代版本：1.7.2
 * 原作者 ：wuhao
 * 完成日期：2024年07月17日
 * 接口修改:
 * 1.修改了Wi-Fi扫描回调接口scan_wifi_callback出参.
 * 2.对外提供了禁止闪联及手工配网的接口.
 *
 * 取代版本：1.8.0
 * 原作者 ：wuhao
 * 完成日期：2025年01月08日
 * 接口修改:
 * 1.设备新规范版本,对外接口有较大更改.
 */

#ifndef __ANDLINK_PUB_H
#define __ANDLINK_PUB_H

#include "cJSON.h"

#ifdef __cplusplus
extern "C" {
#endif

/*
启动的SOFT AP要求满足:
热点名称:CMQLINK-{$deviceType}-****,devieType 为设备在开发者门户申请的设备类型码;****为设备产生的随机四位码，一般是 MAC 地址后缀
通道网络配置:支持 DHCP,设备地址为 *************
通道热点认证方式:广播、 开放式接入
*/

// SOFT AP 默认地址;AP_IP_ADDR,支持DHCP
#define AP_IP_ADDR "*************"

// Andlink对外API接口版本号,若对外接口变化,此字段将会变化
#define ADL_EXPORT_API_VERSION 1

// 设备基本信息相关的宏
#define MAX_DEVICE_TYPE_LEN   15 // 设备产品ID的长度
#define MAX_IPSTR_LEN         45 // ipv4=16 ipv6=46
#define MAX_FUNCSTR_LEN       32 // 管控命令function长度
#define MAX_DEV_ID_LEN        63 // 设备ID长度
#define MAX_SEQID_LEN         31 // 管控流水号长度
#define MAX_BUSINESS_TYPE_LEN 8  // 业务类型最大长度

// 设备令牌长度
#define MAX_GW_TOKEN_LEN      16 // andlinkGWToken长度
#define MAX_ANDLINK_TOKEN_LEN 16 // andlinkToken长度
#define MAX_PRODUCT_TOKEN_LEN 16 // productToken长度
#define MAX_DM_TOKEN_LEN      88 // dmToken最大长度
#define MAX_CONNECT_TOKEN_LEN 32 // connectToken最大长度

// 三码一密最大长度
#define MAX_MAC_LEN      17
#define MAX_CMEI_LEN     15
#define MAX_SN_LEN       25
#define MAX_AUTH_KEY_LEN 72 // authKEY最大长度

// wifi控制相关的宏
#define MAX_SSID_LEN    32
#define MAX_PASSWD_LEN  63
#define MAX_ENCRYPT_LEN 16
#define MAX_RSSI_LEN    4

// WiFi控制选项
typedef enum
{
    WIFI_OPT_STA_START = 1, // 表示打开STA模式(连接热点)
    WIFI_OPT_STA_STOP = 2,  // 表示关闭STA模式(断开热点)
    WIFI_OPT_AP_START = 3,  // 表示打开AP模式(启动SoftAP热点)
    WIFI_OPT_AP_STOP = 4,   // 表示关闭AP模式(关闭SoftAP热点)
    WIFI_OPT_NET_STORE = 5, // 表示存储网络信息
    WIFI_OPT_NET_CLEAR = 6, // 表示清除网络信息
    WIFI_OPT_NET_GET = 7    // 表示获取当前连接的网络信息
} WIFI_CTRL_OPT_e;

// 二维码等扫描控制选项
typedef enum
{
    SCAN_OPT_QRCODE_START = 1, // 表示启动二维码扫描
    SCAN_OPT_QRCODE_STOP = 2,  // 表示关闭二维码扫描
    SCAN_OPT_MAX
} SCAN_CTRL_OPT_e;

// 产品所处模式,SDK调用者一般无需关心研发者调试模式
typedef enum
{
    PRODUCTION_RUNNING_MODE = 0, // 生产运行模式
    FACTORY_TEST_MODE,           // 工厂测试模式
} PRODUCT_MODE_e;

// 配网模式
typedef enum
{
    NETWOKR_MODE_WIRED = 0x01,                     // 表示自发现入网的有线设备
    NETWOKR_MODE_WIFI = 0x02,                      // 表示移动闪联+手工配网(SoftAP)的WI-FI设备
    NETWOKR_MODE_4G,                               // 表示4G入网的设备
    NETWOKR_MODE_BT,                               // 表示移动闪联+手工配网(BLE)的WI-FI设备
    NETWOKR_MODE_WIFI_SCAN_QRCode,                 // 表示移动闪联+手工配网(摄像头扫码配网)的WI-FI设备(设备扫APP二维码)
    NETWOKR_MODE_OTHERS,                           // 表示其他配网设备
    NETWOKR_MODE_WIRED_SCREEN,                     // 表示自身带屏闪联配网的有线设备
    NETWOKR_MODE_WIRED_SCAN_QRCode,                // 表示摄像头扫码配网的有线设备(APP扫设备二维码)
    NETWOKR_MODE_WIFI_SCAN_QRCode_BLE_CONCURRENCY, // 表示手工配网(摄像头扫码配网+BLE配网共存)的WI-FI设备
    NETWOKR_MODE_MAX
} CFG_NET_MODE_e;

// 手动配网模式类型
typedef enum
{
    MANUAL_CONFIG_NET_SOFTAP = 1,
    MANUAL_CONFIG_NET_BLE,
    MANUAL_CONFIG_NET_QRCode,
    MANUAL_CONFIG_NET_QRCode_BLE_CONCURRENCY
} ADL_MANUAL_CONFIG_NET_TYPE_e;

// 下行控制命令响应模式
typedef enum
{
    NORSP_MODE = 0, // 无需响应
    ASYNC_MODE = 1, // 异步响应,采用devDataReport进行响应
    SYNC_MODE = 2,  // 同步响应,下行管控函数的输出参数进行响应,一般用户设备接入本地网关,不常用
} RESP_MODE_e;

// 设备的andlink核心状态
typedef enum
{
    ADL_INIT,
    ADL_CFGNET,                // 配网状态
    ADL_CFGNET_NETINFO,        // 配网状态 收到工作热点信息
    ADL_CFGNET_REJECT_NETINFO, // 配网状态 收到拒绝入网信息
    ADL_CFGNET_SUC,            // 配网成功状态
    ADL_CFGNET_FAIL,           // 配网失败状态
    ADL_BOOTSTRAP,             // 设备开始注册状态
    ADL_BOOTSTRAP_SUC,         // 设备注册成功状态
    ADL_BOOTSTRAP_FAIL,        // 设备注册失败状态
    ADL_BOOT,                  // 设备开始上线状态
    ADL_BOOT_SUC,              // 设备上线成功状态
    ADL_BOOT_FAIL,             // 设备上线失败状态
    ADL_ONLINE,                // 设备在线状态
    ADL_OFFLINE,               // 设备离线状态
    ADL_REBIND,                // 设备重新绑定,有线设备以此为时机,用于实现绑定失败的逻辑
    ADL_BOUND,                 // 设备完成绑定
    ADL_UNBIND,                // 设备解绑
    ADL_STATE_MAX
} ADL_DEV_STATE_e;

// Andlink扩展功能提示
typedef enum
{
    ADL_LOG_SYNC,           // 可选, 日志同步, 设备可以根据需要进行andlink日志文件的备份;
    ADL_ERRNO_GET,          // 可选, 提醒厂商码获取错误, 设备可以根据需要获取andlink运行的错误码;
    ADL_POPUP_WINDOW,       // 可选，有线带屏设备必须设置，有线带屏设备的屏幕弹窗，提醒用户进行入网确权;
    ADL_OVD_USERID_GET,     // 可选，提醒厂商获取安防userID的时机，用于新sdk老二维码配网场景;
    ADL_OFF_PWD_SYNC_START, // 可选，提醒厂商密码同步开始时间点，防止低功耗设备休眠打断密码同步;
    ADL_OFF_PWD_SYNC_END,   // 可选，提醒厂商密码同步结束时间点，防止低功耗设备休眠打断密码同步;
    ADL_EXT_NOTIFY_MAX
} ADL_DEV_EXTFUNC_NOTIFY_e;

// 设备语音提示
typedef enum
{
    /* 闪联配网语音提示 */
    ADL_NET_FLASHLINK_START_CONNECTING,    // 闪联配网认证中
    ADL_NET_FLASHLINK_GUIDE_AP_FAILED,     // 引导热点不存在或连接引导热点失败
    ADL_NET_FLASHLINK_GUIDE_AP_CONN_SUC,   // 连接引导热点成功
    ADL_NET_FLASHLINK_WORK_AP_CONN_FAILED, // 连接工作热点失败
    ADL_NET_FLASHLINK_WORK_AP_CONN_SUC,    // 连接工作热点成功

    /* 手工配网语音提示 */
    ADL_NET_MANUAL_CONNECTING = 10,     // 等待连接
    ADL_NET_MANUAL_GET_NETINFO_SUC,     // 获取配网信息成功
    ADL_NET_MANUAL_WORK_AP_CONN_FAILED, // 连接工作热点失败
    ADL_NET_MANUAL_WORK_AP_CONN_SUC,    // 连接工作热点成功

    /* 绑定语音提示 */
    ADL_BIND_FAIL = 100, // 绑定失败
    ADL_BIND_SUC,        // 绑定成功
    ADL_VOICE_NOTIFY_MAX
} ADL_DEV_VOICE_NOTIFY_e;

// SDK对外开放查询的设备属性信息
typedef enum
{
    ADL_CONNECT_TOKEN, // 获取设备连接业务平台的可信凭据connectToken;
    ADL_DEVICE_ID,     // 获取andlink设备唯一ID(deviceId),正常格式为CMCC-${deviceType}-${id};
    ADL_USER_KEY,      // 获取userkey,默认值为CMCC-${deviceVendor}-${deviceType}
    ADL_BIND_STATUS,   // 获取设备的绑定状态.1,绑定;2为激活;0为非绑定;
    ADL_OVD_USERID,    // 获取安防userId;
    ADL_DEV_ATTR_MAX
} EXPORT_DEVICE_ATTRS_e;

// ANDLINK默认开启的功能点,取值1,2,4,8,16...
typedef enum
{
    // 最多32个元素,每个元素表示一个功能
    ADL_APP_SEARCH_SERVICE = 0x01,              // APP发现服务;
    ADL_OFFLINE_UNBIND_AUTO_REBOOTSTRAP = 0x02, // 离线解绑再次自动注册功能
    ADL_FLASHLINK_CONFIG_NET = 0x04,            // 闪联配网功能
    ADL_MANUAL_CONFIG_NET = 0x08,               // 手工配网功能(SoftAP配网或BLE配网)
    ADL_DEF_EN_FUNC_MAX = 0x80000000
} ADL_DEF_ENABLED_FUNCS_e;

// ANDLINK默认关闭的功能点,取值1,2,4,8,16...
typedef enum
{
    // 最多32个元素,每个元素表示一个功能
    ADL_DNS_CACHE = 0x01,           // DNS缓存使能;
    ADL_BUSINESS_CONN_FIRST = 0x02, // 业务连接优先使能;
    ADL_DEF_DIS_FUNC_MAX = 0x80000000
} ADL_DEF_DISABLED_FUNCS_e;

#ifdef SDK_USING_DEVICE_FUNCS_ENABLE
// device的功能点
typedef enum
{
    // 最多32个元素,每个元素表示一个功能
    DEVICE_WIFI_SENSOR_ENABLE = 0x01, // wifi感知使能;
    DEVICE_XX1 = 0x02,
    DEVICE_XX2 = 0x04
} ADL_DEVICE_FUNCS_e;
#endif

// wifi控制接口
typedef struct
{
    char ssid[MAX_SSID_LEN + 1];
    char password[MAX_PASSWD_LEN + 1];
    char encrypt[MAX_ENCRYPT_LEN + 1];
    int channel;
    int type;                    // 热点类型,0是工作热点;1是配网引导热点
    char mac[MAX_MAC_LEN + 1];   // 若strlen(mac)不为0,说明Bssid唯一,即连接指定热点;
    char rssi[MAX_RSSI_LEN + 1]; // 若strlen(rssi)为0,说明是输出参数,连接热点成功后,输出该热点的信号强度(可选).
} wifi_cfg_info_t;

// 下行控制指令帧结构
typedef struct
{
    char function[MAX_FUNCSTR_LEN + 1];
    char deviceId[MAX_DEV_ID_LEN + 1];
    char childDeviceId[MAX_DEV_ID_LEN + 1];
    char seqId[MAX_SEQID_LEN + 1];
    unsigned int dataLen;
    char *data;
} dn_dev_ctrl_frame_t;

// BLE 配网:btgatt-server结构
typedef struct
{
    unsigned char localName[16];                   // BLE外设名称
    unsigned char localNameLen;                    // BLE外设名称 长度
    unsigned char scanRespData[31];                // BLE扫描请求的响应内容
    unsigned char scanRespDataLen;                 // BLE扫描请求的响应内容 长度
    unsigned char advData[31];                     // BLE部分广播内容,仅包含发现模式和厂商数据2个属性(共12B)
    unsigned char advDataLen;                      // BLE部分广播内容 长度
    unsigned short serviceUUID;                    // 固定,上行数据和下行数据都使用该Service UUID
    unsigned short characteristicUUID_Down_Write;  // APP->设备数据帧
    unsigned short characteristicUUID_Up_Notify;   // 设备->APP状态通知
    unsigned short characteristicUUID_Up_Indicate; // 设备->APP指示
    unsigned short duration;                       // BLE广播发送持续时间,单位秒
    unsigned short timeout;                        // BLE广播发送间隔,单位秒
} adl_ble_gatt;

// 启动SDK的设备属性接口集合
typedef struct
{
    unsigned int version;      // 版本标识,便于向前兼容设计
    CFG_NET_MODE_e cfgNetMode; // 配网方式;
    char *deviceVendor;        // 厂商名称,使用连楹家庭智慧平台分配的厂商编码表示;
    char *deviceType;          // 设备厂商在连楹家庭智慧平台门户注册的产品的产品类型码;即连楹家庭智慧平台门户上创建完产品后,生成的 产品ID;
    char *id;                  // 设备自身唯一标识,建议使用sn,IoT平台以此分配Andlink体系的设备唯一标识deviceId
    char *andlinkToken;        // 设备厂商在连楹家庭智慧平台门户注册的产品的平台验证码
    char *productToken;        // 设备厂商在连楹家庭智慧平台门户注册的产品的产品验证码
    char *firmWareVersion;     // 设备固件版本号
    char *softWareVersion;     // 设备软件版本号
    char *cfgPath;             // andlink配置文件存储路径,此路径需要可读写,断电不丢失;
} adl_dev_attr_t;

// 启动SDK的回调接口集合,SDK的使用者注册、实现,SDK自身调用
typedef struct
{
    unsigned int version; // 版本标识,便于向前兼容设计

    // 按需实现;扫描指定热点列表
    int (*scan_wifi_callback)(wifi_cfg_info_t *wificfg, cJSON *outApMsg);

    // 按需实现;控制WIFI(连接、断开热点;启动、关闭热点;保存、清除热点配置);
    int (*ctrl_wifi_callback)(WIFI_CTRL_OPT_e opt, wifi_cfg_info_t *wificfg, char *outMsg, unsigned int msgBufSize);

    // 按需实现;通知设备Andlink核心状态
    int (*set_andlink_status_callback)(ADL_DEV_STATE_e state);

    // 按需实现;Andlink扩展功能提示,通知设备执行某些特殊操作
    int (*set_extfunc_notify_callback)(ADL_DEV_EXTFUNC_NOTIFY_e type);

    // 按需实现,平台对接的设备不必实现;下行管控;
    int (*dn_send_cmd_callback)(RESP_MODE_e mode, dn_dev_ctrl_frame_t *ctrlFrame, char *eventType, char *respData, unsigned int respBufSize);

    // 按需实现,平台对接的设备不必实现;
    // 通知设备参数同步;sdk上线成功后调用;
    int (*dev_paramsSync_callback)();

    // 按需实现;平台接入的设备不必实现; 若需要支持andlink OTA,此接口实现是可选的,若没实现此接口,则SDK内部实现下载.
    // OTA方案1:下载并升级版本; childDevId为空表示父设备,否则表示子设备;
    int (*download_upgrade_version_callback)(char *childDevId, char *downloadurl, char *filetype, char *md5, unsigned int filesize);

    // 按需实现;平台接入的设备不必实现;若需要支持andlink OTA,此接口必须实现.
    // OTA方案2:升级版本; childDevId为空表示父设备,否则表示子设备;
    int (*upgrade_version_callback)(char *childDevId, char *filename, char *filetype);

    // 必须实现;获取设备IP;sdk以此判断设备是否联网;SDK调用时,两个参数分配的缓存长度为46B
    int (*get_device_ipaddr)(char *ip, char *broadAddr);
    // 按需实现;复位设备IP;平台对接的设备不必实现;
    int (*reset_device_Ipaddr)(void);

    // 按需实现; 若不实现此接口,sdk使用内部实现的读配置项接口;
    // 读配置项的接口; 举例getCfg("deviceId", a, sizeof(a));
    int (*getCfg_callback)(char *item, char *value, unsigned int bufsize);

    // 按需实现; 若不实现此接口,sdk使用内部实现的写配置项接口;
    // 写配置项的接口;举例SetCfg("deviceId","CMCC-10086-666677778888");
    int (*setCfg_callback)(char *item, char *value);

    // 必须实现;DM信息获取;add by 21.02.19
    int (*get_dmInfo_callback)(char *childDeviceId, cJSON *root);

    // 必须实现;Andlink动态扩展信息获取;add by 23.03.03;
    int (*get_extInfo_callback)(char *childDeviceId, cJSON *root);

    // 按需实现;设备语音提示
    int (*set_voice_notify_callback)(ADL_DEV_VOICE_NOTIFY_e type, char *msg);

    // 按需实现;通知设备启动BLE配网
    int (*ble_start_server_callback)(adl_ble_gatt *gattServer);

    // 按需实现;通知设备停止BLE配网
    int (*ble_stop_server_callback)(void);

    // 按需实现;通知设备发送BLE数据
    int (*ble_send_callback)(char *data, unsigned int datalen);

    // 按需实现;通知设备控制扫码配网
    int (*ctrl_scanCode_callback)(SCAN_CTRL_OPT_e opt);
} adl_dev_callback_t;

// 必须调用; sdk 启动(sdk中devAttr,devCbs增加字段,不支持向上兼容,故sdk此接口变化导致的升级,应用程序也需要跟着升级)
int andlink_init(adl_dev_attr_t *devAttr, adl_dev_callback_t *devCbs);

// 设备复位,有恢复出厂或强制复位机制时,必须调用;
int devReset(void);

// 用户按键启动手工配网时,必须调用;
int startManualCfgNet(ADL_MANUAL_CONFIG_NET_TYPE_e type);

// 查询设备相关属性的接口;按需调用;
char *getAdlDeviceInfoStr(EXPORT_DEVICE_ATTRS_e attr);

// 按需调用,平台对接的设备不会调用;子设备DM数据上报接口;注意token的内容从childDevBootstrap接口的出参outAndlinkToken获取; add by 22.09.26
int childDevDmReport(char *childDeviceId, cJSON *root, char *token);

// 按需调用;记录日志
// fid:0表示SDK,1表示设备
// logLevel: 0x00,不输出日志;0x01：单次关键日志，0x02：错误日志，0x04：告警1级，0x08：告警2级，0x10：普通日志，0x20：周期1级；0x40:周期2级;0x80:私密级别;
int printLog(int fid, int logLevel, const char *fmt, ...);

// 设置printLog日志接口记录日志的级别;
// 强烈注意,此接口仅用于厂商调试阶段;正式版本使用,可能会影响andlink的诊断功能.
int set_printLog_debug_level(int fid, int logLevel, char *logTo);

// 获取andlink SDK运行过程中的错误码,类似linux系统的errno机制,用于SDK使用者诊断andlink相关的问题,具体错误码的含义可以在FAQ中搜索.
int adlGetErrno(void);

// 一般无需调用;需要访问特定区域Andlink服务器地址时调用,注意:入参必须是全局地址
int setAndlinkServAddress(char *addr);

// 一般无需调用;需要访问特定Andlink诊断服务器url时调用,注意:入参必须是全局地址
int setAndlinkDgsServUrl(char *url);

// 按需调用;禁止andlink默认开启的某些功能,funcPos可以是ADL_FUNCS_e中元素的组合
int disableAdlFunc(ADL_DEF_ENABLED_FUNCS_e funcPos);

// 按需调用;使能andlink默认关闭的某些功能,funcPos可以是ADL_FUNCS_e中元素的组合
int enableAdlFunc(ADL_DEF_DISABLED_FUNCS_e funcPos);

// 按需调用;设备接入,查询andlink SDK版本号,也是DM版本号;
char *getAndlinkVersion();

// 获取产品模式,取值见:PRODUCT_MODE_e
int getProductMode();

// 按需调用,平台对接的设备不会调用;数据异步上报(返回值无法说明消息是否发送成功); modify  by 22.09.21,不再支持子设备数据上报;
// 删除入参childDevId;添加入参timestamp毫秒时间戳,根据需要填事件发生的时间戳或事件上报的时间戳 modify by 22.10.19
int devDataReport(char *eventType, char *seqId, unsigned long long timestamp, char *data, unsigned int dataLen);

// 按需调用,平台对接的设备不会调用;数据同步上报(内部阻塞等待,返回数据上报成功或失败的结果);add by 23.08.07
// 若需要支持离线续传的事件,采用此接口进行数据上报.
int devDataSyncReport(char *eventType, char *seqId, unsigned long long timestamp, char *data, unsigned int dataLen);

// 按需调用,平台对接的设备不会调用;本地局域网数据上报,默认上报到中国移动生态的路由器; 新增childDevType参数,update by 22.09.21
int local_devDataReport(char *childDevId, char *childDevType, char *eventType, char *seqId, char *data, unsigned int dataLen);

// 按需调用;用于摄像头扫码配网,设备将扫描APP上的二维码的内容传给SDK;
// 返回值:0,接收完全;-1,接收失败;1,接收成功,等待下一页
int adlSetScanCodeInfo(char *netinfo, unsigned int len);

// 按需调用;用于泛摄像头品类,通知andlink业务SDK的连接状态(0:连接成功;-1:连接失败);type取值"OVC",表示安防业务;
int adlSetBusinessStatus(char *type, int status);

// 按需调用;用于带屏扫码设备,主动执行绑定动作
int adlDeviceBinding(char *userKey, char *gwAddress2);

// 按需调用;子设备注册 token最大长度:MAX_PRODUCT_TOKEN_LEN
int childDevBootstrap(char *childDevId, char *childDevType, char *childPdtToken, char *outDeviceToken, char *outAndlinkToken);

// 按需调用;子设备上线
int childDevBoot(char *childDevId, char *childDevType, char *childPdtToken, char *swVersion, char *fmVersion);

// 按需调用,平台对接的设备不会调用;子设备数据上报接口 add by 22.09.21
// modify by 22.10.19  ;添加入参timestamp毫秒时间戳,根据需要填事件发生的时间戳或事件上报的时间戳
int childDevDataReport(char *childDevId, char *childDevType, char *eventType, char *seqId, unsigned long long timestamp, char *data, unsigned int dataLen);

// 蓝牙数据接收处理器
int adlBleRecvHandler(unsigned char *data, unsigned int len);

#ifdef SDK_USING_DEVICE_FUNCS_ENABLE
// 按需调用;设置设备的功能点,funcPos可以是ADL_DEVICE_FUNCS_e元素中的组合
int adl_setDevFunc(ADL_DEVICE_FUNCS_e funcPos);
#endif

// andlink的扩展功能点
#define ANDLINK_EXT_FUNCS_ENABLE
#ifdef ANDLINK_EXT_FUNCS_ENABLE
// andlink广播ID
typedef enum
{
    ADL_BLE_ADV_ID_YDAJ_DISCOVERY,   // 移动爱家APP发现
    ADL_BLE_ADV_ID_NRARBY_DISCOVERY, // OH靠近发现广播句柄
    ADL_BLE_ADV_ID_MAX
} ADL_BLE_ADV_ID_e;

// ble 广播数据接口
typedef struct
{
    unsigned char adv_length;
    unsigned char *adv_data;
    unsigned char scan_rsp_length;
    unsigned char *scan_rsp_data;

    unsigned short min_interval; // BLE广播发送最小间隔,实际间隔=[N*0.625ms],0表示由设备设置默认值
    unsigned short max_interval; // BLE广播发送最大间隔,实际间隔=[N*0.625ms],0表示由设备设置默认值
    unsigned short duration;     // BLE广播发送持续时间,实际时间=[N*10ms],0表示永久发送
} adl_ble_adv_data_t;

// 新增andlink扩展功能回调接口ID
typedef enum
{
    ADL_CB_BLE_START_ADV_DATA, // ble广播发送开始
    ADL_CB_BLE_STOP_ADV_DATA,  // ble广播发送停止
    ADL_CB_MAX
} ADL_EXT_CALLBACKS_TYPE_e;

// 新增andlink扩展功能回调接口
typedef struct
{
    // 开始ble广播发送
    int (*ble_start_adv_data_callback)(unsigned char adv_id, adl_ble_adv_data_t *data);
    // 停止ble广播发送
    int (*ble_stop_adv_data_callback)(unsigned char adv_id);
} adl_ext_callback_t;

// 注册andlink扩展功能回调接口
int register_andlink_ext_callback(ADL_EXT_CALLBACKS_TYPE_e cbType, void *cbHandler);


// 新增andlink扩展功能属性参数设置接口ID
typedef enum
{
    ADL_EXT_ATTR_INT_ANDLINK_LOG_MAX_SIZE, // 接收int类型,设置andlink单个日志文件最大阈值;缺省值为0x80000,即500KB
    ADL_EXT_ATTR_STR_ANDLINK_LOG_PATH,     // 接收string类型,设置andlink日志存储路径;缺省存储在/tmp/andlink
    ADL_EXT_ATTR_INT_SYSTEM_LOG_MAX_SIZE,  // 接收int类型,设置系统单个日志文件最大阈值;缺省值为0x80000,即500KB
    ADL_EXT_ATTR_STR_SYSTEM_LOG_PATH,      // 接收string类型,设置系统日志存储路径;缺省存储在/tmp/andlink
    ADL_EXT_ATTR_INT_ADV_POWER,            // 接收int类型,设置广播实际发射功率,接收int类型,取值=TxPower（设备芯片广播发射功率）– OTA（设备天线损耗）
    ADL_EXT_ATTR_INT_TASK_PRI,             // 接收int类型,设置andlink任务优先级;缺省值为0
    ADL_EXT_ATTR_STR_WIFI_NAME,            // 接收string类型,设置Wi-Fi无线网卡名称;缺省值为wlan0
    ADL_EXT_ATTR_STR_OTA_FILE_PATH,        // 接收string类型,设置OTA文件存储路径
    ADL_EXT_ATTR_STR_OTA_FILE_NAME,        // 接收string类型,设置OTA文件名(包含后缀)
    ADL_EXT_ATTR_INT_OTA_POLICY,           // 接收int类型,设置OTA下载策略：0整包下载;1分包下载
    ADL_EXT_ATTR_INT_OTA_FRAG_SIZE,        // 接收int类型,设置OTA分包下载时设备支持的单片下载大小;单位字节
    ADL_EXT_ATTR_STR_OH_ProdID,            // 接收string类型,开源鸿蒙产品ID
    ADL_EXT_ATTR_MAX
} ADL_EXT_ATTRS_TYPE_e;

// 设置扩展功能int类型的属性参数
int andlink_attr_set_int(ADL_EXT_ATTRS_TYPE_e type, int value);

// 设置扩展功能字符串类型的属性参数,要求参数2输入的地址必须在sdk运行期间持续有效
int andlink_attr_set_str(ADL_EXT_ATTRS_TYPE_e type, const char *value);

#endif // ANDLINK_EXT_FUNCS_ENABLE

#ifdef __cplusplus
}
#endif

#endif
