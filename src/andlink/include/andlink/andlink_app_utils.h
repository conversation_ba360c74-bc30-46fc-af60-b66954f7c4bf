/*
 * 文件名称：andlink_app_utils.h
 * 说 明:集成andlink SDK,生成应用程序,所需要的工具接口声明
 */
#ifndef __ANDLINK_APP_UTILS_H
#define __ANDLINK_APP_UTILS_H
#include "andlink_pub.h"

int shellExecuteCmdAndReadResultsByRow(const char *cmd, char res[][128], unsigned long count);

int shellExecuteCmdAndReadResultsByBlock(const char *cmd, char *bufferOut, unsigned long bufferSize);

int extractSubstring(char *instr, char outBuffer[][64], unsigned long bufSize);

int get_device_mac_address(char *ifname, char *macAddress, unsigned int maxMacAddressLen);

int if_get_ipv4(char *ifname, char *outIpBuffer, char *outBcastBuffer, unsigned int addrBufferSize);

int file_is_null(char *filename);

int get_pid(const char Name[]);

int readCfgItem(char *filename, char *item, char *outbuf, unsigned long bufsize);

int writeCfgItem(const char *fileName, const char *item, const char *value);

unsigned int app_strlen(const char *s);

int app_strncpy_s(char *dest, size_t dmax, const char *src, size_t slen);

int app_snprintf_s(char *str, size_t size, const char *format, ...);

int app_memcpy_s(void *dest, size_t destsz, const void *src, size_t count);

#endif // __ANDLINK_APP_UTILS_H