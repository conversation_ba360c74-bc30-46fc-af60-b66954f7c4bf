/*
 * 文件名称：wpa_supplicant_api.h
 * 说 明:wifi控制相关接口的对外接口
 */
#ifndef __WPA_SUPPLICANT_API_H
#define __WPA_SUPPLICANT_API_H
#include "andlink_pub.h"
#include <cJSON.h>
#ifdef __cplusplus
extern "C" {
#endif
int check_device_mac_info(char *ifname, char *outMac);

int check_device_ip_info(char *ifname, char *outIP, char *outBrdAddress);

int scan_wifi(wifi_cfg_info_t *wificfg, cJSON *outApMsg);

int control_wifi_sta(WIFI_CTRL_OPT_e opt, wifi_cfg_info_t *wificfg, char *outIp, char *outBrdAddr);

int control_wifi_ap(WIFI_CTRL_OPT_e opt, wifi_cfg_info_t *wificfg);

int get_wifi_info(wifi_cfg_info_t *wificfg);
#ifdef __cplusplus
}
#endif
#endif // __WPA_SUPPLICANT_API_H