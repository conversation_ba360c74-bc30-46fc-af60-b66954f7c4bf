from setuptools import find_packages, setup

package_name = 'network'

setup(
    name=package_name,
    version='0.0.0',
    packages=find_packages(exclude=['test']),
    data_files=[
        ('share/ament_index/resource_index/packages',
            ['resource/' + package_name]),
        ('share/' + package_name, ['package.xml']),
    ],
    install_requires=['setuptools'],
    zip_safe=True,
    maintainer='xuhui',
    maintainer_email='<EMAIL>',
    description='xiaoli网络管理节点 - 处理WiFi和蜂窝网络连接',
    license='Apache License 2.0',
    # 使用extras_require替代tests_require
    extras_require={
        'test': ['pytest'],
    },
    entry_points={
        'console_scripts': [
            'network_node = network.network_node:main'
        ],
    },
)
