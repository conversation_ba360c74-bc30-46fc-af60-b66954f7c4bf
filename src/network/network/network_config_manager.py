#!/usr/bin/env python3
"""
网络配置管理器
负责网络状态配置文件的读写、锁管理和状态同步
"""

import json
import os
import fcntl
import time
import threading
from datetime import datetime
from typing import Dict, Any, Optional
import rclpy
from rclpy.logging import get_logger


class NetworkConfigManager:
    """网络配置管理器 - 负责WiFi和5G状态的持久化管理"""
    
    def __init__(self, config_file_path: str, lock_file_path: str, logger=None):
        """
        初始化网络配置管理器
        
        Args:
            config_file_path: 配置文件路径
            lock_file_path: 锁文件路径
            logger: 日志记录器
        """
        self.config_file_path = config_file_path
        self.lock_file_path = lock_file_path
        self.logger = logger if logger else get_logger('network_config_manager')
        
        # 线程锁，用于保护内存中的配置数据
        self._memory_lock = threading.RLock()
        
        # 内存中的配置缓存
        self._config_cache = None
        self._cache_timestamp = 0
        self._cache_ttl = 5  # 缓存有效期5秒
        
        # 默认配置 - 简化版本，只保留核心开关状态
        self._default_config = {
            "wifi": {
                "switch_state": "on",
                "wifiSetStatus": 0  # WiFi配置状态：0-未配置、1-已配置
            },
            "mobile_data": {
                "switch_state": "on"
            }
        }
        
        # 确保配置文件目录存在
        self._ensure_config_directory()
        
        # 初始化配置文件
        self._initialize_config_file()
        
        self.logger.info(f"网络配置管理器初始化完成: {config_file_path}")

    def _get_current_timestamp(self) -> str:
        """获取当前时间戳"""
        return datetime.now().isoformat()

    def _ensure_config_directory(self):
        """确保配置文件目录存在"""
        try:
            config_dir = os.path.dirname(self.config_file_path)
            if not os.path.exists(config_dir):
                os.makedirs(config_dir, mode=0o755, exist_ok=True)
                self.logger.info(f"创建配置目录: {config_dir}")
                
            lock_dir = os.path.dirname(self.lock_file_path)
            if not os.path.exists(lock_dir):
                os.makedirs(lock_dir, mode=0o755, exist_ok=True)
                self.logger.info(f"创建锁文件目录: {lock_dir}")
                
        except Exception as e:
            self.logger.error(f"创建配置目录失败: {e}")
            raise

    def _initialize_config_file(self):
        """初始化配置文件"""
        try:
            if not os.path.exists(self.config_file_path):
                self.logger.info("配置文件不存在，创建默认配置文件")
                self._write_config_with_lock(self._default_config)
            else:
                # 验证现有配置文件
                config = self._read_config_with_lock()
                if not self._validate_config(config):
                    self.logger.warning("配置文件格式无效，重新创建默认配置")
                    self._write_config_with_lock(self._default_config)
                else:
                    # 检查是否需要配置迁移（添加缺失的字段）
                    updated = self._migrate_config_if_needed(config)
                    if updated:
                        self.logger.info("配置文件已更新到最新版本")
                        self._write_config_with_lock(config)
                    
        except Exception as e:
            self.logger.error(f"初始化配置文件失败: {e}")
            # 如果初始化失败，创建默认配置
            try:
                self._write_config_with_lock(self._default_config)
            except Exception as e2:
                self.logger.error(f"创建默认配置文件也失败: {e2}")
                raise

    def _migrate_config_if_needed(self, config: Dict[str, Any]) -> bool:
        """
        迁移配置文件到最新版本，添加缺失的字段
        
        Args:
            config: 当前配置
            
        Returns:
            bool: 是否进行了更新
        """
        updated = False
        
        # 确保 wifi 配置包含 wifiSetStatus 字段
        if "wifi" in config and isinstance(config["wifi"], dict):
            if "wifiSetStatus" not in config["wifi"]:
                config["wifi"]["wifiSetStatus"] = 0  # 默认为未配置状态
                updated = True
                self.logger.info("添加 wifiSetStatus 字段到 wifi 配置")
        
        return updated

    def _validate_config(self, config: Dict[str, Any]) -> bool:
        """验证配置文件格式 - 简化版本"""
        try:
            if not isinstance(config, dict):
                return False
                
            # 检查必要的键 - 简化后只需要wifi和mobile_data
            required_keys = ["wifi", "mobile_data"]
            if not all(key in config for key in required_keys):
                return False
                
            # 检查wifi配置
            wifi_config = config["wifi"]
            if not isinstance(wifi_config, dict) or "switch_state" not in wifi_config:
                return False
                
            # 检查mobile_data配置
            mobile_config = config["mobile_data"]
            if not isinstance(mobile_config, dict) or "switch_state" not in mobile_config:
                return False
                
            return True
            
        except Exception:
            return False

    def _acquire_file_lock(self, file_handle, timeout: float = 10.0) -> bool:
        """
        获取文件锁
        
        Args:
            file_handle: 文件句柄
            timeout: 超时时间（秒）
            
        Returns:
            bool: 是否成功获取锁
        """
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                fcntl.flock(file_handle.fileno(), fcntl.LOCK_EX | fcntl.LOCK_NB)
                return True
            except (IOError, OSError):
                time.sleep(0.1)
        return False

    def _release_file_lock(self, file_handle):
        """释放文件锁"""
        try:
            fcntl.flock(file_handle.fileno(), fcntl.LOCK_UN)
        except (IOError, OSError) as e:
            self.logger.warning(f"释放文件锁失败: {e}")

    def _read_config_with_lock(self) -> Dict[str, Any]:
        """
        使用文件锁读取配置
        
        Returns:
            Dict: 配置数据
        """
        try:
            with open(self.config_file_path, 'r', encoding='utf-8') as f:
                if not self._acquire_file_lock(f):
                    raise Exception("获取文件读锁超时")
                
                try:
                    content = f.read()
                    if not content.strip():
                        self.logger.warning("配置文件为空，返回默认配置")
                        return self._default_config.copy()
                    
                    config = json.loads(content)
                    return config
                    
                finally:
                    self._release_file_lock(f)
                    
        except FileNotFoundError:
            self.logger.warning("配置文件不存在，返回默认配置")
            return self._default_config.copy()
        except json.JSONDecodeError as e:
            self.logger.error(f"配置文件JSON格式错误: {e}")
            return self._default_config.copy()
        except Exception as e:
            self.logger.error(f"读取配置文件失败: {e}")
            return self._default_config.copy()

    def _write_config_with_lock(self, config: Dict[str, Any]):
        """
        使用文件锁写入配置
        
        Args:
            config: 要写入的配置数据
        """
        try:
            # 先写入临时文件，然后原子性替换
            temp_file_path = f"{self.config_file_path}.tmp"
            
            with open(temp_file_path, 'w', encoding='utf-8') as f:
                if not self._acquire_file_lock(f):
                    raise Exception("获取文件写锁超时")
                
                try:
                    json.dump(config, f, indent=2, ensure_ascii=False)
                    f.flush()
                    os.fsync(f.fileno())  # 强制写入磁盘
                    
                finally:
                    self._release_file_lock(f)
            
            # 原子性替换
            os.replace(temp_file_path, self.config_file_path)
            
            # 清除缓存
            with self._memory_lock:
                self._config_cache = None
                
            self.logger.debug("配置文件写入成功")
            
        except Exception as e:
            self.logger.error(f"写入配置文件失败: {e}")
            # 清理临时文件
            try:
                if os.path.exists(temp_file_path):
                    os.remove(temp_file_path)
            except:
                pass
            raise

    def get_config(self) -> Dict[str, Any]:
        """
        获取配置（带缓存）
        
        Returns:
            Dict: 配置数据
        """
        with self._memory_lock:
            current_time = time.time()
            
            # 检查缓存是否有效
            if (self._config_cache is not None and 
                current_time - self._cache_timestamp < self._cache_ttl):
                return self._config_cache.copy()
            
            # 重新读取配置
            config = self._read_config_with_lock()
            self._config_cache = config.copy()
            self._cache_timestamp = current_time
            
            return config.copy()

    def update_config(self, updates: Dict[str, Any]):
        """
        更新配置
        
        Args:
            updates: 要更新的配置项
        """
        with self._memory_lock:
            try:
                # 读取当前配置
                current_config = self._read_config_with_lock()
                
                # 深度合并更新
                self._deep_merge(current_config, updates)
                
                # 写入更新后的配置
                self._write_config_with_lock(current_config)
                
                self.logger.info(f"配置更新成功: {updates}")
                
            except Exception as e:
                self.logger.error(f"更新配置失败: {e}")
                raise

    def _deep_merge(self, target: Dict[str, Any], source: Dict[str, Any]):
        """
        深度合并字典
        
        Args:
            target: 目标字典（会被修改）
            source: 源字典
        """
        for key, value in source.items():
            if key in target and isinstance(target[key], dict) and isinstance(value, dict):
                self._deep_merge(target[key], value)
            else:
                target[key] = value

    def get_wifi_state(self) -> Dict[str, Any]:
        """获取WiFi状态"""
        config = self.get_config()
        return config["wifi"].copy()

    def get_mobile_data_state(self) -> Dict[str, Any]:
        """获取移动数据状态"""
        config = self.get_config()
        return config["mobile_data"].copy()

    def update_wifi_state(self, **kwargs):
        """
        更新WiFi状态
        
        Args:
            **kwargs: WiFi状态参数 (switch_state等)
        """
        updates = {
            "wifi": kwargs
        }
        self.update_config(updates)

    def update_mobile_data_state(self, **kwargs):
        """
        更新移动数据状态
        
        Args:
            **kwargs: 移动数据状态参数 (switch_state等)
        """
        updates = {
            "mobile_data": kwargs
        }
        self.update_config(updates)

    def get_wifi_set_status(self) -> int:
        """
        获取WiFi配置状态
        
        Returns:
            int: WiFi配置状态 (0-未配置、1-已配置)
        """
        wifi_state = self.get_wifi_state()
        return wifi_state.get("wifiSetStatus", 0)

    def update_wifi_set_status(self, status: int):
        """
        更新WiFi配置状态
        
        Args:
            status: WiFi配置状态 (0-未配置、1-已配置)
        """
        if status not in [0, 1]:
            raise ValueError("wifiSetStatus 必须是 0 (未配置) 或 1 (已配置)")
        
        self.update_wifi_state(wifiSetStatus=status)
        self.logger.info(f"WiFi配置状态已更新为: {'已配置' if status == 1 else '未配置'}")


