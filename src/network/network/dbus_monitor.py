#!/usr/bin/env python3
import os
import json
import fcntl
import subprocess
import time
import rclpy
from typing import Optional, Dict, Any, List

class DbusMonitor:
    def __init__(self, networkmanager):
        # 使用ROS日志系统
        self.logger = rclpy.logging.get_logger('dbus_monitor')
        self.logger.info("=====dbus_monitor 程序启动=====")

        # 从network_manager获取实际的网卡名称
        self.networkmanager = networkmanager
        self.wifi_interface = getattr(networkmanager, 'wifi_interface', 'wlp1s0')
        self.mobile_interface = getattr(networkmanager, 'mobile_interface', 'enx00e04c6801d0')

        self.logger.info(f"监控网卡配置 - WiFi: {self.wifi_interface}, 移动网络: {self.mobile_interface}")

        # 初始化状态
        self.wifi_device_paths: List[str] = []
        self.mobile_device_paths: List[str] = []
        self.dbus_monitor_wifi: Optional[bool] = None
        self.dbus_monitor_5g: Optional[bool] = None

        self.monitoring = False
        self.max_retries = 3
        self.retry_delay = 5  # 秒

    def get_device_paths(self) -> bool:
        """获取网络设备路径"""
        self.logger.info(f"开始获取网络设备路径 - WiFi接口: {self.wifi_interface}, 移动网络接口: {self.mobile_interface}")
        try:
            wifi_candidates = []
            mobile_candidates = []
            
            # 获取所有设备对象路径
            cmd = [
                "gdbus", "call", "--system",
                "--dest", "org.freedesktop.NetworkManager",
                "--object-path", "/org/freedesktop/NetworkManager",
                "--method", "org.freedesktop.NetworkManager.GetDevices"
            ]
            result = subprocess.run(cmd, capture_output=True, text=True, check=True, timeout=10)
            device_paths = result.stdout.strip()

            # 遍历设备路径获取接口信息
            for path in device_paths.split(','):
                path = path.strip('(<>)")\'[] ')
                if not path.startswith('/org/freedesktop/NetworkManager/Devices/'):
                    continue

                try:
                    # 获取接口名称
                    interface_cmd = [
                        "gdbus", "call", "--system",
                        "--dest", "org.freedesktop.NetworkManager",
                        "--object-path", path,
                        "--method", "org.freedesktop.DBus.Properties.Get",
                        "org.freedesktop.NetworkManager.Device",
                        "Interface"
                    ]
                    interface_result = subprocess.run(
                        interface_cmd, 
                        capture_output=True, 
                        text=True, 
                        check=True,
                        timeout=5
                    )
                    interface_name = interface_result.stdout.strip('(<>)")\'[] ')
                    # 保留原始接口名称，不过滤字符
                    interface_name = interface_name.strip("'\"")

                    self.logger.debug(f"检测到接口: {interface_name}")

                    # 使用实际的网卡名称进行匹配
                    if self.wifi_interface in interface_name:
                        wifi_candidates.append(path)
                        self.logger.debug(f"找到WiFi接口 {interface_name} 对应路径: {path}")
                    elif self.mobile_interface in interface_name:
                        mobile_candidates.append(path)
                        self.logger.debug(f"找到移动网络接口 {interface_name} 对应路径: {path}")
                        
                except subprocess.TimeoutExpired:
                    self.logger.warning(f"获取设备接口名称超时: {path}")
                    continue
                except Exception as e:
                    self.logger.warning(f"获取设备接口名称失败: {path}, 错误: {e}")
                    continue

            # 保存所有候选设备路径
            self.wifi_device_paths = wifi_candidates
            self.mobile_device_paths = mobile_candidates
            self.logger.info(f"找到的WiFi设备路径 ({self.wifi_interface}): {self.wifi_device_paths}")
            self.logger.info(f"找到的移动网络设备路径 ({self.mobile_interface}): {self.mobile_device_paths}")

            if not self.wifi_device_paths and not self.mobile_device_paths:
                self.logger.error("未找到任何网络设备")
                return False
            return True

        except subprocess.TimeoutExpired:
            self.logger.error("获取设备路径超时")
            return False
        except subprocess.CalledProcessError as e:
            self.logger.error(f"获取设备路径失败: {e}")
            return False
        except Exception as e:
            self.logger.error(f"获取设备路径时发生未知错误: {e}")
            return False

    def handle_state_change(self, device_path: str, new_state: int):
        """处理设备状态变化"""
        # 网络状态码：100=已连接，30=断开连接
        if new_state in [100, 30]:
            # 异步调用网络状态检查，避免阻塞监控线程
            try:
                self.networkmanager.check_network_status()
            except Exception as e:
                self.logger.error(f"调用网络状态检查时发生错误: {e}")

        # 更新设备状态
        if device_path in self.wifi_device_paths:
            device = f"WiFi ({self.wifi_interface})"
            self.dbus_monitor_wifi = (new_state == 100)
            self.logger.info(f"{device} 状态变更: {'连接' if new_state == 100 else '断开'}")

        elif device_path in self.mobile_device_paths:
            device = f"移动网络 ({self.mobile_interface})"
            self.dbus_monitor_5g = (new_state == 100)
            self.logger.info(f"{device} 状态变更: {'连接' if new_state == 100 else '断开'}")

    def monitor_network_status(self):
        """监控网络状态变化，带重连机制"""
        retry_count = 0
        
        while retry_count < self.max_retries:
            try:
                if not self.get_device_paths():
                    self.logger.error("无法启动监控：设备路径获取失败")
                    retry_count += 1
                    if retry_count < self.max_retries:
                        self.logger.info(f"等待 {self.retry_delay} 秒后重试 ({retry_count}/{self.max_retries})")
                        time.sleep(self.retry_delay)
                    continue

                self.logger.info("启动网络状态监控...")
                self.logger.info(f"当前监控设备 - WiFi ({self.wifi_interface}): {self.wifi_device_paths}, 移动网络 ({self.mobile_interface}): {self.mobile_device_paths}")

                cmd = [
                    "gdbus", "monitor", "--system",
                    "--dest", "org.freedesktop.NetworkManager"
                ]
                
                process = subprocess.Popen(
                    cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    universal_newlines=True
                )
                self.logger.info(f"DBus 监控进程启动: PID={process.pid}")
                self.monitoring = True

                try:
                    while self.monitoring:
                        line = process.stdout.readline()
                        if not line:
                            self.logger.warning("DBus 监控输出流结束")
                            break

                        if "StateChanged" in line:
                            try:
                                device_path = line.split(':')[0].strip()
                                
                                # 检查是否是我们关心的设备
                                if device_path not in self.wifi_device_paths and device_path not in self.mobile_device_paths:
                                    continue

                                state = int(line.split('(uint32')[1].split(',')[0].strip())
                                self.logger.info(f"设备 {device_path} 状态变更为: {state}")
                                self.handle_state_change(device_path, state)
                                
                            except (ValueError, IndexError) as e:
                                self.logger.error(f"解析状态信息失败: {e}")
                                self.logger.debug(f"问题行内容: {line}")
                                continue

                    # 正常退出循环
                    break
                    
                except KeyboardInterrupt:
                    self.logger.info("收到终止信号，正在退出...")
                    break
                except Exception as e:
                    self.logger.error(f"监控过程发生未预期的错误: {e}")
                    retry_count += 1
                finally:
                    self.monitoring = False
                    self._cleanup_process(process)
                    
            except Exception as e:
                self.logger.error(f"启动监控时发生错误: {e}")
                retry_count += 1
                
            if retry_count < self.max_retries:
                self.logger.info(f"等待 {self.retry_delay} 秒后重试 ({retry_count}/{self.max_retries})")
                time.sleep(self.retry_delay)
        
        if retry_count >= self.max_retries:
            self.logger.error(f"达到最大重试次数 ({self.max_retries})，停止监控")

    def _cleanup_process(self, process):
        """清理监控进程"""
        self.logger.info("正在清理监控进程...")
        try:
            process.terminate()
            process.wait(timeout=5)
            self.logger.info("监控进程已正常终止")
        except subprocess.TimeoutExpired:
            self.logger.warning("监控进程未能在超时时间内终止，强制杀死")
            process.kill()
        except Exception as e:
            self.logger.error(f"终止进程时发生错误: {e}")

    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        self.logger.info("停止网络状态监控")
