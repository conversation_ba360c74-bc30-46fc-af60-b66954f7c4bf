# cellular_control.py
"""
蜂窝网络模块控制器
支持4G/5G蜂窝网络模块的GPIO控制和网络管理
"""
import os
import time
import subprocess
import rclpy

class CellularModuleController:
    """蜂窝网络模块控制器 - 支持4G/5G网络模块的GPIO操作"""

    GPIO_BASE_PATH = "/sys/class/gpio"

    # GPIO引脚定义
    GPIO_POWER_KEY = 4      # 电源控制引脚
    GPIO_RESET = 24         # 复位引脚
    GPIO_POWER_EN = 27      # 电源使能引脚
    GPIO_STATUS = 29        # 状态指示引脚
    GPIO_NET_MODE = 23      # 网络模式选择引脚
    
    GPIO_YSC_POWER_MODE = 52      # YSC电源引脚

    def __init__(self, node=None, logger=None):
        """
        初始化蜂窝网络模块控制器

        Args:
            node: ROS2节点实例
            logger: 日志记录器
        """
        self.node = node
        self.logger = logger if logger else rclpy.logging.get_logger('cellular_controller')
        self.control_pins = [
            self.GPIO_POWER_KEY,
            self.GPIO_RESET,
            self.GPIO_POWER_EN,
            self.GPIO_STATUS,
            self.GPIO_NET_MODE
        ]

    def initialize(self) -> bool:
        """
        初始化蜂窝网络模块的所有GPIO引脚

        Returns:
            bool: 初始化是否成功
        """
        try:
            self.logger.info("开始初始化蜂窝网络模块...")

            # 导出所有GPIO引脚
            for pin in self.control_pins:
                self._export_pin(pin)

            # 设置GPIO方向为输出
            for pin in self.control_pins:
                self._set_direction(pin, "out")

            # 设置初始电平状态
            self._set_value(self.GPIO_POWER_EN, 1)    # 电源使能高电平
            self._set_value(self.GPIO_RESET, 0)       # 复位引脚低电平
            self._set_value(self.GPIO_STATUS, 0)      # 状态引脚低电平
            self._set_value(self.GPIO_NET_MODE, 0)    # 网络模式引脚低电平

            # 生成电源控制脉冲启动模块
            self._generate_power_on_pulse()

            self.logger.info("蜂窝网络模块GPIO初始化完成")
            return True
        except Exception as e:
            self.logger.error(f"蜂窝网络模块初始化失败: {str(e)}")
            return False

    def power_on(self) -> bool:
        """
        开启蜂窝网络模块电源

        Returns:
            bool: 操作是否成功
        """
        try:
            self.logger.info("正在开启蜂窝网络模块...")
            self._set_value(self.GPIO_POWER_EN, 1)
            self._generate_power_on_pulse()
            time.sleep(2)  # 等待模块启动
            self.logger.info("蜂窝网络模块已开启")
            return True
        except Exception as e:
            self.logger.error(f"开启蜂窝网络模块失败: {str(e)}")
            return False

    def power_off(self) -> bool:
        """
        关闭蜂窝网络模块电源

        Returns:
            bool: 操作是否成功
        """
        try:
            self.logger.info("正在关闭蜂窝网络模块...")
            self._generate_power_off_pulse()
            time.sleep(1)
            self._set_value(self.GPIO_POWER_EN, 0)
            self.logger.info("蜂窝网络模块已关闭")
            return True
        except Exception as e:
            self.logger.error(f"关闭蜂窝网络模块失败: {str(e)}")
            return False

    def reset_module(self) -> bool:
        """
        复位蜂窝网络模块

        Returns:
            bool: 操作是否成功
        """
        try:
            self.logger.info("正在复位蜂窝网络模块...")
            self._set_value(self.GPIO_RESET, 1)
            time.sleep(0.5)
            self._set_value(self.GPIO_RESET, 0)
            time.sleep(2)  # 等待复位完成
            self.logger.info("蜂窝网络模块复位完成")
            return True
        except Exception as e:
            self.logger.error(f"复位蜂窝网络模块失败: {str(e)}")
            return False

    def _export_pin(self, pin: int) -> None:
        """
        导出GPIO引脚到用户空间

        Args:
            pin: GPIO引脚号
        """
        gpio_path = os.path.join(self.GPIO_BASE_PATH, f"gpio{pin}")
        if not os.path.exists(gpio_path):
            export_path = os.path.join(self.GPIO_BASE_PATH, "export")
            try:
                with open(export_path, 'w') as f:
                    f.write(str(pin))
                time.sleep(0.1)  # 等待系统创建目录
                self.logger.debug(f"成功导出 GPIO{pin}")
            except Exception as e:
                raise Exception(f"导出GPIO{pin}失败: {str(e)}")

    def _set_direction(self, pin: int, direction: str) -> None:
        """
        设置GPIO引脚方向

        Args:
            pin: GPIO引脚号
            direction: 方向 ("in" 或 "out")
        """
        direction_path = os.path.join(self.GPIO_BASE_PATH, f"gpio{pin}", "direction")
        try:
            with open(direction_path, 'w') as f:
                f.write(direction)
            mode = "输出" if direction == "out" else "输入"
            self.logger.debug(f"设置 GPIO{pin} 为{mode}模式")
        except Exception as e:
            raise Exception(f"设置GPIO{pin}方向失败: {str(e)}")

    def _set_value(self, pin: int, value: int) -> None:
        """
        设置GPIO引脚电平值

        Args:
            pin: GPIO引脚号
            value: 电平值 (0=低电平, 1=高电平)
        """
        value_path = os.path.join(self.GPIO_BASE_PATH, f"gpio{pin}", "value")
        try:
            with open(value_path, 'w') as f:
                f.write(str(value))
            state = "高电平" if value else "低电平"
            self.logger.debug(f"设置 GPIO{pin} -> {state}")
        except Exception as e:
            raise Exception(f"设置GPIO{pin}值失败: {str(e)}")

    def _generate_power_on_pulse(self, duration: float = 0.5) -> None:
        """
        生成电源开启脉冲信号

        Args:
            duration: 脉冲持续时间(秒)
        """
        try:
            self.logger.debug("生成电源开启脉冲...")
            self._set_value(self.GPIO_POWER_KEY, 1)
            time.sleep(duration)
            self._set_value(self.GPIO_POWER_KEY, 0)
            self.logger.debug("电源开启脉冲完成")
        except Exception as e:
            raise Exception(f"生成电源开启脉冲失败: {str(e)}")

    def _generate_power_off_pulse(self, duration: float = 2.0) -> None:
        """
        生成电源关闭脉冲信号

        Args:
            duration: 脉冲持续时间(秒)
        """
        try:
            self.logger.debug("生成电源关闭脉冲...")
            self._set_value(self.GPIO_POWER_KEY, 1)
            time.sleep(duration)
            self._set_value(self.GPIO_POWER_KEY, 0)
            self.logger.debug("电源关闭脉冲完成")
        except Exception as e:
            raise Exception(f"生成电源关闭脉冲失败: {str(e)}")

    def get_module_status(self) -> dict:
        """
        获取蜂窝网络模块状态

        Returns:
            dict: 模块状态信息
        """
        try:
            status = {}
            for pin in self.control_pins:
                value_path = os.path.join(self.GPIO_BASE_PATH, f"gpio{pin}", "value")
                if os.path.exists(value_path):
                    with open(value_path, 'r') as f:
                        value = int(f.read().strip())
                    status[f"gpio_{pin}"] = value
                else:
                    status[f"gpio_{pin}"] = None

            # 添加引脚功能说明
            status["pin_functions"] = {
                "gpio_4": "电源控制键",
                "gpio_24": "复位引脚",
                "gpio_27": "电源使能",
                "gpio_29": "状态指示",
                "gpio_23": "网络模式"
            }

            return status
        except Exception as e:
            self.logger.error(f"获取模块状态失败: {str(e)}")
            return {}

    def cleanup(self) -> None:
        """
        清理GPIO资源
        """
        try:
            self.logger.info("清理蜂窝网络模块GPIO资源...")
            # 关闭电源使能
            self._set_value(self.GPIO_POWER_EN, 0)

            # 取消导出GPIO引脚
            unexport_path = os.path.join(self.GPIO_BASE_PATH, "unexport")
            for pin in self.control_pins:
                try:
                    with open(unexport_path, 'w') as f:
                        f.write(str(pin))
                    self.logger.debug(f"取消导出 GPIO{pin}")
                except:
                    pass  # 忽略取消导出失败的情况

            self.logger.info("GPIO资源清理完成")
        except Exception as e:
            self.logger.error(f"清理GPIO资源失败: {str(e)}")