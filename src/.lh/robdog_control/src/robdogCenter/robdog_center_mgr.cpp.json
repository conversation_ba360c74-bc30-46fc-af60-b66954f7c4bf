{"sourceFile": "robdog_control/src/robdogCenter/robdog_center_mgr.cpp", "activeCommit": 0, "commits": [{"activePatchIndex": 3, "patches": [{"date": 1754121509883, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1754121749083, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -661,9 +661,9 @@\n     }\n }\n \n void RobdogCenter::update_map_points_path() {\n-    std::string map_points_path_dft = node_->getResourcePath(\"config/map_points.json\");\n+    std::string map_points_path_dft = node_->getResourcePath(\"config/map_points\");\n     std::string current_mapid = RobotState::getInstance().getMapId();\n     // std::string map_points_path_dft = \"/etc/cmcc_robot\";\n     if (!node_->has_parameter(\"map_points_path\")) {\n         node_->declare_parameter<std::string>(\"map_points_path\", map_points_path_dft); \n"}, {"date": 1754122342116, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -661,9 +661,9 @@\n     }\n }\n \n void RobdogCenter::update_map_points_path() {\n-    std::string map_points_path_dft = node_->getResourcePath(\"config/map_points\");\n+    std::string map_points_path_dft = node_->getResourcePath(\"config/map_points.json\");\n     std::string current_mapid = RobotState::getInstance().getMapId();\n     // std::string map_points_path_dft = \"/etc/cmcc_robot\";\n     if (!node_->has_parameter(\"map_points_path\")) {\n         node_->declare_parameter<std::string>(\"map_points_path\", map_points_path_dft); \n"}, {"date": 1754123042655, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -697,9 +697,9 @@\n     }\n     RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"robotdog_file_path: %s\", robotdog_file_path.c_str());\n     RobotState::getInstance().loadConfig(robotdog_file_path);\n \n-    \n+    node_->declare_parameter<std::string>(\"map_points_path\", map_points_path_dft); node_->declare_parameter<std::string>(\"map_points_path\", map_points_path_dft); \n \n     update_map_points_path();\n     ReadMapCfg::getInstance().loadConfig(map_points_path); // 点位信息\n \n"}], "date": 1754121509883, "name": "Commit-0", "content": "#include \"libWebSocket.h\"\n#include \"public/tools.h\"\n#include \"robdog_center_mgr.h\"\n#include \"robotState/RobotState.h\" // 为了存取xml\n#include \"robdogNode/robdog_ctrl_node.h\"\n#include \"robdogHandPosCtrl/robdog_hand_pos.h\" // 为了控制手势识别\n\n#include \"robotSmartRemind/robot_smart_remind.h\" // 为了使用智能提醒\n\n#include \"robotInfoCfg/read_map_point_cfg.h\"\n#include \"robotMgr/robot_info_mgr.h\" // 为了读取状态\n#include <rclcpp/rclcpp.hpp>\n#include <netinet/in.h>\n#include <stdio.h>\n#include <unistd.h>\n#include <uuid/uuid.h>\n#include <geometry_msgs/msg/twist.hpp>\n#include <std_msgs/msg/string.hpp>\n#include <cmath> \n#include <filesystem>\n#include <fstream>\n#include <geometry_msgs/msg/twist.hpp>\n#include <homi_speech_interface/srv/assistant_abort.hpp>\n#include <homi_speech_interface/srv/assistant_take_photo.hpp>\n#include <homi_speech_interface/srv/iot_control.hpp>\n#include <homi_speech_interface/srv/net_ctrl.hpp>\n#include <iostream>\n#include <jsoncpp/json/json.h>\n#include <random>\n#include <string>\n#include <tf2/transform_datatypes.h> // ROS 2 中的 tf2\n#include <thread>\n#include <chrono>\n#include <tf2/transform_datatypes.h> // ROS 2 中的 tf2\n#include <vector>\n#include <atomic>\n#include <chrono>\n#include <homi_speech_interface/srv/set_wake_event.hpp>\n#include <iostream>\n#include <thread>\n#include \"xiaoli_com/xiaoli_pub_def.h\"\n#include <homi_com/homi_utils.hpp>\n#include <future>\n#include <unordered_set>\n#include <sensor_msgs/msg/nav_sat_fix.hpp>\n#include <cmath>\n#include <mutex>\n#include <condition_variable>\n#include <regex>\n#include <stdexcept>              // 异常处理\n\n#include <openssl/evp.h>\n#include <openssl/err.h>\n#include <sys/file.h>\n#include <cerrno>    // 用于errno变量\n#include <cstring>   // 用于strerror()函数\n#include <fcntl.h>   // 用于flock()和锁类型定义\n\n\nusing namespace std;\nusing namespace WS;\nnamespace fs = std::filesystem;\nconst std::string dynamic_nobind_video_path=\"video/dynamic_nobind/dynamic_nobind.mp4\";\nconst std::string static_nobind_video_path=\"video/static_nobind/static_nobind.mp4\";\nconst std::string resource_default_video_path=\"video/default\";\nconst std::string resource_nonet_video_path=\"video/nonet/\";\n\n// ********************************************* 和导航相关的代码 *******************************************\n/*自主导航的步态，下发多点任务时带上\n\"option\": {\n    \"even_low_speed\": False,      // 平地低速\n    \"even_medium_speed\": False,   // 平地中速\n    \"uneven_high_step\": False,    // 越障高速\n    \"even_rl\": False,             // 平地学习\n    \"uneven_rl\": False            // 越障学习\n}*/\n\nunsigned long currentTimeStramp_ = base::homiUtils::getCurrentTimeStamp();\nstd::chrono::seconds timeout_duration(15); // 设置超时时间为15秒\nauto lastCancelMovementTime = std::chrono::steady_clock::now(); // 记录上次收到2200取消移动的时间\nbool bConnected_ = false;\nstring strConnectUrl_ = \"ws://192.168.1.110:19002\";\nint nConnectIndex_ = 0;\nstring g_netctrl_ret;\nlong long g_wifi_set_time =0;\nstd::string g_wifi_set_name;\nstd::string g_ifname = \"wlan0\";\n\n// 静态变量：缓存配置值（仅当前文件可见）\nstatic std::string dt;         // 对应devType（设备类型）\nstatic std::string sn;         // 对应devSn（设备序列号）\nstatic std::string snPwd;      // 对应OVDMediaEncPassword（加密密码）\nstatic std::string timestamp;\n\n// 配置读取状态标志（确保配置只读取一次）\nstatic std::once_flag config_read_flag;\n\nvoid init_openssl() {\n    static std::once_flag ssl_init_flag;\n    std::call_once(ssl_init_flag, []() {\n        RCLCPP_INFO(rclcpp::get_logger(\"RobdogCenter\"), \"初始化OpenSSL库...\");\n        OPENSSL_init_crypto(\n            OPENSSL_INIT_LOAD_CONFIG |\n            OPENSSL_INIT_LOAD_CRYPTO_STRINGS |\n            OPENSSL_INIT_ADD_ALL_DIGESTS,\n            nullptr\n        );\n        RCLCPP_INFO(rclcpp::get_logger(\"RobdogCenter\"), \"OpenSSL库初始化完成\");\n    });\n}\n\n// 新增静态变量存储API URL\nstatic std::string api_url;  // 用于存储从param.yaml中提取的URL\nstatic bool online = true;  // 用于显示当前是测试还是现网\n\n/**\n * @brief 读取配置文件并初始化静态变量（线程安全，只执行一次）\n */\nvoid ensure_config_loaded() {\n    std::call_once(config_read_flag, []() {\n        // ===================== 第一部分：读取cmcc_dev.ini =====================\n        const std::string dev_config_path = \"/etc/cmcc_robot/cmcc_dev.ini\";\n        RCLCPP_INFO(rclcpp::get_logger(\"RobdogCenter\"), \"开始读取配置文件: %s\", dev_config_path.c_str());\n        \n        // 打开INI配置文件\n        std::ifstream dev_config_file(dev_config_path);\n        if (!dev_config_file.is_open()) {\n            RCLCPP_ERROR(\n                rclcpp::get_logger(\"RobdogCenter\"), \n                \"无法打开配置文件: %s (请检查文件权限或路径)\", \n                dev_config_path.c_str()\n            );\n            throw std::runtime_error(\"配置文件打开失败\");\n        }\n\n        // 解析INI文件\n        std::string line;\n        bool in_factory_section = false;\n\n        while (std::getline(dev_config_file, line)) {\n            // 去除行首尾空白\n            size_t start = line.find_first_not_of(\" \\t\\n\\r\");\n            if (start == std::string::npos) continue;\n            line = line.substr(start);\n            size_t end = line.find_last_not_of(\" \\t\\n\\r\");\n            line = line.substr(0, end + 1);\n\n            // 处理Section\n            if (line.front() == '[' && line.back() == ']') {\n                std::string section = line.substr(1, line.size() - 2);\n                in_factory_section = (section == \"factory\");\n                continue;\n            }\n\n            // 跳过注释\n            if (line.front() == ';' || line.front() == '#') continue;\n\n            // 处理键值对\n            if (in_factory_section) {\n                size_t eq_pos = line.find('=');\n                if (eq_pos == std::string::npos) continue;\n                \n                std::string key = line.substr(0, eq_pos);\n                std::string value = line.substr(eq_pos + 1);\n\n                // 去除键值空白\n                start = key.find_first_not_of(\" \\t\");\n                end = key.find_last_not_of(\" \\t\");\n                if (start != std::string::npos && end != std::string::npos) {\n                    key = key.substr(start, end - start + 1);\n                }\n                \n                start = value.find_first_not_of(\" \\t\");\n                end = value.find_last_not_of(\" \\t\");\n                if (start != std::string::npos && end != std::string::npos) {\n                    value = value.substr(start, end - start + 1);\n                }\n\n                // 赋值给静态变量\n                if (key == \"devType\") dt = value;\n                else if (key == \"devSn\") sn = value;\n                else if (key == \"OVDMediaEncPassword\") snPwd = value;\n            }\n        }\n\n        dev_config_file.close();\n\n        // ===================== 第二部分：读取param.yaml =====================\n        const std::string yaml_config_path = \"/usr/bin/cmcc_robot/install/homi_speech/share/homi_speech/launch/config/param.yaml\";\n        RCLCPP_INFO(rclcpp::get_logger(\"RobdogCenter\"), \"开始读取配置文件: %s\", yaml_config_path.c_str());\n        \n        // 打开YAML配置文件\n        std::ifstream yaml_config_file(yaml_config_path);\n        if (!yaml_config_file.is_open()) {\n            RCLCPP_ERROR(\n                rclcpp::get_logger(\"RobdogCenter\"), \n                \"无法打开配置文件: %s (请检查文件权限或路径)\", \n                yaml_config_path.c_str()\n            );\n            throw std::runtime_error(\"配置文件打开失败\");\n        }\n\n        // 读取整个文件内容\n        std::stringstream yaml_buffer;\n        yaml_buffer << yaml_config_file.rdbuf();\n        std::string yaml_content = yaml_buffer.str();\n        yaml_config_file.close();\n\n        // 提取JSON字符串（去除首尾单引号）\n        std::string json_str;\n        if (yaml_content.size() >= 2 && \n            yaml_content.front() == '\\'' && \n            yaml_content.back() == '\\'') {\n            json_str = yaml_content.substr(1, yaml_content.size() - 2);\n        } else {\n            json_str = yaml_content;\n        }\n\n        // 查找并提取URL\n        const std::string url_key = \"\\\"url\\\":\\\"\";\n        size_t url_start_pos = json_str.find(url_key);\n        if (url_start_pos == std::string::npos) {\n            RCLCPP_ERROR(\n                rclcpp::get_logger(\"RobdogCenter\"), \n                \"在param.yaml中未找到url字段\"\n            );\n            throw std::runtime_error(\"配置项缺失\");\n        }\n\n        url_start_pos += url_key.length();\n        size_t url_end_pos = json_str.find('\\\"', url_start_pos);\n        if (url_end_pos == std::string::npos) {\n            RCLCPP_ERROR(\n                rclcpp::get_logger(\"RobdogCenter\"), \n                \"在param.yaml中url字段格式错误\"\n            );\n            throw std::runtime_error(\"配置项格式错误\");\n        }\n\n        api_url = json_str.substr(url_start_pos, url_end_pos - url_start_pos);\n\n        // 定义常量URL模式\n        constexpr const char* PRODUCTION_URL_PATTERN = \"business.homibot.komect.com:9443\";\n        constexpr const char* TEST_URL_PATTERN = \"36.140.17.36:10000\";\n\n        // 在配置读取部分添加环境判断\n        if (api_url.find(PRODUCTION_URL_PATTERN) != std::string::npos) {\n            online = true;\n            RCLCPP_INFO(rclcpp::get_logger(\"RobdogCenter\"), \"当前为现网环境 (%s)\", api_url.c_str());\n        } else if (api_url.find(TEST_URL_PATTERN) != std::string::npos) {\n            online = false;\n            RCLCPP_INFO(rclcpp::get_logger(\"RobdogCenter\"), \"当前为测试环境 (%s)\", api_url.c_str());\n        } else {\n\n            RCLCPP_WARN(rclcpp::get_logger(\"RobdogCenter\"), \n                    \"未知API环境: %s (默认使用测试环境配置)\", \n                    api_url.c_str());\n        }\n        \n        \n        // ===================== 配置项验证 =====================\n        bool dev_config_ok = !dt.empty() && !sn.empty() && !snPwd.empty();\n        bool yaml_config_ok = !api_url.empty();\n        \n        if (!dev_config_ok || !yaml_config_ok) {\n            std::string error_msg;\n            if (!dev_config_ok) {\n                error_msg += \"dev.ini配置缺失: \";\n                if (dt.empty()) error_msg += \"devType, \";\n                if (sn.empty()) error_msg += \"devSn, \";\n                if (snPwd.empty()) error_msg += \"OVDMediaEncPassword, \";\n            }\n            if (!yaml_config_ok) {\n                error_msg += \"param.yaml配置缺失: url\";\n            }\n            \n            // 移除末尾多余的逗号和空格\n            if (!error_msg.empty() && error_msg.back() == ' ') {\n                error_msg = error_msg.substr(0, error_msg.size() - 2);\n            }\n            \n            RCLCPP_ERROR(\n                rclcpp::get_logger(\"RobdogCenter\"), \n                \"%s\", error_msg.c_str()\n            );\n            throw std::runtime_error(\"配置项缺失\");\n        }\n\n        RCLCPP_INFO(rclcpp::get_logger(\"RobdogCenter\"), \"配置文件读取成功\");\n        RCLCPP_INFO(rclcpp::get_logger(\"RobdogCenter\"), \n                    \"配置值: devType=%s, devSn=%s, snPwd=%s, api_url=%s\", \n                    dt.c_str(), sn.c_str(), snPwd.c_str(), api_url.c_str());\n    });\n}\n\n/**\n * @brief 获取当前时间戳（毫秒级）\n * @return 13位毫秒级时间戳字符串\n */\nstd::string get_current_timestamp() {\n    using namespace std::chrono;\n    auto now_ms = time_point_cast<milliseconds>(system_clock::now());\n    auto epoch = now_ms.time_since_epoch();\n    auto value = duration_cast<milliseconds>(epoch);\n    return std::to_string(value.count());\n}\n\n/**\n * @brief 计算SHA-256哈希 + Base64编码签名\n * @param data 待签名数据\n * @return 签名字符串\n */\nstd::string compute_signature(const std::string& data) {\n    init_openssl();  // 确保OpenSSL已初始化\n\n    // 创建哈希上下文\n    EVP_MD_CTX* ctx = EVP_MD_CTX_new();\n    if (!ctx) {\n        char err_buf[1024];\n        ERR_error_string_n(ERR_get_error(), err_buf, sizeof(err_buf));\n        RCLCPP_INFO(rclcpp::get_logger(\"RobdogCenter\"), \"创建哈希上下文失败: %s\", err_buf);\n        throw std::runtime_error(\"Failed to create MD context\");\n    }\n\n    unsigned char hash[EVP_MAX_MD_SIZE];\n    unsigned int hash_len = 0;\n\n    try {\n        // 初始化哈希计算\n        if (1 != EVP_DigestInit_ex(ctx, EVP_sha256(), nullptr)) {\n            char err_buf[1024];\n            ERR_error_string_n(ERR_get_error(), err_buf, sizeof(err_buf));\n            RCLCPP_INFO(rclcpp::get_logger(\"RobdogCenter\"), \"初始化SHA-256失败: %s\", err_buf);\n            throw std::runtime_error(\"Failed to initialize SHA-256\");\n        }\n\n        // 更新数据\n        if (1 != EVP_DigestUpdate(ctx, data.data(), data.size())) {\n            char err_buf[1024];\n            ERR_error_string_n(ERR_get_error(), err_buf, sizeof(err_buf));\n            RCLCPP_INFO(rclcpp::get_logger(\"RobdogCenter\"), \"更新哈希数据失败: %s\", err_buf);\n            throw std::runtime_error(\"Failed to update digest\");\n        }\n\n        // 完成计算\n        if (1 != EVP_DigestFinal_ex(ctx, hash, &hash_len)) {\n            char err_buf[1024];\n            ERR_error_string_n(ERR_get_error(), err_buf, sizeof(err_buf));\n            RCLCPP_INFO(rclcpp::get_logger(\"RobdogCenter\"), \"完成哈希计算失败: %s\", err_buf);\n            throw std::runtime_error(\"Failed to finalize digest\");\n        }\n\n        // 验证哈希长度\n        if (hash_len != EVP_MD_size(EVP_sha256())) {\n            RCLCPP_INFO(rclcpp::get_logger(\"RobdogCenter\"), \"无效的哈希长度: %d (应为32)\", hash_len);\n            throw std::runtime_error(\"Invalid hash length\");\n        }\n    } catch (...) {\n        EVP_MD_CTX_free(ctx);\n        throw;\n    }\n    EVP_MD_CTX_free(ctx);\n\n    // Base64编码\n    size_t base64_len = ((hash_len + 2) / 3) * 4;\n    std::string base64_str(base64_len, '\\0');\n    int result = EVP_EncodeBlock(\n        reinterpret_cast<unsigned char*>(base64_str.data()),\n        hash,\n        hash_len\n    );\n\n    if (result != static_cast<int>(base64_len)) {\n        char err_buf[1024];\n        ERR_error_string_n(ERR_get_error(), err_buf, sizeof(err_buf));\n        RCLCPP_INFO(rclcpp::get_logger(\"RobdogCenter\"), \n                    \"Base64编码失败: %s (预期长度: %zu, 实际: %d)\",\n                    err_buf, base64_len, result);\n        throw std::runtime_error(\"Base64 encoding failed\");\n    }\n\n    return base64_str;\n}\n\n/**\n * @brief URL编码函数（处理所有非保留字符，生成%XX格式）\n * @param s 待编码的字符串（如Base64签名）\n * @return 编码后的字符串（大写十六进制，如+→%2B）\n */\nstring urlEncode(const string& s) {\n    ostringstream oss;\n    oss << hex << uppercase; // 设置为十六进制大写格式\n\n    for (char c : s) {\n        // 保留字符：直接输出（字母、数字、-、_、.、~）\n        if (isalnum(static_cast<unsigned char>(c)) || \n            c == '-' || c == '_' || c == '.' || c == '~') {\n            oss << c;\n        } else {\n            // 非保留字符：编码为%XX（补0至两位十六进制）\n            oss << \"%\" \n                << setw(2) << setfill('0') // 确保两位，不足补0\n                << static_cast<int>(static_cast<unsigned char>(c)); // 避免符号扩展\n        }\n    }\n    return oss.str();\n}\n\n/**\n * @brief 生成签名\n * @return 签名字符串\n * @note 此函数会确保配置加载、生成时间戳、拼接数据并计算签名\n */\nstd::string generate_signature() {\n    try {\n        \n        // 获取当前时间戳（毫秒级）\n        timestamp = get_current_timestamp();\n        RCLCPP_INFO(rclcpp::get_logger(\"RobdogCenter\"), \"生成时间戳: %s\", timestamp.c_str());\n        \n        // 拼接待签名字符串\n        std::ostringstream data_stream;\n        data_stream << \"did=\" << sn\n                   << \"&dt=\" << dt\n                   << \"&timestamp=\" << timestamp\n                   << \"&snPwd=\" << snPwd;\n        \n        std::string data = data_stream.str();\n        RCLCPP_INFO(rclcpp::get_logger(\"RobdogCenter\"), \"待签名数据: %s\", data.c_str());\n        \n        // 计算签名\n        std::string signature = compute_signature(data);\n        RCLCPP_INFO(rclcpp::get_logger(\"RobdogCenter\"), \"生成签名: %s\", signature.c_str());\n        \n        return signature;\n    } catch (const std::exception& e) {\n        RCLCPP_INFO(rclcpp::get_logger(\"RobdogCenter\"), \"生成签名失败: %s\", e.what());\n        throw;\n    }\n}\n\nbool dynamic_QRcode() {\n    try {\n        // 确保配置已加载（线程安全）\n        ensure_config_loaded();\n\n        // ---------------------- 1. 定义路径（固定） ----------------------\n        const std::string nobind_dir = \"/usr/bin/cmcc_robot/install/robdog_control/share/robdog_control/resource/video/dynamic_nobind\";\n        const std::string img_path = nobind_dir + \"/nobind.jpg\";      // 二维码图片路径\n        const std::string video_path = nobind_dir + \"/dynamic_nobind.mp4\";    // 输出视频路径\n\n        // ---------------------- 2. 创建文件夹（避免路径不存在） ----------------------\n        if (!std::filesystem::exists(nobind_dir)) {\n            if (!std::filesystem::create_directories(nobind_dir)) {\n                RCLCPP_INFO(rclcpp::get_logger(\"RobdogCenter\"), \"无法创建文件夹: %s\", nobind_dir.c_str());\n                return false;\n            }\n            RCLCPP_INFO(rclcpp::get_logger(\"RobdogCenter\"), \"创建文件夹成功: %s\", nobind_dir.c_str());\n        }\n\n        // ---------------------- 3. 生成签名与构造API URL ----------------------\n        std::string sign = generate_signature();  // 生成签名（依赖ensure_config_loaded）\n        std::string encoded_sign = urlEncode(sign); // 2. 对签名进行URL编码（关键步骤）        \n        std::ostringstream url_stream;\n\n        // 根据环境选择API地址（修正原逻辑：online=true为测试环境，false为现网）\n        if (online) {\n            url_stream << \"https://business.homibot.komect.com:9443/robot/business/api/user/client/robot/qr/getDeviceInfo?\";\n        } else {\n            url_stream << \"http://36.140.17.36:10000/robot/business/api/user/client/robot/qr/getDeviceInfo?\";\n        }\n\n        // 添加查询参数（注意：参数名需与API要求一致，此处假设为`sign`）\n        url_stream << \"did=\" << sn                 // 设备序列号（来自配置）\n                   << \"&dt=\" << dt                 // 设备类型（来自配置）\n                   << \"&timestamp=\" << timestamp   // 时间戳（generate_signature中生成）\n                   << \"&sign=\" << encoded_sign;            // 签名（generate_signature的返回值）\n\n        std::string api_url = url_stream.str();\n        RCLCPP_INFO(rclcpp::get_logger(\"RobdogCenter\"), \"二维码API URL: %s\", api_url.c_str());\n\n        // ---------------------- 4. 生成二维码图片（使用qrencode） ----------------------\n        std::ostringstream qr_cmd_stream;\n        qr_cmd_stream << \"qrencode -o \\\"\" << img_path << \"\\\" \"  // 输出图片路径（双引号避免空格问题）\n                      << \"-s 10 \"                               // 二维码像素大小（10x10）\n                      << \"'\" << api_url << \"'\";                 // 编码内容（API URL）\n\n        std::string qr_cmd = qr_cmd_stream.str();\n        RCLCPP_INFO(rclcpp::get_logger(\"RobdogCenter\"), \"执行二维码生成命令: %s\", qr_cmd.c_str());\n\n        // 执行命令（返回0表示成功）\n        int qr_status = system(qr_cmd.c_str());\n        if (qr_status != 0) {\n            RCLCPP_ERROR(rclcpp::get_logger(\"RobdogCenter\"), \"二维码生成失败，返回状态: %d\", qr_status);\n            return false;\n        }\n        RCLCPP_INFO(rclcpp::get_logger(\"RobdogCenter\"), \"二维码已生成: %s\", img_path.c_str());\n\n        // ---------------------- 5. 生成循环视频（使用ffmpeg） ----------------------\n        std::ostringstream ffmpeg_cmd_stream;\n        ffmpeg_cmd_stream << \"ffmpeg \"\n                          << \"-loop 1 \"                                  // 循环输入图片（静态图转视频）\n                          << \"-i \\\"\" << img_path << \"\\\" \"                 // 输入图片路径\n                          << \"-c:v libx264 \"                             // 使用H.264编码\n                          << \"-preset ultrafast \"                         // 最快编码速度（牺牲画质换速度）\n                          << \"-tune stillimage \"                         // 针对静态图片优化（减少文件大小）\n                          << \"-vf \\\"transpose=2,scale=800:480:force_original_aspect_ratio=decrease,pad=800:480:(ow-iw)/2:(oh-ih)/2\\\" \"\n                          << \"-r 1 \"                                     // 帧率：1帧/秒（静态图无需高帧率）\n                          << \"-g 1 \"                                     // GOP大小：1（每帧都是关键帧，方便seek）\n                          << \"-x264-params keyint=1 \"                    // 强制每帧为关键帧（与-g 1配合）\n                          << \"-t 2 \"                                     // 视频时长：2秒（循环播放）\n                          << \"-pix_fmt yuv420p \"                         // 像素格式：兼容大部分设备\n                          << \"-an \"                                      // 禁用音频（静态图无需音频）\n                          << \"-y \"                                       // 覆盖输出文件（无需确认）\n                          << \"\\\"\" << video_path << \"\\\"\";                 // 输出视频路径\n\n        std::string ffmpeg_cmd = ffmpeg_cmd_stream.str();\n        RCLCPP_INFO(rclcpp::get_logger(\"RobdogCenter\"), \"执行视频生成命令: %s\", ffmpeg_cmd.c_str());\n\n        // 执行命令（返回0表示成功）\n        int ffmpeg_status = system(ffmpeg_cmd.c_str());\n        if (ffmpeg_status != 0) {\n            RCLCPP_INFO(rclcpp::get_logger(\"RobdogCenter\"), \"视频生成失败，返回状态: %d\", ffmpeg_status);\n            return false;\n        }\n        RCLCPP_INFO(rclcpp::get_logger(\"RobdogCenter\"), \"视频已生成: %s\", video_path.c_str());\n\n        // ---------------------- 6. 成功返回 ----------------------\n        return true;\n    } catch (const std::exception& e) {\n        RCLCPP_INFO(rclcpp::get_logger(\"RobdogCenter\"), \"生成二维码/视频失败: %s\", e.what());\n        return false;\n    }\n}\n\nvoid notifyWsMsgCallback(void *handle, const char *msg, int index) {\n    nConnectIndex_ = index;\n    // RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"notifyWsMsgCallback: %s\", msg);\n    currentTimeStramp_ = base::homiUtils::getCurrentTimeStamp();\n\n    RobdogCenter *parent = (RobdogCenter *)handle;\n    Json::Reader reader;\n    Json::Value value;\n\n    if (false == reader.parse(msg, value)) {\n        return;\n    }\n    if (!value[\"type\"].isNull()) {\n        string strType = value[\"type\"].asString();\n        if (\"connect_success\" == strType) {\n            bConnected_ = true;\n            std::this_thread::sleep_for(std::chrono::milliseconds(20));\n            Json::Value value;\n            Json::Value params;\n            value[\"client_type\"] = CLIENT_LAUNCHER;\n            value[\"action\"] = \"success\";\n            RobdogCenter::getInstance().SendtoNvOrin(value.toStyledString().c_str(), nConnectIndex_);\n        }\n    }\n    if (!value[\"action\"].isNull()) {\n        if (parent) {\n            parent->parseWsActionMsg(value);\n        }\n    }\n}\n\nstd::string removeEscapeCharacters(const std::string& input) {\n    std::string result;\n    for (size_t i = 0; i < input.length(); ++i) {\n        if (input[i] == '\\\\') {\n            // Skip the escape character and handle the next character\n            ++i;\n            if (i < input.length()) {\n                switch (input[i]) {\n                    case 'n':  // Newline\n                    case 't':  // Tab\n                    case 'r':  // Carriage return\n                    case 'b':  // Backspace\n                    case 'f':  // Form feed\n                    case 'a':  // Alert (bell)\n                    case '\\\\': // Backslash\n                    case '\"':  // Double quote\n                    case '\\'': // Single quote\n                        // Do nothing, skip these characters\n                        result += input[i];\n                        break;\n                    default:\n                        // If it's an unknown escape sequence, add the backslash and the character\n                        result += '\\\\';\n                        result += input[i];\n                        break;\n                }\n            }\n        } else {\n            result += input[i];\n        }\n    }\n    return result;\n}\n\nvoid replaceKeyInJsonList(Json::Value& jsonList, const std::string& oldKey, const std::string& newKey) {\n    for (Json::Value::iterator it = jsonList.begin(); it != jsonList.end(); ++it) {\n        if (it->isMember(oldKey)) {\n            (*it)[newKey] = (*it)[oldKey];\n            it->removeMember(oldKey);\n        }\n    }\n}\n\nvoid RobdogCenter::loadMappingTableFromParameters()\n{\n    try {\n        auto video_paths = node_->get_parameter(\"mapping_video_paths\").as_string_array();\n        auto light_cmds = node_->get_parameter(\"mapping_light_cmds\").as_integer_array();\n        auto light_params = node_->get_parameter(\"mapping_light_params\").as_integer_array();\n        auto actions = node_->get_parameter(\"mapping_actions\").as_string_array();\n        auto audio_paths = node_->get_parameter(\"mapping_audio_paths\").as_string_array();\n        auto intervals = node_->get_parameter(\"mapping_intervals\").as_integer_array();\n        const size_t count = video_paths.size();\n        if (light_cmds.size() != count || \n            light_params.size() != count ||\n            actions.size() != count ||\n            audio_paths.size() != count ||\n            intervals.size() != count) {\n            RCLCPP_ERROR(rclcpp::get_logger(\"robdog_control\"), \"Inconsistent array sizes in mapping parameters!\");\n            return;\n        }\n        mapping_table_.clear();\n        for (size_t i = 0; i < count; ++i) {\n            MappingEntry entry{\n                video_paths[i],\n                static_cast<int>(light_cmds[i]),\n                static_cast<int>(light_params[i]),\n                actions[i],\n                audio_paths[i],\n                static_cast<uint32_t>(intervals[i])\n            };\n            mapping_table_.push_back(entry);\n        }\n    }catch (const rclcpp::exceptions::InvalidParameterTypeException& e) {\n        RCLCPP_ERROR(rclcpp::get_logger(\"robdog_control\"), \"Invalid parameter type: %s\", e.what());\n    } catch (const std::exception& e) {\n        RCLCPP_ERROR(rclcpp::get_logger(\"robdog_control\"), \"Error loading mapping table: %s\", e.what());\n    }\n}\n\nRobdogCenter::RobdogCenter() {\n    mapping_table_ = {\n        {\"video/emotion/neutrality/\",DEEP_CMD_LIGHT_05,0,\"sitDown\",\"audio/boot/intro1.wav\",12},\n        {\"video/emotion/happy/\",DEEP_CMD_LIGHT_PINK_KEEP_AWAKE,0,\"happy\",\"audio/boot/intro2.wav\",8},\n        {\"videos/greeting/\",DEEP_CMD_LIGHT_BLUE_KEEP_AWAKE,0,\"\",\"audio/boot/intro3.wav\",3},\n        {\"videos/greeting/\",DEEP_CMD_LIGHT_ORANGE_KEEP_AWAKE,0,\"greeting\",\"audio/boot/intro4.wav\",5},\n        {\"videos/emotion/festival/\",DEEP_CMD_LIGHT_PINK_KEEP_AWAKE,0,\"\",\"audio/boot/intro5.wav\",14},\n    };\n}\n\nRobdogCenter::~RobdogCenter() {\n    stopSequence();\n    if (sequence_thread_.joinable()) {\n        sequence_thread_.join();\n    }\n}\n\nvoid RobdogCenter::update_map_points_path() {\n    std::string map_points_path_dft = node_->getResourcePath(\"config/map_points.json\");\n    std::string current_mapid = RobotState::getInstance().getMapId();\n    // std::string map_points_path_dft = \"/etc/cmcc_robot\";\n    if (!node_->has_parameter(\"map_points_path\")) {\n        node_->declare_parameter<std::string>(\"map_points_path\", map_points_path_dft); \n    }\n    map_points_path = node_->get_parameter(\"map_points_path\").as_string()+\"/\"+ current_mapid +\".json\";\n    if(map_points_path_dft.c_str() != map_points_path && access(map_points_path.c_str(), F_OK) != 0) {\n        // if map_points_path not config/map_points.json and map_points_path does not exist, copy it from config/map_points.json\n        fs::copy_file(map_points_path_dft.c_str(), map_points_path.c_str());\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"missing %s, copy from %s\", map_points_path.c_str(), map_points_path_dft.c_str());\n    }\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"load map_points %s\", map_points_path.c_str());\n    //ReadMapCfg::getInstance().loadConfig(map_points_path); // 点位信息\n}\n\nvoid RobdogCenter::init(RobdogCtrlNode* node) {\n    node_ = node;\n\n    node_->handleLightControl(DEEP_CMD_LIGHT_03, 0);\n\n    lastMoveMessageTime = node_->now();\n    //Yaml文件读取参数，写到形参\n    std::string robotdog_file_path_dft = node_->getResourcePath(\"config/config_robotdog.xml\");\n    if (!node_->has_parameter(\"robotdog_file_path\")) {\n        node_->declare_parameter<std::string>(\"robotdog_file_path\", robotdog_file_path_dft); \n    }\n    std::string robotdog_file_path = node_->get_parameter(\"robotdog_file_path\").as_string();  \n    if (access(robotdog_file_path.c_str(), F_OK) != 0) {\n        //copyFile(robotdog_file_path_dft, robotdog_file_path);\n        fs::copy_file(robotdog_file_path_dft.c_str(), robotdog_file_path.c_str());\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"missing %s, copy from %s\", robotdog_file_path.c_str(), robotdog_file_path_dft.c_str());\n    }\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"robotdog_file_path: %s\", robotdog_file_path.c_str());\n    RobotState::getInstance().loadConfig(robotdog_file_path);\n\n    update_map_points_path();\n    ReadMapCfg::getInstance().loadConfig(map_points_path); // 点位信息\n\n    node_->declare_parameter<string>(\"ws_connect_url\", \"ws://192.168.1.110:19002\"); \n    strConnectUrl_ = node_->get_parameter(\"ws_connect_url\").as_string();  \n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"ws_connect_url: %s\", strConnectUrl_.c_str());\n\n    node_->declare_parameter<int>(\"ws_connect_port\", 19002); \n    uint32_t ws_port = node_->get_parameter(\"ws_connect_port\").as_int();  \n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"ws_connect_port: %d\", ws_port);\n\n    node_->declare_parameter<int>(\"max_cpu_temp\", 67);\n    max_cpu_temp_ = node_->get_parameter(\"max_cpu_temp\").as_int();\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"max_cpu_temp: %d\", max_cpu_temp_);\n\n    node_->declare_parameter<int>(\"max_joint_temp\", 110);\n    max_joint_temp_ = node_->get_parameter(\"max_joint_temp\").as_int();\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"max_joint_temp: %d\", max_joint_temp_);\n\n    node_->declare_parameter<int>(\"min_power_req\", 13);\n    min_power_req_ = node_->get_parameter(\"min_power_req\").as_int();\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"min_power_req: %d\", min_power_req_);\n\n    std::vector<std::string> default_string_array;\n    std::vector<int64_t> default_int_array;\n    \n    node_->declare_parameter(\"mapping_video_paths\", default_string_array);\n    node_->declare_parameter(\"mapping_light_cmds\", default_int_array);\n    node_->declare_parameter(\"mapping_light_params\", default_int_array);\n    node_->declare_parameter(\"mapping_actions\", default_string_array);\n    node_->declare_parameter(\"mapping_audio_paths\", default_string_array);\n    node_->declare_parameter(\"mapping_intervals\", default_int_array);\n    loadMappingTableFromParameters();\n    if (mapping_table_.empty()) {\n        RCLCPP_ERROR(rclcpp::get_logger(\"robdog_control\"), \"Failed to load mapping table!\");\n    } else {\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Loaded %zu mapping entries\", mapping_table_.size());\n        for (const auto& entry : mapping_table_) {\n            RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Video: %s, Action: %s, Audio: %s, Interval: %u\",\n                        entry.video_path.c_str(),\n                        entry.action.c_str(),\n                        entry.audio_path.c_str(),\n                        entry.interval_seconds);\n        }\n    }\n    // WS服务启动\n    WS_Init(EN_WS_ClIENT, ws_port);\n    \n    //设置接受msg的回调函数\n    WS_SetMsgCallback(notifyWsMsgCallback, this);\n    WS_Connect(strConnectUrl_.c_str());\n    // ******************************************** ROS2\n    platCmd_sub_ = node_->create_subscription<homi_speech_interface::msg::SIGCEvent>(\n            \"/homi_speech/sigc_event_topic\", 10,\n            [this, topic_name = \"/homi_speech/sigc_event_topic\"](\n                const homi_speech_interface::msg::SIGCEvent::SharedPtr msg) {\n                robctrlCallback(msg, topic_name);\n            }\n    );\n    \n    app_sub = node_->create_subscription<homi_speech_interface::msg::SIGCEvent>(\n            \"/homi_speech/sigc_event_topic_APP\", 10,\n            [this, topic_name = \"/homi_speech/sigc_event_topic_APP\"](\n                const homi_speech_interface::msg::SIGCEvent::SharedPtr msg) {\n                robctrlCallback(msg, topic_name);\n            }\n    );\n    devAlarmRep_sub_ = node_->create_subscription<std_msgs::msg::String>(\n        \"/device_alarm_report\", 20, \n        std::bind(&RobdogCenter::devAlarmReportCallback, this, std::placeholders::_1)); \n\n    \n    deepCtrl_sub_ = node_->create_subscription<homi_speech_interface::msg::ProprietySet>(\n        \"/deep_udp_ctrl/status_report\", 1, \n        std::bind(&RobdogCenter::deepStatusCallback, this, std::placeholders::_1)); // 接收到控制节点的消息后回调，写状态（主要是电量,充电状态等主动获取的状态）c\n\n    rtk_status_sub_=node_->create_subscription<sensor_msgs::msg::NavSatFix>(\n        \"/fix\", 10,\n        std::bind(&RobdogCenter::navSatStatusCallback, this, std::placeholders::_1));\n\n    navTripStatus_sub_ = node_->create_subscription<std_msgs::msg::String>(\"/task_status\", \n        10, std::bind(&RobdogCenter::navTripStatusCallback, this, std::placeholders::_1));\n\n    internet_connect_status_sub_ = node_->create_subscription<std_msgs::msg::String>(\n        \"internet_connect_status\", 10,\n        std::bind(&RobdogCenter::checkInternetConnectStatus, this, std::placeholders::_1));\n\n    internet_conflict_sub_=node_->create_subscription<std_msgs::msg::String>(\n        \"internet_conflict\", 10,\n        std::bind(&RobdogCenter::checkInternetConflictStatus, this, std::placeholders::_1));\n\n    net_sub_  = node_->create_subscription<std_msgs::msg::String>(\n      \"/net_monitor\", 10,\n      std::bind(&RobdogCenter::netMonitorCallback, this, std::placeholders::_1));\n    \n    wake_sub_=node_->create_subscription<homi_speech_interface::msg::Wakeup>(\n        \"/audio_recorder/wakeup_event\", 10,\n        std::bind(&RobdogCenter::wakeupCallback, this, std::placeholders::_1));\n    velCmd_pub = node_->create_publisher<geometry_msgs::msg::Twist>(\n        \"/catch_turtle/ctrl_instruct\", 10); // 向狗子发布速度命令话题（robot_move）\n    actionCmd_pub = node_->create_publisher<homi_speech_interface::msg::RobdogAction>(\n        \"/catch_turtle/action_type\", 1); // 向狗子发送特定运动指令消息（robot_action）\n    continueMoveCmd_pub = node_->create_publisher<homi_speech_interface::msg::ContinueMove>(\n        \"/catch_turtle/continue_move\", 1); // 向狗子发送持续运动信息（robot_move）\n    follow_control_pub_ = node_->create_publisher<std_msgs::msg::String>(\"/follow_me/from_control\", 10);\n    adjust_distance_publisher_=node_->create_publisher<std_msgs::msg::String>(\"/adjust_distance\",10);\n    andlink_userkey_pub_ = node_->create_publisher<std_msgs::msg::String>(\"andlink_userkey\", 10);\n    flashlight_control_pub_ = node_->create_publisher<std_msgs::msg::String>(\"light_control\", 10);\n\n    /****************************** 感知算法交互模块 **************************/\n    actionPlanningMove_pub = node_->create_publisher<std_msgs::msg::String>(\n        \"/navigation_control\", 10); // 发布机器狗固定点位坐标到感知主机\n    mappingControl_pub = node_->create_publisher<std_msgs::msg::String>(\n        \"/mapping_control\", 10); // 地图更新给感知主机\n    publishVirtualWall = node_->create_publisher<std_msgs::msg::String>(\n        \"/virtual_wall_control\", 10); // 虚拟墙\n    nvidiaService_client = node_->create_client<std_srvs::srv::Trigger>(\n        \"/current_task\");//查询算法当前任务状态，包括建图导航漫步充电\n    client_command_publisher_ = node_->create_publisher<std_msgs::msg::String>(\n        \"/uslam/client_command\", 10);// 宇树slam接口\n    point_transform_publisher_ = node_->create_publisher<std_msgs::msg::String>(\n        \"/point_transform\", 10);\n    /****************************************************************************************/\n    platform_client = node_->create_client<homi_speech_interface::srv::SIGCData>(\n        \"/homi_speech/sigc_data_service\"); // 上发给平台的消息\n    app_client = node_->create_client<homi_speech_interface::srv::SIGCData>(\n        \"/homi_speech/sigc_data_service_APP\");\n    net_client = node_->create_client<homi_speech_interface::srv::NetCtrl>(\n        \"/homi_speech/network_service\");\n    // 向APP发送设备状态消息\n    prope2app_pub = node_->create_publisher<homi_speech_interface::msg::ProperToApp>(\n        \"/catch_turtle/prope_toapp\", 1);\n    /****************************** 定时器 **********************************/\n    ws_heartbeat_timer_ = node_->create_wall_timer(\n        std::chrono::seconds(15),\n        std::bind(&RobdogCenter::heartbeatTimerCallback, this));\n\n    \n    internet_timer_ = node_->create_wall_timer(\n        std::chrono::seconds(5),\n        std::bind(&RobdogCenter::internetTimerCallback, this));\n\n    trip_timer_ = node_->create_wall_timer(\n        std::chrono::seconds(2),\n        std::bind(&RobdogCenter::tripTimerCallback, this));\n    \n    abnormal_monitor_trip = node_->create_wall_timer(\n        std::chrono::milliseconds(300),\n        std::bind(&RobdogCenter::trip_abnormal_monitor, this));\n    // 定时器，1秒触发一次\n    timer_2 = node_->create_wall_timer(\n        std::chrono::seconds(1),\n        std::bind(&RobdogCenter::publishProperties2APP, this));\n\n    timer_robMove = node_->create_wall_timer(\n        std::chrono::milliseconds(static_cast<int>(timer_interval * 1000)),\n        std::bind(&RobdogCenter::timerCallback, this));\n\n    // robPoseStatusTimer_ = node_->create_wall_timer(\n    //     std::chrono::seconds(1),\n    //     std::bind(&RobdogCenter::timerRobotPoseCallback, this));\n\n    utStateTimer_ = node_->create_wall_timer(\n        std::chrono::seconds(2),\n        std::bind(&RobdogCenter::utStatusCallback, this));\n\n    // robPathStatusTimer_ = node_->create_wall_timer(\n    //     std::chrono::seconds(1),\n    //     std::bind(&RobdogCenter::timerRobotPathCallback, this));\n\n    // 初始化10分钟定时器用于检测是否需要主动求陪伴\n    last_active_time_ = node_->now();\n    inactivity_timer_ = node_->create_wall_timer(\n        std::chrono::minutes(10),\n        std::bind(&RobdogCenter::handleInactivityTimeout, this));\n\n    // 初始化5分钟定时器用于检测是否进入休眠状态\n    sleep_timer_ = node_->create_wall_timer(\n        std::chrono::minutes(5),\n        std::bind(&RobdogCenter::handleSleepTimeout, this));\n\n    check_pos_timer_ = node_->create_wall_timer(\n      std::chrono::seconds(1), std::bind(&RobdogCenter::check_pos_timeout, this));\n\n    // odom_sub_ = node_->create_subscription<nav_msgs::msg::Odometry>(\n    //   \"/leg_odom2\", 10, std::bind(&RobdogCenter::odom_callback, this, std::placeholders::_1));\n\n    // 停止5分钟定时器，因为默认是亲密互动模式\n    sleep_timer_->cancel();\n    \n    /****************************** 智能播报相关 **********************************/\n    // 智能播报服务的客户端【上传播报文本】\n    brocast_client = node_->create_client<homi_speech_interface::srv::AssistantSpeechText>(\n        \"/homi_speech/helper_assistant_speech_text_service\");\n\n    //是否结束当前任务\n    endTask_client = node_->create_client<homi_speech_interface::srv::AssistantSpeechText>(\n        \"/homi_speech/helper_assistant_end_text_service\");\n    \n    // wake_pub_ = node_->create_publisher<homi_speech_interface::msg::Wakeup>(\"/audio_recorder/wakeup_event\", 10);\n    developer_mode_pub_ = node_->create_publisher<std_msgs::msg::String>(\"/developer_mode_topic\", 10);\n\n    // 请求语音助手打断当前正在播放的内容\n    brocast_abort_client = node_->create_client<homi_speech_interface::srv::AssistantAbort>(\n        \"/homi_speech/helper_assistant_abort_service\");\n    \n    // 关闭和开启语音助手\n    set_wake_client = node_->create_client<homi_speech_interface::srv::SetWakeEvent>(\n        \"/audio_node/set_wake_event_service\");\n\n    timer_brocast = node_->create_wall_timer(\n        std::chrono::seconds(40),\n        std::bind(&RobdogCenter::SendBrocastCallback, this));\n\n    // 语音助手下发的是否播报被打断指令\n    brocast_sub = node_->create_subscription<homi_speech_interface::msg::AssistantEvent>(\n        \"/homi_speech/speech_assistant_status_topic\", 100,\n        std::bind(&RobdogCenter::BrocastIfAbortCallBack, this, std::placeholders::_1));\n    /****************************** 定时器 **********************************/\n    // 发布状态信息话题\n    status_pub_ = node_->create_publisher<homi_speech_interface::msg::ProprietySet>(\n        \"/deep_udp_ctrl/status_ctrl\", 1);\n\n    // 定时器检查状态\n    timerDog = node_->create_wall_timer(\n        std::chrono::milliseconds(100),\n        std::bind(&RobdogCenter::checkStatusWatchdog, this));\n\n    // 自定义唤醒词\n    set_diyWakeup_client_ = node_->create_client<homi_speech_interface::srv::SetDiyWord>(\n        \"/audio_recorder/diy_wakeup_service\");\n\n    // 接听/挂断/呼叫\n    phone_call_client_ = node_->create_client<homi_speech_interface::srv::PhoneCall>(\n        \"/cmcc_rtc/phone_call_service\");\n\n    /****************************** RTK帐号下发 **********************************/\n    ntrip_account_client_ = node_->create_client<homi_speech_interface::srv::NtripAccount>(\"/ntrip_user_pwd\");\n    ntrip_expire_day_sub_  = node_->create_subscription<std_msgs::msg::String>(\n      \"/ntrip_expire_day\", 10,\n      std::bind(&RobdogCenter::ntripExpireDayCallback, this, std::placeholders::_1));\n    ntrip_status_sub_  = node_->create_subscription<std_msgs::msg::Int64>(\n      \"/ntrip_status\", 10,\n      std::bind(&RobdogCenter::ntripStatusCallback, this, std::placeholders::_1));\n    ntrip_account_sub_  = node_->create_subscription<std_msgs::msg::String>(\n      \"/ntrip_account_topic\", 10,\n      std::bind(&RobdogCenter::ntripAccountCallback, this, std::placeholders::_1));\n    rtkAccountReportTimer_ = node_->create_wall_timer(\n      std::chrono::seconds(30),  // 30秒后首次触发\n      std::bind(&RobdogCenter::rtkAccountReportTimerCallback, this)\n    );\n\n    // use unitree API check nvi status\n    unitreeServerLog_sub_ = node_->create_subscription<std_msgs::msg::String>(\n        \"/uslam/server_log\", 10, \n        std::bind(&RobdogCenter::uslamServerLogCallback, this, std::placeholders::_1)); \n    unitreeCmdClient_pub_ = node_->create_publisher<std_msgs::msg::String>(\n        \"/uslam/client_command\", 10);\n    \n    point_transform_result_sub_  = node_->create_subscription<std_msgs::msg::String>(\n        \"/point_transform_result\", 10,\n        std::bind(&RobdogCenter::pointTransformResultCallback, this, std::placeholders::_1));\n    \n    /****************************** 安防任务相关 **********************************/    \n    liveStreamTask_pub = node_->create_publisher<homi_speech_interface::msg::LiveStreamTask>(\"/live_stream/live_stream_task\", 10);\n\n    buildStatusToCodeMapping();\n}\n\n// void RobdogCenter::resetInactivityTimer() {\n//     inactivity_timer_->cancel();\n//     inactivity_timer_->reset();\n// }\n\nvoid RobdogCenter::SendtoNvOrin(const char* message, int nInd)\n{\n    WS_Send(message, nInd);\n}\n\nstd::string RobdogCenter::getSingleMessage(const std::vector<std::string>& messages)\n{\n    if (messages.empty()){\n    return \"\";\n    }\n    return messages[0];\n}\n\n void RobdogCenter::navSatStatusCallback(const sensor_msgs::msg::NavSatFix::SharedPtr msg)\n {\n    rtkMsg=*msg;\n    status_.rtk.current=rtkMsg.status.status;\n    if(rtkMsg.status.status<=0){\n        rtkMsg.latitude=0;\n        rtkMsg.longitude=0;\n    }\n    int8_t status=msg->status.status;\n    if(status == 2 || status == 1){\n        rtk_status_flag = true;\n    }\n    else{\n        rtk_status_flag = false;\n    }\n }\n\nvoid RobdogCenter::checkInternetConnectStatus(const std_msgs::msg::String::SharedPtr msg)\n{\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Received message: '%s'\", msg->data.c_str());\n    std::string strMsg = msg->data; // JSON字段\n    Json::Value value;\n    Json::Reader reader;\n    if (!reader.parse(strMsg, value)) return; // 解析 JSON\n\n    if (value.isMember(\"isInternetConnect\"))\n    {\n        std::string isInternetConnect = value[\"isInternetConnect\"].asString();\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"isInternetConnect: %s\", isInternetConnect.c_str());\n\n        bool new_status = (isInternetConnect == \"true\");\n\n        // 仅在网络状态发生变化时执行动作\n        if (new_status != is_internet_connected_ ) {\n            RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Internet status is changed !!!\");\n            std::string video_path;\n            if (new_status) {\n                // video_path = node_->getResourcePath(resource_default_video_path);\n                // // 播报网络连接成功提示音\n                // std::thread t_audio(&RobdogCtrlNode::playAudio,node_,node_->getResourcePath(\"internet/internet_normal.wav\"));\n                // t_audio.detach();   \n\n                handleDeviceSettingQuery();             \n            }\n            else {\n                RCLCPP_WARN(rclcpp::get_logger(\"robdog_control\"), \"Internet is disconnected.\");\n            }\n            ExpressionChange::getInstance().async_callback_work(video_path, 0);\n        } \n\n        is_internet_connected_ = new_status;\n\n    } \n    else {\n        RCLCPP_ERROR(rclcpp::get_logger(\"robdog_control\"), \"Key 'isInternetConnect' not found in JSON\");\n    }\n\n    if (value.isMember(\"isWifiConnect\"))\n    {\n        std::string isWifiConnect = value[\"isWifiConnect\"].asString();\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"isWifiConnect: %s\", isWifiConnect.c_str());\n\n        bool new_status = (isWifiConnect == \"true\");\n\n        if (new_status != is_wifi_connected_ ) {\n            RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"wifi is connected.\");\n            if (new_status) {\n                std::vector<std::string> messages = {\n                    \"我们是回家了吗\"};\n                std::string message = getSingleMessage(messages);\n                RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"current message: %s\", message.c_str());\n                sendStringToBrocast(message);                \n            }\n            else {\n                RCLCPP_WARN(rclcpp::get_logger(\"robdog_control\"), \"wifi is disconnected.\");\n                current_mode_ = RobotState::getInstance().getRobdogStatus();\n                if(current_mode_ == 0 && rtk_status_flag)\n                {\n                    std::vector<std::string> messages = {\n                    \"我们是要去外面玩了吗\"};\n                    std::string message = getSingleMessage(messages);\n                    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"current message: %s\", message.c_str());\n                    sendStringToBrocast(message);\n                }\n\n            }\n        }\n\n        is_wifi_connected_ = new_status;  \n\n    }\n    else {\n        RCLCPP_ERROR(rclcpp::get_logger(\"robdog_control\"), \"Key 'isWifiConnect' not found in JSON\");\n    }\n\n}\n\n\nvoid RobdogCenter::checkInternetConflictStatus(const std_msgs::msg::String::SharedPtr msg){\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Received checkInternetConflictStatus message: '%s'\", msg->data.c_str());\n    std::string strMsg = msg->data; \n    if(strMsg ==\"wlan0_conflict_notify\"){\n      \n        // 播报网络冲突提示音\n        std::thread t_audio(&RobdogCtrlNode::playAudio,node_,node_->getResourcePath(\"internet/internet_conflict.wav\"));\n        t_audio.detach(); \n        \n    }\n\n}\n\n// 更新上次活动时间\nvoid RobdogCenter::updateLastActiveTime()\n{\n    last_active_time_ = node_->now();\n    asked_in_last_hour_ = false;\n    quiet_for_three_hours_ = false;\n}\n\n// 机器狗主动求陪伴逻辑\nvoid RobdogCenter::activeDog()\n{\n    //室内漫步需要切换到自主模式\n    homi_speech_interface::msg::RobdogAction zzmsg;\n    zzmsg.actiontype = \"NavCtrl\";\n    zzmsg.actionargument = \"AutoMode\";\n    // publishAction(zzmsg);\n    publishAction(std::make_shared<homi_speech_interface::msg::RobdogAction>(zzmsg));\n\n    // 预留和导航原地溜达接口\n    Json::Value reqValue;\n    reqValue[\"client_type\"] = CLIENT_LAUNCHER;\n    reqValue[\"target_client\"] = CLIENT_NVIDIA;         \n    reqValue[\"action\"] = \"ramble_control\";\n    \n    Json::Value value_ramble;\n    value_ramble[\"action\"] = 0;\n    reqValue[\"params\"] = value_ramble;\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"),\"The message of value_ramble: %s\", reqValue.toStyledString().c_str());\n    SendtoNvOrin(reqValue.toStyledString().c_str(), nConnectIndex_);\n    \n    std::vector<std::string> messages = {\n        \"唉, 我这会儿感觉挺无聊的, 有没有人能陪我玩会儿呢?\",\n        \"哎哟, 我无聊得很啦, 有没有人来和我一起玩会儿呀?\",\n        \"呀, 我有点无聊, 真希望有人能陪我玩一会儿.\",\n        \"哎呀, 我实在无聊, 谁能来跟我消遣一会儿呀?\",\n        \"嘿, 我这会儿无聊透顶, 有没有人能来和我玩会儿?\",\n        \"哟, 我觉得好无聊, 有没有人愿意陪我玩上一会儿呢?\",\n        \"哎, 无聊感袭来, 有没有人可以跟我玩一会儿呀?\",\n        \"哎呀, 无聊得慌, 有没有人能抽空和我玩会儿呢?\",\n        \"嗨, 我无聊得不行, 有没有人能陪我娱乐一会儿呀?\",\n        \"哟呵, 我太无聊啦, 有没有人来跟我玩一阵子呀?\",\n        \"哎, 我无聊得快受不了, 有没有人来和我玩会儿?\",\n        \"哎呀, 我无聊得发慌, 有没有人能陪我玩会儿解解闷?\",\n        \"嘿呀, 我无聊得很, 有没有人能跟我玩会儿打发时间?\",\n        \"哟, 无聊至极, 有没有人愿意陪我玩一会儿呀?\",\n        \"哎, 我这无聊劲儿上来了, 有没有人跟我玩会儿呀?\",\n        \"哎呀, 无聊得快长草了, 有没有人来和我玩会儿呀?\",\n        \"嗨哟, 我无聊得不知所措, 有没有人陪我玩会儿?\",\n        \"哟呵, 无聊得很呐, 有没有人能跟我玩一会儿呢?\",\n        \"哎, 无聊到爆了, 有没有人来和我玩会儿呀?\",\n        \"哎呀, 无聊得不行了, 有没有人能陪我玩一会儿呀?\"\n    };\n    \n    std::random_device rd;\n    std::mt19937 gen(rd());\n    std::uniform_int_distribution<> dis(0, messages.size() - 1);\n    std::string message = messages[dis(gen)];\n\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Requesting accompany: %s\", message.c_str());\n    auto request = std::make_shared<homi_speech_interface::srv::AssistantSpeechText::Request>();\n    request->msg = message;\n\n    if (!endTask_client->wait_for_service(std::chrono::seconds(1))) {\n        RCLCPP_ERROR(rclcpp::get_logger(\"robdog_control\"), \"Service not available after waiting\");\n        return;\n    }\n    endTask_client->async_send_request(request);\n\n    timer_ = node_->create_wall_timer(\n        std::chrono::seconds(15),\n        [this]() {\n            // homi_speech_interface::msg::Wakeup wake_msg;\n            // wake_msg.ivw_word = \"xiaolixiaoli\";\n            // wake_msg.angle = 0;\n            // wake_msg.extra_info = \"\";\n            // wake_pub_->publish(wake_msg);\n            // RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Wakeup message published after 15 seconds\");\n\n            // 结束室内漫步\n            Json::Value reqValue;\n            reqValue[\"client_type\"] = CLIENT_LAUNCHER;\n            reqValue[\"target_client\"] = CLIENT_NVIDIA;         \n            reqValue[\"action\"] = \"ramble_control\";\n            Json::Value value_ramble;\n            value_ramble[\"action\"] = 1;\n            reqValue[\"params\"] = value_ramble;\n            RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"),\"The message of end_ramble: %s\", reqValue.toStyledString().c_str());\n            SendtoNvOrin(reqValue.toStyledString().c_str(), nConnectIndex_);\n            \n            // 取消定时器以确保只执行一次\n            timer_->cancel();\n        }\n    );\n}\n\n// 处理10分钟定时器到期事件，检查是否需要主动求陪伴\nvoid RobdogCenter::handleInactivityTimeout() {\n    if (!inactivity_mode_)\n    {\n        return;\n    }\n\n    // 检查是否已经超过三个小时没有主动求陪伴\n    auto now = std::chrono::system_clock::now();\n    std::time_t now_time = std::chrono::system_clock::to_time_t(now);\n\n    if (quiet_for_three_hours_ && now_time > quiet_for_three_hours_until_) {\n        quiet_for_three_hours_ = false;\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Quiet for three hours period has ended.\");\n    }\n\n    rclcpp::Duration inactive_diff = node_->now() - last_active_time_;\n    if (inactive_diff >= std::chrono::hours(1) - std::chrono::minutes(1))\n    {\n        asked_in_last_hour_ = false; // 重置 asked_in_last_hour_\n    }\n\n    if (!quiet_for_three_hours_ && !asked_in_last_hour_)\n    {\n        if (inactive_diff >= std::chrono::minutes(10) - std::chrono::seconds(10))\n        {\n            asked_in_last_hour_ = true;\n            last_active_time_ = node_->now();\n            inactivity_timer_->reset();\n            activeDog();\n        } \n    }\n}\n\n// 处理5分钟定时器到期事件，检查是否进入休眠状态\nvoid RobdogCenter::handleSleepTimeout() {\n        if (inactivity_mode_)\n        {\n            return;\n        }\n        rclcpp::Duration sleep_diff = node_->now() - last_active_time_;\n        if (sleep_diff >= std::chrono::minutes(5) - std::chrono::seconds(5))\n        {\n            // 进入休眠状态，趴下\n            sleep_timer_->reset();\n            RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Machine dog entering sleep mode due to inactivity.\");\n            homi_speech_interface::msg::RobdogAction msg;\n            msg.actiontype = \"motorSkill\";\n            msg.actionargument = \"getDown\";\n            publishAction(std::make_shared<homi_speech_interface::msg::RobdogAction>(msg));\n        }    \n}\n\nvoid RobdogCenter::readMappingPoints() {\n    // 检查参数是否已经声明\n    // if (!node_->has_parameter(\"map_points_path\")) {\n    //     std::string map_points_path = node_->getResourcePath(\"config/map_points.json\");\n    //     node_->declare_parameter<std::string>(\"map_points_path\", map_points_path);\n    // } else {\n    //     RCLCPP_WARN(rclcpp::get_logger(\"robdog_control\"), \"参数 'map_points_path' 已经声明，跳过重新声明\");\n    // } \n    // map_points_path = node_->get_parameter(\"map_points_path\").as_string();\n\n    ReadMapCfg::getInstance().loadConfig(map_points_path); // 点位信息\n\n}\n\nvoid RobdogCenter::updateMapPoints(const std::string& event, const Json::Value& points) {\n    // 创建一个新的 root 对象\n    Json::Value root;\n    bool enable_points = true;\n\n    update_map_points_path();\n    // 根据 event 字段确定对应的点位类型\n    std::string point_type;\n    if (event == \"deliverExpress\" || event == \"fetchExpress\") {\n        point_type = \"press_point\";\n    } else if (event == \"takePhotos\") {\n        point_type = \"photo_point\";\n    } else if (event == \"familyMovePoint\") {\n        point_type = \"familyMovePoint\";\n    } else if (event == \"parkPatrol\") {\n        point_type = \"patrol_point\";\n    } else if (event == \"goHome\") {\n        point_type = \"goHome_point\";\n    } else if (event == \"deliverCake\") {\n        point_type = \"deliverCake_point\";\n    } else if (event == \"batteryCharging\") {\n        point_type = \"batteryChargingPoint\";\n    } else if (event == \"chargeNav\") {\n        point_type = \"chargeNavPoint\";\n        enable_points = false;\n    } else {\n        std::cerr << \"Unknown updateMapPoints event type: \" << event << std::endl;\n        return;\n    }\n\n    // 2. load current map(if exist)\n    std::ifstream inFile(map_points_path);\n    if (inFile.is_open()) {\n        try {\n            inFile >> root;\n        } catch (...) {\n            std::cerr << \"Error parsing existing JSON file\" << std::endl;\n            root = Json::objectValue;\n        }\n        inFile.close();\n    } else {\n        // if map not exist, create new\n        root = Json::objectValue;\n    }\n\n    // 3. update point\n    if (!root.isMember(\"mapping_point\")) {\n        root[\"mapping_point\"] = Json::objectValue;\n    }\n    \n    // only update current point type\n\n    if (enable_points == true) {\n        root[\"mapping_point\"][point_type][\"points\"] = points;\n    } else {\n        root[\"mapping_point\"][point_type] = points;\n    }\n\n    // 4. write to file\n    std::ofstream outFile(map_points_path);\n    if (!outFile.is_open()) {\n        std::cerr << \"Failed to open file for writing: \" << map_points_path << std::endl;\n        return;\n    }\n    \n    outFile << root.toStyledString();\n    outFile.close();\n\n    // verify content (optional)\n    std::ifstream verifyFile(map_points_path);\n    if (!verifyFile.is_open()) {\n        RCLCPP_ERROR(rclcpp::get_logger(\"robdog_control\"), \"Failed to open file: %s\", map_points_path.c_str());\n        return;\n    }\n\n    std::string content((std::istreambuf_iterator<char>(verifyFile)), std::istreambuf_iterator<char>());\n    verifyFile.close();\n    std::cout << \"Updated content of map_points.json:\\n\" << content << std::endl;\n}\n\n// 暂停函数\nvoid RobdogCenter::sleepForDuration() {\n    // ros::Duration(seconds).sleep();\n    rclcpp::sleep_for((std::chrono::seconds(2))); \n}\n\n// 发送建图初始化响应\nvoid RobdogCenter::sendMapInitialResponse(int code, const std::string& msg) {\n    Json::Value response;\n    response[\"deviceId\"] = RobotState::getInstance().getDeviceId();\n    response[\"domain\"] = \"APP_DEVICE_INTERACTION\";\n    response[\"event\"] = \"robot_map_draw_init_response\";\n    response[\"eventId\"] = getEventId();\n    response[\"requestId\"] = \"requestId\"+to_string(base::homiUtils::getCurrentTimeStamp());\n    response[\"seq\"] = to_string(base::homiUtils::getCurrentTimeStamp());\n\n    Json::Value body;\n    body[\"code\"] = code;\n    body[\"msg\"] = msg;\n    response[\"body\"] = body;\n\n    Json::FastWriter writer;\n    std::string jsonString = writer.write(response);\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"robot_map_draw_init_response: %s\", jsonString.c_str());\n    sendRequestData(jsonString);\n}\n\n// 发送充电桩标记响应\nvoid RobdogCenter::sendChargeMarkResponse(int code, const std::string& msg) {\n    Json::Value response;\n    response[\"deviceId\"] = RobotState::getInstance().getDeviceId();\n    response[\"domain\"] = \"APP_DEVICE_INTERACTION\";\n    if(isChargeMarking){\n        response[\"event\"] = \"charging_pile_mark_response\";\n    }else if(is_charging){\n        response[\"event\"] = \"charging_action_response\";\n    }\n    response[\"eventId\"] = \"eventId_\" + to_string(base::homiUtils::getCurrentTimeStamp());\n    response[\"requestId\"] = \"requestId\"+to_string(base::homiUtils::getCurrentTimeStamp());\n    response[\"seq\"] = to_string(base::homiUtils::getCurrentTimeStamp());\n\n    Json::Value body;\n    body[\"code\"] = code;\n    body[\"msg\"] = msg;\n    std::string charge_pile_id = RobotState::getInstance().getMapId();\n    \n    body[\"mapId\"] = static_cast<Json::Int64>(std::stoll(charge_pile_id));\n    response[\"body\"] = body;\n\n    Json::FastWriter writer;\n    std::string jsonString = writer.write(response);\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"%s response: %s\", response[\"event\"].asString().c_str(),jsonString.c_str());\n    sendRequestData(jsonString);\n}\n\n//超时处理函数\n// void RobdogCenter::startTimeout(std::function<void()> timeoutCallback, std::atomic<bool>& waiting_flag, std::chrono::seconds timeout_duration) {\n//     waiting_flag = true;\n//     auto start_time = std::chrono::steady_clock::now(); // 记录开始等待的时间\n\n//     // 异步执行超时处理\n//     std::async(std::launch::async, [this, timeoutCallback, start_time, &waiting_flag, timeout_duration]() {\n//         std::this_thread::sleep_for(timeout_duration);\n//         if (waiting_flag) {\n//             waiting_flag = false;\n//             timeoutCallback(); // 执行超时回调函数\n//         }\n//     });\n// }\nvoid RobdogCenter::startTimeout(std::function<void()> timeoutCallback, std::function<void()> changeCallback, std::atomic<bool>& waiting_flag, std::chrono::seconds timeout_duration) {\n    waiting_flag = true;\n    auto start_time = std::chrono::steady_clock::now(); // 记录开始等待的时间\n\n    // 异步执行超时处理\n    std::async(std::launch::async, [this, timeoutCallback, changeCallback, start_time, &waiting_flag, timeout_duration]() {\n        while (std::chrono::steady_clock::now() - start_time < timeout_duration) {\n            if (!waiting_flag) {\n                changeCallback(); // 调用changeCallback函数\n                return; // 如果waiting_flag变为false，退出循环\n            }\n            std::this_thread::sleep_for(std::chrono::milliseconds(100)); // 每100毫秒检查一次\n        }\n        if (waiting_flag) {\n            waiting_flag = false;\n            timeoutCallback(); // 执行超时回调函数\n        }\n    });\n}\n//导航节点关闭超时函数\nvoid RobdogCenter::navTimeoutHandler(const Json::Value &value,const std::string& topic_name) {\n    startTimeout([this]() {\n        int taskStatusCode = ERROR_CODE_MAPINI_TIME_OUT;\n        std::string taskStatusMsg = getDescription(taskStatusCode);\n        sendMapInitialResponse(taskStatusCode,taskStatusMsg); // 超时未关闭按初始化失败处理\n        RobotState::getInstance().setCurrentState(RobotStateEnum::NORMAL); // 超时未重连，设置状态为 NORMAL\n    }, [this, value, topic_name]() {\n        response_start_time = std::chrono::steady_clock::now();\n        handleEvent(value[\"event\"].asString(), value, value[\"body\"], topic_name); \n        mapTimeoutHandler();\n    },Navigation_node, std::chrono::seconds(20));\n}\n//建图初始化超时函数\nvoid RobdogCenter::mapTimeoutHandler() {\n    startTimeout([this]() {\n        int taskStatusCode = ERROR_CODE_MAPINI_TIME_OUT;\n        std::string taskStatusMsg = getDescription(taskStatusCode);\n        sendMapInitialResponse(taskStatusCode,taskStatusMsg); // 超时视为失败\n        RobotState::getInstance().setCurrentState(RobotStateEnum::NORMAL); // 超时未重连，设置状态为 NORMAL\n    }, [this]() {\n        // 处理Navigation_node变为false的情况\n    },waiting_for_response, std::chrono::seconds(20));\n}\n\nvoid RobdogCenter::disconnectTimeoutHandler() {\n    startTimeout([this]() {\n        RobotState::getInstance().setCurrentState(RobotStateEnum::NORMAL); // 超时未重连，设置状态为 NORMAL\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Timeout waiting for reconnect, setting state to NORMAL\");\n        sendCommandToUSLAM(\"mapping/stop\");\n        std::string current_mapid = RobotState::getInstance().getMapId();\n        sendMapAction(2,\"\",current_mapid);\n    },  []() {}, waiting_for_reconnect, std::chrono::seconds(15));\n}\n\n// ******************************************  处理平台的事件 *********************************************************************\n// ****************** 处理平台的消息 **************************\nvoid RobdogCenter::robctrlCallback(const homi_speech_interface::msg::SIGCEvent::SharedPtr msg,const std::string& topic_name) {\n    static int count = 0;\n    \n    try {\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Received msg from topic %s: %s\",topic_name.c_str(), msg->event.c_str());\n        // 解析控制指令\n        std::string strMsg = msg->event; // JSON字段\n        Json::Value value;\n        Json::Reader reader;\n        if (!reader.parse(strMsg, value)) return; // 解析 JSON\n        Json::Value body = value[\"body\"];\n        if (!body[\"points\"].isNull()) {\n            updateMapPoints(body[\"event\"].asString(), body[\"points\"]);\n        }\n        // 除了心跳，更新休眠时间\n        if (value[\"event\"] != \"keep_alive\"){\n            updateLastActiveTime(); \n        }\n        else{\n            count++;\n            RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"count: %d\", count);\n            if(count == 1 || count == 2)\n            {\n                handleBindStatusQuery();\n                handleDeviceSettingQuery();\n            }\n\n        }\n\n        // 定义建图语音不可执行的event\n        const std::unordered_set<std::string> map_events = {\"robot_action\", \"move_points\"};\n        // 定义导航时不可执行的event\n        const std::unordered_set<std::string> nav_events = {\"robot_action\", \"map_draw\"};\n        // 检查必须的字段\n        if (!value[\"deviceId\"].isNull() && !value[\"event\"].isNull()) {\n            RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Event: %s\", value[\"event\"].asString().c_str()); // 打印 event\n            RobotState::getInstance().setDeviceId(value[\"deviceId\"].asString());\n            RobotStateEnum currentState = RobotState::getInstance().getCurrentState();\n            RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"CurrentState: %d\", static_cast<int>(currentState));\n            switch (currentState) {\n                case RobotStateEnum::NORMAL:\n                    if (value[\"event\"] == \"map_draw\" && value[\"body\"][\"action\"] == \"start\") {  \n                        response_start_time = std::chrono::steady_clock::now();\n                        RobotState::getInstance().setCurrentState(RobotStateEnum::MAP_INITIALIZING);                     \n                        setEventId(value[\"eventId\"].asString());\n                        // handleEvent(value[\"event\"].asString(), value, value[\"body\"], topic_name); \n                        // std::thread t(std::bind(&RobdogCenter::mapTimeoutHandler,this));\n                        std::thread t(std::bind(&RobdogCenter::navTimeoutHandler, this, value, topic_name));\n                        t.detach(); \n                        checkNvidiaServiceStatus(false,\"\");//关闭建图导航节点\n                    } else {\n                        handleEvent(value[\"event\"].asString(), value, value[\"body\"], topic_name); \n                    }\n                    break;\n                case RobotStateEnum::MAP_INITIALIZING:\n                    if (value[\"event\"] == \"app_disconnect\") {\n                        disconnectTimeoutHandler();\n                    } else if (value[\"event\"] == \"app_connected\") {\n                        waiting_for_reconnect = false; // 收到连接指令，取消超时处理\n                    } else if (value[\"event\"] == \"map_draw\" && value[\"body\"][\"action\"] == \"start\") {\n                        setEventId(value[\"eventId\"].asString());\n                        mapTimeoutHandler();\n                    } else {\n                        handleEvent(value[\"event\"].asString(), value, value[\"body\"], topic_name); \n                    }\n                    break;\n                case RobotStateEnum::MAPPING:\n                    if (value[\"event\"] == \"app_disconnect\") {\n                        disconnectTimeoutHandler();\n                    } else if (value[\"event\"] == \"app_connected\") {\n                        waiting_for_reconnect = false; // 收到连接指令，取消超时处理\n                    } else if (value[\"event\"] == \"map_draw\" && value[\"body\"][\"action\"] == \"start\") {\n                        setEventId(value[\"eventId\"].asString());\n                        std::this_thread::sleep_for(std::chrono::milliseconds(500));\n                        handleEvent(value[\"event\"].asString(), value, value[\"body\"], topic_name); \n                        std::thread t(std::bind(&RobdogCenter::mapTimeoutHandler,this));\n                        t.detach(); \n                        checkNvidiaServiceStatus(false,\"\");//关闭建图导航节点\n                    } else if (map_events.find(value[\"event\"].asString()) != map_events.end()) {\n                        std::string message = \"我正在建图呢,你希望我现在结束任务,跟你互动吗\";\n                        auto request = std::make_shared<homi_speech_interface::srv::AssistantSpeechText::Request>();\n                        request->msg = message;\n                        if (!endTask_client->wait_for_service(std::chrono::seconds(1))) {\n                            RCLCPP_ERROR(rclcpp::get_logger(\"robdog_control\"), \"Service not available after waiting\");\n                            return;\n                        }\n                        endTask_client->async_send_request(request);\n                    } else {    \n                        handleEvent(value[\"event\"].asString(), value, value[\"body\"], topic_name);\n                    }\n                    break;\n                case RobotStateEnum::NAVIGATION:\n                    if (nav_events.find(value[\"event\"].asString()) != nav_events.end()) {\n                        std::string message = \"我在导航呢,请先结束导航任务\";\n                        sendStringToBrocast(message);\n                        RobotState::getInstance().setFollowMeStatus(\"off\");\n                        RobotState::getInstance().setCurrentState(RobotStateEnum::NORMAL); \n                        Json::Value response;\n                        response[\"deviceId\"] = RobotState::getInstance().getDeviceId();\n                        response[\"domain\"] = \"BUSINESS_REPORT\";\n                        response[\"event\"] = \"data_report\";\n                        response[\"eventId\"] = to_string(std::chrono::duration_cast<std::chrono::seconds>(std::chrono::system_clock::now().time_since_epoch()).count());\n                        response[\"seq\"] = to_string(std::chrono::duration_cast<std::chrono::seconds>(std::chrono::system_clock::now().time_since_epoch()).count());\n                        response[\"body\"][\"type\"] = \"followMe\";\n                        response[\"body\"][\"data\"][\"status\"]=RobotState::getInstance().getFollowMeStatus();\n                        response[\"body\"][\"data\"][\"code\"]= 0;\n                        response[\"body\"][\"data\"][\"isFirstTime\"]=false;\n                        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"RobotState::getInstance().getFollowMeStatus is %s\",RobotState::getInstance().getFollowMeStatus().c_str());\n                        Json::FastWriter writer;\n                        std::string jsonString = writer.write(response);\n                        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"),\"Follow me report msg is : %s\", jsonString.c_str());\n                        sendRequestData(jsonString);\n                    } else {    \n                        handleEvent(value[\"event\"].asString(), value, value[\"body\"], topic_name);\n                    }\n                    break;\n                case RobotStateEnum::FOLLOWME:\n                {\n                    if (value[\"event\"] == \"app_disconnect\") {\n                        disconnectTimeoutHandler();\n                    } else if (value[\"event\"] == \"app_connected\") {\n                        waiting_for_reconnect = false; // 收到连接指令，取消超时处理\n                    } else if (value[\"event\"] == \"map_draw\" && value[\"body\"][\"action\"] == \"start\") {\n                        setEventId(value[\"eventId\"].asString());\n                        RCLCPP_WARN(rclcpp::get_logger(\"robdog_control\"),\"%d----In the middle of a follow-up mission, stop mapping?\",__LINE__); \n                        sendMapInitialResponse(ERROR_CODE_MAP_COMMAND_DURING_FOLLOW, \"设备正在跟随任务中,无法建图\");   \n                        std::string message = \"我正在跟随任务中呢，你是希望我结束跟随吗？\";\n                        sendStringToBrocast(message);\n                    } else if(value[\"event\"].asString()==\"robot_action\")\n                    {\n                        const std::string ACTION_TYPE_FOLLOW_ME = \"followMe\";\n                        const std::string ACTION_TYPE_EMERGENCY_STOP = \"emergencyStop\";\n                        const std::string ARGUMENT_OFF = \"off\";\n                        const std::vector<std::string> MOVEMENT_COMMANDS = {\n                            \"comeClose\", \"goFar\", \"goLeft\", \"goRight\", \"comeHere\",\"goBehind\"\n                        };\n                        const auto& body = value[\"body\"];\n                        const std::string actionType = body[\"actionType\"].asString();\n                        std::string actionArg;\n                        if (body.isMember(\"actionArgument\")) {\n                            actionArg = body[\"actionArgument\"].asString();\n                        } else if (body.isMember(\"actionArguement\")) { \n                            actionArg = body[\"actionArguement\"].asString();\n                        }\n                        if ((actionType == ACTION_TYPE_EMERGENCY_STOP) ||\n                            (actionType == ACTION_TYPE_FOLLOW_ME && \n                            (ARGUMENT_OFF == actionArg || \n                            std::find(MOVEMENT_COMMANDS.begin(), MOVEMENT_COMMANDS.end(), actionArg) != MOVEMENT_COMMANDS.end()))) \n                            handleEvent(value[\"event\"].asString(), value, value[\"body\"], topic_name);                        \n                        else {\n                            RCLCPP_WARN(rclcpp::get_logger(\"robdog_control\"),\"In the middle of a follow-up mission, is it over?\");    \n                            std::string message = \"我正在跟随任务中呢，你是希望我结束跟随吗？\";\n                            sendStringToBrocast(message);\n                        }                        \n                    }\n                    else if ((value[\"event\"].asString()==\"robot_move\") || (value[\"event\"].asString()==\"robot_view\")){\n                        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"),\"The remote control has taken over, and the end of the follow\");\n                        std::string message = \"遥控已经接管，结束自动跟随\";\n                        sendStringToBrocast(message);\n                        actionFollow(0);\n                        RobotState::getInstance().setFollowMeStatus(\"off\");\n                        RobotState::getInstance().setCurrentState(RobotStateEnum::NORMAL); \n                        Json::Value response;\n                        response[\"deviceId\"] = RobotState::getInstance().getDeviceId();\n                        response[\"domain\"] = \"BUSINESS_REPORT\";\n                        response[\"event\"] = \"data_report\";\n                        response[\"eventId\"] = to_string(std::chrono::duration_cast<std::chrono::seconds>(std::chrono::system_clock::now().time_since_epoch()).count());\n                        response[\"seq\"] = to_string(std::chrono::duration_cast<std::chrono::seconds>(std::chrono::system_clock::now().time_since_epoch()).count());\n                        response[\"body\"][\"type\"] = \"followMe\";\n                        response[\"body\"][\"data\"][\"status\"]=RobotState::getInstance().getFollowMeStatus();\n                        response[\"body\"][\"data\"][\"code\"]= 0;\n                        response[\"body\"][\"data\"][\"isFirstTime\"]=false;\n                        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"RobotState::getInstance().getFollowMeStatus is %s\",RobotState::getInstance().getFollowMeStatus().c_str());\n                        Json::FastWriter writer;\n                        std::string jsonString = writer.write(response);\n                        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"),\"Follow me report msg is : %s\", jsonString.c_str());\n                        sendRequestData(jsonString);\n                    }\n                    else if (value[\"event\"].asString()==\"move_points\")\n                    {\n                        RCLCPP_WARN(rclcpp::get_logger(\"robdog_control\"),\"In the middle of a follow-up mission, is it over?\");    \n                        std::string message = \"我正在跟随任务中呢，你是希望我结束跟随吗？\";\n                        sendStringToBrocast(message);\n                    }\n                    else {\n                        handleEvent(value[\"event\"].asString(), value, value[\"body\"], topic_name);\n                    }\n                    break;\n                default:\n                    if (value[\"event\"] == \"map_draw\" && value[\"body\"][\"action\"] == \"start\") {\n                        handleEvent(value[\"event\"].asString(), value, value[\"body\"], topic_name);\n                        mapTimeoutHandler();\n                    } else {\n                        handleEvent(value[\"event\"].asString(), value, value[\"body\"], topic_name); \n                        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"current status cannot be obtained normally,handleEvent is used,CurrentState: %d\", static_cast<int>(currentState));\n                    }\n                    break;\n                }\n            }\n        }else {\n            RCLCPP_INFO_STREAM(rclcpp::get_logger(\"robdog_control\"), \"NO ACTION!\");\n        }\n    } catch (const Json::LogicError &e) {\n        RCLCPP_ERROR(rclcpp::get_logger(\"robdog_control\"), \"Logic error: %s\", e.what());\n    } catch (const Json::RuntimeError &e) {\n        RCLCPP_ERROR(rclcpp::get_logger(\"robdog_control\"), \"Runtime error: %s\", e.what());\n    }\n}\n\n// ****************** 处理不同的event字段 **************************\n\nvoid RobdogCenter::handleEvent(const std::string &eventType, const Json::Value &inValue, const Json::Value &jBody,const std::string& topic_name) {\n    static const std::unordered_map<std::string, std::function<void(const Json::Value&,const Json::Value&,const std::string& )>> eventHandlers = {\n        {\"robot_action\", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleRobotAction(inValue); }},\n        {\"robot_move\", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleRobotMove(inValue, body); }},\n        {\"robot_view\", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleRobotView(inValue, body); }},\n        {\"mode_set\", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleModeSet(inValue); }},\n        {\"robot_speed_level\", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleSpeed_level(inValue); }},\n\n        {\"properties_write\", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handlePropertiesWrite(inValue); }},\n        {\"properties_read\", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handlePropertiesRead(inValue); }},\n        {\"hardware_state_query\", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleHardwareStateRead(inValue); }},\n        {\"connect_info_request\", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleConnectInfoRequest(inValue); }},\n\n        {\"phone_call\", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handlePhoneCall(body); }},\n        {\"user_connect_change\", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleUserConnectChange(body,name); }},\n        {\"map_draw\", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleMapDraw(body); }},\n        {\"data_update\", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleDataUpdate(inValue, body); }},\n        {\"move_points\", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleMovePoints(inValue); }},\n        {\"point_report\", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handlePointReport(body); }},\n        {\"remind_ontime\", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleRemindOnTime(body); }},\n        {\"navigation_notify\", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleNavigationNotify(body); }},\n        {\"unbind_notify\", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleUnbindNotify(body); }},\n        {\"bind_notify\", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handlebindNotify(body); }},        \n        {\"unbind_notify_voice\", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleUnbindNotifyVoice(body); }},        \n        // {\"gest_Rec\", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleGestRec(body); }},\n        {\"inter_mode_set\", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleUserInteraction(body); }},\n        {\"stop_task\", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleFinishTask(body); }},\n        {\"voice_response_nlu_raw\", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleVoiceResponseNluRaw(body); }},\n        {\"items_read\",[this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleFollowMeStatus(inValue); }},\n        {\"navigation_request\", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleNavigationRequest(inValue); }},\n        {\"trip_simple_query\", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleTripSimpleQuery(inValue); }},\n        {\"bind_status_response\", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleBindStatusResponse(body); }},\n        {\"coord_report\", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleCoordReportRequest(body); }},\n        {\"device_settings_read\", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleDeviceSettingResponse(body); }},\n        {\"map_reposition\", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleMapReposition(inValue); }},\n        {\"robot_skill_organize\", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleRobotSkillOrganize(body); }},\n        {\"interaction_skills\", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleInteractionSkills(inValue); }},\n        {\"group_skill_organize_res\", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleInteractionSkills(inValue); }},\n        {\"develop_mode\", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleDevelopMode(body); }},\n        {\"guard_ontime\", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleGuardInstruction(body); }},\n        {\"charging_pile_mark\", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleChargeMark(body); }},\n        {\"guard_done\", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleGuardDone(body); }},\n        {\"map_reposition_manual\",[this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleMapRepositionManual(inValue);}},\n        {\"charging_action\",[this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleChargingAction(body);}},\n        {\"create_trace_action\",[this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleTraceTrip(inValue);}},\n        {\"data_report_ctl\",[this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleDataReportCtl(inValue);}},\n        {\"trace_trip_request\",[this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleTracetripNaviRequest(inValue);}},\n        {\"rtk_account_read\",[this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleRtkAccountRead(body);}},\n        {\"login_info_notify\", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleUserkey(body); }},\n        {\"wifi_list_query\",[this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleWifiListQuery(inValue, body);}},\n        {\"wifi_set\",[this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleWifiSet(inValue, body);}},\n        {\"wifi_set_result_query\",[this](const Json::Value &inValue, const Json::Value &body,const std::string& name) { handleWifiSetResultQuery(inValue, body);}},\n        {\"unknow\", [this](const Json::Value &inValue, const Json::Value &body,const std::string& name) {}}\n\n    };\n\n    auto it = eventHandlers.find(eventType);\n    if (it != eventHandlers.end()) {\n        it->second(inValue, jBody, topic_name); // 调用对应的处理函数\n    } \n    // 有可能是keep_alive\n    //else {\n    //     RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"NO ACTION for event type: %d\", eventType);\n    // }\n}\n\n// ****************** event是robot_action **************************\nvoid RobdogCenter::handleRobotAction(const Json::Value &inValue) {\n    Json::Value jBody = inValue[\"body\"];\n    if (jBody[\"actionType\"].isNull()) {\n        handleGeneralAction(jBody);\n    } else {\n        handleSpecificAction(inValue);\n    }\n}\n\n// ******************** 反馈运动状态 **************************\nbool RobdogCenter::isActionOver(const std::string& actionName) { \n    int robot_basic_state = RobotInfoMgr::getInstance().getRobotBasicState();\n\n    auto key = std::make_tuple(robot_basic_state);\n    auto it = utStateQueryTableString.find(key);\n    if (it != utStateQueryTableString.end()) \n    {\n        HomiUtRobotStatus enLastStatus = RobotInfoMgr::getInstance().getUtRobotStatus();\n        std::string strDetails = std::get<0>(it->second);\n        std::cout << \"Current Robot State: \" << strDetails << std::endl;\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"),  \"##### Current Robot State Value: %d #####\", robot_basic_state);\n    }\n    if(robot_basic_state == UT_ROBDOG_STATUS_WALK_UPRIGHT) return true;\n    else return false;\n}\n\nvoid RobdogCenter::handleRobotStatusGet(const Json::Value &inValue) { \n    // 回调给平台\n    Json::Value response;\n    response[\"deviceId\"] = RobotState::getInstance().getDeviceId();\n    response[\"domain\"] = inValue[\"domain\"];\n    response[\"event\"] = \"group_skill_organize_res\"; // \n    response[\"eventId\"] = inValue[\"eventId\"];\n    response[\"seq\"] = to_string(base::homiUtils::getCurrentTimeStamp());\n    response[\"response\"] = \"false\";\n    \n    Json::Value event_body;\n    // 重新设一个全局变量\n    event_body[\"status\"] = true;\n    // if(robot_basic_state == UT_ROBDOG_STATUS_WALK_UPRIGHT) event_body[\"status\"] = true;\n    // else event_body[\"status\"] = false;\n    response[\"body\"] = event_body;\n    // 将 Json::Value 转换成字符串\n    Json::FastWriter writer;\n    std::string jsonString = writer.write(response);\n\n    sendRequestData(jsonString);\n}\n\n// ******************** 动作编排 **************************\nvoid RobdogCenter::handleRobotSkillOrganize(const Json::Value &jBody) {\n    std::vector<Json::Value> robotActions;\n    // \n    // robotActions = jBody[\"signalCommands\"][0]; \n    // std::string actionarguement = robotAction[\"body\"][\"actionArguement\"].asString();\n    // auto it = action_times.find(actionarguement);\n    // if (it != action_times.end()) {\n    //     int action_time = it->second;\n    //     handleActionOrg(robotAction); // 先执行动作，再等待动作执行完成\n    //     robActionSuspendTimer_ = node_->create_wall_timer(\n    //         std::chrono::seconds(action_time),   \n    //         [this, body_copy]() { handleActionOrg(robotAction); }\n    //     );\n    // }\n\n    // for (const auto &robotAction : jBody[\"signalCommands\"]) {\n    //     std::string actionarguement = robotAction[\"body\"][\"actionArguement\"].asString();\n    //     auto it = action_times.find(actionarguement);\n    //     if (it != action_times.end()) {\n    //         int action_time = it->second;\n    //         handleRobotAction(robotAction);  // 需要考虑动作的执行时间\n    //         // std::cout << \"开始执行动作: \" << actionarguement << \"，预计执行时间: \" << action_time << \" 秒。\" << std::endl;\n    //         RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"need %d seconds\", action_time);\n    //         // std::this_thread::sleep_for(std::chrono::seconds(action_time)); // 模拟任务执行时间\n    //         // robActionSuspendTimer_ = node_->create_wall_timer(\n    //         //     std::chrono::seconds(action_time),   \n    //         //     std::bind(&RobdogCenter::ActionSuspend, this)\n    //         // );\n    //         std::cout << \"动作: \" << actionarguement << \" 执行完成！\" << std::endl;\n    //     } else {\n    //         std::cout << \"找不到动作: \" << actionarguement << std::endl;\n    //     }\n    // }\n\n    std::thread th([this, jBody]() {\n        for (const auto &robotAction : jBody[\"signalCommands\"]) {\n            std::string actionarguement = robotAction[\"body\"][\"actionArguement\"].asString();\n            auto it = action_times.find(actionarguement);\n            if (it != action_times.end()) {\n                int action_time = it->second;\n                handleRobotAction(robotAction);  // 需要考虑动作的执行时间\n                // std::cout << \"开始执行动作: \" << actionarguement << \"，预计执行时间: \" << action_time << \" 秒。\" << std::endl;\n                RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"need %d seconds\", action_time);\n                \n                std::this_thread::sleep_for(std::chrono::seconds(action_time)); // 模拟任务执行时间\n                \n                // robActionSuspendTimer_ = node_->create_wall_timer(\n                //     std::chrono::seconds(action_time),   \n                //     std::bind(&RobdogCenter::ActionSuspend, this)\n                // );\n                // robActionSuspendTimer_ = node_->create_wall_timer(\n                // std::chrono::seconds(action_time),\n                //     [this]() {\n                //         RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"action finish!!!!!!\");\n                //     }\n                // );\n                std::cout << \"动作: \" << actionarguement << \" 执行完成！\" << std::endl;\n            } else {\n                std::cout << \"找不到动作: \" << actionarguement << std::endl;\n            }\n        }\n     });\n     th.detach();  // 分离线程使其在后台执行\n}\n\n// 根据颜色和样式生成命令\nunsigned int RobdogCenter::generateLightCommand(const std::string& light_color, const std::string& light_style) {\n    // 查找颜色和样式在map中的对应值\n    auto colorIt = colorMap.find(light_color);\n    auto styleIt = styleMap.find(light_style);\n\n    // 如果找不到对应的颜色或样式，返回无效命令\n    if (colorIt == colorMap.end() || styleIt == styleMap.end()) {\n        std::cerr << \"Invalid color or style\" << std::endl;\n        return 0;\n    }\n\n    // 生成命令：颜色和样式代码结合\n    return (colorIt->second << 8) | styleIt->second;\n}\n\nvoid RobdogCenter::handleInteractionSkills(const Json::Value &inValue){\n    std::thread th([this, inValue]() {\n        Json::Value jBody = inValue[\"body\"];\n        for (const auto &command : jBody[\"commandSetSeq\"]) { // 可能有很多套动作、表情和灯光\n            // ------------- 表情处理 ------------- \n            if (!command[\"expression\"].isNull()) {\n                Json::Value expression = command[\"expression\"];\n                std::string exp_type = expression[\"type\"].asString();\n                int exp_duration = expression[\"duration\"].asInt();\n                static const std::unordered_map<std::string, std::string> InteractionVideos = {\n                    {\"sad\", \"video/emotion/sad\"},\n                    {\"happy\", \"video/emotion/happy\"},\n                    {\"angry\", \"video/emotion/angry\"},\n                    {\"criticism\", \"video/emotion/criticism\"},\n                    {\"neutrality\", \"video/emotion/neutrality\"},\n                    {\"praise\", \"video/emotion/praise\"},\n                    {\"neutral\", \"video/emotion/neutrality\"},\n                    \n                    {\"surprised\", \"video/emotion/praise\"},\n                    {\"weather\", \"video/emotion/weather\"},   // 天气标签\n                    {\"festival\", \"video/emotion/festival\"}   // 节日标签\n                };\n                auto it = InteractionVideos.find(exp_type);\n                if (it != InteractionVideos.end()) {\n                    ExpressionChange::getInstance().async_callback_work(node_->getResourcePath(it->second),1); // 暂时只播放一次\n                }\n            }\n\n            // ------------- 灯光处理 ------------- \n            // 【暂不处理】\n            if (!command[\"light\"].isNull()) {\n                Json::Value light = command[\"light\"];\n                std::string light_color = light[\"color\"].asString(); // white、orange、pink、yellow、blue\n                std::string light_style = light[\"style\"].asString(); // 常亮 — keepAwake;闪烁 — blinking;呼吸灯 — breathing;流水灯 — running\n                \n                std::string light_brightness = light[\"brightness\"].asString();\n                std::string light_frequency = light[\"frequency\"].asString(); // 目前默认为0\n                std::string light_speed = light[\"speed\"].asString(); // 目前默认为0\n                int light_duration = light[\"duration\"].asInt(); // 目前默认为0\n\n                unsigned int cmd = generateLightCommand(light_color, light_style);\n                // std::cout << \"Generated Command: 0x\" << std::hex << cmd << std::dec << std::endl;\n\n                // 根据命令值来匹配对应的宏\n                switch (cmd) {\n                    case DEEP_CMD_LIGHT_01: std::cout << \"Command: DEEP_CMD_LIGHT_01 (橙色闪烁)\" << std::endl; break;\n                    case DEEP_CMD_LIGHT_02: std::cout << \"Command: DEEP_CMD_LIGHT_02 (粉色闪烁)\" << std::endl; break;\n                    case DEEP_CMD_LIGHT_03: std::cout << \"Command: DEEP_CMD_LIGHT_03 (白色呼吸)\" << std::endl; break;\n                    case DEEP_CMD_LIGHT_04: std::cout << \"Command: DEEP_CMD_LIGHT_04 (白色常亮)\" << std::endl; break;\n                    case DEEP_CMD_LIGHT_05: std::cout << \"Command: DEEP_CMD_LIGHT_05 (橙色呼吸)\" << std::endl; break;\n                    case DEEP_CMD_LIGHT_06: std::cout << \"Command: DEEP_CMD_LIGHT_06 (橙色流水)\" << std::endl; break;\n                    default: std::cout << \"Unknown command!\" << std::endl; break;\n                }\n                if(cmd != 0) node_->handleLightControl(cmd, 8);\n            }\n\n            // ------------- 语音处理 ------------- \n            if (!command[\"tts\"].isNull()) {\n                Json::Value tts = command[\"tts\"];\n                std::string tts_type = tts[\"type\"].asString(); // text和audio\n                std::string tts_value = tts[\"value\"].asString();\n                if(tts_type == \"text\"){\n                    sendStringToBrocast(tts_value);\n                }\n                else if(tts_type == \"audio\"){\n                    std::thread([this, tts_value]() {\n                        node_->playAudio(node_->getResourcePath(\"audio/\" + tts_value));\n                    }).detach();\n                }\n            }\n\n            // ------------- 动作处理 -------------\n            if (!command[\"action\"].isNull()) {\n                Json::Value action = command[\"action\"];\n                std::string actionArgument;\n                // 提取动作参数\n                if (!action[\"actionArguement\"].isNull()) {\n                    actionArgument = action[\"actionArguement\"].asString();\n                } else if (!action[\"actionArgument\"].isNull()) {\n                    actionArgument = action[\"actionArgument\"].asString();\n                }\n                // 查找并发布动作\n                auto it_action = actionMap.find(actionArgument);\n                if (it_action != actionMap.end()) {\n                    node_->handleInteractionAction(actionArgument);\n                    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"执行 %s 动作\", actionArgument);\n                } else {\n                    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"找不到动作: %s\", actionArgument);\n                }\n\n                // 创建 promise/future 对象用于等待动作完成\n                std::promise<bool> actionDonePromise;\n                std::future<bool> actionDoneFuture = actionDonePromise.get_future();\n\n                // 启动定时器检查动作状态\n                robActionSuspendTimer_ = node_->create_wall_timer(\n                    std::chrono::milliseconds(500), // 保证动作已经开始执行\n                    [this, &actionDonePromise, actionArgument]{\n                        if (isActionOver(actionArgument)) {\n                            RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"动作 %s 执行完成！\", actionArgument.c_str());\n                            robActionSuspendTimer_->cancel();  // 停止定时器\n                            actionDonePromise.set_value(true);  // 触发 future 完成\n                        }\n                });\n                // 等待动作完成\n                actionDoneFuture.wait();\n            }\n            \n            // 每一套交互之间的间隔\n            int interaction_time = command[\"nextDelay\"].asInt();\n            std::this_thread::sleep_for(std::chrono::seconds(interaction_time)); // 模拟任务执行时间\n        }\n        // 退出循环说明所有动作已经完成\n        handleRobotStatusGet(inValue);\n     });\n     th.detach();  // 分离线程使其在后台执行\n}\n\nvoid RobdogCenter::handleDevelopMode(const Json::Value &jBody) {\n    if (!jBody.isMember(\"isUse\")) {\n\tRCLCPP_ERROR(rclcpp::get_logger(\"robdog_control\"), \"develop_mode: Invalid format\");\n        return;\n    }\n    bool isUse = jBody[\"isUse\"].asBool();\n    auto message = std_msgs::msg::String();\n    message.data = isUse ? \"1\" : \"0\";\n    developer_mode_pub_->publish(message);\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"pub develop_mode: %s\", message.data.c_str());\n\n    return;\n}\n\nvoid RobdogCenter::handleGuardInstruction(const Json::Value &jBody) {\n\n    //RCLCPP_ERROR(rclcpp::get_logger(\"robdog_control\"), \"task signalling detected:%s\",data_string.c_str());\n    homi_speech_interface::msg::LiveStreamTask msg;\n    msg.task = \"task\";\n    msg.start_second = jBody[\"time\"][\"secondOfDay\"].asInt();\n    msg.end_second = jBody[\"time\"][\"endSecondOfDay\"].asInt();\n    liveStreamTask_pub->publish(msg); \n\n    return;\n}\n\nvoid RobdogCenter::GuardDone() {\n    // aplay: 主人，我又完成了一次守卫任务哦，你可以在移动爱家APP里查看我录制的任务录像呢主人，我又完成了一次守卫任务哦，你可以在移动爱家APP里查看我录制的任务录像呢\n    node_->playAudio(node_->getResourcePath(\"audio/GuardDone.wav\"));\n    int max_wait = 50;\n    int stateChange = 0;\n\n    // standUp\n    if(RobotInfoMgr::getInstance().getRobotStatus() == ROBDOG_STATUS_GETDOWN) {\n        homi_speech_interface::msg::RobdogAction sumsg;\n        sumsg.actiontype = \"motorSkill\";\n        sumsg.actionargument = \"standUp\";\n        publishAction(std::make_shared<homi_speech_interface::msg::RobdogAction>(sumsg));\n    }\n    //wait for standup\n    while(max_wait-->=0) {\n        if(RobotInfoMgr::getInstance().getRobotStatus() == ROBDOG_STATUS_FORCE_CTRL_LEVEL_LOWSPPEED_GAIT) {\n            break;\n        }\n        std::this_thread::sleep_for(std::chrono::milliseconds(100));\n    }\n    if(RobotInfoMgr::getInstance().getRobotStatus() != ROBDOG_STATUS_FORCE_CTRL_LEVEL_LOWSPPEED_GAIT) {\n        RCLCPP_WARN(rclcpp::get_logger(\"robdog_control\"), \"GuardDone RobDog still not standing: %d:%d\", RobotInfoMgr::getInstance().getRobotStatus(),ROBDOG_STATUS_FORCE_CTRL_LEVEL_LOWSPPEED_GAIT);\n        node_->playAudio(node_->getResourcePath(\"audio/GuardDoneRequestForInteraction.wav\"));\n        return;\n    }\n\n    // move 8 steps; \"body\":{\"direction\":{\"x\":1},\"actionType\":2}\n    Json::Value body;\n    body[\"direction\"][\"x\"] = 1;\n    body[\"actionType\"] = 2;\n    fspeed_x = 0.3;\n    RobotMoveProc(body);\n\n    max_wait = 100;\n    while(max_wait-->=0) {\n        if(RobotInfoMgr::getInstance().getRobotStatus() == ROBDOG_STATUS_BODYROTWISTING_BY_AXISCOMMAND) {\n            stateChange = 1;\n        } else {\n            if(stateChange == 1) {\n                //move done\n                RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"GuardDone move done: %d\", RobotInfoMgr::getInstance().getRobotStatus());\n                break;\n            }\n        }\n        std::this_thread::sleep_for(std::chrono::milliseconds(50));\n    }\n    fspeed_x = DF_FSPEED_X;\n    if(RobotInfoMgr::getInstance().getRobotStatus() != ROBDOG_STATUS_FORCE_CTRL_LEVEL_LOWSPPEED_GAIT || stateChange != 1) {\n        RCLCPP_WARN(rclcpp::get_logger(\"robdog_control\"), \"robdog may not move, do not stretch, %d\", RobotInfoMgr::getInstance().getRobotStatus());\n        node_->playAudio(node_->getResourcePath(\"audio/GuardDoneRequestForInteraction.wav\"));\n        return;\n    }\n\n    // action 伸懒腰\n    homi_speech_interface::msg::RobdogAction sumsg;\n    sumsg.actiontype = \"motorSkill\";\n    sumsg.actionargument = \"stretch\";\n    publishAction(std::make_shared<homi_speech_interface::msg::RobdogAction>(sumsg));\n    std::this_thread::sleep_for(std::chrono::milliseconds(8000));\n\n    // aplay 我现在很精神呢，可以跟我互动哦\n    node_->playAudio(node_->getResourcePath(\"audio/GuardDoneRequestForInteraction.wav\"));\n}\n\nvoid RobdogCenter::handleGuardDone(const Json::Value &jBody) {\n    std::thread t(&RobdogCenter::GuardDone, this);\n    t.detach();\n}\n\n// -----------间隔固定的时延开启算法 -----------------\n// void RobdogCenter::handleActionOrg(){\n//     handleRobotAction(robotAction);\n//     // 停止定时器\n//     if (robActionSuspendTimer_) {\n//         robActionSuspendTimer_->cancel();\n//     }\n// }\n\n// ****************** event是robot_action但是没有actiontype **************************\nvoid RobdogCenter::handleGeneralAction(const Json::Value &jBody) {\n    // 处理导航任务和其他通用动作\n    expresstion_count = 0;\n    // RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"8959379483739289398\");\n    //导航任务需要切换到自主模式\n    homi_speech_interface::msg::RobdogAction zzmsg;\n    zzmsg.actiontype = \"NavCtrl\";\n    zzmsg.actionargument = \"AutoMode\";\n    // publishAction(zzmsg);\n    publishAction(std::make_shared<homi_speech_interface::msg::RobdogAction>(zzmsg));\n\n    // system(\"/home/<USER>/updateexpression.sh  \"\n    //         \"/home/<USER>/resource/left_right_look.mp4\");\n    std::string taskType;\n    if (!jBody[\"event\"].isNull()) {\n        taskType = jBody[\"event\"].asString();\n        RobotState::getInstance().setMoveTaskType(taskType);\n    } else {\n        RobotState::getInstance().setMoveTaskType(\"\");\n    }\n    // ******************* 处理不同的事件类型 ****************\n    if (taskType == \"takePhotos\") {\n        // 处理拍照任务\n        handleTakePhotos();\n    } else if (taskType == \"deliverExpress\") {\n        // 取快递\n        handleDeliverExpress();\n    } else if (taskType == \"fetchExpress\") {\n        // 寄快递\n        handleFetchExpress();\n    } else if (taskType == \"cancelMovement\") {\n        // 处理取消移动任务\n        handleCancelMovement();\n    } else {\n        // 处理其他任务\n        homi_speech_interface::msg::SIGCEvent move_msg;\n        Json::StreamWriterBuilder writerBuilder;\n        std::string moveMsgs = Json::writeString(writerBuilder, jBody);\n        move_msg.event = moveMsgs;\n        // moveToTarget(move_msg);\n        moveToTarget(std::make_shared<homi_speech_interface::msg::SIGCEvent>(move_msg));\n\n    }\n}\n\n// ****************** event是robot_action但是有actiontype **************************\nvoid RobdogCenter::handleSpecificAction(const Json::Value &inValue) {\n    Json::Value jBody = inValue[\"body\"];\n    std::string actionType = jBody[\"actionType\"].asString();\n    if (actionType == \"followMe\") {    \n        handleFollowMe(jBody);\n    } else if (actionType == \"gestRec\") {\n        // 手势识别开启\n        handleGestRec(jBody);\n    }  else if (actionType == \"sportMode\") {\n        // 运动模式设置\n        handleSportMode(jBody);\n    } else if (actionType == \"motorSkill\") {\n        // 运动技能设置\n        handleMotorSkill(jBody);\n    } else if (actionType == \"emergencyStop\") {\n        // 急停\n        homi_speech_interface::msg::RobdogAction msg;\n        msg.actiontype = \"emergencyStop\";\n        msg.actionargument = \"emergencyStop\";\n        // publishAction(msg);\n        publishAction(std::make_shared<homi_speech_interface::msg::RobdogAction>(msg));\n\n    } else if (actionType == \"resetZero\") {\n        // 关节回零\n        homi_speech_interface::msg::RobdogAction msg;\n        msg.actiontype = \"resetZero\";\n        msg.actionargument = \"resetZero\";\n        // publishAction(msg);\n        publishAction(std::make_shared<homi_speech_interface::msg::RobdogAction>(msg));\n\n    } else if (actionType == \"gaitControl\") {\n        // 和强化学习有关的\n        handleRLSkill(jBody);\n    } else if (actionType == \"stopCompanyAsk\") {\n        handleAcccompany(jBody);\n    } else if (actionType == \"tripStart\") {\n        homi_speech_interface::msg::LiveStreamTask msg;\n        msg.task = \"start\";\n        msg.start_second = 0;\n        msg.end_second = 0;\n        liveStreamTask_pub->publish(msg); \n        \n        handleTripStart(inValue);\n    } else if (actionType == \"tripPause\") {\n        handleTripPause();\n    }else if(actionType == \"selfIntroduce\"){\n        startSequence();\n    } else if (actionType == \"tripCancel\") {\n        homi_speech_interface::msg::LiveStreamTask msg;\n        msg.task = \"stop\";\n        msg.start_second = 0;\n        msg.end_second = 0;\n        liveStreamTask_pub->publish(msg); \n        \n        \n        handleTripCancel();\n    } else if (actionType == \"phoneCallAction\") {\n        if (jBody[\"actionArgument\"].asString() == \"on\") {\n            auto callReq = std::make_shared<homi_speech_interface::srv::PhoneCall_Request>();\n            callReq->type = \"pickup\";\n            callReq->phone_number = \"\";\n            phone_call_client_->async_send_request(callReq);\n        } else if (jBody[\"actionArgument\"].asString() == \"off\") {\n            auto callReq = std::make_shared<homi_speech_interface::srv::PhoneCall_Request>();\n            callReq->type = \"hangup\";\n            callReq->phone_number = \"\";\n            phone_call_client_->async_send_request(callReq);\n        } else {\n            RCLCPP_WARN(rclcpp::get_logger(\"robdog_control\"), \"phoneCallAction: Invalid actionArgument: %s\", jBody[\"actionArgument\"].asString().c_str());\n        }\n    } else {\n        RCLCPP_WARN(rclcpp::get_logger(\"robdog_control\"), \"Unhandled action type: %s\", actionType.c_str());\n    }\n}\n\n// ****************** event是robot_move或者robot_view **************************\nvoid RobdogCenter::RobotMoveProc(const Json::Value &jBody) {\n\n    // 处理机器人动作的逻辑\n    // 初始化 current_twist_msg_ 和 current_continue_msg_ 为全0\n    // current_twist_msg_ = {0}; // 使用聚合初始化\n    // current_continue_msg_ = {\"strType\", 0, 0, 0, 0, 0, 0}; // 使用聚合初始化\n    // geometry_msgs::msg::Twist current_twist_msg_; // 默认构造\n    current_twist_msg_.linear.x = 0.0; // 根据需要初始化每个字段\n    current_twist_msg_.linear.y = 0.0;\n    current_twist_msg_.linear.z = 0.0;\n    current_twist_msg_.angular.x = 0.0;\n    current_twist_msg_.angular.y = 0.0;\n    current_twist_msg_.angular.z = 0.0;\n\n    // 对于其他消息类型，使用类似的方法\n    current_continue_msg_.event = \"robot_move\";\n    current_continue_msg_.x = 0;\n    current_continue_msg_.y = 0;\n    current_continue_msg_.z = 0;\n    current_continue_msg_.yaw = 0;\n    current_continue_msg_.pitch = 0;\n    current_continue_msg_.roll = 0;\n\n    if (!jBody[\"actionType\"].isNull()) {\n        int actionType = jBody[\"actionType\"].asInt();\n        \n        // 更新移动状态\n        // if (actionType == 1 && !move_status_flag) {\n        //     move_status_flag = true;\n        //     system(\"/home/<USER>/updateexpression2.sh /home/<USER>/resource/vedio/look_right_left_step1.mp4 2 /home/<USER>/resource/vedio/look_right_left_step2.mp4\");\n        // } else if (actionType == 0 && move_status_flag) {\n        //     move_status_flag = false;\n        //     system(\"/home/<USER>/updateexpression.sh /home/<USER>/resource/vedio/default.mp4\");\n        // }\n\n        if (actionType == 2) {\n            // 每次执行一个动作之前先静止一会【只有步进模式下才需要】\n            timer_robMove->cancel();\n            velCmd_pub->publish(current_twist_msg_);\n        }\n\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"fspeed_x: %f, fspeed_y: %f, fspeed_z: %f\", fspeed_x, fspeed_y, fspeed_z);\n\n        int count = 0; // 要走的步数\n\n        // 处理方向信息\n        if(!jBody[\"direction\"].isNull()){\n            Json::Value direction = jBody[\"direction\"];\n            for (const auto& axis : {\"x\", \"y\", \"z\", \"yaw\", \"pitch\", \"roll\"}) {\n                if (!direction[axis].isNull() && direction[axis].asInt() != 0) {\n                    int step = direction[axis].asInt();\n                    if (std::string(axis)== \"x\") {\n                        count = std::abs(step);\n                        current_continue_msg_.x = step;\n                        current_twist_msg_.linear.x = (step > 0) ? fspeed_x : -fspeed_x;\n                        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Continue Move, CMD FROM PLATFORM OR APP. STEP_X: %d, fspeed_x: %f\", step, fspeed_x);\n                    } else if (std::string(axis) == \"y\") {\n                        count = std::abs(step);\n                        current_continue_msg_.y = step;\n                        current_twist_msg_.linear.y = (step > 0) ? fspeed_y : -fspeed_y;\n                        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Continue Move, CMD FROM PLATFORM OR APP. STEP_Y: %d, fspeed_y: %f\", step, fspeed_y);\n                    } else if (std::string(axis) == \"z\") {\n                        current_continue_msg_.z = step;\n                    } else if (std::string(axis) == \"yaw\") {\n                        count = std::ceil(static_cast<double>(step) / 15);\n                        current_continue_msg_.yaw = step;\n                        current_twist_msg_.angular.z = (step > 0) ? fspeed_z : -fspeed_z; // 转一次的角速度\n                    } else if (std::string(axis) == \"pitch\") {\n                        current_continue_msg_.pitch = step;\n                    } else if (std::string(axis) == \"roll\") {\n                        current_continue_msg_.roll = step;\n                    }\n                }\n            }\n        }\n        Proceationtype(actionType, count);\n    }\n}\n\nvoid RobdogCenter::RobotViewProc(const Json::Value &jBody) {\n    // 处理机器人动作的逻辑\n    // 初始化 current_twist_msg_ 和 current_continue_msg_ 为全0\n    // current_twist_msg_ = {0}; // 使用聚合初始化\n    // current_continue_msg_ = {\"strType\", 0, 0, 0, 0, 0, 0}; // 使用聚合初始化\n    // geometry_msgs::msg::Twist current_twist_msg_; // 默认构造\n    current_twist_msg_.linear.x = 0.0; // 根据需要初始化每个字段\n    current_twist_msg_.linear.y = 0.0;\n    current_twist_msg_.linear.z = 0.0;\n    current_twist_msg_.angular.x = 0.0;\n    current_twist_msg_.angular.y = 0.0;\n    current_twist_msg_.angular.z = 0.0;\n\n    // 对于其他消息类型，使用类似的方法\n    current_continue_msg_.event = \"robot_view\";\n    current_continue_msg_.x = 0;\n    current_continue_msg_.y = 0;\n    current_continue_msg_.z = 0;\n    current_continue_msg_.yaw = 0;\n    current_continue_msg_.pitch = 0;\n    current_continue_msg_.roll = 0;\n\n    if (!jBody[\"actionType\"].isNull()) {\n        int actionType = jBody[\"actionType\"].asInt();\n        \n        // 更新移动状态\n        // if (actionType == 1 && !move_status_flag) {\n        //     move_status_flag = true;\n        //     system(\"/home/<USER>/updateexpression2.sh /home/<USER>/resource/vedio/look_right_left_step1.mp4 2 /home/<USER>/resource/vedio/look_right_left_step2.mp4\");\n        // } else if (actionType == 0 && move_status_flag) {\n        //     move_status_flag = false;\n        //     system(\"/home/<USER>/updateexpression.sh /home/<USER>/resource/vedio/default.mp4\");\n        // }\n\n        if (actionType == 2) {\n            // 每次执行一个动作之前先静止一会【只有步进模式下才需要】\n            timer_robMove->cancel();\n            velCmd_pub->publish(current_twist_msg_);\n        }\n\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"fspeed_x: %f, fspeed_y: %f, fspeed_z: %f\", fspeed_x, fspeed_y, fspeed_z);\n\n        int count = 0; // 要走的步数\n\n        // 处理方向信息\n        if(!jBody[\"direction\"].isNull()){\n            Json::Value direction = jBody[\"direction\"];\n            for (const auto& axis : {\"x\", \"y\", \"z\", \"yaw\", \"pitch\", \"roll\"}) {\n                if (!direction[axis].isNull() && direction[axis].asInt() != 0) {\n                    int step = direction[axis].asInt();\n                    if (std::string(axis)== \"x\") {\n                        count = std::abs(step);\n                        current_continue_msg_.x = step;\n                        current_twist_msg_.linear.x = (step > 0) ? fspeed_x : -fspeed_x;\n                        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Continue Move, CMD FROM PLATFORM OR APP. STEP_X: %d, fspeed_x: %f\", step, fspeed_x);\n                    } else if (std::string(axis) == \"y\") {\n                        count = std::abs(step);\n                        current_continue_msg_.y = step;\n                        current_twist_msg_.linear.y = (step > 0) ? fspeed_y : -fspeed_y;\n                        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Continue Move, CMD FROM PLATFORM OR APP. STEP_Y: %d, fspeed_y: %f\", step, fspeed_y);\n                    } else if (std::string(axis) == \"z\") {\n                        current_continue_msg_.z = step;\n                    } else if (std::string(axis) == \"yaw\") {\n                        count = std::ceil(static_cast<double>(step) / 15);\n                        current_continue_msg_.yaw = step;\n                        current_twist_msg_.angular.z = (step > 0) ? fspeed_z : -fspeed_z; // 转一次的角速度\n                    } else if (std::string(axis) == \"pitch\") {\n                        current_continue_msg_.pitch = step;\n                    } else if (std::string(axis) == \"roll\") {\n                        current_continue_msg_.roll = step;\n                    }\n                }\n            }\n        }\n        Proceationtype(actionType, count);\n    }\n}\n\nbool RobdogCenter::timeStampCheck(const Json::Value &inValue) \n{\n    // 超时过滤，待时间同步功能完成后放开\n    return true;\n\n    // 获取event时间戳\n    std::string event_time = inValue[\"seq\"].asString();\n    long long event_ts = std::stoll(event_time);\n\n    // 获取当前时间\n    if (event_time.length() == 13)\n    {\n        auto now = std::chrono::system_clock::now();\n        auto timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()).count();\n\n        // 当前时间戳与event时间戳差距超过2s，不执行\n        if (std::abs(timestamp - event_ts) > 2000)\n        {\n            RCLCPP_ERROR(rclcpp::get_logger(\"robdog_control\"), \"Time not correct: %lld, %lld.\", timestamp, event_ts);\n            return false;\n        }\n    }\n    else if (event_time.length() == 10)\n    {\n        auto now = std::chrono::system_clock::now();\n        auto timestamp = std::chrono::duration_cast<std::chrono::seconds>(now.time_since_epoch()).count();\n\n        // 当前时间戳与event时间戳差距超过2s，不执行\n        if (std::abs(timestamp - event_ts) > 2)\n        {\n            RCLCPP_ERROR(rclcpp::get_logger(\"robdog_control\"), \"Time not correct: %lld, %lld.\", timestamp, event_ts);\n            return false;\n        }\n    }\n\n    RCLCPP_ERROR(rclcpp::get_logger(\"robdog_control\"), \"Time correct: %lld.\", event_ts);\n\n    return true;\n}\n\nvoid RobdogCenter::handleRobotMove(const Json::Value &inValue, const Json::Value &jBody) \n{\n    // 检查时间戳\n    if (true == timeStampCheck(inValue))\n    {\n        RobotMoveProc(jBody);\n    }\n}\n\nvoid RobdogCenter::handleRobotView(const Json::Value &inValue, const Json::Value &jBody) \n{\n    // 检查时间戳\n    if (true == timeStampCheck(inValue))\n    {\n        RobotViewProc(jBody);\n    }\n}\n\n// ****************** event是robot_speed_level **************************\nvoid RobdogCenter::handleSpeed_level(const Json::Value &inValue)\n{\n    Json::Value SpeedLevel = inValue[\"body\"][\"speedLevel\"];\n\tint Speed = SpeedLevel.asInt();\n\tRobotInfoMgr::getInstance().setSpeedLevel(Speed);\n\thomi_speech_interface::msg::RobdogAction speedmsg;\n\t\n    #if defined(UNITREE)\n    #else\n    speedmsg.actiontype = \"sportMode\";\n\tif (0 == Speed)\n\t{\n        speedmsg.actionargument = \"walk\";\n\t}\n\telse if (1 == Speed)\n\t{\n\t\tspeedmsg.actionargument = \"medium_speed\";\n\t}\n\telse if (2 == Speed)\n\t{\n        speedmsg.actionargument = \"run\";\n\t}\n    publishAction(std::make_shared<homi_speech_interface::msg::RobdogAction>(speedmsg));\n    #endif\n\n}\n\n// ****************** event是mode_set **************************\nvoid RobdogCenter::handleModeSet(const Json::Value &inValue) {\n    Json::Value deviceMode = inValue[\"body\"][\"deviceMode\"];\n    // 机器狗模式：0-宅家模式 1-遛狗模式 2-外出模式 3-遥控模式\n    //  【目前只保留宅家模式和外出模式】\n    int modeAfter = deviceMode.asInt();\n    int modeBefore = RobotState::getInstance().getRobdogStatus();\n    RobotState::getInstance().setRobdogStatus(modeAfter);\n    setDeviceMode(modeAfter);\n\thomi_speech_interface::msg::RobdogAction speedmsg;\n    Json::Value body;\n    switch (modeAfter) {\n        case 0:\n            // 默认切换到此步态\n            RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Set mode to Home mode\");\n\t\t\n\t\t\tspeedmsg.actiontype = \"sportMode\";\n            #if defined(UNITREE)\n            speedmsg.actionargument = \"AIClassic\";\n            #else\n            speedmsg.actionargument = \"walk\";\n            #endif\n\n\t\t\tpublishAction(std::make_shared<homi_speech_interface::msg::RobdogAction>(speedmsg));\n\t\t\tRobotInfoMgr::getInstance().setSpeedLevel(0);\n            break;\n        case 1:\n            RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Set mode to Walking mode\");\n            break;\n        case 2:\n            // 切换到AI步态\n            // handleTripStart(body); // 切换到自主出行模式\n            RCLCPP_WARN(rclcpp::get_logger(\"robdog_control\"), \"Outdoor mode not supported in this version\");\n\n\t\t\tspeedmsg.actiontype = \"sportMode\";\n\n            #if defined(UNITREE)\n            speedmsg.actionargument = \"AIClassic\";\n            #else\n            speedmsg.actionargument = \"walk\";\n            #endif\n\n\t\t\tpublishAction(std::make_shared<homi_speech_interface::msg::RobdogAction>(speedmsg));\n\t\t\tRobotInfoMgr::getInstance().setSpeedLevel(0);\n            break;\n        case 3:\n            RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Set mode to Remote control mode\");\n            break;\n        default:\n            RCLCPP_ERROR(rclcpp::get_logger(\"robdog_control\"), \"Undefined mode: %d\", modeAfter);\n            break;\n    }\n    // 回调给平台\n    Json::Value response;\n    response[\"deviceId\"] = RobotState::getInstance().getDeviceId();\n    response[\"domain\"] = inValue[\"domain\"];\n    response[\"event\"] = \"mode_set\";\n    response[\"eventId\"] = inValue[\"eventId\"];\n    response[\"seq\"] = to_string(base::homiUtils::getCurrentTimeStamp());\n    response[\"response\"] = \"false\";\n    \n    Json::Value event_body;\n    event_body[\"deviceId\"] = RobotState::getInstance().getDeviceId();\n    event_body[\"modeAfter\"] = modeAfter;\n    event_body[\"modeBefore\"] = modeBefore;\n    if(modeAfter == modeBefore)\n        event_body[\"status\"] = false;\n    else \n        event_body[\"status\"] = true; // 切换失败返回false\n    response[\"body\"] = event_body;\n    // 将 Json::Value 转换成字符串\n    Json::FastWriter writer;\n    std::string jsonString = writer.write(response);\n\n    sendRequestData(jsonString);\n}\n\n// 给平台的回复\nvoid RobdogCenter::sendRequestData(const std::string &data) {\n    auto request_sigc_data = std::make_shared<homi_speech_interface::srv::SIGCData::Request>();\n    request_sigc_data->data = data;  // 设置请求数据\n    // RCLCPP_INFO_STREAM(rclcpp::get_logger(\"robdog_control\"), \"Send res to Server, res is \" << request_sigc_data->data);\n    auto ret = platform_client->wait_for_service(std::chrono::seconds(1));\n    if (!ret) {\n        RCLCPP_WARN(rclcpp::get_logger(\"robdog_control\"), \"Failed to waitForExistence service assistant\");\n    }\n    auto result = platform_client->async_send_request(request_sigc_data, std::bind(&RobdogCenter::plat_srv_callback, this, std::placeholders::_1));\n}\n\n// ****************** event是properties_write **************************\n// 处理属性写入\nvoid RobdogCenter::handlePropertiesWrite(const Json::Value &inValue) {\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"platform set properties\");\n    setProperties(inValue);\n}\n\n// ****************** event是properties_read **************************\n// 处理属性读取\nvoid RobdogCenter::handlePropertiesRead(const Json::Value &inValue) {\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"platform get properties\");\n    std::thread([this, inValue]() {\n        std::string properties_data = get_robot_properties(inValue);  // 获取属性数据\n        sendRequestData(properties_data);  // 发送数据到平台\n    }).detach();  // 分离线程\n}\n\n\nvoid RobdogCenter::handleHardwareStateRead(const Json::Value &inValue) {\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"platform get hardwareState\");\n    std::thread([this, inValue]() {\n        std::string hardware_state_data = get_hardware_state(inValue);  // 获取属性数据\n        sendRequestData(hardware_state_data);  // 发送数据到平台\n    }).detach();  // 分离线程\n}\n\n// ****************** event是connect_info_request **************************\nvoid RobdogCenter::handleConnectInfoRequest(const Json::Value &inValue) {\n    // 处理连接信息请求的逻辑\n    RCLCPP_INFO_STREAM(rclcpp::get_logger(\"robdog_control\"), \"CMD: connect_info_request\");\n    std::string connect_info_data = get_connect_info_request(inValue);  \n    sendRequestData(connect_info_data);     \n}\n\nvoid RobdogCenter::handlePhoneCall(const Json::Value &jBody) {\n    if (jBody[\"phoneNumber\"].isNull()) {\n        RCLCPP_WARN(rclcpp::get_logger(\"robdog_control\"), \"handlePhoneCall: phoneNumber is null\");\n    } else {\n        auto callReq = std::make_shared<homi_speech_interface::srv::PhoneCall_Request>();\n        callReq->type = \"callout\";\n        callReq->phone_number = jBody[\"phoneNumber\"].asString();\n        phone_call_client_->async_send_request(callReq);\n    }    \n}\n\n// ****************** event是user_connect_change **************************\nvoid RobdogCenter::handleUserConnectChange(const Json::Value &jBody,const std::string& topic_name) {\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"user_connect_change\");\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"set operateTs %ld\", jBody[\"operateTs\"].asInt64());\n    if (topic_name==\"/homi_speech/sigc_event_topic\")\n        RobotState::getInstance().setTimeStamp(jBody[\"operateTs\"].asInt64());\n    if (jBody[\"changeType\"].asInt() == 1) // 1代表建联\n    {\n        RobotState::getInstance().setUserConnectStatus(1); // 1代表有用户连接\n        RobotState::getInstance().setUserPhoneNumber(jBody[\"phone\"].asString());\n    } else if (jBody[\"changeType\"].asInt() == 2) // 2代表取消建联\n    {\n        RobotState::getInstance().setUserConnectStatus(0); // 0代表无用户连接\n        RobotState::getInstance().setUserPhoneNumber(\"13933333333\");\n    } else{\n        RCLCPP_ERROR(rclcpp::get_logger(\"robdog_control\"), \"Undefined changeType: %d\", jBody[\"changeType\"].asInt());\n    }\n    RobotState::getInstance().saveConfig();\n}\n\nvoid RobdogCenter::sendNavigationAction(int action,std::string mapId) {\n    Json::Value reqValue;\n    reqValue[\"client_type\"] = CLIENT_LAUNCHER;\n    reqValue[\"target_client\"] = CLIENT_NVIDIA;\n    reqValue[\"action\"] = \"navigation_control\";\n\n    Json::Value params;\n    params[\"action\"] = action;\n    params[\"mapId\"] = mapId;\n    // std::string current_mapid = RobotState::getInstance().getMapId();\n    // RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"%d---current_mapid: %s\", __LINE__, current_mapid.c_str());\n    // RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"%d---send mapid: %s\", __LINE__, mapId.c_str());\n    reqValue[\"params\"] = params;\n\n    // 添加 action 的值到日志中\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"The message of val_env: %s, action: %d\", reqValue.toStyledString().c_str(), action);\n    SendtoNvOrin(reqValue.toStyledString().c_str(), nConnectIndex_);\n}\n\nvoid RobdogCenter::sendCommandToUSLAM(const std::string& command) {\n    std_msgs::msg::String msg;\n    msg.data = command;\n    client_command_publisher_->publish(msg);\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Published command: %s\", msg.data.c_str());\n}\n\nvoid RobdogCenter::sendMapAction(int action,std::string url,std::string mapId) {\n    std::ostringstream oss;\n    oss << \"{\"\n        << \"\\\"mapId\\\":\\\"\" << mapId << \"\\\",\"\n        << \"\\\"action\\\":\" << action << \",\"\n        << \"\\\"url\\\":\\\"\" << url << \"\\\"\"\n        << \"}\";\n\n    std_msgs::msg::String msg;\n    msg.data = oss.str(); \n    mappingControl_pub->publish(msg);\n    \n    if(action == 3) {\n        string map_points_file = node_->get_parameter(\"map_points_path\").as_string()+\"/\"+mapId+\".json\";\n        if (access(map_points_file.c_str(), F_OK) == 0) {\n            unlink(map_points_file.c_str());\n        }\n    }\n\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"sendMapAction: %s\", msg.data.c_str());\n}\n\n// void RobdogCenter::sendLoaclizationAction(const std::string& action) {\n//     sendCommandToUSLAM(\"localization/\" + action);\n// }\n\n// void RobdogCenter::sendNavigationAction(const std::string& action) {\n//     sendCommandToUSLAM(\"navigation/\" + action);\n// }\n\n// void RobdogCenter::sendPatrolAction(const std::string& action) {\n//     sendCommandToUSLAM(\"patrol/\" + action);\n// }\n\nvoid RobdogCenter::sendChargeAction(int action,std::string mapId,const Json::Value& jBody) {\n    Json::Value reqValue;\n    reqValue[\"client_type\"] = CLIENT_LAUNCHER;\n    reqValue[\"target_client\"] = CLIENT_NVIDIA;\n    reqValue[\"action\"] = \"charge_control\";\n\n    Json::Value modifiedJBody;\n    modifiedJBody[\"action\"] = action;\n    modifiedJBody[\"mapId\"] = mapId;\n    modifiedJBody[\"point\"] = jBody;\n    reqValue[\"params\"] = modifiedJBody;\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"),\"The message of value_charge: %s,action: %d\", reqValue.toStyledString().c_str(),action);\n    WS_Send(reqValue.toStyledString().c_str(), nConnectIndex_);\n}\n\nvoid RobdogCenter::handleChargeMark(const Json::Value &jBody) {\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"%d -----handleChargeMark\", __LINE__);\n    std::string current_mapid =  jBody[\"mapId\"].asString();\n    std::string action = jBody[\"action\"].asString();\n    int actionInt;\n\n    if (action == \"start\") {\n        // actionInt = 0;\n        isChargeMarking = true;\n        action = \"autocharge/start\";\n\n    } else if (action == \"cancel\") {\n        // actionInt = 2;\n        isChargeMarking = false;\n        action = \"autocharge/stop\";\n    } else {\n        RCLCPP_WARN(rclcpp::get_logger(\"robdog_control\"), \"Undefined chargemark action: %s\", action.c_str());\n        return ; \n    }\n\n    // sendChargeAction(actionInt,current_mapid,{});\n    sendCommandToUSLAM(action);\n}\n\nvoid RobdogCenter::handleChargingAction(const Json::Value &jBody) { \n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"%d -----handleChargingAction\", __LINE__);\n    std::string current_mapid =  jBody[\"mapId\"].asString();\n    std::string action = jBody[\"action\"].asString();\n    int actionInt;\n\n    if (action == \"start\") {\n        is_charging = true;\n        handleBatteryChargingPoint();\n    } else if (action == \"cancel\") {\n        actionInt = 4;\n        is_charging = false;\n        action = \"autocharge/stop\";\n        sendCommandToUSLAM(action);\n        // sendChargeAction(actionInt,current_mapid,{});\n    } else {\n        RCLCPP_WARN(rclcpp::get_logger(\"robdog_control\"), \"Undefined charge action: %s\", action.c_str());\n        return ; \n    }\n}\n\n// ****************** event是map_draw **************************\nvoid RobdogCenter::handleMapDraw(const Json::Value &jBody) {\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"%d -----map_draw\", __LINE__);\n\n    long long mapId = jBody[\"mapId\"].asInt64();\n    std::string current_mapid = std::to_string(mapId);\n    std::string url = jBody[\"url\"].asString();\n    std::string action = jBody.get(\"action\", \"\").asString();\n    int actionInt;\n    if(mapId == -1){\n        RobotState::getInstance().setMapId(current_mapid); //清空地图，逻辑待实现\n        update_map_points_path();\n    }\n\n\n    if (action == \"start\") {\n        RobotState::getInstance().setMapId(current_mapid);\n        update_map_points_path();\n        RobotState::getInstance().saveConfig();\n        actionInt = 0;\n    } else if (action == \"cancel\") {\n        actionInt = 2;\n        RobotState::getInstance().setCurrentState(RobotStateEnum::NORMAL); \n        \n    } else if (action == \"delete\") {\n        actionInt = 3;\n        //需要调用接口删除保存地图的文件夹，待实现\n        RobotState::getInstance().setCurrentState(RobotStateEnum::NORMAL); \n    } else if (action == \"complete\") {\n        actionInt = 1;\n        action = \"stop\";\n        // RobotState::getInstance().setCurrentState(RobotStateEnum::NORMAL); \n    } else {\n        return ; // 处理其他情况，当前仅有暂停，直接丢弃不处理\n    }\n\n    if (action == \"cancel\") {\n        sendCommandToUSLAM(\"mapping/stop\");\n    }\n    else{\n        sendCommandToUSLAM(\"mapping/\" + action);\n    }\n   \n    sendMapAction(actionInt,url,current_mapid);\n}\n\n// ****************** event是data_update **************************\n// 函数：解析 JSON 字符串并返回 Json::Value 对象\nJson::Value parseJson(const std::string& jsonString) {\n    Json::CharReaderBuilder readerBuilder;\n    Json::Value data;\n    std::string errs;\n    std::istringstream stream(jsonString);\n    if (!Json::parseFromStream(readerBuilder, stream, &data, &errs)) {\n        std::cerr << \"JSON 解析错误: \" << errs ;\n    }\n    return data;\n}\n\n// ****************** event是rtk_account_read **************************\nvoid RobdogCenter::handleRtkAccountRead(const Json::Value &jBody) {\n    Json::Value response;\n    Json::Value body;\n\n    response[\"deviceId\"] = RobotState::getInstance().getDeviceId();\n    response[\"domain\"] = \"DEVICE_PLATFORM_INTERACTION\";\n    response[\"event\"] = \"rtk_account_report\";\n    response[\"eventId\"] = \"eventId_rtk\" + to_string(base::homiUtils::getCurrentTimeStamp());\n    response[\"seq\"] = to_string(base::homiUtils::getCurrentTimeStamp());\n\n    body[\"rtkAccount\"] = rtkAccount;\n    body[\"rtkPass\"] = rtkPass;\n    body[\"status\"] = Json::Int64(rtkAccountStatus);\n    body[\"expire_day\"] = rtkAccountExipreDay;\n    response[\"body\"] = body;\n\n    Json::FastWriter writer;\n    std::string jsonString = writer.write(response);\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"report: %s\", jsonString.c_str());\n\n    sendRequestData(jsonString);\n    rtkAccountStatusReport = false;\n}\n\nvoid RobdogCenter::handleDataUpdate(const Json::Value &inValue, const Json::Value &jBody) {\n    // 处理数据更新的逻辑\n    if (!jBody[\"changeType\"].isNull()) {\n        int entityType = jBody[\"entityType\"].asInt();\n        std::string entityId = jBody[\"entityId\"].asString();\n        // RobotState::getInstance().setMapId(entityId);\n        // update_map_points_path();\n        // RobotState::getInstance().saveConfig();\n\n        if (entityType == 10001) { // 智能播报【创建和更新提醒】\n            // RobdogSmartRemindCtrl::getInstance().CreatAndUpdate(jBody);\n\n            //RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"entityType == 10001\");\n            std::string data_string = jBody[\"data\"].asString();\n            Json::Value data = parseJson(data_string);\n\n            long id = data[\"id\"].asInt64();\n            RobotState::getInstance().setRemindId(id);\n            std::string deviceId = data[\"deviceId\"].asString();\n            std::string title = data[\"title\"].asString();\n            // int remindType = data[\"remindType\"].asInt();\n            bool enabled = data[\"enabled\"].asBool();\n\n            // std::vector<std::string> weekDays;\n\n            // for (const auto& day : data[\"time\"][\"weekDays\"]) {\n            //     weekDays.push_back(day.asString());\n            // }\n\n            // int repeatType = data[\"time\"][\"repeatType\"].asInt();\n            // int dayOfMonth = data[\"time\"][\"dayOfMonth\"].asInt();\n            // int month = data[\"time\"][\"month\"].asInt();\n            // int year = data[\"time\"][\"year\"].asInt();\n\n            std::vector<std::string> contents;\n            std::string text_single = \"\"; // 提醒文本\n            std::string text = \"\"; // 所有提醒文本\n            for (const auto& content : data[\"contents\"]) {\n                text_single = content[\"text\"].asString();\n                contents.push_back(text_single);\n                text += text_single;\n                text += \"          \";\n            }\n\n            std::string remindLocationUid = data[\"remindLocation\"][\"uid\"].asString();\n            std::string remindLocationName = data[\"remindLocation\"][\"name\"].asString();\n            // int familyMemberId = data[\"familyMember\"][\"familyMemberId\"].asInt();\n            std::string nickname = data[\"familyMember\"][\"nickname\"].asString();\n            bool running = data[\"running\"].asBool();\n\n            if (!enabled && jBody[\"changeType\"].asInt() != 1) { // changeType为3的时候需要打断播报\n                // RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"!enabled && jBody[changeType].asInt() != 1\");\n                timer_brocast->cancel();\n                brocast_send_count_ = 0;\n                // homi_speech_interface::srv::AssistantAbort::Request resMsg;\n                // brocast_abort_client->async_send_request(std::make_shared<homi_speech_interface::srv::AssistantAbort::Request>(resMsg));\n                auto reqMsg = std::make_shared<homi_speech_interface::srv::AssistantAbort::Request>();\n                auto ret = brocast_abort_client->wait_for_service(std::chrono::seconds(1));\n                if(ret==false)\n                {\n                    RCLCPP_WARN(rclcpp::get_logger(\"robdog_control\"),\"Failed to waitForExistence service assistant\");\n                }\n                auto result = brocast_abort_client->async_send_request(reqMsg);   \n                RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"cut the brocast!!\");\n\n                // 此处要取消定点移动\n                handleCancelMovement();\n            }\n\n            if (running) {\n                brocast_text = text;\n                int secondOfDay = 0;\n                int endSecondOfDay = 0;\n                Json::Value time_broc = data[\"time\"];\n                if (!time_broc[\"secondOfDay\"].isNull()){\n                    secondOfDay = time_broc[\"secondOfDay\"].asInt();\n                }\n                if (!time_broc[\"endSecondOfDay\"].isNull()) {\n                    endSecondOfDay = time_broc[\"endSecondOfDay\"].asInt();\n                    if (endSecondOfDay != 0 && endSecondOfDay > secondOfDay) { // 如果是时间段的提醒\n                        brocast_total_count_ = (endSecondOfDay - secondOfDay) / 40;}\n                    else{\n                        brocast_total_count_ = 1;// 说明是单点的提醒 (endSecondOfDay等于0也可能是单点提醒)\n                    }\n                } else {\n                    // endSecondOfDay 字段不存在，默认处理为单点提醒\n                    brocast_total_count_ = 1;\n                }\n                long long mapId = 0;\n                std::string current_mapid = \"\";\n                if (!data[\"remindLocation\"][\"mapId\"].isNull()) {\n                    mapId = data[\"remindLocation\"][\"mapId\"].asInt64();\n                    current_mapid = std::to_string(mapId);\n                }\n                if(data[\"remindLocation\"][\"xCoordinate\"].isNull() && data[\"remindLocation\"][\"yCoordinate\"].isNull() && data[\"remindLocation\"][\"angle\"].isNull()){\n                    // 原地播报\n                    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Start On-site broadcasting!!\");\n                    brocast_text = text;\n                    timer_brocast->cancel();\n                    brocast_send_count_ = 0;\n                    timer_brocast->reset();\n                    SendBrocastCallback();\n                }\n                // 判断mapId是否一致，不一致则不前往也不播报\n                else if(!current_mapid.empty() && current_mapid != RobotState::getInstance().getMapId()){\n                    return;\n                }\n                else{\n                    // 新造一个类似于平台的msg\n                    // homi_speech_interface::msg::SIGCEvent::SharedPtr broadcast_msg;\n                    auto broadcast_msg = std::make_shared<homi_speech_interface::msg::SIGCEvent>();\n                    Json::Value value_brocast;\n                    value_brocast[\"body\"] = jBody;\n                    Json::StreamWriterBuilder writer;\n                    std::string jsonString = Json::writeString(writer, value_brocast);\n                    broadcast_msg->event = jsonString;  \n                    // RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"The message will be broadcasted, %s\", broadcast_msg->event.c_str());             \n                    moveToTargetAndBrocast(broadcast_msg); // 放到定点移动模块去执行      \n                }\n            }\n\n        } else if (entityType == 10006) { // 10006表示家庭建图\n            if (!jBody[\"data\"].isNull()) {\n                std::string data_string = jBody[\"data\"].asString(); \n                Json::Value data = parseJson(data_string);\n                data[\"mapId\"] = entityId; // 加入 entityId (发生变更的数据id,填的是地图id，用于后续有多张地图的时候，更新指定地图的数据)\n                // 平台这边还是用entityId表示mapId\n                Json::Value reqValue;\n                reqValue[\"client_type\"] = CLIENT_LAUNCHER;\n                reqValue[\"target_client\"] = CLIENT_NVIDIA;         \n                reqValue[\"action\"] = \"virtual_wall_control\";\n\n                // ************ 按照导航协议构造virtual_wall_control数据 ************\n                // Json::Value value_wall;\n                // value_wall[\"entityId\"] = entityId;\n                // value_wall[\"virtualWall\"] = data;\n                reqValue[\"params\"] = data;\n                RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"),\"The message of virtual_wall_control: %s\", reqValue.toStyledString().c_str());\n                SendtoNvOrin(reqValue.toStyledString().c_str(), nConnectIndex_);\n\n                // Json::StreamWriterBuilder writerBuilder;\n                // std::string navCtrlMsgs = Json::writeString(writerBuilder, data);\n                // std_msgs::msg::String virtualwall_msg;\n                // virtualwall_msg.data = navCtrlMsgs;\n                // publishVirtualWall->publish(virtualwall_msg);\n                // RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Publish virtualwall_msg: %s\", virtualwall_msg.data.c_str());\n            } else if(jBody[\"changeType\"].asInt() == 2){\n                std::string current_mapid = RobotState::getInstance().getMapId();\n                if(entityId != current_mapid){\n                    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"%d----handleDataUpdate mapId %s is not the same as current mapId %s\", __LINE__,entityId.c_str(), current_mapid.c_str());\n                    RobotState::getInstance().setMapId(entityId);\n                    update_map_points_path();\n                    RobotState::getInstance().saveConfig();\n                    sendMapAction(4,\"\",entityId);\n                }\n            }\n        } else if (entityType == 10010) {\n            std::thread([this]() {\n                node_->playAudio(node_->getResourcePath(\"audio/diyww.wav\"));\n            }).detach();\n            std::string data = jBody[\"data\"].asString();\n            Json::Value jdata = parseJson(data);\n            auto diyReq = std::make_shared<homi_speech_interface::srv::SetDiyWord_Request>();\n            diyReq->wakeup_word = jdata[\"wakeupWord\"].asString();\n            set_diyWakeup_client_->async_send_request(diyReq, [this, inValue](rclcpp::Client<homi_speech_interface::srv::SetDiyWord>::SharedFuture resp_future){\n                auto resp = resp_future.get();\n                std::thread([this, resp]() {\n                    std::string message = \"\";\n                    if (resp->status == 0) message = \"唤醒词修改成功,你可以叫我\" + resp->enable_ww + \",我很喜欢我的新名字.经常叫我,我会越来越灵敏哦~\";\n                    else message = \"唤醒词修改失败,你还是叫我\" + resp->enable_ww + \"吧\";\n                    std::this_thread::sleep_for(std::chrono::seconds(10));\n                    sendStringToBrocast(message);\n                }).detach();\n                \n                Json::Value response;\n                response[\"deviceId\"] = RobotState::getInstance().getDeviceId();\n                response[\"domain\"] = inValue[\"domain\"];\n                response[\"event\"] = inValue[\"event\"];\n                response[\"eventId\"] = inValue[\"eventId\"];\n                response[\"seq\"] = to_string(base::homiUtils::getCurrentTimeStamp());\n                response[\"response\"] = \"false\";\n                Json::Value event_body;\n                event_body[\"code\"] = resp->status;\n                if (event_body[\"code\"] == 0)\n                    event_body[\"msg\"] = \"\";\n                else if (event_body[\"code\"] == 1)\n                    event_body[\"msg\"] = \"设备版本不支持\";\n                else if (event_body[\"code\"] == 2)\n                    event_body[\"msg\"] = \"禁用当前唤醒词失败\";\n                else if (event_body[\"code\"] == 3)\n                    event_body[\"msg\"] = \"删除已有唤醒词失败\";\n                else if (event_body[\"code\"] == 4)\n                    event_body[\"msg\"] = \"唤醒词添加失败\";\n                else if (event_body[\"code\"] == 5)\n                    event_body[\"msg\"] = \"唤醒词启用失败\";\n                else\n                    event_body[\"msg\"] = \"未知错误\";\n                response[\"body\"] = event_body;\n                Json::FastWriter writer;\n                std::string jsonString = writer.write(response);\n\n                this->sendRequestData(jsonString);\n            });\n        } else if (entityType == 11200)\n        {\n            RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Update emergency contacts\");\n            std::string entityId = jBody[\"entityId\"].asString();\n            int changeType = jBody[\"changeType\"].asInt();\n            update_emergency_contacts(changeType,entityId,jBody[\"emergencyContact\"]);\n        }\n        else if(entityType == 10013)\n        {\n            int changeType = jBody[\"changeType\"].asInt();\n            std::string data_string = jBody[\"data\"].asString();\n            Json::Value data = parseJson(data_string);\n            std::string uwbTag = data[\"uwbTag\"].asString();\n            if(changeType == 1 || changeType == 2)\n            {\n                RobotState::getInstance().setUwbTag(uwbTag);\n                RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"UWB tag updated: %s\", uwbTag.c_str());\n            }\n            else if(changeType == 3)\n            {\n                RobotState::getInstance().setUwbTag(\"\");\n                RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"UWB tag deleted: %s\", uwbTag.c_str());\n            }\n        }\n        else if (entityType == 10011) \n        {//页面开关\n            RCLCPP_ERROR(rclcpp::get_logger(\"robdog_control\"), \"pushStream signalling detected\");\n            \n            std::string data_string = jBody[\"data\"].asString();\n            Json::Value data = parseJson(data_string);\n            std::string push_string = data[\"pushStream\"].asString();\n            homi_speech_interface::msg::LiveStreamTask msg;\n            msg.task = push_string;           \n            liveStreamTask_pub->publish(msg);\n        }\n\n    } else {\n        RCLCPP_ERROR(rclcpp::get_logger(\"robdog_control\"), \"Undefined changeType: %d\", jBody[\"changeType\"].asInt());\n    }\n}\n\n// ****************** event是move_points【导航任务都是从这个字段下发】 **************************\nvoid RobdogCenter::handleMovePoints(const Json::Value &inValue) {\n    Json::Value jBody = inValue[\"body\"];\n    // 处理移动点的逻辑\n    //导航任务需要切换到自主模式\n    homi_speech_interface::msg::RobdogAction zzmsg;\n    zzmsg.actiontype = \"NavCtrl\";\n    zzmsg.actionargument = \"AutoMode\";\n    // publishAction(zzmsg);\n    publishAction(std::make_shared<homi_speech_interface::msg::RobdogAction>(zzmsg));\n\n    std::string PathEventId = inValue[\"eventId\"].asString();\n    RobotState::getInstance().setPathEventId(PathEventId);\n    \n    Json::Value additionalParams;\n    uuid_t uuid;\n    uuid_generate_random(uuid);  // 生成随机 UUID\n    char uuid_str[37];\n    uuid_unparse_lower(uuid, uuid_str); \n    additionalParams[\"batchId\"] = uuid_str; // \"1234\"; // 多点导航任务的id【每次平台下发导航任务后都到这个地方来为taskid编号】\n    RobotState::getInstance().setbatchId(uuid_str);\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"),\"The UUID of TASK: %s\", uuid_str);\n\n    HomiRobotStatus enLastStatus = RobotInfoMgr::getInstance().getRobotStatus();\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"query robotdog action status before nav_task: %d\", enLastStatus);\n    if(RobotInfoMgr::getInstance().getRobotStatus() == ROBDOG_STATUS_GETDOWN){\n        sendNavigationReport(4);\n        return;\n    }\n    expresstion_count = 0;\n    // 定点移动\n    // event：throwGarbage：丢垃圾；deliverExpress：寄快递；takePhotos：拍照；welcomeHome：欢迎回家;园区巡逻(v1.8)：parkPatrol\n    // system(\"/home/<USER>/updateexpression.sh  /home/<USER>/resource/left_right_look.mp4\");\n    std::string taskType;\n    if (!jBody[\"event\"].isNull()) {\n        taskType = jBody[\"event\"].asString();\n        RobotState::getInstance().setMoveTaskType(taskType);\n    } else {\n        RobotState::getInstance().setMoveTaskType(\"\");\n    }\n   \n    //五楼演示专用事件，固定点位\n    const std::string EVENT_1 = \"deliverMedication\";\n    const std::string EVENT_2 = \"tripRoom\";\n    // // ******************* 处理不同的事件类型 ****************\n    if (taskType == \"takePhotos\") {\n        // 处理拍照任务\n        RobotState::getInstance().setCurrentState(RobotStateEnum::NAVIGATION); // 设置当前状态为导航中\n        handleTakePhotos();\n    } else if (taskType == \"deliverExpress\") {\n        // 处理快递任务\n        RobotState::getInstance().setCurrentState(RobotStateEnum::NAVIGATION); // 设置当前状态为导航中\n        handleDeliverExpress();\n    } else if (taskType == \"fetchExpress\") {\n        // 寄快递\n        RobotState::getInstance().setCurrentState(RobotStateEnum::NAVIGATION); // 设置当前状态为导航中\n        handleFetchExpress();\n    } else if (taskType == \"parkPatrol\") {\n        // 园区巡逻\n        RobotState::getInstance().setCurrentState(RobotStateEnum::NAVIGATION); // 设置当前状态为导航中\n        handleParkPatrol();\n    } else if (taskType == EVENT_1) {\n        // 园区巡逻\n        handlegoHome();\n    } else if (taskType == EVENT_2) {\n        // 园区巡逻\n        handledeliverCake();\n    } else if (taskType == \"cancelMovement\") {\n        // 处理取消移动任务\n        RobotState::getInstance().setCurrentState(RobotStateEnum::NORMAL); // 取消导航状态\n        handleCancelMovement();\n    } else if (taskType == \"familyMovePoint\") {\n        handleRepositioningTask(jBody, [this]() { handleReportFamilyMovePoint(); });\n    } else if (taskType == \"batteryCharging\") {\n        is_charging = true;\n        handleRepositioningTask(jBody, [this]() { handleBatteryChargingPoint(); });\n    } else {\n            // 处理其他任务\n            RobotState::getInstance().setCurrentState(RobotStateEnum::NAVIGATION); // 设置当前状态为导航中\n            homi_speech_interface::msg::SIGCEvent move_msg;\n            Json::StreamWriterBuilder writerBuilder;\n            std::string moveMsgs = Json::writeString(writerBuilder, jBody);\n            move_msg.event = moveMsgs;\n            // moveToTarget(move_msg);\n            moveToTarget(std::make_shared<homi_speech_interface::msg::SIGCEvent>(move_msg));           \n    }\n}\n\n// 导航前先判断是否已重定位过\nvoid RobdogCenter::handleRepositioningTask(const Json::Value& jBody, const std::function<void()>& onSuccess) {\n    repositioning = true;\n    std::string current_mapid; \n    if (!jBody[\"mapId\"].isNull()) {\n        current_mapid = jBody[\"mapId\"].asString();\n    }\n    else {\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"plat move mapId is null\");\n        current_mapid = RobotState::getInstance().getMapId();\n    }\n    if (repositioningResult.load()) {\n        RobotState::getInstance().setCurrentState(RobotStateEnum::NAVIGATION);\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Already relocalized, performing navigation\");\n        RobotState::getInstance().setCurrentState(RobotStateEnum::NAVIGATION);\n        onSuccess();\n    } else {\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"),\"not relocation, start to do relocation\");\n        // checkNvidiaServiceStatus(true, current_mapid);\n        std::thread([this, onSuccess, jBody]() {\n            std::unique_lock<std::mutex> lock(resultMutex);\n            auto timeout = std::chrono::milliseconds(5000); \n            if (resultCV.wait_for(lock, timeout, [this] { return repositioningResult.load(); })) {\n                if (repositioningResult.load()) {\n                    RobotState::getInstance().setCurrentState(RobotStateEnum::NAVIGATION);\n                    onSuccess();\n                } else {\n                    RCLCPP_WARN(rclcpp::get_logger(\"robdog_control\"), \"Repositioning failed!\");\n                    std::thread t_audio(&RobdogCtrlNode::playAudio,node_,node_->getResourcePath(\"audio/nav/auto_repositon_failed.wav\"));\n                    t_audio.detach();\n                    sendNavigationReport(4);\n                    return;\n                }\n            } else {\n                RCLCPP_WARN(rclcpp::get_logger(\"robdog_control\"), \"Timeout waiting for repositioning result!\");\n                std::thread t_audio(&RobdogCtrlNode::playAudio,node_,node_->getResourcePath(\"audio/nav/auto_repositon_failed.wav\"));\n                t_audio.detach();\n                sendNavigationReport(4);\n                return;\n            }\n        }).detach();\n    }\n}\n\n// ****************** event是point_report **************************\nvoid RobdogCenter::handlePointReport(const Json::Value &jBody) {\n    int type = 0;\n    int interval = 0;\n    if(!jBody[\"type\"].isNull() && !jBody[\"interval\"].isNull()){\n        type = jBody[\"type\"].asInt();\n        interval = jBody[\"interval\"].asInt();\n    }\n    else{\n        return;\n    }\n    if (type == 1) {\n        // 处理点报告的逻辑\n        RCLCPP_INFO_STREAM(rclcpp::get_logger(\"robdog_control\"), \"cmd: point_report\");\n        //robPoseStatusTimer_->cancel();\n        auto new_duration = std::chrono::milliseconds(interval);\n        robPoseStatusTimer_ = node_->create_wall_timer(new_duration, \n            std::bind(&RobdogCenter::timerRobotPoseCallback, this));\n    } else if (type == 2) { // 取消点位上报\n        if (robPoseStatusTimer_ && !robPoseStatusTimer_->is_canceled()) {\n            robPoseStatusTimer_->cancel();\n            sendCommandToUSLAM(\"localization/stop\");\n        } // 停止点位上报\n    }\n}\n\n// ****************** event是remind_ontime **************************\nvoid RobdogCenter::handleRemindOnTime(const Json::Value &jBody) {\n\n    // RobdogSmartRemindCtrl::getInstance().RemindOnTime(jBody);\n\n    // 处理定时提醒的逻辑（播报立即执行）\n    long id = jBody[\"id\"].asInt64();     // 数据 ID // 存起来\n    RobotState::getInstance().setRemindId(id);\n    std::string deviceId = jBody[\"deviceId\"].asString(); // 设备 ID\n    std::string title = jBody[\"title\"].asString();       // 标题\n    // int remindType = jBody[\"remindType\"].asInt(); // 1-吃药提醒 2-日程提醒\n    bool enabled = jBody[\"enabled\"].asBool(); // 启用状态\n\n    // std::vector<std::string> weekDays;\n    // for (const auto &day : jBody[\"time\"][\"weekDays\"]) {\n    //     weekDays.push_back(day.asString()); // 取值集合\"Mon\",\"Tue\",\"Wed\",\"Thu\",\"Fri\",\"Sat\",\"Sun\"\n    // }\n\n    // int repeatType = jBody[\"time\"][\"repeatType\"].asInt(); // 重复类型：1:每周; 2:单次\n    // int dayOfMonth = jBody[\"time\"][\"dayOfMonth\"].asInt();    // 一个月中的第几天\n    // int month = jBody[\"time\"][\"month\"].asInt(); // 月份\n    // int year = jBody[\"time\"][\"year\"].asInt();   // 年份\n\n    // 播报的点位信息：\n    std::string uid = jBody[\"remindLocation\"][\"uid\"].asString(); // 点位唯一id\n    std::string name = jBody[\"remindLocation\"][\"name\"].asString(); // 位置名称\n\n    std::vector<std::string> contents;\n    std::string text_single = \"\"; // 提醒文本\n    std::string text = \"\"; // 所有提醒文本\n    for (const auto &content : jBody[\"contents\"]) {\n        std::string contentType = content[\"contentType\"].asString(); // 1-播报内容  2-天气预报\n        text_single = content[\"text\"].asString();     // 提醒文本\n        // int location = content[\"location\"].asInt(); // 所在城市\n        std::string locationStr = content[\"locationStr\"].asString(); // 所在城市，天气提醒必填(冗余)\n\n        // 可以根据需要对内容进行处理或存储\n        contents.push_back(text_single); // 示例：将文本内容存入 vector\n        // 要把文本拼成一个长文本播放\n        // text += \"         \"; \n        text += text_single;\n        text += \"          \";\n    }\n\n    std::string remindLocationUid = jBody[\"remindLocation\"][\"uid\"].asString(); // 提醒位置 uid\n    std::string remindLocationName = jBody[\"remindLocation\"][\"name\"].asString(); // 提醒位置名称\n\n    // int familyMemberId = jBody[\"familyMember\"][\"familyMemberId\"].asInt(); // 关联家庭成员 id\n    std::string nickname = jBody[\"familyMember\"][\"nickname\"].asString(); // 家庭成员昵称\n\n    if (!enabled) {\n        // RCLCPP_INFO_STREAM(rclcpp::get_logger(\"robdog_control\"), \"No broadcasting!\");\n        // RobotBroadcastStatusToPlat(0);\n        timer_brocast->cancel();\n        brocast_send_count_ = 0;\n        // RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"APP Stop Broadcast!\");\n        \n        // homi_speech_interface::srv::AssistantAbort::Request resMsg;\n        // brocast_abort_client->async_send_request(std::make_shared<homi_speech_interface::srv::AssistantAbort::Request>(resMsg));\n        auto reqMsg= std::make_shared<homi_speech_interface::srv::AssistantAbort::Request>();\n        auto ret = brocast_abort_client->wait_for_service(std::chrono::seconds(1));\n        if(ret==false)\n        {\n            RCLCPP_WARN(rclcpp::get_logger(\"robdog_control\"),\"Failed to waitForExistence service assistant\");\n        }\n        auto result = brocast_abort_client->async_send_request(reqMsg);   \n        \n        // 取消定点移动\n        handleCancelMovement();\n        \n    } else {\n        brocast_text = text;\n        int secondOfDay = 0;\n        int endSecondOfDay = 0;\n        if (!jBody[\"time\"][\"secondOfDay\"].isNull()){\n            secondOfDay = jBody[\"time\"][\"secondOfDay\"].asInt();\n        }\n        if (!jBody[\"time\"][\"endSecondOfDay\"].isNull()) {\n            endSecondOfDay = jBody[\"time\"][\"endSecondOfDay\"].asInt();\n            if (endSecondOfDay != 0 && endSecondOfDay > secondOfDay) { // 如果是时间段的提醒\n                brocast_total_count_ = (endSecondOfDay - secondOfDay) / 40;}\n            else{\n                brocast_total_count_ = 1;// 说明是单点的提醒 (endSecondOfDay等于0也可能是单点提醒)\n            }\n        } else {\n            // endSecondOfDay 字段不存在，默认处理为单点提醒\n            brocast_total_count_ = 1;\n        }\n        long long mapId = 0;\n        std::string current_mapid = \"\";\n        if (!jBody[\"remindLocation\"][\"mapId\"].isNull()) {\n            mapId = jBody[\"remindLocation\"][\"mapId\"].asInt64();\n            current_mapid = std::to_string(mapId);\n        }\n        if((jBody[\"remindLocation\"][\"xCoordinate\"].isNull() && jBody[\"remindLocation\"][\"yCoordinate\"].isNull() && jBody[\"remindLocation\"][\"angle\"].isNull()) || RobotState::getInstance().getCurrentState() == RobotStateEnum::NAVIGATION ){\n            // 原地播报\n            RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Start On-site broadcasting!!\");\n            brocast_text = text;\n            timer_brocast->cancel();\n            brocast_send_count_ = 0;\n            timer_brocast->reset();\n            SendBrocastCallback();\n        }\n        // else if (jBody[\"remindLocation\"][\"mapId\"].isNull()) { // 有点位但是没有mapid也会不播报\n        //     // 原地播报\n        //     RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Start On-site broadcasting!!\");\n        //     brocast_text = text;\n        //     timer_brocast->cancel();\n        //     brocast_send_count_ = 0;\n        //     timer_brocast->reset();\n        //     SendBrocastCallback();\n        // }\n        // 判断mapId是否一致，不一致则不前往也不播报\n        else if(!current_mapid.empty() && current_mapid != RobotState::getInstance().getMapId()){\n            return;\n        }\n        else{ // 默认同时有x、y、angle\n            // 新造一个类似于平台的msg\n            // homi_speech_interface::msg::SIGCEvent::SharedPtr broadcast_msg;\n            auto broadcast_msg = std::make_shared<homi_speech_interface::msg::SIGCEvent>();\n            Json::Value value_brocast;\n            value_brocast[\"body\"] = jBody;\n            Json::StreamWriterBuilder writer;\n            std::string jsonString = Json::writeString(writer, value_brocast);\n            broadcast_msg->event = jsonString;  \n            // RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"The message will be broadcasted, %s\", broadcast_msg->event.c_str());             \n            moveToTargetAndBrocast(broadcast_msg); // 放到定点移动模块去执行      \n        }\n    }\n}\n\n// ****************** event是unbind_notify **************************\nvoid RobdogCenter::handleUnbindNotify(const Json::Value &jBody) {\n    // 处理机器人平台发来的解绑请求\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"unbind_notify\");\n    \n    homi_speech_interface::srv::SIGCData::Request req;\n    \n    // 将发送的数据设置为固定的字符串 \"unbind_notify\"\n    req.data = \"unbind_notify\";\n    \n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Send req to andlinkscript, req is %s\", req.data.c_str());\n\n    // 创建客户端并同步调用\n    auto future = app_client->async_send_request(std::make_shared<homi_speech_interface::srv::SIGCData::Request>(req));\n    \n    // 等待并获取响应\n    if (future.wait_for(std::chrono::seconds(1)) == std::future_status::ready) {\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Response from APPserver\");\n    } else {\n        RCLCPP_ERROR(rclcpp::get_logger(\"robdog_control\"), \"Failed to call APPservice Service_demo\");\n    }\n}\n\n// ****************** event是bind_notify **************************\nvoid RobdogCenter::handlebindNotify(const Json::Value &jBody) {\n    // 处理机器人平台发来的绑定成功状态\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"bind_notify\");\n    //解析event事件和设备信息查询\n    Json::FastWriter fastWriter;\n    std::string jsonStr = fastWriter.write(jBody);\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"handlebindNotify jBody:%s \", jsonStr.c_str());\n\n    // 恢复默认唤醒词\n    auto diyReq = std::make_shared<homi_speech_interface::srv::SetDiyWord_Request>();\n    diyReq->wakeup_word = \"灵犀灵犀\";\n    set_diyWakeup_client_->async_send_request(diyReq);\n\n    // 设备绑定成功，异步播放提示音频\n    // std::string audio_path = node_->getResourcePath(\"audio/device_bind_success.wav\");\n    // RCLCPP_INFO(rclcpp::get_logger(\"RobdogCenter\"), \"播放绑定成功提示音: %s\", audio_path.c_str());\n    // std::thread t_audio(&RobdogCtrlNode::playAudio, node_, audio_path);\n    // t_audio.detach();  // 使线程在后台运行\n\n    // 更新绑定状态\n    setBindStatus(1);\n    \n    // 查询设备设置\n    handleDeviceSettingQuery();\n}\n\n// ****************** event是login_info_notify **************************\nvoid RobdogCenter::handleUserkey(const Json::Value &jBody) {\n    // 处理机器人平台发来的Userkey\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"login_info_notify\");\n\n    std::string userkey = jBody[\"userKey\"].asString();\n    \n    // 将发送的数据设置为userkey\n    auto msg = std_msgs::msg::String();\n    msg.data = userkey;  \n    andlink_userkey_pub_->publish(msg);\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Sending userkey to 'andlink_userkey' topic: %s\", msg.data.c_str());\n}\n\n// ****************** wifi扫描是否正在进行 **************************\nint RobdogCenter::is_scan_running() {\n    const char* lock_path = \"/etc/cmcc_robot/wifi_scan.lock\";\n    \n    // 1. 检查锁文件是否存在（保留原逻辑）\n    if (access(lock_path, F_OK) == -1) {\n        if (errno == ENOENT) {\n            RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"锁文件不存在，无扫描进行\");\n            return 2; // 文件不存在，返回2（原逻辑）\n        } else {\n            RCLCPP_ERROR(rclcpp::get_logger(\"robdog_control\"), \"访问锁文件失败: %s (错误: %s)\", \n                         lock_path, strerror(errno));\n            return -1; // 其他错误，返回-1\n        }\n    }\n    \n    // 2. 用`open()`打开锁文件（替代`std::ofstream`）\n    //  flags说明：\n    //  - O_WRONLY：只写模式（对应原`ios::out`）\n    //  - O_CREAT：若文件不存在则创建（对应原`ios::out`的隐含行为）\n    //  - O_APPEND：追加模式（对应原`ios::app`）\n    //  权限说明：0666 → 文件权限为`rw-rw-rw-`（与原`std::ofstream`默认权限一致）\n    int fd = open(lock_path, O_WRONLY | O_CREAT | O_APPEND, 0666);\n    if (fd == -1) {\n        RCLCPP_ERROR(rclcpp::get_logger(\"robdog_control\"), \"无法打开锁文件: %s (错误: %s)\", \n                     lock_path, strerror(errno));\n        return -1; // 打开失败，返回-1\n    }\n    \n    // 3. 尝试获取**非阻塞排他锁**（保留原逻辑）\n    //  - LOCK_EX：排他锁（不允许其他进程加锁）\n    //  - LOCK_NB：非阻塞（无法加锁时立即返回，不阻塞进程）\n    int ret = flock(fd, LOCK_EX | LOCK_NB);\n    if (ret == 0) {\n        // 3.1 加锁成功 → 无进程持有锁，扫描未运行\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"成功获取锁，无扫描进行\");\n        flock(fd, LOCK_UN); // 释放锁（保留原逻辑）\n        close(fd);          // 手动关闭文件描述符（避免泄漏）\n        return 0;           // 返回0（无扫描）\n    } else {\n        // 3.2 加锁失败 → 处理错误情况\n        if (errno == EWOULDBLOCK || errno == EAGAIN) {\n            // 3.2.1 锁已被占用 → 扫描正在运行（原逻辑）\n            RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"锁已被占用，扫描进行中\");\n            close(fd);          // 关闭文件描述符\n            return 1;           // 返回1（正在扫描）\n        } else {\n            // 3.2.2 其他错误（如权限不足）\n            RCLCPP_ERROR(rclcpp::get_logger(\"robdog_control\"), \"获取文件锁失败: %s (错误: %s)\", \n                         lock_path, strerror(errno));\n            close(fd);          // 关闭文件描述符\n            return -1;          // 返回-1（错误）\n        }\n    }\n}\n\n// ****************** event是wifi_list_query **************************\nvoid RobdogCenter::handleWifiList(const Json::Value &jBody) {\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"处理WiFi列表查询请求\");\n    const std::string json_path = \"/etc/cmcc_robot/wifi_scan_result.json\";\n    \n    // 获取当前时间戳（秒级）\n    auto now = std::chrono::system_clock::now();\n    long long current_ts = std::chrono::duration_cast<std::chrono::seconds>(now.time_since_epoch()).count();\n    std::string seq_str = std::to_string(current_ts); \n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"生成时间戳: %s\", seq_str.c_str());\n    \n    Json::Value response;\n    int scan_status = is_scan_running();\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"扫描状态: %d\", scan_status);\n    \n    if (scan_status == 1) {\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"扫描正在进行中，返回扫描中响应\");\n        \n        response[\"deviceId\"] = jBody[\"deviceId\"].asString();\n        response[\"domain\"] = \"DEVICE_PROPERTIES\";\n        response[\"event\"] = \"wifi_list_query\";\n        response[\"eventId\"] = jBody[\"eventId\"].asString();\n        response[\"seq\"] = seq_str;\n        \n        Json::Value body;\n        body[\"queryStatus\"] = 0; // 查询中\n        body[\"wifiList\"] = Json::arrayValue; // 空数组\n        response[\"body\"] = body;\n        \n        Json::FastWriter writer;\n        std::string jsonString = writer.write(response);\n        \n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"响应内容: %s\", jsonString.c_str());\n        sendRequestData(jsonString);\n    } \n    else {       \n        // 尝试读取Python扫描结果\n        try {\n            RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"尝试读取现有扫描结果: %s\", json_path.c_str());\n            \n            // 1. 读取JSON文件内容\n            std::ifstream result_file(json_path);\n            if (!result_file.is_open()) {\n                throw std::runtime_error(\"无法打开结果文件: \" + json_path);\n            }\n            \n            // 读取完整文件内容\n            std::stringstream buffer;\n            buffer << result_file.rdbuf();\n            std::string json_content = buffer.str();\n            result_file.close();\n            RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"文件内容: %s\", json_content.c_str());\n            \n            // 2. 解析JSON\n            RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"解析JSON...\");\n            Json::Value result_json = parseJson(json_content);\n            \n            // 3. 验证时间戳\n            if (!result_json.isMember(\"seq\") || !result_json[\"seq\"].isString()) {\n                throw std::runtime_error(\"JSON缺失有效的seq字段\");\n            }\n            \n            // 4. 检查时间戳有效期\n            long long result_ts = std::stoll(result_json[\"seq\"].asString());\n            long long time_diff = current_ts - result_ts;\n            RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"结果时间戳: %lld, 差值: %lld秒\", result_ts, time_diff);\n            \n            if (time_diff > 60) {\n                throw std::runtime_error(\"结果已过期\");\n            }\n            \n            // 5. 更新结果的时间戳\n            result_json[\"seq\"] = seq_str;\n            RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"更新结果时间戳为当前时间\");\n            \n            // 6. 发送更新后的结果\n            Json::FastWriter writer;\n            std::string jsonString = writer.write(result_json);\n            \n            RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"发送有效扫描结果\");\n            RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"结果内容: %s\", jsonString.c_str());\n            sendRequestData(jsonString);\n            \n            return;\n        } \n        catch (const std::exception& e) {\n            RCLCPP_WARN(rclcpp::get_logger(\"robdog_control\"), \"无法使用现有扫描结果: %s\", e.what());\n        }\n        \n        // 7. 启动新的Python扫描\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"启动新的WiFi扫描...\");\n        \n        std::string cmd = \"python3 /usr/bin/cmcc_robot/install/ble/lib/python3.8/site-packages/ble/wifi_scan_script.py scan &\";\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"执行命令: %s\", cmd.c_str());\n        \n        int ret = system(cmd.c_str());\n        if (ret != 0) {\n            RCLCPP_ERROR(rclcpp::get_logger(\"robdog_control\"), \"启动扫描脚本失败，返回值: %d\", ret);\n        } else {\n            RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"扫描脚本启动成功\");\n        }\n        \n        // 返回\"扫描中\"响应\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"返回扫描中响应\");\n        \n        response[\"deviceId\"] = jBody[\"deviceId\"].asString();\n        response[\"domain\"] = \"DEVICE_PROPERTIES\";\n        response[\"event\"] = \"wifi_list_query\";\n        response[\"eventId\"] = jBody[\"eventId\"].asString();\n        response[\"seq\"] = seq_str;\n        \n        Json::Value body;\n        body[\"queryStatus\"] = 0; // 查询中\n        body[\"wifiList\"] = Json::arrayValue; // 空数组\n        response[\"body\"] = body;\n        \n        Json::FastWriter writer;\n        std::string jsonString = writer.write(response);\n        \n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"响应内容: %s\", jsonString.c_str());\n        sendRequestData(jsonString);\n    }\n}\nbool RobdogCenter::is_hex_digit(char c) {\n    return (c >= '0' && c <= '9') || \n           (c >= 'a' && c <= 'f') || \n           (c >= 'A' && c <= 'F');\n}\n\n// 增强的SSID解码函数（处理转义序列）\nstd::string RobdogCenter::decode_ssid(const std::string& input) {\n    std::string result;\n    size_t i = 0;\n    size_t len = input.size();\n\n    while (i < len) {\n        // 处理\\xXX转义（如中文）\n        if (i + 3 < len && input[i] == '\\\\' && input[i+1] == 'x' &&\n            is_hex_digit(input[i+2]) && is_hex_digit(input[i+3])) {\n            \n            int hex_value;\n            std::stringstream ss;\n            ss << std::hex << input.substr(i+2, 2);\n            ss >> hex_value;\n            \n            result += static_cast<char>(hex_value);\n            i += 4;\n        }\n        // 处理其他转义（如\\t、\\n）\n        else if (i + 1 < len && input[i] == '\\\\') {\n            switch (input[i+1]) {\n                case '\\\\': result += '\\\\'; break;\n                case '\"':  result += '\"';  break;\n                case '\\'': result += '\\''; break;\n                case 't':  result += '\\t'; break;\n                case 'n':  result += '\\n'; break;\n                case 'r':  result += '\\r'; break;\n                default:   result += '\\\\' + input[i+1]; break; // 未知转义保留原样\n            }\n            i += 2;\n        }\n        // 普通字符\n        else {\n            result += input[i];\n            i++;\n        }\n    }\n    return result;\n}\n\n// 提取SSID行（移除空白字符）\nstd::string RobdogCenter::extract_ssid_line(const std::string& line) {\n    size_t pos = line.find(\"SSID: \");\n    if (pos == std::string::npos) return \"\";\n    \n    std::string ssid_line = line.substr(pos + 6); // \"SSID: \" 长度为6\n    size_t endpos = ssid_line.find_last_not_of(\" \\r\\n\\t\"); // 移除行尾空白\n    return (endpos != std::string::npos) ? ssid_line.substr(0, endpos + 1) : \"\";\n}\n\n// 检查WiFi是否连接到目标SSID\nbool RobdogCenter::isWlan0ConnectedToSSID(const std::string& ssid) {\n    std::string cmd = \"iw dev \" + g_ifname + \" link\";\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Executing command: %s\", cmd.c_str());\n    \n    // 执行命令并读取输出\n    std::array<char, 512> buffer;\n    std::unique_ptr<FILE, decltype(&pclose)> pipe(popen(cmd.c_str(), \"r\"), pclose);\n    if (!pipe) {\n        RCLCPP_ERROR(rclcpp::get_logger(\"robdog_control\"), \"Failed to execute iw command\");\n        return false;\n    }\n\n    bool is_connected = false;\n    bool found_not_connected = false;\n    std::string raw_ssid;\n\n    while (fgets(buffer.data(), buffer.size(), pipe.get()) != nullptr) {\n        std::string line(buffer.data());\n        \n        // 检查是否未连接\n        if (line.find(\"Not connected\") != std::string::npos) {\n            found_not_connected = true;\n        }\n        \n        // 提取SSID\n        if (line.find(\"SSID: \") != std::string::npos) {\n            RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Found SSID field in command output\");\n            is_connected = true;\n            \n            raw_ssid = extract_ssid_line(line);\n            if (raw_ssid.empty()) {\n                RCLCPP_WARN(rclcpp::get_logger(\"robdog_control\"), \"SSID field found but could not extract value\");\n                continue;\n            }\n            \n            // 解码SSID（处理转义）\n            std::string actual_ssid = decode_ssid(raw_ssid);\n            RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Raw SSID: '%s', Decoded SSID: '%s', Target SSID: '%s'\",\n                        raw_ssid.c_str(), actual_ssid.c_str(), ssid.c_str());\n            \n            // 比较SSID\n            if (actual_ssid == ssid) {\n                RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Already connected to SSID '%s'\", ssid.c_str());\n                return true;\n            } else {\n                RCLCPP_WARN(rclcpp::get_logger(\"robdog_control\"), \"Connected to different SSID: '%s' (expected '%s')\",\n                            actual_ssid.c_str(), ssid.c_str());\n                return false;\n            }\n        }\n    }\n\n    // 根据结果输出日志\n    if (found_not_connected) {\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Interface %s is not connected to any network\", g_ifname.c_str());\n    } else if (is_connected) {\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Interface %s is connected but no SSID found\", g_ifname.c_str());\n    } else {\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Interface %s status unknown\", g_ifname.c_str());\n    }\n    \n    return false;\n}\n\n// ****************** event是wifi_set **************************\nvoid RobdogCenter::handleWifiSet(const Json::Value &jBody) {\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Received wifi_set request\");\n    \n    // 记录设置时间和SSID\n    g_wifi_set_time = std::chrono::duration_cast<std::chrono::seconds>(\n        std::chrono::system_clock::now().time_since_epoch()\n    ).count();\n    g_wifi_set_name = jBody[\"body\"][\"wifiName\"].asString();\n    std::string wifiPassword = jBody[\"body\"][\"wifiPassword\"].asString();\n\n    // 执行Python脚本设置WiFi（后台运行）\n    std::string cmd = \"python3 /usr/bin/cmcc_robot/install/ble/lib/python3.8/site-packages/ble/wifi_scan_script.py set \" +\n                      g_wifi_set_name + \" \" + wifiPassword + \" &\";\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Executing command: %s\", cmd.c_str());\n}\n\n// ****************** event是wifi_set_result_query **************************\nvoid RobdogCenter::handleWifiSetResult(const Json::Value &jBody) {\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Received wifi_set_result_query request\");\n    \n    // 获取当前时间戳（秒级）\n    auto now = std::chrono::system_clock::now();\n    long long current_ts = std::chrono::duration_cast<std::chrono::seconds>(now.time_since_epoch()).count();\n    Json::Value response;\n    response[\"deviceId\"] = jBody[\"deviceId\"].asString();\n    response[\"domain\"] = \"DEVICE_PROPERTIES\";\n    response[\"event\"] = \"wifi_set_result_query\";\n    response[\"eventId\"] = jBody[\"eventId\"].asString();\n    response[\"seq\"] = std::to_string(current_ts);\n    \n    Json::Value body;\n    \n    // 检查是否连接到目标SSID\n    if (isWlan0ConnectedToSSID(g_wifi_set_name)) {\n        // 情况1：已成功连接\n        body[\"queryStatus\"] = 1; // 连接成功\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \n                   \"WiFi connected to SSID '%s'\", g_wifi_set_name.c_str());\n    } \n    else {\n        // 计算从设置WiFi到现在的时间差（秒）\n        long long time_diff = current_ts - g_wifi_set_time;\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \n                    \"Time since WiFi set: %lld seconds\", time_diff);\n        \n        if (time_diff > 60) {\n            // 情况2：超时（超过60秒仍未连接成功）\n            body[\"queryStatus\"] = 2; // 连接超时\n            RCLCPP_WARN(rclcpp::get_logger(\"robdog_control\"),\n                       \"WiFi connection to SSID '%s' TIMED OUT (after %lld seconds)\",\n                       g_wifi_set_name.c_str(), time_diff);\n        }\n        else {\n            // 情况3：仍在连接中（未超时）\n            body[\"queryStatus\"] = 0; // 连接中/未连接\n            RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"),\n                       \"WiFi not connected to SSID '%s' (still connecting, %lld seconds elapsed)\",\n                       g_wifi_set_name.c_str(), time_diff);\n        }\n    }\n    \n    response[\"body\"] = body;\n\n    // 发送响应\n    Json::FastWriter writer;\n    std::string jsonString = writer.write(response);\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Response: %s\", jsonString.c_str());\n    sendRequestData(jsonString);\n}\n\n// ****************** event是unbind_notify_voice **************************\nvoid RobdogCenter::handleUnbindNotifyVoice(const Json::Value &jBody) {\n    // 处理机器人平台发来的解绑请求\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"unbind_notify_voice\");\n    \n    homi_speech_interface::srv::SIGCData::Request req;\n    \n    // 将发送的数据设置为固定的字符串 \"unbind_notify_voice\"\n    req.data = \"unbind_notify_voice\";\n    \n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Send req to andlinkscript, req is %s\", req.data.c_str());\n\n    // 创建客户端并同步调用\n    auto future = app_client->async_send_request(std::make_shared<homi_speech_interface::srv::SIGCData::Request>(req));\n    \n    // 等待并获取响应\n    if (future.wait_for(std::chrono::seconds(1)) == std::future_status::ready) {\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Response from APPserver\");\n    } else {\n        RCLCPP_ERROR(rclcpp::get_logger(\"robdog_control\"), \"Failed to call APPservice Service_demo\");\n    }\n    std::this_thread::sleep_for(std::chrono::seconds(2)); \n    std::thread t_audio(&RobdogCtrlNode::playAudio,node_,node_->getResourcePath(\"bind_network/reset.wav\"));\n    t_audio.detach();\n}\n\n// ****************** event是navigation_notify **************************\nvoid RobdogCenter::handleNavigationNotify(const Json::Value &jBody) {\n    // 处理导航通知的逻辑（重定位请求）\n    int type = jBody[\"type\"].asInt();\n    if (type == 0) {\n        Json::Value modifiedBody = jBody;   // jBody本身是不能修改的\n        \n        Json::Value pointsArray = Json::Value(Json::arrayValue);\n        pointsArray.append(-1);\n        modifiedBody[\"points\"] = pointsArray;\n        Json::StreamWriterBuilder writerBuilder;\n        std::string navCtrlMsgs = Json::writeString(writerBuilder, modifiedBody);\n        std_msgs::msg::String nav_ctrl_msg;\n        nav_ctrl_msg.data = navCtrlMsgs;\n        actionPlanningMove_pub->publish(nav_ctrl_msg);\n\n        //WebSocket传输给感知主机\n        Json::Value reqValue;\n        reqValue[\"client_type\"] = CLIENT_LAUNCHER;\n        reqValue[\"target_client\"] = CLIENT_NVIDIA;\n        reqValue[\"action\"] = \"navigation_control\";\n        reqValue[\"params\"] = modifiedBody;\n        SendtoNvOrin(reqValue.toStyledString().c_str(), nConnectIndex_);\n\n    }\n}\n// void RobdogCenter::handleMapPointMarking(const Json::Value &jBody) {\n//     // 处理标记点位逻辑\n//     // 建图完成后，在每次标记点位的过程中，算法会实时消除动态障碍物，并在用户完成点位标记后，同步更新地图。\n//     // 需要客户端在用户开始标记点位、结束标记点位时通知平台、本体算法，并且在标点结束时更新当前地图缓存\n//     RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"handleMapPointMarking\");\n//     int action = jBody[\"action\"].asInt();\n//     Json::Value reqValue;\n//     reqValue[\"action\"] = action == 1 ? 20 : 21;\n//     reqValue[\"url\"] = jBody[\"url\"].asString();\n//     reqValue[\"mapId\"] = jBody[\"mapId\"].asString();\n//     Json::StreamWriterBuilder writerBuilder;\n//     std::string navCtrlMsgs = Json::writeString(writerBuilder, reqValue);\n//     std_msgs::msg::String nav_ctrl_msg;\n//     nav_ctrl_msg.data = navCtrlMsgs;\n//     RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"[PUB] %s\",navCtrlMsgs.c_str());\n//     actionPlanningMove_pub->publish(nav_ctrl_msg);\n// }\n\n// // 处理亲密陪伴指令，切换模式或设置安静状态\nvoid RobdogCenter::handleUserInteraction(const Json::Value &jBody)\n{\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"handleUserInteraction\");\n    if (jBody[\"interMode\"].asInt() == 2)\n    {\n        // 亲密模式关闭\n        inactivity_mode_ = false;\n        inactivity_timer_->cancel();\n        sleep_timer_->reset();\n        quiet_for_three_hours_ = true; // 设置三小时安静模式\n        last_active_time_ = node_->now(); // 记录当前时间为最近一次用户询问的时间\n    }\n    else if(jBody[\"interMode\"].asInt() == 1)\n    {\n        // 亲密模式开启\n        inactivity_mode_ = true;\n        sleep_timer_->cancel();\n        inactivity_timer_->reset();\n        asked_in_last_hour_ = false;\n    }\n    else{\n        RCLCPP_WARN(rclcpp::get_logger(\"robdog_control\"),\"%d-------------Invalid interMode value: %d\", __LINE__,jBody[\"interMode\"].asInt());\n\n    }\n}\n\nvoid RobdogCenter::handleFinishTask(const Json::Value &jBody){\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"handleFinishTask\");\n    if (jBody[\"finishTask\"].asString() == \"mapTask\"){\n        RobotState::getInstance().setCurrentState(RobotStateEnum::NORMAL); \n        // 结束建图\n        Json::Value reqValue;\n        Json::Value modifiedJBody;\n        reqValue[\"client_type\"] = CLIENT_LAUNCHER;\n        reqValue[\"target_client\"] = CLIENT_NVIDIA;\n        reqValue[\"action\"] = \"mapping_control\";\n        modifiedJBody[\"action\"] = 2;\n        modifiedJBody[\"mapId\"] = RobotState::getInstance().getMapId();\n        reqValue[\"params\"] = modifiedJBody;\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"),\"The message of finish_map: %s\", reqValue.toStyledString().c_str());\n        SendtoNvOrin(reqValue.toStyledString().c_str(), nConnectIndex_);\n    }\n    else{\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"handleFinishTask: %s\", jBody[\"body\"][\"finishtask\"].asString().c_str());\n    }\n}\n\nvoid RobdogCenter::handleVoiceResponseNluRaw(const Json::Value &jBody) {\n    static const std::unordered_map<std::string, std::string> emotionVideos = {\n        {\"Sadness\", \"video/Sadness.mp4\"},\n        {\"Happiness\", \"video/Happiness.mp4\"},\n        {\"Anger\", \"video/Anger.mp4\"},\n        {\"Praise\", \"video/Praise.mp4\"},\n        {\"Criticism\", \"video/Criticism.mp4\"},\n        {\"Neutrality\", \"video/Neutrality.mp4\"},\n\n    };\n    auto it = emotionVideos.find(jBody[\"emotionTag\"].asString());\n    if (it != emotionVideos.end()) {\n        // ExpressionChange::getInstance().async_callback_work(it->second,1); // 调用对应的处理函数\n        ExpressionChange::getInstance().async_callback_work(node_->getResourcePath(it->second),1);\n    }\n}\n\nvoid RobdogCenter::handleFollowMeStatus(const Json::Value &inValue) {\n    Json::FastWriter fastWriter;\n    std::string jsonStr = fastWriter.write(inValue);\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"handle FollowMe Status InValue:%s \",jsonStr.c_str());\n    Json::Value response = inValue;\n    Json::Value newBody(Json::objectValue);\n    const Json::Value &jBody = inValue[\"body\"];\n    if (jBody.isMember(\"items\") && jBody[\"items\"].isArray()) {\n        const Json::Value& items = jBody[\"items\"];\n        for (const auto& item : items) {\n            if (item.asString() == \"followMe\") {\n                RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Found 'followMe' in items.\");\n                newBody[\"items\"][\"followMe\"][\"status\"] = RobotState::getInstance().getFollowMeStatus();\n                RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"RobotState::getInstance().getFollowMeStatus is %s\",RobotState::getInstance().getFollowMeStatus().c_str());\n                response[\"body\"] = newBody;\n            }\n            else if (item.asString() == \"emergencyStop\"){\n                RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Found 'emergencyStop' in items.\");\n                newBody[\"items\"][\"emergencyStop\"][\"status\"] = (RobotInfoMgr::getInstance().getRobotStatus()==ROBDOG_STATUS_LOSS_CTRL)?\"on\":\"off\";\n                RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"RobotInfoMgr::getInstance().getRobotStatus() is %d\",RobotInfoMgr::getInstance().getRobotStatus());\n                response[\"body\"] = newBody;\n            }\n        }\n    } else {\n        RCLCPP_ERROR(rclcpp::get_logger(\"robdog_control\"),\"'items' is not a valid array or is missing.\" );\n    }\n    std::string jsonString = fastWriter.write(response);\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Res to FollowMe Status platform is %s\",jsonString.c_str());\n    sendRequestData(jsonString);\n}\n\nvoid RobdogCenter::handleBindStatusQuery()\n{\n    Json::Value response;\n    response[\"deviceId\"] = RobotState::getInstance().getDeviceId();\n    response[\"domain\"] = \"ROBOT_BUSINESS_DEVICE\";\n    response[\"event\"] = \"bind_status_query\";\n    response[\"eventId\"] = \"bind_status_query\"+to_string(base::homiUtils::getCurrentTimeStamp());\n    response[\"seq\"] = to_string(base::homiUtils::getCurrentTimeStamp());\n    response[\"response\"] = \"false\";\n\n    Json::Value body={};\n\n    response[\"body\"];\n    response[\"body\"].append(Json::objectValue);\n\n    Json::FastWriter writer;\n    std::string jsonString = writer.write(response);\n\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"handleBindStatusRequest cmd : %s\", jsonString.c_str());\n\n    sendRequestData(jsonString);\n}\n\nvoid RobdogCenter::handleBindStatusResponse(const Json::Value &jBody)\n{\n    Json::FastWriter fastWriter;\n    std::string jsonStr = fastWriter.write(jBody);\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"handleBindStatusResponse InValue:%s \",jsonStr.c_str());\n    if (!jBody[\"status\"].isNull()) {\n        int bindstatus = jBody[\"status\"].asInt();\n        setBindStatus(bindstatus);\n        \n        // 如果是解绑状态(0)，调用handleUnbindNotify处理解绑\n        if (bindstatus == 0) {\n            RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"检测到解绑状态，调用handleUnbindNotify\");\n            handleUnbindNotify(jBody);\n        }\n    }\n}\n\nvoid RobdogCenter::actionNavigationResponse(const string msg3)\n{\n        // ------------- 上报导航路径信息 ----------------------\n    Json::Value response;\n    response[\"deviceId\"] = RobotState::getInstance().getDeviceId();\n    response[\"domain\"] = \"DEVICE_TRIP\";\n    response[\"event\"] = \"navigation_response\";\n    response[\"eventId\"] = getEventId();\n    response[\"seq\"] = to_string(base::homiUtils::getCurrentTimeStamp());\n    response[\"response\"] = \"false\";\n\n// TODO 相关数据需要更新\n    Json::Value body;\n    body[\"tripId\"] = (Json::Int64)getTripId();\n    body[\"checkResult\"] = 1;\n    body[\"distance\"] = -1;\n    body[\"duration\"] = -1;\n    body[\"power\"] = 80;\n    body[\"estimatedCostPower\"] = -1;\n    body[\"maxDistance\"] = -1;\n\n\n    string msg2 =removeEscapeCharacters(msg3);\n    string msg = msg2.substr(1, msg2.length() - 2);\n\n    RCLCPP_INFO(rclcpp::get_logger(\"actionNavigationResponse\"), \"actionPlanningMove2:%s\", msg.c_str());\n    Json::Reader reader;\n    Json::Value value;\n    if (false == reader.parse(msg, value)) {\n        RCLCPP_ERROR(rclcpp::get_logger(\"actionNavigationResponse\"), \"reader.parse failed: %s\", reader.getFormattedErrorMessages().c_str());\n        return;\n    }\n\n    Json::StreamWriterBuilder writer;\n    std::string unescapedMsg = Json::writeString(writer, value[\"12\"]);\n\n    RCLCPP_INFO(rclcpp::get_logger(\"actionNavigationResponse\"), \"Unescaped JSON: %s\", unescapedMsg.c_str());\n\n    if (!value[\"12\"].isNull()) {\n        Json::Value params = value[\"12\"];\n        RCLCPP_INFO(rclcpp::get_logger(\"NvidiaCtrlNode\"), \"params[12]:%s\", params.toStyledString().c_str());\n        Json::Value points = params[\"list\"];\n        replaceKeyInJsonList(points, \"latitude\", \"lat\");\n        replaceKeyInJsonList(points, \"longitude\", \"lon\");\n        body[\"navigationPath\"] = points;\n    }\n    response[\"body\"] = body;\n\n    Json::FastWriter writer2;\n    std::string jsonString = writer2.write(response);\n\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"point actionNavigationResponse cmd : %s\", jsonString.c_str());\n\n    sendRequestData(jsonString);\n}\n\n\nvoid RobdogCenter::handleDeviceSettingQuery()\n{\n    Json::Value response;\n    response[\"deviceId\"] = RobotState::getInstance().getDeviceId();\n    response[\"domain\"] = \"DEVICE_PROPERTIES\";\n    response[\"event\"] = \"device_settings_read\";\n    response[\"eventId\"] = \"device_settings_read\"+to_string(base::homiUtils::getCurrentTimeStamp());\n    response[\"seq\"] = to_string(base::homiUtils::getCurrentTimeStamp());\n    response[\"response\"] = \"false\";\n\n    Json::Value settings(Json::arrayValue);\n    settings.append(\"volume\");\n    settings.append(\"mode\");\n    settings.append(\"flashlight\");\n    settings.append(\"emergencyContacts\");\n    settings.append(\"familyMembers\");\n    settings.append(\"uwbTag\");\n\n    Json::Value body;\n    body[\"settings\"] = settings;\n    response[\"body\"] = body;\n\n    Json::FastWriter writer;\n    std::string jsonString = writer.write(response);\n\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"handleDeviceSettingQuery cmd : %s\", jsonString.c_str());\n\n    sendRequestData(jsonString);\n}\n\nvoid RobdogCenter::handleDeviceSettingResponse(const Json::Value &jBody)\n{\n    Json::FastWriter fastWriter;\n    std::string jsonStr = fastWriter.write(jBody);\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"handleDeviceSettingResponse jBody:%s \",jsonStr.c_str());\n    if (!jBody[\"settings\"].isNull() && jBody[\"settings\"].isObject()) {\n        const Json::Value settings = jBody[\"settings\"];\n        if (!settings[\"flashlight\"].isNull() && settings[\"flashlight\"].isObject()) {\n            std::string flashlightStatus = settings[\"flashlight\"][\"status\"].asString();\n            int flashlightBrightness = settings[\"flashlight\"][\"brightness\"].asInt();\n            RobotState::getInstance().setFlashStatus(flashlightStatus);\n            RobotState::getInstance().setFlashBrightness(flashlightBrightness);\n            RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"RobotState::getInstance().setFlashStatus is %s\",RobotState::getInstance().getFlashStatus().c_str());\n            RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"RobotState::getInstance().setFlashBrightness is %d\", RobotState::getInstance().getFlashBrightness());\n        }\n\n        if (!settings[\"volume\"].isNull()) {\n            int volume = settings[\"volume\"].asInt();\n            RobotState::getInstance().setVolume(volume);\n            RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"RobotState::getInstance().setVolume is %d\", RobotState::getInstance().getVolume());\n        }\n\n        if (!settings[\"mode\"].isNull()) {\n            int mode = settings[\"mode\"].asInt();\n            RobotState::getInstance().setRobdogStatus(mode);\n            RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"RobotState::getInstance().setRobdogStatus is %d\", RobotState::getInstance().getRobdogStatus());\n        }\n\n        // 解析紧急联系人\n        if (settings.isMember(\"emergencyContacts\") && settings[\"emergencyContacts\"].isArray()) {\n            std::vector<EmergencyContact> emergencyContacts;\n            for (const auto& contact : settings[\"emergencyContacts\"]) {\n                EmergencyContact ec;\n                ec.nickName = contact[\"nickName\"].asString();\n                ec.phone = contact[\"phone\"].asString();\n                emergencyContacts.push_back(ec);\n            }\n            RobotState::getInstance().setEmergencyContacts(emergencyContacts);\n        }\n\n        // 解析家庭成员\n        if (settings.isMember(\"familyMembers\") && settings[\"familyMembers\"].isArray()) {\n            std::vector<FamilyMember> familyMembers;\n            for (const auto& member : settings[\"familyMembers\"]) {\n            FamilyMember fm;\n            fm.nickName = member[\"nickName\"].asString();\n            fm.roleName = member[\"roleName\"].asString() == \"null\" ? \"\" : member[\"roleName\"].asString();\n            fm.birthday = member[\"birthday\"].asString();\n            fm.phone = member[\"phone\"].asString();\n            if(member.isMember(\"sex\")) {\n                fm.sex = member[\"sex\"].asInt();\n            } else {\n                fm.sex = 0; \n            }\n            fm.picture = member[\"picture\"].asString();\n            familyMembers.push_back(fm);\n            }\n            RobotState::getInstance().setFamilyMembers(familyMembers);\n        }\n        if (!settings[\"uwbTag\"].isNull()) {\n            std::string uwbTag = settings[\"uwbTag\"].asString();\n            RobotState::getInstance().setUwbTag(uwbTag);\n            RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"RobotState::getInstance().setUwbTag is %s\", RobotState::getInstance().getUwbTag().c_str());\n        }\n\n    }    \n\n}\n\nvoid RobdogCenter::handleMapReposition(const Json::Value &inValue){\n    std::string PathEventId = inValue[\"eventId\"].asString();\n    RobotState::getInstance().setPathEventId(PathEventId);\n    Json::Value jBody = inValue[\"body\"];\n    std::string mapId = jBody[\"mapId\"].asString();\n    std::string current_mapid = RobotState::getInstance().getMapId();\n    if(mapId != current_mapid){\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"%d----handleMapReposition mapId %s is not the same as current mapId %s\", __LINE__,mapId.c_str(), current_mapid.c_str());\n        RobotState::getInstance().setMapId(mapId);\n        update_map_points_path();\n        RobotState::getInstance().saveConfig();\n    }\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"handleMapReposition mapId:%s\",mapId.c_str());\n    repositioning = true;\n    // checkNvidiaServiceStatus(true,mapId);//导航没开则先打开导航\n    // std::thread t_audio(&RobdogCtrlNode::playAudio,node_,node_->getResourcePath(\"audio/nav/reposition_failed.wav\"));\n    // t_audio.detach();\n    sendMapRepositionResponse(NAVIGATION_CODE_NO_AUTOREPOSITION, Json::Value()); // 传递空值\n    repositioning = false;\n\n}\n\n// 地图坐标转像素坐标\nstd::array<double, 3> RobdogCenter::mapToPixel(double map_x, double map_y, double yaw,\n                                               double origin_x, double origin_y, double resolution, int img_height) {\n    // 计算像素坐标\n    double pixel_x = (map_x - origin_x) / resolution;\n    double pixel_y_flip = (map_y - origin_y) / resolution;\n    // 反转y轴\n    double pixel_y = img_height - pixel_y_flip - 1;\n    // yaw（弧度）转角度\n    double angle_deg = yaw ;\n    return {pixel_x, pixel_y, angle_deg};\n}\n\n// 像素坐标转地图坐标\nstd::array<double, 3> RobdogCenter::pixelToMap(double pixel_x, double pixel_y, double angle_deg,\n                                 double origin_x, double origin_y, double resolution, int img_height) {\n    double pixel_y_flip = img_height - pixel_y - 1;\n    double map_x = origin_x + pixel_x * resolution;\n    double map_y = origin_y + pixel_y_flip * resolution;\n    double yaw = angle_deg * M_PI / 180.0;\n    return {map_x, map_y, yaw};\n}\n\nbool RobdogCenter::loadMapConfig(const std::string& filePath, MapConfig& config) {\n    std::ifstream fin(filePath);\n    if (!fin.is_open()) {\n        std::cerr << \"无法打开文件: \" << filePath << std::endl;\n        return false;\n    }\n\n    // 读取宽高\n    fin >> config.img_width >> config.img_height;\n\n    // 读取分辨率\n    fin >> config.resolution;\n\n    // 读取原点坐标 x 和 y\n    fin >> config.origin_x >> config.origin_y;\n\n    fin.close();\n    return true;\n}\n\n\nvoid RobdogCenter::sendPoint(double x, double y, double angle) {\n    // 构造 JSON 消息\n    Json::Value json_data;\n    json_data[\"pix_x\"] = x;\n    json_data[\"pix_y\"] = y;\n    json_data[\"angle\"] = angle;\n\n    // 转换为字符串\n    Json::FastWriter writer;\n    std::string message_str = writer.write(json_data);\n\n    // 发送消息\n    std_msgs::msg::String message;\n    message.data = message_str;\n    point_transform_publisher_->publish(message);\n}\n\nvoid RobdogCenter::pointTransformResultCallback(const std_msgs::msg::String::SharedPtr msg) {\n    try {\n        // 解析 JSON 数据\n        Json::Reader reader;\n        Json::Value value;\n        std::string json_str = msg->data;\n        if (!reader.parse(json_str, value)) {\n            RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Failed to parse JSON: %s\", reader.getFormattedErrorMessages().c_str());\n            return;\n        }\n        double x = value[\"x\"].asDouble();\n        double y = value[\"y\"].asDouble();\n        double angle = value[\"angle\"].asDouble(); \n        RobotState::getInstance().setMoveTaskPose(value); // 更新当前机器人位姿\n        // 打印结果\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Received transformed point: x=%.3f, y=%.3f, angle=%.6f\", x, y, angle);\n    } catch (const std::exception& e) {\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Failed to parse JSON: %s\", e.what());\n    }\n}\n\nvoid RobdogCenter::RepositioningTimeout() {\n    if (repositioning || repositioning_manual) {\n        RCLCPP_WARN(rclcpp::get_logger(\"robdog_control\"), \"map_reposition timeout.\");\n        repositioning_timer_->cancel();\n        int taskStatusCode = NAVIGATION_CODE_TIME_OUT;\n        std::string taskStatusMsg = getDescription(taskStatusCode);\n        Json::Value msgValue;\n        msgValue[\"code\"] = taskStatusCode;\n        msgValue[\"msg\"] = taskStatusMsg;\n        sendMapRepositionResponse(taskStatusCode,msgValue);\n        sendCommandToUSLAM(\"localization/stop\");\n        repositioning = false;\n        repositioning_manual = false;\n    }\n}\n\nvoid RobdogCenter::handleMapRepositionManual(const Json::Value &inValue) { \n    // 重定位定时器,15秒超时\n    repositioning_timer_ = node_->create_wall_timer(std::chrono::seconds(15), \n        std::bind(&RobdogCenter::RepositioningTimeout, this));\n    std::string PathEventId = inValue[\"eventId\"].asString();\n    RobotState::getInstance().setPathEventId(PathEventId);\n    Json::Value jBody = inValue[\"body\"];\n    Json::Value point = jBody[\"point\"];\n    std::string mapId = jBody[\"mapId\"].asString();\n    std::string current_mapid = RobotState::getInstance().getMapId();\n    if(mapId != current_mapid){\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"%d----handleMapRepositionManual mapId %s is not the same as current mapId %s\", __LINE__,mapId.c_str(), current_mapid.c_str());\n        RobotState::getInstance().setMapId(mapId);\n        update_map_points_path();\n        RobotState::getInstance().saveConfig();\n    }\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"handleMapRepositionManual mapId:%s\",mapId.c_str());\n    repositioning_manual = true;\n    double x = point[\"x\"].asDouble();\n    double y = point[\"y\"].asDouble();\n    double angle = point[\"angle\"].asDouble(); // 角度，单位为度\n    sendPoint(x, y, angle); // 发送点位到地图转换节点\n    // 等待转换结果\n    std::this_thread::sleep_for(std::chrono::milliseconds(500)); // 等待500毫秒，确保点位转换完成\n    Json::Value currentPose = RobotState::getInstance().getMoveTaskPose();\n    double map_x = currentPose[\"x\"].asDouble();\n    double map_y = currentPose[\"y\"].asDouble();\n    double yaw = currentPose[\"angle\"].asDouble();\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"current pos x: %f, y: %f, theta: %f\", map_x, map_y, yaw);\n\n\n    // MapConfig config;\n    // if (loadMapConfig(\"/root/tjc_test/map_server_ros2/scripts/tmp/unitree_files/map_display.txt\", config)) {\n    //     RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Width: %d, Height: %d\", config.img_width, config.img_height);\n    //     RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Resolution: %f\", config.resolution);\n    //     RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Origin X: %f, Origin Y: %f\", config.origin_x, config.origin_y);\n    // }\n    // auto result = pixelToMap(x, y, angle, config.origin_x, config.origin_y, config.resolution, config.img_height);\n    // double map_x = result[0], map_y = result[1], yaw = result[2];\n\n    std::ostringstream oss;\n    oss << \"localization/set_initial_pose/\" << map_x << \"/\" << map_y << \"/\" << yaw;\n    sendCommandToUSLAM(oss.str());\n    std::this_thread::sleep_for(std::chrono::milliseconds(200)); // 等待200毫秒，确保指令发送完成\n    sendCommandToUSLAM(\"localization/start\");\n}\n// ---------------------------------------------- 具体的函数实现 ------------------------------------------------\n// 和定时器相关的操作\n\nvoid RobdogCenter::setTotalCount(int count) {\n    total_count_ = count;\n    send_count_ = 0; // 重置已发送次数\n}\n\nvoid RobdogCenter::triggerTimerCallback() {\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Timer Start\");\n    timer_robMove->reset();\n    timerCallback();\n}\n\nvoid RobdogCenter::timerCallback() {\n    if (send_count_ < total_count_) {\n        // publishVelocity(current_twist_msg_);\n        publishVelocity(std::make_shared<geometry_msgs::msg::Twist>(current_twist_msg_));\n\n        ++send_count_; // 增加了定时器次数\n        // RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Pub times: %d, Total Count_: %d\", send_count_, total_count_);\n    } else {\n        // 完成发送后停止定时器\n        // timer_robMove.stop();\n        timer_robMove->cancel();\n        send_count_ = 0;\n    }\n}\n\nvoid RobdogCenter::heartbeatTimerCallback() {\n    if ((base::homiUtils::getCurrentTimeStamp() - currentTimeStramp_) > WEBSOCKET_CON_TIMEOUT) {\n        bConnected_ = false;\n        std::cerr << \"websocket reconnect\" ;\n        WS_Connect(strConnectUrl_.c_str());\n    }\n}\n\nint RobdogCenter::readBindStatusFromConfig() {\n    const std::string configPath = \"/etc/cmcc_robot/andlinkSdk.conf\";\n    std::ifstream configFile(configPath);\n    \n    if (!configFile.is_open()) {\n        RCLCPP_WARN(rclcpp::get_logger(\"RobdogCenter\"), \"无法打开配置文件: %s\", configPath.c_str());\n        return -1;\n    }\n\n    std::string line;\n    while (std::getline(configFile, line)) {\n        // 忽略注释和空行\n        if (line.empty() || line[0] == '#') {\n            continue;\n        }\n\n        // 查找userBind配置项\n        if (line.find(\"userBind\") != std::string::npos) {\n            std::string value = line.substr(line.find(\"=\") + 1);\n            value = trim(value);\n            \n            if (value == \"1\") {\n                RCLCPP_INFO(rclcpp::get_logger(\"RobdogCenter\"), \"从配置文件读取到绑定状态: 已绑定\");\n                return 1;\n            } else if (value == \"0\") {\n                RCLCPP_INFO(rclcpp::get_logger(\"RobdogCenter\"), \"从配置文件读取到绑定状态: 未绑定\");\n                return 0;\n            }\n        }\n    }\n\n    RCLCPP_WARN(rclcpp::get_logger(\"RobdogCenter\"), \"配置文件中未找到userBind配置项\");\n    return 0;\n}\n\nstd::string RobdogCenter::trim(const std::string& str) {\n    size_t first = str.find_first_not_of(\" \\t\\n\\r\");\n    if (first == std::string::npos) {\n        return \"\";\n    }\n    size_t last = str.find_last_not_of(\" \\t\\n\\r\");\n    return str.substr(first, (last - first + 1));\n}\n\nvoid RobdogCenter::internetTimerCallback() {\n    // 发送网络状态请求 - 无论绑定状态如何，都需要定时执行\n    Json::FastWriter writer;\n    Json::Value requestJs;\n    requestJs[\"command\"]=\"getNetworkStatus\";\n    std::string jsonString = writer.write(requestJs);\n    auto reqMsg = std::make_shared<homi_speech_interface::srv::NetCtrl::Request>();\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Request to net_ctrl_srv is %s\", jsonString.c_str());\n    reqMsg->data = jsonString;\n    auto ret = net_client->wait_for_service(std::chrono::milliseconds(500));\n    if(ret==false) {\n        RCLCPP_WARN(rclcpp::get_logger(\"robdog_control\"), \"Failed to waitForExistence service assistant\");\n        return;\n    }\n    auto result = net_client->async_send_request(reqMsg, std::bind(&RobdogCenter::net_srv_callback, this, std::placeholders::_1));\n\n    \n    // processNetworkStatusData();\n    // 注意: 处理逻辑已移至 processNetworkStatusData 函数，将在 net_srv_callback 中调用\n}\nvoid RobdogCenter::wakeupCallback(const std::shared_ptr<homi_speech_interface::msg::Wakeup> msg){\n    RCLCPP_INFO(rclcpp::get_logger(\"RobdogCenter\"),\"Received msg form wakeup node: event:%s,angle:%d\", msg->ivw_word.c_str(),msg->angle);\n    is_interrupted_ = true;  \n    cv_.notify_all();  \n}\nvoid RobdogCenter::startSequence() {\n    stopSequence();  \n    if (sequence_thread_.joinable()) {\n        sequence_thread_.join();\n    }\n    is_interrupted_ = false;  \n    sequence_thread_ = std::thread(&RobdogCenter::sequenceExecutor, this); \n}\nvoid RobdogCenter::stopSequence() {\n    is_interrupted_ = true;  \n    cv_.notify_all();  \n}\nvoid RobdogCenter::sequenceExecutor() {\n    RCLCPP_INFO(rclcpp::get_logger(\"RobdogCenter\"), \"Sequence started.\");\n    for (size_t i = 0; i < mapping_table_.size(); ++i) {\n        if (is_interrupted_) {\n            RCLCPP_WARN(rclcpp::get_logger(\"RobdogCenter\"), \"Sequence interrupted at step %zu.\", i);\n            break;\n        }\n        const auto& entry = mapping_table_[i];\n        ExpressionChange::getInstance().async_callback_work(node_->getResourcePath(entry.video_path), 1);\n        node_->handleLightControl(entry.light_cmd, entry.light_param);\n        if (!entry.action.empty()) {\n            node_->handleInteractionAction(entry.action);\n        }\n        // sendStringToBrocast(entry.audio_path);\n        node_->sendAudio(node_->getResourcePath(entry.audio_path));\n        RCLCPP_INFO(rclcpp::get_logger(\"RobdogCenter\"), \"Sequence play audio %s.\",entry.audio_path.c_str());\n        std::unique_lock<std::mutex> lock(cv_mutex_);\n        bool wait_interrupted = cv_.wait_for(\n            lock, \n            std::chrono::seconds(entry.interval_seconds), \n            [&]() { return is_interrupted_.load(); }  \n        );\n        if (wait_interrupted) {\n            RCLCPP_WARN(rclcpp::get_logger(\"RobdogCenter\"), \"Wait for next step but interrupted.\");\n            is_interrupted_ = false; \n            int result = std::system(\"pkill -f aplay\");\n            if (result != 0) {\n                RCLCPP_WARN(rclcpp::get_logger(\"RobdogCenter\"), \"Pkill aplay failed.\");\n            }\n            break;  \n        }\n        RCLCPP_INFO(rclcpp::get_logger(\"RobdogCenter\"), \"Going to exe next sequence.\");\n    }\n    node_->handleLightControl(DEEP_CMD_LIGHT_04, 0);\n    RCLCPP_INFO(rclcpp::get_logger(\"RobdogCenter\"), \"Sequence finished or interrupted,Restore white light\");\n}\n\n// 处理网络状态数据\nvoid RobdogCenter::processNetworkStatusData() {\n    static int empty_count = 0;\n    static std::string last_internet_status = \"\";\n    static int last_bind_status = -1;\n    static std::chrono::steady_clock::time_point last_audio_time = std::chrono::steady_clock::now();\n    static std::chrono::steady_clock::time_point last_QR_time = std::chrono::steady_clock::now();\n    static std::chrono::steady_clock::time_point last_network_error_audio_time = std::chrono::steady_clock::now();\n    static bool first_bind_stand_triggered = false;  // 新增：标记是否已经触发过首次绑定站立\n    std::string current_internet_status = \"false\";\n    std::string video_path;\n    \n    // 检查绑定状态\n    static int bindStatus = -1;\n\n    bool status_changed = false;\n    bool QR_play = false;\n\n    // 解析 JSON\n    Json::Reader reader;\n    Json::Value value;\n    if (!reader.parse(g_netctrl_ret, value) || value.isNull()) {\n        RCLCPP_ERROR(rclcpp::get_logger(\"RobdogCenter\"), \"解析 JSON 失败: %s\", g_netctrl_ret.c_str());\n        if (bindStatus == -1) {\n            bindStatus = readBindStatusFromConfig();\n        }\n        return;\n    }\n        \n    \n    // 处理绑定状态\n    if (value.isMember(\"userBind\") && !value[\"userBind\"].isNull()) {\n        std::string userBind = value[\"userBind\"].asString();\n        int new_bind_status = (userBind == \"true\") ? 1 : 0;\n        if (new_bind_status != last_bind_status) {  // 只在状态真正变化时更新\n            status_changed = true;\n            RCLCPP_INFO(rclcpp::get_logger(\"RobdogCenter\"), \"从网络响应中获取新的绑定状态: %s\", userBind.c_str());\n            if(new_bind_status != 1){\n                node_->handleLightControl(DEEP_CMD_LIGHT_07, 0);\n            }\n        }\n        bindStatus = new_bind_status;\n    }\n    // }\n    \n    // 处理网络状态\n    if (value.isMember(\"isInternetConnect\") && !value[\"isInternetConnect\"].isNull()) {\n        current_internet_status = value[\"isInternetConnect\"].asString();\n    }\n    // 绑定状态发生变化时更新表情和播放声音\n    RCLCPP_INFO(rclcpp::get_logger(\"RobdogCenter\"), \"bindStatus'%d  last_bind_status:%d\",bindStatus,last_bind_status);\n    if (bindStatus != last_bind_status) {\n        std::string audio_path;\n        last_internet_status=\"\";\n        if (bindStatus == 0) {\n            // 变为未绑定状态\n            if (current_internet_status == \"true\") {\n                video_path = node_->getResourcePath(dynamic_nobind_video_path);\n                dynamic_QRcode();\n                // 粉灯呼吸\n                node_->handleLightControl(DEEP_CMD_LIGHT_PINK_BREATHING, 0);\n            } else {\n                video_path = node_->getResourcePath(static_nobind_video_path);\n                // 黄灯呼吸\n                node_->handleLightControl(DEEP_CMD_LIGHT_YELLOW_BREATHING, 0);\n            }\n            QR_play = true;\n            audio_path = node_->getResourcePath(\"audio/device_not_bound.wav\");\n            RCLCPP_INFO(rclcpp::get_logger(\"RobdogCenter\"), \"绑定状态变为未绑定，更新表情并播放提示音\");\n            \n            // 播放状态变化提示音\n            RCLCPP_INFO(rclcpp::get_logger(\"RobdogCenter\"), \"播放绑定状态变化提示音: %s\", audio_path.c_str());\n            std::thread t_audio(&RobdogCtrlNode::playAudio, node_, audio_path);\n            t_audio.detach();\n\n            node_->GetDown();\n        } else {\n            // 变为已绑定状态\n            video_path = node_->getResourcePath(resource_default_video_path);\n            bool is_unbind_to_bind = (last_bind_status == 0);\n            // 首次绑定成功时触发站立动作\n            // if (!first_bind_stand_triggered) {\n                RCLCPP_INFO(rclcpp::get_logger(\"RobdogCenter\"), \"首次绑定成功，触发站立动作\");\n                node_->StandUp();\n                std::thread check_stand_status([this,is_unbind_to_bind]() {\n                    // 等待5秒\n                    std::this_thread::sleep_for(std::chrono::seconds(5));\n                    \n                                    // 检查站立状态\n                int status = 0;\n                if(RobotInfoMgr::getInstance().getRobotStatus() == ROBDOG_STATUS_FORCE_CTRL_LEVEL_LOWSPPEED_GAIT){\n                    status = 0;  // 站立\n                } else if(RobotInfoMgr::getInstance().getRobotStatus() == ROBDOG_STATUS_GETDOWN){\n                    status = 1;  // 趴下 \n                } else if(RobotInfoMgr::getInstance().getRobotStatus() == ROBDOG_STATUS_SITDOWN){\n                    status = 2;  // 坐下\n                }\n                \n                RCLCPP_INFO(rclcpp::get_logger(\"RobdogCenter\"), \"5秒后检查站立状态: %d (0-站立, 1-趴下, 2-坐下)\", status);\n                \n                std::string audio_path;\n                if (status == 0) { // 站立成功\n                    RCLCPP_INFO(rclcpp::get_logger(\"RobdogCenter\"), \"站立成功，播放欢迎语音\");\n                    if (is_unbind_to_bind) {\n                        RCLCPP_INFO(rclcpp::get_logger(\"RobdogCenter\"), \"Never bound to bind changes, execute the welcome sequence\");\n                        startSequence();\n                    } else {\n                        RCLCPP_INFO(rclcpp::get_logger(\"RobdogCenter\"), \"The binding state changes but does not start from the binding, and the welcome sequence is not executed\");\n                    }\n                    // audio_path = node_->getResourcePath(\"audio/welcome_message.wav\");\n                    // std::thread t_audio(&RobdogCtrlNode::playAudio, node_, audio_path);\n                    // t_audio.detach();\n                } else { // 趴下或坐下\n                    RCLCPP_INFO(rclcpp::get_logger(\"RobdogCenter\"), \"站立失败，播放提示语音并再次尝试站立\");\n                    audio_path = node_->getResourcePath(\"audio/stand_failed.wav\");\n                    std::thread t_audio(&RobdogCtrlNode::playAudio, node_, audio_path);\n                    t_audio.detach();\n                    node_->StandUp(); // 再次尝试站立\n                }\n                });\n                check_stand_status.detach();  // 分离线程，让它在后台运行\n                first_bind_stand_triggered = true;  // 标记已触发\n            // }\n        }\n        \n        // 更新表情\n        ExpressionChange::getInstance().async_callback_work(video_path, 0);\n        \n        last_bind_status = bindStatus;\n        // if (bindStatus != 0) {\n        //     last_internet_status = \"\";  // 重置网络状态，以便触发网络状态检查\n        // } else {\n        //     return;  // 如果变为未绑定状态，直接返回，不检查网络状态\n        // }\n    }\n    \n    // 检查是否需要播放未绑定提示音（每3分钟播放一次）\n    if (bindStatus == 0) {\n        auto current_time = std::chrono::steady_clock::now();\n        auto elapsed_time = std::chrono::duration_cast<std::chrono::minutes>(current_time - last_audio_time).count();\n        if (elapsed_time >= 3) {\n            // 播放未绑定提示音\n            std::string audio_path = node_->getResourcePath(\"audio/device_not_bound.wav\");\n            RCLCPP_INFO(rclcpp::get_logger(\"RobdogCenter\"), \"定期播放未绑定提示音: %s\", audio_path.c_str());\n            std::thread t_audio(&RobdogCtrlNode::playAudio, node_, audio_path);\n            t_audio.detach();\n            last_audio_time = current_time;\n        }\n\n        auto elapsed_qr_time = std::chrono::duration_cast<std::chrono::minutes>(current_time - last_QR_time).count();\n        // 未绑定5分钟更新动态二维码\n        if ((QR_play == false) && (current_internet_status == \"true\") &&(elapsed_qr_time >= 5)) {                \n            video_path = node_->getResourcePath(dynamic_nobind_video_path);\n            dynamic_QRcode();\n            // 更新表情\n            ExpressionChange::getInstance().async_callback_work(video_path, 0);            \n            last_QR_time = current_time;\n        }\n        return;  \n    }\n    \n    // 只有在已绑定状态下才检查网络状态\n    if (bindStatus == 1) {\n        // 检查 isInternetConnect 字段\n        if (value.isMember(\"isInternetConnect\") && !value[\"isInternetConnect\"].isNull()) {\n            \n            // 网络异常时每3分钟播报一次\n            if (current_internet_status != \"true\") {\n                auto current_time = std::chrono::steady_clock::now();\n                auto elapsed_time = std::chrono::duration_cast<std::chrono::minutes>(current_time - last_network_error_audio_time).count();\n                if (elapsed_time >= 3) {\n                    std::string audio_path = node_->getResourcePath(\"audio/network_disconnected.wav\");\n                    RCLCPP_INFO(rclcpp::get_logger(\"RobdogCenter\"), \"定期播放断网提示音: %s\", audio_path.c_str());\n                    std::thread t_audio(&RobdogCtrlNode::playAudio, node_, audio_path);\n                    t_audio.detach();\n                    last_network_error_audio_time = current_time;\n                }\n            }\n            \n            // 只在网络状态发生变化时更新表情\n            if (current_internet_status != last_internet_status) {\n                std::string audio_path;\n                \n                if (current_internet_status == \"true\") {\n                    video_path = node_->getResourcePath(resource_default_video_path);\n                    audio_path = node_->getResourcePath(\"audio/network_connected.wav\");\n                    RCLCPP_INFO(rclcpp::get_logger(\"RobdogCenter\"), \"网络连接恢复，播放提示音\");\n\n                    // 白灯常亮\n                    node_->handleLightControl(DEEP_CMD_LIGHT_04, 0);\n\n                } else {\n                    video_path = node_->getResourcePath(resource_nonet_video_path);\n                    audio_path = node_->getResourcePath(\"audio/network_disconnected.wav\");\n                    RCLCPP_INFO(rclcpp::get_logger(\"RobdogCenter\"), \"网络连接断开，播放提示音\");\n\n                    // 黄灯常亮 【网络断开】\n                    node_->handleLightControl(DEEP_CMD_LIGHT_07, 0);\n                }\n                \n                RCLCPP_INFO(rclcpp::get_logger(\"RobdogCenter\"), \"网络状态变化，更新表情, isInternetConnect: %s, video_path: %s\", \n                    current_internet_status.c_str(), video_path.c_str());\n                ExpressionChange::getInstance().async_callback_work(video_path, 0);\n                \n                // 播放网络状态变化提示音\n                std::thread t_audio(&RobdogCtrlNode::playAudio, node_, audio_path);\n                t_audio.detach();\n                \n                last_internet_status = current_internet_status;\n            }\n        }\n    }\n    \n    // 处理初始化状态\n    // int init_finish_status = getInitFinishStatus();\n    // if (init_finish_status == 0) {\n    //     setInitFinishStatus(1);\n    //     RCLCPP_INFO(rclcpp::get_logger(\"RobdogCenter\"), \"初始化完成，bindStatus: %d\", bindStatus);\n    // }\n}\n\n\n\nvoid RobdogCenter::timerRobotPoseCallback() {\n    Json::Value body = RobotState::getInstance().getCurRobotPose();\n    if (body.isNull()) {\n        // RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"timerRobotPoseCallback: no position\"); //【发的太频繁先注释】\n        return;\n    }\n    // RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"timerRobotPoseCallback: has position\");\n    \n    // ------------- 上报点位信息 ----------------------\n    Json::Value response;\n    response[\"deviceId\"] = RobotState::getInstance().getDeviceId();\n    response[\"domain\"] = \"DEVICE_INTERACTION\";\n    response[\"event\"] = \"point_report\";\n    response[\"eventId\"] = \"robdog_plat_\" + to_string(base::homiUtils::getCurrentTimeStamp());\n    response[\"seq\"] = 0;\n\n    Json::Value currentPoint;\n    currentPoint[\"x\"] = body[\"x\"].asDouble();\n    currentPoint[\"y\"] = body[\"y\"].asDouble();\n    currentPoint[\"angle\"] = body[\"angle\"].asDouble();\n    currentPoint[\"pointStatus\"] = body[\"pointStatus\"].asInt(); \n    response[\"body\"] = currentPoint;\n\n    Json::FastWriter writer;\n    std::string jsonString = writer.write(response);\n\n    if(RobotState::getInstance().getCurrentState() == RobotStateEnum::NAVIGATION){//改成仅导航任务中打印点位信息，避免太过频繁打印\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"point report cmd : %s\", jsonString.c_str());\n    }\n\n    // 调用服务并处理响应【给平台上报点位信息】\n    sendRequestData(jsonString);   \n}\n\n// 路径上报\nvoid RobdogCenter::timerRobotPathCallback() {\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \" --------------------- Start TimerRobotPathCallback ---------------------\");\n    Json::Value body = RobotState::getInstance().getCurRobotPose();\n    Json::Value path = RobotState::getInstance().getMoveTaskPath();\n    if (body.isNull() || path.isNull()) {\n        return;\n    }\n    // ------------- 上报路径信息 ----------------------\n    // 创建输出 JSON 对象\n    Json::Value pathBody;\n    Json::Value outputRoot;\n    outputRoot[\"deviceId\"] = RobotState::getInstance().getDeviceId();\n    outputRoot[\"domain\"] = \"DEVICE_INTERACTION\";\n    outputRoot[\"event\"] = \"move_path_report\";\n    outputRoot[\"eventId\"] = RobotState::getInstance().getPathEventId(); // \"robdog_plat_\" + to_string(base::homiUtils::getCurrentTimeStamp());\n    std::time_t now = std::time(nullptr);\n    outputRoot[\"seq\"] = std::to_string(now);\n    outputRoot[\"response\"] = \"false\";\n    pathBody[\"batchId\"] = RobotState::getInstance().getbatchId();\n\n    if(at_target_){ // 导航任务已经完成，但是没有收到status==2，以导航状态为依据再发一次\n        pathBody[\"status\"] = 2;\n        outputRoot[\"body\"] = pathBody; \n    }\n    else{\n        // 当前点 (currentPoint)\n        pathBody[\"status\"] = 1;\n        Json::Value currentPoint;\n        if(!body[\"x\"].isNull() && !body[\"y\"].isNull() && !body[\"angle\"].isNull()){\n            currentPoint[\"x\"] = body[\"x\"].asDouble();\n            currentPoint[\"y\"] = body[\"y\"].asDouble();\n            currentPoint[\"angle\"] = body[\"angle\"].asDouble();\n        }\n        pathBody[\"currentPoint\"] = currentPoint;\n\n        // 移动路径 (path) \n        Json::Value pathArray(Json::arrayValue);\n        if (!path.isNull() && path.isArray()) {\n            for (const auto& pathPoint : path) {\n                Json::Value pathItem;\n                if (pathPoint.isMember(\"path_x\") && pathPoint.isMember(\"path_y\") && pathPoint.isMember(\"path_angle\")) {\n                    pathItem[\"x\"] = pathPoint[\"path_x\"].asDouble();\n                    pathItem[\"y\"] = pathPoint[\"path_y\"].asDouble();\n                    pathItem[\"angle\"] = pathPoint[\"path_angle\"].asDouble();\n                    pathArray.append(pathItem);\n                }\n            }\n        }\n        pathBody[\"path\"] = pathArray;\n        outputRoot[\"body\"] = pathBody;\n    }\n    \n    Json::FastWriter writer_1;\n    std::string jsonString_1 = writer_1.write(outputRoot);\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"path report cmd : %s\", jsonString_1.c_str());\n\n    // 调用服务并处理响应\n    sendRequestData(jsonString_1);\n    \n\n    if(RobotState::getInstance().getCurrentState() != RobotStateEnum::NAVIGATION) robPathStatusTimer_->cancel();  \n}\n\n// ******************************** 和设备信息相关的 *************************************************************\nvoid RobdogCenter::deepStatusCallback(const homi_speech_interface::msg::ProprietySet::SharedPtr msg) {\n    // RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Received msg form robdog_ctrl Node.cmd is :%d,value is:%d\", msg->cmd, msg->value);\n    switch (msg->cmd) {\n        case POWER_LEVEL_FROM_NODE:\n            RobotState::getInstance().setBatteryLevel(msg->value);\n            RobotState::getInstance().setBatteryChargeStatus(msg->value);\n            break;\n        case WIFI_NAME_FROM_NODE:\n            RobotState::getInstance().setWifiName(msg->exmsg);\n            break;\n        default:\n            break;\n    }\n    RobotState::getInstance().saveConfig();\n    return;\n}\n\nvoid RobdogCenter::utStatusCallback() \n{\n    unitree_go::msg::dds_::SportModeState_ utSportState;\n    unitree_go::msg::dds_::LowState_ utLowState;\n\n    RobotInfoMgr::getInstance().utGetHighState(utSportState);\n    RobotInfoMgr::getInstance().utGetLowState(utLowState);\n\n    RobotState::getInstance().setBatteryLevel(utLowState.bms_state().soc());\n\n    //云深处定义：0未在充电，1充电中 2 充满\n    //宇树定义：正代表充电，负代表放电，与云深处定义不同，需转化\n    if (utLowState.bms_state().current() > 0)\n    {\n        RobotState::getInstance().setBatteryChargeStatus(1);\n    }\n    else\n    {\n        RobotState::getInstance().setBatteryChargeStatus(0);\n    }\n    //RobotState::getInstance().setWifiName(msg->exmsg);\n\n    RobotState::getInstance().saveConfig();\n    \n    return;\n}\n\nstd::string RobdogCenter::get_connect_info_request(const Json::Value &inValue) {\n    Json::Value response = inValue;\n    Json::Value body;\n    Json::Value data;\n    response[\"event\"] = \"connect_info_response\";\n    data[\"status\"] = RobotState::getInstance().getUserConnectStatus();\n    data[\"phone\"] = RobotState::getInstance().getUserPhoneNumber();\n    data[\"lastConnectTs\"] = Json::Int64(RobotState::getInstance().getTimeStamp());\n    // RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"RobotState::getInstance().getTimeStamp=%lld\", RobotState::getInstance().getTimeStamp());\n    body[\"data\"] = data;\n    body[\"code\"] = 0;\n    body[\"msg\"] = \"\";\n    response[\"body\"] = body;\n    // 将 Json::Value 转换成字符串\n    Json::FastWriter writer;\n    std::string jsonString = writer.write(response);\n    return jsonString;\n}\n\nstd::string RobdogCenter::get_robot_properties(const Json::Value &inValue) {\n    // 将整个 JSON 对象转换为字符串\n    Json::FastWriter fastWriter;\n    std::string jsonStr = fastWriter.write(inValue);\n    // 打印输出 JSON 字符串\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"InValue:%s \",jsonStr.c_str());\n    Json::Value response = inValue;\n    const Json::Value &properties = inValue[\"body\"][\"properties\"];\n    Json::Value newBody(Json::objectValue);\n\n    // 使用 promise 和 future 来同步异步数据\n    std::promise<Json::Value> networkStatusPromise;\n    auto networkStatusFuture = networkStatusPromise.get_future();\n\n    for (const auto &property : properties) {\n        std::string propertyName = property.asString();\n\n        if (propertyName == \"networkStatus\") {\n             // 异步获取网络状态\n        // Json::FastWriter writer;\n        // Json::Value requestJs;\n        // requestJs[\"command\"] = \"getNetworkStatus\";\n        // std::string jsonString = writer.write(requestJs);\n        // auto reqMsg = std::make_shared<homi_speech_interface::srv::NetCtrl::Request>();\n        // reqMsg->data = jsonString;\n\n        // auto ret = net_client->wait_for_service(std::chrono::seconds(1));\n        // if (!ret) {\n        //     RCLCPP_WARN(rclcpp::get_logger(\"robdog_control\"), \"Failed to waitForExistence service assistant\");\n        //     networkStatusPromise.set_value(Json::Value()); // 设置空值\n        // } else {\n        //     // auto result = net_client->async_send_request(reqMsg, std::bind(&RobdogCenter::net_srv_callback, this, std::placeholders::_1));\n        //     auto result = net_client->async_send_request(reqMsg, [this, &networkStatusPromise](rclcpp::Client<homi_speech_interface::srv::NetCtrl>::SharedFuture response) {\n        //         Json::Reader reader;\n        //         Json::Value value;\n        //         if (reader.parse(response.get()->result, value)) {\n        //             networkStatusPromise.set_value(value); // 设置解析后的值\n        //         } else {\n        //             networkStatusPromise.set_value(Json::Value()); // 设置空值\n        //         }\n        //     });\n        // }\n        // Json::Value networkStatus = networkStatusFuture.get(); // 获取异步数据\n        // if (!networkStatus.isNull()) {\n        //     newBody[\"properties\"][\"networkStatus\"] = networkStatus;\n        // } else {\n        //     newBody[\"properties\"][\"networkStatus\"][\"wifiState\"] = \"on\";\n        //     newBody[\"properties\"][\"networkStatus\"][\"mobileDataState\"] = \"on\";\n        //     // newBody[\"properties\"][\"networkStatus\"][\"wifiName\"] = \"robot_wifi\";\n        //     newBody[\"properties\"][\"networkStatus\"][\"isWifiConnect\"] = true;\n        // }\n        Json::Reader reader;\n        Json::Value value;\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"),\"g_netctrl_ret is %s\",g_netctrl_ret.c_str());\n        reader.parse(g_netctrl_ret, value);\n        if (!value.isNull()) {\n            newBody[\"properties\"][\"networkStatus\"] = value;\n        } else {\n            newBody[\"properties\"][\"networkStatus\"][\"wifiState\"] = \"off\";\n            newBody[\"properties\"][\"networkStatus\"][\"mobileDataState\"] = \"off\";\n            // newBody[\"properties\"][\"networkStatus\"][\"wifiName\"] = \"robot_wifi\";\n            newBody[\"properties\"][\"networkStatus\"][\"isWifiConnect\"] = \"false\";\n        }\n    } else if (propertyName == \"battery\") {\n            // newBody[\"properties\"][propertyName][\"power\"] = RobotState::getInstance().getBatteryLevel();\n            // newBody[\"properties\"][propertyName][\"status\"] = RobotState::getInstance().getBatteryChargeStatus();\n             Json::Value batteryStatus(Json::objectValue);\n            batteryStatus[\"power\"] = RobotInfoMgr::getInstance().getBatteryLevel();\n            batteryStatus[\"status\"] = (RobotInfoMgr::getInstance().getIsCharging())?1:0;\n            newBody[\"properties\"][propertyName] = batteryStatus;\n        } else if (propertyName == \"intelligent\") {\n            newBody[\"properties\"][propertyName] = RobotState::getInstance().getIntelligentSwitch();\n        } else if (propertyName == \"flashlight\") {\n            newBody[\"properties\"][propertyName][\"status\"] = RobotState::getInstance().getFlashStatus();\n            newBody[\"properties\"][propertyName][\"brightness\"] = RobotState::getInstance().getFlashBrightness();\n        } else if (propertyName == \"volume\") {\n            newBody[\"properties\"][propertyName] = RobotState::getInstance().getVolume();\n        } else if (propertyName == \"connect_info\") {\n            newBody[\"properties\"][propertyName][\"changeType\"] = RobotState::getInstance().getUserConnectStatus();\n            newBody[\"properties\"][propertyName][\"phone\"] = RobotState::getInstance().getUserPhoneNumber();\n        } else if (propertyName == \"posture\") {\n            Json::Value posture(Json::objectValue); //状态，0站立，1趴 2 坐\n            int status = 0;\n            if(RobotInfoMgr::getInstance().getRobotStatus() == ROBDOG_STATUS_FORCE_CTRL_LEVEL_LOWSPPEED_GAIT){\n                status = 0;  // 站立\n            } else if(RobotInfoMgr::getInstance().getRobotStatus() == ROBDOG_STATUS_GETDOWN){\n                status = 1;  // 趴下 \n            } else if(RobotInfoMgr::getInstance().getRobotStatus() == ROBDOG_STATUS_SITDOWN){\n                status = 2;  // 坐下\n            }\n            posture[\"status\"] = status;\n            RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"posture send to platform is %d\",status);\n            newBody[\"properties\"][propertyName] = posture;\n        } else if (propertyName == \"followMe\") {\n            newBody[\"properties\"][propertyName][\"status\"] = RobotState::getInstance().getFollowMeStatus();\n        } else if (propertyName == \"amapLocation\"){\n            newBody[\"properties\"][propertyName][\"lon\"] = rtkMsg.longitude;\n            newBody[\"properties\"][propertyName][\"lat\"] = rtkMsg.latitude;\n        }\n        else if (propertyName == \"temperature\") \n        {\n            char msg[128] = \"\";\n\t\t\tstd::string CPUTempStatus = RobotInfoMgr::getInstance().getCPUTempStatus();\n\t\t\tstd::string TempStatus = RobotInfoMgr::getInstance().getTempStatus();\n\n\t\t\tif (0 != strcmp(\"Normal\", CPUTempStatus.c_str()))\n        \t{\n\t            newBody[\"properties\"][propertyName][\"status\"] = CPUTempStatus;\n\t            if (0 == strcmp(\"TooHigh\", CPUTempStatus.c_str()))\n\t            {\n\t                snprintf(msg, sizeof(msg), \"机器狗CPU温度过高，已停止运行!请等温度恢复正常后，再尝试作业。\");\n\t            }\n\t\t\t\telse if (0 == strcmp(\"TooLow\", CPUTempStatus.c_str()))\n\t\t\t\t{\n\t\t\t\t\tsnprintf(msg, sizeof(msg), \"机器狗CPU温度过低，已停止运行!请等温度恢复正常后，再尝试作业。\");\n\t\t\t\t}\n        \t}\n\t\t\telse if (0 != strcmp(\"Normal\", TempStatus.c_str()))\n\t\t\t{\n\t            newBody[\"properties\"][propertyName][\"status\"] = TempStatus;\n\t\t\t\tif (0 == strcmp(\"TooHigh\", TempStatus.c_str()))\n\t            {\n\t                snprintf(msg, sizeof(msg), \"机器狗关节温度过高，已停止运行!请等温度恢复正常后，再尝试作业。\");\n\t            }\n\t\t\t\telse if (0 == strcmp(\"TooLow\", TempStatus.c_str()))\n\t\t\t\t{\n\t\t\t\t\tsnprintf(msg, sizeof(msg), \"机器狗关节温度过低，已停止运行!请等温度恢复正常后，再尝试作业。\");\n\t\t\t\t}\n\t\t\t}\n            else\n            {\n                newBody[\"properties\"][propertyName][\"status\"] = \"Normal\";\n            }\n\n            newBody[\"properties\"][propertyName][\"msg\"] = msg;\n        }\n\t\telse if (propertyName == \"speed\") \n\t\t{\n\t\t\tnewBody[\"properties\"][propertyName][\"value\"] = RobotInfoMgr::getInstance().getSpeedLevel();\n\t\t}\n    }\n    response[\"body\"] = newBody;\n    // 将 Json::Value 转换成字符串\n    Json::FastWriter writer;\n    std::string jsonString = writer.write(response);\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Request to platform is %s\",jsonString.c_str());\n    return jsonString;\n}\n\nstd::string RobdogCenter::get_hardware_state(const Json::Value &inValue) {\n    // 将整个 JSON 对象转换为字符串\n    Json::FastWriter fastWriter;\n    std::string jsonStr = fastWriter.write(inValue);\n    // 打印输出 JSON 字符串\n    RCLCPP_DEBUG(rclcpp::get_logger(\"robdog_control\"), \"InValue:%s \",jsonStr.c_str());\n    Json::Value response = inValue;\n    const Json::Value &hardwareList = inValue[\"body\"][\"hardwareList\"];\n    Json::Value newBody(Json::objectValue);\n\n    // 使用 promise 和 future 来同步异步数据\n    std::promise<Json::Value> networkStatusPromise;\n    auto networkStatusFuture = networkStatusPromise.get_future();\n\n    for (const auto &property : hardwareList) {\n        std::string propertyName = property.asString();\n\n        if (propertyName == \"realSense\"){\n#ifndef UNITREE\n            newBody[propertyName][\"stateCode\"] = RobotState::getInstance().getRealSenseState();\n#else\n            newBody[propertyName][\"stateCode\"] = 2;\n#endif\n            continue;\n        } else if (propertyName == \"lidar\"){\n            newBody[propertyName][\"stateCode\"] = RobotState::getInstance().getLidarState();\n#ifdef UNITREE\n            newBody[propertyName][\"dirtyPercent\"] = RobotState::getInstance().getLidarDirtyPercent();\n#endif\n            continue;\n        } else if (propertyName == \"battery\"){\n            newBody[\"battery\"][\"power\"] = RobotInfoMgr::getInstance().getBatteryLevel();\n            newBody[\"battery\"][\"status\"] = (RobotInfoMgr::getInstance().getIsCharging())?1:0;\n        } else if (propertyName == \"networkStatus\"){\n            getNetworkInfo(newBody);\n        } else if (propertyName == \"temperature\"){\n            newBody[\"temerature\"] = RobotState::getInstance().getBoardTempStatus();\n        } else if (propertyName == \"hardwareusage\"){\n            newBody[\"temerature\"] = RobotState::getInstance().getBoardTempStatus();\n        } else if (propertyName == \"processstatus\"){\n            newBody[\"process\"] = RobotState::getInstance().getProcessStatus();\n        } else if (propertyName == \"audiodevicestatus\"){\n            newBody[\"AudioDevice\"][\"stateCode\"] = RobotState::getInstance().getAudioStatus();\n        } else if (propertyName == \"uwb\"){\n            newBody[\"uwb\"][\"statusCode\"] = RobotState::getInstance().getUWBStatus();\n        } else if (propertyName == \"bluetooth\"){\n            newBody[\"bluetooth\"][\"status\"] = RobotState::getInstance().getBluetoothStatus();\n        } else if (propertyName == \"rtk\"){\n            newBody[\"rtk\"][\"stateCode\"] = RobotState::getInstance().getRTKState();\n        } else if (propertyName == \"mic\") {\n            newBody[\"mic\"][\"stateCode\"] = RobotState::getInstance().getMicStatus();\n        } else if (propertyName == \"camera\") {\n            newBody[\"camera\"][\"forehead\"][\"stateCode\"] = RobotState::getInstance().getForeheadCameraStatus();\n            newBody[\"camera\"][\"bowtie\"][\"stateCode\"] = RobotState::getInstance().getTieCameraStatus();\n        } else if (propertyName == \"hardwareStatus\") {\n            auto jointTemp = RobotInfoMgr::getInstance().getRobotTemperature();\n            std::string jointPropertyName = \"\";\n            // Joint temperature\n            for(int i = 0;i< MAX_JOINT_NUM;i++) {\n                jointPropertyName = jointNames[i];\n                newBody[propertyName][jointPropertyName][\"temperature\"] = (double)jointTemp[i];\n                newBody[propertyName][jointPropertyName][\"stateCode\"] = (double)jointTemp[i]>TEMP_THREADHOLD ? 1 : 0;\n            }\n        }\n    }\n    response[\"response\"] = false;\n    response[\"body\"] = newBody;\n    // 将 Json::Value 转换成字符串\n    Json::FastWriter writer;\n    std::string jsonString = writer.write(response);\n    RCLCPP_DEBUG(rclcpp::get_logger(\"robdog_control\"), \"Request to platform is %s\",jsonString.c_str());\n    return jsonString;\n}\n\nvoid RobdogCenter::getNetworkInfo(Json::Value& body) {\n\n    Json::Reader reader;\n    Json::Value value;\n    reader.parse(g_netctrl_ret, value);\n    if (value.isNull())\n    {\n        body[\"networkStatus\"][\"wifiState\"] = Json::Value();\n        body[\"networkStatus\"][\"mobileDataState\"] = Json::Value();\n        body[\"networkStatus\"][\"wifiName\"] = Json::Value();\n        body[\"networkStatus\"][\"isWifiConnect\"] = Json::Value();\n    }\n    else {\n        body[\"networkStatus\"][\"wifiState\"] = value[\"wifiState\"];\n        body[\"networkStatus\"][\"mobileDataState\"] = value[\"mobileDataState\"];\n        body[\"networkStatus\"][\"wifiName\"] = value[\"wifiName\"];\n        body[\"networkStatus\"][\"isWifiConnect\"] = value[\"isWifiConnect\"];\n    }\n}\n\nvoid RobdogCenter::setProperties(const Json::Value &request) {\n    const Json::Value &properties = request[\"body\"][\"properties\"];\n    if (properties.isMember(\"flashlight\")) {\n        const Json::Value &flahlight = properties[\"flashlight\"];\n        if (flahlight.isMember(\"status\"))\n            RobotState::getInstance().setFlashStatus(flahlight[\"status\"].asString());\n        if (flahlight.isMember(\"brightness\"))\n            RobotState::getInstance().setFlashBrightness(flahlight[\"brightness\"].asInt());\n        // int valueStatus = (RobotState::getInstance().getFlashStatus() == \"on\") ? 1 : 0;\n        // publishStatusCtrl(DEEP_CMD_FLASHLIGHT, valueStatus, RobotState::getInstance().getFlashBrightness());\n        //改为topic发布通知：\n        if (RobotState::getInstance().getFlashStatus() == \"on\"){\n\n            auto message = std_msgs::msg::String();\n            message.data = \"light_open\";\n            flashlight_control_pub_->publish(message);\n\n        }\n        if (RobotState::getInstance().getFlashStatus() == \"off\"){\n\n            auto message = std_msgs::msg::String();\n            message.data = \"light_close\";\n            flashlight_control_pub_->publish(message);\n\n        }\n\n\n    }\n    if (properties.isMember(\"volume\") || properties.isMember(\"changeVolume\")) {\n        int volume = -1;\n        if(properties.isMember(\"changeVolume\")) {\n            int oldVolume = RobotState::getInstance().getVolume();\n            volume = properties[\"changeVolume\"].asInt() + oldVolume;\n            if(volume > 100) {\n                volume = 100;\n            } else if(volume < 0) {\n                volume = 0;\n            }\n            if(volume == oldVolume) {\n                return;\n            }\n            RobotState::getInstance().setVolume(volume);\n        } else {\n            volume = properties[\"volume\"].asInt();\n            RobotState::getInstance().setVolume(volume);\n        }\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Volume set to %d\", volume);\n        if (volume < 0 || volume > 100) {\n            RCLCPP_ERROR(rclcpp::get_logger(\"robdog_control\"), \"Volume must be between 0 and 100.\");\n            return;\n        }\n        // FILE* fp;\n        // char buffer[50]={0};\n        // fp = popen(R\"(aplay -l | grep \"USB Audio Device\" -A 2 | grep \"card\" | awk '{print $2}' | tr -d ':')\", \"r\");\n        // if(!fp) {\n        //     RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"ThirdpartyAudioDevice Search audio device failed\");\n        //     return;\n        // }\n        // if (fgets(buffer, sizeof(buffer), fp) == nullptr) {\n        //     RCLCPP_ERROR(rclcpp::get_logger(\"robdog_control\"), \"No audio device found.\");\n        // }\n        // if (pclose(fp) == -1) {\n        //     RCLCPP_ERROR(rclcpp::get_logger(\"robdog_control\"), \"Error closing the pipe.\");\n        // }\n        // std::string cardNumber(buffer);\n        // cardNumber.erase(std::remove(cardNumber.begin(), cardNumber.end(), '\\n'), cardNumber.end());\n        // cardNumber.erase(std::remove(cardNumber.begin(), cardNumber.end(), ' '), cardNumber.end());\n        // std::string command = \"amixer -c   \" + (cardNumber) + \"   sset PCM \" + std::to_string(volume) + \"%\";\n        // 宇树狗默认声卡为0,并且音量调节为 50% ~ 100% ，所以需要映射\n        // 将音量设置为其自身的一半再加50\n        volume = 60 + (volume * 2 / 5);\n        // 构建系统命令以设置PCM音量\n        std::string command = \"amixer -c 0 sset PCM \" + std::to_string(volume) + \"%\";\n        // 执行系统命令\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"CMD: %s \", command.c_str());\n        int result = std::system(command.c_str());\n        // 检查系统命令是否执行成功\n        if (result != 0) {\n            // 如果命令返回非零值，记录错误信息\n            RCLCPP_ERROR(rclcpp::get_logger(\"robdog_control\"), \"Error setting PCM volume. Command returned: %d\", result);\n        }\n    }\n    if (properties.isMember(\"networkStatus\")) {\n        const Json::Value &networkStatus = properties[\"networkStatus\"];\n        Json::FastWriter writer;\n        Json::Value requestJs;\n        requestJs[\"command\"] = \"setNetworkStatus\";\n        requestJs[\"networkStatus\"] = networkStatus;\n\n        std::string jsonString = writer.write(requestJs);\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Request to net_ctrl_srv is %s\", jsonString.c_str());\n        auto reqMsg= std::make_shared<homi_speech_interface::srv::NetCtrl::Request>();\n        reqMsg->data = jsonString;\n\n        auto ret = net_client->wait_for_service(std::chrono::seconds(1));\n        if(ret==false)\n        {\n            RCLCPP_WARN(rclcpp::get_logger(\"robdog_control\"),\"Failed to waitForExistence service assistant\");\n        }\n        auto result = net_client->async_send_request(reqMsg, std::bind(&RobdogCenter::net_srv_callback, this, std::placeholders::_1));   \n        \n        if (networkStatus.isMember(\"wifiState\")){\n            RobotState::getInstance().setWifiSwitch(networkStatus[\"wifiState\"].asString());\n        }\n        if (networkStatus.isMember(\"mobileDataState\")){\n\n            RobotState::getInstance().setMobileDataSwitch(networkStatus[\"mobileDataState\"].asString());\n        }\n        int value = (RobotState::getInstance().getWifiSwitch() == \"on\") ? 1 : 0;\n        int exvalue=(RobotState::getInstance().getMobileDataSwitch() == \"on\") ? 1 : 0;\n    }\n    if (properties.isMember(\"intelligent\")) { // 储存平台写入的多地形自适应状态\n        // RobotState::getInstance().setIntelligentSwitch(properties[\"intelligent\"].asString());  // 改到在发布UDP消息的位置保存到xml\n        int value = (properties[\"intelligent\"].asString() == \"on\") ? 1 : 0;\n        // publishStatusCtrl(DEEP_CMD_AI_MOTION, value, 0);\n        if(value && RobotInfoMgr::getInstance().getRobotStatus()==ROBDOG_STATUS_FORCE_CTRL_LEVEL_LOWSPPEED_GAIT){ // 做一个状态判断，必须在站立的情况下才能发送开启AI模式\n            // 向机器狗发送开启多地形自适应的指令（越障+RL）\n            homi_speech_interface::msg::RobdogAction msg;// 创建消息\n            msg.actiontype = \"gaitControl\";\n            msg.actionargument = \"obstacleCross\";\n            publishAction(std::make_shared<homi_speech_interface::msg::RobdogAction>(msg));\n        }\n        else{\n            // 向机器狗发送关闭多地形自适应的指令\n            homi_speech_interface::msg::RobdogAction msg;// 创建消息\n            msg.actiontype = \"gaitControl\";\n            msg.actionargument = \"exit\";\n            publishAction(std::make_shared<homi_speech_interface::msg::RobdogAction>(msg));\n        }\n    }\n    if (properties.isMember(\"rtkAccount\")) {\n        std::string rtkAccount = properties[\"rtkAccount\"].asString();\n        std::string rtkPassword = properties[\"rtkPass\"].asString();\n\n        //publish\n        std::cout << \"rtkAccount: \" << rtkAccount.c_str() << \"rtkPassword:\" <<  rtkPassword.c_str() << std::endl;\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"rtkAccount: %s\", rtkAccount.c_str());\n        auto requestSetRtkInfo =\n            std::make_shared<homi_speech_interface::srv::NtripAccount_Request>();\n        requestSetRtkInfo->user = rtkAccount.c_str();\n        requestSetRtkInfo->pwd = rtkPassword.c_str();\n\n        ntrip_account_client_->async_send_request(requestSetRtkInfo);\n    }\n    RobotState::getInstance().saveConfig();\n}\n\n// 发送建图初始化响应\nvoid RobdogCenter::sendMapCompleteResponse(int code,long mapId) {\n    Json::Value response;\n    response[\"deviceId\"] = RobotState::getInstance().getDeviceId();\n    response[\"domain\"] = \"APP_DEVICE_INTERACTION\";\n    response[\"event\"] = \"device_map_save_response\";\n    response[\"eventId\"] = getEventId();\n    response[\"requestId\"] = \"requestId\"+to_string(base::homiUtils::getCurrentTimeStamp());\n    response[\"seq\"] = to_string(base::homiUtils::getCurrentTimeStamp());\n\n    Json::Value body;\n    body[\"code\"] = code;\n    body[\"mapId\"] = Json::Int64(mapId);\n    body[\"msg\"] = getDescription(code);\n    response[\"body\"] = body;\n\n    Json::FastWriter writer;\n    std::string jsonString = writer.write(response);\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"device_map_save_response: %s\", jsonString.c_str());\n    sendRequestData(jsonString);\n    // sendNavigationAction(1, RobotState::getInstance().getMapId());//建图结束开启导航\n}\n\nvoid RobdogCenter::sendMapRepositionResponse(int code,const Json::Value &jBody) {\n    Json::Value response;\n    response[\"deviceId\"] = RobotState::getInstance().getDeviceId();\n    response[\"domain\"] = \"APP_DEVICE_INTERACTION\";\n    if(repositioning){\n        response[\"event\"] = \"map_reposition\";\n    }\n    else if(repositioning_manual){\n        response[\"event\"] = \"map_reposition_manual\";\n    }\n    response[\"eventId\"] = RobotState::getInstance().getPathEventId();\n    response[\"requestId\"] = \"requestId\"+to_string(base::homiUtils::getCurrentTimeStamp());\n    response[\"seq\"] = to_string(base::homiUtils::getCurrentTimeStamp());\n    response[\"response\"] = \"false\";\n    \n    Json::Value newBody;\n    // Json::FastWriter writer1;\n    // std::string jsonString1 = writer1.write(jBody);\n    // RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"MapReposition msgs!!!!: %s\", jsonString1.c_str());\n\n\n    if(code == NAVIGATION_CODE_RELOCALIZATION_FINISHED){\n        code = 0;//如果重定位成功，响应code为0\n    }\n    newBody[\"code\"] = code;\n    newBody[\"msg\"] = getDescription(code);\n    if (!jBody[\"currentPoint\"].isNull()) {\n        newBody[\"currentPoint\"] = jBody[\"currentPoint\"];\n    } \n    else{\n        newBody[\"currentPoint\"] = RobotState::getInstance().getCurRobotPose();\n    }\n    newBody[\"mapId\"] = RobotState::getInstance().getMapId();\n    response[\"body\"] = newBody;\n\n    Json::FastWriter writer;\n    std::string jsonString = writer.write(response);\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"sendMapRepositionResponse: %s\", jsonString.c_str());\n    sendRequestData(jsonString);\n}\n\nvoid RobdogCenter::deviceAlarmReport(int code) {\n    // ---------------------------------- 告警上报 ------------------------------------------\n    Json::Value alarmReport;\n    Json::Value alarmBody;\n    alarmReport[\"deviceId\"] = RobotState::getInstance().getDeviceId();\n    alarmReport[\"domain\"] = \"DEVICE_ALARM\";\n    alarmReport[\"event\"] = \"device_alarm_report\";\n    alarmReport[\"eventId\"] = \"robdog_alarm_\" + to_string(base::homiUtils::getCurrentTimeStamp());\n    alarmReport[\"requestId\"] = \"requestId\" + to_string(base::homiUtils::getCurrentTimeStamp());\n    alarmReport[\"seq\"] = std::to_string(base::homiUtils::getCurrentTimeStamp());\n    \n    // alarmBody[\"notifystrategy\"] = Json::arrayValue;\n    // Json::Value notifyRole1;\n    // notifyRole1[\"notifyRole\"] = 1;\n    // notifyRole1[\"notifyway\"] = Json::arrayValue;\n    // // notifyRole1[\"notifyway\"].append(1);\n    // notifyRole1[\"notifyway\"].append(3);\n    // alarmBody[\"notifystrategy\"].append(notifyRole1);\n\n    //平台需求，暂时只上报异常情况，1100-1205为建图异常，2100-2208为导航异常\n    //设置告警类别、告警模块、告警级别和告警描述\n      // 定义正则表达式，匹配以21或22开头的整数\n    std::regex pattern(R\"(^(221|222)\\d*$)\");\n    if (1100 <= code && code <= 1205)\n    {\n        alarmBody[\"alarmType\"] = \"建图告警\"; \n        alarmBody[\"launcherModel\"] = \"map_status\";\n        // code = code + base_map;\n        alarmBody[\"alarmCode\"] = code;\n        alarmBody[\"alarmName\"] = getCodeName(code); \n        alarmBody[\"alarmLevel\"] = 4;\n        alarmBody[\"alarmDesc\"] = getDescription(code); \n    }\n    else if (std::regex_match(std::to_string(code), pattern))\n    {\n        // code = code + base_navigation;\n        auto now = std::chrono::steady_clock::now();\n        auto duration = std::chrono::duration_cast<std::chrono::seconds>(now - lastCancelMovementTime).count();\n\n        // 打印时间间隔\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Duration since last cancel movement: %ld seconds\", duration);\n        if (duration >= 1 && RobotState::getInstance().getCurrentState() == RobotStateEnum::NAVIGATION){\n            handleCancelMovement();\n            lastCancelMovementTime = now; // 更新上次调用时间\n            std::thread t_audio(&RobdogCtrlNode::playAudio,node_,node_->getResourcePath(\"audio/nav/unreachable.wav\"));\n            t_audio.detach();\n            RobotState::getInstance().setCurrentState(RobotStateEnum::NORMAL); // 取消导航状态\n            robPathStatusTimer_->cancel(); \n            RobotState::getInstance().setMoveTaskPath(Json::Value()); // 清空路径\n        }\n        else return;\n        \n        alarmBody[\"alarmType\"] = \"导航告警\"; \n        alarmBody[\"launcherModel\"] = \"navigation_status\"; \n        alarmBody[\"alarmCode\"] = code;\n        alarmBody[\"alarmName\"] = getCodeName(code); \n        alarmBody[\"alarmLevel\"] = 4;\n        alarmBody[\"alarmDesc\"] = getDescription(code); \n    }\n    else\n    {\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"code = %d,code is not in the range\",code);\n        return;\n    }\n\n    alarmReport[\"body\"] = alarmBody;\n    Json::FastWriter writer;\n    std::string alarmJsonString = writer.write(alarmReport);\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"alarmJsonString : %s\", alarmJsonString.c_str());\n    sendRequestData(alarmJsonString);\n}\n\nvoid RobdogCenter::processRepositioningResult(int taskStatusCode, const Json::Value &jBody) {\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Starting processRepositioningResult at %ld\", base::homiUtils::getCurrentTimeStamp());\n    // 使用 unique_lock 锁定 resultMutex\n    std::unique_lock<std::mutex> lock(resultMutex);\n    \n    if ((repositioning || repositioning_manual) && taskStatusCode == NAVIGATION_CODE_RELOCALIZATION_FINISHED) {\n        repositioningResult.store(true); // 设置重定位成功\n        std::thread t_audio(&RobdogCtrlNode::playAudio,node_,node_->getResourcePath(\"audio/nav/reposition_success.wav\"));\n        t_audio.detach();\n        sendMapRepositionResponse(taskStatusCode,jBody);\n        repositioning = false;\n        repositioning_manual = false;\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"repositioning is true \");\n    } else if ((repositioning || repositioning_manual) && \n               (taskStatusCode == NAVIGATION_CODE_INVALID_COMMAND_OUTSIDE_NAVIGATION || \n                taskStatusCode == NAVIGATION_CODE_MAP_ID_MISMATCH || \n                taskStatusCode == NAVIGATION_CODE_COMMAND_FAILED || \n                taskStatusCode == NAVIGATION_CODE_RELOCALIZATION_ERROR_LARGE||\n                taskStatusCode == NAVIGATION_CODE_TIME_OUT)) {\n        std::thread t_audio(&RobdogCtrlNode::playAudio,node_,node_->getResourcePath(\"audio/nav/reposition_failed.wav\"));\n        t_audio.detach();\n        repositioningResult.store(false); // 设置重定位失败\n        sendMapRepositionResponse(taskStatusCode,jBody);\n        sendCommandToUSLAM(\"localization/stop\");\n        repositioning = false;\n        repositioning_manual = false;\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"repositioning is false \");\n    }\n\n    resultCV.notify_all(); // 通知等待的线程\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"ending processRepositioningResult at %ld\", base::homiUtils::getCurrentTimeStamp());\n}\n\n// ************************************************ 和感知主机相关的操作(导航) ************************************************\n//处理webSocket消息（按照topic名称处理）\nvoid RobdogCenter::parseWsActionMsg(Json::Value& value) {\n    string action = value[\"action\"].asString();\n    //感知主机下发的机器人移动的角速度和线速度\n    if (action == \"motionArcWithObstacles\") {\n        Json::Value params = value[\"params\"];\n        double lineSpeed = params[\"lineSpeed\"].asDouble();\n        double angularSpeed = params[\"angularSpeed\"].asDouble();\n        geometry_msgs::msg::Twist twist;\n        twist.linear.x = lineSpeed;\n        twist.angular.z = angularSpeed;\n        publishVelocity(std::make_shared<geometry_msgs::msg::Twist>(twist));\n    }\n    else if (action == \"navigation_position\") { // 感知主机需要上报给平台的导航路径信息\n        Json::Value params = value[\"params\"];\n        if (params.isMember(\"mode\")) {\n            if (params[\"mode\"].isInt()) {\n                counterFreq++;\n                if (counterFreq >= RAW_FREQUENCY||params[\"status\"].asInt()==FINISH){\n                    handleStripPathReport(params);\n                    counterFreq=0;\n                }\n                return;\n            }\n        }\n        if (!params[\"x\"].isNull()) {\n            Navigation_node = true; // 有路径上报，说明当前导航节点是开启的\n            // RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Navigation_node is active!!!!!!!!\");\n        }\n        RobotState::getInstance().setCurRobotPose(params); // 里面就包含了点位和路径\n        // 接受感知主机上报的导航位置以及路径规划信息\n        \n    }\n    else if (action == \"navigation_path\") { // 感知主机需要上报给平台的导航路径信息\n\n        Json::Value params = value[\"params\"];\n        if (!params.isMember(\"path\")) {\n            RCLCPP_ERROR(rclcpp::get_logger(\"robdog_control\"), \"No path data received\");\n            return;\n        }\n        Json::Value path = params[\"path\"];\n        if (path.isNull() || !path.isArray()) {\n            RCLCPP_ERROR(rclcpp::get_logger(\"robdog_control\"), \"Invalid path data received\");\n            return;\n        }\n        RobotState::getInstance().setMoveTaskPath(path); // 里面就包含了点位和路径\n        \n    }\n    else if (action == \"playTts\"){\n        std::string textPlay= value[\"params\"][\"text\"].asString();\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"send String To Brocast,content is %s\",textPlay.c_str());   \n        sendStringToBrocast(textPlay);\n    }\n    else if (action == \"console_status\") {\n        int consoleStatusCode = value[\"params\"][\"code\"].asInt();\n        if(consoleStatusCode == 100 && !navHeartbeat){\n            std::string current_mapid = RobotState::getInstance().getMapId();\n            if (current_mapid != \"100\") {\n                RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"%d---current_mapid: %s\", __LINE__, current_mapid.c_str());\n                // checkNvidiaServiceStatus(true,current_mapid);\n                navHeartbeat = true;\n            }\n        }\n    }\n    else if (action == \"task_status\") {   // 和导航任务状态相关的部分\n        Json::Value params = value[\"params\"];\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"params: %s\", Json::FastWriter().write(params).c_str());\n\n        if (!params.isMember(\"msg\") || !params[\"msg\"].isObject()) {\n            RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Invalid params format\");\n            return;\n        }\n\n        int type = params[\"type\"].asInt();\n        Json::Value msgValue = params[\"msg\"];\n        std::string uid = msgValue.isMember(\"uid\") ? msgValue[\"uid\"].asString() : \"\";\n        std::string taskStatusMsg = msgValue.isMember(\"msg\") ? msgValue[\"msg\"].asString() : \"\";\n        int taskStatusCode = params[\"code\"].asInt();\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"task_status code = %d\", taskStatusCode);\n\n        handleTaskStatusCode(taskStatusCode, msgValue);\n    }\n    else if (action == \"pathPlanning\")\n    {\n        Json::StreamWriterBuilder writer;\n        std::string strParams = Json::writeString(writer, value[\"params\"]);\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"pathPlanning: %s\", strParams.c_str());\n        actionNavigationResponse(strParams);\n    }\n    else if(action == \"robotPose\")\n    {\n        actionRobdogPose(value[\"params\"]);\n    }\n}\n\nvoid RobdogCenter::handleTaskStatusCode(int taskStatusCode, const Json::Value& msgValue) {\n    if (11000 <= taskStatusCode && taskStatusCode <= 11205) {\n        deviceAlarmReport(taskStatusCode);\n        // taskStatusCode += base_map;\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"%d------------MAP_taskStatusCode = %d\", __LINE__,taskStatusCode);\n        RobotStateEnum currentState = RobotState::getInstance().getCurrentState();\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"CurrentState: %d\", static_cast<int>(currentState));\n        if(RobotState::getInstance().getCurrentState() == RobotStateEnum::MAPPING){\n            handleMapCompleteStatus(taskStatusCode, msgValue);\n            RobotState::getInstance().setCurrentState(RobotStateEnum::NORMAL); \n        }\n        if(RobotState::getInstance().getCurrentState() == RobotStateEnum::MAP_INITIALIZING && taskStatusCode != MAP_CODE_CANCELLED && taskStatusCode != MAP_CODE_STOPPED){\n            handleMapInitialStatus(taskStatusCode, msgValue);\n        }\n        \n    } else if (22000 <= taskStatusCode && taskStatusCode <= 22209) {\n        deviceAlarmReport(taskStatusCode);\n        // taskStatusCode += base_navigation;\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"%d------------NAV_taskStatusCode = %d\", __LINE__,taskStatusCode);\n        // handleNavBeforeRepositioning(taskStatusCode, msgValue);//宇树不做重定位前检查导航是否打开与开错的情况\n        handleNavigationStatus(taskStatusCode,msgValue);\n    } else if (55000 <= taskStatusCode && taskStatusCode <= 55211) {\n        // taskStatusCode += base_charging;\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"%d------------CHARGE_taskStatusCode = %d\", __LINE__,taskStatusCode);\n        if(isChargeMarking){\n            handleChargeMarkStatus(taskStatusCode, msgValue);\n        }\n        else if(is_charging){\n            handleChargeActionStatus(taskStatusCode, msgValue);\n        }\n    } else {\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"taskStatusCode = %d, not in the range\", taskStatusCode);\n    }\n}\n\nvoid RobdogCenter::handleMapInitialStatus(int taskStatusCode, const Json::Value& msgValue) {\n    auto duration = std::chrono::steady_clock::now() - response_start_time;\n    auto duration_ms = std::chrono::duration_cast<std::chrono::milliseconds>(duration);\n\n    // 打印调试信息\n    RCLCPP_INFO(\n        rclcpp::get_logger(\"robdog_control\"), \n        \"Duration: %ld ms, waiting_for_response: %s\", \n        duration_ms.count(), \n        waiting_for_response ? \"true\" : \"false\"\n    );\n    if (waiting_for_response && duration <= timeout_duration) {\n        if (taskStatusCode == MAP_CODE_STARTED) {\n            sendMapInitialResponse(0, \"\");\n            RobotState::getInstance().setCurrentState(RobotStateEnum::MAPPING);\n        } else if (isInitializationFailure(taskStatusCode)) {\n            sendMapInitialResponse(taskStatusCode, getDescription(taskStatusCode));\n            RobotState::getInstance().setCurrentState(RobotStateEnum::NORMAL);\n        }\n        waiting_for_response = false;\n    }\n}\n\nbool RobdogCenter::isInitializationFailure(int taskStatusCode) {\n    return taskStatusCode == ERROR_CODE_MAP_UPLOAD_FAILED ||\n        taskStatusCode == ERROR_CODE_NEW_MAP_COMMAND_DURING_BUILD ||\n        taskStatusCode == ERROR_CODE_NAVIGATION_COMMAND_DURING_BUILD ||\n        taskStatusCode == ERROR_CODE_NODE_CRASHED;\n}\n\nvoid RobdogCenter::handleMapCompleteStatus(int taskStatusCode, const Json::Value& msgValue) {\n    if (msgValue.isMember(\"mapId\")) {\n        std::string mapIdStr = msgValue[\"mapId\"].asString();\n        long mapId = -1; // 设置一个默认值，表示无效的 mapId\n        if (std::regex_match(mapIdStr, std::regex(\"^[0-9]+$\"))) {\n            mapId = std::stoll(mapIdStr);\n            RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Converted mapId: %ld\", mapId);\n        } else {\n            RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Invalid mapId format: %s\", mapIdStr.c_str());\n        }\n        if (taskStatusCode == MAP_CODE_STOPPED) {\n            taskStatusCode = 0;\n            if (mapId != -1) { // 确保 mapId 是有效的\n                // sendCommandToUSLAM(\"common/set_map_id/\" + mapIdStr);\n                sendMapCompleteResponse(taskStatusCode, mapId);\n                // std::this_thread::sleep_for(std::chrono::milliseconds(100));\n                // sendCommandToUSLAM(\"common/get_map_id\");\n            }\n        } else if (taskStatusCode == ERROR_CODE_MAP_SAVE_FAILED || taskStatusCode == ERROR_CODE_MAP_ID_MISMATCH || taskStatusCode == ERROR_CODE_MAP_UPLOAD_FAILED) {\n            sendMapCompleteResponse(taskStatusCode, mapId);           \n        }\n    } else {\n        RCLCPP_WARN(rclcpp::get_logger(\"robdog_control\"), \"Missing mapId in MapCompleteStatus\");\n    }\n}\n\nbool RobdogCenter::isChargeMarkFailure(int taskStatusCode) {\n    return taskStatusCode == CHARGING_ERROR_INVALID_COMMAND_FORMAT ||\n        taskStatusCode == CHARGING_ERROR_NAVIGATION_NODE_NOT_STARTED ||\n        taskStatusCode == CHARGING_ERROR_POINT_FUNCTION_FAILED ||\n        taskStatusCode == CHARGING_ERROR_POINT_ENTRY_FAILED_NO_QRCODE ||\n        taskStatusCode == CHARGING_ERROR_POINT_ENTRY_FAILED_ALGORITHM_ERROR ||\n        taskStatusCode == CHARGING_ERROR_CHARGING_FAILED_ALIGNMENT_TIMEOUT ||\n        taskStatusCode == CHARGING_ERROR_CHARGING_FUNCTION_FAILED_NO_POLE;\n}\n\nvoid RobdogCenter::handleChargeMarkStatus(int taskStatusCode, const Json::Value& msgValue) {\n    if (taskStatusCode == CHARGING_CODE_POINT_ENTRY_COMPLETED) {\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"handleChargeMarkStatus\");\n        Json::Value point = RobotState::getInstance().getCurRobotPose();\n        Json::Value arrayPoint(Json::arrayValue);\n        arrayPoint.append(point);\n\n        Json::FastWriter writer;\n        std::string jsonStr = writer.write(arrayPoint);\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"ChargeMark point is %s\", jsonStr.c_str());\n\n        sendChargeMarkResponse(0, \"\");\n        updateMapPoints(\"batteryCharging\", arrayPoint);\n        isChargeMarking = false;\n    } else if (isChargeMarkFailure(taskStatusCode)) {\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"ChargeMarkFailure\");\n        sendChargeMarkResponse(1, getDescription(taskStatusCode));\n        isChargeMarking = false;\n    }\n}\n\nvoid RobdogCenter::handleChargeActionStatus(int taskStatusCode, const Json::Value& msgValue) {\n    // if(taskStatusCode == NAVIGATION_CODE_NAVIGATION_TASK_COMPLETED){//已到达导航点\n    //     RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"arrived at charging navigation point\");\n    //     std::string current_mapid = RobotState::getInstance().getMapId();\n    //     int actionInt = 3;\n    //     std::string BatteryChargingPoint = ReadMapCfg::getInstance().getBatteryChargingPoints();\n    //     Json::Reader reader;\n    //     Json::Value point;\n    //     if (!reader.parse(BatteryChargingPoint, point)) {\n    //         RCLCPP_ERROR(rclcpp::get_logger(\"robdog_control\"), \"Failed to parse JSON: %s\", BatteryChargingPoint.c_str());\n    //         return;\n    //     }\n    //     sendChargeAction(actionInt,current_mapid,point);\n    // } else \n    if(taskStatusCode == CHARGING_CODE_CHARGING_COMPLETED) {\n        sendChargeMarkResponse(0, \"\");\n    } else if (isChargeMarkFailure(taskStatusCode)) {\n        sendChargeMarkResponse(1, getDescription(taskStatusCode));\n        is_charging = false;\n    }\n}\n\nvoid RobdogCenter::handleNavBeforeRepositioning(int taskStatusCode, const Json::Value& msgValue) {\n    if (repositioning) {\n        if (taskStatusCode == NAVIGATION_CODE_NODE_STOPPED) {//重定位先关闭不一致的导航节点，关闭成功后开启当前mapid导航\n            RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Opening navigation with New mapId: %s\", RobotState::getInstance().getMapId().c_str());\n            sendNavigationAction(1, RobotState::getInstance().getMapId());\n        } else if (taskStatusCode == NAVIGATION_CODE_NODE_STARTED) {//重定位之前开启导航成功后下发重定位\n            RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Navigation node started, sending navigation action 11 with Map ID: %s\", RobotState::getInstance().getMapId().c_str());\n            sendNavigationAction(11, RobotState::getInstance().getMapId());\n        } else if (isRepositioningFailure(taskStatusCode)) {//重定位之前关闭导航失败结束重定位状态，repositioning置为false\n            RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Closing navigation failed before map reposition.\");\n            sendMapRepositionResponse(taskStatusCode,msgValue);\n            repositioning = false;\n        }\n    }\n}\n\nbool RobdogCenter::isRepositioningFailure(int taskStatusCode) {\n    return taskStatusCode == NAVIGATION_CODE_NEW_NAVIGATION_COMMAND_DURING_NAVIGATION ||\n        taskStatusCode == NAVIGATION_CODE_NEW_NAVIGATION_COMMAND_DURING_MAPPING ||\n        taskStatusCode == NAVIGATION_CODE_NODE_CRASHED;\n}\n\n//导航相关状态处理与上报\nvoid RobdogCenter::handleNavigationStatus(int taskStatusCode,const Json::Value& jBody) {\n    int status = mapCodeToStatus(taskStatusCode);\n    if (status <= NAVIGATION_STATUS_UNREACHABLE_DURING_MOVEMENT) {\n        sendNavigationReport(status);\n        if (status == 1) {//导航完成\n            handleNavigationTaskCompletion();\n        }\n    }\n\n    if (taskStatusCode == NAVIGATION_CODE_SINGLE_POINT_NAVIGATION_COMPLETED) {\n        atSinglePoint = true;\n    }\n    if (taskStatusCode == NAVIGATION_CODE_NAVIGATION_TASK_CANCELED) {//导航取消\n        RobotState::getInstance().setCurrentState(RobotStateEnum::NORMAL); \n    }\n    if (taskStatusCode == NAVIGATION_CODE_NODE_STARTED) {\n        Navigation_node = true;\n    }\n    if (taskStatusCode == NAVIGATION_CODE_NODE_STOPPED) {\n        Navigation_node = false;\n    }\n    if (isRepositioningResult(taskStatusCode)) {\n        repositioning_timer_->cancel();\n        processRepositioningResult(taskStatusCode, jBody);\n    }\n}\n\nvoid RobdogCenter::sendNavigationReport(int status) {\n    Json::Value response;\n    Json::Value body;\n    body[\"status\"] = status;\n    body[\"batchId\"] = RobotState::getInstance().getbatchId();\n    response[\"deviceId\"] = RobotState::getInstance().getDeviceId();\n    response[\"domain\"] = \"DEVICE_INTERACTION\";\n    response[\"event\"] = \"navigation_report\";\n    response[\"eventId\"] = RobotState::getInstance().getPathEventId().empty() ?\n                        \"robdog_plat_\" + std::to_string(base::homiUtils::getCurrentTimeStamp()) :\n                        RobotState::getInstance().getPathEventId();\n    response[\"seq\"] = Json::Int64(base::homiUtils::getCurrentTimeStamp());\n    response[\"body\"] = body;\n    Json::FastWriter writer;\n    std::string navigationReportString = writer.write(response);\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"navigation_report : %s\", navigationReportString.c_str());\n    sendRequestData(navigationReportString);\n}\n\nvoid RobdogCenter::handleNavigationTaskCompletion() {\n    expresstion_count++;\n    if (expresstion_count == 8) {\n        expresstion_count = 0;\n    }\n    at_target_ = true;\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"at_target_: %d\", at_target_);\n    RobotState::getInstance().setCurrentState(RobotStateEnum::NORMAL);\n    sendCommandToUSLAM(\"navigation/stop\");\n}\n\nbool RobdogCenter::isRepositioningResult(int taskStatusCode) {\n    return taskStatusCode == NAVIGATION_CODE_RELOCALIZATION_FINISHED ||\n        taskStatusCode == NAVIGATION_CODE_INVALID_COMMAND_OUTSIDE_NAVIGATION ||\n        taskStatusCode == NAVIGATION_CODE_MAP_ID_MISMATCH ||\n        taskStatusCode == NAVIGATION_CODE_COMMAND_FAILED ||\n        taskStatusCode == NAVIGATION_CODE_RELOCALIZATION_ERROR_LARGE;\n}\n\n// 导航任务模块\nvoid RobdogCenter::moveToTarget(const homi_speech_interface::msg::SIGCEvent::SharedPtr msg) {\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Prepare sent msg to nvidia.\");\n\n    std::string navCtrlMsgs = msg->event; // JSON字段\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"The msg will be sent to nvidia:%s \", navCtrlMsgs.c_str());\n\n    Json::Reader reader;\n    Json::Value value;\n    if (!reader.parse(navCtrlMsgs, value)) {\n        RCLCPP_ERROR(rclcpp::get_logger(\"robdog_control\"), \"Failed to parse JSON: %s\", navCtrlMsgs.c_str());\n        return;\n    }\n    sendCommandToUSLAM(\"navigation/start\");\n\n    nav_wait_start_time_ = std::chrono::steady_clock::now();\n        nav_wait_timer_ = node_->create_wall_timer(\n        std::chrono::milliseconds(100),\n        [this, value]() {\n            auto duration = std::chrono::steady_clock::now() - nav_wait_start_time_;\n            if (Navigation_node) {\n                const Json::Value& points = value[\"points\"];\n                if (!points.empty()) {\n                    const auto& point = points[0];\n                    double x = point[\"x\"].asDouble();\n                    double y = point[\"y\"].asDouble();\n                    double angle = point[\"angle\"].asDouble();\n                    sendPoint(x, y, angle);\n                }\n                // 只创建一次 move_point_timer_，并立即 cancel nav_wait_timer_\n                nav_wait_timer_->cancel();\n                move_point_timer_ = node_->create_wall_timer(\n                    std::chrono::milliseconds(500),\n                    [this]() {\n                        Json::Value currentPose = RobotState::getInstance().getMoveTaskPose();\n                        double map_x = currentPose[\"x\"].asDouble();\n                        double map_y = currentPose[\"y\"].asDouble();\n                        double yaw = currentPose[\"angle\"].asDouble();\n                        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"target x: %f, y: %f, theta: %f\", map_x, map_y, yaw);\n                        std::ostringstream oss;\n                        oss << \"navigation/set_goal_pose/\" << map_x << \"/\" << map_y << \"/\" << yaw;\n                        sendCommandToUSLAM(oss.str());\n                        move_point_timer_->cancel();\n                    });\n            } else if (duration > nav_wait_timeout_) {\n                nav_wait_timer_->cancel();\n                RCLCPP_WARN(rclcpp::get_logger(\"robdog_control\"), \"Timeout waiting for Navigation_node to become true\");\n            }\n        }\n    );\n}\n\n// 园区巡逻\nvoid RobdogCenter::moveToPatrolPoint(const homi_speech_interface::msg::SIGCEvent::SharedPtr msg) {\n        moveToTarget(msg);\n        // 创建一个定时器来检查目标状态\n        std::string jsonStr = msg->event;\n        Json::Reader reader;\n        Json::Value jBody;\n        if (reader.parse(jsonStr, jBody)) {\n        // 解析成功，可以访问 jBody 中的数据\n        // std::cout << \"解析成功,jBody: \" << jBody.toStyledString() << std::endl;\n        auto callback = std::bind(&RobdogCenter::checkPatrolStatus, this, jBody);\n        robPatrolStatusTimer_ = node_->create_wall_timer(std::chrono::seconds(1), [callback]() { callback(); });\n    } else {\n        // 解析失败，输出错误信息\n        std::cerr << \"解析JSON字符串失败: \" << reader.getFormattedErrorMessages() << std::endl;\n    } \n}\n\n//充电\nvoid RobdogCenter::moveToBatteryPoint(const homi_speech_interface::msg::SIGCEvent::SharedPtr msg) {\n        homi_speech_interface::msg::RobdogAction zzmsg;\n        zzmsg.actiontype = \"NavCtrl\";\n        zzmsg.actionargument = \"AutoMode\";\n        publishAction(std::make_shared<homi_speech_interface::msg::RobdogAction>(zzmsg));\n\n        moveToTarget(msg);\n        // 创建一个定时器来检查目标状态\n        std::string jsonStr = msg->event;\n        Json::Reader reader;\n        Json::Value jBody;\n        if (reader.parse(jsonStr, jBody)) {\n        // 解析成功，可以访问 jBody 中的数据\n        // std::cout << \"解析成功,jBody: \" << jBody.toStyledString() << std::endl;\n        auto callback = std::bind(&RobdogCenter::checkBatteryStatus, this, jBody);\n        robPatrolStatusTimer_ = node_->create_wall_timer(std::chrono::seconds(1), [callback]() { callback(); });\n    } else {\n        // 解析失败，输出错误信息\n        std::cerr << \"解析JSON字符串失败: \" << reader.getFormattedErrorMessages() << std::endl;\n    } \n}\n\nvoid RobdogCenter::moveToHomePoint(const homi_speech_interface::msg::SIGCEvent::SharedPtr msg) {\n    moveToTarget(msg);\n    // 创建一个定时器来检查目标状态\n    std::string jsonStr = msg->event;\n    Json::Reader reader;\n    Json::Value jBody;\n    if (reader.parse(jsonStr, jBody)) {\n    // 解析成功，可以访问 jBody 中的数据\n    // std::cout << \"解析成功,jBody: \" << jBody.toStyledString() << std::endl;\n    auto callback = std::bind(&RobdogCenter::checkGoHomeStatus, this, jBody);\n    robPatrolStatusTimer_ = node_->create_wall_timer(std::chrono::seconds(1), [callback]() { callback(); });\n    } else {\n        // 解析失败，输出错误信息\n        std::cerr << \"解析JSON字符串失败: \" << reader.getFormattedErrorMessages() << std::endl;\n    } \n}\n\nvoid RobdogCenter::moveTodeliverCakePoint(const homi_speech_interface::msg::SIGCEvent::SharedPtr msg) {\n    moveToTarget(msg);\n    // 创建一个定时器来检查目标状态\n    std::string jsonStr = msg->event;\n    Json::Reader reader;\n    Json::Value jBody;\n    if (reader.parse(jsonStr, jBody)) {\n    // 解析成功，可以访问 jBody 中的数据\n    // std::cout << \"解析成功,jBody: \" << jBody.toStyledString() << std::endl;\n    auto callback = std::bind(&RobdogCenter::checkDeliverCakeStatus, this, jBody);\n    robPatrolStatusTimer_ = node_->create_wall_timer(std::chrono::seconds(1), [callback]() { callback(); });\n    } else {\n        // 解析失败，输出错误信息\n        std::cerr << \"解析JSON字符串失败: \" << reader.getFormattedErrorMessages() << std::endl;\n    } \n}\n\nvoid RobdogCenter::handleCancelMovement() {\n    // 处理取消移动任务\n    // ------------------------ WebSocket传输给感知主机 ------------------------\n    sendCommandToUSLAM(\"navigation/stop\");\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"),\"the message of handleCancelMovement: %s\", \"navigation/stop\");\n}\n\n// void RobdogCenter::navPositionCallback(const geometry_msgs::msg::Pose::SharedPtr pos) {\n//     // std::string strMsg = msg->data;\n//     Json::Value value;\n//     value[\"x\"] = pos->position.x;\n//     value[\"y\"] = pos->position.y;\n//     // tf2::Quaternion quaternion;\n//     // tf2::convert(pos.orientation, quaternion);\n//     // 使用四元数构造函数来创建tf2::Quaternion\n//     tf2::Quaternion quaternion(pos->orientation.x, pos->orientation.y,\n//                                pos->orientation.z, pos->orientation.w);\n//     double roll, pitch, yaw;\n//     tf2::Matrix3x3(quaternion).getRPY(roll, pitch, yaw);\n//     yaw = yaw * 180.0 / M_PI;\n//     value[\"angle\"] = yaw;\n    \n//     // RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Received robot position %s\",value.toStyledString().c_str());\n\n//     RobotState::getInstance().setCurRobotPose(value);\n// }\n// void RobdogCenter::navPositionCallback(const nav_msgs::msg::Odometry::SharedPtr odom) {\n//     Json::Value value;\n//     value[\"x\"] = odom->pose.pose.position.x;\n//     value[\"y\"] = odom->pose.pose.position.y;\n//     tf2::Quaternion quaternion(\n//         odom->pose.pose.orientation.x,\n//         odom->pose.pose.orientation.y,\n//         odom->pose.pose.orientation.z,\n//         odom->pose.pose.orientation.w\n//     );\n//     double roll, pitch, yaw;\n//     tf2::Matrix3x3(quaternion).getRPY(roll, pitch, yaw);\n//     yaw = yaw * 180.0 / M_PI;\n//     value[\"angle\"] = yaw;\n//     RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Received robot position %s\",value.toStyledString().c_str());\n\n//     MapConfig config;\n//     if (loadMapConfig(\"/root/tjc_test/map_server_ros2/scripts/tmp/unitree_files/map_display.txt\", config)) {\n//         // RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Width: %d, Height: %d\", config.img_width, config.img_height);\n//         // RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Resolution: %f\", config.resolution);\n//         // RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Origin X: %f, Origin Y: %f\", config.origin_x, config.origin_y);\n//     }\n    \n//     auto result = RobdogCenter::mapToPixel(value[\"x\"].asDouble(), value[\"y\"].asDouble(), yaw, config.origin_x,  config.origin_y, config.resolution, config.img_height);\n//     double pixel_x = result[0], pixel_y = result[1], angle_deg = result[2];\n//     RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Map pixel: (%f, %f), Angle: %f\", pixel_x, pixel_y, angle_deg);\n//     value[\"x\"] = pixel_x;\n//     value[\"y\"] = pixel_y;\n//     value[\"angle\"] = angle_deg;\n//     RobotState::getInstance().setCurRobotPose(value);\n// }\n\nint RobdogCenter::mapCodeToStatus(int code) {\n    // 根据 code 映射到对应的 task_status\n    switch (code) {\n        case NAVIGATION_CODE_NAVIGATION_TASK_COMPLETED:\n            return NAVIGATION_STATUS_MOVE_TASK_FINISHED;  // 导航任务完成\n        case NAVIGATION_CODE_TARGET_UNREACHABLE:\n            return NAVIGATION_STATUS_POINT_UNREACHABLE;  // 目标点不可达\n        // case NAVIGATION_CODE_RELOCALIZATION_ERROR_LARGE: // 2207 重定位误差大\n        //     return NAVIGATION_STATUS_POINT_UNREACHABLE;  // 目标点不可达\n        case NAVIGATION_CODE_CANNOT_GO:\n            return NAVIGATION_STATUS_CANNOTGO;  // 无法出发\n        case NAVIGATION_CODE_NAVIGATION_TASK_CANCELED:\n            return NAVIGATION_STATUS_MOVEMENT_CANCEL_SUCCESS;  // 导航任务已取消\n        case NAVIGATION_CODE_SINGLE_POINT_NAVIGATION_COMPLETED:\n            return NAVIGATION_STATUS_SINGLE_POINT_NAVIGATION_COMPLETED; // 单点导航任务已完成\n        case NAVIGATION_CODE_TASK_FAILED:\n            return NAVIGATION_STATUS_UNREACHABLE_DURING_MOVEMENT; // 任务失败\n        default:\n             // 处理未预期的代码值\n            RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"unknown code is %d \",code);\n            // return NAVIGATION_STATUS_POINT_UNREACHABLE;  // 其他情况默认为目标点不可达\n            return NAVIGATION_STATUS_OUT_OF_RANGE; // 其他情况，默认不可达会导致APP端一直报错\n    }\n}\n\nstd::string RobdogCenter::getCodeName(int code) {\n    auto it = navigationCodeDescriptions.find(code);\n    if (it != navigationCodeDescriptions.end()) {\n        return std::get<0>(it->second);\n    }\n    return \"未知状态码\";\n}\n\nstd::string RobdogCenter::getDescription(int code) {\n    auto it = navigationCodeDescriptions.find(code);\n    if (it != navigationCodeDescriptions.end()) {\n        return std::get<1>(it->second);\n    }\n    return \"未知状态码\";\n}\n\n// 0-停止跟随 1-开启跟随 2-开启UWB跟随\nvoid RobdogCenter::actionFollow(int status) {\n    string strAction = 0 == status ? \"endFollow\"\n                     : 1 == status ? \"startFollow\"\n                     : 2 == status ? \"startUwbSummon\" : \"endFollow\";\n    Json::Value value;\n    Json::Value params;\n    value[\"client_type\"] = CLIENT_LAUNCHER;\n    value[\"target_client\"] = CLIENT_NVIDIA;\n    value[\"action\"] = strAction;\n    // SendtoNvOrin(value.toStyledString().c_str(), nConnectIndex_);\n    auto msg = std::make_shared<std_msgs::msg::String>();\n    msg->data = value.toStyledString();\n    try {\n        follow_control_pub_->publish(*msg);\n        RCLCPP_DEBUG(rclcpp::get_logger(\"FollowMe\"), \n                    \"Published follow control: %s\", value.toStyledString().c_str());\n    } catch (const std::exception& e) {\n        RCLCPP_ERROR(rclcpp::get_logger(\"FollowMe\"),\n                    \"Failed to publish follow control: %s\", e.what());\n    }\n}\n\n// ----------------------------------------- 和定点移动有关的 ----------------------------------------------\nbool RobdogCenter::isAtTarget(const homi_speech_interface::msg::SIGCEvent::SharedPtr msg) {\n    std::string strMsg = msg->event; // JSON字段\n    Json::Reader reader;\n    Json::Value value;\n    reader.parse(strMsg, value);\n    Json::Value jBody = value[\"body\"];\n    // 获取当前机器人位置\n    Json::Value currentPose = RobotState::getInstance().getCurRobotPose();\n    double curX = currentPose[\"x\"].asDouble();\n    double curY = currentPose[\"y\"].asDouble();\n    double curAngle = currentPose[\"angle\"].asDouble();\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"current pos x: %f, y: %f, theta: %f\", curX, curY, curAngle);\n\n    // 目标位置\n    double targetX = 0.0;\n    double targetY = 0.0;\n    double targetTheta = 0.0;\n\n    if (!jBody[\"points\"].isNull()){\n        Json::Value arrayPoints = jBody[\"points\"];\n        if (arrayPoints.isArray()) {\n            for (auto it : arrayPoints) {\n                Json::Value jPoint = it;\n                targetX = jPoint[\"x\"].asDouble();\n                targetY = jPoint[\"y\"].asDouble();\n                targetTheta = jPoint[\"angle\"].asDouble();\n            }\n        }\n    }\n    // 智能播报给的location\n    else if (!jBody[\"remindLocation\"].isNull()){\n        Json::Value jPoint = jBody[\"remindLocation\"];\n        targetX = jPoint[\"x\"].asDouble();\n        targetY = jPoint[\"y\"].asDouble();\n        targetTheta = jPoint[\"angle\"].asDouble();\n    }\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"target pos x: %f, y: %f, theta: %f\", targetX, targetY, targetTheta);\n    \n    // RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"planning move pos x: %f, y: %f, theta: %f\", targetX, targetY, targetTheta);\n    // 比较当前位置与目标位置\n    return (fabs(targetX - curX) < 0.01 && fabs(targetY - curY) < 0.01 &&\n            fabs(targetTheta - curAngle) < 0.01);\n}\n\nvoid RobdogCenter::playAudio(const std::string &filePath) {\n    if(node_) {\n        node_->playAudio(filePath);\n    }\n}\n\nvoid RobdogCenter::checkTargetStatus() {\n    // 如果到达目标\n    if (at_target_) {\n        std::thread t_audio(&RobdogCtrlNode::playAudio,node_,node_->getResourcePath(\"audio/nav/到位打开氛围灯.wav\"));\n        t_audio.detach();\n        // 调用iot控制打开氛围灯\n        iot_control_client_ = node_->create_client<homi_speech_interface::srv::IotControl>(\n            \"/homi_speech/speech_iot_control_service\");\n        auto request_iot_control = std::make_shared<homi_speech_interface::srv::IotControl::Request>();\n        auto ret = iot_control_client_->wait_for_service(std::chrono::seconds(1));\n        if(ret==false)\n        {\n            RCLCPP_WARN(rclcpp::get_logger(\"robdog_control\"),\"Failed to waitForExistence  Iot control service assistant\");\n        }\n        request_iot_control->param = \"{\\\"outletStatus\\\":1}\";\n        auto result = iot_control_client_->async_send_request(request_iot_control);   \n\n\n        // 调用语音助手拍照\n        take_photo_client_ = node_->create_client<homi_speech_interface::srv::AssistantTakePhoto>(\n            \"/homi_speech/helper_assistant_takephoto_service\");\n        auto request_take_photo = std::make_shared<homi_speech_interface::srv::AssistantTakePhoto::Request>();\n        auto ret_1 = take_photo_client_->wait_for_service(std::chrono::seconds(1));\n        if(ret_1==false)\n        {\n            RCLCPP_WARN(rclcpp::get_logger(\"robdog_control\"),\"Failed to waitForExistence take photo service assistant\");\n        }\n        auto result_1 = take_photo_client_->async_send_request(request_take_photo);  \n\n        at_target_ = false;\n        // 停止定时器\n        robMoveStatusTimer_->cancel(); \n    }\n}\n\n// 园区巡逻相关\nvoid RobdogCenter::checkPatrolStatus(const Json::Value &jBody) {\n    homi_speech_interface::msg::RobdogAction msg;\n    if (atSinglePoint) {\n        if (jBody.isMember(\"points\")) {\n        const Json::Value& points = jBody[\"points\"];\n            for (const auto& point : points) {\n                if (point.isMember(\"uid\") && point[\"uid\"].asString() == uid) {\n                    if (point.isMember(\"actions\")) {\n                        const Json::Value& actions = point[\"actions\"];\n                        for (const auto& action : actions) {\n                            int actionValue = action[\"action\"].asInt();\n                            std::string angle = action[\"angle\"].asString();\n                            if (actionValue >= 1 && actionValue <= static_cast<int>(actionVector.size())) {\n                                Json::Value reqValue;\n                                reqValue[\"client_type\"] = CLIENT_LAUNCHER;\n                                reqValue[\"target_client\"] = CLIENT_NVIDIA;         \n                                reqValue[\"action\"] = \"navigation_control\";\n\n                                // 暂停导航\n                                Json::Value additionalParams;\n                                additionalParams[\"action\"] = 13; // 暂停导航\n                                additionalParams[\"mapId\"] = RobotState::getInstance().getMapId(); //  导航功能使用的地图\n                                additionalParams[\"batchId\"] = RobotState::getInstance().getbatchId(); // \"1234\"; // 多点导航任务的id(就是batchid)\n                                reqValue[\"params\"] = additionalParams;\n                                // RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"),\"The message of checkPatrolStatus: %s\", reqValue.toStyledString().c_str());\n                                SendtoNvOrin(reqValue.toStyledString().c_str(), nConnectIndex_);\n\n                                // 先旋转\n                                msg.actiontype = \"rotateCtl\";\n                                msg.actionargument = angle;\n                                std::cout << \"angle: \" << msg.actionargument << std::endl;\n                                publishAction(std::make_shared<homi_speech_interface::msg::RobdogAction>(msg));\n                                // 暂停时间stayDuration，等待动作完成\n                                int stayDuration = point[\"stayDuration\"].asInt();\n                                std::this_thread::sleep_for(std::chrono::seconds(stayDuration));\n                                // 再执行动作\n                                msg.actiontype = \"motorSkill\";\n                                msg.actionargument = actionVector[actionValue - 1];\n                                std::cout << \"Action: \" << msg.actionargument << std::endl;\n                                publishAction(std::make_shared<homi_speech_interface::msg::RobdogAction>(msg));\n                                // 暂停时间stayDuration，等待动作完成\n                                std::this_thread::sleep_for(std::chrono::seconds(stayDuration));\n                                // 继续导航\n                                additionalParams[\"action\"] = 14; \n                                reqValue[\"params\"] = additionalParams;\n                                // RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"),\"The message of checkPatrolStatus: %s\", reqValue.toStyledString().c_str());\n                                SendtoNvOrin(reqValue.toStyledString().c_str(), nConnectIndex_);\n                            } else {\n                                msg.actionargument = \"unknown\";\n                            }\n                        }\n                    }\n                }\n            }\n        }\n        atSinglePoint = false;\n       \n    }\n     // 如果整个导航任务完成，则取消定时器\n    if(at_target_){\n        // 停止定时器\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"导航任务完成，取消定时器\");\n        robPatrolStatusTimer_->cancel(); \n        at_target_ = false; // 一次导航任务完成\n    }\n}\n\n// 充电\nvoid RobdogCenter::checkBatteryStatus(const Json::Value &jBody) {\n    if(at_target_){\n        //已到达充电导航点，下发开始充电指令\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"arrived at target, start charging\");\n        // Json::Value points;\n        // std::string ChargeNavPoints = ReadMapCfg::getInstance().getChargeNavPoints();\n        // Json::Reader reader;\n        // reader.parse(ChargeNavPoints, points);\n        // std::string current_mapid = RobotState::getInstance().getMapId();\n        // sendChargeAction(3,current_mapid,points);\n        sendCommandToUSLAM(\"autocharge/start\");\n        robPatrolStatusTimer_->cancel(); \n        at_target_ = false; \n    }\n} \n\nvoid runCommand(const std::string& command) {  \n    system(command.c_str());  \n} \n\nvoid playAudioAsync(RobdogCenter* thiz, const std::string audiofile) {  \n    thiz->playAudio(audiofile);\n} \n\nvoid RobdogCenter::checkGoHomeStatus(const Json::Value &jBody) {\n    if(at_target_){\n        // 切表情\n        std::string video_path=RobotState::getInstance().getResourcePath(\"video/眨眼+开心+星星眼.mp4\");\n        ExpressionChange::getInstance().async_callback_work(video_path,1);\n        std::this_thread::sleep_for(std::chrono::seconds(3));\n\n        // // 做动作\n        // homi_speech_interface::msg::RobdogAction msg;\n        // msg.actiontype = \"motorSkill\";\n        // msg.actionargument = \"twistBody\";\n        // std::cout << \"Action: \" << msg.actionargument << std::endl;\n        // publishAction(std::make_shared<homi_speech_interface::msg::RobdogAction>(msg));\n\n        // 播放音频\n        std::string file_path = node_->getResourcePath(\"audio/nav/gohome.wav\");\n        std::string command = \"aplay \\\"\" + file_path + \"\\\"\";\n        //std::thread t(runCommand, command); \n        std::thread t(playAudioAsync,this,file_path);\n\t\tt.detach(); \n\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"GoHome任务完成，取消定时器\");\n        robPatrolStatusTimer_->cancel(); \n        at_target_ = false; \n    }\n}\n\nvoid RobdogCenter::checkDeliverCakeStatus(const Json::Value &jBody) {\n    if(at_target_){\n        homi_speech_interface::msg::RobdogAction msg;\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"DeliverCake任务完成，取消定时器\");\n        robPatrolStatusTimer_->cancel(); \n        at_target_ = false; \n        // msg.actiontype = \"motorSkill\";\n        // msg.actionargument = \"greeting\";\n        // std::cout << \"Action: \" << msg.actionargument << std::endl;\n        // publishAction(std::make_shared<homi_speech_interface::msg::RobdogAction>(msg));\n        // std::string file_path = \"/home/<USER>/resource/audio/gohome.wav\";\n        // playAudio(file_path);\n        // // 暂停时间stayDuration，等待动作完成\n        // std::this_thread::sleep_for(std::chrono::seconds(15));\n\n        // msg.actiontype = \"motorSkill\";\n        // msg.actionargument = \"twistBody\";\n        // std::cout << \"Action: \" << msg.actionargument << std::endl;\n        // publishAction(std::make_shared<homi_speech_interface::msg::RobdogAction>(msg));\n        // std::string file_path = \"/home/<USER>/resource/audio/sofa.wav\";\n        // playAudio(file_path);\n        // // 暂停时间stayDuration，等待动作完成\n        // std::this_thread::sleep_for(std::chrono::seconds(15));\n        \n    }\n}\n\n void RobdogCenter::moveToTargetAndPlayAudio(const homi_speech_interface::msg::SIGCEvent::SharedPtr msg) {\n    // 如果已在指定地点，则直接播放音频\n    if (isAtTarget(msg)) {\n        std::thread t_audio(&RobdogCtrlNode::playAudio,node_,node_->getResourcePath(\"audio/nav/1312直接打开氛围灯.wav\"));\n        t_audio.detach();\n\n        \n        // rclcpp::TimerBase::SharedPtr robMoveStatusTimer_;\n\n        iot_control_client_ = node_->create_client<homi_speech_interface::srv::IotControl>(\"/homi_speech/speech_iot_control_service\");\n        take_photo_client_ = node_->create_client<homi_speech_interface::srv::AssistantTakePhoto>(\"/homi_speech/helper_assistant_takephoto_service\");\n\n\n        // 调用iot控制打开氛围灯\n        auto request = std::make_shared<homi_speech_interface::srv::IotControl::Request>();\n        auto ret = iot_control_client_->wait_for_service(std::chrono::seconds(1));\n        if(ret==false)\n        {\n            RCLCPP_WARN(rclcpp::get_logger(\"robdog_control\"),\"Failed to waitForExistence service assistant\");\n        }\n        request->param = \"{\\\"outletStatus\\\":1}\";\n        auto result = iot_control_client_->async_send_request(request);   \n\n        // 第四步，调用语音助手\n        auto request_1 = std::make_shared<homi_speech_interface::srv::AssistantTakePhoto::Request>();\n        auto ret_1 = take_photo_client_->wait_for_service(std::chrono::seconds(1));\n        if(ret_1==false)\n        {\n            RCLCPP_WARN(rclcpp::get_logger(\"robdog_control\"),\"Failed to waitForExistence service assistant\");\n        }\n        auto result_1 = take_photo_client_->async_send_request(request_1);   \n\n    } else {\n        moveToTarget(msg);\n        std::thread t_audio(&RobdogCtrlNode::playAudio,node_,node_->getResourcePath(\"audio/nav/去玄关.wav\"));\n        t_audio.detach();\n        // 创建一个定时器来检查目标状态\n        robMoveStatusTimer_ = node_->create_wall_timer(std::chrono::seconds(1), std::bind(&RobdogCenter::checkTargetStatus, this));\n    }\n}\n\n// 和拍照有关\n void RobdogCenter::callHelperPhoto() {\n    rclcpp::Client<homi_speech_interface::srv::AssistantTakePhoto>::SharedPtr assistant_take_photo_client_;\n    assistant_take_photo_client_ = node_->create_client<homi_speech_interface::srv::AssistantTakePhoto>(\"/homi_speech/helper_assistant_takephoto_service\");\n    if (!assistant_take_photo_client_->wait_for_service(std::chrono::seconds(1))) {\n        RCLCPP_ERROR(rclcpp::get_logger(\"robdog_control\"), \"Service not available after waiting\");\n        return;\n    }\n\n    auto request = std::make_shared<homi_speech_interface::srv::AssistantTakePhoto::Request>();\n    // 这里可以根据需要设置请求参数\n    auto ret = assistant_take_photo_client_->wait_for_service(std::chrono::seconds(1));\n    if(ret==false)\n    {\n        RCLCPP_WARN(rclcpp::get_logger(\"robdog_control\"),\"Failed to waitForExistence service assistant\");\n    }\n    auto result = assistant_take_photo_client_->async_send_request(request, std::bind(&RobdogCenter::takephoto_srv_callback, this, std::placeholders::_1));   \n}\n\nvoid RobdogCenter::takePhotoService() {\n    rclcpp::Publisher<std_msgs::msg::String>::SharedPtr pub_;\n    pub_ = node_->create_publisher<std_msgs::msg::String>(\"/take_photo\", 10);\n    std_msgs::msg::String msg;\n    msg.data = \"take_photo\";\n    pub_->publish(msg);\n\n    // 输出日志\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Published message: [%s]\", msg.data.c_str());\n\n    /*\n    // 如果需要打开氛围灯，可以在这里添加相关代码\n    if (!iot_control_client_->wait_for_service(std::chrono::seconds(1))) {\n        RCLCPP_ERROR(rclcpp::get_logger(\"robdog_control\"), \"IotControl service not available\");\n        return;\n    }\n    \n    auto request1 = std::make_shared<homi_speech_interface::srv::IotControl::Request>();\n    request1->param = \"{\\\"outletStatus\\\":0}\";\n\n    auto future1 = iot_control_client_->async_send_request(request1);\n    if (rclcpp::spin_until_future_complete(node_->get_node_base_interface(), future1) == rclcpp::FutureReturnCode::SUCCESS) {\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"IotControl response: %d\", future1.get()->errorCode);\n    } else {\n        RCLCPP_ERROR(rclcpp::get_logger(\"robdog_control\"), \"Failed to call IotControl service\");\n    }\n    */\n}\n\n\n// ************************************************* 智能播报相关 ***************************************************************\n void RobdogCenter::moveToTargetAndBrocast(const homi_speech_interface::msg::SIGCEvent::SharedPtr msg) {\n    // 如果已在指定地点，则直接开始播报\n    if (isAtTarget(msg)) {\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Start broadcasting!!\");\n        timer_brocast->cancel();\n        brocast_send_count_ = 0;\n        timer_brocast->reset();\n        SendBrocastCallback();\n    } else {\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Start MovetoTarget!!\");\n        // 创建一个用于发送给感知主机的msg（用于多点导航）\n        auto msg_to_nvidia =  std::make_shared<homi_speech_interface::msg::SIGCEvent>();\n        std::string data_string = msg->event;\n        Json::Value inputRoot = parseJson(data_string);\n        Json::Value outputRoot;\n\n        // 添加 points 数组\n        Json::Value pointsArray(Json::arrayValue);\n        Json::Value point;\n        \n        // 这里获取 \"remindLocation\" 中的数据，并转换到 points 数组\n        Json::Value remindLocation = inputRoot[\"body\"][\"remindLocation\"];\n        point[\"x\"] = remindLocation[\"x\"].asDouble();\n        point[\"y\"] = remindLocation[\"y\"].asDouble();\n        point[\"angle\"] = remindLocation[\"angle\"].asDouble();\n        point[\"option\"] = \"even_low_speed\";  // 根据需求填写\n        point[\"uid\"] = \"1\";  // 这里可以根据实际情况调整\n        \n        pointsArray.append(point);\n        outputRoot[\"points\"] = pointsArray;\n\n        // 把json转为string\n        Json::StreamWriterBuilder writer;\n        std::string jsonString = Json::writeString(writer, outputRoot);\n        msg_to_nvidia->event = jsonString;  \n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"The message of moveToTarget is: %s\", jsonString.c_str());\n\n        moveToTarget(msg_to_nvidia);\n        // 创建一个定时器来检查目标状态\n        robMoveStatusTimer_brocast = node_->create_wall_timer(std::chrono::seconds(1), std::bind(&RobdogCenter::checkTargetStatus_brocast, this));\n    }\n}\n\nvoid RobdogCenter::checkTargetStatus_brocast() {\n    // 如果到达目标\n    if (at_target_) {\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Arrive target point and start broadcasting!!\");\n        timer_brocast->cancel();\n        brocast_send_count_ = 0;\n        timer_brocast->reset();\n        SendBrocastCallback();\n\n        at_target_ = false; // 一次导航任务完成\n        // 停止定时器\n        robMoveStatusTimer_brocast->cancel(); \n    }\n    // 如果导航任务取消了就关闭检查的定时器\n    else if(RobotState::getInstance().getCurrentState() != RobotStateEnum::NAVIGATION){\n        robMoveStatusTimer_brocast->cancel(); \n    }\n}\n\n// 接受语音助手上报的信息（是否终止）\nvoid RobdogCenter::BrocastIfAbortCallBack(const homi_speech_interface::msg::AssistantEvent::SharedPtr msg) {\n    std::string sectionId_past = RobotState::getInstance().getSectionId(); // 语音助手之前上报的sectionId\n    std::string sectionId_cur = msg->section_id;\n    std::string aborting = msg->description;\n    // RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"aborting: %s, sectionId_past: %s, sectionId_cur: %s\",\n    // aborting.c_str(), sectionId_past.c_str(), sectionId_cur.c_str());\n    // if(sectionId_past == sectionId_cur)\n    // RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"6666666666666666666666666666666666666666\");\n    if (sectionId_past == sectionId_cur && aborting == \"UserAbort\") {   // 被语音词唤醒\n        RobotBroadcastStatusToPlat(0); // 被打断\n        timer_brocast->cancel();\n        brocast_send_count_ = 0;\n        // RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"interrupted by the wake word!!!\");\n    }\n}\n\n// 向平台上传智能播报状态，被唤醒词打断了需上报\nvoid RobdogCenter::RobotBroadcastStatusToPlat(int status) {\n    // 构建状态报告的 JSON\n    Json::Value response;\n\n    response[\"deviceId\"] = RobotState::getInstance().getDeviceId();\n    response[\"domain\"] = \"ROBOT_BUSINESS_DEVICE\";        // 域\n    response[\"event\"] = \"broadcast_report\";              // 事件类型\n    response[\"eventId\"] = \"111111111\";                   // 事件 ID\n    response[\"seq\"] = std::to_string(base::homiUtils::getCurrentTimeStamp()); // 时间戳作为序列号\n\n    // 将状态写入 JSON\n    response[\"body\"][\"status\"] = status; // 状态字段：0-打断，1-正常运行\n    long id = RobotState::getInstance().getRemindId();\n    response[\"body\"][\"remindId\"] = Json::Int64(id); // 设备 ID【直接读取】\n\n    // 将 JSON 转换为字符串\n    Json::StreamWriterBuilder writerBuilder;\n    std::string jsonString = Json::writeString(writerBuilder, response);\n\n    // 打印信息\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Send res to plat, res is %s\", jsonString.c_str());\n\n    // 调用服务并处理响应\n    sendRequestData(jsonString);  \n}\n\n// 定时发送播报文本\nvoid RobdogCenter::SendBrocastCallback() {\n    // RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Brocast times: %d, Total Count_: %d\", brocast_send_count_, brocast_total_count_);\n    if (brocast_send_count_ < brocast_total_count_) { // 人为定义发送次数\n        sendStringToBrocast(brocast_text); // 向语音助手发送播报文本\n        // RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Start Brocasting: %s.\", brocast_text);\n        ++brocast_send_count_; // 增加了定时器次数\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Start Brocasting: %s, Pub times: %d, Total Count_: %d\", brocast_text, brocast_send_count_, brocast_total_count_);\n    } else {\n        // 完成发送后停止定时器\n        timer_brocast->cancel();\n        brocast_send_count_ = 0;\n    }\n}\n\n// 调用服务向语音助手发送智能播报文本（收到的sectionId要保存起来）\nvoid RobdogCenter::sendStringToBrocast(const std::string &message) {\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Sending to brocast: %s\", message.c_str()); // 播报文本\n   \n    // 创建请求消息\n    auto request = std::make_shared<homi_speech_interface::srv::AssistantSpeechText::Request>();\n    request->msg = message;\n\n    // 调用服务并处理响应\n    if (!brocast_client->wait_for_service(std::chrono::seconds(1))) {\n        RCLCPP_ERROR(rclcpp::get_logger(\"robdog_control\"), \"Service not available after waiting\");\n        return;\n    }\n\n    auto ret = brocast_client->wait_for_service(std::chrono::seconds(1));\n    if(ret==false)\n    {\n        RCLCPP_WARN(rclcpp::get_logger(\"robdog_control\"),\"Failed to waitForExistence service assistant\");\n    }\n    auto result = brocast_client->async_send_request(request, std::bind(&RobdogCenter::brocast_srv_callback, this, std::placeholders::_1));   \n\n}\n\n// void RobdogCenter::checkNvidia_srv_status(bool openNavigationIfClosed, std::string mapId) {\n//     RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Starting checkNvidia_srv_status at %ld\", base::homiUtils::getCurrentTimeStamp());\n//     RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"unitreeMappingStatus %d unitreeNavigationStatus %d \",unitreeMappingStatus,unitreeNavigationStatus);\n//     // 提取各个算法的状态信息\n//     bool mapping_active = unitreeMappingStatus;\n//     bool navigation_active = unitreeNavigationStatus;\n//     // bool charge_active = \"\";\n//     std::string current_mapid = unitreeUsingMapId;\n\n//     // 并行记录日志\n//     RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"MappingAct: %d, NavigationAct: %d, mapId:%s\",mapping_active,navigation_active,current_mapid.c_str());\n\n//     // NORMAL&MAPPING状态：如果导航开着需要关闭，如果导航关闭不需要做任何处理；重定位指令：如果导航开着需要判断 mapId 与下发的 mapId 是否一致，如果一致不需要任何处理，如果不一致需要先关闭原来的导航，再开启 mapId 的导航，再下发重定位。\n//     if (navigation_active) {\n//         Navigation_node = true;\n//         if (openNavigationIfClosed) {\n//             // 收到重定位指令\n//             if (current_mapid != mapId) {\n//                 RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Navigation is active with different Map ID, closing navigation with Map ID: %s\", current_mapid.c_str());\n//                 // sendNavigationAction(0, current_mapid);\n//             } else {\n//                 RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Navigation is active with the same Map ID, no action needed.\");\n//                 // sendNavigationAction(11, mapId);//导航已经开了，直接发送重定位指令\n//                 sendCommandToUSLAM(\"localization/set_initial_pose/0/0/0\"); \n//             }\n//         } else {\n//             // NORMAL&MAPPING状态，直接关闭\n//             RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Navigation is active, closing navigation with Map ID: %s\", current_mapid.c_str());\n//             sendCommandToUSLAM(\"localization/stop\");\n//             // sendCommandToUSLAM(\"navigation/stop\");//暂不确定是哪一个\n//         }\n//     } else if (openNavigationIfClosed) {\n//         RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Navigation is not active, opening navigation with Plat_Map ID: %s\", mapId.c_str());\n//         sendNavigationAction(1, mapId);\n//     } \n//     else {\n//         Navigation_node = false;\n//         RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"navigation service is not running\");\n//     }\n//     if (mapping_active) {\n//         RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Mapping is active, closing Mapping with Map ID: %s\", current_mapid.c_str());\n//         sendMapAction(\"cancel\");\n//     }\n// }\nvoid RobdogCenter::checkNvidia_srv_status(bool openNavigationIfClosed, std::string mapId) {\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Starting checkNvidia_srv_status at %ld\", base::homiUtils::getCurrentTimeStamp());\n    // 提取各个算法的状态信息\n    bool mapping_active = (unitreeMappingStatus == 1);\n    bool navigation_active = (unitreeNavigationStatus == 1);\n    // bool charge_active = \"\";\n    std::string current_mapid = unitreeUsingMapId;\n\n    // 并行记录日志\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"MappingAct: %d, NavigationAct: %d, mapId:%s\",mapping_active,navigation_active,current_mapid.c_str());\n\n    // NORMAL&MAPPING状态：如果导航开着需要关闭，如果导航关闭不需要做任何处理；重定位指令：如果导航开着需要判断 mapId 与下发的 mapId 是否一致，如果一致不需要任何处理，如果不一致需要先关闭原来的导航，再开启 mapId 的导航，再下发重定位。\n    \n        // Navigation_node = false;\n}\n\n// 调用服务查询算法服务状态\nvoid RobdogCenter::checkNvidiaServiceStatus(bool openNavigationIfClosed,std::string mapId) {\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Starting checkNvidiaServiceStatus at %ld\", base::homiUtils::getCurrentTimeStamp());\n\n    unitreeMappingStatus = -1;\n    unitreeNavigationStatus = -1;\n    unitreeLocalizationStatus = -1;\n    unitreeUsingMapId = \"\";\n    std_msgs::msg::String msg;\n\n    const std::vector<std::string> commands = {\n        \"mapping/stop\",\n        \"navigation/stop\",\n        \"localization/stop\"\n    };\n    for (const auto& cmd : commands) {\n        sendCommandToUSLAM(cmd);\n        std::this_thread::sleep_for(std::chrono::milliseconds(50));  // 等待 2 秒\n    }\n\n    const std::chrono::seconds total_timeout(3);\n    const std::chrono::milliseconds sleep_time(100);\n    auto start_time = std::chrono::steady_clock::now();\n    // while (rclcpp::ok()) {\n    //     if (std::chrono::duration_cast<std::chrono::seconds>(std::chrono::steady_clock::now() - start_time) >= total_timeout) {\n    //         RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Timeout waiting for status updates.\");\n    //         break;\n    //     }\n    //     RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"unitreeMappingStatus  %d, unitreeNavigationStatus %d, unitreeLocalizationStatus %d, unitreeUsingMapId %s\",unitreeMappingStatus,unitreeNavigationStatus,unitreeLocalizationStatus,unitreeUsingMapId.c_str());\n    //     if (unitreeMappingStatus != -1 && unitreeNavigationStatus != -1 && unitreeLocalizationStatus != -1 && unitreeUsingMapId != \"\") {\n    //         break;\n    //     }\n\t// std::this_thread::sleep_for(sleep_time);\n    // }\n\n    checkNvidia_srv_status(openNavigationIfClosed, mapId);\n}\n\n// ****************************** 和平台消息有关的处理 ******************************************\n// ****************** event是robot_action但是没有actiontype **************************\nvoid RobdogCenter::handleTakePhotos() {\n    ReadMapCfg::getInstance().loadConfig(map_points_path);\n    homi_speech_interface::msg::SIGCEvent photo_msg;\n    photo_msg.event = ReadMapCfg::getInstance().getPhonePoints();\n    if(photo_msg.event.empty()) {\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"get PHOTO_POINT error\");\n        return;\n    } else {\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"get PHOTO_POINT, %s\", photo_msg.event.c_str());\n    }\n    moveToTargetAndPlayAudio(std::make_shared<homi_speech_interface::msg::SIGCEvent>(photo_msg));\n}\n\nvoid RobdogCenter::handleDeliverExpress() {\n    // 取快递\n    ReadMapCfg::getInstance().loadConfig(map_points_path);\n    std::thread t_audio(&RobdogCtrlNode::playAudio,node_,node_->getResourcePath(\"audio/nav/出发去快递站.wav\"));\n    t_audio.detach();\n    homi_speech_interface::msg::SIGCEvent press_msg;\n\n    press_msg.event = ReadMapCfg::getInstance().getPressPoints();\n    if (press_msg.event.empty()) {\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"get PRESS_POINT error\");\n        return;\n    } else {\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"get PRESS_POINT, %s\", press_msg.event.c_str());\n    }\n    moveToTarget(std::make_shared<homi_speech_interface::msg::SIGCEvent>(press_msg));  \n}\n\nvoid RobdogCenter::handleFetchExpress() {\n    // 取快递\n    ReadMapCfg::getInstance().loadConfig(map_points_path);\n    std::thread t_audio(&RobdogCtrlNode::playAudio,node_,node_->getResourcePath(\"audio/nav/fetchExpress.wav\"));\n    t_audio.detach();\n    homi_speech_interface::msg::SIGCEvent press_msg;\n    press_msg.event = ReadMapCfg::getInstance().getPressPoints();\n    if (press_msg.event.empty()) {\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"get PRESS_POINT error\");\n    } else {\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"get PRESS_POINT, %s\", press_msg.event.c_str());\n    }\n    moveToTarget(std::make_shared<homi_speech_interface::msg::SIGCEvent>(press_msg));\n\n}\n\nvoid RobdogCenter::handleReportFamilyMovePoint() {\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"----------------------- handleReportFamilyMovePoint ----------------------- \");\n    robPathStatusTimer_ = node_->create_wall_timer(std::chrono::seconds(1), \n            std::bind(&RobdogCenter::timerRobotPathCallback, this)); // 一边上报路径信息\n    // 一边执行定点移动任务【】\n    readMappingPoints();\n    homi_speech_interface::msg::SIGCEvent familyMove_msg;\n    familyMove_msg.event = ReadMapCfg::getInstance().getFamilyMovePoints();\n    if(familyMove_msg.event.empty()) {\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"get PRESS_POINT error\");\n    } else {\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"get familyMove_POINT, %s\", familyMove_msg.event.c_str());\n    }\n    moveToPatrolPoint(std::make_shared<homi_speech_interface::msg::SIGCEvent>(familyMove_msg));\n}\n\nvoid RobdogCenter::handleBatteryChargingPoint() {\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"----------------------- handleBatteryChargingPoint ----------------------- \");\n    robPathStatusTimer_ = node_->create_wall_timer(std::chrono::seconds(1), \n            std::bind(&RobdogCenter::timerRobotPathCallback, this)); // 一边上报路径信息\n    readMappingPoints();\n    homi_speech_interface::msg::SIGCEvent batteryCharging_msg;\n    batteryCharging_msg.event = ReadMapCfg::getInstance().getBatteryChargingPoints();\n    if(batteryCharging_msg.event.empty()) {\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"get PRESS_POINT error\");\n    } else {\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"get batteryCharging_POINT, %s\", batteryCharging_msg.event.c_str());\n    }\n    moveToBatteryPoint(std::make_shared<homi_speech_interface::msg::SIGCEvent>(batteryCharging_msg));\n}\nvoid RobdogCenter::handleParkPatrol() {\n    // 处理巡逻任务\n    readMappingPoints();\n    homi_speech_interface::msg::SIGCEvent patrol_msg;\n    patrol_msg.event = ReadMapCfg::getInstance().getPatrolPoints();\n    if(patrol_msg.event.empty()) {\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"get PRESS_POINT error\");\n    } else {\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"get PATROL_POINT, %s\", patrol_msg.event.c_str());\n    }\n    // homi_speech_interface::msg::SIGCEvent patrol_msg;\n    // Json::StreamWriterBuilder writerBuilder;\n    // std::string moveMsgs = Json::writeString(writerBuilder, jBody);\n    // patrol_msg.event = moveMsgs;\n    moveToPatrolPoint(std::make_shared<homi_speech_interface::msg::SIGCEvent>(patrol_msg));\n}\n\nvoid RobdogCenter::handlegoHome() {\n    // 处理巡逻任务\n    readMappingPoints();\n    homi_speech_interface::msg::SIGCEvent goHome_msg;\n    goHome_msg.event = ReadMapCfg::getInstance().getGoHomePoints();\n    if(goHome_msg.event.empty()) {\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"get goHome_point error\");\n    } else {\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"get goHome_point, %s\", goHome_msg.event.c_str());\n    }\n    // homi_speech_interface::msg::SIGCEvent goHome_msg;\n    // Json::StreamWriterBuilder writerBuilder;\n    // std::string moveMsgs = Json::writeString(writerBuilder, jBody);\n    // goHome_msg.event = moveMsgs;\n    moveToHomePoint(std::make_shared<homi_speech_interface::msg::SIGCEvent>(goHome_msg));\n}\n\nvoid RobdogCenter::handledeliverCake() {\n    // 处理巡逻任务\n    readMappingPoints();\n    homi_speech_interface::msg::SIGCEvent deliverCake_msg;\n    deliverCake_msg.event = ReadMapCfg::getInstance().getDeliverCakePoints();\n    if(deliverCake_msg.event.empty()) {\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"get deliverCake_point error\");\n    } else {\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"get deliverCake_POINT, %s\", deliverCake_msg.event.c_str());\n    }\n    // homi_speech_interface::msg::SIGCEvent deliverCake_msg;\n    // Json::StreamWriterBuilder writerBuilder;\n    // std::string moveMsgs = Json::writeString(writerBuilder, jBody);\n    // deliverCake_msg.event = moveMsgs;\n    moveTodeliverCakePoint(std::make_shared<homi_speech_interface::msg::SIGCEvent>(deliverCake_msg));\n }\n\n// ****************** event是robot_action但是有actiontype **************************\nvoid RobdogCenter::handleFollowMe(const Json::Value &jBody) {\n    string actionArgument = \"\";\n    const std::vector<std::string> MOVEMENT_COMMANDS = {\"comeClose\", \"goFar\", \"goLeft\", \"goRight\", \"comeHere\"};\n    if (!jBody[\"actionArguement\"].isNull()) {\n        actionArgument = jBody[\"actionArguement\"].asString();\n    } else if (!jBody[\"actionArgument\"].isNull()) {\n        actionArgument = jBody[\"actionArgument\"].asString();\n    }\n    if(RobotState::getInstance().getFollowMeStatus()==\"on\"&&\n        std::find(MOVEMENT_COMMANDS.begin(), MOVEMENT_COMMANDS.end(), actionArgument) != MOVEMENT_COMMANDS.end())\n    {\n        auto message = std_msgs::msg::String();\n        message.data = actionArgument;\n        adjust_distance_publisher_->publish(message);\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Sent movement command '%s' to /adjust_distance topic\", actionArgument.c_str());\n    }\n    else if (RobotState::getInstance().getFollowMeStatus()==\"off\"&&\n        std::find(MOVEMENT_COMMANDS.begin(), MOVEMENT_COMMANDS.end(), actionArgument) != MOVEMENT_COMMANDS.end()){\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"The robot dog is on a fixed-point summoning mission.\");\n        homi_speech_interface::msg::RobdogAction msg;\n        msg.actiontype = \"followMe\";\n        msg.actionargument = \"comeHere\";\n        publishAction(std::make_shared<homi_speech_interface::msg::RobdogAction>(msg));\n        actionFollow(2);\n    }\n    // 开启跟随\n    if (actionArgument == \"on\") {\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Robot is following the user.\");\n        homi_speech_interface::msg::RobdogAction msg;\n        msg.actiontype = \"followMe\";\n        msg.actionargument = \"on\";\n        publishAction(std::make_shared<homi_speech_interface::msg::RobdogAction>(msg));\n        actionFollow(1);\n        RobotState::getInstance().setFollowMeStatus(\"on\");\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"RobotState::getInstance().getFollowMeStatus is %s\",RobotState::getInstance().getFollowMeStatus().c_str());\n        RobotState::getInstance().setCurrentState(RobotStateEnum::FOLLOWME); \n    }\n    // 关闭跟随\n    else if (actionArgument == \"off\") {\n        RCLCPP_INFO_STREAM(rclcpp::get_logger(\"robdog_control\"), \"Robot is not following the user.\");\n        actionFollow(0);\n        RobotState::getInstance().setFollowMeStatus(\"off\");\n        RobotState::getInstance().setCurrentState(RobotStateEnum::NORMAL); \n    } \n    if(actionArgument ==\"on\"||actionArgument ==\"off\"){\n        Json::Value response;\n        response[\"deviceId\"] = RobotState::getInstance().getDeviceId();\n        response[\"domain\"] = \"BUSINESS_REPORT\";\n        response[\"event\"] = \"data_report\";\n        response[\"eventId\"] = to_string(std::chrono::duration_cast<std::chrono::seconds>(std::chrono::system_clock::now().time_since_epoch()).count());\n        response[\"seq\"] = to_string(std::chrono::duration_cast<std::chrono::seconds>(std::chrono::system_clock::now().time_since_epoch()).count());\n        response[\"body\"][\"type\"] = \"followMe\";\n        response[\"body\"][\"data\"][\"status\"]=RobotState::getInstance().getFollowMeStatus();\n        response[\"body\"][\"data\"][\"code\"]=0;\n        response[\"body\"][\"data\"][\"isFirstTime\"]=false;\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"RobotState::getInstance().getFollowMeStatus is %s\",RobotState::getInstance().getFollowMeStatus().c_str());\n        Json::FastWriter writer;\n        std::string jsonString = writer.write(response);\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"),\"Follow me report msg is : %s\", jsonString.c_str());\n        sendRequestData(jsonString);\n    }\n}\n\n// 处理手势识别任务【目前平台只能开启手势识别】\nvoid RobdogCenter::handleGestRec(const Json::Value &jBody) {\n    // if(!jBody[\"gestRec\"].isNull()){\n    //     bool gestRec = jBody[\"gestRec\"].asBool();\n    //     if(gestRec) {\n    //         RobdogHandPosCtrl::getInstance().handPosStart();\n    //     }\n    // }\n    string actionArgument = \"\";\n    if (!jBody[\"actionArguement\"].isNull()) {\n        actionArgument = jBody[\"actionArguement\"].asString();\n    } else if (!jBody[\"actionArgument\"].isNull()) {\n        actionArgument = jBody[\"actionArgument\"].asString();\n    }\n    // 开启手势识别\n    if (actionArgument == \"on\") {\n        RobdogHandPosCtrl::getInstance().handPosStart();\n    }\n    // 关闭手势识别\n    else if (actionArgument == \"off\") {\n        RobdogHandPosCtrl::getInstance().handPosCtrl(\"stop\"); // 关闭手势识别算法\n    } \n}\nvoid RobdogCenter::handleSportMode(const Json::Value &jBody) {\n    try {\n        std::string actionArgument;\n        if (!jBody[\"actionArguement\"].isNull()) {\n            actionArgument = jBody[\"actionArguement\"].asString();\n        } else if (!jBody[\"actionArgument\"].isNull()) {\n            actionArgument = jBody[\"actionArgument\"].asString();\n        }\n        auto it = sportModeMap.find(actionArgument);\n        if (it != sportModeMap.end()) {\n            // publishStatusCtrl(DEEP_CMD_ACTION,sportModeMap[actionArgument],0);\n            // 发送给控制节点\n            homi_speech_interface::msg::RobdogAction msg;\n            msg.actiontype = \"sportMode\";\n            msg.actionargument = actionArgument;\n            // publishAction(msg);\n            publishAction(std::make_shared<homi_speech_interface::msg::RobdogAction>(msg));\n\n        } else\n        throw std::invalid_argument(\"Unknown actionArgument name: \" + actionArgument);\n    } catch (const std::exception &e) {\n        std::cerr << e.what() << '\\n';\n    }\n}\n\nvoid RobdogCenter::handleMotorSkill(const Json::Value &jBody) {\n    try {\n        std::string actionArgument;\n        // 提取动作参数\n        if (!jBody[\"actionArguement\"].isNull()) {\n            actionArgument = jBody[\"actionArguement\"].asString();\n        } else if (!jBody[\"actionArgument\"].isNull()) {\n            actionArgument = jBody[\"actionArgument\"].asString();\n        }\n        // 查找并发布动作\n        auto it = actionMap.find(actionArgument);\n        if (it != actionMap.end()) {\n            // 创建消息\n            homi_speech_interface::msg::RobdogAction msg;\n            msg.actiontype = \"motorSkill\";\n            msg.actionargument = actionArgument; //it->second;\n\n            // 直接在这里执行动作的相应表情和语音\n            // if(actionArgument == \"standUp\"){\n            //     ExpressionChange::getInstance().async_callback_work(\"/home/<USER>/resource/vedio/touch/\",1);\n            // }\n            // else if(actionArgument == \"twistBody\"){\n            //     ExpressionChange::getInstance().async_callback_work(\"/home/<USER>/resource/vedio/twist/\",1);\n            // }\n            // else if(actionArgument == \"greeting\"){\n            //     ExpressionChange::getInstance().async_callback_work(\"/home/<USER>/resource/vedio/hello/\",1);\n            // }\n            // else if(actionArgument == \"sitDown\"){\n            //     ExpressionChange::getInstance().async_callback_work(\"/home/<USER>/resource/左看上看眨眼.mp4\",1);\n            // }\n\n            // publishAction(msg);\n            if (actionArgument == \"standUp\"&&RobotInfoMgr::getInstance().getRobotStatus() == ROBDOG_STATUS_FORCE_CTRL_LEVEL_LOWSPPEED_GAIT) \n                return;\n            if (actionArgument == \"getDown\"&&RobotInfoMgr::getInstance().getRobotStatus() == ROBDOG_STATUS_GETDOWN) \n                return;\n            publishAction(std::make_shared<homi_speech_interface::msg::RobdogAction>(msg));\n\n            // RCLCPP_INFO_STREAM(rclcpp::get_logger(\"robdog_control\"), \"Recv \" << actionArgument << \".\");\n        } else {\n            RCLCPP_INFO_STREAM(rclcpp::get_logger(\"robdog_control\"), \"Unhandled action argument: \" << actionArgument);\n        }\n    } catch (const std::exception& e) {\n        std::cerr << \"Error handling motor skill action: \" << e.what() << '\\n';\n    }\n}\n\nvoid RobdogCenter::handleRLSkill(const Json::Value &jBody) {\n    try {\n        std::string actionArgument;\n        // 提取动作参数\n        if (!jBody[\"actionArgument\"].isNull()) {\n            actionArgument = jBody[\"actionArgument\"].asString();\n        } else if (!jBody[\"actionArguement\"].isNull()) {\n            actionArgument = jBody[\"actionArguement\"].asString();\n        }\n\n        // 定义可处理的动作\n        std::unordered_set<std::string> validActions = {\n            \"obstacleCross\",\n            \"flatGround\",\n            \"exit\"\n        };\n\n        // 创建消息\n        homi_speech_interface::msg::RobdogAction msg;\n        msg.actiontype = \"gaitControl\";\n\n        // 检查动作参数并发布\n        if (validActions.find(actionArgument) != validActions.end()) {\n            msg.actionargument = actionArgument;\n            // publishAction(msg);\n            publishAction(std::make_shared<homi_speech_interface::msg::RobdogAction>(msg));\n\n        }\n    } catch (const std::exception& e) {\n        std::cerr << \"Error handling gait control action: \" << e.what() << '\\n';\n    }\n}\n\nvoid RobdogCenter::handleAcccompany(const Json::Value &jBody) {\n    std::string actionArgument;\n    if (!jBody[\"actionArgument\"].isNull()) {\n        actionArgument = jBody[\"actionArgument\"].asString();\n    } else if (!jBody[\"actionArguement\"].isNull()) {\n        actionArgument = jBody[\"actionArguement\"].asString();\n    } else {\n        RCLCPP_WARN(rclcpp::get_logger(\"robdog_center_mgr\"), \"Neither actionArgument nor actionArguement found in the input\");\n        return;\n    }\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_center_mgr\"), \"Received action argument: %s\", actionArgument.c_str());\n    if (actionArgument == \"on\") {\n        // 设置3小时内的不再主动求陪伴状态\n        quiet_for_three_hours_ = true;\n        auto now = std::chrono::system_clock::now();\n        auto threeHoursLater = now + std::chrono::hours(3);\n        quiet_for_three_hours_until_ = std::chrono::system_clock::to_time_t(threeHoursLater);\n\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"User does not want to be accompanied for the next 3 hours.\");\n    } else if (actionArgument == \"off\") {\n        // 用户需要陪伴的逻辑\n        quiet_for_three_hours_ = false;\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"User wants to be accompanied.\");\n    } else {\n        RCLCPP_WARN(rclcpp::get_logger(\"robdog_control\"), \"Invalid actionArgument received.\");\n    }\n}\n\n\nint RobdogCenter::checkTripReady(){\n    // 如224001: 电量不足  224002：设备当前位置与导航时不一致 ...\n    return 1;\n}\n\n\n\n\n//  ************************* \nvoid RobdogCenter::checkStatusWatchdog() {\n    if (watchDogMonitor) {\n        // 检查是否超过2秒未收到消息\n        // if ((ros::Time::now() - lastMoveMessageTime).toSec() > 2.0) {\n        //     ROS_WARN(\"No move message for more than two seconds. Stop Move.\");\n        if ((node_->now() - lastMoveMessageTime).seconds() > 2.0) {\n            RCLCPP_WARN(rclcpp::get_logger(\"robdog_control\"), \"No move message for more than two seconds. Stop Move.\");\n            watchDogMonitor = false; // 停止监测\n\n            current_continue_msg_.event = \"stopActionDelay\";\n            continueMoveCmd_pub->publish(current_continue_msg_);\n\n            RCLCPP_WARN(rclcpp::get_logger(\"robdog_control\"), \"publish stopActionDelay.\");\n        }\n    }\n}\n\n//  ************************* 移动方式（步进还是持续移动） ************************* \nvoid RobdogCenter::Proceationtype(int actiontype, int step) {\n    // 步进【基本上是语音发送的指令，直接把twist传到ctrl执行即可】\n    if (actiontype == 2) {\n        // RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Published velocity: linear.x = %f, linear.y = %f, angular.z =\n        // %f, angular.x = %f, angular.y = %f, angular.z = %f\",\n        // current_twist_msg_.linear.x, current_twist_msg_.linear.y,\n        // current_twist_msg_.linear.z, current_twist_msg_.angular.x,\n        // current_twist_msg_.angular.y, current_twist_msg_.angular.z);\n        // RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"count = %d\", step);\n        \n        sleepForDuration(); // 暂停2秒 resting_time\n        // 方式一：直接发送速度【配置在qt里面的方法】\n        int count = std::abs(step); // 假设肯定不会发送0值\n        setTotalCount(count);\n        triggerTimerCallback();\n\n        // 方式二：利用云深处新提供的接口【移动固定距离经常不灵，而且向左右移动固定距离是小碎步，不是明显的一步】\n        // current_continue_msg_.event = \"stepMode\";\n        // continueMoveCmd_pub.publish(current_continue_msg_);\n    }\n    // 连续移动【摇杆的指令，要通过轴指令控制狗】\n    else if (actiontype == 1) {\n        // 把current_continue_msg_传给控制节点\n        watchDogMonitor = true;\n        // lastMoveMessageTime = ros::Time::now();\n        lastMoveMessageTime = node_->now();\n        continueMoveCmd_pub->publish(current_continue_msg_);\n    } else {\n        if (current_continue_msg_.event == \"robot_view\")\n        {\n            node_->setRobotView(0, 0, 0);\n        }\n        else if (current_continue_msg_.event == \"robot_move\")\n        {\n            node_->setRobotMove(0, 0, 0);\n        }\n\n        current_continue_msg_.event = \"stopAction\";\n        continueMoveCmd_pub->publish(current_continue_msg_);\n    }\n}\n\n// ****************** event是data_update **************************\nvoid RobdogCenter::update_emergency_contacts(int updateType,const std::string& entityId, const Json::Value& emergencyContact) {\n    std::unique_ptr<SQLiteDB> db=std::make_unique<SQLiteDB>(\"robdog.db\",rclcpp::get_logger(\"robdog_control\"));\n    if (!db->open()) {\n        RCLCPP_ERROR(rclcpp::get_logger(\"robdog_control\"),\"robdog.db open or create failed\");\n        return ;\n    }\n    switch (updateType)\n    {\n    case 1:{\n        db->createTable(\"emergency_contacts\", \"id TEXT PRIMARY KEY, nickName TEXT, phone TEXT\");\n        std::map<std::string, std::string> values={{\"id\", entityId},{\"nickName\", emergencyContact[\"nickName\"].asString()},{\"phone\", emergencyContact[\"phone\"].asString()}};\n        if (db->insert(\"emergency_contacts\", values)) {\n            RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Emergency contact created successfully.\" );\n        } else {\n            RCLCPP_ERROR(rclcpp::get_logger(\"robdog_control\"),\"Failed to create emergency contact.\" );\n        }\n        break;\n    }\n    case 2:{\n        std::string condition = \"id = '\" + entityId + \"'\";\n        std::map<std::string, std::string> values = {{\"nickName\", emergencyContact[\"nickName\"].asString()},{\"phone\", emergencyContact[\"phone\"].asString()}};\n        if (db->update(\"emergency_contacts\", values, condition)) {\n            RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Emergency contact update successfully.\" );\n        } else {\n            RCLCPP_ERROR(rclcpp::get_logger(\"robdog_control\"),\"Failed to update emergency contact.\" );\n        }\n        break;\n    }\n    case 3:{\n        std::string condition = \"id = '\" + entityId + \"'\";\n        if (db->remove(\"emergency_contacts\", condition)) {\n            RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Emergency contact delete successfully.\" );\n        } else {\n            RCLCPP_ERROR(rclcpp::get_logger(\"robdog_control\"),\"Failed to delete emergency contact.\" );\n        }        \n        break;\n    }\n    default:\n        break;\n    }\n    db->close();\n}\n\n// 一些发布函数\n//  ************************* 发给机器狗控制节点 ************************* \nvoid RobdogCenter::publishAction(const homi_speech_interface::msg::RobdogAction::SharedPtr robdogAction) {\n    actionCmd_pub->publish(*robdogAction);\n    // RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Published ActionType: %s, ActionArgument: %s\",robdogAction.actiontype.c_str(),robdogAction.actionargument.c_str());\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Published ActionType: %s, ActionArgument: %s\",\n                    robdogAction->actiontype.c_str(),\n                    robdogAction->actionargument.c_str());\n}\n\nvoid RobdogCenter::publishVelocity(const geometry_msgs::msg::Twist::SharedPtr velocity) {\n    velCmd_pub->publish(*velocity);\n    // RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Published velocity: linear.x = %f, linear.y = %f, angular.z = \"\n    //         \"%f, angular.x = %f, angular.y = %f, angular.z = %f\",velocity.linear.x, velocity.linear.y, velocity.linear.z,velocity.angular.x, velocity.angular.y, velocity.angular.z);\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \n                    \"Published velocity:\\n\"\n                    \"Linear Velocity: x = %f, y = %f, z = %f\\n\"\n                    \"Angular Velocity: x = %f, y = %f, z = %f\",\n                    velocity->linear.x, velocity->linear.y, velocity->linear.z,\n                    velocity->angular.x, velocity->angular.y, velocity->angular.z);\n}\n\nvoid RobdogCenter::publishStatusCtrl(int cmd, int value, int exvalue) {\n    homi_speech_interface::msg::ProprietySet msg;\n    msg.cmd = cmd;\n    msg.value = value;\n    msg.exvalue = exvalue;\n    status_pub_->publish(msg);\n    // RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"cmd [0x%x] value[%d] exvalu[%d]\", cmd, value, exvalue);\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"cmd [0x%x] value[%d] exvalue[%d]\", cmd, value, exvalue);\n}\n\n// 持续发送（向APP）发送设备信息\nvoid RobdogCenter::publishProperties2APP() {\n    homi_speech_interface::msg::ProperToApp resMsg;\n    Json::Value response(Json::objectValue);\n    Json::Value newBody(Json::objectValue);\n    newBody[\"properties\"][\"networkStatus\"][\"wifiState\"] = RobotState::getInstance().getWifiSwitch();\n    newBody[\"properties\"][\"networkStatus\"][\"mobileDataState\"] = RobotState::getInstance().getMobileDataSwitch();\n    newBody[\"properties\"][\"networkStatus\"][\"wifiName\"] = RobotState::getInstance().getWifiName();\n    newBody[\"properties\"][\"networkStatus\"][\"isWifiConnect\"] = (RobotState::getInstance().getWifiSwitch() == \"on\");\n    newBody[\"properties\"][\"battery\"][\"power\"] = RobotInfoMgr::getInstance().getBatteryLevel();\n    newBody[\"properties\"][\"battery\"][\"status\"] = RobotState::getInstance().getBatteryChargeStatus();\n    newBody[\"properties\"][\"connect_info\"][\"changeType\"] = RobotState::getInstance().getUserConnectStatus();\n    newBody[\"properties\"][\"connect_info\"][\"phone\"] = RobotState::getInstance().getUserPhoneNumber();\n\n    // geometry_msgs::Pose pos = RobotState::getInstance().getCurRobotPose();\n\n    //将 geometry_msgs::Quaternion 转换为 tf::Quaternion\n    // tf::Quaternion quat;\n    // tf::quaternionMsgToTF(pos.orientation, quat);\n    // 从四元数计算出欧拉角 (roll, pitch, yaw)\n    // tf::Matrix3x3 mat(quat);\n    // double roll, pitch, yaw;\n    // mat.getRPY(roll, pitch, yaw);\n    // newBody[\"properties\"][\"robot_pos\"][\"x\"] = pos.position.x;\n    // newBody[\"properties\"][\"robot_pos\"][\"y\"] = pos.position.y;\n    // newBody[\"properties\"][\"robot_pos\"][\"angle\"] = (yaw * 180.0 / M_PI);\n\n    response[\"body\"] = newBody;\n    // 将 Json::Value 转换成字符串\n    Json::FastWriter writer;\n    std::string jsonString = writer.write(response);\n    resMsg.device_prope = jsonString;\n    prope2app_pub->publish(resMsg);\n}\n\n\n//  ****************** 异步回调函数 ****************** \n\n// 创建接收到服务器(平台的交互)回复的异步回调函数\nvoid RobdogCenter::plat_srv_callback(rclcpp::Client<homi_speech_interface::srv::SIGCData>::SharedFuture response) // (std::shared_ptr<homi_speech_interface::srv::SIGCData::Request> response) \n{\n    // 使用response的get()获取\n    auto response_value = response.get();\n    int errorCode = response_value->error_code;\n    if (errorCode!=0)\n        RCLCPP_WARN(rclcpp::get_logger(\"robdog_control\"),\"service assistant return error code %d\",errorCode);\n}\n\nvoid RobdogCenter::takephoto_srv_callback(rclcpp::Client<homi_speech_interface::srv::AssistantTakePhoto>::SharedFuture response)\n{\n    // 使用response的get()获取\n    auto response_value = response.get();\n    int errorCode = response_value->error_code;\n    RCLCPP_WARN(rclcpp::get_logger(\"robdog_control\"),\"service assistant return error code %d\",errorCode);\n}\n\nvoid RobdogCenter::net_srv_callback(rclcpp::Client<homi_speech_interface::srv::NetCtrl>::SharedFuture response)\n{\n    // 使用response的get()获取\n    auto response_value = response.get();\n    int errorCode = response_value->error_code;\n    g_netctrl_ret=response_value->result.c_str();\n    RCLCPP_WARN(rclcpp::get_logger(\"robdog_control\"),\"service assistant return error code %d\",errorCode);\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Response result from net_ctrl_srv: %s\", response_value->result.c_str());\n    processNetworkStatusData();\n}\n\nvoid RobdogCenter::brocast_srv_callback(rclcpp::Client<homi_speech_interface::srv::AssistantSpeechText>::SharedFuture response)\n{\n    // 使用response的get()获取\n    auto response_value = response.get();\n    std::string sectionId  = response_value->section_id;\n    RobotState::getInstance().setSectionId(sectionId);\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Received sectionId from server: %s\", sectionId.c_str());\n\n}\n\nstd::string RobdogCenter::getStatusDescription(const std::string& input) {\n    static const std::unordered_map<std::string, std::string> statusMap = buildStatusMapping();\n\n    auto it = statusMap.find(input);\n    if (it != statusMap.end()) {\n        return it->second;  // 返回对应的中文描述\n    } else {\n        return \"未知状态\";  // 如果未找到，返回默认值\n    }\n}\n\nint RobdogCenter::getCodeForStatus(const std::string& status) {\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"getCodeForStatus: %s\", status.c_str());\n    auto it = statusToCodesMap.find(status);\n    if (it != statusToCodesMap.end() && !it->second.empty()) {\n        return it->second[0];  // 返回第一个匹配的 code\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"getCodeForStatus: %d\", it->second[0]);\n    }\n    else {\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"STATUS NOT IN RANGE: %s\", status.c_str());\n        return -1;\n    }\n}\n\nvoid RobdogCenter::uslamServerLogCallback(const std_msgs::msg::String::SharedPtr msg) {\n    std::string statusStr = msg->data;\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Received server log: %s\", statusStr.c_str());\n\n    int status;\n    std::string mapId;\n\n    // Common 获取地图ID处理\n   if (statusStr.find(\"common/get_map_id/map_id/\") != std::string::npos) {\n        std::string prefix = \"common/get_map_id/map_id/\";\n        std::string mapId = statusStr.substr(prefix.size());\n        unitreeUsingMapId = mapId;\n        RobotState::getInstance().setMapId(unitreeUsingMapId);\n        update_map_points_path();\n        RobotState::getInstance().saveConfig();\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Current map ID updated: %s\", mapId.c_str());\n    }\n\n    if (sscanf(statusStr.c_str(), \"mapping/get_status/status/%d\", &status) == 1) {\n            unitreeMappingStatus = status;\n            RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Mapping status updated: %d\", status);\n    } else if (sscanf(statusStr.c_str(), \"localization/get_status/status/%d\", &status) == 1) {\n            unitreeLocalizationStatus = status;\n            RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Localization status updated: %d\", status);\n    } else if (sscanf(statusStr.c_str(), \"navigation/get_status/status/%d\", &status) == 1) {\n            unitreeNavigationStatus = status;\n            RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Navigation status updated: %d\", status);\n    }\n\n    Json::Value params;\n    Json::Value msgValue;\n    int taskStatusCode = getCodeForStatus(statusStr);\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Status code: %d\", taskStatusCode);\n    std::string statusDescription = getStatusDescription(statusStr);\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Status code: %d, Description: %s\", taskStatusCode, statusDescription.c_str());\n    std::string current_mapid = RobotState::getInstance().getMapId();\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Current map ID: %s\", current_mapid.c_str());\n    if (taskStatusCode != -1) { \n        params[\"code\"] = taskStatusCode;\n        msgValue[\"mapId\"] = current_mapid;\n        msgValue[\"msg\"] = statusDescription;\n        params[\"msg\"] = msgValue;\n        handleTaskStatusCode(taskStatusCode, msgValue);\n    }\n}\n\nvoid RobdogCenter::devAlarmReportCallback(const std_msgs::msg::String::SharedPtr msg) {\n    std::unordered_map<int, int> mapping = {{4101, 2},{4102, 3},{4106, 4}};\n    Json::Value response;\n    response[\"deviceId\"] = RobotState::getInstance().getDeviceId();\n    response[\"domain\"] = \"DEVICE_ALARM\";\n    response[\"event\"] = \"device_alarm_report\";\n    response[\"eventId\"] = \"robdog_plat_\" + to_string(std::chrono::duration_cast<std::chrono::seconds>(std::chrono::system_clock::now().time_since_epoch()).count());\n    response[\"seq\"] = to_string(std::chrono::duration_cast<std::chrono::seconds>(std::chrono::system_clock::now().time_since_epoch()).count());\n    Json::Value body;\n    Json::Reader reader;\n\n    std::string strMsg = msg->data;\n    if (!reader.parse(strMsg, body)) \n        RCLCPP_WARN(rclcpp::get_logger(\"robdog_control\"),\"Can not parse warning msg\");\n    auto isFirstTime=false;\n    if (body[\"alarmCode\"].asInt()==14101||body[\"alarmCode\"].asInt()==14102){\n        body[\"alarmCode\"]=body[\"alarmCode\"].asInt()-10000;\n        isFirstTime=true;\n    }\n    if (body[\"alarmCode\"].asInt()==4106)\n        isFirstTime=true;\n\n    response[\"body\"] = body;\n    Json::FastWriter writer;\n    std::string jsonString = writer.write(response);\n    sendRequestData(jsonString);\n    if (body[\"alarmCode\"].asInt()<=4106&&body[\"alarmCode\"].asInt()>=4101){\n        RobotState::getInstance().setFollowMeStatus(\"off\");\n        RobotState::getInstance().setCurrentState(RobotStateEnum::NORMAL); \n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"),\"report follow me off status : %d\", body[\"alarmCode\"].asInt());\n        Json::Value response;\n        response[\"deviceId\"] = RobotState::getInstance().getDeviceId();\n        response[\"domain\"] = \"BUSINESS_REPORT\";\n        response[\"event\"] = \"data_report\";\n        response[\"eventId\"] = to_string(std::chrono::duration_cast<std::chrono::seconds>(std::chrono::system_clock::now().time_since_epoch()).count());\n        response[\"seq\"] = to_string(std::chrono::duration_cast<std::chrono::seconds>(std::chrono::system_clock::now().time_since_epoch()).count());\n        response[\"body\"][\"type\"] = \"followMe\";\n        response[\"body\"][\"data\"][\"status\"]=RobotState::getInstance().getFollowMeStatus();\n        int mappedValue = -1;\n        if (mapping.find(body[\"alarmCode\"].asInt()) != mapping.end()) \n            mappedValue = mapping[body[\"alarmCode\"].asInt()];\n        response[\"body\"][\"data\"][\"code\"]= mappedValue;\n        response[\"body\"][\"data\"][\"isFirstTime\"]=isFirstTime;\n        Json::FastWriter writer;\n        std::string jsonString = writer.write(response);\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"),\"Follow me  warning msg is : %s\", jsonString.c_str());\n        sendRequestData(jsonString);\n    }\n}\nvoid RobdogCenter::handleDataReportCtl(const Json::Value &inValue){\n    try {\n        if (inValue[\"body\"][\"actionType\"].asString() == \"start\") {\n            const std::string device_id = inValue[\"deviceId\"].asString();\n            active_types_.clear();\n            for (const auto& type : inValue[\"body\"][\"dataTypeList\"]) {\n                active_types_.insert(type.asString());\n            }\n\n            status_.rtk.last = -1;    // 设置为非法值确保首次上报\n            status_.rtk.current = status_.rtk.current; // 保持当前实际值\n            status_.indoor.last = -1; // indoorSignal同样设置为非法值确保首次上报\n            status_.indoor.current = status_.indoor.current; // 保持当前实际值\n            status_.temperature.last = \"\"; // 空值强制触发温度上报\n\n            status_.temperature.last =RobotInfoMgr::getInstance().getTempStatus();\n            if (!pos_tem_rep_timer_) {\n                pos_tem_rep_timer_ = node_->create_wall_timer(\n                2s, \n                [this, device_id]() { check_pos_and_temp_status(device_id); }\n                );\n                check_pos_and_temp_status(device_id);//fisrt check;\n                RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"position and temerature  status monitor START\");\n            }\n        } else if (inValue[\"body\"][\"actionType\"].asString()== \"stop\") {\n            if (pos_tem_rep_timer_) {\n                pos_tem_rep_timer_->cancel();\n                pos_tem_rep_timer_.reset();\n                RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"position and temerature status monitor STOP\");\n            }\n            active_types_.clear();\n        }\n    } catch (const Json::LogicError &e) {\n        RCLCPP_ERROR(rclcpp::get_logger(\"robdog_control\"), \"handleDataReportCtl Logic error: %s\", e.what());\n    } catch (const Json::RuntimeError &e) {\n        RCLCPP_ERROR(rclcpp::get_logger(\"robdog_control\"), \"handleDataReportCtl Runtime error: %s\", e.what());\n    }\n}\n\nvoid RobdogCenter::check_pos_and_temp_status(const std::string& device_id)\n{\n    if (active_types_.empty()) return;\n    Json::Value report_body;\n    bool has_changes = false;\n    if (active_types_.count(\"positioningSignal\")) {\n        const bool last_positive = (status_.rtk.last > 0);\n        const bool curr_positive = (status_.rtk.current > 0);\n        RCLCPP_INFO_THROTTLE(node_->get_logger(),*node_->get_clock(),5000,\"status_.rtk.last is %d,status_.rtk.current is %d,last_positive is %d,curr_positive is %d\",status_.rtk.last,status_.rtk.current,last_positive,curr_positive);        \n        if (last_positive != curr_positive || status_.rtk.last == -2) {\n            report_body[\"positioningSignal\"][\"status\"] = status_.rtk.current > 0 ? 1 : 0;\n            status_.rtk.last = status_.rtk.current;\n            has_changes = true;\n        }\n    }\n    if (active_types_.count(\"temperature\")) {\n        status_.temperature.current = RobotInfoMgr::getInstance().getTempStatus();\n        RCLCPP_INFO_THROTTLE(node_->get_logger(),*node_->get_clock(),3000,\"CURRENT RobotInfoMgr::getInstance().getTempStatus() is %s\",status_.temperature.current.c_str());\n        if (status_.temperature.current != status_.temperature.last) {\n            report_body[\"temperature\"][\"status\"] = status_.temperature.current;\n            status_.temperature.last = status_.temperature.current;\n            has_changes = true; \n        }\n    }\n    if (active_types_.count(\"indoorSignal\")) {\n        status_.indoor.current = repositioningResult.load();\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"CURRENT repositioningResult is %d\",status_.indoor.current);\n        const bool last_positive = (status_.indoor.last > 0);\n        const bool curr_positive = (status_.indoor.current > 0);\n        if (last_positive != curr_positive || status_.indoor.last == -1) {\n            report_body[\"indoorSignal\"][\"status\"] = status_.indoor.current ;\n            status_.indoor.last = status_.indoor.current;\n            has_changes = true;\n        }\n    }\n    if (has_changes) {\n        Json::Value report;\n        report[\"body\"]=report_body;\n        report[\"eventId\"] = to_string(std::chrono::duration_cast<std::chrono::seconds>(std::chrono::system_clock::now().time_since_epoch()).count());\n        report[\"seq\"] = to_string(std::chrono::duration_cast<std::chrono::seconds>(std::chrono::system_clock::now().time_since_epoch()).count());\n        report[\"deviceId\"]=device_id;\n        report[\"event\"]=\"data_report_v2\";\n        report[\"domain\"]=\"BUSINESS_REPORT\";\n        report[\"response\"]=false;\n        Json::FastWriter writer;\n        std::string jsonString = writer.write(report);\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"handleDataReportCtl the status of temp or position has changed.Report msg:%s\",jsonString.c_str());\n        sendRequestData(jsonString);\n    }\n}\n\nvoid RobdogCenter::ntripExpireDayCallback(const std_msgs::msg::String::SharedPtr msg) {\n    if (!msg) {\n        return;\n    }\n    std::string rtkAccountExipreDay_tmp = msg->data;\n    if (rtkAccountExipreDay_tmp != rtkAccountExipreDay) {\n        rtkAccountExipreDay = rtkAccountExipreDay_tmp;\n        rtkAccountStatusReport = true;\n    }\n}\n\nvoid RobdogCenter::ntripStatusCallback(const std_msgs::msg::Int64::SharedPtr msg) {\n    if (!msg) {\n        return;\n    }\n    int64_t rtkAccountStatus_tmp = msg->data;\n    if (rtkAccountStatus_tmp != rtkAccountStatus) {\n        rtkAccountStatus = rtkAccountStatus_tmp;\n        rtkAccountStatusReport = true;\n    }\n}\n\nvoid RobdogCenter::ntripAccountCallback(const std_msgs::msg::String::SharedPtr msg) {\n    if (!msg) {\n        return;\n    }\n    std::string data = msg->data;\n\n    // 检查是否无账号\n    if (data == \"No Account Configured\") {\n        if(rtkAccount != \"\" || rtkPass != \"\") {\n            rtkAccount = \"\";\n            rtkPass = \"\";\n            rtkAccountStatusReport = true;\n        }\n        return;\n    }\n    std::regex pattern(R\"(User:\\s*(\\w+)\\s*\\|\\s*Pwd:\\s*(\\w+))\");\n    std::smatch matches;\n\n    if (std::regex_search(data, matches, pattern) && matches.size() == 3) {\n        std::string rtkAccount_tmp = matches[1].str();\n        std::string rtkPass_tmp = matches[2].str();\n        if(rtkAccount != rtkAccount_tmp) {\n            rtkAccount = rtkAccount_tmp;\n            rtkAccountStatusReport = true;\n        }\n        if(rtkPass != rtkPass_tmp) {\n            rtkPass = rtkPass_tmp;\n            rtkAccountStatusReport = true;\n        }\n    } else {\n        return;\n    }\n}\n\nvoid RobdogCenter::rtkAccountReportTimerCallback() {\n    Json::Value properties;\n\n    if(rtkAccountStatusReport == true) {\n        handleRtkAccountRead(properties);\n    }\n}\n\n// ****************** WiFi相关处理方法 **************************\n\n// 处理WiFi列表查询 ：平台请求 → ROS2服务调用 → 接收响应 → 转换格式 → 发送给平台\nvoid RobdogCenter::handleWifiListQuery(const Json::Value &inValue, const Json::Value &jBody) {\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"handleWifiListQuery called\");\n\n    try {\n        // 检查节点指针是否有效\n        if (!node_) {\n            RCLCPP_ERROR(rclcpp::get_logger(\"robdog_control\"), \"Node pointer is null\");\n            sendWifiResponse(inValue, WifiQueryStatus::FAILED);\n            return;\n        }\n\n        // 检查网络服务客户端是否可用\n        if (!net_client) {\n            RCLCPP_ERROR(rclcpp::get_logger(\"robdog_control\"), \"Network client is null\");\n            sendWifiResponse(inValue, WifiQueryStatus::FAILED);\n            return;\n        }\n\n        // 等待服务可用\n        if (!net_client->wait_for_service(std::chrono::seconds(2))) {\n            RCLCPP_ERROR(rclcpp::get_logger(\"robdog_control\"), \"Network service not available\");\n            sendWifiResponse(inValue, WifiQueryStatus::FAILED);\n            return;\n        }\n\n        // 创建服务请求\n        auto request = std::make_shared<homi_speech_interface::srv::NetCtrl::Request>();\n        request->data = \"{\\\"command\\\": \\\"scanWifiNetworks\\\"}\";\n\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Calling network service for WiFi scan\");\n\n        // 使用异步调用和回调函数的方式，参考其他代码实现\n        auto future = net_client->async_send_request(request,\n            [this, inValue](rclcpp::Client<homi_speech_interface::srv::NetCtrl>::SharedFuture response) {\n                try {\n                    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Network service response received\");\n                    \n                    auto serviceResponse = response.get();\n                    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Network service result: %s\", serviceResponse->result.c_str());\n                    \n\n                    // 转换服务响应为平台格式并发送\n                    Json::Value platformResponse = convertServiceResponseToPlatformFormat(inValue, serviceResponse->result);\n                    Json::StreamWriterBuilder writerBuilder;\n                    std::string responseStr = Json::writeString(writerBuilder, platformResponse);\n                    \n                    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Platform response converted, sending to platform...\");\n                    sendRequestData(responseStr);\n\n                    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"WiFi list query completed successfully\");\n\n                } catch (const std::exception& e) {\n                    RCLCPP_ERROR(rclcpp::get_logger(\"robdog_control\"), \"Error in WiFi service callback: %s\", e.what());\n                    sendWifiResponse(inValue, WifiQueryStatus::FAILED);\n                }\n            });\n\n    } catch (const std::exception& e) {\n        RCLCPP_ERROR(rclcpp::get_logger(\"robdog_control\"), \"Error in handleWifiListQuery: %s\", e.what());\n        sendWifiResponse(inValue, WifiQueryStatus::FAILED);\n    }\n}\n\n// 数据转换方法：将ROS2服务响应转换为平台格式\nJson::Value RobdogCenter::convertServiceResponseToPlatformFormat(const Json::Value &inValue, const std::string &serviceResponse) {\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Converting service response to platform format\");\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Raw service response: %s\", serviceResponse.c_str());\n\n    // 构建基础响应结构\n    Json::Value response;\n    response[\"deviceId\"] = inValue[\"deviceId\"];\n    response[\"domain\"] = \"DEVICE_PROPERTIES\";\n    response[\"event\"] = \"wifi_list_query\";\n    response[\"eventId\"] = inValue[\"eventId\"];\n    response[\"seq\"] = std::to_string(std::chrono::duration_cast<std::chrono::milliseconds>(\n        std::chrono::system_clock::now().time_since_epoch()).count());\n\n    // 解析服务响应\n    Json::Reader reader;\n    Json::Value serviceResult;\n    if (!reader.parse(serviceResponse, serviceResult)) {\n        RCLCPP_ERROR(rclcpp::get_logger(\"robdog_control\"), \"Failed to parse service response JSON\");\n        response[\"body\"][\"queryStatus\"] = 2; // 查询失败\n        response[\"body\"][\"wifiList\"] = Json::Value(Json::arrayValue);\n        return response;\n    }\n\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Service response JSON parsed successfully\");\n\n    // 根据服务响应状态进行转换\n    std::string status = serviceResult.get(\"status\", \"\").asString();\n    int code = serviceResult.get(\"code\", 500).asInt();\n    std::string message = serviceResult.get(\"message\", \"\").asString();\n    bool cached = serviceResult.get(\"cached\", false).asBool();\n    \n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Service status: %s, code: %d, message: %s, cached: %s\", \n                status.c_str(), code, message.c_str(), cached ? \"true\" : \"false\");\n\n    if (status == \"scanning\") {\n        // 扫描中状态（首次启动扫描或扫描进行中）\n        response[\"body\"][\"queryStatus\"] = 0; // 查询中\n        response[\"body\"][\"wifiList\"] = Json::Value(Json::arrayValue);\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Service response: scanning (code: %d) - %s\", code, message.c_str());\n    } else if (status == \"completed\") {\n        // 扫描完成状态\n        response[\"body\"][\"queryStatus\"] = 1; // 查询成功\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Service response: completed (cached: %s), processing networks...\", \n                    cached ? \"true\" : \"false\");\n\n        // 转换networks数组为wifiList\n        Json::Value wifiList(Json::arrayValue);\n        if (serviceResult.isMember(\"networks\") && serviceResult[\"networks\"].isArray()) {\n            const Json::Value& networks = serviceResult[\"networks\"];\n            int totalCount = serviceResult.get(\"count\", 0).asInt();\n            RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Found %d networks in service response (total count: %d)\", \n                        networks.size(), totalCount);\n\n            for (const auto& network : networks) {\n                if (network.isMember(\"ssid\") && network.isMember(\"security\")) {\n                    Json::Value wifiInfo;\n                    wifiInfo[\"wifiName\"] = network[\"ssid\"].asString();\n\n                    // 直接使用signal_level作为信号强度\n                    int signal = network[\"signal\"].asInt();\n                    int signalStrength = 0;\n                    \n                    // 检查是否存在signal_level字段\n                    if (network.isMember(\"signal_level\")) {\n                        signalStrength = network[\"signal_level\"].asInt();\n                        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Using direct signal_level: %d\", signalStrength);\n                    } else {\n                        // 兼容旧版本：如果没有signal_level字段，保留原有转换逻辑\n                        signalStrength = -120 + (signal * 110 / 100); // 将0-100%转换为-120到-10dBm\n                        if (signalStrength > -10) signalStrength = -10;\n                        if (signalStrength < -120) signalStrength = -120;\n                        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Converted signal %d%% to signalStrength: %d dBm\", signal, signalStrength);\n                    }\n                    wifiInfo[\"signalStrength\"] = signalStrength;\n\n                    // 判断加密状态\n                    std::string security = network[\"security\"].asString();\n                    int encryptState = 0; // 默认未加密\n                    if (!security.empty() && security != \"none\" && security != \"--\") {\n                        encryptState = 1; // 已加密\n                    }\n                    wifiInfo[\"encryptState\"] = encryptState;\n\n                    // 记录BSSID信息（如果存在）\n                    if (network.isMember(\"bssid\")) {\n                        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Processing WiFi: %s (BSSID: %s), signal: %d%%, security: %s, encrypted: %d\", \n                                    network[\"ssid\"].asString().c_str(), network[\"bssid\"].asString().c_str(), \n                                    signal, security.c_str(), encryptState);\n                    } else {\n                        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Processing WiFi: %s, signal: %d%%, security: %s, encrypted: %d\", \n                                    network[\"ssid\"].asString().c_str(), signal, security.c_str(), encryptState);\n                    }\n\n                    // 根据协议要求：本体过滤未加密的WiFi，只返回加密的WiFi\n                    if (encryptState == 1) {\n                        wifiList.append(wifiInfo);\n                    }\n                }\n            }\n        }\n\n        response[\"body\"][\"wifiList\"] = wifiList;\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Service response: completed, found %u encrypted networks (cached: %s)\", \n                    wifiList.size(), cached ? \"true\" : \"false\");\n    } else if (status == \"error\") {\n        // 错误状态\n        response[\"body\"][\"queryStatus\"] = 2; // 查询失败\n        response[\"body\"][\"wifiList\"] = Json::Value(Json::arrayValue);\n        std::string errorMsg = serviceResult.get(\"error\", \"\").asString();\n        RCLCPP_ERROR(rclcpp::get_logger(\"robdog_control\"), \"Service response: error (code: %d) - %s\", code, errorMsg.c_str());\n    } else {\n        // 未知状态\n        response[\"body\"][\"queryStatus\"] = 2; // 查询失败\n        response[\"body\"][\"wifiList\"] = Json::Value(Json::arrayValue);\n        RCLCPP_ERROR(rclcpp::get_logger(\"robdog_control\"), \"Service response: unknown status '%s' (code: %d)\", status.c_str(), code);\n    }\n\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"convertServiceResponseToPlatformFormat completed\");\n    return response;\n}\n\n// WiFi响应封装统一函数\nvoid RobdogCenter::sendWifiResponse(const Json::Value &inValue, WifiQueryStatus queryStatus, const Json::Value &wifiList) {\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"sendWifiResponse called - Status: %d, WiFi count: %d\", \n                static_cast<int>(queryStatus), wifiList.size());\n    \n    Json::Value response;\n    response[\"deviceId\"] = inValue[\"deviceId\"];\n    response[\"domain\"] = \"DEVICE_PROPERTIES\";\n    response[\"event\"] = \"wifi_list_query\";\n    response[\"eventId\"] = inValue[\"eventId\"];\n    response[\"seq\"] = std::to_string(std::chrono::duration_cast<std::chrono::milliseconds>(\n        std::chrono::system_clock::now().time_since_epoch()).count());\n\n    // 严格按照平台响应格式\n    response[\"body\"][\"queryStatus\"] = static_cast<int>(queryStatus);\n    response[\"body\"][\"wifiList\"] = wifiList;\n\n    Json::StreamWriterBuilder writerBuilder;\n    std::string responseStr = Json::writeString(writerBuilder, response);\n    \n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"sendWifiResponse - Sending response: %s\", responseStr.c_str());\n    \n    sendRequestData(responseStr);\n    \n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"sendWifiResponse completed\");\n}\n\n\n\n\n\n// 处理WiFi设置\nvoid RobdogCenter::handleWifiSet(const Json::Value &inValue, const Json::Value &jBody) {\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"handleWifiSet called\");\n\n    try {\n        // 获取WiFi参数（使用标准字段名）\n        std::string wifiName = jBody[\"wifiName\"].asString();\n        std::string wifiPassword = jBody[\"wifiPassword\"].asString();\n\n        if (wifiName.empty()) {\n            RCLCPP_ERROR(rclcpp::get_logger(\"robdog_control\"), \"WiFi name cannot be empty\");\n            return;\n        }\n\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Attempting to connect to WiFi: %s\", wifiName.c_str());\n        // 注意：密码不记录到日志中以保护隐私\n\n        // 构建JSON命令字符串\n        Json::Value commandJson;\n        commandJson[\"command\"] = \"connectWifi\";\n        commandJson[\"ssid\"] = wifiName;\n        if (!wifiPassword.empty()) {\n            commandJson[\"password\"] = wifiPassword;\n        }\n\n        Json::StreamWriterBuilder writerBuilder;\n        std::string commandStr = Json::writeString(writerBuilder, commandJson);\n\n        // 创建服务请求\n        auto request = std::make_shared<homi_speech_interface::srv::NetCtrl::Request>();\n        request->data = commandStr;\n\n        // 设置WiFi连接状态为连接中\n        wifiSetResult.store(WifiSetResult::CONNECTING);\n        targetWifiSSID = wifiName;\n        wifiConnectStartTime = std::chrono::steady_clock::now();\n        \n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Calling network service for WiFi connection: %s\", wifiName.c_str());\n\n        // 使用异步调用和回调函数的方式\n        auto future = net_client->async_send_request(request,\n            [this, inValue, wifiName](rclcpp::Client<homi_speech_interface::srv::NetCtrl>::SharedFuture response) {\n                try {\n                    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Network service response received for WiFi connection\");\n                    \n                    auto serviceResponse = response.get();\n                    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Network service result for WiFi connection: %s\", serviceResponse->result.c_str());\n\n                    // 解析服务响应并更新WiFi连接状态\n                    Json::Value responseJson;\n                    Json::Reader reader;\n                    if (reader.parse(serviceResponse->result, responseJson)) {\n                        if (responseJson.isMember(\"success\") && responseJson[\"success\"].asBool()) {\n                            wifiSetResult.store(WifiSetResult::CONNECT_SUCCESS);\n                            RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"WiFi connection successful\");\n                        } else if (responseJson.isMember(\"error\")) {\n                            std::string error = responseJson[\"error\"].asString();\n                            if (error.find(\"password\") != std::string::npos || error.find(\"authentication\") != std::string::npos) {\n                                wifiSetResult.store(WifiSetResult::CONNECT_PASSWORD_ERROR);\n                                RCLCPP_ERROR(rclcpp::get_logger(\"robdog_control\"), \"WiFi connection failed: password error\");\n                            } else {\n                                wifiSetResult.store(WifiSetResult::CONNECT_TIMEOUT);\n                                RCLCPP_ERROR(rclcpp::get_logger(\"robdog_control\"), \"WiFi connection failed: timeout or other error\");\n                            }\n                        } else {\n                            wifiSetResult.store(WifiSetResult::CONNECT_TIMEOUT);\n                            RCLCPP_ERROR(rclcpp::get_logger(\"robdog_control\"), \"WiFi connection failed: unknown error\");\n                        }\n                    } else {\n                        wifiSetResult.store(WifiSetResult::CONNECT_TIMEOUT);\n                        RCLCPP_ERROR(rclcpp::get_logger(\"robdog_control\"), \"Failed to parse WiFi connection response\");\n                    }\n\n                    // // 转换服务响应为平台格式并发送\n                    // Json::Value platformResponse = convertServiceResponseToPlatformFormat(inValue, serviceResponse->result);\n                    // Json::StreamWriterBuilder writerBuilder;\n                    // std::string responseStr = Json::writeString(writerBuilder, platformResponse);\n                    \n                    // // 发送响应到平台\n                    // sendRequestData(responseStr);\n                    \n                } catch (const std::exception& e) {\n                    RCLCPP_ERROR(rclcpp::get_logger(\"robdog_control\"), \"Error in WiFi connection callback: %s\", e.what());\n                    wifiSetResult.store(WifiSetResult::CONNECT_TIMEOUT);\n                }\n            });\n\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"WiFi connection request sent to network service for: %s\", wifiName.c_str());\n\n    } catch (const std::exception& e) {\n        RCLCPP_ERROR(rclcpp::get_logger(\"robdog_control\"), \"Error in handleWifiSet: %s\", e.what());\n    }\n}\n\n// 处理WiFi设置结果查询\nvoid RobdogCenter::handleWifiSetResultQuery(const Json::Value &inValue, const Json::Value &jBody) {\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"handleWifiSetResultQuery called\");\n\n    try {\n        // 获取当前WiFi连接状态\n        WifiSetResult currentResult = wifiSetResult.load();\n        int wifiSetResultValue = static_cast<int>(currentResult);\n        \n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Current WiFi connection status: %d\", wifiSetResultValue);\n\n        // 检查连接超时（如果状态为连接中且超过30秒）\n        if (currentResult == WifiSetResult::CONNECTING) {\n            auto now = std::chrono::steady_clock::now();\n            auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(now - wifiConnectStartTime).count();\n            \n            if (elapsed > 30) { // 30秒超时\n                wifiSetResult.store(WifiSetResult::CONNECT_TIMEOUT);\n                currentResult = WifiSetResult::CONNECT_TIMEOUT;\n                wifiSetResultValue = static_cast<int>(currentResult);\n                RCLCPP_WARN(rclcpp::get_logger(\"robdog_control\"), \"WiFi connection timeout after %ld seconds\", elapsed);\n            }\n        }\n\n        // 构建标准响应消息\n        Json::Value response;\n        response[\"deviceId\"] = inValue[\"deviceId\"];\n        response[\"domain\"] = \"DEVICE_PROPERTIES\";\n        response[\"event\"] = \"wifi_set_result_query\";\n        response[\"eventId\"] = inValue[\"eventId\"];\n        response[\"seq\"] = std::to_string(std::chrono::duration_cast<std::chrono::milliseconds>(\n            std::chrono::system_clock::now().time_since_epoch()).count());\n        response[\"body\"][\"wifiSetResult\"] = wifiSetResultValue;\n\n        // 发送响应\n        Json::StreamWriterBuilder writerBuilder;\n        std::string responseStr = Json::writeString(writerBuilder, response);\n        sendRequestData(responseStr);\n\n        // RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"WiFi set result query completed, result: %d\", wifiSetResult);\n\n    } catch (const std::exception& e) {\n        RCLCPP_ERROR(rclcpp::get_logger(\"robdog_control\"), \"Error in handleWifiSetResultQuery: %s\", e.what());\n\n        // 发送错误响应（连接失败）\n        Json::Value errorResponse;\n        errorResponse[\"deviceId\"] = inValue[\"deviceId\"];\n        errorResponse[\"domain\"] = \"DEVICE_PROPERTIES\";\n        errorResponse[\"event\"] = \"wifi_set_result_query\";\n        errorResponse[\"eventId\"] = inValue[\"eventId\"];\n        errorResponse[\"seq\"] = std::to_string(std::chrono::duration_cast<std::chrono::milliseconds>(\n            std::chrono::system_clock::now().time_since_epoch()).count());\n        errorResponse[\"body\"][\"wifiSetResult\"] = 2; // 连接失败（超时）\n\n        Json::StreamWriterBuilder writerBuilder;\n        std::string errorStr = Json::writeString(writerBuilder, errorResponse);\n        sendRequestData(errorStr);;\n    }\n}\n\n"}]}