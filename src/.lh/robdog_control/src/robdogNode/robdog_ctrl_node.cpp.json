{"sourceFile": "robdog_control/src/robdogNode/robdog_ctrl_node.cpp", "activeCommit": 0, "commits": [{"activePatchIndex": 1, "patches": [{"date": 1754118873350, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1754266949913, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -225,64 +225,79 @@\n   return pathstr;\n }\n \n void RobdogCtrlNode::initNode() {\n-    velCmd_ = this->create_subscription<geometry_msgs::msg::Twist>(\n-        \"/catch_turtle/ctrl_instruct\", 10, \n-        std::bind(&RobdogCtrlNode::velCmdCallback, this, std::placeholders::_1)\n-    );\n+    try {\n+        RCLCPP_INFO(this->get_logger(), \"Creating velocity command subscription...\");\n+        velCmd_ = this->create_subscription<geometry_msgs::msg::Twist>(\n+            \"/catch_turtle/ctrl_instruct\", 10,\n+            std::bind(&RobdogCtrlNode::velCmdCallback, this, std::placeholders::_1)\n+        );\n+        RCLCPP_INFO(this->get_logger(), \"Velocity command subscription created successfully\");\n \n-    // 订阅新的UDP连接消息\n-    if (robManuModel == ROB_MANU_DEEP_LITE)\n-    {\n+        // 订阅新的UDP连接消息\n+        if (robManuModel == ROB_MANU_DEEP_LITE)\n+        {\n+            RCLCPP_INFO(this->get_logger(), \"Creating UDP connection subscription...\");\n             UdpConnectCmd_ = this->create_subscription<homi_speech_interface::msg::NewUdpConnect>(\n-            \"/catch_turtle/ctrl_udpconnect\", 1,\n-            std::bind(&RobdogCtrlNode::updateSocket, this, std::placeholders::_1)\n+                \"/catch_turtle/ctrl_udpconnect\", 1,\n+                std::bind(&RobdogCtrlNode::updateSocket, this, std::placeholders::_1)\n+            );\n+            RCLCPP_INFO(this->get_logger(), \"UDP connection subscription created successfully\");\n+        }\n+\n+        // 订阅特定运动消息\n+        RCLCPP_INFO(this->get_logger(), \"Creating action command subscription...\");\n+        actionCmd_ = this->create_subscription<homi_speech_interface::msg::RobdogAction>(\n+            \"/catch_turtle/action_type\", 1,\n+            std::bind(&RobdogCtrlNode::MoveSkillscallback, this, std::placeholders::_1)\n         );\n-    }\n+        RCLCPP_INFO(this->get_logger(), \"Action command subscription created successfully\");\n \n-    // 订阅特定运动消息\n-    actionCmd_ = this->create_subscription<homi_speech_interface::msg::RobdogAction>(\n-        \"/catch_turtle/action_type\", 1,\n-        std::bind(&RobdogCtrlNode::MoveSkillscallback, this, std::placeholders::_1)\n-    );\n-\n-    // 订阅持续运动消息\n-    continueMoveCmd_ = this->create_subscription<homi_speech_interface::msg::ContinueMove>(\n-        \"/catch_turtle/continue_move\", 1,\n-        std::bind(&RobdogCtrlNode::continueMovecallback, this, std::placeholders::_1)\n-    );\n-\n-    if (robManuModel == ROB_MANU_DEEP_LITE)\n-    {\n-        timer_ = this->create_wall_timer(\n-            std::chrono::milliseconds(100),\n-            std::bind(&RobdogCtrlNode::HeartBeatCallback, this)\n+        // 订阅持续运动消息\n+        RCLCPP_INFO(this->get_logger(), \"Creating continue move subscription...\");\n+        continueMoveCmd_ = this->create_subscription<homi_speech_interface::msg::ContinueMove>(\n+            \"/catch_turtle/continue_move\", 1,\n+            std::bind(&RobdogCtrlNode::continueMovecallback, this, std::placeholders::_1)\n         );\n-    }\n+        RCLCPP_INFO(this->get_logger(), \"Continue move subscription created successfully\");\n \n-    /*ifly_sub_ = this->create_subscription<homi_speech_interface::msg::Wakeup>(\n-        \"/audio_recorder/wakeup_event\", 1,\n-        [this](const std::shared_ptr<homi_speech_interface::msg::Wakeup> msg) {\n-            this->iflyCallback(msg);\n+        if (robManuModel == ROB_MANU_DEEP_LITE)\n+        {\n+            RCLCPP_INFO(this->get_logger(), \"Creating heartbeat timer...\");\n+            timer_ = this->create_wall_timer(\n+                std::chrono::milliseconds(100),\n+                std::bind(&RobdogCtrlNode::HeartBeatCallback, this)\n+            );\n+            RCLCPP_INFO(this->get_logger(), \"Heartbeat timer created successfully\");\n         }\n-    );*/\n-    RCLCPP_userDefinedSub_INFO(this->get_logger(), \"ifly_sub_ subscribe topic /audio_node/wakeup_event\");\n \n-     = this->create_subscription<homi_speech_interface::msg::ProprietySet>(\n-        \"/deep_udp_ctrl/status_ctrl\", \n-        10, \n-        [this](const std::shared_ptr<homi_speech_interface::msg::ProprietySet> msg) {\n-            this->userDefinedCtrlCallback(msg);\n-        }\n-    );\n+        /*ifly_sub_ = this->create_subscription<homi_speech_interface::msg::Wakeup>(\n+            \"/audio_recorder/wakeup_event\", 1,\n+            [this](const std::shared_ptr<homi_speech_interface::msg::Wakeup> msg) {\n+                this->iflyCallback(msg);\n+            }\n+        );*/\n+        RCLCPP_INFO(this->get_logger(), \"ifly_sub_ subscribe topic /audio_node/wakeup_event\");\n \n-    RCLCPP_INFO(this->get_logger(), \"userDefinedSub_ subscribe topic /deep_udp_ctrl/status_ctrl\");\n+        RCLCPP_INFO(this->get_logger(), \"Creating user defined subscription...\");\n+        userDefinedSub_ = this->create_subscription<homi_speech_interface::msg::ProprietySet>(\n+            \"/deep_udp_ctrl/status_ctrl\",\n+            10,\n+            [this](const std::shared_ptr<homi_speech_interface::msg::ProprietySet> msg) {\n+                this->userDefinedCtrlCallback(msg);\n+            }\n+        );\n+        RCLCPP_INFO(this->get_logger(), \"User defined subscription created successfully\");\n \n-    statusReportPub = this->create_publisher<homi_speech_interface::msg::ProprietySet>(\n-        \"/deep_udp_ctrl/status_report\", 1\n-    );\n+        RCLCPP_INFO(this->get_logger(), \"userDefinedSub_ subscribe topic /deep_udp_ctrl/status_ctrl\");\n \n+        RCLCPP_INFO(this->get_logger(), \"Creating status report publisher...\");\n+        statusReportPub = this->create_publisher<homi_speech_interface::msg::ProprietySet>(\n+            \"/deep_udp_ctrl/status_report\", 1\n+        );\n+        RCLCPP_INFO(this->get_logger(), \"Status report publisher created successfully\");\n+\n     robotStatusPub_ = this->create_publisher<homi_speech_interface::msg::RobdogState>(\n         \"/robdog_control/robdog_state\", 1\n     );\n     wakeupPub_=this->create_publisher<homi_speech_interface::msg::Wakeup>(\"/robdog_control/wakeupHomi\",1);\n"}], "date": 1754118873349, "name": "Commit-0", "content": "#include \"robdog_ctrl_node.h\"\n#include \"robotMgr/robot_info_mgr.h\"\n#include \"public/tools.h\"\n#include \"robotState/RobotState.h\" // 为了存取xml\n#include <arpa/inet.h>\n#include <errno.h>\n#include <fcntl.h>\n#include <poll.h>\n#include <sys/file.h>\n#include <sys/socket.h>\n#include <unistd.h>\n#include <sstream>\n#include <string>\n#include <filesystem>\n#include <vector>\n#include <random> \n#include <arpa/inet.h> \n#include <cmath> \n#include <thread>\n#include <stdexcept>\n#include <array>\n#include <regex>\n#include <mutex>\n#include <condition_variable>\n#include <sys/stat.h>\n#include <dirent.h>\n#include <homi_speech_interface/srv/play_file.hpp>\n#include <homi_speech_interface/def.h>\n#include <ament_index_cpp/get_package_share_directory.hpp>\n#include <iostream>\n#include <ifaddrs.h>\n#include <netinet/in.h>\n#include <arpa/inet.h>\n#include <cstring>\n\n#pragma pack(4)\n\nuint32_t hton_int(int value) {\n    return htonl(value);\n}\n\n// ros::Timer timer;\nrclcpp::TimerBase::SharedPtr timer;\n// rclcpp::Timer::SharedPtr timer;\n\nbool timer_active = false;\nhomi_speech_interface::msg::ContinueMove::SharedPtr g_robot_motion;\n\nCtrlMoveData g_MoveData = {\"\", 0, 0, 0, 0, 0, 0};\n\nclass FloatRandomGenerator {\npublic:\n    FloatRandomGenerator(float min, float max, size_t num_samples)\n        : min(min), max(max), current_index(0) {\n        float step = (max - min) / static_cast<float>(num_samples - 1);\n        for (size_t i = 0; i < num_samples; ++i) {\n            float value = min + i * step;\n            numbers.push_back(value);\n        }\n        std::shuffle(numbers.begin(), numbers.end(), rng);\n    }\n    float next() {\n        if (current_index >= numbers.size()) {\n            std::cout<<\"No more numbers available.\";\n        }\n        return numbers[current_index++];\n    }\nprivate:\n    float min, max;\n    std::vector<float> numbers;\n    size_t current_index;\n    std::default_random_engine rng{static_cast<unsigned int>(std::time(nullptr))};\n};\n\nlong getVideoDurationInMilliseconds(const std::string& filePath) {\n    if (filePath.empty()) {\n        // ROS_INFO(\"File path cannot be empty.\");\n        RCLCPP_ERROR(rclcpp::get_logger(\"deep_udp_ctrl_node\"), \"File path cannot be empty.\");\n        throw std::invalid_argument(\"File path cannot be empty.\");\n    }\n    std::string command = \"ffmpeg -i \\\"\" + filePath + \"\\\" 2>&1 | grep \\\"Duration\\\"\";\n    std::array<char, 128> buffer;\n    std::string result;\n    FILE* pipe = popen(command.c_str(), \"r\");\n    if (!pipe) {\n        RCLCPP_ERROR(rclcpp::get_logger(\"deep_udp_ctrl_node\"), \"popen() failed!\");\n        // ROS_INFO(\"popen() failed!\");\n    }\n    while (fgets(buffer.data(), buffer.size(), pipe) != nullptr) {\n        result += buffer.data();\n    }\n    pclose(pipe);\n    std::regex durationRegex(R\"(Duration: (\\d+):(\\d+):(\\d+)\\.(\\d+))\");\n    std::smatch match;\n    if (std::regex_search(result, match, durationRegex)) {\n        int hours = std::stoi(match[1]);\n        int minutes = std::stoi(match[2]);\n        int seconds = std::stoi(match[3]);\n        int milliseconds = std::stoi(match[4]);\n        long totalMilliseconds = (hours * 3600 + minutes * 60 + seconds) * 1000 + milliseconds * 10;\n        return totalMilliseconds;\n    } else {\n        throw std::runtime_error(\"Could not extract duration from ffmpeg output.\");\n    }\n}\n\nstd::string get_random_file_from_directory(const std::string &directory)\n{\n    DIR *dir = opendir(directory.c_str());\n    if (dir == nullptr) {\n        std::cerr << \"Failed to open directory: \" << directory << std::endl;\n        return \"\";\n    }\n    struct DirectoryCloser {\n        void operator()(DIR* dir) const {\n            if (dir != nullptr) closedir(dir);\n        }\n    };\n    std::unique_ptr<DIR, DirectoryCloser> safe_dir(dir);\n    std::vector<std::string> video_files;\n    struct dirent *entry;\n    while ((entry = readdir(safe_dir.get())) != nullptr) {\n        if (entry->d_name[0] == '.' && (entry->d_name[1] == '\\0' || (entry->d_name[1] == '.' && entry->d_name[2] == '\\0'))) {\n            continue;\n        }\n        std::string full_path = directory + \"/\" + entry->d_name;\n        struct stat file_info;\n        if (stat(full_path.c_str(), &file_info) == 0 && S_ISREG(file_info.st_mode)) {\n            video_files.push_back(full_path);\n        }\n    }\n    if (video_files.empty()) {\n        std::cerr << \"No files found in directory: \" << directory << std::endl;\n        return \"\";\n    }\n    static bool seeded = false;\n    if (!seeded) {\n        srand(static_cast<unsigned>(time(nullptr)));\n        seeded = true;\n    }\n    int random_index = rand() % video_files.size();\n    std::cout << \"Selected file: \" << video_files[random_index] << std::endl;\n    return video_files[random_index];\n}\n\n// void execute_script(const std::string& directory) {\n//     std::vector<std::string> files;    long duration;\n//     try {\n//         if (!std::filesystem::exists(directory) || !std::filesystem::is_directory(directory)) {\n//             std::cerr << \"Directory does not exist or is not a directory: \" << directory << std::endl;\n//             return;\n//         }\n//         for (const auto &entry : std::filesystem::directory_iterator(directory)) {\n//             if (entry.is_regular_file()) {\n//                 files.push_back(entry.path().string());\n//             }\n//         }\n//     } catch (const std::filesystem::filesystem_error &e) {\n//         std::cerr << \"Filesystem error: \" << e.what() << std::endl;\n//         return; // 处理完错误后正常返回\n//     } catch (const std::exception &e) {\n//         std::cerr << \"Exception: \" << e.what() << std::endl;\n//         return; // 处理其他异常\n//     }    \n//     std::random_device rd;\n//     std::mt19937 gen(rd());\n//     std::uniform_int_distribution<> dis(0, files.size() - 1);\n//     try {\n//         duration = getVideoDurationInMilliseconds(files[dis(gen)]);\n//     } \n//     catch (const std::exception& e) {\n//         std::cerr << \"Error: \" << e.what() << std::endl;\n//         return ;\n//     }\n//     std::string command_1 = \"/home/<USER>/updateexpression.sh \" + files[dis(gen)];\n//     system(command_1.c_str());\n//     sleep(duration/1000);\n//     command_1 = \"/home/<USER>/updateexpression.sh /home/<USER>/resource/vedio/default.mp4\";\n//     system(command_1.c_str());\n// }\n\n// void specific_expression(const std::string& path) {\n//     if (path.empty()) {\n//         // ROS_INFO(\"File path cannot be empty.\");\n//         throw std::invalid_argument(\"File path cannot be empty.\");\n//     }\n//     std::string command_2 = \"/home/<USER>/updateexpression.sh \" + path;\n//     system(command_2.c_str());\n// }\n\nRobdogCtrlNode::RobdogCtrlNode() : Node(\"robdog_control_node\"),last_vel_steady_time_(Clock::now())\n{\n    RobdogCtrlNode::setRobManuModel();\n}\n\nRobdogCtrlNode::~RobdogCtrlNode() {\n    if (sockfd_ != -1) {\n        close(sockfd_);\n    }\n}\n\n/* 获取机器人厂商型号 */\nRobManuModel RobdogCtrlNode::getRobManuModel()\n{\n    return robManuModel;\n}\n\n/* 设置机器人厂商型号 */\nvoid RobdogCtrlNode::setRobManuModel(){\n#if defined(YSC1_0)\n    robManuModel = ROB_MANU_DEEP_LITE;\n#elif defined(YSC1_1)\n    robManuModel = ROB_MANU_DEEP_LITE;\n#elif defined(UNITREE)\n    robManuModel = ROB_MANU_UNITREE_GO2;\n#else\n    robManuModel = ROB_MANU_DEEP_LITE;\n#endif\n}\n\nstd::string RobdogCtrlNode::getResourcePath(const std::string &file_name){\n  static const std::string package_share_dir = ament_index_cpp::get_package_share_directory(\"robdog_control\");\n  auto pathstr= package_share_dir + \"/resource/\" + file_name;\n  RCLCPP_INFO(this->get_logger(), \"file dir is : %s\", pathstr.c_str());\n  return pathstr;\n}\n\nvoid RobdogCtrlNode::initNode() {\n    velCmd_ = this->create_subscription<geometry_msgs::msg::Twist>(\n        \"/catch_turtle/ctrl_instruct\", 10, \n        std::bind(&RobdogCtrlNode::velCmdCallback, this, std::placeholders::_1)\n    );\n\n    // 订阅新的UDP连接消息\n    if (robManuModel == ROB_MANU_DEEP_LITE)\n    {\n            UdpConnectCmd_ = this->create_subscription<homi_speech_interface::msg::NewUdpConnect>(\n            \"/catch_turtle/ctrl_udpconnect\", 1,\n            std::bind(&RobdogCtrlNode::updateSocket, this, std::placeholders::_1)\n        );\n    }\n\n    // 订阅特定运动消息\n    actionCmd_ = this->create_subscription<homi_speech_interface::msg::RobdogAction>(\n        \"/catch_turtle/action_type\", 1,\n        std::bind(&RobdogCtrlNode::MoveSkillscallback, this, std::placeholders::_1)\n    );\n\n    // 订阅持续运动消息\n    continueMoveCmd_ = this->create_subscription<homi_speech_interface::msg::ContinueMove>(\n        \"/catch_turtle/continue_move\", 1,\n        std::bind(&RobdogCtrlNode::continueMovecallback, this, std::placeholders::_1)\n    );\n\n    if (robManuModel == ROB_MANU_DEEP_LITE)\n    {\n        timer_ = this->create_wall_timer(\n            std::chrono::milliseconds(100),\n            std::bind(&RobdogCtrlNode::HeartBeatCallback, this)\n        );\n    }\n\n    /*ifly_sub_ = this->create_subscription<homi_speech_interface::msg::Wakeup>(\n        \"/audio_recorder/wakeup_event\", 1,\n        [this](const std::shared_ptr<homi_speech_interface::msg::Wakeup> msg) {\n            this->iflyCallback(msg);\n        }\n    );*/\n    RCLCPP_userDefinedSub_INFO(this->get_logger(), \"ifly_sub_ subscribe topic /audio_node/wakeup_event\");\n\n     = this->create_subscription<homi_speech_interface::msg::ProprietySet>(\n        \"/deep_udp_ctrl/status_ctrl\", \n        10, \n        [this](const std::shared_ptr<homi_speech_interface::msg::ProprietySet> msg) {\n            this->userDefinedCtrlCallback(msg);\n        }\n    );\n\n    RCLCPP_INFO(this->get_logger(), \"userDefinedSub_ subscribe topic /deep_udp_ctrl/status_ctrl\");\n\n    statusReportPub = this->create_publisher<homi_speech_interface::msg::ProprietySet>(\n        \"/deep_udp_ctrl/status_report\", 1\n    );\n\n    robotStatusPub_ = this->create_publisher<homi_speech_interface::msg::RobdogState>(\n        \"/robdog_control/robdog_state\", 1\n    );\n    wakeupPub_=this->create_publisher<homi_speech_interface::msg::Wakeup>(\"/robdog_control/wakeupHomi\",1);\n    robotExpressionPub_= this->create_publisher<std_msgs::msg::String>(\"/robdog_control/changeExpression\", 1);\n    this->declare_parameter(\"state_paths\", std::vector<std::string>());\n    // sockfd_ = -1;\n\n    peripherals_ctrl_client_ = this->create_client<homi_speech_interface::srv::PeripheralsCtrl>(\"/robdog_control/peripherals_ctrl\");\n    peripherals_send_request();\n    peripherals_status_client_ = this->create_client<homi_speech_interface::srv::PeripheralsStatus>(\"/robdog_control/peripherals_status\");\n    peripherals_monitor_sub_ =  this->create_subscription<std_msgs::msg::String>(\n        \"/robdog_control/peripherals_monitor\", 10,\n        std::bind(&RobdogCtrlNode::handle_peripherals_monitor_message, this, std::placeholders::_1)\n    );\n\n    ut_status_report_timer = this->create_wall_timer(\n        std::chrono::seconds(2),\n        std::bind(&RobdogCtrlNode::utStatusReportCallback, this));\n}\n\nvoid RobdogCtrlNode::setRobotMove(float x, float y, float z)\n{\n    strncpy(g_MoveData.event, \"robot_move\", sizeof(g_MoveData.event) -1);\n    g_MoveData.event[sizeof(g_MoveData.event) -1] = '\\0';\n    g_MoveData.x = x;\n    g_MoveData.y = y;\n    g_MoveData.z = z;\n\n    return;\n}\n\nvoid RobdogCtrlNode::setRobotView(float yaw, float pitch, float roll)\n{\n    strncpy(g_MoveData.event, \"robot_view\", sizeof(g_MoveData.event) - 1);\n    g_MoveData.event[sizeof(g_MoveData.event) -1] = '\\0';\n    g_MoveData.yaw = yaw;\n    g_MoveData.pitch = pitch;\n    g_MoveData.roll = roll;\n\n    return;\n}\n\nvoid RobdogCtrlNode::peripherals_send_request() {\n  if (!peripherals_ctrl_client_->wait_for_service(1s)) {\n    RCLCPP_WARN(this->get_logger(), \"Service /robdog_control/peripherals_ctrl not available.\");\n    return;\n  }\n  try {\n\n    Json::Value request_json;\n\n    const std::string config_path = ament_index_cpp::get_package_share_directory(\"robdog_control\") \n                                  + \"/resource/config/peripherals_config.json\";\n    \n    std::ifstream config_file(config_path);\n    if (!config_file) {\n      throw std::runtime_error(\"Cannot open config file: \" + config_path);\n    }\n\n    Json::Value config_json;\n    std::string parse_errors;\n    if (!Json::parseFromStream(Json::CharReaderBuilder(), config_file, &config_json, &parse_errors)) {\n      throw std::runtime_error(\"Config parse failed: \" + parse_errors);\n    }\n\n    request_json[\"command\"] = config_json[\"command\"];\n    request_json[\"data\"] = config_json[\"data\"];\n\n    Json::StreamWriterBuilder writer;\n    const std::string json_str = Json::writeString(writer, request_json);\n\n    auto request = std::make_shared<homi_speech_interface::srv::PeripheralsCtrl::Request>();\n    request->data = json_str;\n\n    auto future = peripherals_ctrl_client_->async_send_request(\n      request,\n      [this](rclcpp::Client<homi_speech_interface::srv::PeripheralsCtrl>::SharedFuture result) {\n        try {\n          auto response = result.get();\n                 \n          Json::Value response_json;\n          Json::CharReaderBuilder reader;\n          std::string errors;\n          std::istringstream stream(response->result);\n          \n          if (Json::parseFromStream(reader, stream, &response_json, &errors)) {\n            if (response_json.get(\"success\", false).asBool()) {\n              RCLCPP_INFO(this->get_logger(), \"RGB PWM updated: R=%d, G=%d, B=%d\",\n                        response_json[\"red\"].asInt(),\n                        response_json[\"green\"].asInt(),\n                        response_json[\"blue\"].asInt());\n            } else {\n              RCLCPP_ERROR(this->get_logger(), \"Service error: %s\", \n                         response_json[\"error\"].asString().c_str());\n            }\n          } else {\n            RCLCPP_ERROR(this->get_logger(), \"Response parse error: %s\", errors.c_str());\n          }\n        } catch (const std::exception &e) {\n          RCLCPP_ERROR(this->get_logger(), \"Callback error: %s\", e.what());\n        }\n      }\n    );\n\n  } catch (const Json::Exception& e) {\n    RCLCPP_ERROR(this->get_logger(), \"JSON error: %s\", e.what());\n  } catch (const std::exception& e) {\n    RCLCPP_ERROR(this->get_logger(), \"System error: %s\", e.what());\n  }\n}\n\n\nvoid RobdogCtrlNode::send_peripherals_ctrl_request() {\n    if (!peripherals_ctrl_client_->wait_for_service(std::chrono::seconds(1))) {\n        RCLCPP_WARN(this->get_logger(), \"Service /robdog_control/peripherals_ctrl not available.\");\n        return;\n    }\n\n    Json::Value request_json;\n    request_json[\"command\"] = \"start_control\";\n    request_json[\"timestamp\"] = static_cast<unsigned int>(this->now().seconds());\n\n    Json::StreamWriterBuilder writer;\n    std::string json_request = Json::writeString(writer, request_json);\n\n    auto request = std::make_shared<homi_speech_interface::srv::PeripheralsCtrl::Request>();\n    request->data = json_request;\n\n    auto future = peripherals_ctrl_client_->async_send_request(\n        request,\n        [this](rclcpp::Client<homi_speech_interface::srv::PeripheralsCtrl>::SharedFuture result) {\n            try {\n                auto response = result.get();\n                Json::CharReaderBuilder reader;\n                Json::Value response_json;\n                std::string errors;\n                std::istringstream stream(response->result);\n                if (Json::parseFromStream(reader, stream, &response_json, &errors)) {\n                    RCLCPP_INFO(this->get_logger(), \"Peripherals control response: %s\", response_json.toStyledString().c_str());\n                } else {\n                    RCLCPP_ERROR(this->get_logger(), \"Failed to parse JSON response: %s\", errors.c_str());\n                }\n            } catch (const std::exception &e) {\n                RCLCPP_ERROR(this->get_logger(), \"Failed to call service: %s\", e.what());\n            }\n        }\n    );\n}\n\nvoid RobdogCtrlNode::send_peripherals_status_request() {\n    if (!peripherals_status_client_->wait_for_service(std::chrono::seconds(1))) {\n        RCLCPP_WARN(this->get_logger(), \"Service /robdog_control/peripherals_status not available.\");\n        return;\n    }\n\n    Json::Value request_json;\n    request_json[\"query\"] = \"status_check\";\n    request_json[\"timestamp\"] = static_cast<unsigned int>(this->now().seconds());\n\n    Json::StreamWriterBuilder writer;\n    std::string json_request = Json::writeString(writer, request_json);\n\n    auto request = std::make_shared<homi_speech_interface::srv::PeripheralsStatus::Request>();\n    request->data = json_request;\n\n    auto future = peripherals_status_client_->async_send_request(\n        request,\n        [this](rclcpp::Client<homi_speech_interface::srv::PeripheralsStatus>::SharedFuture result) {\n            try {\n                auto response = result.get();\n                Json::CharReaderBuilder reader;\n                Json::Value response_json;\n                std::string errors;\n                std::istringstream stream(response->result);\n                if (Json::parseFromStream(reader, stream, &response_json, &errors)) {\n                    RCLCPP_INFO(this->get_logger(), \"Peripherals status response: %s\", response_json.toStyledString().c_str());\n                } else {\n                    RCLCPP_ERROR(this->get_logger(), \"Failed to parse JSON response: %s\", errors.c_str());\n                }\n            } catch (const std::exception &e) {\n                RCLCPP_ERROR(this->get_logger(), \"Failed to call service: %s\", e.what());\n            }\n        }\n    );\n}\n\nvoid RobdogCtrlNode::handle_peripherals_monitor_message(const std_msgs::msg::String::SharedPtr msg) {\n    try {\n        Json::Value root;\n        JSONCPP_STRING err;\n        Json::CharReaderBuilder readerBuilder;\n        \n        const std::string& raw_json = msg->data;\n        std::istringstream json_stream(raw_json);\n\n        if (!Json::parseFromStream(readerBuilder, json_stream, &root, &err)) {\n            RCLCPP_ERROR(this->get_logger(), \"JSON解析失败: %s\", err.c_str());\n            return;\n        }\n\n        if (!root.isMember(\"command\") || root[\"command\"].asString() != \"touch_event\") {\n            RCLCPP_DEBUG(this->get_logger(), \"忽略非触控事件命令\");\n            return;\n        }\n\n        // 检查data成员是否存在\n        if (!root.isMember(\"data\") || !root[\"data\"].isObject()) {\n            RCLCPP_WARN(this->get_logger(), \"触摸事件消息缺少data对象\");\n            return;\n        }\n\n        // 检查data对象中是否有任何一个值为true\n        bool any_touch_active = false;\n        const Json::Value& data = root[\"data\"];\n        Json::Value::Members members = data.getMemberNames();\n        \n        for (const auto& key : members) {\n            if (data[key].asBool()) {\n                any_touch_active = true;\n                RCLCPP_INFO(this->get_logger(), \"检测到触摸: 引脚 %s = true\", key.c_str());\n                break;\n            }\n        }\n        \n        if (any_touch_active) {\n            RCLCPP_INFO(this->get_logger(), \"HEAD has been touched\");\n\n            if(true == RobotInfoMgr::getInstance().utStandCheck())\n            {\n                // 站立状态\n                 robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_TWIST);\n            }\n\n            // robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_TWIST);\n\n            RCLCPP_INFO(this->get_logger(), \"before ExpressionChange::getInstance().async_callback_work\");\n            ExpressionChange::getInstance().async_callback_work(getResourcePath(\"video/touch/\"),1);\n            RCLCPP_INFO(this->get_logger(), \"after ExpressionChange::getInstance().async_callback_work\");\n            \n            std::thread t_audio(&RobdogCtrlNode::playAudio,this, getResourcePath(\"audio/touch/\"));\n            t_audio.detach();\n        }\n\n    } catch (const std::exception& e) {\n        RCLCPP_ERROR(this->get_logger(), \"消息处理异常: %s\", e.what());\n    }\n}\n\n\n// ************************************* 把playAudio函数放到了节点类里面，具体功能需要后续测试 ***************************************\nvoid RobdogCtrlNode::playAudio(const std::string& filePath) {\n#if 0\n// 通过调用服务播放本地文件\n    playAudioFile(filePath);\n#else\n    string audioFile=filePath;\n    struct stat statbuf;\n    if (stat(filePath.c_str(), &statbuf) != 0) {\n        RCLCPP_ERROR(this->get_logger(), \"Can not get path\");\n        return;\n    }\n    if (S_ISDIR(statbuf.st_mode)){\n        std::string random_file = get_random_file_from_directory(filePath);\n        if (!random_file.empty()) {\n            audioFile = random_file;\n            RCLCPP_INFO(this->get_logger(), \"Going to play random audio file: %s\", audioFile.c_str());\n        } else {\n            RCLCPP_ERROR(this->get_logger(), \"No video files found in directory: %s\", random_file.c_str());\n            return;\n        }\n    }\n    // FILE* fp;\n    // char buffer[50];\n    // fp = popen(R\"(aplay -l | grep \"USB Audio Devic\" -A 2 | grep \"card\" | awk '{print $2}' | tr -d ':')\", \"r\");\n    // if (!fp) {\n    //     RCLCPP_ERROR(this->get_logger(), \"ThirdpartyAudioDevice Search audio device failed\");\n    //     return;\n    // }\n    // fgets(buffer, sizeof(buffer), fp);\n    // std::string device_name = \"plughw:\" + std::string(1, buffer[0]) + \",0\";\n\n    auto client_abort = this->create_client<homi_speech_interface::srv::AssistantAbort>(\"/homi_speech/helper_assistant_abort_service\");\n    auto request_abort = std::make_shared<homi_speech_interface::srv::AssistantAbort::Request>();\n    auto response_abort = client_abort->async_send_request(request_abort);\n\n    auto client_wake = this->create_client<homi_speech_interface::srv::SetWakeEvent>(\"/audio_node/set_wake_event_service\");\n    auto request_wake = std::make_shared<homi_speech_interface::srv::SetWakeEvent::Request>();\n    request_wake->target = false;\n    auto response_wake = client_wake->async_send_request(request_wake);\n\n    // std::string command_3 = std::string(\"aplay\") + \" \\\"\" + audioFile + \"\\\"\";\n    std::string command_3= \"aplay  \\\"\" +  audioFile + \"\\\"\";\n    RCLCPP_INFO(this->get_logger(), \"Going to exe command: %s\", command_3.c_str());\n    std::system(command_3.c_str());\n\n    // 开启语音唤醒事件\n    request_wake->target = true;\n    response_wake = client_wake->async_send_request(request_wake);\n#endif\n}\n\nvoid RobdogCtrlNode::playAudioFile (const std::string& filePath) {\n    // // 调用 homi_audio_player play file 服务\n    // RCLCPP_INFO(this->get_logger(),\" playAudioFile: %s\",filePath.c_str());\n    // auto clt = this->create_client<homi_speech_interface::srv::PlayFile>(HOMI_AUDIO_PLAYER_PLAYFILE_SERVICE);\n    // auto req = std::make_shared<homi_speech_interface::srv::PlayFile::Request>();\n    // req->url = filePath;\n    // auto ret = clt->wait_for_service(std::chrono::seconds(1));\n    // if(ret==false){\n    //     RCLCPP_WARN(this->get_logger(),\"wait_for_service %s failed! \",HOMI_AUDIO_PLAYER_PLAYFILE_SERVICE);\n    // } else {\n    //     auto result = clt->async_send_request(req);\n    //     // 同步等待结果\n    //     if (rclcpp::spin_until_future_complete(this->shared_from_this(), result) == rclcpp::FutureReturnCode::SUCCESS) {\n    //         RCLCPP_INFO(this->get_logger(), \"call playfile service ret: %d\", result.get()->error_code);\n    //     } else {\n    //         RCLCPP_ERROR(this->get_logger(), \"call service failed! %s\",HOMI_AUDIO_PLAYER_PLAYFILE_SERVICE);\n    //     }\n    // } \n}\n\nvoid RobdogCtrlNode::userDefinedCtrlCallback(const std::shared_ptr<homi_speech_interface::msg::ProprietySet>& msg) {\n// void RobdogCtrlNode::userDefinedCtrlCallback(const homi_speech_interface::msg::ProprietySetPtr& msg){\n    // ROS_INFO(\"Received msg form Platintera Node: cmd:%x,value:%d\", msg->cmd,msg->value);\n    \n    switch (msg->cmd)\n    {\n    case DEEP_CMD_ACTION:\n        \n        robdogCtrlDev->robdogCtrl_UserDefined(msg->value);\n\n        break;\n    case DEEP_CMD_FLASHLIGHT:\n        deep_ctl(DEEP_CMD_FLASHLIGHT,msg->value,msg->exvalue);\n        break;\n    case DEEP_CMD_LIGHT:\n        deep_ctl(DEEP_CMD_LIGHT,msg->value,msg->exvalue);\n    // case DEEP_CMD_AI_MOTION: // 打开多地形自适应\n    //     deep_ctl(DEEP_CMD_AI_MOTION,msg->value,msg->exvalue);\n    default:\n        break;\n    }\n}\nvoid RobdogCtrlNode::positionCtrl(float x, float y,float radian)\n{\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Going to excute positionCtrl\");\n\n    unsigned seed = std::chrono::system_clock::now().time_since_epoch().count();\n    std::default_random_engine generator(seed);\n    std::uniform_real_distribution<double> distribution(0.01, 0.1);\n    double randomNumber = distribution(generator);\n    radian=radian+randomNumber;\n\n    robdogCtrlDev->robdogCtrl_Position(x, y, radian);\n\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Start turn left or right move\");        \n}\n\nvoid RobdogCtrlNode::startRotation(std::function<void()> callback, std::chrono::seconds timeout,const std::shared_ptr<homi_speech_interface::msg::Wakeup> msg) {\n    if (RobotState::getInstance().getCurrentState()!=RobotStateEnum::NORMAL)\n        return;\n    auto should_continue_ = std::make_shared<std::atomic<bool>>(true);\n    float real_angle=(msg->angle+30)*static_cast<float>(M_PI) / 180.0f; \n    if (real_angle<60*static_cast<float>(M_PI) / 180.0f ||2*M_PI-real_angle<60*static_cast<float>(M_PI)/ 180.0f) {\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"real_angle<30,do not need rotate redirect msg to homi directly\"); \n        callback();        \n        return;\n    }\n    positionCtrl(0.0f,0.0f,(msg->angle+30)<180?real_angle:-(2*M_PI-real_angle));\n    std::thread([this,callback, should_continue_,timeout]() mutable {\n        auto start_time = std::chrono::steady_clock::now();\n        bool has_called_callback = false;\n        while (should_continue_->load())\n        {\n            RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"RobotInfoMgr::getInstance().getRobotStatus()=%d\",RobotInfoMgr::getInstance().getRobotStatus());\n\t        if (RobotInfoMgr::getInstance().getRobotStatus()==ROBDOG_STATUS_BODYROTWISTING_BY_AXISCOMMAND){\n            \tRCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"The robot completes its rotation within the specified time!\");\n                try {\n                    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"going to execute callback\");\n                    callback(); \n                    has_called_callback = true;\n                    break; \n                } catch (const std::exception& e) {\n                    RCLCPP_ERROR(this->get_logger(), \"Error in callback: %s\", e.what());\n                }\n            }\n            std::this_thread::sleep_for(std::chrono::milliseconds(500));\n            RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"sleep_for(std::chrono::milliseconds(500)，should_continue_=%d\",should_continue_->load());\n            if (!should_continue_->load()) {\n                RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"should_continue_=%d\",should_continue_->load());\n                break;\n            }\n            auto elapsed_time = std::chrono::steady_clock::now() - start_time;\n            RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"elapsed_time=%lld,now=%lld,start_time=%lld\",elapsed_time,std::chrono::steady_clock::now(),start_time);\n            RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"has_called_callback=%d\",has_called_callback);\n            if (std::chrono::duration_cast<std::chrono::seconds>(elapsed_time)  >= timeout && !has_called_callback) {\n                RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Timeout reached, executing callback.\");\n                try {\n                    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"going to execute callback\");\n                    callback();\n                    has_called_callback = true; \n                } catch (const std::exception& e) {\n                    RCLCPP_ERROR(this->get_logger(), \"Error in callback: %s\", e.what());\n                }\n                break;\n            }\n        }\n        should_continue_->store(false);\n    }).detach();\n}\n\nvoid RobdogCtrlNode::iflyCallback(const std::shared_ptr<homi_speech_interface::msg::Wakeup> msg)\n{\n    RCLCPP_INFO(this->get_logger(),\"Received msg form IFly Node: event:%s,angle:%d\", msg->ivw_word.c_str(),msg->angle);\n\n    robdogCtrlDev->robdogCtrl_PositionAngVel();\n\n    try {\n        startRotation([this,msg]() {\n            wakeupPub_->publish(*msg);\n            RCLCPP_INFO(this->get_logger(),\"Forward homi_speech_interface::msg::Wakeup topic messages\");\n        },\n        std::chrono::seconds(6),msg);\n    } catch (const std::exception& e) {\n        std::cerr << \"Error: \" << e.what() << '\\n';\n    }\n}\n\nvoid RobdogCtrlNode::initSocket(std::string devip_str, int port, int remote_port) {\n    devip_str_ = devip_str;\n    port_ = port;\n    remote_port_ = remote_port;\n    RCLCPP_INFO(this->get_logger(), \"initSocket: %s, %d %d\" , devip_str.c_str(), port_, remote_port);\n    // ROS_INFO_STREAM(\"Initializing socket: \" << devip_str << \", \" << port << \", \" << remote_port);\n\n    sockfd_ = -1;\n    if (!devip_str_.empty()) {\n        inet_aton(devip_str_.c_str(), &client_addr.sin_addr);\n    }\n\n    // 创建UDP套接字「指令通信」\n    sockfd_ = socket(PF_INET, SOCK_DGRAM, 0);\n    if (sockfd_ == -1) {\n        perror(\"socket\");\n        return;\n    }\n    // 创建UDP套接字「touch」\n    sockfd_customized=socket(PF_INET, SOCK_DGRAM, 0);\n    if (sockfd_customized == -1) {\n        perror(\"socket\");\n        return;\n    }\n    // 创建UDP套接字「light」\n    // sockfd_light=socket(PF_INET, SOCK_DGRAM, 0);\n    // if (sockfd_light == -1) {\n    //     perror(\"socket\");\n    //     return;\n    // }\n    sockaddr_in my_addr,my_addr_pro,my_addr_light;\n    memset(&my_addr, 0, sizeof(my_addr));\n    my_addr.sin_family = AF_INET;\n    my_addr.sin_port = htons(port_);\n    my_addr.sin_addr.s_addr = INADDR_ANY;\n\n    memset(&my_addr_pro, 0, sizeof(my_addr_pro));\n    my_addr_pro.sin_family = AF_INET;\n    my_addr_pro.sin_port = htons(1234);\n    my_addr_pro.sin_addr.s_addr = INADDR_ANY;\n\n    // memset(&my_addr_light, 0, sizeof(my_addr_light));\n    // my_addr_light.sin_family = AF_INET;\n    // my_addr_light.sin_port = htons(12345);\n    // my_addr_light.sin_addr.s_addr = INADDR_ANY;\n\n    if (bind(sockfd_, (sockaddr *)&my_addr, sizeof(my_addr)) == -1) {  \n        perror(\"bind\");\n        return;\n    }\n    if (bind(sockfd_customized, (sockaddr *)&my_addr_pro, sizeof(my_addr_pro)) == -1) {  \n        perror(\"bind\");\n        return;\n    }\n    // if (bind(sockfd_light, (sockaddr *)&my_addr_light, sizeof(my_addr_light)) == -1) {  \n    //     perror(\"bind\");\n    //     return;\n    // }\n\n    if (fcntl(sockfd_, F_SETFL, O_NONBLOCK | FASYNC) < 0) {\n        perror(\"fcntl\");\n        return;\n    }\n    if (fcntl(sockfd_customized, F_SETFL, O_NONBLOCK | FASYNC) < 0) {\n        perror(\"fcntl\");\n        return;\n    }\n    // if (fcntl(sockfd_light, F_SETFL, O_NONBLOCK | FASYNC) < 0) {\n    //     perror(\"fcntl\");\n    //     return;\n    // }\n    client_addr.sin_family = AF_INET;\n    client_addr.sin_port = htons(remote_port_);\n\n    // ROS_INFO_STREAM(\"Socket fd is \" << sockfd_);\n    RCLCPP_INFO(this->get_logger(), \"socket fd is %d\", sockfd_);\n\n}\n\n// 根据QT界面传入的IP地址，更新新的UDP连接\nvoid RobdogCtrlNode::updateSocket(const homi_speech_interface::msg::NewUdpConnect::SharedPtr msg) {\n    // std::string new_devip_str, int new_remote_port, int new_local_port\n    std::string new_devip_str = msg->new_devip_str;\n    int new_remote_port = msg->new_remote_port;\n    int new_local_port = msg->new_local_port;\n    // Check if the new IP address or local port is different from the current ones\n    if (new_devip_str != devip_str_ || new_remote_port != remote_port_ || new_local_port != port_) {\n        // Close the existing sockets\n        if (sockfd_ != -1) {\n            close(sockfd_);\n            sockfd_ = -1;\n        }\n        if (sockfd_customized != -1) {\n            close(sockfd_customized);\n            sockfd_customized = -1;\n        }\n\n        // Update the device IP, remote port, and local port\n        devip_str_ = new_devip_str;\n        remote_port_ = new_remote_port;\n        port_ = new_local_port; // Update local port\n\n        // Reinitialize the socket with the new parameters\n        initSocket(devip_str_, port_, remote_port_);\n    } else {\n        // ROS_INFO_STREAM(\"No change in IP address, remote port, or local port. No update needed.\");\n    }\n}\n\nint RobdogCtrlNode::getPacket(char *pkt, size_t packet_size) {\n    struct pollfd fds[2];\n    fds[0].fd = sockfd_;\n    fds[0].events = POLLIN;\n    fds[1].fd = sockfd_customized;\n    fds[1].events = POLLIN;\n    const int POLL_TIMEOUT = 1000; // 毫秒\n    sockaddr_in sender_address;\n    socklen_t sender_address_len = sizeof(sender_address);\n\tint retval = poll(fds, 2, POLL_TIMEOUT);\n\tif (retval < 0) {\n\t\t\tif (errno != EINTR) {\n\t\t\t\t// ROS_ERROR_STREAM(\"poll() error: \" << strerror(errno));\n\t\t\t}\n\t\t\treturn 1;\n\t}\n\tif (retval == 0) {\n\t\t\treturn 1;\n\t}\n\tif (fds[0].revents & (POLLERR | POLLHUP | POLLNVAL)) {\n\t\t\t// ROS_ERROR(\"poll() reports error\");\n\t\t\treturn 1;\n\t}\n    // 接收数据\n    if (fds[0].revents & POLLIN){\n        memset(pkt, 0, packet_size);\n        ssize_t nbytes = recvfrom(sockfd_, pkt, packet_size, 0,\n                                    (sockaddr *)&sender_address, &sender_address_len);\n\n        if (nbytes < 0) {\n            if (errno != EWOULDBLOCK) {\n                perror(\"recvfail\");\n                // ROS_ERROR(\"recvfail\");\n                return 1;\n                // continue;\n            }\n        } \n        else if (nbytes >= 195){   // 按照云深处给的例程\n        // 收到了数据\n        // else if (static_cast<size_t>(nbytes) == packet_size) {\n            if (!devip_str_.empty() && sender_address.sin_addr.s_addr != devip_.s_addr){\n                // ROS_INFO(\"devip_str_: %s\", devip_str_.c_str());\n                // Convert the IP addresses to strings\n                std::string sender_ip = inet_ntoa(sender_address.sin_addr);\n                std::string dev_ip = inet_ntoa(devip_);\n                // ROS_INFO(\"sender_address.sin_addr: %s, devip_.s_addr: %s\", sender_ip.c_str(), dev_ip.c_str());\n                return 0;\n            }\n        }\n  }\n\tif (fds[1].revents & POLLIN) {\n\t\t\tmemset(pkt, 0, packet_size);\n\t\t\tint num = recvfrom(sockfd_customized, pkt, packet_size, 0,\n\t\t\t\t\t\t\t\t\t\t\t\t(struct sockaddr*)&sender_address, &sender_address_len);\n\t\t\tif (num < 0) {\n\t\t\t\t\tperror(\"recvfrom\");\n\t\t\t\t\treturn 1;\n\t\t\t}\n\t\t\tchar ip_str[INET_ADDRSTRLEN];\n\t\t\tinet_ntop(AF_INET, &sender_address.sin_addr, ip_str, sizeof(ip_str));\n\t\t\tstd::cout << \"Received from \" << ip_str << \":\" \n\t\t\t\t\t\t\t\t<< ntohs(sender_address.sin_port) \n\t\t\t\t\t\t\t\t<< \" on socket \" << sockfd_customized <<std::endl;\n\t\t\thandle_UDP_data(pkt,num);\n  }\n  return 1;\n}\n\n// 接收温度数据\nint RobdogCtrlNode::getPacket_temperature(char *pkt, size_t packet_size) {\n    // EthCommand c;\n    // CommandMessage cm;\n    // // Command cmd_test;\n    // timespec test_time;\n    // static uint32_t imu_count = 0;\n\n    struct pollfd fds[2];\n    fds[0].fd = sockfd_;\n    fds[0].events = POLLIN;\n    fds[1].fd = sockfd_customized;\n    fds[1].events = POLLIN;\n    const int POLL_TIMEOUT = 1000; // 毫秒\n    sockaddr_in sender_address;\n    socklen_t sender_address_len = sizeof(sender_address);\n\tint retval = poll(fds, 2, POLL_TIMEOUT);\n\tif (retval < 0) {\n\t\t\tif (errno != EINTR) {\n\t\t\t\t// ROS_ERROR_STREAM(\"poll() error: \" << strerror(errno));\n\t\t\t}\n\t\t\treturn 1;\n\t}\n\tif (retval == 0) {\n\t\t\treturn 1;\n\t}\n\tif (fds[0].revents & (POLLERR | POLLHUP | POLLNVAL)) {\n\t\t\t// ROS_ERROR(\"poll() reports error\");\n\t\t\treturn 1;\n\t}\n    // 接收数据\n    if (fds[0].revents & POLLIN){\n        memset(pkt, 0, packet_size);\n        ssize_t nbytes = recvfrom(sockfd_, pkt, packet_size, 0,\n                                    (sockaddr *)&sender_address, &sender_address_len);\n\n        if (nbytes < 0) {\n            if (errno != EWOULDBLOCK) {\n                perror(\"recvfail\");\n                // ROS_ERROR(\"recvfail\");\n                return 1;\n                // continue;\n            }\n        } \n        else if (nbytes > 0){   // 接收到了数据\n            if (!devip_str_.empty() && sender_address.sin_addr.s_addr != devip_.s_addr){\n                // ROS_INFO(\"devip_str_: %s\", devip_str_.c_str());\n                // Convert the IP addresses to strings\n                std::string sender_ip = inet_ntoa(sender_address.sin_addr);\n                std::string dev_ip = inet_ntoa(devip_);\n                // ROS_INFO(\"sender_address.sin_addr: %s, devip_.s_addr: %s\", sender_ip.c_str(), dev_ip.c_str());\n                return 0;\n            }\n        }\n    }\n// \tif (fds[1].revents & POLLIN) {\n// \t\t\tmemset(pkt, 0, packet_size);\n// \t\t\tint num = recvfrom(sockfd_customized, pkt, packet_size, 0,\n// \t\t\t\t\t\t\t\t\t\t\t\t(struct sockaddr*)&sender_address, &sender_address_len);\n// \t\t\tif (num < 0) {\n// \t\t\t\t\tperror(\"recvfrom\");\n// \t\t\t\t\treturn 1;\n// \t\t\t}\n// \t\t\tchar ip_str[INET_ADDRSTRLEN];\n// \t\t\tinet_ntop(AF_INET, &sender_address.sin_addr, ip_str, sizeof(ip_str));\n// \t\t\tstd::cout << \"Received from \" << ip_str << \":\" \n// \t\t\t\t\t\t\t\t<< ntohs(sender_address.sin_port) \n// \t\t\t\t\t\t\t\t<< \" on socket \" << sockfd_customized <<std::endl;\n// \t\t\thandle_UDP_data(pkt,num);\n//   }   \n  return 1;\n}\n\nint RobdogCtrlNode::sendPacket(uint8_t *pkt, size_t packet_size)\n{\n    ssize_t nbytes = sendto(sockfd_, pkt, packet_size, 0,\n                            (struct sockaddr *)&client_addr, sizeof(client_addr));\n    if (nbytes < 0) {\n        RCLCPP_ERROR(this->get_logger(), \"Failed to send packet to ysc!!!\");\n         return 1;\n    } else if ((size_t)nbytes == packet_size) {\n        //ROS_INFO(\"Packet sent successfully\"); \n    } else if  ((size_t)nbytes < packet_size&&(size_t)nbytes>0)\n    {\n        RCLCPP_ERROR(this->get_logger(), \"Failed to send packet to ysc!!!Send byte=%d, Should send =%d\",nbytes,packet_size);\n        return 1;\n    }\n\n    return 0;\n}\n\nint RobdogCtrlNode::sendPacketUserDefine(uint8_t *pkt, size_t packet_size) {\n    sockaddr_in userClient_addr;\n    userClient_addr.sin_family = AF_INET;\n    userClient_addr.sin_port = htons(12345);\n    userClient_addr.sin_addr.s_addr = inet_addr(\"127.0.0.1\"); \n    ssize_t nbytes = sendto(sockfd_, pkt, packet_size, 0,\n                            (struct sockaddr *)&userClient_addr, sizeof(userClient_addr));\n    if (nbytes < 0) {\n        RCLCPP_INFO(this->get_logger(), \"Failed to send packetFailed to send packetFailed to send packetFailed to send packetFailed to send packet\");\n        return 1;\n    } else if ((size_t)nbytes == packet_size) {\n    \n    }else if ((size_t)nbytes < packet_size&&(size_t)nbytes>0)\n    {\n        RCLCPP_ERROR(this->get_logger(), \"Failed to send packet to ysc!!!Send byte=%d, Should send =%d\",nbytes,packet_size);\n        return 1;\n    }\n    return 0;\n}\n\nvoid RobdogCtrlNode::StandUp()\n{\n    RCLCPP_INFO(this->get_logger(), \"StandUp StandUp StandUp StandUp StandUp\");\n\n    int flag = RobotInfoMgr::getInstance().checkActionState(\"motorSkill\", \"standUp\");\n    if (!flag)  // 不执行动作\n        robdogCtrlDev->robdogCtrl_StandUp();\n\n    rclcpp::sleep_for(std::chrono::milliseconds(200)); \n    // ros::Duration(0.2).sleep(); // 替换rclcpp::sleep_for\n\n    robdogCtrlDev->robdogCtrl_MoveMode();\n    rclcpp::sleep_for(std::chrono::milliseconds(200)); \n    // ros::Duration(0.2).sleep();\n\n    robdogCtrlDev->robdogCtrl_AutoMode();\n    rclcpp::sleep_for(std::chrono::milliseconds(200)); \n    // ros::Duration(0.2).sleep();\n\n    // command.code = 0x21012109; //开启停障\n    // command.type = 0;\n    // command.size = 0x20;\n    // sendPacket((uint8_t*)&command, sizeof(command));\n\n}\n\nvoid RobdogCtrlNode::GetDown() {\n    RCLCPP_INFO(this->get_logger(), \"GetDown GetDown GetDown GetDown GetDown\");\n    int flag = RobotInfoMgr::getInstance().checkActionState(\"motorSkill\", \"getDown\");\n    if (!flag) \n        robdogCtrlDev->robdogCtrl_GetDown();\n\n}\n\n\n// static int testcnt=0;\nvoid RobdogCtrlNode::HeartBeatCallback() { // 新增了一个参数输入\n    // sleep(10);\n    // RCLCPP_INFO(this->get_logger(), \"TEST SLEEP 10S\");\n}\n\nvoid RobdogCtrlNode:: executeAction(const std::string& MoveSkill) {\n\n    if (MoveSkill == \"standUp\")\n    {\n        robdogCtrlDev->robdogCtrl_StandUp();\n    }\n    else if (MoveSkill == \"getDown\")\n    {\n        robdogCtrlDev->robdogCtrl_GetDown();\n    }\n    else if (MoveSkill == \"twistBody\")\n    {\n        robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_TWIST);\n    }\n    else if (MoveSkill == \"turnOver\")\n    {\n        robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_TURNOVER);\n    }\n    else if (MoveSkill == \"backflip\")\n    {\n        robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_BACKFLIP);\n    }\n    else if (MoveSkill == \"greeting\")\n    {\n        robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_HELLO);\n    }\n    else if (MoveSkill == \"jumpForward\")\n    {\n        robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_JUMPFORWARD);\n    }\n    else if (MoveSkill == \"twistJump\")\n    {\n        robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_TWISTJUMP);\n    }\n    else if (MoveSkill == \"twistAss\")\n    {\n        robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_TWISTASS);\n    }\n    else if (MoveSkill == \"shakeBody\")\n    {\n        robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_FASTSHAKEBODY);\n    }\n    else if (MoveSkill == \"dance\")\n    {\n        robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_DANCE);\n    }\n\n    std::cout << \"Executing action: \" << MoveSkill << std::endl;\n}\n\nvoid RobdogCtrlNode::startStandUpToExcute(std::function<void()> callback, std::chrono::seconds timeout) {\n    std::atomic<bool> is_standing_{false};\n    std::atomic<bool> should_continue_{true};\n    std::mutex mtx_;\n    std::condition_variable cv_;\n    std::thread([this, &cv_, &mtx_, &is_standing_, &should_continue_]() mutable {\n        executeAction(\"standUp\");\n        while (should_continue_)\n        {\n            if (RobotInfoMgr::getInstance().getRobotStatus()==ROBDOG_STATUS_FORCE_CTRL_LEVEL_LOWSPPEED_GAIT){\n                std::lock_guard<std::mutex> lock(mtx_);\n                is_standing_ = true; \n                cv_.notify_one();  \n                break; \n            }\n            std::this_thread::sleep_for(std::chrono::milliseconds(100));\n        }\n    }).detach();\n    std::unique_lock<std::mutex> lock(mtx_);\n    if (cv_.wait_for(lock, timeout, [&]() { return is_standing_.load(); })) {\n        std::cout << \"Robot is standing!\" << std::endl;\n        callback();\n    } else {\n        RCLCPP_INFO(this->get_logger(), \"Timeout: Robot did not stand up in time.\");\n        should_continue_.store(false);  \n        callback();\n    }\n}\nvoid RobdogCtrlNode::sendAudio(const std::string& path) {\n    if (!path.empty()){\n        std::thread t_audio(&RobdogCtrlNode::playAudio, this, path);\n        t_audio.detach();\n    }\n}\n// 运动功能\nvoid RobdogCtrlNode::MoveSkillscallback(const homi_speech_interface::msg::RobdogAction::SharedPtr msg) { \n\n    robdogCtrlDev->robdogCtrl_AutoMode();\n    \n    // 新增了一个参数输入\n    // RCLCPP_INFO(this->get_logger(), \"actiontype: %s, actionargument: %s\", msg->actiontype.c_str(), msg->actionargument.c_str());\n    // actionargument 不一定有\n    // 在执行动作之前把 当前状态类型 以及 动作指令 传到状态管理模块    \n    std::string actiontype = msg->actiontype;\n    if (msg && !msg->actionargument.empty()) {\n        std::string actionargument = msg->actionargument;\n        if (RobotInfoMgr::getInstance().checkActionState(actiontype, actionargument)) return; // 不执行动作\n    }\n    // 跟随或避障\n    if (actiontype == \"followMe\"||actiontype==\"tripStart\"){\n      std::string follome = msg->actionargument; // 详细的运动技能\n      if (follome == \"on\") {\n        \n        robdogCtrlDev->robdogCtrl_AutoMode();\n\n        RCLCPP_INFO(this->get_logger(), \"Set mode Autonomous mode(followMe/Trip Start)\");\n      }else if (follome == \"off\"||follome == \"comeHere\") {\n        robdogCtrlDev->robdogCtrl_AutoMode();\n      }\n\n    }\n    // 运动模式\n    if (actiontype == \"sportMode\"){\n      std::string SportMode = msg->actionargument; \n      // SportMode = msg.actionargument; \n      if (SportMode == \"walk\") {\n\t  \t//低速步态\n        robdogCtrlDev->robdogCtrl_ChangeGait(ROBDOGCTRL_GAIT_WALK);\n      }\n      else if (SportMode == \"run\") {\n\t  \t//高速步态\n        robdogCtrlDev->robdogCtrl_ChangeGait(ROBDOGCTRL_GAIT_RUN);\n      }\n      else if (SportMode == \"stairClimbe\") {\n        robdogCtrlDev->robdogCtrl_ChangeGait(ROBDOGCTRL_GAIT_STAIRCLIMB);\n      }\n      else if (SportMode == \"climbe\") {\n        robdogCtrlDev->robdogCtrl_ChangeGait(ROBDOGCTRL_GAIT_CLIMB);\n      }\n      else if (SportMode == \"AIClassic\") {\n        robdogCtrlDev->robdogCtrl_ChangeGait(ROBDOGCTRL_GAIT_AICLASSIC);\n      }\n      else if (SportMode == \"AINimble\") {\n        robdogCtrlDev->robdogCtrl_ChangeGait(ROBDOGCTRL_GAIT_AINIMBLE);\n      }\n      else if (SportMode == \"jumpRun\") {\n        robdogCtrlDev->robdogCtrl_ChangeGait(ROBDOGCTRL_GAIT_FREEJUMP);\n      }\n      else if (SportMode == \"runSide\") {\n        robdogCtrlDev->robdogCtrl_ChangeGait(ROBDOGCTRL_GAIT_FREEBOUND);\n      }\n\t  else if (SportMode == \"medium_speed\")\n\t  {\n\t  \t//中速步态\n        #if defined(UNITREE)\n        #else\n        #endif\n\t  }\n\n    // 牵引模式后续会提供接口\n    //   else if (SportMode == \"traction\") {\n    //       command.code = 0x21010300; // 平地低速步态\n    //       command.type = 0;\n    //       command.size = 0;\n    //       sendPacket((uint8_t*)&command, sizeof(command));\n    //   }\n    }\n    // 运动技能\n    if (actiontype == \"motorSkill\"){\n        RCLCPP_INFO(this->get_logger(), \"actiontype: %s, actionargument: %s\", msg->actiontype.c_str(), msg->actionargument.c_str());\n        /*\n        扭身体 0x21010204\n        翻身 0x21010205\n        太空步 0x2101030C\n        后空翻 0x21010502\n        打招呼 0x21010507\n        向前跳 0x2101050B\n        扭身跳 0x2101020D\n        \n        还没实现的指令：\n            比心：fingerHeart \n            作yi:makeBow\n        */\n        std::string MoveSkill = msg->actionargument;\n        // 原本：在站立和趴下之间切换【如果在站立情况下再喊站立会导致趴下】 \n        // try {\n        //     if (RobotInfoMgr::getInstance().getRobotStatus()==ROBDOG_STATUS_FORCE_CTRL_LEVEL_LOWSPPEED_GAIT) {\n        //         executeAction(MoveSkill);\n        //     } else {\n        //         startStandUpToExcute([this, MoveSkill]() { this->executeAction(MoveSkill);}, std::chrono::seconds(5));\n        //     }\n        // } catch (const std::exception& e) {\n        //     std::cerr << \"Error: \" << e.what() << '\\n';\n        // }\n\n        if(MoveSkill == \"standUp\"){// 趴下状态 && robdog_status == 1 \n            robdogCtrlDev->robdogCtrl_StandUp();\n            ExpressionChange::getInstance().async_callback_work(getResourcePath(\"video/standUp/\"),1);\n        }\n\n        else if(MoveSkill == \"getDown\"){ // && robdog_status != 1){ \n            robdogCtrlDev->robdogCtrl_GetDown();\n            ExpressionChange::getInstance().async_callback_work(getResourcePath(\"video/getDown/\"),1);\n        }\n\n        else if(MoveSkill == \"twistBody\"){\n            robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_TWIST);\n            ExpressionChange::getInstance().async_callback_work(getResourcePath(\"video/twist/\"),1);\n            std::thread t_audio(&RobdogCtrlNode::playAudio,this, getResourcePath(\"audio/twist/\"));\n            t_audio.detach();\n            // std::thread audioTwist(&RobdogCtrlNode::playAudio,this, \"/home/<USER>/resource/audio/twist/\");\n            // audioTwist.detach();\n            // ExpressionChange::getInstance().async_callback_work(\"/home/<USER>/resource/vedio/twist/\",1);\n            this->handleLightControl(DEEP_CMD_LIGHT_ORANGE_KEEP_AWAKE, 8);\n        }\n        // else if(MoveSkill == \"turnOver\"){\n        //     system(\"/home/<USER>/updateexpression.sh /home/<USER>/resource/眨眼+开心+星星眼.mp4\"); \n        //     robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_TURNOVER);\n        // }\n        \n        // else if(MoveSkill == \"backflip\"){\n        //     system(\"/home/<USER>/updateexpression.sh /home/<USER>/resource/眨眼+开心+星星眼.mp4\"); \n        //     robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_BACKFLIP);\n        // }\n\n        else if(MoveSkill == \"greeting\"){\n            robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_HELLO);\n            ExpressionChange::getInstance().async_callback_work(getResourcePath(\"video/greeting/\"),1);\n            std::thread t_audio(&RobdogCtrlNode::playAudio,this, getResourcePath(\"audio/hello/\"));\n            t_audio.detach();\n            // std::thread audioGreet(&RobdogCtrlNode::playAudio,this, \"/home/<USER>/resource/audio/hello/\");\n            // audioGreet.detach();\n            // ExpressionChange::getInstance().async_callback_work(\"/home/<USER>/resource/vedio/hello/\",1);\n        }\n\n        // else if(MoveSkill == \"jumpForward\"){\n        //     system(\"/home/<USER>/updateexpression.sh /home/<USER>/resource/step1_戴墨镜.mp4\"); \n        //     deep_ctl(DEEP_CMD_LIGHT, DEEP_CMD_LIGHT_8, 0);\n        //     robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_JUMPFORWARD);\n        // }\n\n        // else if(MoveSkill == \"twistJump\"){\n        //     system(\"/home/<USER>/updateexpression.sh /home/<USER>/resource/step1_戴墨镜.mp4\"); \n        //     robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_TWISTJUMP);\n        // }\n\n        else if(MoveSkill == \"sitDown\"){ \n            robdogCtrlDev->robdogCtrl_Sit();\n            ExpressionChange::getInstance().async_callback_work(getResourcePath(\"video/sitDown/\"),1);\n            std::thread t_audio(&RobdogCtrlNode::playAudio,this, getResourcePath(\"audio/sitDown/\"));\n            t_audio.detach();\n            // std::thread audioGreet(&RobdogCtrlNode::playAudio, this,\"/home/<USER>/resource/audio/sitDown\");\n            // audioGreet.detach();           \n            // ExpressionChange::getInstance().async_callback_work(\"/home/<USER>/resource/左看上看眨眼.mp4\",1);\n        }\n        else if(MoveSkill == \"twistAss\"){ // 蹦达 twistAss\n            robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_TWISTASS);\n            RCLCPP_INFO(this->get_logger(), \"before ExpressionChange::getInstance().async_callback_work\");\n            // ExpressionChange::getInstance().async_callback_work(\"/home/<USER>/resource/vedio/jumping/\",1);\n            ExpressionChange::getInstance().async_callback_work(getResourcePath(\"video/twistAss/\"),1);\n            RCLCPP_INFO(this->get_logger(), \"AFTER ExpressionChange::getInstance().async_callback_work\");\n            // std::thread audioGreet(&RobdogCtrlNode::playAudio, this,\"/home/<USER>/resource/audio/jumping/153164244739772416.wav\");\n            // audioGreet.detach();\n            std::thread t_audio(&RobdogCtrlNode::playAudio,this, getResourcePath(\"audio/jumping/153164244739772416.wav\"));\n            t_audio.detach();\n            this->handleLightControl(DEEP_CMD_LIGHT_ORANGE_KEEP_AWAKE, 8);\n        }\n        else if(MoveSkill == \"shakeBody\"){ // 摇摆 shakeBody\n            robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_SHAKEBODY);\n            // ExpressionChange::getInstance().async_callback_work(\"/home/<USER>/resource/vedio/shake/\",1);\n            // std::thread audioGreet(&RobdogCtrlNode::playAudio,this, \"/home/<USER>/resource/audio/Slowshake/\");\n            // audioGreet.detach();\n            ExpressionChange::getInstance().async_callback_work(getResourcePath(\"video/shake/\"),1);\n            std::thread t_audio(&RobdogCtrlNode::playAudio,this, getResourcePath(\"audio/Slowshake/\"));\n            t_audio.detach();\n            this->handleLightControl(DEEP_CMD_LIGHT_ORANGE_KEEP_AWAKE, 8);\n        }\n        else if(MoveSkill == \"fastShakeBody\"){ // 快速摇摆 FastshakeBody\n            robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_FASTSHAKEBODY);\n            // ExpressionChange::getInstance().async_callback_work(\"/home/<USER>/resource/vedio/shake/\",1);\n            // std::thread audioGreet(&RobdogCtrlNode::playAudio,this, \"/home/<USER>/resource/audio/shake/\");\n            // audioGreet.detach();\n            ExpressionChange::getInstance().async_callback_work(getResourcePath(\"video/fastShakeBody/\"),1);\n            std::thread t_audio(&RobdogCtrlNode::playAudio,this, getResourcePath(\"audio/shake/\"));\n            t_audio.detach();\n            this->handleLightControl(DEEP_CMD_LIGHT_ORANGE_KEEP_AWAKE, 8);\n        }\n        else if(MoveSkill == \"dance\"){\n            robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_DANCE);\n            // ExpressionChange::getInstance().async_callback_work(\"/home/<USER>/resource/vedio/dance/\",1);\n            // std::thread audioGreet(&RobdogCtrlNode::playAudio, this,\"/home/<USER>/resource/audio/dance/\");\n            // audioGreet.detach();\n            ExpressionChange::getInstance().async_callback_work(getResourcePath(\"video/dance/\"),1);\n            std::thread t_audio(&RobdogCtrlNode::playAudio,this, getResourcePath(\"audio/dance/\"));\n            t_audio.detach();\n        }\n        else if (MoveSkill==\"stretch\") // 伸懒腰\n        {\n            robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_STRETCH);\n            // ExpressionChange::getInstance().async_callback_work(\"/home/<USER>/resource/vedio/dance/\",1);\n            // std::thread audioGreet(&RobdogCtrlNode::playAudio, this,\"/home/<USER>/resource/audio/stretch/\");\n            // audioGreet.detach();\n            ExpressionChange::getInstance().async_callback_work(getResourcePath(\"video/stretch/\"),1);\n            std::thread t_audio(&RobdogCtrlNode::playAudio,this, getResourcePath(\"audio/stretch/\"));\n            t_audio.detach();\n        }\n        else if (MoveSkill==\"chestOut\") // 挺胸\n        {\n            robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_CHESTOUT);\n            // ExpressionChange::getInstance().async_callback_work(\"/home/<USER>/resource/vedio/星星眼.mp4\",1);\n            // std::thread audioGreet(&RobdogCtrlNode::playAudio, this,\"/home/<USER>/resource/audio/chestOut/\");\n            // audioGreet.detach();\n            ExpressionChange::getInstance().async_callback_work(getResourcePath(\"video/chestOut/\"),1);\n            std::thread t_audio(&RobdogCtrlNode::playAudio,this, getResourcePath(\"audio/chestOut/\"));\n            t_audio.detach();\n            this->handleLightControl(DEEP_CMD_LIGHT_ORANGE_KEEP_AWAKE, 8);\n        }\n        else if (MoveSkill==\"newYearCall\")//拜年，作揖\n        {\n            robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_NEWYEARCALL);\n            // ExpressionChange::getInstance().async_callback_work(\"/home/<USER>/resource/vedio/笑眯眯飘爱心.mp4\",1);\n            // std::thread audioGreet(&RobdogCtrlNode::playAudio, this,\"/home/<USER>/resource/audio/happyNewYear/\");\n            // audioGreet.detach();\n            ExpressionChange::getInstance().async_callback_work(getResourcePath(\"video/happyNewYear/\"),1);\n            std::thread t_audio(&RobdogCtrlNode::playAudio,this, getResourcePath(\"audio/happyNewYear/\"));\n            t_audio.detach();\n            this->handleLightControl(DEEP_CMD_LIGHT_PINK_KEEP_AWAKE, 8);\n        }\n        else if (MoveSkill==\"fingerHeart\")//比心\n        {\n            robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_FINGERHEART);\n            // ExpressionChange::getInstance().async_callback_work(\"/home/<USER>/resource/vedio/笑眯眯飘爱心.mp4\",1);\n            // std::thread audioGreet(&RobdogCtrlNode::playAudio, this,\"/home/<USER>/resource/audio/Fingerheart/\");\n            // audioGreet.detach();\n            ExpressionChange::getInstance().async_callback_work(getResourcePath(\"video/fingerHeart/\"),1);\n            std::thread t_audio(&RobdogCtrlNode::playAudio,this, getResourcePath(\"audio/Fingerheart/\"));\n            t_audio.detach();\n            this->handleLightControl(DEEP_CMD_LIGHT_PINK_KEEP_AWAKE, 8);\n        }\n        else if (MoveSkill==\"happy\")            // 开心\n        {\n            robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_HAPPY);\n        }\n        else if(MoveSkill == \"jumpForward\")     // 向前跳\n        {\n            robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_JUMPFORWARD);\n        }\n        else if (MoveSkill==\"leap\")             // 扑人\n        {\n            robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_LEAP);\n        }\n        else if (MoveSkill==\"danceV2\")          // 跳舞2\n        {\n            robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_DANCEV2);\n        }\n        else if (MoveSkill==\"walkUpsideDown\")   // 倒立\n        {\n            robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_HANDSTAND);\n        }\n        else if (MoveSkill==\"standErect\")       // 直立\n        {\n            robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_WALKUPRIGHT);\n        }\n    }\n    if (actiontype == \"emergencyStop\"){\n        \n        robdogCtrlDev->robdogCtrl_StopMove();\n        memset(&g_MoveData, 0, sizeof(g_MoveData));\n        \n        if (timer_active) {\n            robdogCtrlDev->robdogCtrl_AutoMode();\n            // timer.stop();\n            timer->cancel();\n            timer_active = false;\n            // ROS_INFO(\"Timer is stopped,Robot move stopped.\");\n        }\n\n        robdogCtrlDev->robdogCtrl_EmergencyStop();\n    }\n    if (actiontype == \"resetZero\"){ \n        robdogCtrlDev->robdogCtrl_ResetZero();\n    }\n    // 和强化学习有关的\n    // if (actiontype == \"ObstaclesRL\"){ \n    //     command.code = 0x21010529;\n    //     command.type = 0;\n    //     command.size = 2;  // 指令值\n    //     sendPacket((uint8_t*)&command, sizeof(command));\n    // }\n    // if (actiontype == \"FlatRL\"){ \n    //     command.code = 0x2101052a;\n    //     command.type = 0;\n    //     command.size = 2;  // 指令值\n    //     sendPacket((uint8_t*)&command, sizeof(command));\n    // }\n    // if (actiontype == \"ExitRL\"){ \n    //     command.code = 0x2101052b;\n    //     command.type = 0;\n    //     command.size = 2;  // 指令值\n    //     sendPacket((uint8_t*)&command, sizeof(command));\n    // }\n    if (actiontype == \"NavCtrl\"){\n        std::string MoveSkill = msg->actionargument;\n        if(MoveSkill == \"AutoMode\"){ \n            robdogCtrlDev->robdogCtrl_AutoMode();\n        }\n    }\n    // 和强化学习有关的（最终平台用到的接口）\n    if (actiontype == \"gaitControl\"){ \n        std::string MoveSkill = msg->actionargument;\n        if(MoveSkill == \"obstacleCross\"){ \n            robdogCtrlDev->robdogCtrl_ChangeGait(ROBDOGCTRL_GAIT_OBSTACLE_CROSS);\n            RobotState::getInstance().setIntelligentSwitch(\"on\");\n        }\n        if(MoveSkill == \"flatGround\"){ \n            robdogCtrlDev->robdogCtrl_ChangeGait(ROBDOGCTRL_GAIT_FLATGROUND);\n        }\n        if(MoveSkill == \"exit\"){ \n            robdogCtrlDev->robdogCtrl_ChangeGait(ROBDOGCTRL_GAIT_EXIT);\n            RobotState::getInstance().setIntelligentSwitch(\"off\");\n        }\n    }\n    if (actiontype == \"rotateCtl\"){\n        std::string angle = msg->actionargument;\n        float real_angle=std::stoi(angle)*static_cast<float>(M_PI) / 180.0f;\n        positionCtrl(0.0f,0.0f,real_angle);\n    }\n}\n\nvoid RobdogCtrlNode::timerCallbackForRobotMove() \n{\n    //RCLCPP_WARN(this->get_logger(), \"================================\");\n    //RCLCPP_WARN(this->get_logger(), \"event: %s\", g_MoveData.event);\n    //RCLCPP_WARN(this->get_logger(), \"x: %d, y: %d, z: %d\", g_MoveData.x, g_MoveData.y, g_MoveData.z);\n    //RCLCPP_WARN(this->get_logger(), \"yaw: %d, pitch: %d, roll: %d\", g_MoveData.yaw, g_MoveData.pitch, g_MoveData.roll);\n    //RCLCPP_WARN(this->get_logger(), \"================================\");\n\n    robdogCtrlDev->robdogCtrl_ContinueMove(&g_MoveData);\n}\n\nvoid RobdogCtrlNode::continueMovecallback(const homi_speech_interface::msg::ContinueMove::SharedPtr robot_motion){\n\n    g_robot_motion=robot_motion;\n\n    if(robot_motion->event == \"robot_move\")\n    {\n        // std::array<double, 2> ultralS = RobotInfoMgr::getInstance().getUltrasound();\n        // if (ultralS[0]<=ULTS_THR){\n        //     RCLCPP_WARN(this->get_logger(), \"An obstacle was detected 0.28 meters forward\");\n        //     std::thread t_audio(&RobdogCtrlNode::playAudio,this, \"/home/<USER>/resource/audio/Obstacle/\");\n        //     t_audio.detach();\n        // }\n        setRobotMove(robot_motion->x, robot_motion->y, robot_motion->z);\n        if (robot_motion->x != 0||robot_motion->y!= 0||robot_motion->z!=0)\n        {\n            if (!timer_active) \n            {\n                robdogCtrlDev->robdogCtrl_MoveMode();\n                robdogCtrlDev->robdogCtrl_ManualMode();\n                robdogCtrlDev->robdogCtrl_AvoidClose();\n\n                timer = this->create_wall_timer(std::chrono::milliseconds(50), // 0.03秒\n                    std::bind(&RobdogCtrlNode::timerCallbackForRobotMove, this));\n                timer_active = true;\n            }\n        }\n        else\n            RCLCPP_WARN(this->get_logger(), \"Receive robot move cmd. But value is 0\");\n    }\n    else if (robot_motion->event == \"robot_view\")\n    {\n        setRobotView(robot_motion->yaw, robot_motion->pitch, robot_motion->roll);\n        if (robot_motion->pitch != 0||robot_motion->yaw!= 0||robot_motion->roll!=0) \n        {\n            if (!timer_active) \n            {\n                robdogCtrlDev->robdogCtrl_MoveMode();\n                robdogCtrlDev->robdogCtrl_ManualMode();\n                robdogCtrlDev->robdogCtrl_AvoidClose();\n\n                // timer = ros::NodeHandle().createTimer(ros::Duration(0.03), &RobdogCtrlNode::timerCallbackForRobotMove,this);\n                timer = this->create_wall_timer(\n                    std::chrono::milliseconds(50), // 0.03秒\n                    std::bind(&RobdogCtrlNode::timerCallbackForRobotMove, this));\n                timer_active = true;\n                // ROS_INFO(\"Timer is running,Robot move started.\");\n            }\n        }\n        else\n            // ROS_WARN(\"Recieve robot view cmd.But value is 0\");\n            RCLCPP_WARN(this->get_logger(), \"Receive robot view cmd. But value is 0\");\n    }\n    // 移动固定距离和转向\n    else if(robot_motion->event == \"stepMode\")\n    {\n        // ROS_INFO(\"Recieve Remote Ctrl robot_motion.event=%s,x=%d,y=%d,z=%d,yaw=%d,pitch=%d,roll=%d\",robot_motion.event.c_str(),robot_motion.x,robot_motion.y,robot_motion.z,robot_motion.yaw,robot_motion.pitch,robot_motion.roll);\n        // ROS_INFO(\"stepMode: x = %d, y = %d, yaw = %d\", robot_motion.x, robot_motion.y, robot_motion.yaw);\n        // robot_motion.x表示向前走的步数，一步表示20cm\n        // robot_motion.y表示向左走的步数，一步表示20cm\n        // robot_motion.yaw表示向右转的角度(角度制)\n        float stepx = robot_motion->x * 0.2;\n        float stepy = robot_motion->y * 0.2;\n        positionCtrl(stepx,stepy,((robot_motion->yaw)*static_cast<float>(M_PI) / 180.0f));\n    }\n    else if(robot_motion->event == \"stopAction\")\n    {\n        if ((g_MoveData.x != 0) || (g_MoveData.y != 0) || (g_MoveData.z != 0) || \n            (g_MoveData.pitch != 0) || (g_MoveData.yaw != 0) || (g_MoveData.roll != 0))\n        {\n            RCLCPP_INFO(this->get_logger(), \"=============== Not Stop =================\");\n            RCLCPP_INFO(this->get_logger(), \"event: %s\", g_MoveData.event);\n            RCLCPP_INFO(this->get_logger(), \"x: %d, y: %d, z: %d\", g_MoveData.x, g_MoveData.y, g_MoveData.z);\n            RCLCPP_INFO(this->get_logger(), \"yaw: %d, pitch: %d, roll: %d\", g_MoveData.yaw, g_MoveData.pitch, g_MoveData.roll);\n            RCLCPP_INFO(this->get_logger(), \"==========================================\");\n\n            return;\n        }\n\n        robdogCtrlDev->robdogCtrl_StopMove();\n        memset(&g_MoveData, 0, sizeof(g_MoveData));\n        \n        if (timer_active) {\n            robdogCtrlDev->robdogCtrl_AutoMode();\n            // timer.stop();\n            timer->cancel();\n            timer_active = false;\n            // ROS_INFO(\"Timer is stopped,Robot move stopped.\");\n        }\n        // ROS_INFO(\"Timer has already stopped.\");\n    }\n    else if(robot_motion->event == \"stopActionDelay\")\n    {\n        robdogCtrlDev->robdogCtrl_StopMove();\n        memset(&g_MoveData, 0, sizeof(g_MoveData));\n        \n        if (timer_active) {\n            robdogCtrlDev->robdogCtrl_AutoMode();\n            // timer.stop();\n            timer->cancel();\n            timer_active = false;\n            // ROS_INFO(\"Timer is stopped,Robot move stopped.\");\n        }\n        // ROS_INFO(\"Timer has already stopped.\");\n    }\n}\n\n// 收到速度之后的回调函数(把指令发给机器狗) \nvoid RobdogCtrlNode::velCmdCallback(const geometry_msgs::msg::Twist::SharedPtr msg)\n{\n    const auto current_steady = Clock::now();\n    const auto elapsed = current_steady - last_vel_steady_time_;\n    const auto elapsed_ms = std::chrono::duration_cast<std::chrono::milliseconds>(elapsed);\n    if (elapsed_ms.count() < VEL_TIME_INTERVAL) {\n        return; \n    }\n    last_vel_steady_time_ = current_steady;\n\n    robdogCtrlDev->robdogCtrl_AutoMode();\n    \n    robdogCtrlDev->robdogCtrl_Move(msg->linear.x, msg->linear.y, msg->angular.z);\n\n    RCLCPP_INFO_THROTTLE(this->get_logger(),*this->get_clock(),3000,\"send vel message to dog successful\");\n}\n\n// 向机器狗请求机器人状态信息【在线程里面去执行】\nvoid RobdogCtrlNode::send_command() \n{\n    robdogCtrlDev->robdogCtrl_State();\n\n    // std::this_thread::sleep_for(std::chrono::milliseconds(20)); // 1/50 second\n}\n\n// 向机器人请求温度信息\nvoid RobdogCtrlNode::send_command_temperature()\n{\n    robdogCtrlDev->robdogCtrl_Temperature();\n    // std::this_thread::sleep_for(std::chrono::milliseconds(20)); // 1/50 second\n}\n\n// 在这里将机器人状态信息发布出去【应该在界面按键触发之后再去发布还是定时发布？】\nvoid RobdogCtrlNode::publishRobdagStateToQt(std::string robot_state_details) {\n    homi_speech_interface::msg::RobdogState stateMsg;    \n    stateMsg.robot_state_details = robot_state_details;\n    robotStatusPub_->publish(stateMsg);\n}\nvoid RobdogCtrlNode::changeExpression(const int status) {\n    auto it = state_path_map_.find(status);\n    if (it != state_path_map_.end()) {\n        std_msgs::msg::String path_msg;\n        path_msg.data = it->second;\n        robotExpressionPub_->publish(path_msg);\n        RCLCPP_INFO(this->get_logger(), \"State changed to %d, publishing path: %s\", status, path_msg.data.c_str());\n    } else {\n        RCLCPP_WARN(this->get_logger(), \"No path configured for state %d\", status);\n    }\n}\n\nvoid RobdogCtrlNode:: handle_UDP_data(char *data,size_t length)\n{\n    head_t* header = reinterpret_cast<head_t*>(data);\n    rsp_body_t body;\n    pk_t *pkg=(pk_t*)data;\n\n   \n    if (length >= sizeof(head_t)) {\n        switch (ntohl(header->cmd)) {\n            case DEEP_CMD_SENSOR:\n                if (ntohl(pkg->rsp_body.sensor.value)==0x30){\n                    robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_TWIST);\n                    RCLCPP_INFO(this->get_logger(), \"before ExpressionChange::getInstance().async_callback_work\");\n                    ExpressionChange::getInstance().async_callback_work(getResourcePath(\"video/touch/\"),1);\n                    RCLCPP_INFO(this->get_logger(), \"after ExpressionChange::getInstance().async_callback_work\");\n                    // std::thread t_expression(execute_script, \"/home/<USER>/resource/vedio/touch/\");\n                    // t_expression.detach();\n                    std::thread t_audio(&RobdogCtrlNode::playAudio,this, getResourcePath(\"audio/touch/\"));\n                    t_audio.detach();\n                    // deep_ctl(DEEP_CMD_LIGHT, DEEP_CMD_LIGHT_8, 0);\n                }\n                break;\n            case DEEP_CMD_POWER:\n                if (length >= sizeof(head_t) + sizeof(power_status)) {\n                    homi_speech_interface::msg::ProprietySet repMsg;\n                    memcpy(&body.powerAttribute, data + sizeof(head_t), sizeof(power_status));\n                    repMsg.cmd=POWER_LEVEL_FROM_NODE;\n                    repMsg.value=body.powerAttribute.power_level;\n                    repMsg.exvalue=body.powerAttribute.chargeStatus;\n                    statusReportPub->publish(repMsg);\n                    // ROS_INFO(\"Recieve battery info,going to pubulish.Battery level is[%d],Battery charging state is [%d]\",repMsg.value,repMsg.exvalue);\n                }\n                else\n                    // ROS_ERROR_STREAM(\"Battery's data is wrong\");\n\n            default:\n                // ROS_ERROR_STREAM( \"Unknown command code: \" << std::hex << static_cast<int>(header->cmd) << std::dec << std::endl);\n                break;\n        }\n    } else {\n        // ROS_ERROR_STREAM( \"Invalid packet length: \" << length << std::endl);\n    }\n}\n\nvoid RobdogCtrlNode::deep_ctl(int cmdID,int cmdValue,int cmdValueEx)\n{\n    pk_t pkg_deep;\n    memset(&pkg_deep,0,sizeof(pkg_deep));\n    // ROS_INFO_STREAM(\"Start to control deep robot,cmd id is \"<<std::hex<<cmdID);\n    // ROS_INFO(\"Start to control deep robot,cmd id is 0x%x,cmdValue=0x%x,cmdValueEx=0x%x\",cmdID,cmdValue,cmdValueEx);\n    // 设置头部信息\n    pkg_deep.head.len=hton_int(sizeof(pk_t));\n    pkg_deep.head.stat = hton_int(DEEP_STATUS_RUN); \n    pkg_deep.head.cmd=hton_int(cmdID);\n    std::string directory_head = \"/data/test/audio/head/\";\n    std::string directory_back = \"/data/test/audio/back/\";\n    std::string randomFile ;\n\n    switch (cmdID)\n    {\n    case DEEP_CMD_LIGHT:\n      pkg_deep.rqt_body.light.color = hton_int(cmdValue); \n    //   pkg_deep.rqt_body.light.duration = 5000;          // 假设持续时间为5秒\n      break;\n    case DEEP_CMD_EXPRESSION:\n      pkg_deep.rqt_body.expression.type = cmdValue; \n      break;\n    case DEEP_CMD_MOTION:\n      pkg_deep.rqt_body.motion.type = cmdValue; \n      break;\n    case DEEP_CMD_NET:\n      pkg_deep.rqt_body.net.netswitch = cmdValue; \n      break;\n    case DEEP_CMD_MOVEANDRO:\n      pkg_deep.rqt_body.attitude.angle=cmdValue;\n      pkg_deep.rqt_body.attitude.angle=cmdValueEx;\n      break;\n    case DEEP_CMD_FLASHLIGHT:\n      if (cmdValue==0)\n        pkg_deep.rqt_body.flashlight.brightness=0;\n      else\n        {pkg_deep.rqt_body.flashlight.brightness=hton_int(cmdValue);}\n        break;\n    case DEEP_CMD_AI_MOTION:\n      pkg_deep.rqt_body.intelligentSwitch.type =cmdValue;\n      break;\n    case DEEP_CMD_AUDIO:\n        // if (cmdValue==DEEP_CMD_AUDIO_HEAD)\n        //   randomFile=getRandomFilePath(directory_head);\n        // else \n        //   randomFile=getRandomFilePath(directory_back);\n        if (!randomFile.empty()) {\n            // ROS_INFO_STREAM(\"Play audio file: \" << randomFile << std::endl);\n        } else {\n            // ROS_ERROR_STREAM(\"No files found in the directory\" << std::endl);\n        }\n      memcpy(pkg_deep.rqt_body.audio.path,randomFile.c_str(),sizeof(pkg_deep.rqt_body.audio.path));\n      break;\n    default:\n      break;\n    }\n    sendPacketUserDefine((uint8_t*)&pkg_deep, sizeof(pkg_deep));\n}\n\nvoid RobdogCtrlNode::leftNine(){\n    // CommandHead command_handpos;  \n    // command_handpos.code = 0x21010C0A; // 左转90度\n    // command_handpos.type = 0;\n    // command_handpos.size = 13;  \n    // sendPacket((uint8_t*)&command_handpos, sizeof(command_handpos));\n\n    positionCtrl(0,0,(90*static_cast<float>(M_PI) / 180.0f));\n\n    if (robActionSuspendTimer_) {\n        robActionSuspendTimer_->cancel();\n    }\n}\n\nvoid RobdogCtrlNode::rightNine(){\n    // CommandHead command_handpos;  \n    // command_handpos.code = 0x21010C0A; // 右转90度\n    // command_handpos.type = 0;\n    // command_handpos.size = 14;  \n    // sendPacket((uint8_t*)&command_handpos, sizeof(command_handpos));\n\n    positionCtrl(0,0,(-90*static_cast<float>(M_PI) / 180.0f));\n\n    if (robActionSuspendTimer_) {\n        robActionSuspendTimer_->cancel();\n    }\n}\n\nvoid RobdogCtrlNode::cancelHandCode(){\n\n    robdogCtrlDev->robdogCtrl_VoiceStand(0);\n\n    if (robCancelHandCode_) {\n        robCancelHandCode_->cancel();\n    }\n}\n\n// 手势识别对应的动作的执行\nvoid RobdogCtrlNode::handleHandPosAction(int pos_type){\n\n    robdogCtrlDev->robdogCtrl_ManualMode();\n\n    if(pos_type == 1){ // 打招呼\n        robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_HELLO);\n        // ExpressionChange::getInstance().async_callback_work(\"/home/<USER>/resource/vedio/shake/\",1); // 播放表情 心心眼\n        // std::thread audioGreet(&RobdogCtrlNode::playAudio, this,\"/home/<USER>/resource/audio/handpos/handposGreeting.wav\");\n        // audioGreet.detach();\n        ExpressionChange::getInstance().async_callback_work(getResourcePath(\"video/shake/\"),1);\n        std::thread t_audio(&RobdogCtrlNode::playAudio,this, getResourcePath(\"audio/handpos/handposGreeting.wav\"));\n        t_audio.detach();\n    } else if(pos_type == 2){ // 扭身体\n        robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_TWIST);\n        RCLCPP_WARN(this->get_logger(), \"sockfd_sockfd_sockfd_sockfd_sockfd_sockfd_sockfd_ %d\", sockfd_);\n        // ExpressionChange::getInstance().async_callback_work(\"/home/<USER>/resource/vedio/dance/\",1); // 播放表情 墨镜\n        // std::thread audioGreet(&RobdogCtrlNode::playAudio, this,\"/home/<USER>/resource/audio/handpos/handposTwistbody.wav\");\n        // audioGreet.detach();\n        ExpressionChange::getInstance().async_callback_work(getResourcePath(\"video/dance/\"),1);\n        std::thread t_audio(&RobdogCtrlNode::playAudio,this, getResourcePath(\"audio/handpos/handposTwistbody.wav\"));\n        t_audio.detach();\n    } else if(pos_type == 3){ // 坐下\n        robdogCtrlDev->robdogCtrl_VoiceStand(2);\n\n        // 消除手动指令\n        robCancelHandCode_ = this->create_wall_timer(\n            std::chrono::seconds(1),   \n            std::bind(&RobdogCtrlNode::cancelHandCode, this)\n        ); \n\n        // ExpressionChange::getInstance().async_callback_work(\"/home/<USER>/resource/vedio/shake/\",1); // 播放表情 心心眼\n        // std::thread audioGreet(&RobdogCtrlNode::playAudio, this,\"/home/<USER>/resource/audio/handpos/handposSitdown.wav\");\n        // audioGreet.detach();\n        ExpressionChange::getInstance().async_callback_work(getResourcePath(\"video/shake/\"),1);\n        std::thread t_audio(&RobdogCtrlNode::playAudio,this, getResourcePath(\"audio/handpos/handposSitdown.wav\"));\n        t_audio.detach();\n    } else if(pos_type == 4){ // 站立\n            robdogCtrlDev->robdogCtrl_VoiceStand(1);\n\n        // 消除手动指令\n        robCancelHandCode_ = this->create_wall_timer(\n            std::chrono::seconds(1),   \n            std::bind(&RobdogCtrlNode::cancelHandCode, this)\n        ); \n\n        // ExpressionChange::getInstance().async_callback_work(\"/home/<USER>/resource/vedio/touch/stareyes.mp4\", 1); // 播放表情 星星眼\n        // std::thread audioGreet(&RobdogCtrlNode::playAudio, this,\"/home/<USER>/resource/audio/handpos/handposStandup.wav\");\n        // audioGreet.detach();\n        ExpressionChange::getInstance().async_callback_work(getResourcePath(\"video/touch/stareyes.mp4\"),1);\n        std::thread t_audio(&RobdogCtrlNode::playAudio,this, getResourcePath(\"audio/handpos/handposStandup.wav\"));\n        t_audio.detach();\n    } else if(pos_type == 5){\n        // std::thread audioGreet(&RobdogCtrlNode::playAudio, this,\"/home/<USER>/resource/audio/handpos/handposTurnleftorright.wav\");\n        // audioGreet.detach();\n        std::thread t_audio(&RobdogCtrlNode::playAudio,this, getResourcePath(\"audio/handpos/handposTurnleftorright.wav\"));\n        t_audio.detach();\n        leftNine();\n        //std::this_thread::sleep_for(std::chrono::seconds(3));\n        robActionSuspendTimer_ = this->create_wall_timer(\n            std::chrono::seconds(6),   \n            std::bind(&RobdogCtrlNode::rightNine, this)\n        );\n        \n    } else if(pos_type == 6){ // 右转转回\n        // std::thread audioGreet(&RobdogCtrlNode::playAudio, this,\"/home/<USER>/resource/audio/handpos/handposTurnleftorright.wav\");\n        // audioGreet.detach();\n        std::thread t_audio(&RobdogCtrlNode::playAudio,this, getResourcePath(\"audio/handpos/handposTurnleftorright.wav\"));\n        t_audio.detach();\n        rightNine();\n        // std::this_thread::sleep_for(std::chrono::seconds(3));\n        robActionSuspendTimer_ = this->create_wall_timer(\n            std::chrono::seconds(6),   \n            std::bind(&RobdogCtrlNode::leftNine, this)\n        ); \n    }\n}\n\n// 无表情和离线语音的动作执行纯净版 宇树动作：\"standUp\"，\"getDown\"，\"twistBody\"，\"greeting\"，\"sitDown\"，\"dance\"，\"stretch\"，\n// \"newYearCall\"，\"fingerHeart\"，\"happy\"，\"jumpForward\"，\"leap\"，\"danceV2\"，\"walkUpsideDown\"，\"standErect\"\nvoid RobdogCtrlNode::handleInteractionAction(std::string MoveSkill){\n\n    robdogCtrlDev->robdogCtrl_AutoMode();\n    RCLCPP_INFO(this->get_logger(), \"InteractionAction actionargument: %s\", MoveSkill.c_str());\n\n    if(MoveSkill == \"standUp\"){\n        robdogCtrlDev->robdogCtrl_StandUp();\n    }\n    else if(MoveSkill == \"getDown\"){ // && robdog_status != 1){ \n        robdogCtrlDev->robdogCtrl_GetDown();\n    }\n    else if(MoveSkill == \"twistBody\"){\n        robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_TWIST);\n    }\n    else if(MoveSkill == \"greeting\"){\n        robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_HELLO);\n    }\n    else if(MoveSkill == \"sitDown\"){ \n        robdogCtrlDev->robdogCtrl_Sit();\n    }\n    else if(MoveSkill == \"dance\"){\n        robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_DANCE);\n    }\n    else if (MoveSkill==\"stretch\") // 伸懒腰\n    {\n        robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_STRETCH);\n    }\n    else if (MoveSkill==\"newYearCall\")//拜年，作揖\n    {\n        robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_NEWYEARCALL);\n    }\n    else if (MoveSkill==\"fingerHeart\")//比心\n    {\n        robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_FINGERHEART);\n    }\n    else if (MoveSkill==\"happy\")            // 开心\n    {\n        robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_HAPPY);\n    }\n    else if(MoveSkill == \"jumpForward\")     // 向前跳\n    {\n        robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_JUMPFORWARD);\n    }\n    else if (MoveSkill==\"leap\")             // 扑人\n    {\n        robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_LEAP);\n    }\n    else if (MoveSkill==\"danceV2\")          // 跳舞2\n    {\n        robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_DANCEV2);\n    }\n    else if (MoveSkill==\"walkUpsideDown\")   // 倒立\n    {\n        robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_HANDSTAND);\n    }\n    else if (MoveSkill==\"standErect\")       // 直立\n    {\n        robdogCtrlDev->robdogCtrl_Locomotion(ROBDOGCTRL_MOTION_WALKUPRIGHT);\n    }\n}\n\nvoid RobdogCtrlNode::utStatusReportCallback() \n{\n    homi_speech_interface::msg::ProprietySet repMsg;\n\n    unitree_go::msg::dds_::SportModeState_ utSportState;\n    unitree_go::msg::dds_::LowState_ utLowState;\n\n    RobotInfoMgr::getInstance().utGetHighState(utSportState);\n    RobotInfoMgr::getInstance().utGetLowState(utLowState);\n\n    repMsg.cmd=POWER_LEVEL_FROM_NODE;\n    repMsg.value=utLowState.bms_state().soc();\n\n    //云深处定义：0未在充电，1充电中 2 充满\n    //宇树定义：正代表充电，负代表放电，与云深处定义不同，需转化\n    if (utLowState.bms_state().current() > 0)\n    {\n        repMsg.exvalue = 1;\n    }\n    else\n    {\n        repMsg.exvalue = 0;\n    }\n\n    statusReportPub->publish(repMsg);\n\n    return;\n}\n\nint RobdogCtrlNode::sendPacket_light(uint8_t *pkt, size_t packet_size) {\n    int sockfd_light = socket(PF_INET, SOCK_DGRAM, 0);\n    sockaddr_in light_addr;\n    light_addr.sin_family = AF_INET;\n    light_addr.sin_port = htons(12345);\n    light_addr.sin_addr.s_addr = inet_addr(\"127.0.0.1\"); \n    ssize_t nbytes = sendto(sockfd_light, pkt, packet_size, 0,\n                            (struct sockaddr *)&light_addr, sizeof(light_addr));\n    if (nbytes < 0) {\n        RCLCPP_INFO(this->get_logger(), \"Failed to send packetFailed to send packetFailed to send packetFailed to send packetFailed to send packet\");\n        return 1;\n    } else if ((size_t)nbytes == packet_size) {\n    \n    }else if ((size_t)nbytes < packet_size&&(size_t)nbytes>0)\n    {\n        RCLCPP_ERROR(this->get_logger(), \"Failed to send packet to ysc!!!Send byte=%d, Should send =%d\",nbytes,packet_size);\n        return 1;\n    }\n    return 0;\n}\n\nvoid SYS_L_2_B_32(unsigned char *buf, size_t &off, unsigned int value) {\n    unsigned int net_value = htonl(value);\n    memcpy(&buf[off], &net_value, sizeof(unsigned int));\n    off += sizeof(unsigned int);\n}\n\n// light_control\nvoid RobdogCtrlNode::handleLightControl(unsigned int send_cmd,int duration) {\n    // 检查service客户端是否已创建\n    if (!peripherals_ctrl_client_) {\n        RCLCPP_ERROR(this->get_logger(), \"peripherals_ctrl_client_未初始化\");\n        return;\n    }\n\n    // 等待服务可用\n    if (!peripherals_ctrl_client_->wait_for_service(std::chrono::seconds(1))) {\n        RCLCPP_WARN(this->get_logger(), \"服务/robdog_control/peripherals_ctrl不可用\");\n        return;\n    }\n\n    if (restore_timer_) {\n        restore_timer_->cancel();\n        restore_timer_.reset();\n        RCLCPP_DEBUG(this->get_logger(), \"已取消之前的灯光恢复定时器\");\n    }  \n\n    // 准备请求数据\n    std::string mode;\n    std::string color = \"#FFFFFF\"; // 默认白色\n    double effect_speed = 1.0;\n    double brightness = 0.8;\n    std::string secondary_color;\n\n    // 根据send_cmd参数(宏定义)配置不同的灯光模式\n    switch (send_cmd) {\n        case DEEP_CMD_LIGHT_01:  // 0x00000202 橙色闪烁 惊讶（surprised）、愤怒（angry）\n            mode = \"blink\";\n            color = \"#FF2A00\"; // 橙色\n            effect_speed = 2.0;\n            brightness = 0.8;\n            RCLCPP_INFO(this->get_logger(), \"设置LED模式: 橙色闪烁 (惊讶/愤怒)\");\n            break;\n\n        case DEEP_CMD_LIGHT_ORANGE_KEEP_AWAKE:  // 0x00000202 橙色常亮 \n            mode = \"static\";\n            color = \"#FF2A00\"; // 橙色\n            brightness = 0.8;\n            RCLCPP_INFO(this->get_logger(), \"设置LED模式: 橙色常亮\");\n            break;\n        case DEEP_CMD_LIGHT_05:  // 0x00000203 橙色呼吸 厌恶（disgusted）\n            mode = \"breath\";\n            color = \"#FF2A00\"; // 橙色\n            effect_speed = 1.0;\n            brightness = 0.8;\n            RCLCPP_INFO(this->get_logger(), \"设置LED模式: 橙色呼吸 (厌恶)\");\n            break;\n            \n        case DEEP_CMD_LIGHT_06:  // 0x00000204 橙色流水 恐惧（fearful）\n            mode = \"flow\";\n            color = \"#FF2A00\"; // 橙色\n            effect_speed = 1.5;\n            brightness = 0.8;\n            RCLCPP_INFO(this->get_logger(), \"设置LED模式: 橙色流水 (恐惧)\");\n            break;            \n\n        case DEEP_CMD_LIGHT_02:  // 0x00000302 粉色闪烁 高兴（happy）\n            mode = \"blink\";\n            color = \"#580C1F\"; // 粉色\n            effect_speed = 2.0;\n            brightness = 0.8;\n            RCLCPP_INFO(this->get_logger(), \"设置LED模式: 粉色闪烁 (高兴)\");\n            break;\n\n        case DEEP_CMD_LIGHT_PINK_KEEP_AWAKE:  // 0x00000302 粉色常亮\n            mode = \"static\";\n            color = \"#580C1F\"; // 粉色\n            brightness = 0.8;\n            RCLCPP_INFO(this->get_logger(), \"设置LED模式: 粉色常亮\");\n            break;\n            \n        case DEEP_CMD_LIGHT_PINK_BREATHING:  // 0x00000302 粉色呼吸\n            mode = \"breath\";\n            color = \"#580C1F\"; // 粉色\n            effect_speed = 1.0;\n            brightness = 0.8;\n            RCLCPP_INFO(this->get_logger(), \"设置LED模式: 粉色呼吸\");\n            break;            \n\n        case DEEP_CMD_LIGHT_PINK_RUNNING:  // 0x00000302 粉色流水\n            mode = \"flow\";\n            color = \"#580C1F\"; // 粉色\n            effect_speed = 1.5;\n            brightness = 0.8;\n            RCLCPP_INFO(this->get_logger(), \"设置LED模式: 粉色流水\");\n            break;            \n\n        case DEEP_CMD_LIGHT_03:  // 0x00000403 白色呼吸\n            mode = \"breath\";\n            color = \"#FFFFFF\"; // 白色\n            effect_speed = 1.0;\n            brightness = 0.8;\n            RCLCPP_INFO(this->get_logger(), \"设置LED模式: 白色呼吸\");\n            break;\n            \n        case DEEP_CMD_LIGHT_04:  // 0x00000401 白色常亮 中立（neutral）\n            mode = \"static\";\n            color = \"#FFFFFF\"; // 白色\n            brightness = 0.8;\n            RCLCPP_INFO(this->get_logger(), \"设置LED模式: 白色常亮 (中立)\");\n            break;\n\n        case DEEP_CMD_LIGHT_WHITE_BLINKING:  // 0x00000403 白色闪烁\n            mode = \"breath\";\n            color = \"#FFFFFF\"; // 白色\n            effect_speed = 2.0;\n            brightness = 0.8;\n            RCLCPP_INFO(this->get_logger(), \"设置LED模式: 白色闪烁\");\n            break;            \n\n        case DEEP_CMD_LIGHT_WHITE_RUNNING:  // 0x00000403 白色流水\n            mode = \"flow\";\n            color = \"#FFFFFF\"; // 白色\n            effect_speed = 1.5;\n            brightness = 0.8;\n            RCLCPP_INFO(this->get_logger(), \"设置LED模式: 白色流水\");\n            break;             \n\n        case DEEP_CMD_LIGHT_07:  // 0x00000101 黄色常亮\n            mode = \"static\";\n            color = \"#FFFF00\"; // 黄色\n            brightness = 0.8;\n            RCLCPP_INFO(this->get_logger(), \"设置LED模式: 黄色常亮\");\n            break;\n\n        case DEEP_CMD_LIGHT_YELLOW_BLINKING:  // 0x00000101 黄色闪烁\n            mode = \"blink\";\n            color = \"#FFFF00\"; // 黄色\n            effect_speed = 2.0;\n            brightness = 0.8;\n            RCLCPP_INFO(this->get_logger(), \"设置LED模式: 黄色闪烁\");\n            break;\n\n        case DEEP_CMD_LIGHT_YELLOW_BREATHING:  // 0x00000101 黄色呼吸\n            mode = \"breath\";\n            color = \"#FFFF00\"; // 黄色\n            effect_speed = 1.0;\n            brightness = 0.8;\n            RCLCPP_INFO(this->get_logger(), \"设置LED模式: 黄色呼吸\");\n            break;\n\n        case DEEP_CMD_LIGHT_YELLOW_RUNNING:  // 0x00000101 黄色流水\n            mode = \"flow\";\n            color = \"#FFFF00\"; // 黄色\n            effect_speed = 1.5;\n            brightness = 0.8;\n            RCLCPP_INFO(this->get_logger(), \"设置LED模式: 黄色流水\");\n            break;\n\n\n        case DEEP_CMD_LIGHT_08:  // 0x00000503 蓝色呼吸 悲伤（sad）\n            mode = \"breath\";\n            color = \"#0000FF\"; // 蓝色\n            effect_speed = 1.0;\n            brightness = 0.8;\n            RCLCPP_INFO(this->get_logger(), \"设置LED模式: 蓝色呼吸 (悲伤)\");\n            break;\n\n        case DEEP_CMD_LIGHT_BLUE_KEEP_AWAKE:  // 0x00000503 蓝色常亮\n            mode = \"static\";\n            color = \"#0000FF\"; // 蓝色\n            brightness = 0.8;\n            RCLCPP_INFO(this->get_logger(), \"设置LED模式: 蓝色常亮\");\n            break;            \n\n        case DEEP_CMD_LIGHT_BLUE_BLINKING:  // 0x00000503 蓝色闪烁\n            mode = \"blink\";\n            color = \"#0000FF\"; // 蓝色\n            effect_speed = 2.0;\n            brightness = 0.8;\n            RCLCPP_INFO(this->get_logger(), \"设置LED模式: 蓝色闪烁\");\n            break;            \n        case DEEP_CMD_LIGHT_BLUE_RUNNING:  // 0x00000503 蓝色流水\n            mode = \"flow\";\n            color = \"#0000FF\"; // 蓝色\n            effect_speed = 1.5;\n            brightness = 0.8;\n            RCLCPP_INFO(this->get_logger(), \"设置LED模式: 蓝色流水\");\n            break;\n\n        case 0: // 关闭灯光\n            mode = \"off\";\n            RCLCPP_INFO(this->get_logger(), \"设置LED模式: 关闭\");\n            break;\n            \n        default: // 默认白灯\n            mode = \"static\";\n            color = \"#FFFFFF\"; // 白色\n            RCLCPP_INFO(this->get_logger(), \"设置LED模式: 未知命令 0x%X, 使用默认白灯\", send_cmd);\n            break;\n    }\n\n    // 构造并发送灯光请求\n    Json::Value request_json;\n    request_json[\"command\"] = \"set_led_mode\";\n    request_json[\"mode\"] = mode;\n    request_json[\"color\"] = color;\n    request_json[\"effect_speed\"] = effect_speed;\n    request_json[\"brightness\"] = brightness;\n\n    // 如果是渐变模式，添加次要颜色参数\n    if (mode == \"gradient\" && !secondary_color.empty()) {\n        request_json[\"secondary_color\"] = secondary_color;\n    }\n\n    // 序列化JSON\n    Json::StreamWriterBuilder writer;\n    std::string json_str = Json::writeString(writer, request_json);\n    \n    RCLCPP_INFO(this->get_logger(), \"发送灯光控制请求: %s\", json_str.c_str());\n\n    // 创建请求\n    auto request = std::make_shared<homi_speech_interface::srv::PeripheralsCtrl::Request>();\n    request->data = json_str;\n\n    auto future = peripherals_ctrl_client_->async_send_request(\n        request,\n        [this](rclcpp::Client<homi_speech_interface::srv::PeripheralsCtrl>::SharedFuture future) {\n            try {\n                auto response = future.get();\n                if (response->error_code) {\n                    RCLCPP_INFO(this->get_logger(), \"灯光控制成功: %s\", response->result.c_str());\n                } else {\n                    RCLCPP_ERROR(this->get_logger(), \"灯光控制失败: %s\", response->result.c_str());\n                }\n            } catch (const std::exception &e) {\n                RCLCPP_ERROR(this->get_logger(), \"调用灯光控制服务异常: %s\", e.what());\n            }\n        }\n    );\n\n    // 设置定时器恢复默认灯光\n    if (duration > 0) {\n        auto duration_sec = std::chrono::seconds(duration);\n        restore_timer_ = this->create_wall_timer(\n            duration_sec,\n            [this]() {\n                RCLCPP_INFO(this->get_logger(), \"灯光持续时间结束，恢复默认灯光\");\n                this->sendDefaultLight();\n            }\n        );\n        RCLCPP_INFO(this->get_logger(), \"已设置灯光持续时间: %d 秒\", duration);\n    }\n}\n\nvoid RobdogCtrlNode::sendDefaultLight() {\n    // 构造默认灯光请求（白色常亮）\n    Json::Value request_json;\n    request_json[\"command\"] = \"set_led_mode\";\n    request_json[\"mode\"] = \"static\";\n    request_json[\"color\"] = \"#FFFFFF\"; // 白色\n    request_json[\"brightness\"] = 0.8;\n    \n    Json::StreamWriterBuilder writer;\n    std::string json_str = Json::writeString(writer, request_json);\n    \n    RCLCPP_INFO(this->get_logger(), \"恢复默认灯光: %s\", json_str.c_str());\n    \n    auto request = std::make_shared<homi_speech_interface::srv::PeripheralsCtrl::Request>();\n    request->data = json_str;\n\n    peripherals_ctrl_client_->async_send_request(request);\n}\n\nvoid RobdogCtrlNode::peripherals_set_fan_speed(int speed) {\n  if (!peripherals_ctrl_client_->wait_for_service(1s)) {\n    RCLCPP_WARN(this->get_logger(), \"Service /robdog_control/peripherals_ctrl not available.\");\n    return;\n  }\n  try {\n\n    Json::Value request_json;\n\n    request_json[\"command\"] = \"set_fan_speed\";\n    request_json[\"speed\"] = speed;\n    RCLCPP_WARN(this->get_logger(), \"testing send speed: %d\", speed);\n    Json::StreamWriterBuilder writer;\n    const std::string json_str = Json::writeString(writer, request_json);\n\n    auto request = std::make_shared<homi_speech_interface::srv::PeripheralsCtrl::Request>();\n    request->data = json_str;\n\n    auto future = peripherals_ctrl_client_->async_send_request(\n      request,\n      [this](rclcpp::Client<homi_speech_interface::srv::PeripheralsCtrl>::SharedFuture result) {\n        try {\n          auto response = result.get();\n\n          Json::Value response_json;\n          Json::CharReaderBuilder reader;\n          std::string errors;\n          std::istringstream stream(response->result);\n\n          if (Json::parseFromStream(reader, stream, &response_json, &errors)) {\n            if (response_json.get(\"success\", false).asBool()) {\n              RCLCPP_INFO(this->get_logger(), \"Fan speed updated: %d\\%\",\n                        response_json[\"speed\"].asInt());\n            } else {\n              RCLCPP_ERROR(this->get_logger(), \"Service error: %s\", \n                         response_json[\"error\"].asString().c_str());\n            }\n          } else {\n            RCLCPP_ERROR(this->get_logger(), \"Response parse error: %s\", errors.c_str());\n          }\n        } catch (const std::exception &e) {\n          RCLCPP_ERROR(this->get_logger(), \"Callback error: %s\", e.what());\n        }\n      }\n    );\n\n  } catch (const Json::Exception& e) {\n    RCLCPP_ERROR(this->get_logger(), \"JSON error: %s\", e.what());\n  } catch (const std::exception& e) {\n    RCLCPP_ERROR(this->get_logger(), \"System error: %s\", e.what());\n  }\n}\n"}]}