{"sourceFile": "robdog_control/src/robdogNode/robdog_ctrl_unitree.cpp", "activeCommit": 0, "commits": [{"activePatchIndex": 1, "patches": [{"date": 1754223932239, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1754266964364, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1471,21 +1471,21 @@\n \n     calibration_client.SetTimeout(30.0f);\n     calibration_client.Init();\n \n-    // thread ut_threadProc(bind(&RobDog_Ctrl_Unitree::utThreadProc, this));\n-    // ut_threadProc.detach();\n+    thread ut_threadProc(bind(&RobDog_Ctrl_Unitree::utThreadProc, this));\n+    ut_threadProc.detach();\n \n-    // thread ut_threadInit(bind(&RobDog_Ctrl_Unitree::utThreadInit, this));\n-    // ut_threadInit.detach();\n+    thread ut_threadInit(bind(&RobDog_Ctrl_Unitree::utThreadInit, this));\n+    ut_threadInit.detach();\n \n-    // utPubJoystick.reset(new ChannelPublisher<unitree_go::msg::dds_::WirelessController_>(\"rt/wirelesscontroller_unprocessed\"));\n-    // utPubJoystick->InitChannel();\n+    utPubJoystick.reset(new ChannelPublisher<unitree_go::msg::dds_::WirelessController_>(\"rt/wirelesscontroller_unprocessed\"));\n+    utPubJoystick->InitChannel();\n \n-    // ut_para_sub_ =  node_ctrl_->create_subscription<std_msgs::msg::String>(\n-    //     \"/robdog_control/unitree_para\", 10,\n-    //     std::bind(&RobDog_Ctrl_Unitree::utSetCtrlPara, this, std::placeholders::_1)\n-    // );\n+    ut_para_sub_ =  node_ctrl_->create_subscription<std_msgs::msg::String>(\n+        \"/robdog_control/unitree_para\", 10,\n+        std::bind(&RobDog_Ctrl_Unitree::utSetCtrlPara, this, std::placeholders::_1)\n+    );\n \n     //关闭雷达\n     //lidarSwitch.data(\"ON\");\n     //utPubLidarSwitch.reset(new ChannelPublisher<std_msgs::msg::dds_::String_>(\"rt/utlidar/switch\"));\n"}], "date": 1754223932239, "name": "Commit-0", "content": "#include \"public/tools.h\"\n#include \"robotMgr/robot_info_mgr.h\" // 为了读取状态\n#include \"robdog_ctrl_node.h\"\n\n// 读控制指令类型\nUtCtrlType_E RobDog_Ctrl_Unitree::utRdCtrlType()\n{\n    std::shared_lock<std::shared_mutex> lock(utTypeMutex);\n\n    return utCtrlType;\n}\n\n// 写控制指令类型\nvoid RobDog_Ctrl_Unitree::utWrCtrlType(UtCtrlType_E type)\n{\n    std::unique_lock<std::shared_mutex> lock(utTypeMutex);\n\n    utCtrlType = type;\n\n    return;\n}\n\n// 读步态信息\nRobdogCtrlGait RobDog_Ctrl_Unitree::utRdCtrlGait()\n{\n    std::shared_lock<std::shared_mutex> lock(utGaitMutex);\n\n    return utMoveGait;\n}\n\n// 写步态信息\nvoid RobDog_Ctrl_Unitree::utWrCtrlGait(RobdogCtrlGait gait)\n{\n    std::unique_lock<std::shared_mutex> lock(utGaitMutex);\n\n    utMoveGait = gait;\n\n    return;\n}\n\n// 读动作信息\nRobdogCtrlMotion RobDog_Ctrl_Unitree::utRdCtrlMotion()\n{\n    std::shared_lock<std::shared_mutex> lock(utMotionMutex);\n\n    return utLocomotion;\n}\n\n// 写动作信息\nvoid RobDog_Ctrl_Unitree::utWrCtrlMotion(RobdogCtrlMotion motion)\n{\n    std::unique_lock<std::shared_mutex> lock(utMotionMutex);\n\n    utLocomotion = motion;\n\n    return;\n}\n\n// 开启避障服务\nint32_t RobDog_Ctrl_Unitree::utAvoidOpen()\n{\n    int32_t ret = ROBDOGCTRL_ERROR_SUCCESS;\n    int32_t status = 0;\n\n    RCLCPP_INFO(node_ctrl_->get_logger(), \"Enter Function: %s\", __func__);\n\n    utAvoidSwitch = true;\n\n    ret = utRsc.ServiceSwitch(\"obstacles_avoid\", 1, status);\n    if (ret != ROBDOGCTRL_ERROR_SUCCESS)\n    {\n        RCLCPP_INFO(node_ctrl_->get_logger(), \"obstacles_avoid on failed! ret = %d, status = %d\", ret, status);\n        return ROBDOGCTRL_ERROR_FAILED;\n    }\n\n    ret = ov_client.UseRemoteCommandFromApi(true);\n    if (ret != ROBDOGCTRL_ERROR_SUCCESS)\n    {\n        RCLCPP_INFO(node_ctrl_->get_logger(), \"UseRemoteCommandFromApi failed! ret = %d\", ret);\n        return ROBDOGCTRL_ERROR_FAILED;\n    }\n    \n    return ROBDOGCTRL_ERROR_SUCCESS;\n}\n\n// 关闭避障服务\nint32_t RobDog_Ctrl_Unitree::utAvoidClose()\n{\n    int32_t ret = ROBDOGCTRL_ERROR_SUCCESS;\n    int32_t status = 0;\n\n    RCLCPP_INFO(node_ctrl_->get_logger(), \"Enter Function: %s\", __func__);\n\n    utAvoidSwitch = false;\n\n    ret = ov_client.UseRemoteCommandFromApi(false);\n    if (ret != ROBDOGCTRL_ERROR_SUCCESS)\n    {\n        RCLCPP_INFO(node_ctrl_->get_logger(), \"UseRemoteCommandFromApi failed! ret = %d\", ret);\n        return ROBDOGCTRL_ERROR_FAILED;\n    }\n\n    ret = utRsc.ServiceSwitch(\"obstacles_avoid\", 0, status);\n    if (ret != ROBDOGCTRL_ERROR_SUCCESS)\n    {\n        RCLCPP_INFO(node_ctrl_->get_logger(), \"obstacles_avoid off failed! ret = %d, status = %d\", ret, status);\n        return ROBDOGCTRL_ERROR_FAILED;\n    }\n\n    return ROBDOGCTRL_ERROR_SUCCESS;\n}\n\nvoid RobDog_Ctrl_Unitree::utSetCtrlPara(const std_msgs::msg::String::SharedPtr msg)\n{\n    const std::string& dema_cmd = msg->data;\n    int32_t ret = ROBDOGCTRL_ERROR_SUCCESS;\n\n    // ros2 topic pub /robdog_control/unitree_para std_msgs/msg/String 'data: \"joystick_on\"' -1\n    RCLCPP_INFO(node_ctrl_->get_logger(), \"================= start ================\");\n    RCLCPP_INFO(node_ctrl_->get_logger(), \"========================================\");\n    RCLCPP_INFO(node_ctrl_->get_logger(), \"Unitree SetCtrlPara: %s\", dema_cmd.c_str());\n    RCLCPP_INFO(node_ctrl_->get_logger(), \"========================================\");\n\n    if (dema_cmd == \"imu1\")\n    {\n        RCLCPP_INFO(node_ctrl_->get_logger(), \"Unitree SetCtrlPara: imu1\");\n        ret = robdogCtrl_IMUDemaStage1();\n    }\n    else if (dema_cmd == \"imu2\")\n    {\n        RCLCPP_INFO(node_ctrl_->get_logger(), \"Unitree SetCtrlPara: imu2\");\n        ret = robdogCtrl_IMUDemaStage2();\n    }\n    else if (dema_cmd == \"joint1\")\n    {\n        RCLCPP_INFO(node_ctrl_->get_logger(), \"Unitree SetCtrlPara: joint1\");\n        ret = robdogCtrl_JointDemaStage1();\n    }\n    else if (dema_cmd == \"joint2\")\n    {\n        RCLCPP_INFO(node_ctrl_->get_logger(), \"Unitree SetCtrlPara: joint2\");\n        ret = robdogCtrl_JointDemaStage2();\n    }\n    else if (dema_cmd == \"joint3\")\n    {\n        RCLCPP_INFO(node_ctrl_->get_logger(), \"Unitree SetCtrlPara: joint3\");\n        ret = robdogCtrl_JointDemaStage3();\n    }\n    else if (dema_cmd == \"joint4\")\n    {\n        RCLCPP_INFO(node_ctrl_->get_logger(), \"Unitree SetCtrlPara: joint4\");\n        ret = robdogCtrl_JointDemaStage4();\n    }\n    else if (dema_cmd == \"joystick_on\")\n    {\n        RCLCPP_INFO(node_ctrl_->get_logger(), \"Unitree SetCtrlPara: joystick_on\");\n        ret = utJoystickSwitch = true;\n    }\n    else if (dema_cmd == \"joystick_off\")\n    {\n        RCLCPP_INFO(node_ctrl_->get_logger(), \"Unitree SetCtrlPara: joystick_off\");\n        ret = utJoystickSwitch = false;\n    }\n    else if (dema_cmd == \"avoid_on\")\n    {\n        RCLCPP_INFO(node_ctrl_->get_logger(), \"Unitree SetCtrlPara: avoid_on\");\n        ret = utAvoidOpen();\n    }\n    else if (dema_cmd == \"avoid_off\")\n    {\n        RCLCPP_INFO(node_ctrl_->get_logger(), \"Unitree SetCtrlPara: avoid_off\");\n        ret = utAvoidClose();\n    }\n    else if (dema_cmd == \"speedlevel_low\")\n    {\n        RCLCPP_INFO(node_ctrl_->get_logger(), \"Unitree SetCtrlPara: speedlevel_low\");\n        utSetSpeedLevel(-1);\n    }\n    else if (dema_cmd == \"speedlevel_high\")\n    {\n        RCLCPP_INFO(node_ctrl_->get_logger(), \"Unitree SetCtrlPara: speedlevel_high\");\n        utSetSpeedLevel(1);\n    }\n\n    if (ret != ROBDOGCTRL_ERROR_SUCCESS)\n    {\n        RCLCPP_INFO(node_ctrl_->get_logger(), \"Unitree SetCtrlPara Error.\");\n    }\n\n    RCLCPP_INFO(node_ctrl_->get_logger(), \"================= end ================\");\n\n    return;\n}\n\n// 设置速度档位\nvoid RobDog_Ctrl_Unitree::utSetSpeedLevel(int32_t level)\n{\n    int32_t ret = ROBDOGCTRL_ERROR_SUCCESS;\n\n    // 设置速度档位\n    RCLCPP_INFO(node_ctrl_->get_logger(), \"SpeedLevel Set %d!\", level);\n    ret = utSc.SpeedLevel(level);\n    if (ret != ROBDOGCTRL_ERROR_SUCCESS)\n    {\n        RCLCPP_ERROR(node_ctrl_->get_logger(), \"Speed Level Set failed! ret = %d\", ret);\n    }\n\n    return;\n}\n\n// 速度清零\nvoid RobDog_Ctrl_Unitree::utSpeedClear()\n{\n    utLastX = 0.0;\n    utLastY = 0.0;\n    utLastYaw = 0.0;\n\t\n    utJoystickMsg.lx(0.0);\n    utJoystickMsg.ly(0.0);\n    utJoystickMsg.rx(0.0);\n    utJoystickMsg.ry(0.0);\n\n    return;\n}\n\n// 等待站立动作完成\nbool RobDog_Ctrl_Unitree::utStandUpWait()\n{\n    int cnt = 0;\n\n    while((false == RobotInfoMgr::getInstance().utStandCheck()) && (cnt < 60))\n    {\n        RCLCPP_INFO(node_ctrl_->get_logger(), \"Wait Stand: %d\", cnt);\n        std::this_thread::sleep_for(std::chrono::milliseconds(200));\n        cnt++;\n    }\n\n    if (cnt == 60)\n    {\n        RCLCPP_INFO(node_ctrl_->get_logger(), \"Not Stand Up.\");\n        return false;\n    }\n\n    RCLCPP_INFO(node_ctrl_->get_logger(), \"Stand Up Now.\");\n\n    return true;\n}\n\n// 检查步态是否与用户设置一致\nbool RobDog_Ctrl_Unitree::utGaitCheck()\n{\n    HomiUtRobotStatus enLastStatus = UT_ROBDOG_STATUS_STATE;\n    RobdogCtrlGait gait;\n\n    // 获取步态设置信息\n    gait = utRdCtrlGait();\n\n    // 获取状态信息\n    enLastStatus = RobotInfoMgr::getInstance().getUtRobotStatus();\n\n    // 检查设置步态（常规步行）是否与状态一致\n    if ((enLastStatus == UT_ROBDOG_STATUS_STATIC_WALK) && \n        (gait == ROBDOGCTRL_GAIT_WALK || gait == ROBDOGCTRL_GAIT_EXIT))\n    {\n        return true;\n    }\n\n    // 检查设置步态（常规跑步）是否与状态一致\n    if ((enLastStatus == UT_ROBDOG_STATUS_TROT_RUN) && \n        (gait == ROBDOGCTRL_GAIT_RUN))\n    {\n        return true;\n    }\n\n    // 检查设置步态（经典）是否与状态一致\n    if ((enLastStatus == UT_ROBDOG_STATUS_CLASSIC) && \n        (gait == ROBDOGCTRL_GAIT_AICLASSIC))\n    {\n        return true;\n    }\n\n    // 检查设置步态（灵动）是否与状态一致\n    if ((enLastStatus == UT_ROBDOG_STATUS_FREE_WALK) && \n        (gait == ROBDOGCTRL_GAIT_AINIMBLE))\n    {\n        return true;\n    }\n\n    // 检查设置步态（跳跃跑）是否与状态一致\n    if ((enLastStatus == UT_ROBDOG_STATUS_FREE_JUMP) && \n        (gait == ROBDOGCTRL_GAIT_FREEJUMP))\n    {\n        return true;\n    }\n\n    // 检查设置步态（并腿跑）是否与状态一致\n    if ((enLastStatus == UT_ROBDOG_STATUS_FREE_BOUND) && \n        (gait == ROBDOGCTRL_GAIT_FREEBOUND))\n    {\n        return true;\n    }\n\n    RCLCPP_INFO(node_ctrl_->get_logger(), \"Gait State Not Match With User Set.\");\n\n    return false;\n}\n\n// 起立\nint32_t RobDog_Ctrl_Unitree::utStandUpProc(bool changeGait)\n{\n    std::string form;\n    std::string name;\n    int32_t ret = ROBDOGCTRL_ERROR_SUCCESS;\n\n    RCLCPP_INFO(node_ctrl_->get_logger(), \"Thread Enter Function: %s\", __func__);\n\n    // 已经站立\n    if (true == RobotInfoMgr::getInstance().utStandCheck())\n    {\n        RCLCPP_INFO(node_ctrl_->get_logger(), \"Already Standup Now!\");\n        return ROBDOGCTRL_ERROR_SUCCESS;\n    }\n\n    // 正在执行动作或在运动状态，退出\n    if ((true == RobotInfoMgr::getInstance().utLocomotionCheck()) || \n        (true == RobotInfoMgr::getInstance().utMoveCheck()))\n    {\n        RCLCPP_INFO(node_ctrl_->get_logger(), \"Moving or Locomotion!\");\n        return ROBDOGCTRL_ERROR_INVALID_STATE;\n    }\n\n    if (true == RobotInfoMgr::getInstance().utSitCheck())\n    {\n        // 从坐下状态恢复到平衡站立\n        ret = utSc.RiseSit();\n        if (ret != 0)\n        {\n            RCLCPP_ERROR(node_ctrl_->get_logger(), \"RiseSit Action failed! ret = %d\", ret);\n            return ret;\n        }\n    }\n    else\n    {\n        // 从翻倒或趴下状态恢复至平衡站立状态。不论是否翻倒，都会恢复至站立。\n        RCLCPP_INFO(node_ctrl_->get_logger(), \"Recover Stand Up!\");\n        ret = utSc.RecoveryStand();\n        if (ret != ROBDOGCTRL_ERROR_SUCCESS)\n        {\n            RCLCPP_ERROR(node_ctrl_->get_logger(), \"RecoveryStand Action failed! ret = %d\", ret);\n            return ret;\n        }\n    }\n\n    // 狗子趴下站起来默认为灵动模式\n    // 常规模式下，做完动作，默认为平衡站立，行走为原步态（趴下站起来除外）\n    // ai步态下，做完动作，默认为平衡站立，行走为灵动步态（趴下站起来除外）\n\n    // 检查是否站立成功\n    if (false == utStandUpWait())\n    {\n        RCLCPP_ERROR(node_ctrl_->get_logger(), \"Wait Standup Error\");\n        return ROBDOGCTRL_ERROR_FAILED;\n    }\n\n    // 是否需要切换步态\n    if(true == changeGait)\n    {\n        ret = utChangeGaitProc();\n        if (ret != ROBDOGCTRL_ERROR_SUCCESS)\n        {\n            RCLCPP_ERROR(node_ctrl_->get_logger(), \"Change Gait Proc Failed! ret = %d\", ret);\n        }\n    }\n\n    RCLCPP_INFO(node_ctrl_->get_logger(), \"Standup Proc Success!\");\n\n    return ROBDOGCTRL_ERROR_SUCCESS;\n}\n\n// 趴下\nint32_t RobDog_Ctrl_Unitree::utGetDownProc()              \n{\n    int32_t ret = ROBDOGCTRL_ERROR_SUCCESS;\n\n    RCLCPP_INFO(node_ctrl_->get_logger(), \"Thread Enter Function: %s\", __func__);\n\n    // 已经趴下\n    if (true == RobotInfoMgr::getInstance().utStandDownCheck())\n    {\n        RCLCPP_INFO(node_ctrl_->get_logger(), \"Already Stand Down!\");\n        return ROBDOGCTRL_ERROR_SUCCESS;\n    }\n\n    // 正在运动，或者做动作，不执行趴下\n    if ((true == RobotInfoMgr::getInstance().utMoveCheck()) || (true == RobotInfoMgr::getInstance().utLocomotionCheck()))\n    {\n        RCLCPP_INFO(node_ctrl_->get_logger(), \"Invalid State: Moving or Locomotion!\");\n        return ROBDOGCTRL_ERROR_INVALID_STATE;\n    }\n\n    // 非站立状态，先站立\n    ret = utStandUpProc(false);\n    if (ret != ROBDOGCTRL_ERROR_SUCCESS)\n    {\n        RCLCPP_ERROR(node_ctrl_->get_logger(), \"Stand Up failed! ret = %d\", ret);\n        return ret;\n    }\n\n    // 趴下\n    ret = utSc.StandDown();\n    if (ret != ROBDOGCTRL_ERROR_SUCCESS)\n    {\n        RCLCPP_ERROR(node_ctrl_->get_logger(), \"StandDown Action failed! ret = %d\", ret);\n        return ret;\n    }\n\n    RCLCPP_INFO(node_ctrl_->get_logger(), \"StandDown Proc Success!\");\n    \n    return ROBDOGCTRL_ERROR_SUCCESS;\n}\n\n// 坐下\nint32_t RobDog_Ctrl_Unitree::utSitProc()       \t        \n{\n    int32_t ret = ROBDOGCTRL_ERROR_SUCCESS;\n\n    RCLCPP_INFO(node_ctrl_->get_logger(), \"Thread Enter Function: %s\", __func__);\n\n    // 已坐下，无需处理\n    if (true == RobotInfoMgr::getInstance().utSitCheck())\n    {\n        RCLCPP_INFO(node_ctrl_->get_logger(), \"Already Sit!\");\n        return ROBDOGCTRL_ERROR_SUCCESS;\n    }\n\n    // 正在运动，或者做动作，不执行坐下\n    if (true == RobotInfoMgr::getInstance().utLocomotionCheck() || true == RobotInfoMgr::getInstance().utMoveCheck())\n    {\n        RCLCPP_INFO(node_ctrl_->get_logger(), \"Invalid State: Moving or Locomotion!\");\n        return ROBDOGCTRL_ERROR_INVALID_STATE;\n    }\n\n    // 非站立状态，先站立\n    ret = utStandUpProc(false);\n    if (ret != ROBDOGCTRL_ERROR_SUCCESS)\n    {\n        RCLCPP_ERROR(node_ctrl_->get_logger(), \"Stand Up failed! ret = %d\", ret);\n        return ret;\n    }\n\n    // 坐下\n    ret = utSc.Sit();\n    if (ret != ROBDOGCTRL_ERROR_SUCCESS)\n    {\n        RCLCPP_ERROR(node_ctrl_->get_logger(), \"Sit Action failed! ret = %d\", ret);\n        return ret;\n    }\n\n    RCLCPP_INFO(node_ctrl_->get_logger(), \"Sit Proc Success!\");\n\n    // 坐下8秒后站起来\n    std::this_thread::sleep_for(std::chrono::milliseconds(8000));\n    ret = utStandUpProc(true);\n    if (ret != ROBDOGCTRL_ERROR_SUCCESS)\n    {\n        RCLCPP_ERROR(node_ctrl_->get_logger(), \"Stand Up failed! ret = %d\", ret);\n        return ret;\n    }\n\n    return ROBDOGCTRL_ERROR_SUCCESS;\n}\n\n// 动作处理函数\nint32_t RobDog_Ctrl_Unitree::utLocomotionProc()\n{\n    int32_t ret = ROBDOGCTRL_ERROR_SUCCESS;\n    RobdogCtrlMotion motion = ROBDOGCTRL_MOTION_INVALID;\n\n    RCLCPP_INFO(node_ctrl_->get_logger(), \"Thread Enter Function: %s\", __func__);\n\n    // 获取用户下发动作\n    motion = utRdCtrlMotion();\n\n    // 无效或者不支持动作，直接返回\n    if ((motion == ROBDOGCTRL_MOTION_INVALID) || (motion == ROBDOGCTRL_MOTION_TURNOVER) ||\n        (motion == ROBDOGCTRL_MOTION_TWISTJUMP) || (motion == ROBDOGCTRL_MOTION_BACKFLIP))\n    {\n        utWrCtrlMotion(ROBDOGCTRL_MOTION_INVALID);\n        return ROBDOGCTRL_ERROR_SUCCESS;\n    }\n\n    //正在做动作或运动中\n    if ((true == RobotInfoMgr::getInstance().utLocomotionCheck()) || (true == RobotInfoMgr::getInstance().utMoveCheck()))\n    {\n        RCLCPP_INFO(node_ctrl_->get_logger(), \"Already Locomotion or Moving!\");\n        utWrCtrlMotion(ROBDOGCTRL_MOTION_INVALID);\n        return ROBDOGCTRL_ERROR_INVALID_STATE;\n    }\n\n    // 非站立状态，先站立\n    ret = utStandUpProc(false);\n    if (ret != ROBDOGCTRL_ERROR_SUCCESS)\n    {\n        RCLCPP_ERROR(node_ctrl_->get_logger(), \"Stand Up failed! ret = %d\", ret);\n        utWrCtrlMotion(ROBDOGCTRL_MOTION_INVALID);\n        return ret;\n    }\n\n    //做动作\n    switch(motion)\n    {\n        case ROBDOGCTRL_MOTION_TWIST:            //扭身体  -->开心\n        {\n            RCLCPP_INFO(node_ctrl_->get_logger(), \"Locomotion: Twist!\");\n            ret = utSc.Content();\n            break;\n        }\n        case ROBDOGCTRL_MOTION_DANCE:            // 跳舞\n        {\n            RCLCPP_INFO(node_ctrl_->get_logger(), \"Locomotion: Dance!\");\n            ret = utSc.Dance1();\n            break;\n        }\n        case ROBDOGCTRL_MOTION_FINGERHEART:      //比心\n        {\n            RCLCPP_INFO(node_ctrl_->get_logger(), \"Locomotion: Finger Heart!\");\n            ret = utSc.Heart();\n            break;\n        }\n        case ROBDOGCTRL_MOTION_HELLO:            //打招呼\n        {\n            RCLCPP_INFO(node_ctrl_->get_logger(), \"Locomotion: Hello!\");\n            ret = utSc.Hello();\n            break;\n        }\n        case ROBDOGCTRL_MOTION_STRETCH:          //伸懒腰\n        {\n            RCLCPP_INFO(node_ctrl_->get_logger(), \"Locomotion: Stretch!\");\n            ret = utSc.Stretch();\n            break;\n        }\n        case ROBDOGCTRL_MOTION_NEWYEARCALL:      //拜年\n        {\n            RCLCPP_INFO(node_ctrl_->get_logger(), \"Locomotion: New Year Call!\");\n            ret = utSc.Scrape();\n            break;\n        }\n        case ROBDOGCTRL_MOTION_HAPPY:            //开心\n        {\n            RCLCPP_INFO(node_ctrl_->get_logger(), \"Locomotion: Happy!\");\n            ret = utSc.Content();\n            break;\n        }\n        case ROBDOGCTRL_MOTION_JUMPFORWARD:      //向前跳\n        {\n            RCLCPP_INFO(node_ctrl_->get_logger(), \"Locomotion: Jump Forward!\");\n            ret = utSc.FrontJump();\n            break;\n        }\n        case ROBDOGCTRL_MOTION_LEAP:             //向前扑人\n        {\n            RCLCPP_INFO(node_ctrl_->get_logger(), \"Locomotion: Front Pounce!\");\n            ret = utSc.FrontPounce();\n            break;\n        }\n        case ROBDOGCTRL_MOTION_DANCEV2:          //跳舞2\n        {\n            RCLCPP_INFO(node_ctrl_->get_logger(), \"Locomotion: Dance v2!\");\n            ret = utSc.Dance2();\n            break;\n        }\n        case ROBDOGCTRL_MOTION_WALKUPRIGHT:       //站立\n        {\n            RCLCPP_INFO(node_ctrl_->get_logger(), \"Locomotion: Walk Upright!\");\n            ret = utSc.WalkUpright(true);\n            std::this_thread::sleep_for(std::chrono::milliseconds(3000));\n            ret = utSc.WalkUpright(false);\n            break;\n        }\n        case ROBDOGCTRL_MOTION_HANDSTAND:         //倒立\n        {\n            RCLCPP_INFO(node_ctrl_->get_logger(), \"Locomotion: Hand Stand!\");\n            ret = utSc.HandStand(true);\n            std::this_thread::sleep_for(std::chrono::milliseconds(3000));\n            ret = utSc.HandStand(false);\n            break;\n        }\n        default:\n        {\n            RCLCPP_ERROR(node_ctrl_->get_logger(), \"Invalid Sport Motion! motion = %d\", motion);\n            utWrCtrlMotion(ROBDOGCTRL_MOTION_INVALID);\n            return ROBDOGCTRL_ERROR_INVALID_PARA;\n        }\n    }\n\n    if (ret != ROBDOGCTRL_ERROR_SUCCESS)\n    {\n        RCLCPP_ERROR(node_ctrl_->get_logger(), \"Local Motion failed! ret = %d, motion = %d\", ret, motion);\n        utWrCtrlMotion(ROBDOGCTRL_MOTION_INVALID);\n        return ret;\n    }\n\n    // 检查是否站立成功\n    if (false == utStandUpWait())\n    {\n        RCLCPP_ERROR(node_ctrl_->get_logger(), \"Wait Standup Error\");\n        utWrCtrlMotion(ROBDOGCTRL_MOTION_INVALID);\n        return ROBDOGCTRL_ERROR_INVALID_STATE;\n    }\n\n    // 执行完动作恢复步态\n    ret = utChangeGaitProc();\n    if (ret != ROBDOGCTRL_ERROR_SUCCESS)\n    {\n        RCLCPP_ERROR(node_ctrl_->get_logger(), \"RecoveryStand Action failed! ret = %d\", ret);\n        utWrCtrlMotion(ROBDOGCTRL_MOTION_INVALID);\n        return ret;\n    }\n\n    utWrCtrlMotion(ROBDOGCTRL_MOTION_INVALID);\n\n    RCLCPP_INFO(node_ctrl_->get_logger(), \"Locomotion Proc Success!\");\n\n    return ROBDOGCTRL_ERROR_SUCCESS;\n}\n\n// 步态切换处理\nint32_t RobDog_Ctrl_Unitree::utChangeGaitProc()\n{\n    int32_t ret = ROBDOGCTRL_ERROR_SUCCESS;\n    RobdogCtrlGait gait;\n\n    RCLCPP_INFO(node_ctrl_->get_logger(), \"Thread Enter Function: %s\", __func__);\n\n    // 趴下、坐下或者执行动作，不能切换步态\n    if ((true == RobotInfoMgr::getInstance().utStandDownCheck()) || \n        (true == RobotInfoMgr::getInstance().utSitCheck()) || \n        (true == RobotInfoMgr::getInstance().utLocomotionCheck()))\n    {\n        RCLCPP_INFO(node_ctrl_->get_logger(), \"Stand Down, Sit or Locomotion. Can not Change Gait!\");\n        return ROBDOGCTRL_ERROR_INVALID_STATE;\n    }\n\n    // 获取用户设置步态信息\n    gait = utRdCtrlGait();\n    if (gait == ROBDOGCTRL_GAIT_INVALID)\n    {\n        RCLCPP_ERROR(node_ctrl_->get_logger(), \"Invalid Gait!\");\n        return ROBDOGCTRL_ERROR_INVALID_PARA;\n    }\n\n    // 检查当前步态和设置步态是否一致\n    if (true == utGaitCheck())\n    {\n        RCLCPP_INFO(node_ctrl_->get_logger(), \"Gait Match!\");\n        return ROBDOGCTRL_ERROR_SUCCESS;\n    }\n\n    switch(gait)\n    {\n        case ROBDOGCTRL_GAIT_WALK:\n        {\n            RCLCPP_INFO(node_ctrl_->get_logger(), \"Change Walk Gait!\");\n            ret = utSc.StaticWalk();\n            utSpeedGait = UT_SPEED_GAIT_WALK;\n            break;\n        }\n        case ROBDOGCTRL_GAIT_RUN:\n        {\n            RCLCPP_INFO(node_ctrl_->get_logger(), \"Change Run Gait!\");\n            ret = utSc.TrotRun();\n            utSpeedGait = UT_SPEED_GAIT_RUN;\n            break;\n        }\n        case ROBDOGCTRL_GAIT_AICLASSIC:\n        {\n            RCLCPP_INFO(node_ctrl_->get_logger(), \"Change AI Classic Gait!\");\n            ret = utSc.ClassicWalk(true);\n            utSpeedGait = UT_SPEED_GAIT_AICLASSIC;\n            utSetSpeedLevel(utSpeedLevel);\n            break;\n        }\n        case ROBDOGCTRL_GAIT_AINIMBLE:\n        {\n            RCLCPP_INFO(node_ctrl_->get_logger(), \"Change AI Flex Gait!\");\n            ret = utSc.FreeWalk();\n            utSpeedGait = UT_SPEED_GAIT_AINIMBLE;\n            utSetSpeedLevel(utSpeedLevel);\n            break;\n        }\n        case ROBDOGCTRL_GAIT_FREEJUMP:\n        {\n            RCLCPP_INFO(node_ctrl_->get_logger(), \"Change FreeJump Gait!\");\n            ret = utSc.FreeJump(true);\n            utSpeedGait = UT_SPEED_GAIT_FREEJUMP;\n            utSetSpeedLevel(utSpeedLevel);\n            break;\n        }\n        case ROBDOGCTRL_GAIT_FREEBOUND:\n        {\n            RCLCPP_INFO(node_ctrl_->get_logger(), \"Change FreeBound Gait!\");\n            ret = utSc.FreeBound(true);\n            utSpeedGait = UT_SPEED_GAIT_FREEBOND;\n            utSetSpeedLevel(utSpeedLevel);\n            break;\n        }\n        case ROBDOGCTRL_GAIT_EXIT:\n        {\n            RCLCPP_INFO(node_ctrl_->get_logger(), \"Change exit ai Gait!\");\n            ret = utSc.StaticWalk();\n            utSpeedGait = UT_SPEED_GAIT_WALK;\n            break;\n        }\n        default:\n        {\n            // 步态信息有误，强制修改为默认步态\n            RCLCPP_ERROR(node_ctrl_->get_logger(), \"Invalid Gait! gait = %d\", gait);\n            utWrCtrlGait(ROBDOGCTRL_GAIT_AICLASSIC);\n            return ROBDOGCTRL_ERROR_INVALID_PARA;\n        }\n    }\n\n    // 检测步态切换是否成功\n    if (ret != ROBDOGCTRL_ERROR_SUCCESS)\n    {\n        RCLCPP_ERROR(node_ctrl_->get_logger(), \"Change Gait failed! ret = %d, gait = %d\", ret, gait);\n        return ret;\n    }\n\n    RCLCPP_INFO(node_ctrl_->get_logger(), \"Change Gait Proc Success!\");\n\n    return ROBDOGCTRL_ERROR_SUCCESS;\n}\n\nint32_t RobDog_Ctrl_Unitree::utMoveProc(bool joystick, float x, float y, float yaw)\n{\n    int32_t ret = ROBDOGCTRL_ERROR_SUCCESS;\n    UtCtrlType_E ctrlType = UT_CTRL_TYPE_INVALID;\n\n    // 存在关节温度过热\n    if (RobotInfoMgr::getInstance().utCheckTempStatus())\n    {\n        RCLCPP_INFO(node_ctrl_->get_logger(), \"Over Heat, Can not Move!\");\n        return ROBDOGCTRL_ERROR_INVALID_STATE;\n    }\n\n    // 检查是否有其他动作在执行\n    ctrlType = utRdCtrlType();\n    if (ctrlType != UT_CTRL_TYPE_INVALID)\n    {\n        RCLCPP_INFO(node_ctrl_->get_logger(), \"Doing other action! ctrlType: %d\", ctrlType);\n        return ROBDOGCTRL_ERROR_INVALID_STATE;\n    }\n\n    ret = utChangeGaitProc();\n    if (ret != ROBDOGCTRL_ERROR_SUCCESS)\n    {\n        RCLCPP_ERROR(node_ctrl_->get_logger(), \"utChangeGaitProc failed! ret = %d\", ret);\n        return ret;\n    }\n\n    if (utJoystickSwitch)\n    {\n        utJoystickMsg.lx(y);\n        utJoystickMsg.ly(x);\n        utJoystickMsg.rx(yaw);\n\n        RCLCPP_INFO(node_ctrl_->get_logger(), \"====== JoyStick: x: %f, y: %f, yaw: %f ======\", \n                    utJoystickMsg.lx(), utJoystickMsg.ly(), utJoystickMsg.rx());\n\n        utPubJoystick->Write(utJoystickMsg);\n    }\n    else\n    {\n        if (utAvoidSwitch == true)\n        {\n            RCLCPP_INFO(node_ctrl_->get_logger(), \"====== avoid on, Move: x: %f, y: %f, yaw: %f ======\", x, y, yaw);\n            ret = ov_client.Move(x, y, yaw);\n        }\n        else\n        {\n            RCLCPP_INFO(node_ctrl_->get_logger(), \"====== avoid off, Move: x: %f, y: %f, yaw: %f ======\", x, y, yaw);\n            ret = utSc.Move(x, y, yaw);\n        }\n\n        if (ret != ROBDOGCTRL_ERROR_SUCCESS)\n        {\n            RCLCPP_ERROR(node_ctrl_->get_logger(), \"Move Action failed! ret = %d\", ret);\n            return ret;\n        }\n    }\n\n    return ROBDOGCTRL_ERROR_SUCCESS;\n}\n\n// 宇树线程处理函数\nint32_t RobDog_Ctrl_Unitree::utThreadProc()\n{\n    int32_t ret = ROBDOGCTRL_ERROR_SUCCESS;\n    UtCtrlType_E ctrlType = UT_CTRL_TYPE_INVALID;\n\n    RCLCPP_INFO(node_ctrl_->get_logger(), \"Enter Function: %s\", __func__);\n\n    while(1)\n    {\n        // 等待运控板状态信息更新\n        if (false == RobotInfoMgr::getInstance().utConnectWait())\n        {\n            RCLCPP_ERROR(node_ctrl_->get_logger(), \"utThreadProc: Wait Connect Error\");\n            continue;\n        }\n\n        std::this_thread::sleep_for(std::chrono::milliseconds(100));\n        ret = ROBDOGCTRL_ERROR_SUCCESS;\n        \n        // 获取指令类型\n        ctrlType = utRdCtrlType();\n        if ((ctrlType == UT_CTRL_TYPE_INVALID) || (ctrlType >= UT_CTRL_TYPE_MAX))\n        {\n            RCLCPP_INFO_THROTTLE(node_ctrl_->get_logger(), *node_ctrl_->get_clock(), 5000, \"===== No Action =====\");\n            continue;\n        }\n\n        RCLCPP_INFO(node_ctrl_->get_logger(), \"===== Action: ctrlType %d =====\", ctrlType);\n\n        // 调用处理函数\n        switch (ctrlType)\n        {\n            case UT_CTRL_TYPE_STANDUP:\n            {\n                ret = utStandUpProc(true);\n                break;\n            }\n            case UT_CTRL_TYPE_GETDOWN:\n            {\n                ret = utGetDownProc();\n                break;\n            }\n            case UT_CTRL_TYPE_SIT:\n            {\n                ret = utSitProc();\n                break;\n            }\n            case UT_CTRL_TYPE_LOCOMOTION:\n            {\n                ret = utLocomotionProc();\n                break;\n            }\n            case UT_CTRL_TYPE_CHANGE_GAIT:\n            {\n                ret = utChangeGaitProc();\n                break;\n            }\n            default:\n            {\n                //do nothing\n                break;\n            }\n        }\n\n        // 错误检查\n        if (ret != ROBDOGCTRL_ERROR_SUCCESS)\n        {\n            RCLCPP_INFO(node_ctrl_->get_logger(), \"Unitree Control Thread Proc Error. ctrlType = %d, ret = %d\", ctrlType, ret);\n        }\n\n        utWrCtrlType(UT_CTRL_TYPE_INVALID);\n    }\n\n    return ROBDOGCTRL_ERROR_SUCCESS;\n}\n\nint32_t RobDog_Ctrl_Unitree::utThreadInit()\n{\n    int32_t ret = ROBDOGCTRL_ERROR_SUCCESS;\n    int32_t status;\n\n    RCLCPP_INFO(node_ctrl_->get_logger(), \"Enter Function: %s\", __func__);\n\n    while(1)\n    {\n        // 等待通信正常\n        if (false == RobotInfoMgr::getInstance().utConnectWait())\n        {\n            RCLCPP_ERROR(node_ctrl_->get_logger(), \"utThreadInit: Wait Connect Error\");\n            continue;\n        }\n\n        // 关闭宇树避障服务\n        if (utAvoidInitFlag == false)\n        {\n            ret = utRsc.ServiceSwitch(\"obstacles_avoid\", 0, status);\n            if (ret == ROBDOGCTRL_ERROR_SUCCESS)\n            {\n                utAvoidInitFlag = true;\n            }\n\n            RCLCPP_INFO(node_ctrl_->get_logger(), \"ServiceSwitch return: %d , status: %d\", ret, status);\n        }\n\n        // 自动翻身设置为不生效\n        if (utAutoRecoverInitFlag == false)\n        {\n            ret = utSc.AutoRecoverSet(false);\n            if (ret == ROBDOGCTRL_ERROR_SUCCESS)\n            {\n                utAutoRecoverInitFlag = true;\n            }\n\n            RCLCPP_INFO(node_ctrl_->get_logger(), \"AutoRecoverSet return: %d\", ret);\n        }\n\n        // 全都设置成功，退出\n        if ((utAvoidInitFlag == true) && (utAutoRecoverInitFlag == true))\n        {\n            break;\n        }\n\n        std::this_thread::sleep_for(std::chrono::milliseconds(2000));\n    }\n\n    return ROBDOGCTRL_ERROR_SUCCESS;\n}\n\n// 检测是否有动作在执行\nbool RobDog_Ctrl_Unitree::robdogCtrl_CheckCtrlType()              \t\n{\n    UtCtrlType_E ctrlType = UT_CTRL_TYPE_INVALID;\n\n    RCLCPP_INFO(node_ctrl_->get_logger(), \"Enter Function: %s\", __func__);\n\n    // 检查是否有其他动作在执行\n    ctrlType = utRdCtrlType();\n    if (ctrlType != UT_CTRL_TYPE_INVALID)\n    {\n        return true;\n    }\n\n    return false;\n}\n\n// 起立\nint32_t RobDog_Ctrl_Unitree::robdogCtrl_StandUp()              \t\n{\n    UtCtrlType_E ctrlType = UT_CTRL_TYPE_INVALID;\n\n    RCLCPP_INFO(node_ctrl_->get_logger(), \"Enter Function: %s\", __func__);\n\n    // 存在关节温度过热\n    if (RobotInfoMgr::getInstance().utCheckTempStatus())\n    {\n        RCLCPP_INFO(node_ctrl_->get_logger(), \"robdogCtrl_StandUp: Over Heat, Can not Move!\");\n        return ROBDOGCTRL_ERROR_INVALID_STATE;\n    }\n\n    // 检查是否有其他动作在执行\n    ctrlType = utRdCtrlType();\n    if (ctrlType != UT_CTRL_TYPE_INVALID)\n    {\n        RCLCPP_INFO(node_ctrl_->get_logger(), \"Doing other action! ctrlType: %d\", ctrlType);\n        return ROBDOGCTRL_ERROR_INVALID_STATE;\n    }\n\n    // 写入站立动作\n    utWrCtrlType(UT_CTRL_TYPE_STANDUP);\n\n    return ROBDOGCTRL_ERROR_SUCCESS;\n}\n\n// 趴下\nint32_t RobDog_Ctrl_Unitree::robdogCtrl_GetDown()              \n{\n    UtCtrlType_E ctrlType = UT_CTRL_TYPE_INVALID;\n\n    RCLCPP_INFO(node_ctrl_->get_logger(), \"Enter Function: %s\", __func__);\n\n    // 检查是否有其他动作在执行\n    ctrlType = utRdCtrlType();\n    if (ctrlType != UT_CTRL_TYPE_INVALID)\n    {\n        RCLCPP_INFO(node_ctrl_->get_logger(), \"Doing other action! ctrlType: %d\", ctrlType);\n        return ROBDOGCTRL_ERROR_INVALID_STATE;\n    }\n\n    // 写入动作\n    utWrCtrlType(UT_CTRL_TYPE_GETDOWN);\n\n    return ROBDOGCTRL_ERROR_SUCCESS;\n}\n\n// 坐下\nint32_t RobDog_Ctrl_Unitree::robdogCtrl_Sit()       \t        \n{\n    UtCtrlType_E ctrlType = UT_CTRL_TYPE_INVALID;\n\n    RCLCPP_INFO(node_ctrl_->get_logger(), \"Enter Function: %s\", __func__);\n\n    // 存在关节温度过热\n    if (RobotInfoMgr::getInstance().utCheckTempStatus())\n    {\n        RCLCPP_INFO(node_ctrl_->get_logger(), \"robdogCtrl_Sit: Over Heat, Can not Move!\");\n        return ROBDOGCTRL_ERROR_INVALID_STATE;\n    }\n\n    // 检查是否有其他动作在执行\n    ctrlType = utRdCtrlType();\n    if (ctrlType != UT_CTRL_TYPE_INVALID)\n    {\n        RCLCPP_INFO(node_ctrl_->get_logger(), \"Doing other action! ctrlType: %d\", ctrlType);\n        return ROBDOGCTRL_ERROR_INVALID_STATE;\n    }\n\n    // 写入动作\n    utWrCtrlType(UT_CTRL_TYPE_SIT);\n\n    return ROBDOGCTRL_ERROR_SUCCESS;\n}\n\n// 做动作\nint32_t RobDog_Ctrl_Unitree::robdogCtrl_Locomotion(RobdogCtrlMotion motion)\n{\n    UtCtrlType_E ctrlType = UT_CTRL_TYPE_INVALID;\n\n    RCLCPP_INFO(node_ctrl_->get_logger(), \"Enter Function: %s\", __func__);\n\n    // 存在关节温度过热\n    if (RobotInfoMgr::getInstance().utCheckTempStatus())\n    {\n        RCLCPP_INFO(node_ctrl_->get_logger(), \"robdogCtrl_Locomotion: Over Heat, Can not Move!\");\n        return ROBDOGCTRL_ERROR_INVALID_STATE;\n    }\n\n    // 检查是否有其他动作在执行\n    ctrlType = utRdCtrlType();\n    if (ctrlType != UT_CTRL_TYPE_INVALID)\n    {\n        RCLCPP_INFO(node_ctrl_->get_logger(), \"Doing other action! ctrlType: %d\", ctrlType);\n        return ROBDOGCTRL_ERROR_INVALID_STATE;\n    }\n\n    // 写入动作\n    utWrCtrlMotion(motion);\n    utWrCtrlType(UT_CTRL_TYPE_LOCOMOTION);\n\n    return ROBDOGCTRL_ERROR_SUCCESS;\n}\n\n// 切换步态\nint32_t RobDog_Ctrl_Unitree::robdogCtrl_ChangeGait(RobdogCtrlGait gait)           \t    \n{\n    UtCtrlType_E ctrlType = UT_CTRL_TYPE_INVALID;\n\n    RCLCPP_INFO(node_ctrl_->get_logger(), \"Enter Function: %s\", __func__);\n\n    // 检查是否有其他动作在执行\n    ctrlType = utRdCtrlType();\n    if (ctrlType != UT_CTRL_TYPE_INVALID)\n    {\n        RCLCPP_INFO(node_ctrl_->get_logger(), \"Doing other action! ctrlType: %d\", ctrlType);\n        return ROBDOGCTRL_ERROR_INVALID_STATE;\n    }\n\n    // 写入步态\n    utWrCtrlGait(gait);\n    utWrCtrlType(UT_CTRL_TYPE_CHANGE_GAIT);\n\n    return ROBDOGCTRL_ERROR_SUCCESS;\n}\n\n// 语音指令（起立/趴下）\nint32_t RobDog_Ctrl_Unitree::robdogCtrl_VoiceStand(int32_t cmd)        \t\n{\n    int32_t ret = ROBDOGCTRL_ERROR_SUCCESS;\n\n    RCLCPP_INFO(node_ctrl_->get_logger(), \"Enter Function: %s\", __func__);\n\n    if (cmd == 1)\n    {\n        ret = robdogCtrl_StandUp();\n    }\n    else if (cmd ==2)\n    {\n        ret = robdogCtrl_Sit();\n    }\n\n    if (ret != ROBDOGCTRL_ERROR_SUCCESS)\n    {\n        RCLCPP_ERROR(node_ctrl_->get_logger(), \"VoiceStand Set Failed! ret = %d, cmd = %d\", ret, cmd);\n        return ret;\n    }\n\n    return ROBDOGCTRL_ERROR_SUCCESS;\n}\n\n// 持续运动\nint32_t RobDog_Ctrl_Unitree::robdogCtrl_ContinueMove(CtrlMoveData *pMoveData)\n{\n    int32_t level = 0;\n    int32_t speedIndex = 0;\n    RobdogCtrlGait gait;\n\n    RCLCPP_INFO(node_ctrl_->get_logger(), \"level = %d !!!!!!!!\", level);\n\n    level = RobotInfoMgr::getInstance().getSpeedLevel();\n    speedIndex = utSpeedGait + UT_SPEED_GAIT_MAX * level;\n\n    // 平滑处理\n    if (utJoystickSwitch)\n    {\n        utLastX = utLastX * (1 - utSmooth) + pMoveData->x * utSpeedJoystick[speedIndex][UT_SPEED_DIRECT_X] * utSmooth;\n        utLastY = utLastY * (1 - utSmooth) + pMoveData->y * utSpeedJoystick[speedIndex][UT_SPEED_DIRECT_Y] * utSmooth;\n        utLastYaw = utLastYaw * (1 - utSmooth) + pMoveData->yaw * utSpeedJoystick[speedIndex][UT_SPEED_DIRECT_YAW] * utSmooth;\n    }\n    else\n    {\n        utLastX = utLastX * (1 - utSmoothMove) + pMoveData->x * utSpeedMove[speedIndex][UT_SPEED_DIRECT_X] * utSmoothMove;\n        utLastY = utLastY * (1 - utSmoothMove) + pMoveData->y * utSpeedMove[speedIndex][UT_SPEED_DIRECT_Y] * utSmoothMove;\n        utLastYaw = utLastYaw * (1 - utSmoothMove) + pMoveData->yaw * utSpeedMove[speedIndex][UT_SPEED_DIRECT_YAW] * utSmoothMove;\n    }\n\n    // 经典步态低中档位切换为宇树慢速\n    gait = utRdCtrlGait();\n    if (gait == ROBDOGCTRL_GAIT_AICLASSIC && level != 2)\n    {\n        if (utSpeedLevel != -1)\n        {\n            utSpeedLevel = -1;\n            utSetSpeedLevel(utSpeedLevel);\n        }\n    }\n\n    utMoveProc(utJoystickSwitch, utLastX, utLastY, utLastYaw);\n\n    return ROBDOGCTRL_ERROR_SUCCESS;\n}\n\n// 运动接口\nint32_t RobDog_Ctrl_Unitree::robdogCtrl_Move(float x, float y, float yaw)\n{\n    utMoveProc(false, x, y, yaw);\n\n    return ROBDOGCTRL_ERROR_SUCCESS;\n}\n\nint32_t RobDog_Ctrl_Unitree::robdogCtrl_StopMove()\n{\n    int32_t ret = ROBDOGCTRL_ERROR_SUCCESS;\n    int32_t level = 0;\n    RobdogCtrlGait gait;\n\n    RCLCPP_INFO(node_ctrl_->get_logger(), \"Enter Function: %s\", __func__);\n\n    utSpeedClear();\n\n    if (utJoystickSwitch)\n    {\n        utPubJoystick->Write(utJoystickMsg);\n    }\n    else\n    {\n        if (utAvoidSwitch == true)\n        {\n            ret = ov_client.Move(0, 0, 0);\n        }\n        else\n        {\n            ret = utSc.Move(0, 0, 0);\n        }\n\n        if (ret != ROBDOGCTRL_ERROR_SUCCESS)\n        {\n            RCLCPP_ERROR(node_ctrl_->get_logger(), \"StopMove Action failed! ret = %d\", ret);\n            return ret;\n        }\n    }\n\n    // 经典步态低中档停止后，恢复宇树快速\n    gait = utRdCtrlGait();\n    level = RobotInfoMgr::getInstance().getSpeedLevel();\n    if (gait == ROBDOGCTRL_GAIT_AICLASSIC && level != 2)\n    {\n        if (utSpeedLevel != 1)\n        {\n            utSpeedLevel = 1;\n            utSetSpeedLevel(utSpeedLevel);\n        }\n    }\n\n    return ROBDOGCTRL_ERROR_SUCCESS;\n}\n\n// 回零\nint32_t RobDog_Ctrl_Unitree::robdogCtrl_ResetZero()\n{\n    int32_t ret = ROBDOGCTRL_ERROR_SUCCESS;\n\n    RCLCPP_INFO(node_ctrl_->get_logger(), \"Enter Function: %s\", __func__);\n\n    utSpeedClear();\n\n    if (utAvoidSwitch == true)\n    {\n        ret = ov_client.Move(0, 0, 0);\n    }\n    else\n    {\n        ret = utSc.Move(0, 0, 0);\n    }\n\n    if (ret != ROBDOGCTRL_ERROR_SUCCESS)\n    {\n        RCLCPP_ERROR(node_ctrl_->get_logger(), \"StopMove Action failed! ret = %d\", ret);\n        return ret;\n    }\n\n    return ROBDOGCTRL_ERROR_SUCCESS;\n}\n\n// 软急停\nint32_t RobDog_Ctrl_Unitree::robdogCtrl_EmergencyStop()\n{\n    int32_t ret = ROBDOGCTRL_ERROR_SUCCESS;\n    \n    RCLCPP_INFO(node_ctrl_->get_logger(), \"Enter Function: %s\", __func__);\n\n    utSpeedClear();\n\n    ret = utSc.Damp();\n    if (ret != ROBDOGCTRL_ERROR_SUCCESS)\n    {\n        RCLCPP_ERROR(node_ctrl_->get_logger(), \"Damp Action failed! ret = %d\", ret);\n        return ret;\n    }\n\n    return ROBDOGCTRL_ERROR_SUCCESS;\n}\n\n// 机器人IMU标定1阶段，平趴在地\nint32_t RobDog_Ctrl_Unitree::robdogCtrl_IMUDemaStage1()\n{\n    int32_t ret = ROBDOGCTRL_ERROR_SUCCESS;\n    int32_t status;\n\n    RCLCPP_INFO(node_ctrl_->get_logger(), \"Enter Function: %s\", __func__);\n\n    // 关闭运控\n    ret = utRsc.ServiceSwitch(\"mcf\", 0, status);\n    if (ret != ROBDOGCTRL_ERROR_SUCCESS)\n    {\n        RCLCPP_ERROR(node_ctrl_->get_logger(), \"MCF Service Switch Off Failed! ret = %d\", ret);\n        return ret;\n    }\n\n    ret = calibration_client.DoBasicDemaStartImuDemaInZeroTorque();\n    if (ret != ROBDOGCTRL_ERROR_SUCCESS)\n    {\n        RCLCPP_ERROR(node_ctrl_->get_logger(), \"DoBasicDemaStartImuDemaInZeroTorque Failed! ret = %d\", ret);\n        return ret;\n    }\n\n    return ROBDOGCTRL_ERROR_SUCCESS;\n}\n\n// 机器人IMU标定2阶段，平趴在地\nint32_t RobDog_Ctrl_Unitree::robdogCtrl_IMUDemaStage2()\n{\n    int32_t ret = ROBDOGCTRL_ERROR_SUCCESS;\n\n    RCLCPP_INFO(node_ctrl_->get_logger(), \"Enter Function: %s\", __func__);\n\n    ret = calibration_client.DoBasicDemaStartImuDema();\n    if (ret != ROBDOGCTRL_ERROR_SUCCESS)\n    {\n        RCLCPP_ERROR(node_ctrl_->get_logger(), \"DoBasicDemaStartImuDema Failed! ret = %d\", ret);\n        return ret;\n    }\n\n    return ROBDOGCTRL_ERROR_SUCCESS;\n}\n\n// 机器人关节标定1阶段，平趴在地\nint32_t RobDog_Ctrl_Unitree::robdogCtrl_JointDemaStage1()\n{\n    int32_t ret = ROBDOGCTRL_ERROR_SUCCESS;\n    int32_t status;\n    \n    RCLCPP_INFO(node_ctrl_->get_logger(), \"Enter Function: %s\", __func__);\n\n    // 关闭运控\n    ret = utRsc.ServiceSwitch(\"mcf\", 0, status);\n    if (ret != ROBDOGCTRL_ERROR_SUCCESS)\n    {\n        RCLCPP_ERROR(node_ctrl_->get_logger(), \"MCF Service Switch Off Failed! ret = %d\", ret);\n        return ret;\n    }\n\n    ret = calibration_client.DoBasicDemaStartJointDemaInZeroTorque();\n    if (ret != ROBDOGCTRL_ERROR_SUCCESS)\n    {\n        RCLCPP_ERROR(node_ctrl_->get_logger(), \"DoBasicDemaStartJointDemaInZeroTorque Failed! ret = %d\", ret);\n        return ret;\n    }\n\n    return ROBDOGCTRL_ERROR_SUCCESS;\n}\n\n// 机器人关节标定2阶段，所有机身电机摆对称三角\nint32_t RobDog_Ctrl_Unitree::robdogCtrl_JointDemaStage2()\n{\n    int32_t ret = ROBDOGCTRL_ERROR_SUCCESS;\n\n    RCLCPP_INFO(node_ctrl_->get_logger(), \"Enter Function: %s\", __func__);\n\n    ret = calibration_client.DoBasicDemaStartJointDemaAllJoint0();\n    if (ret != ROBDOGCTRL_ERROR_SUCCESS)\n    {\n        RCLCPP_ERROR(node_ctrl_->get_logger(), \"DoBasicDemaStartJointDemaAllJoint0 Failed! ret = %d\", ret);\n        return ret;\n    }\n\n    return ROBDOGCTRL_ERROR_SUCCESS;\n}\n\n// 机器人关节标定3阶段，所有大腿电机，小腿电机标定工装卡位置\nint32_t RobDog_Ctrl_Unitree::robdogCtrl_JointDemaStage3()\n{\n    int32_t ret = ROBDOGCTRL_ERROR_SUCCESS;\n\n    RCLCPP_INFO(node_ctrl_->get_logger(), \"Enter Function: %s\", __func__);\n\n    ret = calibration_client.DoBasicDemaStartJointDemaFrontJoint12();\n    if (ret != ROBDOGCTRL_ERROR_SUCCESS)\n    {\n        RCLCPP_ERROR(node_ctrl_->get_logger(), \"DoBasicDemaStartJointDemaFrontJoint12 Failed! ret = %d\", ret);\n        return ret;\n    }\n\n    ret = calibration_client.DoBasicDemaStartJointDemaRearJoint12();\n    if (ret != ROBDOGCTRL_ERROR_SUCCESS)\n    {\n        RCLCPP_ERROR(node_ctrl_->get_logger(), \"DoBasicDemaStartJointDemaRearJoint12 Failed! ret = %d\", ret);\n        return ret;\n    }\n\n    return ROBDOGCTRL_ERROR_SUCCESS;\n}\n\n// 机器人关节标定4阶段，保存数据\nint32_t RobDog_Ctrl_Unitree::robdogCtrl_JointDemaStage4()\n{\n    int32_t ret = ROBDOGCTRL_ERROR_SUCCESS;\n\n    RCLCPP_INFO(node_ctrl_->get_logger(), \"Enter Function: %s\", __func__);\n\n    ret = calibration_client.DoBasicDemaStartJointDemaSaveData();\n    if (ret != ROBDOGCTRL_ERROR_SUCCESS)\n    {\n        RCLCPP_ERROR(node_ctrl_->get_logger(), \"DoBasicDemaStartJointDemaSaveData Failed! ret = %d\", ret);\n        return ret;\n    }\n\n    return ROBDOGCTRL_ERROR_SUCCESS;\n}\n\n// 开启避障功能\nint32_t RobDog_Ctrl_Unitree::robdogCtrl_AvoidOpen()\n{\n    int32_t ret = ROBDOGCTRL_ERROR_SUCCESS;\n\n    RCLCPP_INFO(node_ctrl_->get_logger(), \"Enter Function: %s\", __func__);\n    \n    return ROBDOGCTRL_ERROR_SUCCESS;\n}\n\n// 关闭避障功能\nint32_t RobDog_Ctrl_Unitree::robdogCtrl_AvoidClose()\n{\n    int32_t ret = ROBDOGCTRL_ERROR_SUCCESS;\n\n    RCLCPP_INFO(node_ctrl_->get_logger(), \"Enter Function: %s\", __func__);\n\n    return ROBDOGCTRL_ERROR_SUCCESS;\n}\n\n// 手动模式\nint32_t RobDog_Ctrl_Unitree::robdogCtrl_ManualMode()\n{\n    RCLCPP_INFO(node_ctrl_->get_logger(), \"Enter Function: %s\", __func__);\n\n    return ROBDOGCTRL_ERROR_SUCCESS;\n}\n\n// 自主模式\nint32_t RobDog_Ctrl_Unitree::robdogCtrl_AutoMode()\n{\n    RCLCPP_INFO(node_ctrl_->get_logger(), \"Enter Function: %s\", __func__);\n    \n    return ROBDOGCTRL_ERROR_SUCCESS;\n}\n\n// 移动模式\nint32_t RobDog_Ctrl_Unitree::robdogCtrl_MoveMode()        \t    \n{\n    RCLCPP_INFO(node_ctrl_->get_logger(), \"Enter Function: %s\", __func__);\n    return ROBDOGCTRL_ERROR_SUCCESS;\n}\n\n// 心跳\nint32_t RobDog_Ctrl_Unitree::robdogCtrl_HeartBeat()        \t\n{\n    //RCLCPP_INFO(node_ctrl_->get_logger(), \"Enter Function: %s\", __func__);\n    return ROBDOGCTRL_ERROR_SUCCESS;\n}\n\n// 机器人温度信息\nint32_t RobDog_Ctrl_Unitree::robdogCtrl_Temperature()       \t\n{\n    RCLCPP_INFO(node_ctrl_->get_logger(), \"Enter Function: %s\", __func__);\n    return ROBDOGCTRL_ERROR_SUCCESS;\n}\n\n// 0x31010D07\nint32_t RobDog_Ctrl_Unitree::robdogCtrl_Position(float x, float y, float radian)   \t        \n{\n    RCLCPP_INFO(node_ctrl_->get_logger(), \"Enter Function: %s\", __func__);\n    return ROBDOGCTRL_ERROR_SUCCESS;\n}\n\n// 0x122\nint32_t RobDog_Ctrl_Unitree::robdogCtrl_PositionAngVel()       \n{\n    RCLCPP_INFO(node_ctrl_->get_logger(), \"Enter Function: %s\", __func__);\n    return ROBDOGCTRL_ERROR_SUCCESS;\n}\n\n// 用户定义, 0x00000160\nint32_t RobDog_Ctrl_Unitree::robdogCtrl_UserDefined(int32_t cmd)          \t\n{\n    RCLCPP_INFO(node_ctrl_->get_logger(), \"Enter Function: %s\", __func__);\n    return ROBDOGCTRL_ERROR_SUCCESS;\n}\n\n// 机器人状态信息\nint32_t RobDog_Ctrl_Unitree::robdogCtrl_State()             \t\n{\n    RCLCPP_INFO(node_ctrl_->get_logger(), \"Enter Function: %s\", __func__);\n    return ROBDOGCTRL_ERROR_SUCCESS;\n}\n\nint32_t RobDog_Ctrl_Unitree::robdogCtrl_Init(void* ctrl_ptr_)\n{\n    int32_t ret = ROBDOGCTRL_ERROR_SUCCESS;\n\n    if(nullptr == ctrl_ptr_) \n    {\n        return ROBDOGCTRL_ERROR_INVALID_PARA;\n    }\n\n    node_ctrl_ = (RobdogCtrlNode *)ctrl_ptr_;\n    \n    RCLCPP_INFO(node_ctrl_->get_logger(), \"Enter Function: %s\", __func__);\n\n    utSc.SetTimeout(20.0f);\n    utSc.Init();\n\n    utRsc.SetTimeout(10.0f);\n    utRsc.Init();\n\n    ov_client.SetTimeout(10.0f);\n    ov_client.Init();\n\n    calibration_client.SetTimeout(30.0f);\n    calibration_client.Init();\n\n    // thread ut_threadProc(bind(&RobDog_Ctrl_Unitree::utThreadProc, this));\n    // ut_threadProc.detach();\n\n    // thread ut_threadInit(bind(&RobDog_Ctrl_Unitree::utThreadInit, this));\n    // ut_threadInit.detach();\n\n    // utPubJoystick.reset(new ChannelPublisher<unitree_go::msg::dds_::WirelessController_>(\"rt/wirelesscontroller_unprocessed\"));\n    // utPubJoystick->InitChannel();\n\n    // ut_para_sub_ =  node_ctrl_->create_subscription<std_msgs::msg::String>(\n    //     \"/robdog_control/unitree_para\", 10,\n    //     std::bind(&RobDog_Ctrl_Unitree::utSetCtrlPara, this, std::placeholders::_1)\n    // );\n\n    //关闭雷达\n    //lidarSwitch.data(\"ON\");\n    //utPubLidarSwitch.reset(new ChannelPublisher<std_msgs::msg::dds_::String_>(\"rt/utlidar/switch\"));\n    //utPubLidarSwitch->InitChannel();\n    //utPubLidarSwitch->Write(lidarSwitch);\n\n    return ROBDOGCTRL_ERROR_SUCCESS;\n}\n"}]}