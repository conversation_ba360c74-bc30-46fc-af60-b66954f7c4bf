{"sourceFile": "robdog_control/src/robdog_control.cpp", "activeCommit": 0, "commits": [{"activePatchIndex": 1, "patches": [{"date": 1754267018796, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1754267028496, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -116,36 +116,16 @@\n     std::string devSN = ctrl_node_->get_parameter(\"devSN\").as_string();  \n     RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"devSN: %s\", devSN.c_str());\n \n     //机器人信息管理类初始化\n-    try {\n-        RCLCPP_INFO(ctrl_node_->get_logger(), \"Initializing node...\");\n-        ctrl_node_->initNode();\n-        RCLCPP_INFO(ctrl_node_->get_logger(), \"Node initialized successfully\");\n-\n-        RCLCPP_INFO(ctrl_node_->get_logger(), \"Initializing singleton managers...\");\n-        RobotState::getInstance().setDeviceId(devSN);\n-        RobotInfoMgr::getInstance().init(ctrl_node_.get());\n-        RobdogCenter::getInstance().init(ctrl_node_.get());\n-        TaskInfoMgr::getInstance().init(ctrl_node_.get());\n-        AlarmInfoMgr::getInstance().init(ctrl_node_.get());\n-        RobdogHandPosCtrl::getInstance().init(ctrl_node_.get());\n-        RobotState::getInstance().setFolderPath(folder_path);\n-        RCLCPP_INFO(ctrl_node_->get_logger(), \"All singleton managers initialized successfully\");\n-    }\n-    catch (const std::bad_alloc& e) {\n-        RCLCPP_ERROR(ctrl_node_->get_logger(), \"Memory allocation failed during singleton initialization: %s\", e.what());\n-        RCLCPP_ERROR(ctrl_node_->get_logger(), \"This may be due to insufficient memory or memory fragmentation\");\n-        return -1;\n-    }\n-    catch (const std::exception& e) {\n-        RCLCPP_ERROR(ctrl_node_->get_logger(), \"Exception during singleton initialization: %s\", e.what());\n-        return -1;\n-    }\n-    catch (...) {\n-        RCLCPP_ERROR(ctrl_node_->get_logger(), \"Unknown exception during singleton initialization\");\n-        return -1;\n-    }\n+    ctrl_node_->initNode();\n+    RobotState::getInstance().setDeviceId(devSN);\n+    RobotInfoMgr::getInstance().init(ctrl_node_.get());\n+    RobdogCenter::getInstance().init(ctrl_node_.get());\n+    TaskInfoMgr::getInstance().init(ctrl_node_.get());\n+    AlarmInfoMgr::getInstance().init(ctrl_node_.get());\n+    RobdogHandPosCtrl::getInstance().init(ctrl_node_.get());\n+    RobotState::getInstance().setFolderPath(folder_path);\n     std::atomic<bool> keep_running(true);  \n     auto send_thread = std::thread([&keep_running, ctrl_node_]() {\n         while (keep_running.load()) \n         {\n"}], "date": 1754267018796, "name": "Commit-0", "content": "//\n/*****websocket通讯服务端节点******/\n/**********默认端口为19002********/\n//\n#include \"rclcpp/rclcpp.hpp\"\n#include <cstring>\n\n#include <iostream>\n#include <fstream>\n#include <tuple>\n#include <unordered_map>\n#include <map>\n#include <chrono>\n#include <string>\n#include <csignal>  // 新增\n#include <signal.h> // 新增\n#include <jsoncpp/json/json.h>\n\n#include <cstdint> // For fixed-width integer types\n#include <array>   // For std::array\n\n#include <thread>       // 引入线程库\n#include <atomic>       // 引入原子操作库\n#include <chrono>   // 用于 std::chrono\n#include \"robdogNode/robdog_ctrl_node.h\"\n#include \"robotMgr/robot_info_mgr.h\"\n#include \"robdogCenter/robdog_center_mgr.h\"\n#include \"taskMgr/task_info_mgr.h\"\n#include \"alarmMgr/robor_alarm_mgr.h\"\n#include \"robdogHandPosCtrl/robdog_hand_pos.h\"\n#include \"robotState/RobotState.h\"\n#include \"followMe/followMeNode.h\"\nusing namespace std;\n\n//bool stopRequested = false;\n//bool pauseRequested = false;\n#pragma pack(4)\n\n// 信号处理函数\nvoid signal_handler(int signal) {\n    switch (signal) {\n        case SIGINT:\n            std::cout << \"Caught Ctrl+C (SIGINT)!\" << std::endl;\n            //stopRequested = true;\n            //rclcpp::shutdown();   // 否则程序结束无法坐下\n            break;\n        case SIGTSTP:\n            std::cout << \"Caught Ctrl+Z!\" << std::endl;\n            //pauseRequested = !pauseRequested; // 切换暂停状态\n            break;\n        default:\n            std::cout << \"Caught \" << signal << std::endl;\n            // 处理其他信号\n            break;\n    }\n}\n\nint main(int argc, char **argv)\n{\n    //  ------------------ 注册信号处理函数（ctrl+c） ------------------ \n    struct sigaction sa_ctrl_c;\n    sa_ctrl_c.sa_handler = &signal_handler;\n    sigemptyset(&sa_ctrl_c.sa_mask);\n    sa_ctrl_c.sa_flags = 0;\n    if (sigaction(SIGINT, &sa_ctrl_c, NULL) == -1) {\n        std::cerr << \"Failed to register SIGINT handler\" << std::endl;\n        return 1;\n    }\n    \n    // ------------------ 注册信号处理函数（ctrl+z） ------------------ \n    struct sigaction sa_tstp;\n    sa_tstp.sa_handler = &signal_handler;\n    sigemptyset(&sa_tstp.sa_mask);\n    sa_tstp.sa_flags = 0;\n    if (sigaction(SIGTSTP, &sa_tstp, NULL) == -1) {\n        std::cerr << \"Failed to register SIGTSTP handler\" << std::endl;\n        return 1;\n    }\n\n    boost::filesystem::path file_path = boost::filesystem::canonical(argv[0]);\n    boost::filesystem::path folder_path = file_path.parent_path();\n    \n    // ROS2代码：\n    rclcpp::init(argc, argv);\n\n    // 创建一个节点\n    std::shared_ptr<RobdogCtrlNode> ctrl_node_ = std::make_shared<RobdogCtrlNode>();\n    auto follow_node_ = std::make_shared<FollowNode>();\n\n    rclcpp::executors::MultiThreadedExecutor executor(rclcpp::ExecutorOptions(), 4);  // 指定线程数\n    // auto executor = rclcpp::executors::SingleThreadedExecutor();\n    executor.add_node(ctrl_node_);\n    executor.add_node(follow_node_);\n\n    // socket 通信\n    RobManuModel manu = ROB_MANU_DEEP_LITE;\n    manu = ctrl_node_->getRobManuModel();\n    //ctrl_node_->initSocket(\"192.168.1.120\", 6688, 43893);\n    if (manu == ROB_MANU_DEEP_LITE)\n    {\n        ctrl_node_->initSocket(\"192.168.1.120\", 6688, 43893);\n        ctrl_node_->robdogCtrlDev = std::make_shared<RobDog_Ctrl_Deep>();\n        ctrl_node_->robdogCtrlDev->robdogCtrl_Init((void *)ctrl_node_.get());\n    }\n    else if (manu == ROB_MANU_UNITREE_GO2)\n    {\n        unitree::robot::ChannelFactory::Instance()->Init(0);\n        ctrl_node_->robdogCtrlDev = std::make_shared<RobDog_Ctrl_Unitree>();\n        ctrl_node_->robdogCtrlDev->robdogCtrl_Init((void *)ctrl_node_.get());\n    }\n\n    // 等待一段时间确保 UDP 通信初始化完成\n    rclcpp::sleep_for(std::chrono::milliseconds(200));\n\n    ctrl_node_->declare_parameter<string>(\"devSN\", \"12345\"); \n    std::string devSN = ctrl_node_->get_parameter(\"devSN\").as_string();  \n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"devSN: %s\", devSN.c_str());\n\n    //机器人信息管理类初始化\n    try {\n        RCLCPP_INFO(ctrl_node_->get_logger(), \"Initializing node...\");\n        ctrl_node_->initNode();\n        RCLCPP_INFO(ctrl_node_->get_logger(), \"Node initialized successfully\");\n\n        RCLCPP_INFO(ctrl_node_->get_logger(), \"Initializing singleton managers...\");\n        RobotState::getInstance().setDeviceId(devSN);\n        RobotInfoMgr::getInstance().init(ctrl_node_.get());\n        RobdogCenter::getInstance().init(ctrl_node_.get());\n        TaskInfoMgr::getInstance().init(ctrl_node_.get());\n        AlarmInfoMgr::getInstance().init(ctrl_node_.get());\n        RobdogHandPosCtrl::getInstance().init(ctrl_node_.get());\n        RobotState::getInstance().setFolderPath(folder_path);\n        RCLCPP_INFO(ctrl_node_->get_logger(), \"All singleton managers initialized successfully\");\n    }\n    catch (const std::bad_alloc& e) {\n        RCLCPP_ERROR(ctrl_node_->get_logger(), \"Memory allocation failed during singleton initialization: %s\", e.what());\n        RCLCPP_ERROR(ctrl_node_->get_logger(), \"This may be due to insufficient memory or memory fragmentation\");\n        return -1;\n    }\n    catch (const std::exception& e) {\n        RCLCPP_ERROR(ctrl_node_->get_logger(), \"Exception during singleton initialization: %s\", e.what());\n        return -1;\n    }\n    catch (...) {\n        RCLCPP_ERROR(ctrl_node_->get_logger(), \"Unknown exception during singleton initialization\");\n        return -1;\n    }\n    std::atomic<bool> keep_running(true);  \n    auto send_thread = std::thread([&keep_running, ctrl_node_]() {\n        while (keep_running.load()) \n        {\n            ctrl_node_->robdogCtrlDev->robdogCtrl_HeartBeat();\n            std::this_thread::sleep_for(std::chrono::milliseconds(100));\n            RCLCPP_INFO_THROTTLE(ctrl_node_->get_logger(),*ctrl_node_->get_clock(),4000,\"SEND Heartbeat Pack To JY_EXE!\");\n        }\n    });\n\n    // ctrl_node_->StandUp();\n\n    // 进入 ROS 2 事件循环\n    executor.spin();\n    keep_running.store(false);  \n    if (send_thread.joinable()) {\n        send_thread.join(); \n    }\n    rclcpp::shutdown();\n    return 0;\n}\n"}]}