{"sourceFile": "robdog_control/src/robotMgr/robot_info_mgr.cpp", "activeCommit": 0, "commits": [{"activePatchIndex": 6, "patches": [{"date": 1754200211846, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1754223135806, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -976,9 +976,9 @@\n     }\n \n     if (cnt == 60)\n     {\n-        RCLCPP_INFO(node_ctrl_->get_logger(), \"Sport Control Board Connection Error.\");\n+        // RCLCPP_INFO(node_ctrl_->get_logger(), \"Sport Control Board Connection Error.\");\n         return false;\n     }\n \n     RCLCPP_INFO_THROTTLE(node_ctrl_->get_logger(), *node_ctrl_->get_clock(), 3000, \"Sport Control Board Connected.\");\n"}, {"date": 1754230061124, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -163,10 +163,10 @@\n     devAlarmRep_sub = node_ctrl_->create_subscription<std_msgs::msg::String>(\n         \"/device_alarm_report\", 20,\n         std::bind(&RobotInfoMgr::devAlarmReportCallback, this, std::placeholders::_1));\n \n-    std::thread data_collect_thread = std::thread(&RobotInfoMgr::dataCollect, this);\n-    data_collect_thread.detach();\n+    // std::thread data_collect_thread = std::thread(&RobotInfoMgr::dataCollect, this);\n+    // data_collect_thread.detach();\n \n     node_ctrl_->declare_parameter<int>(\"state_report_interval\", 10);\n     uint32_t state_report_interval = node_ctrl_->get_parameter(\"state_report_interval\").as_int();\n     RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"state_report_interval: %d\", state_report_interval);\n@@ -971,9 +971,9 @@\n     while ((getUtRobotStatus() == UT_ROBDOG_STATUS_STATE) && (cnt < 60))\n     {\n         std::this_thread::sleep_for(std::chrono::milliseconds(1000));\n         cnt++;\n-        RCLCPP_INFO(node_ctrl_->get_logger(), \"Wait Connect: %d\", cnt);\n+        // RCLCPP_INFO(node_ctrl_->get_logger(), \"Wait Connect: %d\", cnt);\n     }\n \n     if (cnt == 60)\n     {\n"}, {"date": 1754230174423, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -174,41 +174,41 @@\n     state_report_timer_ = node_ctrl_->create_wall_timer(\n         std::chrono::seconds(state_report_interval),\n         std::bind(&RobotInfoMgr::stateReporter, this));\n \n-    if (node_ctrl_->robManuModel == ROB_MANU_DEEP_LITE)\n-    {\n-        thread send_thread(bind(&RobotInfoMgr::sendrobdogcall, this));\n-        send_thread.detach();\n-        thread recv_thread(bind(&RobotInfoMgr::receiveAndProcessData, this));\n-        recv_thread.detach();\n+    // if (node_ctrl_->robManuModel == ROB_MANU_DEEP_LITE)\n+    // {\n+    //     thread send_thread(bind(&RobotInfoMgr::sendrobdogcall, this));\n+    //     send_thread.detach();\n+    //     thread recv_thread(bind(&RobotInfoMgr::receiveAndProcessData, this));\n+    //     recv_thread.detach();\n \n-        // 接收温度信息\n-        thread send_thread_2(bind(&RobotInfoMgr::sendrobdogcall_temperature, this));\n-        send_thread_2.detach();\n-        thread recv_thread_2(bind(&RobotInfoMgr::receiveAndProcessData_temperature, this));\n-        recv_thread_2.detach();\n-    }\n-    else if (node_ctrl_->robManuModel == ROB_MANU_UNITREE_GO2)\n-    {\n+    //     // 接收温度信息\n+    //     thread send_thread_2(bind(&RobotInfoMgr::sendrobdogcall_temperature, this));\n+    //     send_thread_2.detach();\n+    //     thread recv_thread_2(bind(&RobotInfoMgr::receiveAndProcessData_temperature, this));\n+    //     recv_thread_2.detach();\n+    // }\n+    // else if (node_ctrl_->robManuModel == ROB_MANU_UNITREE_GO2)\n+    // {\n \n-        node_ctrl_->declare_parameter<int>(\"max_fan_speed\", 0);\n-        max_fan_speed = node_ctrl_->get_parameter(\"max_fan_speed\").as_int();\n-        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"max_fan_speed: %d\", max_fan_speed);\n+    //     node_ctrl_->declare_parameter<int>(\"max_fan_speed\", 0);\n+    //     max_fan_speed = node_ctrl_->get_parameter(\"max_fan_speed\").as_int();\n+    //     RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"max_fan_speed: %d\", max_fan_speed);\n \n-        node_ctrl_->declare_parameter<int>(\"half_max_fan_speed\", 20);\n-        half_max_fan_speed = node_ctrl_->get_parameter(\"half_max_fan_speed\").as_int();\n-        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"half_max_fan_speed: %d\", half_max_fan_speed);\n+    //     node_ctrl_->declare_parameter<int>(\"half_max_fan_speed\", 20);\n+    //     half_max_fan_speed = node_ctrl_->get_parameter(\"half_max_fan_speed\").as_int();\n+    //     RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"half_max_fan_speed: %d\", half_max_fan_speed);\n \n-        node_ctrl_->declare_parameter<int>(\"min_fan_speed\", 50);\n-        min_fan_speed = node_ctrl_->get_parameter(\"min_fan_speed\").as_int();\n-        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"min_fan_speed: %d\", min_fan_speed);\n+    //     node_ctrl_->declare_parameter<int>(\"min_fan_speed\", 50);\n+    //     min_fan_speed = node_ctrl_->get_parameter(\"min_fan_speed\").as_int();\n+    //     RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"min_fan_speed: %d\", min_fan_speed);\n \n-        current_fan_speed = min_fan_speed;\n+    //     current_fan_speed = min_fan_speed;\n \n-        thread ut_state_thread(bind(&RobotInfoMgr::utStateThreadCall, this));\n-        ut_state_thread.detach();\n-    }\n+    //     thread ut_state_thread(bind(&RobotInfoMgr::utStateThreadCall, this));\n+    //     ut_state_thread.detach();\n+    // }\n \n     {\n         std::unique_lock<std::mutex> lock(data_mutex_);\n         data_cv_.wait(lock, [this]() { return data_collected_; });\n"}, {"date": 1754267004047, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -163,10 +163,10 @@\n     devAlarmRep_sub = node_ctrl_->create_subscription<std_msgs::msg::String>(\n         \"/device_alarm_report\", 20,\n         std::bind(&RobotInfoMgr::devAlarmReportCallback, this, std::placeholders::_1));\n \n-    // std::thread data_collect_thread = std::thread(&RobotInfoMgr::dataCollect, this);\n-    // data_collect_thread.detach();\n+    std::thread data_collect_thread = std::thread(&RobotInfoMgr::dataCollect, this);\n+    data_collect_thread.detach();\n \n     node_ctrl_->declare_parameter<int>(\"state_report_interval\", 10);\n     uint32_t state_report_interval = node_ctrl_->get_parameter(\"state_report_interval\").as_int();\n     RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"state_report_interval: %d\", state_report_interval);\n@@ -174,41 +174,41 @@\n     state_report_timer_ = node_ctrl_->create_wall_timer(\n         std::chrono::seconds(state_report_interval),\n         std::bind(&RobotInfoMgr::stateReporter, this));\n \n-    // if (node_ctrl_->robManuModel == ROB_MANU_DEEP_LITE)\n-    // {\n-    //     thread send_thread(bind(&RobotInfoMgr::sendrobdogcall, this));\n-    //     send_thread.detach();\n-    //     thread recv_thread(bind(&RobotInfoMgr::receiveAndProcessData, this));\n-    //     recv_thread.detach();\n+    if (node_ctrl_->robManuModel == ROB_MANU_DEEP_LITE)\n+    {\n+        thread send_thread(bind(&RobotInfoMgr::sendrobdogcall, this));\n+        send_thread.detach();\n+        thread recv_thread(bind(&RobotInfoMgr::receiveAndProcessData, this));\n+        recv_thread.detach();\n \n-    //     // 接收温度信息\n-    //     thread send_thread_2(bind(&RobotInfoMgr::sendrobdogcall_temperature, this));\n-    //     send_thread_2.detach();\n-    //     thread recv_thread_2(bind(&RobotInfoMgr::receiveAndProcessData_temperature, this));\n-    //     recv_thread_2.detach();\n-    // }\n-    // else if (node_ctrl_->robManuModel == ROB_MANU_UNITREE_GO2)\n-    // {\n+        // 接收温度信息\n+        thread send_thread_2(bind(&RobotInfoMgr::sendrobdogcall_temperature, this));\n+        send_thread_2.detach();\n+        thread recv_thread_2(bind(&RobotInfoMgr::receiveAndProcessData_temperature, this));\n+        recv_thread_2.detach();\n+    }\n+    else if (node_ctrl_->robManuModel == ROB_MANU_UNITREE_GO2)\n+    {\n \n-    //     node_ctrl_->declare_parameter<int>(\"max_fan_speed\", 0);\n-    //     max_fan_speed = node_ctrl_->get_parameter(\"max_fan_speed\").as_int();\n-    //     RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"max_fan_speed: %d\", max_fan_speed);\n+        node_ctrl_->declare_parameter<int>(\"max_fan_speed\", 0);\n+        max_fan_speed = node_ctrl_->get_parameter(\"max_fan_speed\").as_int();\n+        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"max_fan_speed: %d\", max_fan_speed);\n \n-    //     node_ctrl_->declare_parameter<int>(\"half_max_fan_speed\", 20);\n-    //     half_max_fan_speed = node_ctrl_->get_parameter(\"half_max_fan_speed\").as_int();\n-    //     RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"half_max_fan_speed: %d\", half_max_fan_speed);\n+        node_ctrl_->declare_parameter<int>(\"half_max_fan_speed\", 20);\n+        half_max_fan_speed = node_ctrl_->get_parameter(\"half_max_fan_speed\").as_int();\n+        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"half_max_fan_speed: %d\", half_max_fan_speed);\n \n-    //     node_ctrl_->declare_parameter<int>(\"min_fan_speed\", 50);\n-    //     min_fan_speed = node_ctrl_->get_parameter(\"min_fan_speed\").as_int();\n-    //     RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"min_fan_speed: %d\", min_fan_speed);\n+        node_ctrl_->declare_parameter<int>(\"min_fan_speed\", 50);\n+        min_fan_speed = node_ctrl_->get_parameter(\"min_fan_speed\").as_int();\n+        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"min_fan_speed: %d\", min_fan_speed);\n \n-    //     current_fan_speed = min_fan_speed;\n+        current_fan_speed = min_fan_speed;\n \n-    //     thread ut_state_thread(bind(&RobotInfoMgr::utStateThreadCall, this));\n-    //     ut_state_thread.detach();\n-    // }\n+        thread ut_state_thread(bind(&RobotInfoMgr::utStateThreadCall, this));\n+        ut_state_thread.detach();\n+    }\n \n     {\n         std::unique_lock<std::mutex> lock(data_mutex_);\n         data_cv_.wait(lock, [this]() { return data_collected_; });\n"}, {"date": 1754269871276, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -971,9 +971,9 @@\n     while ((getUtRobotStatus() == UT_ROBDOG_STATUS_STATE) && (cnt < 60))\n     {\n         std::this_thread::sleep_for(std::chrono::milliseconds(1000));\n         cnt++;\n-        // RCLCPP_INFO(node_ctrl_->get_logger(), \"Wait Connect: %d\", cnt);\n+        RCLCPP_INFO(node_ctrl_->get_logger(), \"Wait Connect: %d\", cnt);\n     }\n \n     if (cnt == 60)\n     {\n"}, {"date": 1754276017876, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -879,9 +879,9 @@\n         threshold = TEMP_THREADHOLD;\n     }\n \n     // 温度正常不播报，距离上次播报60s内不播报\n-    if ((tempStatus == \"Normal\") || ((node_ctrl_->now() - lastBroadcastTime).seconds() < 60.0))\n+    if ((tempStatus == \"Normal\") || ((node_ctrl_->now() - lastBroadcastTime).seconds() < 120.0))\n     {\n         return;\n     }\n \n@@ -971,18 +971,18 @@\n     while ((getUtRobotStatus() == UT_ROBDOG_STATUS_STATE) && (cnt < 60))\n     {\n         std::this_thread::sleep_for(std::chrono::milliseconds(1000));\n         cnt++;\n-        RCLCPP_INFO(node_ctrl_->get_logger(), \"Wait Connect: %d\", cnt);\n+        // RCLCPP_INFO(node_ctrl_->get_logger(), \"Wait Connect: %d\", cnt);\n     }\n \n     if (cnt == 60)\n     {\n-        // RCLCPP_INFO(node_ctrl_->get_logger(), \"Sport Control Board Connection Error.\");\n+        RCLCPP_INFO(node_ctrl_->get_logger(), \"Sport Control Board Connection Error.\");\n         return false;\n     }\n \n-    RCLCPP_INFO_THROTTLE(node_ctrl_->get_logger(), *node_ctrl_->get_clock(), 3000, \"Sport Control Board Connected.\");\n+    // RCLCPP_INFO_THROTTLE(node_ctrl_->get_logger(), *node_ctrl_->get_clock(), 3000, \"Sport Control Board Connected.\");\n \n     return true;\n }\n \n"}], "date": 1754200211846, "name": "Commit-0", "content": "/*\n * @Author: 高亚军 <EMAIL>\n * @Date: 2024-10-01 10:04:29\n * @LastEditors: 高亚军 <EMAIL>\n * @LastEditTime: 2024-10-02 21:02:13\n * @FilePath: \\robot-application\\deeprobots_application_ros1\\src\\robdog_platintera\\src\\robdog_subpub\\read_map_point_config\\read_map_point_cfg.cpp\n * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE\n */\n\n#include <iostream>\n#include <fstream>\n#include <sstream>\n#include <chrono>\n#include <cstring>\n#include <rclcpp/rclcpp.hpp>\n#include <std_msgs/msg/string.hpp>\n#include <ament_index_cpp/get_package_share_directory.hpp>\n#include \"robdogNode/robdog_ctrl_node.h\"\n#include \"robot_info_mgr.h\"\n#include <homi_com/homi_utils.hpp>\n#include \"robotState/RobotState.h\"\n#include \"xiaoli_com/xiaoli_pub_def.h\"\n#include <regex>\n#include <mutex>\n#include <condition_variable>\n\nextern string g_netctrl_ret;\n\n#define TOPIC_HIGHSTATE       \"rt/lf/sportmodestate\"\n#define TOPIC_LOWSTATE        \"rt/lf/lowstate\"\n#define TOPIC_CLOUD           \"rt/utlidar/cloud\"\n#define TOPIC_CLOUD_DESKEWED  \"rt/utlidar/cloud_deskewed\"\n#define TOPIC_LIDARSTATE      \"rt/utlidar/lidar_state\"\n#define utStateDumpSwitch  0\n\nstd::map<std::string, std::map<std::string, std::string>> brocastText = {\n    {\"CPU\", {\n        {\"TooLow\", \"机器狗身体温度过低，已停止运行!请等温度恢复正常后，再尝试作业\"},\n        {\"Low_more\", \"哎呀，当前我的身体温度过低！当前温度为%0.0f度，请停止作业，将我移动至合适位置吧\"},\n        {\"Low\", \"哎呀，当前我的身体温度接近危险值！当前温度为%0.0f度，建议休息一下或者减少负载\"},\n        {\"High\", \"哎呀，当前我的身体温度接近危险值！当前温度为%0.0f度，建议休息一下或者减少负载\"},\n        {\"High_more\", \"哎呀，当前我的身体温度过高！当前温度为%0.0f度，请停止作业，将我移动至合适位置吧\"},\n        {\"TooHigh\", \"机器狗身体温度过高，已停止运行!请等温度恢复正常后，再尝试作业\"}\n    }},\n    {\"joint\", {\n        {\"TooLow\", \"机器狗关节温度过低，已停止运行!请等温度恢复正常后，再尝试作业\"},\n        {\"Low_more\", \"哎呀，当前我的关节温度过低！当前温度为%0.0f度，请停止作业，将我移动至合适位置吧\"},\n        {\"Low\", \"哎呀，当前我的关节温度接近危险值！当前温度为%0.0f度，建议休息一下或者减少负载\"},\n        {\"High\", \"哎呀，当前我的关节温度接近危险值！当前温度为%0.0f度，建议休息一下或者减少负载\"},\n        {\"High_more\", \"哎呀，当前我的关节温度过高！当前温度为%0.0f度，请停止作业，将我移动至合适位置吧\"},\n        {\"TooHigh\", \"机器狗关节温度过高，已停止运行!请等温度恢复正常后，再尝试作业\"}\n    }}\n};\n\nstd::string strArrayPart[ROB_MANU_MAX][3] =\n{\n    {\"swing \", \"knee \", \"hip \"},\n    {\"Hip \", \"Thigh \", \"Calf \"}\n};\n\nstd::string strArrayLocation[ROB_MANU_MAX][4] = \n{\n    {\"front left \", \"front right \", \"back left \", \"back right \"},\n    {\"front right \", \"front left \", \"back right \", \"back left \"}\n};\n\n// 定义状态查询表\nusing StateKey = std::tuple<int32_t, int32_t, int32_t>;\nusing StateValue = std::tuple<std::string, HomiRobotStatus>;\nstatic std::map<StateKey, StateValue> stateQueryTableString = {\n    {{1, 0, 0}, {\"趴下状态\", ROBDOG_STATUS_GETDOWN}},\n    {{1, 0, 11}, {\"正在执行向前跳\", ROBDOG_STATUS_FORWARD_JUMPPING}},   \n    {{4, 0, 0}, {\"准备起立状态\", ROBDOG_STATUS_READYTOSTAND}},\n    {{5, 0, 0}, {\"正在起立状态\", ROBDOG_STATUS_STANDING}},\n    {{6, 0, 0}, {\"力控状态（静止站立）且步态为平地低速步态\", ROBDOG_STATUS_FORCE_CTRL_LEVEL_LOWSPPEED_GAIT}},\n    {{6, 0, 1}, {\"正在以平地低速步态踏步或正在根据轴指令扭动身体\", ROBDOG_STATUS_BODYROTWISTING_BY_AXISCOMMAND}}, \n    {{6, 0, 2}, {\"正在执行扭身体\", ROBDOG_STATUS_BODYROTWISTING}},   \n    {{6, 0, 4}, {\"正在执行扭身跳\", ROBDOG_STATUS_BODYJUMPPING}},\n    {{6, 2, 0}, {\"力控状态（静止站立）且步态为通用越障步态\", ROBDOG_STATUS_FORCE_CTRL_GEN_OBS_GAIT}},\n    {{6, 2, 1}, {\"正在以通用越障步态踏步\", ROBDOG_STATUS_GEN_OBS_GAIT_STEPPING}},\n    {{6, 4, 0}, {\"力控状态（静止站立）且步态为平地中速步态\", ROBDOG_STATUS_FORCE_CTRL_LEVEL_MIDSPEED_GAIT}},\n    {{6, 4, 1}, {\"正在以平地中速步态踏步\", ROBDOG_STATUS_LEVEL_MIDSPEED_GAIT_STEPPING}},\n    {{6, 5, 0}, {\"力控状态（静止站立）且步态为平地高速步态\", ROBDOG_STATUS_FORCE_CTRL_LEVEL_HIGHSPEED_GAIT}},\n    {{6, 5, 1}, {\"正在以平地高速步态踏步\", ROBDOG_STATUS_LEVEL_HIGHSPEED_GAIT_STEPPING}},\n    {{6, 6, 0}, {\"力控状态（静止站立）且步态为抓地越障步态\", ROBDOG_STATUS_FORCE_CTRL_GROUND_GRIPPING_GAIT}},\n    {{6, 6, 1}, {\"正在以抓地越障步态踏步\", ROBDOG_STATUS_GROUND_GRIPPING_GAIT_STEPPING}},\n    {{6, 12, 1}, {\"正在执行太空步\", ROBDOG_STATUS_MOONWALKING}},\n    {{6, 13, 0}, {\"力控状态（静止站立）且步态为高踏步越障步态\", ROBDOG_STATUS_FORCE_CTRL_HIGHSTEP_OBS_GAIT}},\n    {{6, 13, 1}, {\"正在以高踏步越障步态踏步\", ROBDOG_STATUS_HIGHSTEP_OBS_GAIT_STEPPING}},\n    {{7, 0, 0}, {\"正在趴下状态\", ROBDOG_STATUS_PRONEING}},\n    {{8, 0, 0}, {\"失控保护状态\", ROBDOG_STATUS_LOSS_CTRL}},\n    {{9, 0, 0}, {\"姿态调整状态\", ROBDOG_STATUS_ATTITUDE_ADJUST_MODE}},\n    {{11, 0, 0}, {\"正在执行翻身\", ROBDOG_STATUS_ROLL_OVERING}},\n    {{17, 0, 0}, {\"回零状态\", ROBDOG_STATUS_RESET}},\n    {{18, 0, 0}, {\"正在执行后空翻\", ROBDOG_STATUS_BACKFLIP}},\n    {{20, 0, 0}, {\"正在执行打招呼\", ROBDOG_STATUS_HELLO}},\n    {{16, 16, 14}, {\"AI模式下站立\", ROBDOG_STATUS_AI}},\n    {{19, 0, 0}, {\"正在执行坐下\", ROBDOG_STATUS_SITDOWNING}},\n    {{25, 0, 0}, {\"坐下状态\", ROBDOG_STATUS_SITDOWN}}\n};\n\n// 解析机器人上报的数据\nDeepRobotState::DeepRobotState(const char* data, RobdogCtrlNode* udp_ctrl) {\n    if (!data) return; \n    std::memcpy(&robot_basic_state, data, sizeof(robot_basic_state));\n    std::memcpy(&robot_gait_state, data + sizeof(robot_basic_state), sizeof(robot_gait_state));\n    std::memcpy(rpy.data(), data + 8, sizeof(double) * 3);\n    std::memcpy(rpy_vel.data(), data + 32, sizeof(double) * 3);\n    std::memcpy(xyz_acc.data(), data + 56, sizeof(double) * 3);\n    std::memcpy(pos_world.data(), data + 80, sizeof(double) * 3);\n    std::memcpy(vel_world.data(), data + 104, sizeof(double) * 3);\n    std::memcpy(vel_body.data(), data + 128, sizeof(double) * 3);\n    std::memcpy(&touch_down_and_stair_trot, data + 152, sizeof(touch_down_and_stair_trot));\n    std::memcpy(&is_charging, data + 156, sizeof(is_charging));\n    std::memcpy(&error_state, data + 160, sizeof(error_state));   // 因为对齐要偏移四个字节\n    std::memcpy(&robot_motion_state, data + 164, sizeof(robot_motion_state));\n    std::memcpy(&battery_level, data + 168, sizeof(battery_level));\n    std::memcpy(&task_state, data + 176, sizeof(task_state));\n    std::memcpy(&is_robot_need_move, data + 180, sizeof(is_robot_need_move));\n    std::memcpy(&zero_position_flag, data + 181, sizeof(zero_position_flag));\n    std::memcpy(ultrasound.data(), data + 184, sizeof(double) * 2);  \n}\n\n// RobotStateReceived 构造函数实现\nDeepRobotStateReceived::DeepRobotStateReceived(const char* data, RobdogCtrlNode* udp_ctrl) \n: robot_state(data + 12, udp_ctrl) {\n    // 解析 RobotStateReceived 的数据\n    std::memcpy(&code, data, sizeof(code));\n    data += sizeof(code);\n    std::memcpy(&size, data, sizeof(size));\n    data += sizeof(size);\n    std::memcpy(&cons_code, data, sizeof(cons_code));\n}\n\nRobotInfoMgr::RobotInfoMgr() {\n}\n\nRobotInfoMgr::~RobotInfoMgr() {\n}\n\nvoid RobotInfoMgr::init(RobdogCtrlNode* ctrl_ptr_) {\n    if(nullptr == ctrl_ptr_) {\n        return;\n    }\n    node_ctrl_ = ctrl_ptr_;\n\n    lastBroadcastTime = node_ctrl_->now();\n\n    robotInfoWarnPub_=node_ctrl_->create_publisher<std_msgs::msg::String>(\"/device_alarm_report\", 10);\n#ifndef UNITREE\n    external_device_status_sub = node_ctrl_->create_subscription<std_msgs::msg::String>(\n            \"/external_device_status\", 10, std::bind(&RobotInfoMgr::external_device_status_callback, this, std::placeholders::_1));\n#endif\n\n\t/****************************** 智能播报相关 **********************************/\n    // 智能播报服务的客户端【上传播报文本】\n    brocast_client = node_ctrl_->create_client<homi_speech_interface::srv::AssistantSpeechText>(\n        \"/homi_speech/helper_assistant_speech_text_service\");\n\n    platform_client = node_ctrl_->create_client<homi_speech_interface::srv::SIGCData>(\n        \"/homi_speech/sigc_data_service\"); // 上发给平台的消息\n\n    devAlarmRep_sub = node_ctrl_->create_subscription<std_msgs::msg::String>(\n        \"/device_alarm_report\", 20,\n        std::bind(&RobotInfoMgr::devAlarmReportCallback, this, std::placeholders::_1));\n\n    std::thread data_collect_thread = std::thread(&RobotInfoMgr::dataCollect, this);\n    data_collect_thread.detach();\n\n    node_ctrl_->declare_parameter<int>(\"state_report_interval\", 10);\n    uint32_t state_report_interval = node_ctrl_->get_parameter(\"state_report_interval\").as_int();\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"state_report_interval: %d\", state_report_interval);\n\n    state_report_timer_ = node_ctrl_->create_wall_timer(\n        std::chrono::seconds(state_report_interval),\n        std::bind(&RobotInfoMgr::stateReporter, this));\n\n    if (node_ctrl_->robManuModel == ROB_MANU_DEEP_LITE)\n    {\n        thread send_thread(bind(&RobotInfoMgr::sendrobdogcall, this));\n        send_thread.detach();\n        thread recv_thread(bind(&RobotInfoMgr::receiveAndProcessData, this));\n        recv_thread.detach();\n\n        // 接收温度信息\n        thread send_thread_2(bind(&RobotInfoMgr::sendrobdogcall_temperature, this));\n        send_thread_2.detach();\n        thread recv_thread_2(bind(&RobotInfoMgr::receiveAndProcessData_temperature, this));\n        recv_thread_2.detach();\n    }\n    else if (node_ctrl_->robManuModel == ROB_MANU_UNITREE_GO2)\n    {\n\n        node_ctrl_->declare_parameter<int>(\"max_fan_speed\", 0);\n        max_fan_speed = node_ctrl_->get_parameter(\"max_fan_speed\").as_int();\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"max_fan_speed: %d\", max_fan_speed);\n\n        node_ctrl_->declare_parameter<int>(\"half_max_fan_speed\", 20);\n        half_max_fan_speed = node_ctrl_->get_parameter(\"half_max_fan_speed\").as_int();\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"half_max_fan_speed: %d\", half_max_fan_speed);\n\n        node_ctrl_->declare_parameter<int>(\"min_fan_speed\", 50);\n        min_fan_speed = node_ctrl_->get_parameter(\"min_fan_speed\").as_int();\n        RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"min_fan_speed: %d\", min_fan_speed);\n\n        current_fan_speed = min_fan_speed;\n\n        thread ut_state_thread(bind(&RobotInfoMgr::utStateThreadCall, this));\n        ut_state_thread.detach();\n    }\n\n    {\n        std::unique_lock<std::mutex> lock(data_mutex_);\n        data_cv_.wait(lock, [this]() { return data_collected_; });\n    }\n    stateReporter();\n}\n\n//  ****************** 异步回调函数 ******************\n\n// 创建接收到服务器(平台的交互)回复的异步回调函数\nvoid RobotInfoMgr::plat_srv_callback(rclcpp::Client<homi_speech_interface::srv::SIGCData>::SharedFuture response) // (std::shared_ptr<homi_speech_interface::srv::SIGCData::Request> response)\n{\n    // 使用response的get()获取\n    auto response_value = response.get();\n    int errorCode = response_value->error_code;\n    if (errorCode!=0)\n        RCLCPP_WARN(rclcpp::get_logger(\"robdog_control\"),\"service assistant return error code %d\",errorCode);\n}\n\n// 给平台的回复\nvoid RobotInfoMgr::sendRequestData(const std::string &data)\n{\n    auto request_sigc_data = std::make_shared<homi_speech_interface::srv::SIGCData::Request>();\n    request_sigc_data->data = data;  // 设置请求数据\n    auto ret = platform_client->wait_for_service(std::chrono::seconds(1));\n    if (!ret)\n\t{\n        RCLCPP_WARN(rclcpp::get_logger(\"robdog_control\"), \"Failed to waitForExistence service assistant\");\n    }\n    auto result = platform_client->async_send_request(request_sigc_data, std::bind(&RobotInfoMgr::plat_srv_callback, this, std::placeholders::_1));\n}\n\nvoid RobotInfoMgr::to_json(const DeepRobotState& state, Json::Value& j) {\n    j[\"robot_basic_state\"] = state.robot_basic_state;\n    j[\"robot_gait_state\"] = state.robot_gait_state;\n\n    j[\"rpy\"] = Json::arrayValue;\n    for (double val : state.rpy) {\n        j[\"rpy\"].append(val);\n    }\n\n    j[\"rpy_vel\"] = Json::arrayValue;\n    for (double val : state.rpy_vel) {\n        j[\"rpy_vel\"].append(val);\n    }\n\n    j[\"xyz_acc\"] = Json::arrayValue;\n    for (double val : state.xyz_acc) {\n        j[\"xyz_acc\"].append(val);\n    }\n\n    j[\"pos_world\"] = Json::arrayValue;\n    for (double val : state.pos_world) {\n        j[\"pos_world\"].append(val);\n    }\n\n    j[\"vel_world\"] = Json::arrayValue;\n    for (double val : state.vel_world) {\n        j[\"vel_world\"].append(val);\n    }\n\n    j[\"vel_body\"] = Json::arrayValue;\n    for (double val : state.vel_body) {\n        j[\"vel_body\"].append(val);\n    }\n\n    j[\"touch_down_and_stair_trot\"] = state.touch_down_and_stair_trot;\n    j[\"is_charging\"] = state.is_charging;\n    j[\"error_state\"] = state.error_state;\n    j[\"robot_motion_state\"] = state.robot_motion_state;\n    j[\"battery_level\"] = state.battery_level;\n    j[\"task_state\"] = state.task_state;\n    j[\"is_robot_need_move\"] = state.is_robot_need_move;\n    j[\"zero_position_flag\"] = state.zero_position_flag;\n\n    j[\"ultrasound\"] = Json::arrayValue;\n    for (double val : state.ultrasound) {\n        j[\"ultrasound\"].append(val);\n    }\n    RobotInfoMgr::getInstance().setRobotBasicState(state.robot_basic_state);\n    RobotInfoMgr::getInstance().setRobotGaitState(state.robot_gait_state);\n    RobotInfoMgr::getInstance().setRPY(state.rpy);\n    RobotInfoMgr::getInstance().setRPYVel(state.rpy_vel);\n    RobotInfoMgr::getInstance().setXYZAcc(state.xyz_acc);\n    RobotInfoMgr::getInstance().setPosWorld(state.pos_world);\n    RobotInfoMgr::getInstance().setVelWorld(state.vel_world); \n    RobotInfoMgr::getInstance().setVelBody(state.vel_body);\n    RobotInfoMgr::getInstance().setTouchDownAndStairTrot(state.touch_down_and_stair_trot);\n    RobotInfoMgr::getInstance().setIsCharging(state.is_charging); \n    RobotInfoMgr::getInstance().setErrorState(state.error_state);\n    RobotInfoMgr::getInstance().setRobotMotionState(state.robot_motion_state);\n    RobotInfoMgr::getInstance().setBatteryLevel(state.battery_level);\n    RobotInfoMgr::getInstance().setTaskState(state.task_state);    \n    RobotInfoMgr::getInstance().setIsRobotNeedMove(state.is_robot_need_move);\n    RobotInfoMgr::getInstance().setZeroPositionFlag(state.zero_position_flag);\n    RobotInfoMgr::getInstance().setUltrasound(state.ultrasound);  \n    int robot_basic_state = RobotInfoMgr::getInstance().getRobotBasicState();\n    int robot_gait_state = RobotInfoMgr::getInstance().getRobotGaitState();\n    int robot_motion_state = RobotInfoMgr::getInstance().getRobotMotionState();\n    std::array<double, 2> ultralS=RobotInfoMgr::getInstance().getUltrasound();\n    auto key = std::make_tuple(robot_basic_state, robot_gait_state, robot_motion_state);\n    auto it = stateQueryTableString.find(key);\n    if (it != stateQueryTableString.end() && node_ctrl_) {\n        HomiRobotStatus enLastStatus = RobotInfoMgr::getInstance().getRobotStatus();\n        if(enLastStatus != std::get<1>(it->second)){\n            enLastStatus = std::get<1>(it->second);\n            RobotInfoMgr::getInstance().setRobotStatus(enLastStatus);\n            // node_ctrl_->changeExpression(enLastStatus);\n\n            std::string strDetails = std::get<0>(it->second);\n            node_ctrl_->publishRobdagStateToQt(strDetails);\n            std::cout << \"Current Robot State: \" << strDetails << std::endl;\n        }\n    } else {\n        std::cout << \"Current Robot State: \" << \"Unknown State\" << std::endl;\n        std::cout << \"robot_basic_state: \" << robot_basic_state << std::endl;\n        std::cout << \"robot_gait_state: \" << robot_gait_state << std::endl;\n        std::cout << \"robot_motion_state: \" << robot_motion_state << std::endl;\n    }\n}\n\n// ****************************************************************************\n// 判断函数，接收tuple和指令\nint RobotInfoMgr::checkActionState(const std::string& actionType, const std::string& actionArgument)\n{\n    if (node_ctrl_->robManuModel == ROB_MANU_DEEP_LITE)\n    {\n        auto state = getRobotStatus();\n        if (state == ROBDOG_STATUS_GETDOWN) \n        { \n            // 趴下状态\n            if (actionType == \"motorSkill\" && actionArgument == \"getDown\")\n                return 1;\n        }\n        if (state == ROBDOG_STATUS_FORCE_CTRL_LEVEL_LOWSPPEED_GAIT) \n        {  \n            // 站立状态\n            if (actionType == \"motorSkill\" && actionArgument == \"standUp\")\n                return 1;\n        }\n    }\n    else if (node_ctrl_->robManuModel == ROB_MANU_UNITREE_GO2)\n    {\n        if (true == utStandDownCheck()) \n        { \n            // 趴下状态\n            if (actionType == \"motorSkill\" && actionArgument == \"getDown\")\n                return 1;\n        }\n        if(true == utStandCheck())\n        {\n            // 站立状态\n            if (actionType == \"motorSkill\" && actionArgument == \"standUp\")\n                return 1;\n        }\n    }\n\n    return 0;\n}\n\n//持续发送指令\nvoid RobotInfoMgr::sendrobdogcall() {\n    while (true) {\n        if (node_ctrl_) {\n            node_ctrl_->send_command();\n            std::this_thread::sleep_for(std::chrono::seconds(1));\n            // RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"send robdog state!!!!!!!!!!!!!!!\");\n        }\n    }\n}\n\n//持续发送指令\nvoid RobotInfoMgr::sendrobdogcall_temperature() {\n    while (true) {\n        if (node_ctrl_) {\n            node_ctrl_->send_command_temperature();\n            std::this_thread::sleep_for(std::chrono::seconds(3));\n            // RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"send robdog temperature!!!!!!!!!!!!!!!\");\n        }\n    }\n}\n\nstd::string RobotInfoMgr::get_unitree_temperature_status(const double threshold, const double temp)\n{\n    if (temp >= threshold) return \"TooHigh\";\n    if (temp >= (threshold * 0.9)) return \"High\";\n    if (temp <= TEMP_TOOLOW_THREADHOLD) return \"TooLow\";\n    if (temp <= TEMP_LOW_THREADHOLD)  return \"Low\";\n    return \"Normal\";\n}\n\nstd::string RobotInfoMgr::get_temperature_status()\n{\n    for (double tempJoint : RobotInfoMgr::getInstance().getRobotTemperature()) {\n        if (tempJoint >= 110.0f) return \"TooHigh\";\n        if (tempJoint >= 100.0f) return \"High\";\n        if (tempJoint < 0.0f)    return \"TooLow\";\n        if (tempJoint < 10.0f)   return \"Low\";\n    }\n}\n\nstd::string RobotInfoMgr::get_cpu_temperature_status(const double tempCPU)\n{\n    if (tempCPU >= 65.0f) return \"TooHigh\";\n    if (tempCPU >= 50.0f) return \"High\";\n    if (tempCPU < 0.0f)   return \"TooLow\";\n    if (tempCPU < 10.0f)  return \"Low\";\n    return \"Normal\";\n}\n\n// 持续接收指令\nvoid RobotInfoMgr::receiveAndProcessData() {\n    char packet[4096];\n    size_t packet_size = sizeof(packet);\n    while (true) {\n        if(node_ctrl_) {\n            int result = node_ctrl_->getPacket(packet, packet_size);\n            std::this_thread::sleep_for(std::chrono::milliseconds(20)); // 适当的延时\n            if(!result){\n                // RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"receive robdog state!!!!!!!!!!!!!!!\");\n                // 处理接收到的数据包\n                DeepRobotStateReceived robot_state_received(packet, node_ctrl_);\n                if (robot_state_received.code == 2305) {\n                    //2305为状态包，解析\n                    Json::Value status_json;\n                    to_json(robot_state_received.robot_state, status_json);\n                    double battery_level_tmp = RobotInfoMgr::getInstance().getBatteryLevel();\n                    batteryLevelAlarmHandle(battery_level_tmp);\n                }\n            }\n        }\n    }\n}\n\nvoid RobotInfoMgr::brocast_srv_callback(rclcpp::Client<homi_speech_interface::srv::AssistantSpeechText>::SharedFuture response)\n{\n    // 使用response的get()获取\n    auto response_value = response.get();\n    std::string sectionId  = response_value->section_id;\n    RobotState::getInstance().setSectionId(sectionId);\n    RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Received sectionId from server: %s\", sectionId.c_str());\n\n}\n\n// 调用服务向语音助手发送智能播报文本（收到的sectionId要保存起来）\nvoid RobotInfoMgr::sendStringToBrocast(const std::string &message) \n{\n    RCLCPP_DEBUG(rclcpp::get_logger(\"robdog_control\"), \"Sending to brocast: %s\", message.c_str()); // 播报文本\n\n    // 创建请求消息\n    auto request = std::make_shared<homi_speech_interface::srv::AssistantSpeechText::Request>();\n    request->msg = message;\n\n    // 调用服务并处理响应\n    if (!brocast_client->wait_for_service(std::chrono::seconds(1))) \n    {\n        RCLCPP_ERROR(rclcpp::get_logger(\"robdog_control\"), \"Service not available after waiting\");\n        return;\n    }\n\n    auto ret = brocast_client->wait_for_service(std::chrono::seconds(1));\n    if(ret==false)\n    {\n        RCLCPP_WARN(rclcpp::get_logger(\"robdog_control\"),\"Failed to waitForExistence service assistant\");\n    }\n    auto result = brocast_client->async_send_request(request, std::bind(&RobotInfoMgr::brocast_srv_callback, this, std::placeholders::_1));\n}\n\n// 持续接收指令\nvoid RobotInfoMgr::receiveAndProcessData_temperature() {\n    EthCommand c;\n    CommandMessage cm;\n    // CommandTemp cmd_test;\n    timespec test_time;\n    static uint32_t imu_count = 0;\n\n    char packet[4096];\n    size_t packet_size = sizeof(packet);\n    std::array<double, MAX_JOINT_NUM> temp_array = {};\n    while (true) {\n        if(node_ctrl_) {\n            int result = node_ctrl_->getPacket_temperature(packet, packet_size);\n            std::this_thread::sleep_for(std::chrono::milliseconds(20)); // 适当的延时\n            if(!result){\n                // RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"receive robdog temperature!!!!!!!!!!!!!!!\");\n                // 处理接收到的数据包\n                // std::memcpy(&robot_temperature, packet, sizeof(robot_temperature));\n                // RobotInfoMgr::getInstance().setRobotTemperature(robot_temperature);\n                memcpy(&cm,packet,sizeof(cm));\n                // CommandTemp nc(cm.command.code,cm.command.paramters_size, cm.data_buffer);\n                if(cm.command.type == command_type::CommandType::kMessValues || cm.command.type == command_type::CommandType::kSingleValue){\n                    switch (cm.command.code){\n                        case ROBOT_STATE_CMD:\n                            // clock_gettime(1,&test_time);\n                            // memcpy(&state_rec_, cm.data_buffer, sizeof(state_rec_));\n                            // if (CallBack_) CallBack_(ROBOT_STATE_CMD);\n                            break;\n                        case BATTERY_CODE:\n                            RCLCPP_INFO_THROTTLE(node_ctrl_->get_logger(),*node_ctrl_->get_clock(),3000,\"battery is %f\",cm.command.value);\n\t\t\t                batteryLevelAlarmHandle(cm.command.value);\n                            break;\n                        case CPU_TEMPERATURE_CODE:\n                            RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"receive robdog temperature: %u\", cm.command.value);\n                            \n                            RobotInfoMgr::getInstance().setRobotCPUTemperature(cm.command.value);\n                            RobotInfoMgr::getInstance().setCPUTempStatus(get_cpu_temperature_status(cm.command.value));\n                            CPUOverheatingAlarmHandle(cm.command.value);\n                            break;\n                        case TEMPERATURE_CODE:\n                            // 12个关节的温度，一个腿上三个\n                            float temp[MAX_JOINT_NUM];\n                            memcpy(temp, cm.data_buffer, sizeof(temp));\n                            for(int i = 0 ; i < MAX_JOINT_NUM; i++) {\n                                // printf(\"Joint %d Temp %+3.3f\\r\\n\", i , temp[i]);\n                                RCLCPP_INFO(rclcpp::get_logger(\"robdog_control\"), \"Joint %d Temp %+3.3f\\r\\n\", i , temp[i]);\n                                temp_array[i] = static_cast<double>(temp[i]);\n                                jointOverheatingAlarmHandle(temp_array[i], i);\n                            }\n                            RobotInfoMgr::getInstance().setRobotTemperature(temp_array);\n                            RobotInfoMgr::getInstance().setTempStatus(get_temperature_status());\n                            break;\n                        // case IMU_DATA_CODE:\n                        //     imu_count ++;\n                        //     ImuData imu_data;\n                        //     memcpy(&imu_data, cm.data_buffer, sizeof(imu_data));\n                        //     if(imu_count % 200 == 0){\n                        //         printf(\"angle %+3.3f  %+3.3f  %+3.3f \\r\\n\", imu_data.angle_roll, imu_data.angle_pitch, imu_data.angle_yaw);\n                        //         printf(\"vel   %+3.3f  %+3.3f  %+3.3f \\r\\n\", imu_data.angular_velocity_roll, imu_data.angular_velocity_pitch, imu_data.angular_velocity_yaw);\n                        //         printf(\"accel %+3.3f  %+3.3f  %+3.3f \\r\\n\", imu_data.acc_x, imu_data.acc_y, imu_data.acc_z);\n                        //         std::cout << \"===========\" << imu_data.timestamp << \"=============\" << std::endl;\n                        //     }\n                        //     break;\n\n                        default:\n                            // printf(\"recv code 0x%x\", cm.command.code);\n                            // std::cout << std::endl;\n                            break;\n                    }\n                }\n            }\n        }\n    }\n}\n\nvoid RobotInfoMgr::batteryLevelAlarmHandle(double BatteryLevel) {\n    Json::Value body;\n    int report_level=-1;\n    bool *alarmBattery_p = NULL;\n    static const std::string package_share_dir = ament_index_cpp::get_package_share_directory(\"robdog_control\");\n\n    if(BatteryLevel > BATTERY_LEVEL_3) {\n        /* BatteryLevel>30% : clear all alarm(if alarm triggered) */\n        alarmBattery10Percent = false;\n        alarmBattery20Percent = false;\n        alarmBattery30Percent = false;\n        return;\n    } else if (BatteryLevel > BATTERY_LEVEL_2) {\n        /* BatteryLevel: 20%-30% : clear BATTERY_LEVEL_1 and BATTERY_LEVEL_2 alarm(if alarm triggered), \n         * and send BATTERY_LEVEL_3 alarm\n         * */\n        alarmBattery10Percent = false;\n        alarmBattery20Percent = false;\n        if(alarmBattery30Percent) {\n            return;\n        }\n\treport_level=3;\n        alarmBattery_p = &alarmBattery30Percent;\n    } else if(BatteryLevel > BATTERY_LEVEL_1) {\n        /* BatteryLevel: 10%-20% : clear BATTERY_LEVEL_1 alarm(if alarm triggered), \n         * and send BATTERY_LEVEL_2 alarm\n         * */\n        alarmBattery10Percent = false;\n        if(alarmBattery20Percent) {\n            return;\n        }\n\treport_level=2;\n        alarmBattery_p = &alarmBattery20Percent;\n    } else {\n        /* BatteryLevel: 0%-10%\n         * send BATTERY_LEVEL_1 alarm\n         * */\n        if(alarmBattery10Percent) {\n            return;\n        }\n\treport_level=1;\n        alarmBattery_p = &alarmBattery10Percent;\n    }\n    // need report battery level alarm\n    body[\"alarmCode\"] = BATTERY_ALARM_BASE*100+report_level;\n    body[\"alarmName\"] = \"batteryLevel\"+std::to_string(report_level);\n    body[\"alarmLevel\"] = 2;\n    body[\"alarmType\"] = \"batteryLevel\";\n    body[\"alarmDesc\"] = \"Battery level less than \"+std::to_string(report_level*10);\n    body[\"launcherModel\"] = \"Battery\";\n    body[\"data\"][\"batteryLevel\"] = BatteryLevel;\n\n    Json::StreamWriterBuilder writer;\n    writer[\"commentStyle\"] = \"None\";\n    writer[\"indentation\"] = \"\";\n    std_msgs::msg::String bodyStr;\n    bodyStr.data=Json::writeString(writer, body);\n\n    robotInfoWarnPub_->publish(bodyStr);\n    *alarmBattery_p = true;\n\n    auto pathstr= package_share_dir + \"/resource/audio/BatteryLevelLessThan\" + std::to_string(report_level) + \"0.wav\";\n    std::string command = \"aplay \"+pathstr;\n    RCLCPP_DEBUG(rclcpp::get_logger(\"robdog_control\"), \"play audio : %s\", command.c_str());\n    system(command.c_str());\n    return;\n}\n\nvoid RobotInfoMgr::CPUOverheatingAlarmHandle(uint32_t temp) {\n    Json::Value body;\n    if(temp <= CPU_TEMP_THREADHOLD) {\n        CPUTempPublished = false;\n        return;\n    } else {\n        if(CPUTempPublished) {\n            return;\n        }\n    }\n    \n    body[\"alarmCode\"] = HW_MOTION_CPU_OVERHEATING;\n    body[\"alarmName\"] = \"CPUOverheating\";\n    body[\"alarmLevel\"] = 2;\n    body[\"alarmType\"] = \"CPUOverheating\";\n    body[\"alarmDesc\"] = \"Sport motherboard CPU overheating\";\n    body[\"launcherModel\"] = \"MotionCPU\";\n    body[\"data\"][\"temperature\"] = temp;\n    Json::StreamWriterBuilder writer;\n    writer[\"commentStyle\"] = \"None\";\n    writer[\"indentation\"] = \"\";\n    std_msgs::msg::String bodyStr;\n    bodyStr.data=Json::writeString(writer, body);\n\n    robotInfoWarnPub_->publish(bodyStr);\n    CPUTempPublished = true;\n\n    return;\n}\n\nvoid RobotInfoMgr::jointOverheatingAlarmHandle(double temp, int num) {\n    Json::Value body;\n    std::string H = \"\";\n    std::string L = \"\";\n    int indexPart = num % 3;\n    int indexLocation = num / 3;\n\n    if(temp <= TEMP_THREADHOLD) \n    {\n        JointTempPublished[num] = false;\n        return;\n    } \n    else\n    {\n        if(JointTempPublished[num]) \n        {\n            return;\n        }\n    }\n\n    H = strArrayPart[node_ctrl_->robManuModel][indexPart];\n    L = strArrayLocation[node_ctrl_->robManuModel][indexLocation];\n\n    // 01 02 001 XX 02\n    body[\"alarmCode\"] = (HW_MOTION_JOINT*100+num+1)*100+2;\n    body[\"alarmName\"] = \"JointOverheating\"+std::to_string(num+1);\n    body[\"alarmLevel\"] = 2;\n    body[\"alarmType\"] = \"JointOverheating\"+std::to_string(num+1);\n    std::ostringstream oss;\n    oss << \"Sport motherboard \" << L << H << \"overheating\";\n    body[\"alarmDesc\"] = oss.str();\n    body[\"launcherModel\"] = \"MotionJoint\";\n    body[\"data\"][\"temperature\"] = temp;\n    Json::StreamWriterBuilder writer;\n    writer[\"commentStyle\"] = \"None\";\n    writer[\"indentation\"] = \"\";\n    std_msgs::msg::String bodyStr;\n    bodyStr.data=Json::writeString(writer, body);\n\n    std::this_thread::sleep_for(std::chrono::milliseconds(1));\n    robotInfoWarnPub_->publish(bodyStr);\n    JointTempPublished[num] = true;\n\n    return;\n}\n\n/*\n运动模式\n0. idle, default stand\n1. balanceStand\n2. pose\n3. locomotion\n4. reserve\n5. lieDown\n6. jointLock\n7. damping\n8. recoveryStand\n9. reserve\n10. sit\n11. frontFlip\n12. frontJump\n13. frontPounc\n*/\n\nbool sport_state_flag = false;\nbool low_state_flag = false;\nbool lidar_flag = false;\nunitree_go::msg::dds_::SportModeState_ sport_state;\nunitree_go::msg::dds_::LowState_ low_state;\nuint32_t JointHeatFlag = 0;\nunitree_go::msg::dds_::LidarState_ lidar_state;\n\nstd::map<UtStateKey, UtStateValue> utStateQueryTableString = \n{\n    {{UT_SPORT_ERROR_CODE_FREE_WALK},        {\"灵动模式\", UT_ROBDOG_STATUS_FREE_WALK}},\n    {{UT_SPORT_ERROR_CODE_DAMPING},          {\"阻尼状态\", UT_ROBDOG_STATUS_DAMPING}},\n    {{UT_SPORT_ERROR_CODE_LOCK_STAND},       {\"站⽴锁定状态\", UT_ROBDOG_STATUS_LOCK_STAND}},\n    {{UT_SPORT_ERROR_CODE_SQUAT},            {\"蹲下状态\", UT_ROBDOG_STATUS_SQUAT}},\n    {{UT_SPORT_ERROR_CODE_SQUAT_2},          {\"蹲下状态\", UT_ROBDOG_STATUS_SQUAT}},\n    {{UT_SPORT_ERROR_CODE_LOCOMOTION},       {\"做动作(打招呼/伸懒腰/舞蹈/拜年/⽐心/开心)\", UT_ROBDOG_STATUS_LOCOMOTION}},\n    {{UT_SPORT_ERROR_CODE_SIT},              {\"坐下状态\", UT_ROBDOG_STATUS_SIT}},\n    {{UT_SPORT_ERROR_CODE_FRONT_JUMP},       {\"前跳状态\", UT_ROBDOG_STATUS_FRONT_JUMP}},\n    {{UT_SPORT_ERROR_CODE_FRONT_POUNCE},     {\"扑人状态\", UT_ROBDOG_STATUS_FRONT_POUNCE}},\n    {{UT_SPORT_ERROR_CODE_BALANCE_STAND},    {\"平衡站立\", UT_ROBDOG_STATUS_BALANCE_STAND}},\n    {{UT_SPORT_ERROR_CODE_STATIC_WALK},      {\"常规行走状态\", UT_ROBDOG_STATUS_STATIC_WALK}},\n    {{UT_SPORT_ERROR_CODE_TROT_RUN},         {\"常规跑步状态\", UT_ROBDOG_STATUS_TROT_RUN}},\n    {{UT_SPORT_ERROR_CODE_ECONOMIC_GAIT},    {\"常规续航状态\", UT_ROBDOG_STATUS_ECONOMIC_GAIT}},\n    {{UT_SPORT_ERROR_CODE_POSE},             {\"摆姿势状态\", UT_ROBDOG_STATUS_POSE}},\n    {{UT_SPORT_ERROR_CODE_RECOVERYSTANDING}, {\"正在恢复站立\", UT_ROBDOG_STATUS_RECOVERYSTANDING}},\n    {{UT_SPORT_ERROR_CODE_FREE_AVOID},       {\"闪避状态\", UT_ROBDOG_STATUS_FREE_AVOID}},\n    {{UT_SPORT_ERROR_CODE_FREE_BOUND},       {\"并腿跑状态\", UT_ROBDOG_STATUS_FREE_BOUND}},\n    {{UT_SPORT_ERROR_CODE_FREE_JUMP},        {\"跳跃跑状态\", UT_ROBDOG_STATUS_FREE_JUMP}},\n    {{UT_SPORT_ERROR_CODE_CLASSIC},          {\"经典状态\", UT_ROBDOG_STATUS_CLASSIC}},\n    {{UT_SPORT_ERROR_CODE_HAND_STAND},       {\"倒立状态\", UT_ROBDOG_STATUS_HAND_STAND}},\n    {{UT_SPORT_ERROR_CODE_FRONT_FLIP},       {\"前空翻状态\", UT_ROBDOG_STATUS_FRONT_FLIP}},\n    {{UT_SPORT_ERROR_CODE_BACK_FLIP},        {\"后空翻状态\", UT_ROBDOG_STATUS_BACK_FLIP}},\n    {{UT_SPORT_ERROR_CODE_LEFT_FLIP},        {\"左空翻状态\", UT_ROBDOG_STATUS_LEFT_FLIP}},\n    {{UT_SPORT_ERROR_CODE_CROSS_STEP},       {\"交叉步状态\", UT_ROBDOG_STATUS_CROSS_STEP}},\n    {{UT_SPORT_ERROR_CODE_WALK_UPRIGHT},     {\"直立状态\", UT_ROBDOG_STATUS_WALK_UPRIGHT}},\n    {{UT_SPORT_ERROR_CODE_PULL},             {\"牵引状态\", UT_ROBDOG_STATUS_PULL}}\n};\n\nusing UtMotorErrorKey = std::tuple<int32_t>;\nusing UtMotorErrorValue = std::tuple<std::string>;\nstatic std::map<UtMotorErrorKey, UtMotorErrorValue> utMotorErrorTableString = \n{\n    {{0x01}, {\"过流\"}},\n    {{0x02}, {\"过压\"}},\n    {{0x04}, {\"驱动过热\"}},\n    {{0x08}, {\"母线欠压\"}},\n    {{0x10}, {\"绕组过热\"}},\n    {{0x20}, {\"编码器异常\"}},\n    {{0x100}, {\"电机通信中断\"}}\n};\n\nvoid RobotInfoMgr::utMotorAlarmHandle(std::array<uint32_t, 2> &reserve, int num) \n{\n    int32_t newCode = reserve[0];\n    int32_t oldCode = MotorErrorPublished[num];\n    Json::Value body;\n    std::string H = \"\";\n    std::string L = \"\";\n    std::string Desc = \"\";\n    int indexPart = num % 3;\n    int indexLocation = num / 3;\n\n    /* 更新数据 */\n    MotorErrorPublished[num] = newCode;\n\n    /* 判断是否需告警 */\n    if(oldCode == newCode || 0 == newCode) \n    {\n        return;\n    } \n    \n    H = strArrayPart[node_ctrl_->robManuModel][indexPart];\n    L = strArrayLocation[node_ctrl_->robManuModel][indexLocation];\n    auto key = std::make_tuple(newCode);\n    auto it = utMotorErrorTableString.find(key);\n    if (it != utMotorErrorTableString.end() && node_ctrl_) \n    {\n        Desc = std::get<0>(it->second);\n    }\n\n    // 01 02 001 XX 03\n    body[\"alarmCode\"] = (HW_MOTION_JOINT * 100 + num + 1) * 100 + 3;\n    body[\"alarmName\"] = \"MotorError\" + std::to_string(num + 1);\n    body[\"alarmLevel\"] = 2;\n    body[\"alarmType\"] = \"MotorError\" + std::to_string(num + 1);\n    std::ostringstream oss;\n    oss << \"Sport motherboard \" << L << H << \"MotorError：\" << Desc;\n    body[\"alarmDesc\"] = oss.str();\n    body[\"launcherModel\"] = \"Motor\";\n    body[\"data\"][\"code\"] = newCode;\n    Json::StreamWriterBuilder writer;\n    writer[\"commentStyle\"] = \"None\";\n    writer[\"indentation\"] = \"\";\n    std_msgs::msg::String bodyStr;\n    bodyStr.data=Json::writeString(writer, body);\n\n    std::this_thread::sleep_for(std::chrono::milliseconds(1));\n    robotInfoWarnPub_->publish(bodyStr);\n\n    return;\n}\n\n// 向平台上传智能播报状态，被唤醒词打断了需上报\nvoid RobotInfoMgr::utRobotBroadcastStatusToPlat(std::string device)\n{\n    char text[256] = {0};\n    std::string highTempStatus;\n    std::string lowTempStatus;\n    std::string tempStatus;\n    double Temp = -100;\n    double highTemp = -100;\n    double lowTemp = 100;\n    float threshold = 0.0;\n\n    // 更新温度状态信息\n    if (device == \"CPU\") \n    {\n        Temp = getRobotCPUTemperature();\n        tempStatus = get_unitree_temperature_status(CPU_TEMP_THREADHOLD, Temp);\n        setCPUTempStatus(tempStatus);\n        threshold = CPU_TEMP_THREADHOLD;\n    }\n    else \n    {\n        // 获取关节最高温度和最低温度\n        auto jointTemp = getRobotTemperature();\n        for (double tempJoint : jointTemp) \n        {\n            if (tempJoint > highTemp)\n            {\n                highTemp = tempJoint;\n            }\n\n            if (tempJoint < lowTemp)\n            {\n                lowTemp = tempJoint;\n            }\n        }\n\n        // 检测温度是否正常\n        highTempStatus = get_unitree_temperature_status(TEMP_THREADHOLD, highTemp);\n        lowTempStatus = get_unitree_temperature_status(TEMP_THREADHOLD, lowTemp);\n        if ((highTempStatus == \"Normal\") && (lowTempStatus == \"Normal\"))\n        {\n            tempStatus = \"Normal\";\n        }\n        else\n        {\n            if (highTempStatus != \"Normal\")\n            {\n                Temp = highTemp;\n                tempStatus = highTempStatus;\n            }\n            else\n            {\n                Temp = lowTemp;\n                tempStatus = lowTempStatus;\n            }\n        }\n\n        setTempStatus(tempStatus);\n        threshold = TEMP_THREADHOLD;\n    }\n\n    // 温度正常不播报，距离上次播报60s内不播报\n    if ((tempStatus == \"Normal\") || ((node_ctrl_->now() - lastBroadcastTime).seconds() < 60.0))\n    {\n        return;\n    }\n\n    RCLCPP_INFO(node_ctrl_->get_logger(), \"##### Temp Status Abnormal: %s #####\", tempStatus.c_str());\n\n    //检查是否提醒用户移动至安全区域\n    if (Temp >= threshold * 0.95 && Temp < threshold)\n    {\n        tempStatus = \"High_more\";\n    }\n    else if (Temp > TEMP_TOOLOW_THREADHOLD && Temp <= TEMP_LOW_THREADHOLD * 0.5)\n    {\n        tempStatus = \"Low_more\";\n    }\n\n    // 调用语音播报\n    std::string message = \"\";\n    snprintf(text, sizeof(text), brocastText.at(device).at(tempStatus).c_str(), Temp);\n    lastBroadcastTime = node_ctrl_->now();\n    message = text;\n    sendStringToBrocast(message);\n\n    // 温度异常趴下\n    utAbnormalStandDown();\n}\n\n// 检查温度是否异常\nbool RobotInfoMgr::utCheckTempStatus()\n{\n    std::string currStatus;\n    std::string tempStatus;\n\n    // cpu温度异常\n    currStatus = getCPUTempStatus();\n    if ((tempStatus == \"TooHigh\") || (tempStatus == \"TooLow\"))\n    {\n        return true;\n    }\n\n    // 关节温度异常\n    tempStatus = getTempStatus();\n    if ((tempStatus == \"TooHigh\") || (tempStatus == \"TooLow\"))\n    {\n        return true;\n    }\n\n    return false;\n}\n\n// 温度异常趴下\nvoid RobotInfoMgr::utAbnormalStandDown()\n{\n    HomiUtRobotStatus enLastStatus = UT_ROBDOG_STATUS_STATE;\n\n    // 非过热或过冷状态\n    if (utCheckTempStatus() == false)\n    {\n        return;\n    }\n\n    // 趴下状态无需处理\n    enLastStatus = getUtRobotStatus();\n    if (enLastStatus == UT_ROBDOG_STATUS_DAMPING || enLastStatus == UT_ROBDOG_STATUS_SQUAT)\n    {\n        return;\n    }\n\n    // 站立状态趴下\n    if (true == utStandCheck())\n    {\n        // 静止站立状态，趴下\n        RCLCPP_INFO(node_ctrl_->get_logger(),  \"Joint Over Heat!\");\n        node_ctrl_->robdogCtrlDev->robdogCtrl_GetDown();\n        \n        // 等待狗子趴下\n        std::this_thread::sleep_for(std::chrono::milliseconds(3000));\n    }\n\n    return;\n}\n\n// 等待与运控板通信正常\nbool RobotInfoMgr::utConnectWait()\n{\n    int cnt = 0;\n\n    while ((getUtRobotStatus() == UT_ROBDOG_STATUS_STATE) && (cnt < 60))\n    {\n        std::this_thread::sleep_for(std::chrono::milliseconds(1000));\n        cnt++;\n        RCLCPP_INFO(node_ctrl_->get_logger(), \"Wait Connect: %d\", cnt);\n    }\n\n    if (cnt == 60)\n    {\n        RCLCPP_INFO(node_ctrl_->get_logger(), \"Sport Control Board Connection Error.\");\n        return false;\n    }\n\n    RCLCPP_INFO_THROTTLE(node_ctrl_->get_logger(), *node_ctrl_->get_clock(), 3000, \"Sport Control Board Connected.\");\n\n    return true;\n}\n\n// 检测是否静止站立状态\nbool RobotInfoMgr::utStandCheck()\n{\n    unitree_go::msg::dds_::SportModeState_ utSportState;\n    float absNum = 0;\n    uint32_t error_code = 0;\n\n    utGetHighState(utSportState);\n    error_code = utSportState.error_code();\n    if (error_code == UT_SPORT_ERROR_CODE_FREE_WALK || error_code == UT_SPORT_ERROR_CODE_CLASSIC ||\n        error_code == UT_SPORT_ERROR_CODE_FREE_BOUND || error_code == UT_SPORT_ERROR_CODE_FREE_JUMP)\n    {\n        // 三维速度,绝对值小于0.15认为静止\n        for (float elem : utSportState.velocity()) \n        {\n            absNum = std::fabs(elem);\n            if (absNum > 0.15)\n            {\n                RCLCPP_INFO(node_ctrl_->get_logger(), \"AI Mode, Not Stand Up Status (X,Y).\");\n                return false;\n            }\n        }\n\n        //偏航速度,绝对值小于0.15认为静止\n        absNum = std::fabs(utSportState.yaw_speed());\n        if (absNum > 0.15)\n        {\n            RCLCPP_INFO(node_ctrl_->get_logger(), \"AI Mode, Not Stand Up Status (Yaw).\");\n            return false;\n        }\n    }\n    else if (error_code != UT_SPORT_ERROR_CODE_BALANCE_STAND)\n    {\n        RCLCPP_INFO(node_ctrl_->get_logger(), \"Normal Mode, Not Stand Up Status. code=%d\", error_code);\n        return false;\n    }\n\n    RCLCPP_INFO(node_ctrl_->get_logger(), \"Stand Up Status. Not Moving!\");\n\n    return true;\n}\n\n// 检测是否趴下状态\nbool RobotInfoMgr::utStandDownCheck()\n{\n    HomiUtRobotStatus enLastStatus = UT_ROBDOG_STATUS_STATE;\n    int ret = 0;\n\n    enLastStatus = getUtRobotStatus();\n    if (enLastStatus == UT_ROBDOG_STATUS_DAMPING || enLastStatus == UT_ROBDOG_STATUS_SQUAT)\n    {\n        RCLCPP_INFO(node_ctrl_->get_logger(), \"Stand Down Status.\");\n        return true;\n    }\n\n    RCLCPP_INFO(node_ctrl_->get_logger(), \"Not Stand Down Status.\");\n\n    return false;\n}\n\n// 检测是否坐下状态\nbool RobotInfoMgr::utSitCheck()\n{\n    HomiUtRobotStatus enLastStatus = UT_ROBDOG_STATUS_STATE;\n    int ret = 0;\n\n    enLastStatus = getUtRobotStatus();\n    if (enLastStatus == UT_ROBDOG_STATUS_SIT)\n    {\n        RCLCPP_INFO(node_ctrl_->get_logger(), \"Sit Status.\");\n        return true;\n    }\n\n    RCLCPP_INFO(node_ctrl_->get_logger(), \"Not Sit Status.\");\n\n    return false;\n}\n\n// 检测是否在执行动作\nbool RobotInfoMgr::utLocomotionCheck()\n{\n    HomiUtRobotStatus enLastStatus = UT_ROBDOG_STATUS_STATE;\n    int ret = 0;\n\n    enLastStatus = getUtRobotStatus();\n    if (enLastStatus == UT_ROBDOG_STATUS_LOCOMOTION)\n    {\n        RCLCPP_INFO(node_ctrl_->get_logger(), \"Locomotion Status.\");\n        return true;\n    }\n    else if (enLastStatus == UT_ROBDOG_STATUS_HAND_STAND || enLastStatus == UT_ROBDOG_STATUS_WALK_UPRIGHT)\n    {\n        RCLCPP_INFO(node_ctrl_->get_logger(), \"HandStand or WalkUpright Status.\");\n        return true;\n    }\n    else if (enLastStatus == UT_ROBDOG_STATUS_FRONT_JUMP || enLastStatus == UT_ROBDOG_STATUS_FRONT_POUNCE)\n    {\n        RCLCPP_INFO(node_ctrl_->get_logger(), \"Front jump or Front pounce Status.\");\n        return true;\n    }\n    \n    RCLCPP_INFO(node_ctrl_->get_logger(), \"Not Locomotion Status.\");\n\n    return false;\n}\n\n// 检测是否运动状态\nbool RobotInfoMgr::utMoveCheck()\n{\n    unitree_go::msg::dds_::SportModeState_ utSportState;\n    float absNum = 0;\n    uint32_t error_code = 0;\n\n    utGetHighState(utSportState);\n    error_code = utSportState.error_code();\n    if (error_code == UT_SPORT_ERROR_CODE_FREE_WALK || error_code == UT_SPORT_ERROR_CODE_CLASSIC ||\n        error_code == UT_SPORT_ERROR_CODE_FREE_BOUND || error_code == UT_SPORT_ERROR_CODE_FREE_JUMP)\n    {\n        // 三维速度,绝对值小于0.15认为静止\n        for (float elem : utSportState.velocity()) \n        {\n            absNum = std::fabs(elem);\n            if (absNum > 0.15)\n            {\n                RCLCPP_INFO(node_ctrl_->get_logger(), \"AI Mode, Moving Status (x,y).\");\n                return true;\n            }\n        }\n\n        //偏航速度,绝对值小于0.15认为静止\n        absNum = std::fabs(utSportState.yaw_speed());\n        if (absNum > 0.15)\n        {\n            RCLCPP_INFO(node_ctrl_->get_logger(), \"AI Mode, Moving Status (yaw).\");\n            return true;\n        }\n    }\n    else if (error_code == UT_SPORT_ERROR_CODE_STATIC_WALK || error_code == UT_SPORT_ERROR_CODE_TROT_RUN ||\n             error_code == UT_SPORT_ERROR_CODE_HAND_STAND || error_code == UT_SPORT_ERROR_CODE_WALK_UPRIGHT)\n    {\n        RCLCPP_INFO(node_ctrl_->get_logger(), \"Walk, run, hand stand or walk upright!\");\n        return true;\n    }\n\n    RCLCPP_INFO(node_ctrl_->get_logger(), \"Not Moving.\");\n\n    return false;\n}\n\n// 获取高层状态\nvoid RobotInfoMgr::utGetHighState(unitree_go::msg::dds_::SportModeState_ &sport_state_info)\n{\n    sport_state_info = sport_state;\n\n    return;\n}\n\n// 获取低层状态\nvoid RobotInfoMgr::utGetLowState(unitree_go::msg::dds_::LowState_ &low_state_info)\n{\n    low_state_info = low_state;\n\n    return;\n}\n\n// 获取雷达状态\nvoid RobotInfoMgr::utGetLidarState(unitree_go::msg::dds_::LidarState_ &lidar_state_info)\n{\n    lidar_state_info = lidar_state;\n\n    return;\n}\n\n/* 处理状态信息 */\nvoid RobotInfoMgr::utHighStateHandler(const void *message)\n{\n    if (message == nullptr)\n    {\n        RCLCPP_ERROR(node_ctrl_->get_logger(),  \"utHighStateHandler Null Pointer!\");\n        return;\n    }\n\n    sport_state = *(unitree_go::msg::dds_::SportModeState_ *)message;\n    sport_state_flag = true;\n\n    return;\n}\n\n/* 处理状态信息 */\nvoid RobotInfoMgr::utLowStateHandler(const void *message)\n{\n    if (message == nullptr)\n    {\n        RCLCPP_ERROR(node_ctrl_->get_logger(),  \"utLowStateHandler Null Pointer!\");\n        return;\n    }\n\n    low_state = *(unitree_go::msg::dds_::LowState_ *)message;\n    low_state_flag = true;\n\n    return;\n}\n\n/* 处理雷达状态信息 */\nvoid RobotInfoMgr::utLidarStateHandler(const void *message)\n{\n    if (message == nullptr)\n    {\n        RCLCPP_ERROR(node_ctrl_->get_logger(),  \"utLidarStateHandler Null Pointer!\");\n        return;\n    }\n\n    lidar_state = *(unitree_go::msg::dds_::LidarState_ *)message;\n    lidar_flag = true;\n\n    return;\n}\n\nvoid RobotInfoMgr::utCloudHandler(const void *message)\n{\n  const sensor_msgs::msg::dds_::PointCloud2_ *cloud_msg = (const sensor_msgs::msg::dds_::PointCloud2_ *)message;\n\n  #if 0\n  std::cout << \"Received a raw cloud here!\"\n            << \"\\n\\tstamp = \" << cloud_msg->header().stamp().sec() << \".\" << cloud_msg->header().stamp().nanosec()\n            << \"\\n\\tframe = \" << cloud_msg->header().frame_id()\n            << \"\\n\\tpoints number = \" << cloud_msg->width()\n            << std::endl\n            << std::endl;\n  #endif\n}\n\nvoid RobotInfoMgr::setHeadFanSpeed()\n{\n    double CPUTemp = RobotInfoMgr::getInstance().getRobotCPUTemperature();\n    if (CPUTemp >= CPU_TEMP_THREADHOLD && current_fan_speed != max_fan_speed)\n    {\n        node_ctrl_->peripherals_set_fan_speed(max_fan_speed);\n        current_fan_speed = max_fan_speed;\n    }\n    else if (CPUTemp < CPU_TEMP_THREADHOLD\n          && CPUTemp >= CPU_TEMP_THREADHOLD * 0.8\n          && current_fan_speed != min_fan_speed)\n    {\n        node_ctrl_->peripherals_set_fan_speed(min_fan_speed);\n        current_fan_speed = min_fan_speed;\n    }\n    else if  (current_fan_speed != min_fan_speed && CPUTemp < CPU_TEMP_THREADHOLD * 0.8)\n    {\n        node_ctrl_->peripherals_set_fan_speed(min_fan_speed);\n        current_fan_speed = min_fan_speed;\n    }\n}\n\nvoid RobotInfoMgr::utCPUTemperatureSet() \n{\n    /* 更新状态信息 */\n    double CPUTemp;\n    CPUTemp = getCpuTemperature();\n\n    setRobotCPUTemperature(CPUTemp);\n    utRobotBroadcastStatusToPlat(\"CPU\");\n    CPUOverheatingAlarmHandle(CPUTemp);\n    setHeadFanSpeed();\n}\n\nvoid RobotInfoMgr::utSportStateSet() \n{\n    unitree_go::msg::dds_::SportModeState_ utSportState;\n    std::array<float, 3> rpy = {};\n    std::array<float, 3> gyroscope = {};\n    std::array<float, 3> accelerometer = {};\n    std::array<float, 3> position = {};\n    std::array<float, 3> velocity = {};\n    std::array<double, 3> rpy_d = {};\n    std::array<double, 3> gyroscope_d = {};\n    std::array<double, 3> accelerometer_d = {};\n    std::array<double, 3> position_d = {};\n    std::array<double, 3> velocity_d = {};\n\n    /* 获取状态信息 */\n    utGetHighState(utSportState);\n\n    /* 设置运动状态信息 */\n    if (sport_state_flag == true)\n    {\n        /* 类型转换 */\n        rpy = utSportState.imu_state().rpy();\n        gyroscope = utSportState.imu_state().gyroscope();\n        accelerometer = utSportState.imu_state().accelerometer();\n        position = utSportState.position();\n        velocity = utSportState.velocity();\n        std::transform(rpy.begin(), rpy.end(), rpy_d.begin(), [](float f) { return static_cast<double>(f); });\n        std::transform(gyroscope.begin(), gyroscope.end(), gyroscope_d.begin(), [](float f) { return static_cast<double>(f); });\n        std::transform(accelerometer.begin(), accelerometer.end(), accelerometer_d.begin(), [](float f) { return static_cast<double>(f); });\n        std::transform(position.begin(), position.end(), position_d.begin(), [](float f) { return static_cast<double>(f); });\n        std::transform(velocity.begin(), velocity.end(), velocity_d.begin(), [](float f) { return static_cast<double>(f); });\n\n        /* 先按照宇树实现来赋值 */\n        setRPY(rpy_d);\n        setRPYVel(gyroscope_d);\n        setXYZAcc(accelerometer_d);\n        setPosWorld(position_d);\n        setVelWorld(velocity_d); \n        setErrorState(utSportState.error_code());\n        setRobotBasicState(utSportState.error_code());\n        setRobotGaitState(utSportState.gait_type());\n        setRobotMotionState(utSportState.progress());\n\n        #if 0\n        /* 无下列信息，且暂时用不到 */\n        setVelBody(state.vel_body);\n        setTouchDownAndStairTrot(state.touch_down_and_stair_trot);\n        setTaskState(state.task_state);    \n        setIsRobotNeedMove(state.is_robot_need_move);\n        setZeroPositionFlag(state.zero_position_flag);\n        setUltrasound(state.ultrasound);  \n        #endif\n\n        /* 设置机器狗状态，状态值先和云深处区分开 */\n        int robot_basic_state = getRobotBasicState();\n        //int robot_gait_state = getRobotGaitState();\n        //int robot_motion_state = getRobotMotionState();\n        //auto key = std::make_tuple(robot_basic_state, robot_gait_state, robot_motion_state);\n        auto key = std::make_tuple(robot_basic_state);\n        auto it = utStateQueryTableString.find(key);\n        if (it != utStateQueryTableString.end() && node_ctrl_) \n        {\n            HomiUtRobotStatus enLastStatus = getUtRobotStatus();\n            if(enLastStatus != std::get<1>(it->second))\n            {\n                enLastStatus = std::get<1>(it->second);\n                setUtRobotStatus(enLastStatus);\n                // node_ctrl_->changeExpression(enLastStatus);\n\n                std::string strDetails = std::get<0>(it->second);\n                node_ctrl_->publishRobdagStateToQt(strDetails);\n                std::cout << \"Current Robot State: \" << strDetails << std::endl;\n                RCLCPP_INFO(node_ctrl_->get_logger(),  \"##### Current Robot State Value: %d #####\", robot_basic_state);\n            }\n        }\n        else\n        {\n            std::cout << \"Current Robot State: \" << \"Unknown State\" << std::endl;\n            std::cout << \"robot_basic_state: \" << robot_basic_state << std::endl;\n            RCLCPP_INFO(node_ctrl_->get_logger(),  \"##### Current Robot State Value: %d #####\", robot_basic_state);\n            //std::cout << \"robot_gait_state: \" << robot_gait_state << std::endl;\n            //std::cout << \"robot_motion_state: \" << robot_motion_state << std::endl;\n        }\n    }\n\n    return;\n}\n\nvoid RobotInfoMgr::utLowStateSet() \n{\n    unitree_go::msg::dds_::LowState_ utLowState;\n    int index = 0;\n    std::array<double, MAX_JOINT_NUM> temperature = {};\n    std::array<uint32_t, 2> reserve = {};\n\n    /* 获取状态信息 */\n    utGetLowState(utLowState);\n\n    /* 设置低层状态信息 */\n    if (low_state_flag == true)\n    {\n        /* 设置电池电量，告警检测\n           云深处定义：0未在充电，1充电中 2 充满\n           宇树定义：正代表充电，负代表放电，与云深处定义不同，需转化\n        */\n        setBatteryLevel(utLowState.bms_state().soc());\n        batteryLevelAlarmHandle(utLowState.bms_state().soc());\n\n        /* 设置充电状态信息 */\n        if (utLowState.bms_state().current() > 0)\n        {\n            setIsCharging(1);\n        }\n        else\n        {\n            setIsCharging(0);\n        }\n\n        //设置关节温度信息，告警检测\n        for(index = 0 ; index < MAX_JOINT_NUM; index++) \n        {\n            temperature[index] = utLowState.motor_state()[index].temperature();\n            jointOverheatingAlarmHandle(temperature[index], index);\n\n            reserve = utLowState.motor_state()[index].reserve();\n            utMotorAlarmHandle(reserve, index);\n        }\n        setRobotTemperature(temperature);\n        utRobotBroadcastStatusToPlat(\"joint\");\n    }\n\n    return;\n}\n\n/* 机器人全量高层状态信息 */\nvoid RobotInfoMgr::utHighStateDump() \n{\n    unitree_go::msg::dds_::SportModeState_ utSportState;\n\n    /* 获取状态信息 */\n    utGetHighState(utSportState);\n\n    std::cout << \"---------------------------- high state ----------------------------\" << std::endl;\n\n    // 时间戳\n    std::cout << \"stamp: \" << utSportState.stamp().sec() << std::endl;\n\n    // 错误代码\n    std::cout << \"error_code: \" << utSportState.error_code() << std::endl;\n\n    // IMU状态, 四元数 (w,x,y,z)\n    std::cout << \"imu_state.quaternion: \";\n    for (float elem : utSportState.imu_state().quaternion()) \n    {\n        std::cout << elem << \" \";\n    }\n    std::cout << std::endl;\n\n    // IMU状态, 角速度（unit: rad/s)\n    std::cout << \"imu_state.gyroscope: \";\n    for (float elem : utSportState.imu_state().gyroscope()) \n    {\n        std::cout << elem << \" \";\n    }\n    std::cout << std::endl;\n\n    // IMU状态, 加速度 m/(s2)\n    std::cout << \"imu_state.accelerometer: \";\n    for (float elem : utSportState.imu_state().accelerometer()) \n    {\n        std::cout << elem << \" \";\n    }\n    std::cout << std::endl;\n\n    // IMU状态, 欧拉角（unit: rad)\n    std::cout << \"imu_state.rpy: \";\n    for (float elem : utSportState.imu_state().rpy()) \n    {\n        std::cout << elem << \" \";\n    }\n    std::cout << std::endl;\n\n    // IMU状态, 温度\n    std::cout << \"imu_state.temperature: \" << static_cast<int>(utSportState.imu_state().temperature()) << std::endl;\n\n    /*\n    运动模式：\n    0. idle, default stand\n    1. balanceStand\n    2. pose\n    3. locomotion\n    4. reserve\n    5. lieDown\n    6. jointLock\n    7. damping\n    8. recoveryStand\n    9. reserve\n    10. sit\n    11. frontFlip\n    12. frontJump\n    13. frontPounc\n    */\n    std::cout << \"mode: \" << static_cast<int>(utSportState.mode()) << std::endl;\n\n    /*\n    是否动作执行状态\n    0. dance false;\n    1. dance true\n    */\n    std::cout << \"progress: \" << utSportState.progress() << std::endl;\n\n    /*\n    0.idle  \n    1.trot\n    2.run  \n    3.climb stair  \n    4.forwardDownStair   \n    9.adjust\n    */\n    std::cout << \"gait_type: \" << static_cast<int>(utSportState.gait_type()) << std::endl;\n\n    // 抬腿高度\n    std::cout << \"foot_raise_height: \" << utSportState.foot_raise_height() << std::endl;\n\n    // 三维位置\n    std::cout << \"position: \";\n    for (float elem : utSportState.position()) \n    {\n        std::cout << elem << \" \";\n    }\n    std::cout << std::endl;\n\n    // 机体高度\n    std::cout << \"body_height: \" << utSportState.body_height() << std::endl;\n\n    // 三维速度\n    std::cout << \"velocity: \";\n    for (float elem : utSportState.velocity()) \n    {\n        std::cout << elem << \" \";\n    }\n    std::cout << std::endl;\n\n    //偏航速度\n    std::cout << \"yaw_speed: \" << utSportState.yaw_speed() << std::endl;\n\n    // 障碍物距离\n    std::cout << \"range_obstacle: \";\n    for (float elem : utSportState.range_obstacle()) \n    {\n        std::cout << elem << \" \";\n    }\n    std::cout << std::endl;\n\n    // 四个足端力\n    std::cout << \"foot_force: \";\n    for (int16_t elem : utSportState.foot_force()) \n    {\n        std::cout << elem << \" \";\n    }\n    std::cout << std::endl;\n\n    // 足端相对于机体的位置\n    std::cout << \"foot_position_body: \";\n    for (float elem : utSportState.foot_position_body()) \n    {\n        std::cout << elem << \" \";\n    }\n    std::cout << std::endl;    \n\n    // 足端相对与机体的速度\n    std::cout << \"foot_speed_body: \";\n    for (float elem : utSportState.foot_speed_body()) \n    {\n        std::cout << elem << \" \";\n    }\n    std::cout << std::endl;\n\n    // 当前跟踪的路径点\n    std::cout << \"path_point: \" << std::endl;\n    for (unitree_go::msg::dds_::PathPoint_ elem : utSportState.path_point()) \n    {\n        std::cout << \"    t_from_start: \" << elem.t_from_start();     // 路径点所处时刻\n        std::cout << \"    x: \" << elem.x();                           // x位置\n        std::cout << \"    y: \" << elem.y();                           // y位置\n        std::cout << \"    yaw: \" << elem.yaw() ;                      // 偏航角\n        std::cout << \"    vx: \" << elem.vx();                         // x速度 \n        std::cout << \"    vy: \" << elem.vy();                         // y速度\n        std::cout << \"    vyaw: \" << elem.vyaw();                     // 偏航速度\n        std::cout << std::endl;\n    }\n\n    std::cout << \"----------------------------- end ---------------------------\" << std::endl;\n    \n    return;\n}\n\n/* 机器人全量低层状态信息 */\nvoid RobotInfoMgr::utLowStateDump() \n{\n    unitree_go::msg::dds_::LowState_ utLowState;\n\n    /* 获取状态信息 */\n    utGetLowState(utLowState);\n\n    std::cout << \"---------------------------- low state ----------------------------\" << std::endl;\n\n    // 帧头，数据校验用（0xFE,0xEF）。\n    std::cout << \"head: \";\n    for (uint8_t elem : utLowState.head()) \n    {\n        std::cout << static_cast<int>(elem) << \" \";\n    }\n    std::cout << std::endl;    \n\n    //沿用的，但是目前不用。\n    std::cout << \"level_flag: \" << static_cast<int>(utLowState.level_flag()) << std::endl;\n\n    //沿用的，但是目前不用。\n    std::cout << \"frame_reserve: \" << static_cast<int>(utLowState.frame_reserve()) << std::endl;\n\n    //已经改为文件存储形式，目前没用。\n    std::cout << \"sn: \";\n    for (uint32_t elem : utLowState.sn()) \n    {\n        std::cout << elem << \" \";\n    }\n    std::cout << std::endl;    \n\n    //沿用的，但是目前不用。\n    std::cout << \"version: \";\n    for (uint32_t elem : utLowState.version()) \n    {\n        std::cout << elem << \" \";\n    }\n    std::cout << std::endl;  \n\n    //沿用的，但是目前不用。\n    std::cout << \"bandwidth: \" << utLowState.bandwidth() << std::endl;\n\n    //IMU数据信息。四元数数据\n    std::cout << \"imu_state.quaternion: \";\n    for (float elem : utLowState.imu_state().quaternion()) \n    {\n        std::cout << elem << \" \";\n    }\n    std::cout << std::endl;  \n\n    //IMU数据信息。角速度信息（0 -> x ,0 -> y ,0 -> z）\n    std::cout << \"imu_state.gyroscope: \";\n    for (float elem : utLowState.imu_state().gyroscope()) \n    {\n        std::cout << elem << \" \";\n    }\n    std::cout << std::endl;  \n\n    //IMU数据信息。加速度信息（0 -> x ,0 -> y ,0 -> z）\n    std::cout << \"imu_state.accelerometer: \";\n    for (float elem : utLowState.imu_state().accelerometer()) \n    {\n        std::cout << elem << \" \";\n    }\n    std::cout << std::endl;  \n\n    //IMU数据信息。欧拉角信息：默认为弧度值（可按照实际情况改为角度值），\n    //可按照实际数值显示（弧度值范围：-7 - +7，显示3位小数）。（数组：0-roll（翻滚角），1-pitch（俯仰角），2-yaw（偏航角））\n    std::cout << \"imu_state.rpy: \";\n    for (float elem : utLowState.imu_state().rpy()) \n    {\n        std::cout << elem << \" \";\n    }\n    std::cout << std::endl;  \n\n    //IMU数据信息。IMU 温度信息（摄氏度）。\n    std::cout << \"imu_state.temperature: \" << static_cast<int>(utLowState.imu_state().temperature()) << std::endl;\n\n    /*\n    电机反馈的实时信息：\n    mode:          电机控制模式（Foc模式（工作模式）-> 0x01 ，stop模式（待机模式）-> 0x00。）\n    q:             关机反馈位置信息：默认为弧度值（可按照实际情况改为角度值），可按照实际数值显示（弧度值范围：-7 - +7，显示3位小数）。\n    dq:            关节反馈速度\n    ddq:           关节反馈加速度\n    tau_est:       关节反馈力矩\n    q_raw:         沿用的，但是目前不用。\n    dq_raw:        沿用的，但是目前不用。\n    ddq_raw:       沿用的，但是目前不用。\n    temperature:   电机温度信息：类型：int8_t ，可按照实际数值显示（范围：-100 - 150）。\n    lost:          电机丢包信息：可按照实际数值显示（范围：0-9999999999）。\n    reserve[2]:    当前电机通信频率+电机错误标志位：（数组：0-电机错误标志位（范围：0-255，可按照实际数值显示），1-当前电机通信频率（范围：0-800，可按照实际数值显示））\n    \n    电机顺序，目前只用12电机，后面保留。\n        FR_0 -> 0 , FR_1 -> 1  , FR_2 -> 2\n        FL_0 -> 3 , FL_1 -> 4  , FL_2 -> 5\n        RR_0 -> 6 , RR_1 -> 7  , RR_2 -> 8\n        RL_0 -> 9 , RL_1 -> 10 , RL_2 -> 11\n        Leg 0 ：FR，右前腿\n        Leg 1 ：FL，左前腿\n        Leg 2 ：RR，右后腿\n        Leg 3 ：RL，左后腿\n        Joint 0 ：Hip，机身关节\n        Joint 1 ：Thigh，大腿关节\n        Joint 2 ：Calf，小腿关节\n    */\n    std::cout << \"motor_state: \" << std::endl;\n    for (unitree_go::msg::dds_::MotorState_ elem : utLowState.motor_state()) \n    {\n        std::cout << \"    ----------------\" << std::endl;\n        std::cout << \"    mode: \" << static_cast<int>(elem.mode()) << std::endl;\n        std::cout << \"    q: \" << elem.q() << std::endl;\n        std::cout << \"    dq: \" << elem.dq() << std::endl;\n        std::cout << \"    ddq: \" << elem.ddq() << std::endl;\n        std::cout << \"    tau_est: \" << elem.tau_est() << std::endl;\n        std::cout << \"    q_raw: \" << elem.q_raw() << std::endl;\n        std::cout << \"    dq_raw: \" << elem.dq_raw() << std::endl;\n        std::cout << \"    ddq_raw: \" << elem.ddq_raw() << std::endl;\n        std::cout << \"    temperature: \" << static_cast<int>(elem.temperature()) << std::endl;\n        std::cout << \"    lost: \" << elem.lost() << std::endl;\n        std::cout << \"    reserve: \";\n        for (uint32_t elem_in : elem.reserve()) \n        {\n            std::cout << elem_in << \" \";\n        }\n        std::cout << std::endl;\n    }\n\n    /* \n    octet version_high;    电池版本\n    octet version_low;     电池版本\n    octet status;          电池状态信息。\n        0：SAFE,（未开启电池）\n        1：WAKE_UP,（唤醒事件）\n        6：PRECHG, （电池预冲电中）\n        7：CHG, （电池正常充电中）\n        8：DCHG, （电池正常放电中）\n        9：SELF_DCHG, （电池自放电中）\n        11：ALARM, （电池存在警告）\n        12：RESET_ALARM, （等待按键复位警告中）\n        13：AUTO_RECOVERY （复位中）\n    octet soc;             电池电量信息：（类型：uint8_t）(范围1% - 100%)\n    long current;          充放电信息：（正：代表充电，负代表放电）可按照实际数值显示\n    unsigned short cycle;  充电循环次数\n    */\n    std::cout << \"bms_state.version_high: \" << static_cast<int>(utLowState.bms_state().version_high()) << std::endl;\n    std::cout << \"bms_state.version_low: \" << static_cast<int>(utLowState.bms_state().version_low()) << std::endl;\n    std::cout << \"bms_state.status: \" << static_cast<int>(utLowState.bms_state().status()) << std::endl;\n    std::cout << \"bms_state.soc: \" << static_cast<int>(utLowState.bms_state().soc()) << std::endl;\n    std::cout << \"bms_state.current: \" << utLowState.bms_state().current() << std::endl;\n    std::cout << \"bms_state.cycle: \" << utLowState.bms_state().cycle() << std::endl;\n\n    // 电池内部两个NTC的温度（int8_t）（范围：-100 - 150）。  0- BAT1; 1- BAT2 \n    std::cout << \"bms_state.bq_ntc: \";\n    for (uint8_t elem : utLowState.bms_state().bq_ntc()) \n    {\n        std::cout << static_cast<int>(elem) << \" \";\n    }\n    std::cout << std::endl;  \n\n    // 电池NTC数组：0 - RES，1 - MOS （int8_t）（范围：-100 - 150）\n    std::cout << \"bms_state.mcu_ntc: \";\n    for (uint8_t elem : utLowState.bms_state().mcu_ntc()) \n    {\n        std::cout << static_cast<int>(elem) << \" \";\n    }\n    std::cout << std::endl;  \n\n    // 电池内部15节电池的电压。\n    std::cout << \"bms_state.cell_vol: \";\n    for (uint16_t elem : utLowState.bms_state().cell_vol()) \n    {\n        std::cout << elem << \" \";\n    }\n    std::cout << std::endl;  \n\n    //足端力（范围0-4095），可按照实际数值显示。（数组：0-FR，1-FL，2-RR, 3-RL）\n    std::cout << \"foot_force: \";\n    for (int16_t elem : utLowState.foot_force()) \n    {\n        std::cout << elem << \" \";\n    }\n    std::cout << std::endl;  \n\n    //沿用的，但是目前不用。\n    std::cout << \"foot_force_est: \";\n    for (int16_t elem : utLowState.foot_force_est()) \n    {\n        std::cout << elem << \" \";\n    }\n    std::cout << std::endl;  \n\n    //1ms计时用，按照1ms递增。\n    std::cout << \"tick: \" << utLowState.tick() << std::endl;\n\n    //遥控器原始数据。\n    std::cout << \"wireless_remote: \";\n    for (uint8_t elem : utLowState.wireless_remote()) \n    {\n        std::cout << static_cast<int>(elem) << \" \";\n    }\n    std::cout << std::endl;  \n\n    /* 各个组件状态显示\n        &0x80 -  电机               超时标志          1-超时   0-正常\n        &0x40 -  小Mcu              超时标志          1-超时   0-正常\n        &0x20 -  遥控器             超时标志          1-超时   0-正常\n        &0x10 -  电池               超时标志          1-超时   0-正常\n        &0x04 -  自动充电           自动充电状态标志  1-不充电           0-充电\n        &0x02 -  板载电流错误标志   错误标志          1-板载电流异常     0-正常\n        &0x01 -  运控命令超时       超时标志          1-超时             0-正常\n    */\n    std::cout << \"bit_flag: \" << static_cast<int>(utLowState.bit_flag()) << std::endl;\n\n    // 卷线器电流（范围：0 - 3A）。\n    std::cout << \"adc_reel: \" << utLowState.adc_reel() << std::endl;\n\n    // 主板中心温度值（范围：-20 - 100℃）。\n    std::cout << \"temperature_ntc1: \" << static_cast<int>(utLowState.temperature_ntc1()) << std::endl;\n\n    // 自动充电温度（范围：-20 - 100℃）。\n    std::cout << \"temperature_ntc2: \" << static_cast<int>(utLowState.temperature_ntc2()) << std::endl;\n\n    // 此电压值为主板电压 -> 电池电压 。\n    std::cout << \"power_v: \" << utLowState.power_v() << std::endl;\n\n    // 此电流值为主板电流值 -> 电机电流。\n    std::cout << \"power_a: \" << utLowState.power_a() << std::endl;\n\n    /*\n    风扇转速（目前可按照实际数值显示0-10000）。（0-左后转速 , 1-右后转速，2-前转速，单位转/分钟）\n    （堵转检测：3-&0x01：左后堵转 , &0x02：右后堵转，&0x04：前堵转）\n    */\n    std::cout << \"fan_frequency: \";\n    for (int16_t elem : utLowState.fan_frequency()) \n    {\n        std::cout << elem << \" \";\n    }\n    std::cout << std::endl;  \n\n    //保留位。\n    std::cout << \"reserve: \" << utLowState.reserve() << std::endl;\n\n    //数据CRC校验用。\n    std::cout << \"crc: \" << utLowState.crc() << std::endl;\n\n    std::cout << \"----------------------------- end ---------------------------\" << std::endl;\n    \n    return;\n}\n\nvoid RobotInfoMgr::utStateDump() \n{\n    if (utStateDumpSwitch == 0)\n    {\n        return;\n    }\n\n    if (low_state_flag == true)\n    {\n        utLowStateDump();\n    }\n\n    /* 设置运动状态信息 */\n    if (sport_state_flag == true)\n    {\n        utHighStateDump();\n    }\n\n    return;\n}\n\nvoid RobotInfoMgr::utStateThreadCall() \n{\n    double battery_level_tmp = 0;\n    double ntc1 = 0;\n\n    suber_cloud.reset(new ChannelSubscriber<sensor_msgs::msg::dds_::PointCloud2_>(TOPIC_CLOUD));\n    suber_cloud->InitChannel(std::bind(&RobotInfoMgr::utCloudHandler, this, std::placeholders::_1), 1);\n\n    suber_sport.reset(new ChannelSubscriber<unitree_go::msg::dds_::SportModeState_>(TOPIC_HIGHSTATE));\n    suber_sport->InitChannel(std::bind(&RobotInfoMgr::utHighStateHandler, this, std::placeholders::_1), 1);\n\n    suber_low.reset(new ChannelSubscriber<unitree_go::msg::dds_::LowState_>(TOPIC_LOWSTATE));\n    suber_low->InitChannel(std::bind(&RobotInfoMgr::utLowStateHandler, this, std::placeholders::_1), 1);\n\n    suber_lidar.reset(new ChannelSubscriber<unitree_go::msg::dds_::LidarState_>(TOPIC_LIDARSTATE));\n    suber_lidar->InitChannel(std::bind(&RobotInfoMgr::utLidarStateHandler, this, std::placeholders::_1), 1);\n\n    while (true) \n    {\n        /* 更新状态信息 */\n        utCPUTemperatureSet();\n        utSportStateSet();\n        utLowStateSet();\n        utStateDump();\n        std::this_thread::sleep_for(std::chrono::milliseconds(100)); // 适当的延时\n    }\n\n    return;\n}\n\nvoid RobotInfoMgr::devAlarmReportCallback(const std_msgs::msg::String::SharedPtr msg) {\n    Json::Value body;\n    Json::Reader reader;\n    std::string strMsg = msg->data;\n    if (!reader.parse(strMsg, body))\n        RCLCPP_WARN(rclcpp::get_logger(\"robdog_control\"),\"Can not parse warning msg\");\n    if (body[\"alarmName\"] == \"NoForeheadCamera\")\n        RobotState::getInstance().setForeheadCameraStatus(1);\n    else if (body[\"alarmName\"] == \"NoForeheadCameraClear\")\n        RobotState::getInstance().setForeheadCameraStatus(0);\n    else if (body[\"alarmName\"] == \"NoTieCamera\")\n        RobotState::getInstance().setTieCameraStatus(1);\n    else if (body[\"alarmName\"] == \"NoTCameraClear\")\n        RobotState::getInstance().setTieCameraStatus(0);\n    else if (body[\"alarmName\"] == \"NoMicrophone\")\n        RobotState::getInstance().setMicStatus(1);\n    else if (body[\"alarmName\"] == \"NoMicrophoneClear\")\n        RobotState::getInstance().setMicStatus(0);\n}\n\n#ifndef UNITREE\n/*\n * Receive external devices status msg and record\n * */\nvoid RobotInfoMgr::external_device_status_callback(const std_msgs::msg::String::SharedPtr msg) {\n    std::string strMsg = msg->data;\n    Json::Value body;\n    Json::Reader reader;\n\n    if (!reader.parse(strMsg, body)) {\n        RCLCPP_WARN(rclcpp::get_logger(\"robdog_control\"),\"Can not parse warning msg\");\n        return;\n    }\n\n    if (!body.isMember(\"extDev\") || !body.isMember(\"stateCode\")) {\n        RCLCPP_WARN(rclcpp::get_logger(\"robdog_control\"), \"Missing 'extDev' or 'stateCode' in JSON message: %s\", strMsg.c_str());\n        return;\n    }\n    if(body[\"extDev\"] == \"lidar\") {\n        int oldStateCode = RobotState::getInstance().getLidarState();\n        if(oldStateCode != body[\"stateCode\"].asInt()) {\n            RobotState::getInstance().setLidarState(body[\"stateCode\"].asInt());\n        }\n    } else if (body[\"extDev\"] == \"realSense\") {\n        int oldStateCode = RobotState::getInstance().getRealSenseState();\n        if(oldStateCode != body[\"stateCode\"].asInt()) {\n            RobotState::getInstance().setRealSenseState(body[\"stateCode\"].asInt());\n        }\n    } else if (body[\"extDev\"] == \"kernel\") {\n        RobotState::getInstance().setNvidiaCpuTemp(body[\"stateCode\"][\"temperature\"].asDouble());\n\n        std::string oldStatue = RobotState::getInstance().getNvidiaCpuUsage();\n        if(oldStatue != body[\"stateCode\"][\"cpuusage\"].asString()) {\n            RobotState::getInstance().setNvidiaCpuUsage(body[\"stateCode\"][\"cpuusage\"].asString());\n        }\n\n        oldStatue = RobotState::getInstance().getNvidiaMemUsage();\n        if(oldStatue != body[\"stateCode\"][\"memusage\"].asString()) {\n            RobotState::getInstance().setNvidiaMemUsage(body[\"stateCode\"][\"memusage\"].asString());\n        }\n\n        RobotState::getInstance().setNvidiaProcess(body[\"stateCode\"][\"ProcessList\"]);\n    } else if (body[\"extDev\"] == \"RTK\") {\n        int oldStateCode = RobotState::getInstance().getRTKState();\n        if(oldStateCode != body[\"stateCode\"].asInt()) {\n            RobotState::getInstance().setRTKState(body[\"stateCode\"].asInt());\n        }\n    }\n}\n#endif\n\nstd::string RobotInfoMgr::executeCommand(const std::string& cmd) {\n\n    char buffer[128];\n    std::string result = \"\";\n    FILE* pipe = popen(cmd.c_str(), \"r\");\n    if (!pipe) {\n        throw std::runtime_error(\"popen() failed!\");\n    }\n    try {\n        while (fgets(buffer, sizeof(buffer), pipe) != nullptr) {\n            result += buffer;\n        }\n    } catch (...) {\n        pclose(pipe);\n        throw;\n    }\n    pclose(pipe);\n    return result;\n}\n\ndouble RobotInfoMgr::getCpuTemperature() {\n\n    double cpu_temp = -99;\n    std::string cmdOutput;\n    std::string cmd;\n\n    if (access(\"/sys/class/thermal/thermal_zone0/temp\", F_OK) == 0) {\n        cmd = \"cat /sys/class/thermal/thermal_zone0/temp\";\n        cmdOutput = executeCommand(cmd);\n        cmdOutput.erase(cmdOutput.find_last_not_of(\" \\n\\r\\t\") + 1);\n        cpu_temp = std::stoi(cmdOutput) / 1000.0f;\n    }\n    else {\n        try {\n            cmd = \"sensors\";\n            cmdOutput = executeCommand(cmd);\n            std::regex temp_regex(R\"(temp1:\\s*([+-]?[0-9]+(?:\\.[0-9]+)?)°C)\");\n            std::sregex_iterator it(cmdOutput.begin(), cmdOutput.end(), temp_regex);\n            std::sregex_iterator end;\n            double temp;\n            for (; it != end; ++it) {\n                temp = std::stod((*it)[1]);\n                if (temp > cpu_temp)\n                    cpu_temp = temp;\n            }\n        } catch (const std::exception& e) {\n            RCLCPP_ERROR(rclcpp::get_logger(\"robdog_control\"), \"sensors command failed with error: %s\", e.what());\n        }\n    }\n    return cpu_temp;\n}\n\nvoid RobotInfoMgr::getCpuWarningMsg(char *msgBuffer, size_t bufferSize, double CPUTemp) {\n    if (CPUTemp >= CPU_TEMP_THREADHOLD) {\n        snprintf(msgBuffer, bufferSize,\n                \"机器狗温度过高(当前温度:%.1f)！已停止运行！请等待温度回复正常后，再尝试作业\", CPUTemp);\n    } else if (CPUTemp < 0) {\n        snprintf(msgBuffer, bufferSize,\n                \"机器狗温度过低(当前温度:%.1f)！已停止运行！请等待温度回复正常后，再尝试作业\", CPUTemp);\n    } else {\n        msgBuffer[0] = '\\0';\n    }\n}\n\nvoid RobotInfoMgr::getCpuTemperatureStatus() {\n\n    Json::Value BoardTemps;\n#ifndef UNITREE\n    double CPUTemp;\n    char msg[128] = \"\";\n    CPUTemp = getCpuTemperature();\n    BoardTemps[\"InteractiveBoard\"][\"status\"] = RobotInfoMgr::getInstance().get_cpu_temperature_status(CPUTemp);\n    getCpuWarningMsg(msg, sizeof(msg), CPUTemp);\n    BoardTemps[\"InteractiveBoard\"][\"msg\"] = msg;\n\n    BoardTemps[\"MotionControlBoard\"][\"status\"] = RobotInfoMgr::getInstance().getCPUTempStatus();\n    getCpuWarningMsg(msg, sizeof(msg), RobotInfoMgr::getInstance().getRobotCPUTemperature());\n    BoardTemps[\"MotionControlBoard\"][\"msg\"] = msg;\n\n    CPUTemp = RobotState::getInstance().getNvidiaCpuTemp();\n    BoardTemps[\"NvidiaBoard\"][\"status\"] = RobotInfoMgr::getInstance().get_cpu_temperature_status(CPUTemp);\n    getCpuWarningMsg(msg, sizeof(msg), CPUTemp);\n    BoardTemps[\"NvidiaBoard\"][\"msg\"] = msg;\n#else\n    double CPUTemp;\n    char msg[128] = \"\";\n    CPUTemp = RobotInfoMgr::getInstance().getRobotCPUTemperature();\n    BoardTemps[\"status\"] = RobotInfoMgr::getInstance().getCPUTempStatus();\n    getCpuWarningMsg(msg, sizeof(msg), CPUTemp);\n    BoardTemps[\"msg\"] = msg;\n#endif\n    RobotState::getInstance().setBoardTempStatus(BoardTemps);\n}\n\nstd::string RobotInfoMgr::getCpuUtility(const std::string& cmd_base) {\n    std::string cpuUsage;\n    std::string cmd = cmd_base + \"top -bn1 | grep '%Cpu' | awk '{print $2}'\";\n\n    cpuUsage = executeCommand(cmd);\n    cpuUsage.erase(cpuUsage.find_last_not_of(\" \\n\\r\\t\") + 1);\n    cpuUsage += \"%\";\n    return cpuUsage;\n}\n\nstd::string RobotInfoMgr::getMemUtility(const std::string& cmd_base) {\n    std::string cmd = cmd_base + \"free | awk '/Mem:/ {printf(\\\"%.2f%%\\\\n\\\", $3/$2 * 100)}'\";\n    std::string memUsabe = executeCommand(cmd);\n    memUsabe.erase(memUsabe.find_last_not_of(\" \\n\\r\\t\") + 1);\n    return memUsabe;\n}\n\nvoid RobotInfoMgr::getHardwareUsage() {\n    Json::Value usages;\n#ifndef UNITREE\n    usages[\"InteractiveBoard\"][\"cpu\"] = getCpuUtility(\"\");\n    usages[\"InteractiveBoard\"][\"memory\"] = getMemUtility(\"\");\n    usages[\"MotionControlBoard\"][\"cpu\"] = getCpuUtility(MotionControlBoard_cmd_base);\n    usages[\"MotionControlBoard\"][\"memory\"] = getMemUtility(MotionControlBoard_cmd_base);\n    usages[\"NvidiaBoard\"][\"cpu\"] = RobotState::getInstance().getNvidiaCpuUsage();\n    usages[\"NvidiaBoard\"][\"memory\"] = RobotState::getInstance().getNvidiaMemUsage();\n#else\n    usages[\"cpu\"] = getCpuUtility(\"\");\n    usages[\"memory\"] = getMemUtility(\"\");\n#endif\n    RobotState::getInstance().setHDUtilization(usages);\n}\n\nJson::Value RobotInfoMgr::getProcessStatus(const std::string& cmd_base, const std::vector<std::string>& ProcessList) {\n\n    Json::Value errProcess(Json::arrayValue);\n    std::string cmd = cmd_base + \"ps -efww\";\n    std::string output = executeCommand(cmd);\n\n    for (const auto& proc : ProcessList) {\n        if (output.find(proc) == std::string::npos) {\n            errProcess.append(proc);\n        }\n    }\n    return errProcess;\n}\n\nvoid RobotInfoMgr::getBoardProcessStatus() {\n\n    Json::Value Processes;\n\n#ifndef UNITREE\n    Processes[\"InteractiveBoard\"][\"ProcessList\"] = getProcessStatus(\"\", InteractiveBoardProcessList);\n    if (Processes[\"InteractiveBoard\"][\"ProcessList\"].empty())\n        Processes[\"InteractiveBoard\"][\"status\"] = 0;\n    else\n        Processes[\"InteractiveBoard\"][\"status\"] = 1;\n\n    Processes[\"MotionControlBoard\"][\"ProcessList\"] = getProcessStatus(MotionControlBoard_cmd_base, MotionControlBoardProcessList);\n    if (Processes[\"MotionControlBoard\"][\"ProcessList\"].empty())\n        Processes[\"MotionControlBoard\"][\"status\"] = 0;\n    else\n        Processes[\"MotionControlBoard\"][\"status\"] = 1;\n\n    Processes[\"NvidiaBoard\"][\"ProcessList\"] = RobotState::getInstance().getNvidiaProcess();\n    if (Processes[\"NvidiaBoard\"][\"ProcessList\"].empty())\n        Processes[\"NvidiaBoard\"][\"status\"] = 0;\n    else\n        Processes[\"NvidiaBoard\"][\"status\"] = 1;\n#else\n    Processes[\"status\"] = getProcessStatus(\"\", UnitreeProcessList);\n    if (Processes[\"ProcessList\"].empty())\n        Processes[\"status\"] = 0;\n    else\n        Processes[\"status\"] = 1;\n#endif\n    RobotState::getInstance().setProcessStatus(Processes);\n}\n\nvoid RobotInfoMgr::getUSBAudioDeviceStatus() {\n\n    std::string output = executeCommand(\"aplay -l\");\n    if (output.find(\"no soundcards found\") == std::string::npos) {\n        RobotState::getInstance().setAudioStatus(0); // normal\n    } else {\n        RobotState::getInstance().setAudioStatus(1); // error\n    }\n}\n\nvoid RobotInfoMgr::collectUWBStatus() {\n#ifndef UNITREE\n\tstd::string uwb_serial_ports[] = {\"tty0\", \"tty8\", \"tty9\"};\n#else\n    std::string uwb_serial_ports[] = {\"tty3\", \"tty4\", \"tty5\"};\n#endif\n    int statusCode = 0;\n\tfor (const auto& port : uwb_serial_ports) {\n\t\tstd::string cmd = \"ls /dev/\" + port + \" > /dev/null 2>&1\";\n\t\tif (system(cmd.c_str()) != 0) {\n            statusCode = 1;\n\t\t\tbreak;\n\t\t}\n\t}\n    RobotState::getInstance().setUWBStatus(statusCode);\n}\n\nvoid RobotInfoMgr::collectBluetoothStatus() {\n\n#ifndef UNITREE\n    std::string cmd = MotionControlBoard_cmd_base + \"bluetoothctl list\";\n#else\n    std::string cmd = \"bluetoothctl list\";\n#endif\n    std::string output;\n    output = executeCommand(cmd);\n    if (output.find(\"Controller\") != std::string::npos)\n        RobotState::getInstance().setBluetoothStatus(0);\n    else\n        RobotState::getInstance().setBluetoothStatus(1);\n}\n\n#ifdef UNITREE\nvoid RobotInfoMgr::getRTKStatusCode() {\n    if (access(\"/dev/ttyS0\", F_OK) == 0) {\n        RobotState::getInstance().setRTKState(0);\n    }\n    else {\n        RobotState::getInstance().setRTKState(1);\n    }\n}\n#endif\n\nvoid RobotInfoMgr::getNetworkInfo(Json::Value& body) {\n\n    Json::Reader reader;\n    Json::Value value;\n\n    reader.parse(g_netctrl_ret, value);\n    if (value.isNull())\n    {\n        body[\"networkStatus\"][\"wifiState\"] = Json::Value();\n        body[\"networkStatus\"][\"mobileDataState\"] = Json::Value();\n        body[\"networkStatus\"][\"wifiName\"] = Json::Value();\n        body[\"networkStatus\"][\"isWifiConnect\"] = Json::Value();\n    }\n    else {\n        body[\"networkStatus\"][\"wifiState\"] = value[\"wifiState\"];\n        body[\"networkStatus\"][\"mobileDataState\"] = value[\"mobileDataState\"];\n        body[\"networkStatus\"][\"wifiName\"] = value[\"wifiName\"];\n        body[\"networkStatus\"][\"isWifiConnect\"] = value[\"isWifiConnect\"];\n    }\n}\n\nvoid RobotInfoMgr::dataCollect() {\n    // 定义数据收集lambda函数\n    auto collectAllData = [this]() {\n        getCpuTemperatureStatus();\n        getHardwareUsage();\n        getBoardProcessStatus();\n        getUSBAudioDeviceStatus();\n        collectUWBStatus();\n        collectBluetoothStatus();\n#ifdef UNITREE\n        getRTKStatusCode();\n        collectLidarState();\n#endif\n    };\n\n    // 第一次执行数据收集\n    collectAllData();\n\n    // 设置标志并通知\n    {\n        std::lock_guard<std::mutex> lock(data_mutex_);\n        data_collected_ = true;\n    }\n    data_cv_.notify_all();\n\n    // 进入定期收集循环\n    while(true) {\n        sleep(30);\n        collectAllData();\n    }\n}\n\n#ifdef UNITREE\nvoid RobotInfoMgr::collectLidarState() {\n    if (lidar_flag == true) {\n        unitree_go::msg::dds_::LidarState_ lidar_state;\n        utGetLidarState(lidar_state);\n        RobotState::getInstance().setLidarDirtyPercent(lidar_state.dirty_percentage());\n        if (lidar_state.error_state() == 0)\n            RobotState::getInstance().setLidarState(0);\n        else\n            RobotState::getInstance().setLidarState(1);\n    }\n    else\n    {\n        RobotState::getInstance().setLidarDirtyPercent(0xFF);\n        RobotState::getInstance().setLidarState(2);\n    }\n}\n#endif\nvoid RobotInfoMgr::stateReporter() {\n\n    std::mutex mtx_;\n    std::condition_variable cv_;\n\n    Json::Value root;\n    Json::Value body;\n\n#ifdef UNITREE\n    unitree_go::msg::dds_::LowState_ low_state_temp;\n    if (low_state_flag == true) {\n        utGetLowState(low_state_temp);\n    }\n    unitree_go::msg::dds_::LidarState_ lidar_state_temp;\n    if (lidar_flag == true) {\n        utGetLidarState(lidar_state_temp);\n    }\n#endif\n\n    auto jointTemp = RobotInfoMgr::getInstance().getRobotTemperature();\n    std::string propertyName = \"\";\n\n    // Joint temperature\n    for(int i = 0;i< MAX_JOINT_NUM;i++) {\n        propertyName = jointNames[i];\n        body[\"hardwareStatus\"][propertyName][\"temperature\"] = (double)jointTemp[i];\n        body[\"hardwareStatus\"][propertyName][\"stateCode\"] = (double)jointTemp[i]>TEMP_THREADHOLD ? 1 : 0;\n#ifdef UNITREE\n        body[\"hardwareStatus\"][propertyName][\"status\"] = low_state_temp.motor_state()[i].reserve()[0];\n        body[\"hardwareStatus\"][propertyName][\"frequency\"] = low_state_temp.motor_state()[i].reserve()[1];\n#endif\n    }\n    body[\"lidar\"][\"stateCode\"] = RobotState::getInstance().getLidarState();\n#ifdef UNITREE\n    body[\"lidar\"][\"dirtyPercent\"] = RobotState::getInstance().getLidarDirtyPercent();\n    body[\"lidar\"][\"status\"] = static_cast<int>(lidar_state_temp.error_state());\n    // imu\n    body[\"imu\"][\"temperature\"] = static_cast<int>(low_state_temp.imu_state().temperature());\n#endif\n#ifndef UNITREE\n    body[\"realSense\"][\"stateCode\"] = RobotState::getInstance().getRealSenseState();\n#endif\n\tbody[\"battery\"][\"power\"] = RobotInfoMgr::getInstance().getBatteryLevel();\n    body[\"battery\"][\"status\"] = (RobotInfoMgr::getInstance().getIsCharging())?1:0;\n#ifdef UNITREE\n    body[\"battery\"][\"stateCode\"] = static_cast<int>(low_state_temp.bms_state().status());\n    body[\"battery\"][\"cycle\"] = static_cast<int>(low_state_temp.bms_state().cycle());\n#endif\n    getNetworkInfo(body);\n    body[\"temperature\"] = RobotState::getInstance().getBoardTempStatus();\n    body[\"Utilization\"] = RobotState::getInstance().getHDUtilization();\n    body[\"process\"] = RobotState::getInstance().getProcessStatus();\n    body[\"AudioDevice\"][\"stateCode\"] = RobotState::getInstance().getAudioStatus();\n    body[\"uwb\"][\"statusCode\"] = RobotState::getInstance().getUWBStatus();\n    body[\"bluetooth\"][\"status\"] = RobotState::getInstance().getBluetoothStatus();\n    body[\"rtk\"][\"stateCode\"] = RobotState::getInstance().getRTKState();\n    body[\"mic\"][\"stateCode\"] = RobotState::getInstance().getMicStatus();\n    body[\"camera\"][\"forehead\"][\"stateCode\"] = RobotState::getInstance().getForeheadCameraStatus();\n    body[\"camera\"][\"bowtie\"][\"stateCode\"] = RobotState::getInstance().getTieCameraStatus();\n\n    root[\"deviceId\"] = RobotState::getInstance().getDeviceId();\n    RCLCPP_DEBUG(rclcpp::get_logger(\"robdog_control\"), \">>>>>>> deviceid %s\", RobotState::getInstance().getDeviceId().c_str());\n    root[\"domain\"] = \"DEVICE_MONITOR\";\n    root[\"event\"] = \"hardware_state_report\";\n    std::time_t now = std::time(nullptr);\n    root[\"eventId\"] = \"state_report_\" + std::to_string(now);\n    root[\"seq\"] = std::to_string(now);\n    root[\"response\"] = \"false\";\n    root[\"body\"] = body;\n\n    Json::StreamWriterBuilder writerBuilder;\n    std::string jsonString = Json::writeString(writerBuilder, root);\n    RCLCPP_DEBUG(rclcpp::get_logger(\"robdog_control\"), \"Request to platform is %s\",root.toStyledString().c_str());\n    sendRequestData(jsonString);\n}\n"}]}