{"sourceFile": "robdog_control/CMakeLists.txt", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1754200235148, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1754200235148, "name": "Commit-0", "content": "cmake_minimum_required(VERSION 3.8)\nproject(robdog_control)\n\nif(\"${CMAKE_BUILD_TYPE}\" STREQUAL \"Release\")\n  set(CMAKE_C_FLAGS_RELEASE \"${CMAKE_C_FLAGS_RELEASE} -s\")\n  set(CMAKE_CXX_FLAGS_RELEASE \"${CMAKE_CXX_FLAGS_RELEASE} -s\")\nendif()\n\nif(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES \"Clang\")\n  add_compile_options(-Wall -Wextra -Wpedantic)\nendif()\n\n# Default to C++14\nif(NOT CMAKE_CXX_STANDARD)\n  set(CMAKE_CXX_STANDARD 11)\n  set(CMAKE_CXX_STANDARD 14)\nendif()\n\n# 检测当前 Ubuntu 版本号\nexecute_process(\n  COMMAND lsb_release -rs\n  OUTPUT_VARIABLE UBUNTU_VERSION\n  OUTPUT_STRIP_TRAILING_WHITESPACE\n)\n\nmessage(STATUS \"Detected Ubuntu version: ${UBUNTU_VERSION}\")\n\nif(UBUNTU_VERSION VERSION_GREATER_EQUAL \"22.04\")\n  message(STATUS \"Ubuntu 22.04 or newer detected.\")\n  # 针对 Ubuntu 22.04 或更高版本的特定处理逻辑\n# 设置 ROS 2 环境\n  if(NOT DEFINED ENV{AMENT_PREFIX_PATH})\n    set(ENV{AMENT_PREFIX_PATH} \"/opt/ros/humble\")\n  endif()\n  link_directories(/opt/ros/humble/lib)\nelseif(UBUNTU_VERSION VERSION_GREATER_EQUAL \"20.04\")\n  message(STATUS \"Ubuntu 20.04 detected.\")\n  # 针对 Ubuntu 20.04 的特定处理逻辑\n  # 设置 ROS 2 环境\n  if(NOT DEFINED ENV{AMENT_PREFIX_PATH})\n    set(ENV{AMENT_PREFIX_PATH} \"/opt/ros/foxy\")\n  endif()\nlink_directories(/opt/ros/foxy/lib)\nelse()\n  message(STATUS \"Older Ubuntu version detected.\")\n  # 针对更旧版本的特定处理逻辑\nendif()\n\n# find dependencies\nfind_package(ament_cmake REQUIRED)\nfind_package(OpenSSL REQUIRED)\nfind_package(rclcpp REQUIRED)\nfind_package(rclcpp_action REQUIRED)\nfind_package(std_msgs REQUIRED)\nfind_package(geometry_msgs REQUIRED)\nfind_package(sensor_msgs REQUIRED)\nfind_package(OpenCV REQUIRED)\nfind_package(cv_bridge REQUIRED)\nfind_package(image_transport REQUIRED)\nfind_package(tf2_ros REQUIRED)\nfind_package(tf2_geometry_msgs REQUIRED)\nfind_package(nav_msgs REQUIRED)\nfind_package(rosidl_default_generators REQUIRED)\nfind_package(homi_speech_interface REQUIRED)\nfind_package(Boost REQUIRED COMPONENTS filesystem)\nfind_package(std_srvs REQUIRED)\nfind_package(rosidl_typesupport_cpp REQUIRED)\n# find_package(tinyxml2 REQUIRED)\n# find_package(ASound REQUIRED)\n\n# uncomment the following section in order to fill in\n# further dependencies manually.\n# find_package(<dependency> REQUIRED)\n\n# 查找系统平台\nif (${CMAKE_HOST_WIN32})\n    if(CMAKE_CXX_FLAGS MATCHES \"-m32\")\n        set(LIB_DIRS ${PROJECT_INIT_PATH}/lib/win/x86)\n    else()\n        set(LIB_DIRS ${PROJECT_INIT_PATH}/lib/win/x64)\n    endif()\nelseif(${CMAKE_HOST_UNIX})\n    message(\"Linux system detected.\")\n    \n    # Linux 平台特定处理逻辑\n    if(CMAKE_HOST_SYSTEM_PROCESSOR MATCHES \"x86_64\")\n        message(\"Support x86-64/AMD64 architecture.\")\n        if(CMAKE_CXX_FLAGS MATCHES \"-m32\")\n            message(\"32-bit architecture detected.\")\n            set(LIB_DIRS ${PROJECT_INIT_PATH}/lib/x86)\n        else()\n            message(\"64-bit architecture detected.\")\n            set(LIB_DIRS ${PROJECT_INIT_PATH}/lib/x86_64)\n        endif()\n    elseif(CMAKE_HOST_SYSTEM_PROCESSOR MATCHES \"aarch64\")\n        message(\"ARM 64-bit architecture detected, no support for x86-64/AMD64.\")\n        set(LIB_DIRS ${PROJECT_INIT_PATH}/lib/aarch64)\n    endif()\nelseif (${CMAKE_SYSTEM} MATCHES \"FreeBSD|OpenBSD\")\n    message(\"FreeBSD or OpenBSD system detected.\")\nendif()\n\n# 检测当前 Ubuntu 版本号\nexecute_process(\n  COMMAND lsb_release -rs\n  OUTPUT_VARIABLE UBUNTU_VERSION\n  OUTPUT_STRIP_TRAILING_WHITESPACE\n)\n\nmessage(STATUS \"Detected Ubuntu version: ${UBUNTU_VERSION}\")\n\nif(UBUNTU_VERSION VERSION_GREATER_EQUAL \"22.04\")\n  message(STATUS \"Ubuntu 22.04 or newer detected.\")\n  # 针对 Ubuntu 22.04 或更高版本的特定处理逻辑\n  set(CMAKE_CXX_STANDARD 17)\n  set(tinyxml2_DIR /usr/lib/x86_64-linux-gnu)\nelseif(UBUNTU_VERSION VERSION_GREATER_EQUAL \"20.04\")\n  message(STATUS \"Ubuntu 20.04 detected.\")\n  # 针对 Ubuntu 20.04 的特定处理逻辑\n  set(CMAKE_CXX_STANDARD 14)\n  set(tinyxml2_DIR /usr/lib/x86_64-linux-gnu)\nelse()\n  message(STATUS \"Older Ubuntu version detected.\")\n  # 针对更旧版本的特定处理逻辑\n  set(CMAKE_CXX_STANDARD 11)\nendif()\n\n############ robdog_control_node ############\nget_filename_component(SRC_DIR ${CMAKE_CURRENT_SOURCE_DIR} DIRECTORY)\nget_filename_component(MAIN_DIR ${SRC_DIR} DIRECTORY)\nmessage(${MAIN_DIR})\n\nif(${CMAKE_HOST_UNIX})\n    message(\"当前系统为 Linux 架构\")\n    # Linux 平台特定处理逻辑\n    if(CMAKE_HOST_SYSTEM_PROCESSOR MATCHES \"x86_64\")\n        message(\"当前系统为 x86-64/AMD64 架构\")\n        if(CMAKE_CXX_FLAGS MATCHES \"-m32\")\n            message(\"m32\")\n            find_library(WEBSOCKET_LIB WebSocket PATHS ${MAIN_DIR}/lib/x86_64 NO_DEFAULT_PATH)\n            find_library(DDSC_LIB ddsc PATHS ${MAIN_DIR}/lib/x86_64 NO_DEFAULT_PATH)\n            find_library(DDSCXX_LIB ddscxx PATHS ${MAIN_DIR}/lib/x86_64 NO_DEFAULT_PATH)\n            find_library(UNITREE_SDK2_LIB unitree_sdk2 PATHS ${MAIN_DIR}/lib/x86_64 NO_DEFAULT_PATH)\n        else()\n            message(\"x64\")\n            find_library(WEBSOCKET_LIB WebSocket PATHS ${MAIN_DIR}/lib/x86_64 NO_DEFAULT_PATH)\n            find_library(DDSC_LIB ddsc PATHS ${MAIN_DIR}/lib/x86_64 NO_DEFAULT_PATH)\n            find_library(DDSCXX_LIB ddscxx PATHS ${MAIN_DIR}/lib/x86_64 NO_DEFAULT_PATH)\n            find_library(UNITREE_SDK2_LIB unitree_sdk2 PATHS ${MAIN_DIR}/lib/x86_64 NO_DEFAULT_PATH)\n        endif()\n    elseif(CMAKE_HOST_SYSTEM_PROCESSOR MATCHES \"aarch64\")\n        message(\"aarch64\")\n        find_library(WEBSOCKET_LIB WebSocket PATHS ${MAIN_DIR}/lib/aarch64 NO_DEFAULT_PATH)\n        find_library(DDSC_LIB ddsc PATHS ${MAIN_DIR}/lib/aarch64 NO_DEFAULT_PATH)\n        find_library(DDSCXX_LIB ddscxx PATHS ${MAIN_DIR}/lib/aarch64 NO_DEFAULT_PATH)\n        find_library(UNITREE_SDK2_LIB unitree_sdk2 PATHS ${MAIN_DIR}/lib/aarch64 NO_DEFAULT_PATH)\n    endif()\nelseif (${CMAKE_SYSTEM} MATCHES \"FreeBSD|OpenBSD\")\n    # FreeBSD 或 OpenBSD 平台特定处理逻辑\nendif()\nif(NOT WEBSOCKET_LIB)\n  message(FATAL_ERROR \"Could not find libWebSocket.so\")\nendif()\n\n# 添加动态库\nadd_library(WebSocket SHARED IMPORTED)\nset_target_properties(WebSocket PROPERTIES\n  IMPORTED_LOCATION ${WEBSOCKET_LIB}\n)\n\n# 安装动态库\nif(NOT CMAKE_CUSTOM_LIB_INSTALL)\ninstall(FILES ${WEBSOCKET_LIB} DESTINATION lib/${PROJECT_NAME})\nelse()\ninstall(FILES ${WEBSOCKET_LIB} DESTINATION ${CMAKE_CUSTOM_LIB_INSTALL})\nendif()\n\ninclude_directories(\n ${CMAKE_CURRENT_SOURCE_DIR}\n ${CMAKE_CURRENT_BINARY_DIR}\n ${CMAKE_CURRENT_SOURCE_DIR}/include\n ${CMAKE_CURRENT_SOURCE_DIR}/src\n ${OpenCV_INCLUDE_DIRS}\n ${homi_speech_interface_INCLUDE_DIRS}\n ${MAIN_DIR}/src/homi_speech_interface/include\n ${MAIN_DIR}/include\n ${Boost_INCLUDE_DIRS}\n ${MAIN_DIR}/include/unitree\n ${MAIN_DIR}/include/unitree/ddscxx\n)\n\nSET(CMAKE_CXX_FLAGS \"${CMAKE_CXX_FLAGS} -lcurl\")\nset(tinyxml2_DIR /usr/lib/x86_64-linux-gnu)\n\nadd_executable(\n  robdog_control_node\n\n  src/robdog_control.cpp\n  src/robdogNode/robdog_ctrl_node.h\n  src/robdogNode/robdog_ctrl_node.cpp\n  src/robdogNode/robdog_ctrl_api.h\n  src/robdogNode/robdog_ctrl_unitree.h\n  src/robdogNode/robdog_ctrl_unitree.cpp\n  src/robdogNode/robdog_ctrl_deep.h\n  src/robdogNode/robdog_ctrl_deep.cpp\n\n  src/robotMgr/robot_info_mgr.h\n  src/robotMgr/robot_info_mgr.cpp\n\n  src/robotInfoCfg/read_map_point_cfg.h\n  src/robotInfoCfg/read_map_point_cfg.cpp\n\n  src/robdogCenter/robdog_center_mgr.h\n  src/robdogCenter/robdog_center_mgr.cpp\n\n  src/robdogHandPosCtrl/robdog_hand_pos.cpp\n  src/robdogHandPosCtrl/robdog_hand_pos.h\n\n  src/alarmMgr/robor_alarm_mgr.h\n  src/alarmMgr/robor_alarm_mgr.cpp\n  src/alarmMgr/alarm_info.h\n  src/alarmMgr/alarm_info.cpp\n\n  src/taskMgr/task_info_mgr.h\n  src/taskMgr/task_info_mgr.cpp\n  src/taskMgr/task_info.h\n  src/taskMgr/task_info.cpp\n\n  src/robotState/RobotState.h\n  src/robotState/RobotState.cpp\n  src/robotTrip/trip.cpp\n\n  src/public/audio_ctrl.h\n  src/public/audio_ctrl.cpp\n  \n  src/public/litedb.h\n  src/public/litedb.cpp\n  src/public/taskBase.h\n  src/public/taskBase.cpp\n\n  src/public/vedio_change.cpp\n  src/public/tools.h\n\n  src/robotSmartRemind/robot_smart_remind.cpp\n  src/robotSmartRemind/robot_smart_remind.h\n\n  src/followMe/followMeNode.cpp\n  src/followMe/followMeNode.h\n)\n\nament_target_dependencies(robdog_control_node rclcpp std_msgs geometry_msgs tf2_ros homi_speech_interface std_srvs rosidl_typesupport_cpp nav_msgs)\n\ntarget_compile_features(robdog_control_node PUBLIC c_std_99 cxx_std_17)  # Require C99 and C++17\n# ament_target_dependencies(deep_udp_ctrl rclcpp std_msgs geometry_msgs)\ntarget_link_libraries(robdog_control_node \n  #curl \n  jsoncpp \n  tinyxml2\n  #WS\n  ${WEBSOCKET_LIB}\n  asound\n  pthread\n  sqlite3\n  std_srvs__rosidl_typesupport_cpp\n  rosidl_typesupport_cpp\n  nav_msgs::nav_msgs__rosidl_typesupport_cpp\n  ${Boost_LIBRARIES}\n  OpenSSL::Crypto\n)\n\nfind_library(UUID_LIB uuid)\n\n#find_library(DDSC_LIB ddsc PATHS /opt/unitree_robotics/lib NO_DEFAULT_PATH)\n#find_library(DDSCXX_LIB ddscxx PATHS /opt/unitree_robotics/lib NO_DEFAULT_PATH)\n#find_library(UNITREE_SDK2_LIB unitree_sdk2 PATHS /opt/unitree_robotics/lib NO_DEFAULT_PATH)\n#find_library(UNITREE_ROS2_IDL_CPP_LIB unitree_ros2_idl_cpp)\n\nset_target_properties(robdog_control_node PROPERTIES\n  INSTALL_RPATH \"${CMAKE_INSTALL_PREFIX}/lib/${PROJECT_NAME}\"\n)\n\nif (UUID_LIB)\n    target_link_libraries(robdog_control_node ${UUID_LIB})\nelse()\n    message(FATAL_ERROR \"libuuid not found!\")\nendif()\n\nif (DDSC_LIB)\n    target_link_libraries(robdog_control_node ${DDSC_LIB})\n    get_filename_component(DDSC_LIB_PATH ${DDSC_LIB} DIRECTORY)\nelse()\n    message(FATAL_ERROR \"libddsc not found!\")\nendif()\n\nif (DDSCXX_LIB)\n    target_link_libraries(robdog_control_node ${DDSCXX_LIB})\n    get_filename_component(DDSCXX_LIB_PATH ${DDSCXX_LIB} DIRECTORY)\nelse()\n    message(FATAL_ERROR \"libddscxx not found!\")\nendif()\n\nif (UNITREE_SDK2_LIB)\n    target_link_libraries(robdog_control_node ${UNITREE_SDK2_LIB})\nelse()\n    message(FATAL_ERROR \"libunitree_sdk2 not found!\")\nendif()\n\n#if (UNITREE_ROS2_IDL_CPP_LIB)\n#    target_link_libraries(robdog_control_node ${UNITREE_ROS2_IDL_CPP_LIB})\n#else()\n#    message(FATAL_ERROR \"libunitree_ros2_idl_cpp not found!\")\n#endif()\n\ninstall(TARGETS robdog_control_node DESTINATION lib/${PROJECT_NAME})\ninstall(DIRECTORY resource DESTINATION share/${PROJECT_NAME}/)\nif(CMAKE_CUSTOM_LIB_INSTALL)\nif(DDSC_LIB)\ninstall(PROGRAMS\n  ${DDSC_LIB}\n  ${DDSC_LIB_PATH}/libddsc.so.0\n  DESTINATION ${CMAKE_CUSTOM_LIB_INSTALL}\n)\nendif()\nif(DDSCXX_LIB)\ninstall(PROGRAMS\n  ${DDSCXX_LIB}\n  ${DDSCXX_LIB_PATH}/libddscxx.so.0\n  DESTINATION ${CMAKE_CUSTOM_LIB_INSTALL}\n)\nendif()\nendif()\nif(BUILD_TESTING)\n  find_package(ament_lint_auto REQUIRED)\n  set(ament_cmake_copyright_FOUND TRUE)\n  set(ament_cmake_cpplint_FOUND TRUE)\n  ament_lint_auto_find_test_dependencies()\nendif()\n\nset(CMAKE_CUSTOM_PRODUCT_NO \"2_0\" CACHE STRING \"Custom product number (e.g., 1_0. 1_1, 2_0)\")\nif(\"${CMAKE_CUSTOM_PRODUCT_NO}\" STREQUAL \"1_0\")\n  target_compile_definitions(robdog_control_node PUBLIC YSC1_0)\nelseif(\"${CMAKE_CUSTOM_PRODUCT_NO}\" STREQUAL \"1_1\")\n  target_compile_definitions(robdog_control_node PUBLIC YSC1_1)\nelseif(\"${CMAKE_CUSTOM_PRODUCT_NO}\" STREQUAL \"2_0\")\n  target_compile_definitions(robdog_control_node PUBLIC UNITREE)\nendif()\n\nament_package()\n"}]}