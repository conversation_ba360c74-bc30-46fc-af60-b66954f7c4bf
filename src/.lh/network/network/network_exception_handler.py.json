{"sourceFile": "network/network/network_exception_handler.py", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1754306038854, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1754306038854, "name": "Commit-0", "content": "#!/usr/bin/env python3\n# -*- coding: utf-8 -*-\n\n\"\"\"\nNetwork节点异常处理器\n专门处理WiFi连接、网络接口等异常情况和恢复策略\n\"\"\"\n\nimport time\nimport subprocess\nimport threading\nimport socket\nfrom typing import Dict, Any, Optional\nimport os\n\n# 使用相对导入，如果失败则使用绝对导入\ntry:\n    from ..ble.ble.exception_manager import ExceptionManager, ExceptionType, ExceptionSeverity, ExceptionRecord\nexcept ImportError:\n    try:\n        from ble.ble.exception_manager import ExceptionManager, ExceptionType, ExceptionSeverity, ExceptionRecord\n    except ImportError:\n        import sys\n        sys.path.append('/mine/worktrees/unitree-debug/xiaoli_application_ros2/src')\n        from ble.ble.exception_manager import ExceptionManager, ExceptionType, ExceptionSeverity, ExceptionRecord\n\n\nclass NetworkExceptionHandler:\n    \"\"\"Network异常处理器\"\"\"\n    \n    def __init__(self, network_node, exception_manager: ExceptionManager):\n        self.network_node = network_node\n        self.exception_manager = exception_manager\n        self.logger = network_node.get_logger()\n        \n        # 网络健康检查配置\n        self.network_health_check_interval = 60  # 60秒检查一次\n        self.wifi_check_timeout = 10  # WiFi连接检查超时\n        self.connectivity_check_timeout = 5  # 网络连通性检查超时\n        \n        # WiFi重连配置\n        self.max_wifi_reconnect_attempts = 3\n        self.wifi_reconnect_delay = 10  # WiFi重连延迟（秒）\n        \n        # 网络接口重启配置\n        self.interface_restart_delay = 5\n        \n        # 连续失败计数\n        self.wifi_failure_count = 0\n        self.connectivity_failure_count = 0\n        \n        # 注册异常处理器\n        self._register_handlers()\n        self._register_recovery_strategies()\n        \n        # 启动健康检查\n        self.health_check_thread = threading.Thread(target=self._health_check_loop, daemon=True)\n        self.health_check_thread.start()\n        \n        self.logger.info(\"Network异常处理器已初始化\")\n\n    def _register_handlers(self):\n        \"\"\"注册异常处理器\"\"\"\n        self.exception_manager.register_handler(\n            ExceptionType.WIFI_CONNECTION_ERROR, \n            self._handle_wifi_connection_error\n        )\n        self.exception_manager.register_handler(\n            ExceptionType.WIFI_SCAN_ERROR, \n            self._handle_wifi_scan_error\n        )\n        self.exception_manager.register_handler(\n            ExceptionType.WIFI_AUTHENTICATION_ERROR, \n            self._handle_wifi_auth_error\n        )\n        self.exception_manager.register_handler(\n            ExceptionType.NETWORK_INTERFACE_ERROR, \n            self._handle_interface_error\n        )\n        self.exception_manager.register_handler(\n            ExceptionType.NETWORK_CONNECTIVITY_ERROR, \n            self._handle_connectivity_error\n        )\n        self.exception_manager.register_handler(\n            ExceptionType.DNS_RESOLUTION_ERROR, \n            self._handle_dns_error\n        )\n\n    def _register_recovery_strategies(self):\n        \"\"\"注册恢复策略\"\"\"\n        self.exception_manager.register_recovery_strategy(\n            ExceptionType.WIFI_CONNECTION_ERROR, \n            self._recover_wifi_connection\n        )\n        self.exception_manager.register_recovery_strategy(\n            ExceptionType.WIFI_SCAN_ERROR, \n            self._recover_wifi_scan\n        )\n        self.exception_manager.register_recovery_strategy(\n            ExceptionType.WIFI_AUTHENTICATION_ERROR, \n            self._recover_wifi_auth\n        )\n        self.exception_manager.register_recovery_strategy(\n            ExceptionType.NETWORK_INTERFACE_ERROR, \n            self._recover_interface_error\n        )\n        self.exception_manager.register_recovery_strategy(\n            ExceptionType.NETWORK_CONNECTIVITY_ERROR, \n            self._recover_connectivity\n        )\n        self.exception_manager.register_recovery_strategy(\n            ExceptionType.DNS_RESOLUTION_ERROR, \n            self._recover_dns_error\n        )\n\n    def report_wifi_connection_error(self, error_message: str, context: Optional[Dict[str, Any]] = None):\n        \"\"\"报告WiFi连接错误\"\"\"\n        self.wifi_failure_count += 1\n        severity = ExceptionSeverity.HIGH if self.wifi_failure_count >= 3 else ExceptionSeverity.MEDIUM\n        \n        self.exception_manager.report_exception(\n            exception_type=ExceptionType.WIFI_CONNECTION_ERROR,\n            message=f\"WiFi连接失败: {error_message}\",\n            severity=severity,\n            context={\n                **(context or {}),\n                \"failure_count\": self.wifi_failure_count\n            }\n        )\n\n    def report_wifi_scan_error(self, error_message: str, context: Optional[Dict[str, Any]] = None):\n        \"\"\"报告WiFi扫描错误\"\"\"\n        self.exception_manager.report_exception(\n            exception_type=ExceptionType.WIFI_SCAN_ERROR,\n            message=f\"WiFi扫描失败: {error_message}\",\n            severity=ExceptionSeverity.MEDIUM,\n            context=context\n        )\n\n    def report_connectivity_error(self, error_message: str, context: Optional[Dict[str, Any]] = None):\n        \"\"\"报告网络连通性错误\"\"\"\n        self.connectivity_failure_count += 1\n        severity = ExceptionSeverity.HIGH if self.connectivity_failure_count >= 5 else ExceptionSeverity.MEDIUM\n        \n        self.exception_manager.report_exception(\n            exception_type=ExceptionType.NETWORK_CONNECTIVITY_ERROR,\n            message=f\"网络连通性异常: {error_message}\",\n            severity=severity,\n            context={\n                **(context or {}),\n                \"failure_count\": self.connectivity_failure_count\n            }\n        )\n\n    def report_successful_wifi_connection(self):\n        \"\"\"报告WiFi连接成功\"\"\"\n        self.wifi_failure_count = 0\n\n    def report_successful_connectivity(self):\n        \"\"\"报告网络连通性正常\"\"\"\n        self.connectivity_failure_count = 0\n\n    def _handle_wifi_connection_error(self, record: ExceptionRecord):\n        \"\"\"处理WiFi连接错误\"\"\"\n        self.logger.warning(f\"处理WiFi连接错误: {record.message}\")\n        \n        # 检查WiFi接口状态\n        if hasattr(self.network_node, 'network_manager'):\n            try:\n                wifi_interface = getattr(self.network_node.network_manager, 'wifi_interface', 'wlan0')\n                status = self._check_interface_status(wifi_interface)\n                self.logger.info(f\"WiFi接口 {wifi_interface} 状态: {status}\")\n            except Exception as e:\n                self.logger.error(f\"检查WiFi接口状态失败: {e}\")\n\n    def _handle_wifi_scan_error(self, record: ExceptionRecord):\n        \"\"\"处理WiFi扫描错误\"\"\"\n        self.logger.warning(f\"处理WiFi扫描错误: {record.message}\")\n\n    def _handle_wifi_auth_error(self, record: ExceptionRecord):\n        \"\"\"处理WiFi认证错误\"\"\"\n        self.logger.warning(f\"处理WiFi认证错误: {record.message}\")\n\n    def _handle_interface_error(self, record: ExceptionRecord):\n        \"\"\"处理网络接口错误\"\"\"\n        self.logger.error(f\"处理网络接口错误: {record.message}\")\n\n    def _handle_connectivity_error(self, record: ExceptionRecord):\n        \"\"\"处理网络连通性错误\"\"\"\n        self.logger.warning(f\"处理网络连通性错误: {record.message}\")\n\n    def _handle_dns_error(self, record: ExceptionRecord):\n        \"\"\"处理DNS错误\"\"\"\n        self.logger.warning(f\"处理DNS错误: {record.message}\")\n\n    def _recover_wifi_connection(self, record: ExceptionRecord) -> bool:\n        \"\"\"恢复WiFi连接\"\"\"\n        try:\n            self.logger.info(\"尝试恢复WiFi连接...\")\n            \n            if not hasattr(self.network_node, 'network_manager'):\n                self.logger.error(\"Network Manager未初始化\")\n                return False\n            \n            network_manager = self.network_node.network_manager\n            wifi_interface = getattr(network_manager, 'wifi_interface', 'wlan0')\n            \n            # 步骤1: 重启WiFi接口\n            if self._restart_wifi_interface(wifi_interface):\n                time.sleep(self.interface_restart_delay)\n                \n                # 步骤2: 尝试自动连接\n                if hasattr(network_manager, 'auto_scan_and_connect_wifi'):\n                    network_manager.auto_scan_and_connect_wifi()\n                    \n                    # 步骤3: 验证连接\n                    time.sleep(10)  # 等待连接建立\n                    if self._verify_wifi_connection(wifi_interface):\n                        self.logger.info(\"WiFi连接恢复成功\")\n                        self.report_successful_wifi_connection()\n                        return True\n            \n            # 步骤4: 如果自动连接失败，尝试手动重连已保存的网络\n            if self._reconnect_saved_networks():\n                self.logger.info(\"WiFi连接恢复成功（手动重连）\")\n                self.report_successful_wifi_connection()\n                return True\n            \n            self.logger.warning(\"WiFi连接恢复失败\")\n            return False\n            \n        except Exception as e:\n            self.logger.error(f\"恢复WiFi连接时发生异常: {e}\")\n            return False\n\n    def _recover_wifi_scan(self, record: ExceptionRecord) -> bool:\n        \"\"\"恢复WiFi扫描功能\"\"\"\n        try:\n            self.logger.info(\"尝试恢复WiFi扫描功能...\")\n            \n            wifi_interface = getattr(self.network_node.network_manager, 'wifi_interface', 'wlan0')\n            \n            # 重启WiFi接口\n            if self._restart_wifi_interface(wifi_interface):\n                time.sleep(5)\n                \n                # 测试扫描功能\n                if self._test_wifi_scan(wifi_interface):\n                    self.logger.info(\"WiFi扫描功能恢复成功\")\n                    return True\n            \n            return False\n            \n        except Exception as e:\n            self.logger.error(f\"恢复WiFi扫描功能时发生异常: {e}\")\n            return False\n\n    def _recover_wifi_auth(self, record: ExceptionRecord) -> bool:\n        \"\"\"恢复WiFi认证\"\"\"\n        try:\n            self.logger.info(\"尝试恢复WiFi认证...\")\n            \n            # 重启wpa_supplicant服务\n            if self._restart_wpa_supplicant():\n                time.sleep(5)\n                \n                # 重新加载配置\n                subprocess.run(['sudo', 'wpa_cli', 'reconfigure'], \n                             check=False, capture_output=True)\n                \n                self.logger.info(\"WiFi认证恢复完成\")\n                return True\n            \n            return False\n            \n        except Exception as e:\n            self.logger.error(f\"恢复WiFi认证时发生异常: {e}\")\n            return False\n\n    def _recover_interface_error(self, record: ExceptionRecord) -> bool:\n        \"\"\"恢复网络接口错误\"\"\"\n        try:\n            self.logger.info(\"尝试恢复网络接口...\")\n            \n            # 重启网络服务\n            if self._restart_network_services():\n                time.sleep(10)\n                \n                # 验证接口状态\n                wifi_interface = getattr(self.network_node.network_manager, 'wifi_interface', 'wlan0')\n                mobile_interface = getattr(self.network_node.network_manager, 'mobile_interface', 'eth0')\n                \n                wifi_ok = self._check_interface_status(wifi_interface)\n                mobile_ok = self._check_interface_status(mobile_interface)\n                \n                if wifi_ok or mobile_ok:\n                    self.logger.info(\"网络接口恢复成功\")\n                    return True\n            \n            return False\n            \n        except Exception as e:\n            self.logger.error(f\"恢复网络接口时发生异常: {e}\")\n            return False\n\n    def _recover_connectivity(self, record: ExceptionRecord) -> bool:\n        \"\"\"恢复网络连通性\"\"\"\n        try:\n            self.logger.info(\"尝试恢复网络连通性...\")\n            \n            # 步骤1: 检查并重启DNS服务\n            if self._restart_dns_service():\n                time.sleep(3)\n                \n                # 步骤2: 测试连通性\n                if self._test_internet_connectivity():\n                    self.logger.info(\"网络连通性恢复成功\")\n                    self.report_successful_connectivity()\n                    return True\n            \n            # 步骤3: 如果DNS重启无效，尝试重启网络接口\n            wifi_interface = getattr(self.network_node.network_manager, 'wifi_interface', 'wlan0')\n            if self._restart_wifi_interface(wifi_interface):\n                time.sleep(10)\n                \n                if self._test_internet_connectivity():\n                    self.logger.info(\"网络连通性恢复成功（重启接口）\")\n                    self.report_successful_connectivity()\n                    return True\n            \n            return False\n            \n        except Exception as e:\n            self.logger.error(f\"恢复网络连通性时发生异常: {e}\")\n            return False\n\n    def _recover_dns_error(self, record: ExceptionRecord) -> bool:\n        \"\"\"恢复DNS错误\"\"\"\n        try:\n            self.logger.info(\"尝试恢复DNS解析...\")\n            \n            # 重启DNS服务\n            if self._restart_dns_service():\n                time.sleep(3)\n                \n                # 测试DNS解析\n                if self._test_dns_resolution():\n                    self.logger.info(\"DNS解析恢复成功\")\n                    return True\n            \n            return False\n            \n        except Exception as e:\n            self.logger.error(f\"恢复DNS解析时发生异常: {e}\")\n            return False\n\n    def _restart_wifi_interface(self, interface: str) -> bool:\n        \"\"\"重启WiFi接口\"\"\"\n        try:\n            self.logger.info(f\"重启WiFi接口: {interface}\")\n            \n            # 禁用接口\n            subprocess.run(['sudo', 'ip', 'link', 'set', interface, 'down'], \n                         check=False, capture_output=True)\n            time.sleep(2)\n            \n            # 启用接口\n            result = subprocess.run(['sudo', 'ip', 'link', 'set', interface, 'up'], \n                                  check=False, capture_output=True)\n            \n            return result.returncode == 0\n            \n        except Exception as e:\n            self.logger.error(f\"重启WiFi接口失败: {e}\")\n            return False\n\n    def _restart_wpa_supplicant(self) -> bool:\n        \"\"\"重启wpa_supplicant服务\"\"\"\n        try:\n            self.logger.info(\"重启wpa_supplicant服务...\")\n            \n            # 停止服务\n            subprocess.run(['sudo', 'systemctl', 'stop', 'wpa_supplicant'], \n                         check=False, capture_output=True)\n            time.sleep(2)\n            \n            # 启动服务\n            result = subprocess.run(['sudo', 'systemctl', 'start', 'wpa_supplicant'], \n                                  check=False, capture_output=True)\n            \n            return result.returncode == 0\n            \n        except Exception as e:\n            self.logger.error(f\"重启wpa_supplicant服务失败: {e}\")\n            return False\n\n    def _restart_network_services(self) -> bool:\n        \"\"\"重启网络服务\"\"\"\n        try:\n            self.logger.info(\"重启网络服务...\")\n            \n            services = ['systemd-networkd', 'NetworkManager']\n            \n            for service in services:\n                try:\n                    subprocess.run(['sudo', 'systemctl', 'restart', service], \n                                 check=False, capture_output=True, timeout=30)\n                    self.logger.info(f\"重启服务成功: {service}\")\n                    return True\n                except:\n                    continue\n            \n            return False\n            \n        except Exception as e:\n            self.logger.error(f\"重启网络服务失败: {e}\")\n            return False\n\n    def _restart_dns_service(self) -> bool:\n        \"\"\"重启DNS服务\"\"\"\n        try:\n            self.logger.info(\"重启DNS服务...\")\n            \n            result = subprocess.run(['sudo', 'systemctl', 'restart', 'systemd-resolved'], \n                                  check=False, capture_output=True)\n            \n            return result.returncode == 0\n            \n        except Exception as e:\n            self.logger.error(f\"重启DNS服务失败: {e}\")\n            return False\n\n    def _check_interface_status(self, interface: str) -> bool:\n        \"\"\"检查网络接口状态\"\"\"\n        try:\n            result = subprocess.run(['ip', 'link', 'show', interface], \n                                  check=False, capture_output=True, text=True)\n            \n            if result.returncode == 0:\n                return 'state UP' in result.stdout\n            return False\n            \n        except Exception as e:\n            self.logger.error(f\"检查接口状态失败: {e}\")\n            return False\n\n    def _verify_wifi_connection(self, interface: str) -> bool:\n        \"\"\"验证WiFi连接\"\"\"\n        try:\n            # 检查接口是否有IP地址\n            result = subprocess.run(['ip', 'addr', 'show', interface], \n                                  check=False, capture_output=True, text=True)\n            \n            if result.returncode == 0 and 'inet ' in result.stdout:\n                # 进一步检查网络连通性\n                return self._test_internet_connectivity()\n            \n            return False\n            \n        except Exception as e:\n            self.logger.error(f\"验证WiFi连接失败: {e}\")\n            return False\n\n    def _test_wifi_scan(self, interface: str) -> bool:\n        \"\"\"测试WiFi扫描功能\"\"\"\n        try:\n            result = subprocess.run(['sudo', 'iw', interface, 'scan'], \n                                  check=False, capture_output=True, timeout=10)\n            \n            return result.returncode == 0\n            \n        except Exception as e:\n            self.logger.error(f\"测试WiFi扫描失败: {e}\")\n            return False\n\n    def _test_internet_connectivity(self) -> bool:\n        \"\"\"测试互联网连通性\"\"\"\n        try:\n            # 尝试连接到公共DNS服务器\n            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)\n            sock.settimeout(self.connectivity_check_timeout)\n            result = sock.connect_ex(('*******', 53))\n            sock.close()\n            \n            return result == 0\n            \n        except Exception as e:\n            self.logger.error(f\"测试网络连通性失败: {e}\")\n            return False\n\n    def _test_dns_resolution(self) -> bool:\n        \"\"\"测试DNS解析\"\"\"\n        try:\n            socket.gethostbyname('www.google.com')\n            return True\n        except Exception as e:\n            self.logger.error(f\"DNS解析测试失败: {e}\")\n            return False\n\n    def _reconnect_saved_networks(self) -> bool:\n        \"\"\"重连已保存的网络\"\"\"\n        try:\n            if hasattr(self.network_node, 'network_manager'):\n                network_manager = self.network_node.network_manager\n                if hasattr(network_manager, '_read_wpa_supplicant_config'):\n                    saved_networks = network_manager._read_wpa_supplicant_config()\n                    \n                    if saved_networks:\n                        for network in saved_networks:\n                            ssid = network.get('ssid', '')\n                            password = network.get('psk', '')\n                            \n                            if ssid:\n                                self.logger.info(f\"尝试连接到保存的网络: {ssid}\")\n                                \n                                # 使用网络管理器的连接方法\n                                if hasattr(network_manager, 'connect_wifi_network'):\n                                    result = network_manager.connect_wifi_network(ssid, password)\n                                    if result and result.get('success'):\n                                        return True\n            \n            return False\n            \n        except Exception as e:\n            self.logger.error(f\"重连已保存网络失败: {e}\")\n            return False\n\n    def _health_check_loop(self):\n        \"\"\"健康检查循环\"\"\"\n        while True:\n            try:\n                time.sleep(self.network_health_check_interval)\n                self._perform_health_check()\n            except Exception as e:\n                self.logger.error(f\"网络健康检查时发生错误: {e}\")\n\n    def _perform_health_check(self):\n        \"\"\"执行网络健康检查\"\"\"\n        try:\n            # 检查WiFi接口状态\n            if hasattr(self.network_node, 'network_manager'):\n                wifi_interface = getattr(self.network_node.network_manager, 'wifi_interface', 'wlan0')\n                \n                if not self._check_interface_status(wifi_interface):\n                    self.exception_manager.report_exception(\n                        exception_type=ExceptionType.NETWORK_INTERFACE_ERROR,\n                        message=f\"WiFi接口 {wifi_interface} 状态异常\",\n                        severity=ExceptionSeverity.HIGH,\n                        context={\"interface\": wifi_interface}\n                    )\n                \n                # 检查网络连通性\n                if not self._test_internet_connectivity():\n                    self.report_connectivity_error(\n                        \"网络连通性检查失败\",\n                        {\"check_type\": \"health_check\"}\n                    )\n                else:\n                    self.report_successful_connectivity()\n                \n                # 检查DNS解析\n                if not self._test_dns_resolution():\n                    self.exception_manager.report_exception(\n                        exception_type=ExceptionType.DNS_RESOLUTION_ERROR,\n                        message=\"DNS解析检查失败\",\n                        severity=ExceptionSeverity.MEDIUM,\n                        context={\"check_type\": \"health_check\"}\n                    )\n                    \n        except Exception as e:\n            self.logger.error(f\"健康检查执行失败: {e}\")\n\n    def get_health_status(self) -> Dict[str, Any]:\n        \"\"\"获取网络健康状态\"\"\"\n        try:\n            wifi_interface = getattr(self.network_node.network_manager, 'wifi_interface', 'wlan0')\n            mobile_interface = getattr(self.network_node.network_manager, 'mobile_interface', 'eth0')\n            \n            return {\n                \"wifi_health\": {\n                    \"interface\": wifi_interface,\n                    \"is_up\": self._check_interface_status(wifi_interface),\n                    \"failure_count\": self.wifi_failure_count\n                },\n                \"mobile_health\": {\n                    \"interface\": mobile_interface,\n                    \"is_up\": self._check_interface_status(mobile_interface)\n                },\n                \"connectivity_health\": {\n                    \"internet_reachable\": self._test_internet_connectivity(),\n                    \"dns_working\": self._test_dns_resolution(),\n                    \"failure_count\": self.connectivity_failure_count\n                }\n            }\n        except Exception as e:\n            self.logger.error(f\"获取健康状态失败: {e}\")\n            return {\"error\": str(e)} "}]}