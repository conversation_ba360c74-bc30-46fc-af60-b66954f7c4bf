{"sourceFile": "network/network/network_config_manager.py", "activeCommit": 0, "commits": [{"activePatchIndex": 2, "patches": [{"date": 1754270420220, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1754270445542, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -90,8 +90,14 @@\n                 config = self._read_config_with_lock()\n                 if not self._validate_config(config):\n                     self.logger.warning(\"配置文件格式无效，重新创建默认配置\")\n                     self._write_config_with_lock(self._default_config)\n+                else:\n+                    # 检查是否需要配置迁移（添加缺失的字段）\n+                    updated = self._migrate_config_if_needed(config)\n+                    if updated:\n+                        self.logger.info(\"配置文件已更新到最新版本\")\n+                        self._write_config_with_lock(config)\n                     \n         except Exception as e:\n             self.logger.error(f\"初始化配置文件失败: {e}\")\n             # 如果初始化失败，创建默认配置\n@@ -100,8 +106,29 @@\n             except Exception as e2:\n                 self.logger.error(f\"创建默认配置文件也失败: {e2}\")\n                 raise\n \n+    def _migrate_config_if_needed(self, config: Dict[str, Any]) -> bool:\n+        \"\"\"\n+        迁移配置文件到最新版本，添加缺失的字段\n+        \n+        Args:\n+            config: 当前配置\n+            \n+        Returns:\n+            bool: 是否进行了更新\n+        \"\"\"\n+        updated = False\n+        \n+        # 确保 wifi 配置包含 wifiSetStatus 字段\n+        if \"wifi\" in config and isinstance(config[\"wifi\"], dict):\n+            if \"wifiSetStatus\" not in config[\"wifi\"]:\n+                config[\"wifi\"][\"wifiSetStatus\"] = 0  # 默认为未配置状态\n+                updated = True\n+                self.logger.info(\"添加 wifiSetStatus 字段到 wifi 配置\")\n+        \n+        return updated\n+\n     def _validate_config(self, config: Dict[str, Any]) -> bool:\n         \"\"\"验证配置文件格式 - 简化版本\"\"\"\n         try:\n             if not isinstance(config, dict):\n"}, {"date": 1754270462338, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -350,5 +350,28 @@\n             \"mobile_data\": kwargs\n         }\n         self.update_config(updates)\n \n+    def get_wifi_set_status(self) -> int:\n+        \"\"\"\n+        获取WiFi配置状态\n+        \n+        Returns:\n+            int: WiFi配置状态 (0-未配置、1-已配置)\n+        \"\"\"\n+        wifi_state = self.get_wifi_state()\n+        return wifi_state.get(\"wifiSetStatus\", 0)\n \n+    def update_wifi_set_status(self, status: int):\n+        \"\"\"\n+        更新WiFi配置状态\n+        \n+        Args:\n+            status: WiFi配置状态 (0-未配置、1-已配置)\n+        \"\"\"\n+        if status not in [0, 1]:\n+            raise ValueError(\"wifiSetStatus 必须是 0 (未配置) 或 1 (已配置)\")\n+        \n+        self.update_wifi_state(wifiSetStatus=status)\n+        self.logger.info(f\"WiFi配置状态已更新为: {'已配置' if status == 1 else '未配置'}\")\n+\n+\n"}], "date": 1754270420220, "name": "Commit-0", "content": "#!/usr/bin/env python3\n\"\"\"\n网络配置管理器\n负责网络状态配置文件的读写、锁管理和状态同步\n\"\"\"\n\nimport json\nimport os\nimport fcntl\nimport time\nimport threading\nfrom datetime import datetime\nfrom typing import Dict, Any, Optional\nimport rclpy\nfrom rclpy.logging import get_logger\n\n\nclass NetworkConfigManager:\n    \"\"\"网络配置管理器 - 负责WiFi和5G状态的持久化管理\"\"\"\n    \n    def __init__(self, config_file_path: str, lock_file_path: str, logger=None):\n        \"\"\"\n        初始化网络配置管理器\n        \n        Args:\n            config_file_path: 配置文件路径\n            lock_file_path: 锁文件路径\n            logger: 日志记录器\n        \"\"\"\n        self.config_file_path = config_file_path\n        self.lock_file_path = lock_file_path\n        self.logger = logger if logger else get_logger('network_config_manager')\n        \n        # 线程锁，用于保护内存中的配置数据\n        self._memory_lock = threading.RLock()\n        \n        # 内存中的配置缓存\n        self._config_cache = None\n        self._cache_timestamp = 0\n        self._cache_ttl = 5  # 缓存有效期5秒\n        \n        # 默认配置 - 简化版本，只保留核心开关状态\n        self._default_config = {\n            \"wifi\": {\n                \"switch_state\": \"on\",\n                \"wifiSetStatus\": 0  # WiFi配置状态：0-未配置、1-已配置\n            },\n            \"mobile_data\": {\n                \"switch_state\": \"on\"\n            }\n        }\n        \n        # 确保配置文件目录存在\n        self._ensure_config_directory()\n        \n        # 初始化配置文件\n        self._initialize_config_file()\n        \n        self.logger.info(f\"网络配置管理器初始化完成: {config_file_path}\")\n\n    def _get_current_timestamp(self) -> str:\n        \"\"\"获取当前时间戳\"\"\"\n        return datetime.now().isoformat()\n\n    def _ensure_config_directory(self):\n        \"\"\"确保配置文件目录存在\"\"\"\n        try:\n            config_dir = os.path.dirname(self.config_file_path)\n            if not os.path.exists(config_dir):\n                os.makedirs(config_dir, mode=0o755, exist_ok=True)\n                self.logger.info(f\"创建配置目录: {config_dir}\")\n                \n            lock_dir = os.path.dirname(self.lock_file_path)\n            if not os.path.exists(lock_dir):\n                os.makedirs(lock_dir, mode=0o755, exist_ok=True)\n                self.logger.info(f\"创建锁文件目录: {lock_dir}\")\n                \n        except Exception as e:\n            self.logger.error(f\"创建配置目录失败: {e}\")\n            raise\n\n    def _initialize_config_file(self):\n        \"\"\"初始化配置文件\"\"\"\n        try:\n            if not os.path.exists(self.config_file_path):\n                self.logger.info(\"配置文件不存在，创建默认配置文件\")\n                self._write_config_with_lock(self._default_config)\n            else:\n                # 验证现有配置文件\n                config = self._read_config_with_lock()\n                if not self._validate_config(config):\n                    self.logger.warning(\"配置文件格式无效，重新创建默认配置\")\n                    self._write_config_with_lock(self._default_config)\n                    \n        except Exception as e:\n            self.logger.error(f\"初始化配置文件失败: {e}\")\n            # 如果初始化失败，创建默认配置\n            try:\n                self._write_config_with_lock(self._default_config)\n            except Exception as e2:\n                self.logger.error(f\"创建默认配置文件也失败: {e2}\")\n                raise\n\n    def _validate_config(self, config: Dict[str, Any]) -> bool:\n        \"\"\"验证配置文件格式 - 简化版本\"\"\"\n        try:\n            if not isinstance(config, dict):\n                return False\n                \n            # 检查必要的键 - 简化后只需要wifi和mobile_data\n            required_keys = [\"wifi\", \"mobile_data\"]\n            if not all(key in config for key in required_keys):\n                return False\n                \n            # 检查wifi配置\n            wifi_config = config[\"wifi\"]\n            if not isinstance(wifi_config, dict) or \"switch_state\" not in wifi_config:\n                return False\n                \n            # 检查mobile_data配置\n            mobile_config = config[\"mobile_data\"]\n            if not isinstance(mobile_config, dict) or \"switch_state\" not in mobile_config:\n                return False\n                \n            return True\n            \n        except Exception:\n            return False\n\n    def _acquire_file_lock(self, file_handle, timeout: float = 10.0) -> bool:\n        \"\"\"\n        获取文件锁\n        \n        Args:\n            file_handle: 文件句柄\n            timeout: 超时时间（秒）\n            \n        Returns:\n            bool: 是否成功获取锁\n        \"\"\"\n        start_time = time.time()\n        while time.time() - start_time < timeout:\n            try:\n                fcntl.flock(file_handle.fileno(), fcntl.LOCK_EX | fcntl.LOCK_NB)\n                return True\n            except (IOError, OSError):\n                time.sleep(0.1)\n        return False\n\n    def _release_file_lock(self, file_handle):\n        \"\"\"释放文件锁\"\"\"\n        try:\n            fcntl.flock(file_handle.fileno(), fcntl.LOCK_UN)\n        except (IOError, OSError) as e:\n            self.logger.warning(f\"释放文件锁失败: {e}\")\n\n    def _read_config_with_lock(self) -> Dict[str, Any]:\n        \"\"\"\n        使用文件锁读取配置\n        \n        Returns:\n            Dict: 配置数据\n        \"\"\"\n        try:\n            with open(self.config_file_path, 'r', encoding='utf-8') as f:\n                if not self._acquire_file_lock(f):\n                    raise Exception(\"获取文件读锁超时\")\n                \n                try:\n                    content = f.read()\n                    if not content.strip():\n                        self.logger.warning(\"配置文件为空，返回默认配置\")\n                        return self._default_config.copy()\n                    \n                    config = json.loads(content)\n                    return config\n                    \n                finally:\n                    self._release_file_lock(f)\n                    \n        except FileNotFoundError:\n            self.logger.warning(\"配置文件不存在，返回默认配置\")\n            return self._default_config.copy()\n        except json.JSONDecodeError as e:\n            self.logger.error(f\"配置文件JSON格式错误: {e}\")\n            return self._default_config.copy()\n        except Exception as e:\n            self.logger.error(f\"读取配置文件失败: {e}\")\n            return self._default_config.copy()\n\n    def _write_config_with_lock(self, config: Dict[str, Any]):\n        \"\"\"\n        使用文件锁写入配置\n        \n        Args:\n            config: 要写入的配置数据\n        \"\"\"\n        try:\n            # 先写入临时文件，然后原子性替换\n            temp_file_path = f\"{self.config_file_path}.tmp\"\n            \n            with open(temp_file_path, 'w', encoding='utf-8') as f:\n                if not self._acquire_file_lock(f):\n                    raise Exception(\"获取文件写锁超时\")\n                \n                try:\n                    json.dump(config, f, indent=2, ensure_ascii=False)\n                    f.flush()\n                    os.fsync(f.fileno())  # 强制写入磁盘\n                    \n                finally:\n                    self._release_file_lock(f)\n            \n            # 原子性替换\n            os.replace(temp_file_path, self.config_file_path)\n            \n            # 清除缓存\n            with self._memory_lock:\n                self._config_cache = None\n                \n            self.logger.debug(\"配置文件写入成功\")\n            \n        except Exception as e:\n            self.logger.error(f\"写入配置文件失败: {e}\")\n            # 清理临时文件\n            try:\n                if os.path.exists(temp_file_path):\n                    os.remove(temp_file_path)\n            except:\n                pass\n            raise\n\n    def get_config(self) -> Dict[str, Any]:\n        \"\"\"\n        获取配置（带缓存）\n        \n        Returns:\n            Dict: 配置数据\n        \"\"\"\n        with self._memory_lock:\n            current_time = time.time()\n            \n            # 检查缓存是否有效\n            if (self._config_cache is not None and \n                current_time - self._cache_timestamp < self._cache_ttl):\n                return self._config_cache.copy()\n            \n            # 重新读取配置\n            config = self._read_config_with_lock()\n            self._config_cache = config.copy()\n            self._cache_timestamp = current_time\n            \n            return config.copy()\n\n    def update_config(self, updates: Dict[str, Any]):\n        \"\"\"\n        更新配置\n        \n        Args:\n            updates: 要更新的配置项\n        \"\"\"\n        with self._memory_lock:\n            try:\n                # 读取当前配置\n                current_config = self._read_config_with_lock()\n                \n                # 深度合并更新\n                self._deep_merge(current_config, updates)\n                \n                # 写入更新后的配置\n                self._write_config_with_lock(current_config)\n                \n                self.logger.info(f\"配置更新成功: {updates}\")\n                \n            except Exception as e:\n                self.logger.error(f\"更新配置失败: {e}\")\n                raise\n\n    def _deep_merge(self, target: Dict[str, Any], source: Dict[str, Any]):\n        \"\"\"\n        深度合并字典\n        \n        Args:\n            target: 目标字典（会被修改）\n            source: 源字典\n        \"\"\"\n        for key, value in source.items():\n            if key in target and isinstance(target[key], dict) and isinstance(value, dict):\n                self._deep_merge(target[key], value)\n            else:\n                target[key] = value\n\n    def get_wifi_state(self) -> Dict[str, Any]:\n        \"\"\"获取WiFi状态\"\"\"\n        config = self.get_config()\n        return config[\"wifi\"].copy()\n\n    def get_mobile_data_state(self) -> Dict[str, Any]:\n        \"\"\"获取移动数据状态\"\"\"\n        config = self.get_config()\n        return config[\"mobile_data\"].copy()\n\n    def update_wifi_state(self, **kwargs):\n        \"\"\"\n        更新WiFi状态\n        \n        Args:\n            **kwargs: WiFi状态参数 (switch_state等)\n        \"\"\"\n        updates = {\n            \"wifi\": kwargs\n        }\n        self.update_config(updates)\n\n    def update_mobile_data_state(self, **kwargs):\n        \"\"\"\n        更新移动数据状态\n        \n        Args:\n            **kwargs: 移动数据状态参数 (switch_state等)\n        \"\"\"\n        updates = {\n            \"mobile_data\": kwargs\n        }\n        self.update_config(updates)\n\n\n"}]}