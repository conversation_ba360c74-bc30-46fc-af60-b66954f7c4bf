{"sourceFile": "network/network/network_node.py", "activeCommit": 0, "commits": [{"activePatchIndex": 1, "patches": [{"date": 1754362681828, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1754362691880, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -314,9 +314,9 @@\n                 # 清理资源\n                 curl.close()\n                 buffer.close()\n \n-        else:\n+        # else:\n             # self.get_logger().info(\"jwae_tproxy模式未启用,跳过相关进程启动\")\n             # self.network_manager.run_command(\"sudo bash /usr/bin/cmcc_robot/install/env/jwae_tproxy/stop_jwae_tproxy.sh\")        \n \n \n"}], "date": 1754362681828, "name": "Commit-0", "content": "import os\nimport json\nimport socket\nimport time\nimport subprocess\nimport fcntl\nimport rclpy\nfrom rclpy.node import Node\nimport dbus\nimport dbus.mainloop.glib\nimport threading\nfrom gi.repository import GLib\n# 导入homi_speech_interface服务 - 最优解\nfrom homi_speech_interface.srv import NetCtrl, SIGCData\nfrom std_msgs.msg import String\nimport configparser\nimport struct\nimport concurrent.futures\nfrom concurrent.futures import ThreadPoolExecutor\n\nimport signal\nimport sys\n\n# 解决导入问题 - 最优解\nimport os\nimport sys\nfrom pathlib import Path\n\nimport pycurl\nfrom io import BytesIO\n\n# 获取当前文件所在目录和上级目录\ncurrent_dir = Path(__file__).parent  # .../network/network/\nparent_dir = current_dir.parent      # .../network/\nsrc_dir = parent_dir.parent          # .../src/\n\n# 添加src目录到Python路径，确保可以找到network包\nif str(src_dir) not in sys.path:\n    sys.path.insert(0, str(src_dir))\n\ntry:\n    # 尝试相对导入（作为包运行时）\n    from .network_manager import NetworkManager\n    from .cellular_control import CellularModuleController\nexcept ImportError:\n    try:\n        # 尝试从network.network导入（从src目录）\n        from network.network.network_manager import NetworkManager\n        from network.network.cellular_control import CellularModuleController\n    except ImportError:\n        # 最后尝试直接导入（当前目录）\n        sys.path.insert(0, str(current_dir))\n        from network_manager import NetworkManager\n        from cellular_control import CellularModuleController\n\nclass InternetStartNode(Node):\n    def _setup_logging(self):\n        \"\"\"配置日志级别 - 支持多种方式配置\n        优先级：命令行参数 > 环境变量 > 配置文件参数 > 默认值(INFO)\n        \"\"\"\n        import sys\n\n        # 默认日志级别\n        log_level = \"INFO\"\n\n        # 1. 检查命令行参数 (最高优先级)\n        if '--log-level' in sys.argv:\n            try:\n                idx = sys.argv.index('--log-level')\n                if idx + 1 < len(sys.argv):\n                    log_level = sys.argv[idx + 1].upper()\n                    self.get_logger().info(f\"📝 使用命令行参数日志级别: {log_level}\")\n            except (IndexError, ValueError):\n                pass\n\n        # 2. 检查环境变量\n        elif 'ROS_LOG_LEVEL' in os.environ:\n            log_level = os.environ['ROS_LOG_LEVEL'].upper()\n            self.get_logger().info(f\"📝 使用环境变量日志级别: {log_level}\")\n\n        # 3. 配置文件参数会在后面的declare_parameter中处理\n\n        # 设置日志级别\n        level_map = {\n            'DEBUG': rclpy.logging.LoggingSeverity.DEBUG,\n            'INFO': rclpy.logging.LoggingSeverity.INFO,\n            'WARN': rclpy.logging.LoggingSeverity.WARN,\n            'WARNING': rclpy.logging.LoggingSeverity.WARN,\n            'ERROR': rclpy.logging.LoggingSeverity.ERROR,\n            'FATAL': rclpy.logging.LoggingSeverity.FATAL\n        }\n\n        if log_level in level_map:\n            self.get_logger().set_level(level_map[log_level])\n            self.current_log_level = log_level\n        else:\n            self.get_logger().set_level(rclpy.logging.LoggingSeverity.INFO)\n            self.current_log_level = \"INFO\"\n            self.get_logger().warn(f\"⚠️  无效的日志级别 '{log_level}'，使用默认值 INFO\")\n\n    def __init__(self):\n        super().__init__('network_node')\n\n        # 配置日志级别 - 支持多种方式\n        self._setup_logging()\n\n        self.get_logger().info(\"初始化 networkNode 节点\")\n\n        # 检测调试模式\n        self.debug_mode = os.environ.get('NETWORK_DEBUG_MODE', '0') == '1'\n        self.test_mode = os.environ.get('NETWORK_TEST_MODE', '0') == '1'\n\n        if self.debug_mode:\n            self.get_logger().info(\"🐛 调试模式已启用\")\n        if self.test_mode:\n            self.get_logger().info(\"🧪 测试模式已启用 - 将模拟硬件操作\")\n\n        # 声明日志级别参数 (配置文件参数，优先级第3)\n        self.declare_parameter('log_level', self.current_log_level)\n        config_log_level = self.get_parameter('log_level').value.upper()\n\n        # 如果配置文件中的日志级别与当前不同，且没有命令行参数和环境变量，则使用配置文件的\n        if (config_log_level != self.current_log_level and\n            '--log-level' not in sys.argv and\n            'ROS_LOG_LEVEL' not in os.environ):\n\n            level_map = {\n                'DEBUG': rclpy.logging.LoggingSeverity.DEBUG,\n                'INFO': rclpy.logging.LoggingSeverity.INFO,\n                'WARN': rclpy.logging.LoggingSeverity.WARN,\n                'WARNING': rclpy.logging.LoggingSeverity.WARN,\n                'ERROR': rclpy.logging.LoggingSeverity.ERROR,\n                'FATAL': rclpy.logging.LoggingSeverity.FATAL\n            }\n\n            if config_log_level in level_map:\n                self.get_logger().set_level(level_map[config_log_level])\n                self.current_log_level = config_log_level\n                self.get_logger().info(f\"📝 使用配置文件日志级别: {config_log_level}\")\n\n        self.get_logger().info(f\"📊 当前日志级别: {self.current_log_level}\")\n\n        self.declare_parameter('cellular_option', \"enable\")\n        self.cellular_controller = CellularModuleController(node=self, logger=self.get_logger())\n\n        # self.declare_parameter('device_type', \"ysc\")\n        self.declare_parameter('device_type', \"unitree\")\n        self.device_type = self.get_parameter('device_type').value\n        \n        self.declare_parameter('wifi_connect_interface', \"wlan0\")\n        self.wifi_interface = self.get_parameter('wifi_connect_interface').value\n        self.declare_parameter('mobile_connect_interface', \"eth1\")\n        self.mobile_interface = self.get_parameter('mobile_connect_interface').value\n        self.declare_parameter('ethernet_interface', \"eth0\")\n        self.ethernet_interface = self.get_parameter('ethernet_interface').value\n        self.declare_parameter('p2p_connect_interface', \"wlan1\")\n        self.ap_interface = self.get_parameter('p2p_connect_interface').value\n        self.declare_parameter('timer_interval', 20)  # 定时器时间间隔\n        self.timer_interval = self.get_parameter('timer_interval').value\n\n        self.declare_parameter('static_ip', '***********')  # 默认静态 IP 地址\n        self.declare_parameter('ssid', 'xiaoli99')  # 默认 SSID\n        self.static_ip = self.get_parameter('static_ip').get_parameter_value().string_value\n\n        # DNS服务器配置参数\n        self.declare_parameter('dns_primary_servers', [\"*********\", \"************\", \"***************\"])\n        self.declare_parameter('dns_backup_servers', [\"************\", \"*******\", \"*******\"])\n        self.dns_primary_servers = self.get_parameter('dns_primary_servers').value\n        self.dns_backup_servers = self.get_parameter('dns_backup_servers').value\n\n        self.declare_parameter('jwae_tproxy', \"enable\")\n        self.jwae_tproxy_mode=self.get_parameter('jwae_tproxy').get_parameter_value().string_value\n\n        # 网络状态配置文件路径参数\n        self.declare_parameter('network_state_config_file', \"/etc/cmcc_robot/network_state.conf\")\n        self.declare_parameter('network_state_lock_file', \"/var/lock/network_state.lock\")\n        self.network_state_config_file = self.get_parameter('network_state_config_file').value\n        self.network_state_lock_file = self.get_parameter('network_state_lock_file').value\n\n        # 检查并安装必要的网络工具\n        self.check_and_install_network_tools()\n        \n        if self.device_type == \"unitree\":\n            # 初始化蜂窝网络模块\n            self._init_cellular_module()\n\n        # init ssid\n        config_file_path = '/etc/cmcc_robot/cmcc_dev.ini'\n        devNO = None\n        devSn = None\n        if os.path.exists(config_file_path):\n            cmcc_config = configparser.ConfigParser()\n            try:\n                cmcc_config.read(config_file_path)\n                devNO = cmcc_config.get('factory', 'devNo', fallback=None)\n                devSn = cmcc_config.get('factory', 'devSn', fallback=None)\n            except configparser.Error as e:\n                self.get_logger().info(f\"Error: Failed to read configuration file '{config_file_path}'. {e}\")\n        if devNO is None:\n            if devSn is not None:\n                devNO = devSn[-5:] if len(devSn) >= 5 else '99'\n            else:\n                devNO = \"99\"\n        self.ssid = f\"xiaoli{devNO}\"\n        self.get_logger().info(f\"配置的AP接口: {self.ap_interface}  静态 IP 地址: {self.static_ip}\")\n\n        self.last_freq = None  # 用于记录上一次的频段\n\n        self.network_manager = NetworkManager(\n            node=self,\n            wifi_interface=self.wifi_interface,\n            mobile_interface=self.mobile_interface,\n            ap_interface=self.ap_interface,\n            logger=self.get_logger(),\n            config_file_path=self.network_state_config_file,\n            lock_file_path=self.network_state_lock_file\n        )  # 移除 dhcp_service 参数\n\n        # self.network_manager.run_ap_start()\n        # self.timer = self.create_timer(self.timer_interval, self.network_manager.update_ap_name)\n        # self.get_logger().info(f\"定时器已启动，每 {self.timer_interval} 秒更新 AP 名称\")\n        self.network_manager.run_ap_stop()\n\n        # === 创建服务 ===\n        self.network_control_service = self.create_service(\n            NetCtrl,\n            '/homi_speech/network_service',\n            self.handle_network_control_request\n        )\n\n        self.robot_control_service = self.create_service(\n            SIGCData,\n            '/homi_speech/sigc_data_service_APP',\n            self.handle_robot_control_request\n        )\n\n        # === 创建发布者 ===\n        self.network_status_publisher = self.create_publisher(String, 'network_status', 10)\n        self.network_conflict_publisher = self.create_publisher(String, 'network_conflict', 10)\n        self.dns_health_publisher = self.create_publisher(String, 'dns_health_status', 10)\n\n        self.get_logger().info(\"✅ 网络服务和发布者已初始化\")\n\n        # === 设备特定配置 ===\n        if self.device_type == \"unitree\":\n            self.network_manager.run_command(f\"nmcli dev set {self.wifi_interface} managed no\")\n        else:\n            self.network_manager.run_command(f\"nmcli dev set {self.wifi_interface} managed yes\")\n\n        self.network_manager.jwae_tproxy_mode=self.jwae_tproxy_mode\n        if self.jwae_tproxy_mode == \"enable\":\n            self.get_logger().info(\"启动tproxy和jwae进程\")\n            self.network_manager.run_command(\"sudo bash /usr/bin/cmcc_robot/install/env/jwae_tproxy/stop_jwae_tproxy.sh\")\n            self.network_manager.run_command(\"sudo bash /usr/bin/cmcc_robot/install/env/jwae_tproxy/tproxy_rule.sh start\")\n            self.network_manager.run_command(\"sudo bash /usr/bin/cmcc_robot/install/env/jwae_tproxy/start_jwae.sh\")\n            \n            # 读取配置，键不存在时返回默认值\"on\"\n            wifi_state = self.network_manager.config.get(\"wifiState\", \"on\")\n            mobile_state = self.network_manager.config.get(\"mobileDataState\", \"on\")\n\n            # 特殊逻辑：初始化上电时，当两个状态都为 \"off\" 时，强制设置为 \"enable\"\n            if wifi_state == \"off\" and mobile_state == \"off\":\n                wifi_value = \"enable\"\n                mobile_value = \"enable\"\n                self.get_logger().info(\"检测到WiFi和移动数据同时关闭,强制启用状态\")\n            else:\n                # 正常状态转换逻辑\n                wifi_value = \"enable\" if wifi_state == \"on\" else \"disable\"\n                mobile_value = \"enable\" if mobile_state == \"on\" else \"disable\"\n\n            self.get_logger().info(f\"最终网络状态: WiFi={wifi_value}, 移动数据={mobile_value}\")\n\n            # 使用pycurl替换原有curl命令\n            buffer = BytesIO()\n            curl = pycurl.Curl()\n            \n            try:\n                # 设置请求参数\n                post_data = json.dumps({\"wifi\": wifi_value, \"mobile\": mobile_value})\n                curl.setopt(pycurl.URL, 'http://127.0.0.1:12000/setNetworkStatus')\n                curl.setopt(pycurl.POST, 1)\n                curl.setopt(pycurl.POSTFIELDS, post_data)\n                curl.setopt(pycurl.HTTPHEADER, ['Content-Type: application/json'])\n                curl.setopt(pycurl.WRITEFUNCTION, buffer.write)\n                \n                # 设置超时时间（单位：秒）\n                curl.setopt(pycurl.TIMEOUT, 10)\n                \n                # 执行请求\n                curl.perform()\n                \n                # 获取响应\n                status_code = curl.getinfo(pycurl.RESPONSE_CODE)\n                response = buffer.getvalue().decode()\n                \n                # 处理响应\n                if status_code == 200:\n                    try:\n                        response_data = json.loads(response)\n                        if response_data.get(\"result\") == \"success\":\n                            self.get_logger().info(\"首次jwae_tproxy开启时setNetworkStatus wifi和mobile从内存中读取状态\")\n                        else:\n                            self.get_logger().error(f\"setNetworkStatus返回失败: HTTP {status_code}, 响应: {response}\")\n                    except Exception as e:\n                        self.get_logger().error(f\"解析响应失败: {str(e)}, 原始响应: {response}\")\n                else:\n                    self.get_logger().error(f\"请求失败: HTTP 状态码 {status_code}\")\n            \n            except pycurl.error as e:\n                errno, errstr = e.args\n                self.get_logger().error(f\"PyCurl请求失败: ({errno}) {errstr}\")\n            \n            finally:\n                # 清理资源\n                curl.close()\n                buffer.close()\n\n        else:\n            # self.get_logger().info(\"jwae_tproxy模式未启用,跳过相关进程启动\")\n            # self.network_manager.run_command(\"sudo bash /usr/bin/cmcc_robot/install/env/jwae_tproxy/stop_jwae_tproxy.sh\")        \n\n\n        # === 启动网络监控 ===\n        # 初始网络状态检查（同步）\n        self.network_manager.check_network_status()\n\n        # 创建异步网络检查定时器\n        self.network_check_timer = self.create_timer(self.timer_interval, self.async_check_network_status)\n        self.get_logger().info(f\"异步网络检查定时器已启动，每 {self.timer_interval} 秒检查网络状态\")\n\n        # 注意：DNS健康检查已集成到网络状态检查中，无需独立的DNS定时器\n        # 网络状态检查每20秒执行，当发现连接异常时会自动执行DNS健康检查和修复\n\n        if self.device_type != \"unitree\":\n            self.network_manager.start_dbus_monitor()\n            self.get_logger().info(\"启动 DBus 网络状态监控线程\")\n\n        # 冲突检测相关状态\n        self.conflict_detected = False\n        self.last_conflict_check = 0\n        self.conflict_check_interval = 30  # 检测间隔30秒\n        self.wlan0_has_ip = False  # 记录wlan0是否有IP\n        self.wlan0_ip_detected = False  # 记录wlan0获得IP后是否已检测\n\n        self.bind_mode = False  # 绑定模式标志\n\n        self.bind_subscriber = self.create_subscription(\n            String,\n            'andlink_network',\n            self.bind_notify_callback,\n            10\n        )\n        self.get_logger().info(\"已创建绑定通知订阅者\")\n\n        # 初始化线程池用于异步网络检查\n        self.thread_executor = ThreadPoolExecutor(max_workers=2, thread_name_prefix=\"network_check\")\n        self.network_check_running = False  # 标记是否有网络检查正在运行\n        self.get_logger().info(\"已初始化异步网络检查线程池\")\n\n    def _init_cellular_module(self):\n        \"\"\"根据参数初始化蜂窝网络模块\"\"\"\n        self.cellular_option = self.get_parameter('cellular_option').value\n        self.get_logger().info(f\"蜂窝网络模块配置选项: {self.cellular_option}\")\n\n        if self.cellular_option == \"enable\":\n            self.get_logger().info(\"正在初始化蜂窝网络模块...\")\n            if self.cellular_controller.initialize():\n                self.get_logger().info(\"蜂窝网络模块启动成功\")\n            else:\n                self.get_logger().error(\"蜂窝网络模块初始化失败,请检查硬件连接\")\n        else:\n            self.get_logger().info(\"蜂窝网络模块禁用,跳过初始化\")\n\n    def async_check_network_status(self):\n        \"\"\"异步网络状态检查入口\"\"\"\n        if self.network_check_running:\n            self.get_logger().debug(\"网络检查正在运行中，跳过本次检查\")\n            return\n\n        if self.bind_mode:\n            self.get_logger().debug(\"绑定模式中，跳过网络状态检测\")\n            return\n\n        # 提交异步任务\n        future = self.thread_executor.submit(self._async_network_check_worker)\n        future.add_done_callback(self._network_check_completed)\n        self.network_check_running = True\n        self.get_logger().debug(\"已提交异步网络检查任务\")\n\n    def _async_network_check_worker(self):\n        \"\"\"异步网络检查工作线程\"\"\"\n        try:\n            self.get_logger().debug(\"开始异步网络状态检查\")\n            start_time = time.time()\n\n            # 执行网络状态检查\n            self.network_manager.check_network_status()\n\n            elapsed_time = time.time() - start_time\n            self.get_logger().debug(f\"异步网络状态检查完成，耗时: {elapsed_time:.2f}秒\")\n            return True\n\n        except Exception as e:\n            self.get_logger().error(f\"异步网络状态检查失败: {e}\")\n            return False\n\n    def _network_check_completed(self, future):\n        \"\"\"网络检查完成回调\"\"\"\n        try:\n            result = future.result()\n            if result:\n                self.get_logger().debug(\"异步网络状态检查成功完成\")\n            else:\n                self.get_logger().warning(\"异步网络状态检查失败\")\n        except Exception as e:\n            self.get_logger().error(f\"异步网络状态检查异常: {e}\")\n        finally:\n            self.network_check_running = False\n\n    def bind_notify_callback(self, msg):\n        \"\"\"处理绑定通知的回调函数\"\"\"\n        if msg.data == \"notify_userbind_start\":\n            self.get_logger().info(\"接收到 notify_userbind_start 消息，暂停网络状态检测\")\n            self.bind_mode = True\n            self.network_manager.bind_mode = True\n\n        elif msg.data == \"notify_userbind_end\":\n            self.get_logger().info(\"接收到 notify_userbind_end 消息，恢复网络状态检测\")\n            self.bind_mode = False\n            self.network_manager.bind_mode = False\n\n            # 恢复后立即检查一次网络状态（异步）\n            self.async_check_network_status()\n\n    def destroy_node(self):\n        \"\"\"节点销毁时的清理工作\"\"\"\n        try:\n            self.get_logger().info(\"正在关闭异步网络检查线程池...\")\n            self.thread_executor.shutdown(wait=True, timeout=10)\n            self.get_logger().info(\"异步网络检查线程池已关闭\")\n        except Exception as e:\n            self.get_logger().error(f\"关闭线程池时发生错误: {e}\")\n        finally:\n            super().destroy_node()\n\n    def pause_timer(self):\n        \"\"\"暂停网络状态检查定时器\"\"\"\n        if hasattr(self, 'timer') and self.timer is not None:\n            self.timer.cancel()\n            self.get_logger().info(\"网络状态检查定时器已暂停\")\n            self.timer = None\n\n    def resume_timer(self):\n        \"\"\"恢复网络状态检查定时器\"\"\"\n        if not hasattr(self, 'timer') or self.timer is None:\n            self.timer = self.create_timer(\n                self.timer_interval, self.async_check_network_status\n            )\n            self.get_logger().info(f\"异步网络状态检查定时器已恢复，每 {self.timer_interval} 秒检查一次\")\n\n    def _manage_ap_timer(self, start=True):\n        \"\"\"统一管理AP定时器\"\"\"\n        if hasattr(self, 'ap_timer'):\n            if self.ap_timer is not None:\n                self.ap_timer.cancel()\n                self.ap_timer = None\n                \n        if start:\n            self.ap_timer = self.create_timer(self.timer_interval, self.network_manager.update_ap_name)\n            self.get_logger().info(f\"AP名称更新定时器已启动，每 {self.timer_interval} 秒更新一次\")\n\n    def handle_network_control_request(self, request, response):\n        \"\"\"\n        处理网络控制请求\n\n        Args:\n            request: 网络控制请求\n            response: 网络控制响应\n\n        Returns:\n            response: 处理后的响应\n        \"\"\"\n        self.get_logger().info(f\"处理网络控制请求: {request.data}\")\n        return self.network_manager.handle_network_status(request, response)\n\n    def handle_robot_control_request(self, request, response):\n        \"\"\"\n        处理机器人控制请求（AP开关控制）\n\n        Args:\n            request: 机器人控制请求\n            response: 机器人控制响应\n\n        Returns:\n            response: 处理后的响应\n        \"\"\"\n        self.get_logger().info(f\"处理机器人控制请求: {request.data}\")\n\n        try:\n            if request.data == \"openap_notify\":\n                self.get_logger().info(\"收到开启AP请求，执行AP启动序列\")\n                self.network_manager.run_ap_start()\n                self._manage_ap_timer(start=True)\n                response.error_code = 0\n\n            elif request.data == \"closeap_notify\":\n                self.get_logger().info(\"收到关闭AP请求，执行AP关闭序列\")\n                self.network_manager.run_ap_stop()\n                self._manage_ap_timer(start=False)\n                response.error_code = 0\n\n            else:\n                self.get_logger().warning(f\"未处理的机器人控制请求: {request.data}\")\n                response.error_code = 1\n\n        except Exception as e:\n            self.get_logger().error(f\"处理机器人控制请求时发生错误: {e}\")\n            response.error_code = 1\n\n        return response\n\n    def check_network_conflict(self):\n        \"\"\"\n        优化后的网络冲突检测：只当WiFi接口有IP时检测一次\n\n        检测WiFi接口和移动网络接口是否在同一子网，避免网络冲突\n        \"\"\"\n        current_time = time.time()\n        # self.get_logger().info(\"###### enter check network conflict!!!\")\n        wifi_ip, wifi_netmask = self.get_interface_network(self.wifi_interface)\n        has_ip_now = wifi_ip is not None\n\n        if not self.wlan0_has_ip and has_ip_now:\n            self.get_logger().info(f\"{self.wifi_interface} 获得IP地址，准备进行冲突检测\")\n            self.wlan0_ip_detected = False  # 新IP，需要检测\n            self.conflict_detected = False  # 重置冲突状态\n\n        self.wlan0_has_ip = has_ip_now\n        \n\n        if not has_ip_now:\n            return  # 没有IP，跳过检测\n            \n        if self.wlan0_ip_detected:\n            return\n            \n        # 只有在满足时间间隔时才进行检测\n        if current_time - self.last_conflict_check < self.conflict_check_interval:\n            return\n            \n        self.last_conflict_check = current_time\n        \n        try:\n            # 获取有线以太网接口的IP信息\n            ethernet_ip, ethernet_netmask = self.get_interface_network(self.ethernet_interface)\n            if not ethernet_ip or not ethernet_netmask:\n                self.get_logger().info(f\"{self.ethernet_interface} 没有有效的IP地址，跳过冲突检测\")\n                return\n\n            ethernet_net_addr = self.calculate_network_address(ethernet_ip, ethernet_netmask)\n            wifi_net_addr = self.calculate_network_address(wifi_ip, wifi_netmask)\n\n            # 检查WiFi和有线以太网是否在同一个子网\n            if ethernet_net_addr == wifi_net_addr:\n                self.get_logger().warning(f\"网络冲突: {self.wifi_interface} 和 {self.ethernet_interface} 在同一子网 ({wifi_ip} vs {ethernet_ip})\")\n\n                self.publish_conflict_notification()\n                self.conflict_detected = True\n            else:\n                self.conflict_detected = False\n\n            # 标记为已检测\n            self.wlan0_ip_detected = True\n            \n        except Exception as e:\n            self.get_logger().error(f\"网络冲突检测失败: {e}\")\n        finally:\n            self.wlan0_ip_detected = True\n\n    def publish_conflict_notification(self):\n        \"\"\"\n        发布网络冲突通知\n\n        当WiFi和有线以太网接口在同一子网时发布冲突通知\n        \"\"\"\n        msg = String()\n        msg.data = f\"network_conflict_notify_{self.wifi_interface}_{self.ethernet_interface}\"\n        self.network_conflict_publisher.publish(msg)\n        self.get_logger().warning(f\"已发布网络冲突通知消息: WiFi({self.wifi_interface}) 与 以太网({self.ethernet_interface}) 冲突\")\n\n    # DNS健康检查已集成到网络状态检查中，移除独立的DNS检查方法\n    # 当网络连接异常时，check_external_connectivity()会自动执行DNS健康检查和修复\n\n    def publish_dns_status(self, dns_report: dict):\n        \"\"\"发布DNS状态信息\"\"\"\n        try:\n            # 简化DNS状态信息用于发布\n            status_info = {\n                \"current_dns\": dns_report.get(\"current_dns\"),\n                \"failure_count\": dns_report.get(\"failure_count\", 0),\n                \"last_check\": dns_report.get(\"last_check_time\", 0),\n                \"status\": \"healthy\" if dns_report.get(\"failure_count\", 0) < 3 else \"degraded\"\n            }\n            \n            msg = String()\n            msg.data = json.dumps(status_info)\n            self.dns_health_publisher.publish(msg)\n            self.get_logger().info(f\"已发布DNS健康状态: {status_info}\")\n            \n        except Exception as e:\n            self.get_logger().error(f\"发布DNS状态时发生错误: {e}\")\n\n    @staticmethod\n    def get_interface_network(interface):\n        \"\"\"获取接口的IP和子网掩码\"\"\"\n        try:\n            cmd = [\"ip\", \"-o\", \"-4\", \"addr\", \"show\", \"dev\", interface]\n            result = subprocess.run(cmd, capture_output=True, text=True, check=True)\n            \n            if result.returncode == 0 and \"inet\" in result.stdout:\n                line = result.stdout.split(\"\\n\")[0]\n                parts = line.split()\n                ip_with_mask = parts[3]\n                \n                if \"/\" in ip_with_mask:\n                    ip, mask_bits = ip_with_mask.split(\"/\")\n                    mask_bits = int(mask_bits)\n                    \n                    mask = (0xffffffff >> (32 - mask_bits)) << (32 - mask_bits)\n                    netmask = socket.inet_ntoa(struct.pack(\">I\", mask))\n                    \n                    return ip, netmask\n        except Exception as e:\n            return None, None\n        return None, None\n\n    @staticmethod\n    def calculate_network_address(ip, netmask):\n        \"\"\"计算网络地址\"\"\"\n        ip_int = int.from_bytes(socket.inet_aton(ip), byteorder=\"big\")\n        mask_int = int.from_bytes(socket.inet_aton(netmask), byteorder=\"big\")\n        network_addr = ip_int & mask_int\n        return network_addr\n\n    def check_and_install_network_tools(self):\n        \"\"\"检查并安装网络相关工具包，如 iputils-ping 和 dnsutils\"\"\"\n        required_packages = ['iputils-ping', 'dnsutils']\n        missing_packages = []\n        \n        self.get_logger().info(\"检查网络工具包是否安装...\")\n        \n        for package in required_packages:\n            try:\n                # 使用dpkg检查软件包是否已安装\n                result = subprocess.run(\n                    [\"dpkg\", \"-s\", package], \n                    stdout=subprocess.PIPE, \n                    stderr=subprocess.PIPE, \n                    text=True\n                )\n                \n                if result.returncode != 0:\n                    self.get_logger().info(f\"未安装软件包: {package}\")\n                    missing_packages.append(package)\n                else:\n                    self.get_logger().info(f\"软件包已安装: {package}\")\n                    \n            except Exception as e:\n                self.get_logger().error(f\"检查软件包 {package} 时出错: {e}\")\n                missing_packages.append(package)  # 检查失败时也将其标记为缺失\n        \n        # 如果有缺失的软件包，则安装\n        if missing_packages:\n            try:\n                self.get_logger().info(f\"开始安装缺失的软件包: {', '.join(missing_packages)}\")\n                \n                # 使用非交互模式的apt安装\n                cmd = [\"sudo\", \"DEBIAN_FRONTEND=noninteractive\", \"apt-get\", \"update\", \"-y\"]\n                update_result = subprocess.run(\n                    cmd,\n                    stdout=subprocess.PIPE,\n                    stderr=subprocess.PIPE,\n                    text=True\n                )\n                \n                if update_result.returncode != 0:\n                    self.get_logger().error(f\"更新软件包仓库失败: {update_result.stderr}\")\n                \n                # 安装所有缺失的软件包\n                install_cmd = [\"sudo\", \"DEBIAN_FRONTEND=noninteractive\", \"apt-get\", \"install\", \"-y\"] + missing_packages\n                install_result = subprocess.run(\n                    install_cmd,\n                    stdout=subprocess.PIPE,\n                    stderr=subprocess.PIPE,\n                    text=True\n                )\n                \n                if install_result.returncode == 0:\n                    self.get_logger().info(f\"成功安装所有缺失的软件包: {', '.join(missing_packages)}\")\n                else:\n                    self.get_logger().error(f\"安装软件包失败: {install_result.stderr}\")\n                    \n            except Exception as e:\n                self.get_logger().error(f\"安装缺失软件包时出错: {e}\")\n        else:\n            self.get_logger().info(\"所有必要的网络工具包均已安装\")\n\ndef main(args=None):\n    global node\n    rclpy.init(args=args)\n    node = InternetStartNode()\n\n    conflict_timer = node.create_timer(5, node.check_network_conflict)\n\n    try:\n        rclpy.spin(node)\n    except KeyboardInterrupt:\n        node.get_logger().info(\"检测到 KeyboardInterrupt, 正在关闭节点...\")\n    finally:\n        pass\n\nif __name__ == \"__main__\":\n    main()"}]}