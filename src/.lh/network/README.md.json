{"sourceFile": "network/README.md", "activeCommit": 0, "commits": [{"activePatchIndex": 13, "patches": [{"date": 1754272810973, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1754272824349, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -57,8 +57,10 @@\n | 命令 | 功能描述 | 必需参数 | 可选参数 |\n |------|---------|---------|---------|\n | `getNetworkStatus` | 获取当前网络状态 | 无 | 无 |\n | `setNetworkStatus` | 设置网络开关状态 | 无 | `networkStatus` |\n+| `getWifiSetStatus` | 获取WiFi配置状态 | 无 | 无 |\n+| `setWifiSetStatus` | 设置WiFi配置状态 | `status` | 无 |\n | `getDnsStatus` | 获取DNS状态信息 | 无 | 无 |\n | `switchDnsServer` | 切换DNS服务器 | `dnsServer` | 无 |\n | `refreshDnsServers` | 刷新并选择最佳DNS | 无 | 无 |\n | `getNetworkDiagnostic` | 获取网络诊断信息 | 无 | `interface` |\n"}, {"date": 1754272840388, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -102,8 +102,63 @@\n # 只设置移动数据状态（WiFi状态保持当前状态）\n ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl \"{data: '{\\\"command\\\": \\\"setNetworkStatus\\\", \\\"networkStatus\\\": {\\\"mobileDataState\\\": \\\"on\\\"}}'}\"\n ```\n \n+#### WiFi配置状态命令详细说明\n+\n+**getWifiSetStatus命令**：\n+获取当前WiFi配置状态，用于判断用户是否已经配置过WiFi连接。\n+\n+**参数格式**：\n+```json\n+{\n+  \"command\": \"getWifiSetStatus\"\n+}\n+```\n+\n+**返回示例**：\n+```json\n+{\n+  \"success\": true,\n+  \"data\": {\n+    \"wifiSetStatus\": 0,\n+    \"statusText\": \"未配置\"\n+  }\n+}\n+```\n+\n+**setWifiSetStatus命令**：\n+设置WiFi配置状态，用于标记WiFi是否已经配置。\n+\n+**参数格式**：\n+```json\n+{\n+  \"command\": \"setWifiSetStatus\",\n+  \"status\": 0  // 0=未配置, 1=已配置\n+}\n+```\n+\n+**使用限制**：\n+- `status` 参数必须是 0 或 1\n+- 0 表示未配置状态\n+- 1 表示已配置状态\n+\n+**示例用法**：\n+```bash\n+# 获取WiFi配置状态\n+ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl \"{data: '{\\\"command\\\": \\\"getWifiSetStatus\\\"}'}\"\n+\n+# 设置WiFi为已配置状态\n+ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl \"{data: '{\\\"command\\\": \\\"setWifiSetStatus\\\", \\\"status\\\": 1}'}\"\n+\n+# 设置WiFi为未配置状态\n+ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl \"{data: '{\\\"command\\\": \\\"setWifiSetStatus\\\", \\\"status\\\": 0}'}\"\n+```\n+\n+**自动更新机制**：\n+- 当WiFi成功连接时，系统会自动将 `wifiSetStatus` 设置为 1（已配置）\n+- 可以手动设置为 0 来重置配置状态，便于重新引导用户配置WiFi\n+\n ## ⚙️ 配置参数\n \n ### 网络接口配置\n ```yaml\n"}, {"date": 1754272849814, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -280,8 +280,14 @@\n \n # 获取绑定状态\n ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl \"{data: '{\\\"command\\\": \\\"getBindStatus\\\"}'}\"\n \n+# 获取WiFi配置状态\n+ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl \"{data: '{\\\"command\\\": \\\"getWifiSetStatus\\\"}'}\"\n+\n+# 设置WiFi配置状态\n+ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl \"{data: '{\\\"command\\\": \\\"setWifiSetStatus\\\", \\\"status\\\": 1}'}\"\n+\n # 刷新DNS服务器\n ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl \"{data: '{\\\"command\\\": \\\"refreshDnsServers\\\"}'}\"\n \n # 获取网络诊断信息\n"}, {"date": 1754272865137, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -426,17 +426,25 @@\n - [VS Code调试配置](../../../.vscode/launch.json)\n \n ## 📅 版本信息\n \n-- **版本**: v2.1.0 (文档更新版本)\n+- **版本**: v2.2.0 (WiFi配置状态增强版本)\n - **创建日期**: 2025-01-22\n-- **最后更新**: 2025-07-24\n-- **兼容性**: ROS2 Humble\n+- **最后更新**: 2025-01-23\n+- **兼容性**: ROS2 Foxy/Humble\n - **维护状态**: 🟢 活跃维护\n-- **重要更新**: ✅ 节点定义和测试文档更新\n+- **重要更新**: ✅ 新增WiFi配置状态字段和相关接口\n \n ## 🚀 最新更新\n \n+### v2.2.0 - WiFi配置状态增强版本 (2025-01-23)\n+- ✅ **新增 wifiSetStatus 字段**: 添加WiFi配置状态跟踪功能\n+- ✅ **新增服务命令**: 添加 `getWifiSetStatus` 和 `setWifiSetStatus` 命令\n+- ✅ **自动状态更新**: WiFi连接成功时自动设置为已配置状态\n+- ✅ **向后兼容**: 自动迁移旧配置文件，添加新字段\n+- ✅ **配置持久化**: 状态信息持久化存储到配置文件\n+- ✅ **完整文档**: 提供详细的使用说明和测试示例\n+\n ### v2.1.0 - 文档更新版本 (2025-07-24)\n - ✅ **ROS2接口文档更新**: 修正了服务名称和接口定义\n - ✅ **服务调用参数修正**: 更新了NetCtrl服务的正确JSON格式调用方式\n - ✅ **完整命令文档**: 添加了所有支持的NetCtrl命令示例，包括setNetworkStatus\n"}, {"date": 1754273101173, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -59,8 +59,15 @@\n | `getNetworkStatus` | 获取当前网络状态 | 无 | 无 |\n | `setNetworkStatus` | 设置网络开关状态 | 无 | `networkStatus` |\n | `getWifiSetStatus` | 获取WiFi配置状态 | 无 | 无 |\n | `setWifiSetStatus` | 设置WiFi配置状态 | `status` | 无 |\n+| `scanWifiNetworks` | 扫描可用WiFi网络 | 无 | 无 |\n+| `connectWifi` | 连接到指定WiFi网络 | `ssid` | `password` |\n+| `disconnectWifi` | 断开当前WiFi连接 | 无 | 无 |\n+| `getWifiConnectionStatus` | 获取WiFi连接状态 | 无 | 无 |\n+| `getWifiHardwareStatus` | 获取WiFi硬件状态 | 无 | 无 |\n+| `getWifiScanStatus` | 获取WiFi扫描状态 | 无 | 无 |\n+| `clearWifiScanCache` | 清除WiFi扫描缓存 | 无 | 无 |\n | `getDnsStatus` | 获取DNS状态信息 | 无 | 无 |\n | `switchDnsServer` | 切换DNS服务器 | `dnsServer` | 无 |\n | `refreshDnsServers` | 刷新并选择最佳DNS | 无 | 无 |\n | `getNetworkDiagnostic` | 获取网络诊断信息 | 无 | `interface` |\n"}, {"date": 1754273125827, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -164,8 +164,124 @@\n **自动更新机制**：\n - 当WiFi成功连接时，系统会自动将 `wifiSetStatus` 设置为 1（已配置）\n - 可以手动设置为 0 来重置配置状态，便于重新引导用户配置WiFi\n \n+#### WiFi扫描和连接命令详细说明\n+\n+**scanWifiNetworks命令**：\n+扫描附近可用的WiFi网络，支持异步扫描和缓存机制。\n+\n+**参数格式**：\n+```json\n+{\n+  \"command\": \"scanWifiNetworks\"\n+}\n+```\n+\n+**返回示例**：\n+```json\n+{\n+  \"status\": \"completed\",\n+  \"code\": 200,\n+  \"networks\": [\n+    {\n+      \"ssid\": \"MyWiFi\",\n+      \"bssid\": \"00:11:22:33:44:55\",\n+      \"signal_strength\": \"85%\",\n+      \"signal_level\": -35,\n+      \"frequency\": \"2437 MHz\",\n+      \"channel\": \"6\",\n+      \"security\": \"WPA2\",\n+      \"rate\": \"54 Mbps\"\n+    }\n+  ],\n+  \"count\": 1,\n+  \"cached\": false,\n+  \"scan_time\": 3.2,\n+  \"message\": \"扫描完成\"\n+}\n+```\n+\n+**connectWifi命令**：\n+连接到指定的WiFi网络，支持开放网络和加密网络。\n+\n+**参数格式**：\n+```json\n+{\n+  \"command\": \"connectWifi\",\n+  \"ssid\": \"MyWiFi\",\n+  \"password\": \"mypassword\"  // 可选，开放网络时不需要\n+}\n+```\n+\n+**返回示例**：\n+```json\n+{\n+  \"success\": true,\n+  \"message\": \"成功连接到 MyWiFi\",\n+  \"error_code\": 200,\n+  \"already_connected\": false\n+}\n+```\n+\n+**disconnectWifi命令**：\n+断开当前WiFi连接。\n+\n+**参数格式**：\n+```json\n+{\n+  \"command\": \"disconnectWifi\"\n+}\n+```\n+\n+**getWifiConnectionStatus命令**：\n+获取当前WiFi连接的详细状态信息。\n+\n+**返回示例**：\n+```json\n+{\n+  \"interface\": \"wlp1s0\",\n+  \"current_ssid\": \"MyWiFi\",\n+  \"signal_strength\": \"85%\",\n+  \"ip_address\": \"*************\",\n+  \"mac_address\": \"aa:bb:cc:dd:ee:ff\",\n+  \"connection_status\": \"connected\"\n+}\n+```\n+\n+**getWifiScanStatus命令**：\n+获取WiFi扫描的当前状态，用于检查是否正在扫描或缓存是否有效。\n+\n+**clearWifiScanCache命令**：\n+清除WiFi扫描缓存，强制下次扫描时重新获取网络列表。\n+\n+**示例用法**：\n+```bash\n+# 扫描WiFi网络\n+ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl \"{data: '{\\\"command\\\": \\\"scanWifiNetworks\\\"}'}\"\n+\n+# 连接WiFi网络（有密码）\n+ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl \"{data: '{\\\"command\\\": \\\"connectWifi\\\", \\\"ssid\\\": \\\"MyWiFi\\\", \\\"password\\\": \\\"mypassword\\\"}'}\"\n+\n+# 连接WiFi网络（开放网络）\n+ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl \"{data: '{\\\"command\\\": \\\"connectWifi\\\", \\\"ssid\\\": \\\"OpenWiFi\\\"}'}\"\n+\n+# 断开WiFi连接\n+ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl \"{data: '{\\\"command\\\": \\\"disconnectWifi\\\"}'}\"\n+\n+# 获取WiFi连接状态\n+ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl \"{data: '{\\\"command\\\": \\\"getWifiConnectionStatus\\\"}'}\"\n+\n+# 获取WiFi硬件状态\n+ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl \"{data: '{\\\"command\\\": \\\"getWifiHardwareStatus\\\"}'}\"\n+\n+# 获取WiFi扫描状态\n+ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl \"{data: '{\\\"command\\\": \\\"getWifiScanStatus\\\"}'}\"\n+\n+# 清除WiFi扫描缓存\n+ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl \"{data: '{\\\"command\\\": \\\"clearWifiScanCache\\\"}'}\"\n+```\n+\n ## ⚙️ 配置参数\n \n ### 网络接口配置\n ```yaml\n"}, {"date": 1754273136297, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -409,8 +409,20 @@\n \n # 设置WiFi配置状态\n ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl \"{data: '{\\\"command\\\": \\\"setWifiSetStatus\\\", \\\"status\\\": 1}'}\"\n \n+# 扫描WiFi网络\n+ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl \"{data: '{\\\"command\\\": \\\"scanWifiNetworks\\\"}'}\"\n+\n+# 连接WiFi网络\n+ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl \"{data: '{\\\"command\\\": \\\"connectWifi\\\", \\\"ssid\\\": \\\"MyWiFi\\\", \\\"password\\\": \\\"mypassword\\\"}'}\"\n+\n+# 断开WiFi连接\n+ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl \"{data: '{\\\"command\\\": \\\"disconnectWifi\\\"}'}\"\n+\n+# 获取WiFi连接状态\n+ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl \"{data: '{\\\"command\\\": \\\"getWifiConnectionStatus\\\"}'}\"\n+\n # 刷新DNS服务器\n ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl \"{data: '{\\\"command\\\": \\\"refreshDnsServers\\\"}'}\"\n \n # 获取网络诊断信息\n"}, {"date": 1754273149620, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -573,8 +573,11 @@\n \n ### v2.2.0 - WiFi配置状态增强版本 (2025-01-23)\n - ✅ **新增 wifiSetStatus 字段**: 添加WiFi配置状态跟踪功能\n - ✅ **新增服务命令**: 添加 `getWifiSetStatus` 和 `setWifiSetStatus` 命令\n+- ✅ **WiFi扫描和连接**: 完善 `scanWifiNetworks`、`connectWifi`、`disconnectWifi` 等命令文档\n+- ✅ **WiFi状态查询**: 添加 `getWifiConnectionStatus`、`getWifiHardwareStatus` 等命令说明\n+- ✅ **扫描缓存管理**: 添加 `getWifiScanStatus`、`clearWifiScanCache` 命令文档\n - ✅ **自动状态更新**: WiFi连接成功时自动设置为已配置状态\n - ✅ **向后兼容**: 自动迁移旧配置文件，添加新字段\n - ✅ **配置持久化**: 状态信息持久化存储到配置文件\n - ✅ **完整文档**: 提供详细的使用说明和测试示例\n"}, {"date": 1754276418438, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -167,9 +167,9 @@\n \n #### WiFi扫描和连接命令详细说明\n \n **scanWifiNetworks命令**：\n-扫描附近可用的WiFi网络，支持异步扫描和缓存机制。\n+扫描附近可用的WiFi网络，支持异步扫描和缓存机制。如果WiFi处于关闭状态，会自动开启WiFi进行扫描。\n \n **参数格式**：\n ```json\n {\n"}, {"date": 1754276428909, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -196,12 +196,18 @@\n   ],\n   \"count\": 1,\n   \"cached\": false,\n   \"scan_time\": 3.2,\n-  \"message\": \"扫描完成\"\n+  \"message\": \"扫描完成\",\n+  \"wifi_auto_enabled\": false,\n+  \"wifi_status_changed\": \"WiFi已从关闭状态自动开启以进行扫描\"\n }\n ```\n \n+**字段说明**：\n+- `wifi_auto_enabled`: 布尔值，表示是否自动开启了WiFi\n+- `wifi_status_changed`: 字符串，当WiFi被自动开启时的说明信息\n+\n **connectWifi命令**：\n 连接到指定的WiFi网络，支持开放网络和加密网络。\n \n **参数格式**：\n"}, {"date": 1754276445402, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -582,8 +582,9 @@\n - ✅ **新增服务命令**: 添加 `getWifiSetStatus` 和 `setWifiSetStatus` 命令\n - ✅ **WiFi扫描和连接**: 完善 `scanWifiNetworks`、`connectWifi`、`disconnectWifi` 等命令文档\n - ✅ **WiFi状态查询**: 添加 `getWifiConnectionStatus`、`getWifiHardwareStatus` 等命令说明\n - ✅ **扫描缓存管理**: 添加 `getWifiScanStatus`、`clearWifiScanCache` 命令文档\n+- ✅ **智能WiFi扫描**: `scanWifiNetworks` 命令自动检测并开启关闭的WiFi\n - ✅ **自动状态更新**: WiFi连接成功时自动设置为已配置状态\n - ✅ **向后兼容**: 自动迁移旧配置文件，添加新字段\n - ✅ **配置持久化**: 状态信息持久化存储到配置文件\n - ✅ **完整文档**: 提供详细的使用说明和测试示例\n"}, {"date": 1754283076272, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -265,9 +265,9 @@\n # 扫描WiFi网络\n ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl \"{data: '{\\\"command\\\": \\\"scanWifiNetworks\\\"}'}\"\n \n # 连接WiFi网络（有密码）\n-ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl \"{data: '{\\\"command\\\": \\\"connectWifi\\\", \\\"ssid\\\": \\\"MyWiFi\\\", \\\"password\\\": \\\"mypassword\\\"}'}\"\n+ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl \"{data: '{\\\"command\\\": \\\"connectWifi\\\", \\\"ssid\\\": \\\"CMCC-533c-5G\\\", \\\"password\\\": \\\"12345678\\\"}'}\"\n \n # 连接WiFi网络（开放网络）\n ros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl \"{data: '{\\\"command\\\": \\\"connectWifi\\\", \\\"ssid\\\": \\\"OpenWiFi\\\"}'}\"\n \n"}, {"date": 1754287653548, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -454,9 +454,9 @@\n \n ### 发布者功能测试\n ```bash\n # 监听网络状态话题\n-ros2 topic echo /internet_connect_status\n+ros2 topic echo /network_status\n \n # 监听网络冲突信息\n ros2 topic echo /internet_conflict\n \n"}], "date": 1754272810973, "name": "Commit-0", "content": "# Network模块文档\n\n## 📋 模块概述\n\nNetwork模块是xiaoli_application_ros2项目的核心网络管理组件，负责机器人的网络连接管理、状态监控和用户绑定控制。\n\n## 🏗️ 架构设计\n\n```\nNetwork模块\n├── network_node.py          # 主节点 - ROS2节点入口\n├── network_manager.py       # 网络管理器 - 核心业务逻辑\n├── cellular_control.py      # 蜂窝网络控制 - 4G/5G模块管理\n└── dbus_monitor.py         # D-Bus监控 - 系统网络事件监听\n```\n\n## 🔧 核心功能\n\n### 1. 网络连接管理\n- **WiFi连接**: 自动连接和管理WiFi网络\n- **AP热点**: 创建和管理接入点模式\n- **蜂窝网络**: 4G/5G模块控制和管理\n- **网络冲突检测**: 防止多网络接口冲突\n\n### 2. 状态监控\n- **实时网络状态**: 监控所有网络接口状态\n- **DNS状态检测**: 检测DNS服务器可用性\n- **互联网连通性**: 验证外网连接状态\n- **IP地址管理**: 动态IP分配和静态IP配置\n\n### 3. 用户绑定控制\n- **绑定模式**: 支持用户绑定过程中的网络控制\n- **状态暂停/恢复**: 在绑定过程中暂停网络检测\n- **消息订阅**: 通过ROS2话题接收控制指令\n\n## 📡 ROS2接口\n\n### 订阅话题\n| 话题名称 | 消息类型 | 功能描述 | 队列大小 |\n|---------|---------|---------|---------|\n| `/andlink_network` | std_msgs/msg/String | 用户绑定控制消息 | 10 |\n\n### 发布话题\n| 话题名称 | 消息类型 | 功能描述 | 发布频率 |\n|---------|---------|---------|---------|\n| `/dns_status` | std_msgs/msg/String | DNS服务器状态 | 5分钟间隔 |\n| `/internet_conflict` | std_msgs/msg/String | 网络冲突信息 | 事件触发 |\n| `/internet_connect_status` | std_msgs/msg/String | 互联网连接状态 | 定时检查(20秒间隔) |\n\n### 服务接口\n| 服务名称 | 服务类型 | 功能描述 | 实际服务名 |\n|---------|---------|---------|---------|\n| 网络控制服务 | homi_speech_interface/srv/NetCtrl | 网络状态查询和控制 | `/homi_speech/network_service` |\n| 机器人控制服务 | homi_speech_interface/srv/SIGCData | AP热点开关控制 | `/homi_speech/robot_control_service` |\n\n#### 网络控制服务支持的命令\n| 命令 | 功能描述 | 必需参数 | 可选参数 |\n|------|---------|---------|---------|\n| `getNetworkStatus` | 获取当前网络状态 | 无 | 无 |\n| `setNetworkStatus` | 设置网络开关状态 | 无 | `networkStatus` |\n| `getDnsStatus` | 获取DNS状态信息 | 无 | 无 |\n| `switchDnsServer` | 切换DNS服务器 | `dnsServer` | 无 |\n| `refreshDnsServers` | 刷新并选择最佳DNS | 无 | 无 |\n| `getNetworkDiagnostic` | 获取网络诊断信息 | 无 | `interface` |\n| `performDnsHealthCheck` | 执行DNS健康检查 | 无 | `interface` |\n| `getBindStatus` | 获取用户绑定状态 | 无 | 无 |\n\n#### setNetworkStatus命令详细说明\n`setNetworkStatus`命令用于控制WiFi和移动数据的开关状态：\n\n**参数格式**：\n```json\n{\n  \"command\": \"setNetworkStatus\",\n  \"networkStatus\": {\n    \"wifiState\": \"on|off\",      // 可选，缺失时使用当前状态\n    \"mobileDataState\": \"on|off\" // 可选，缺失时使用当前状态\n  }\n}\n```\n\n**使用限制**：\n- `wifiState`和`mobileDataState`不能同时为\"off\"\n- 参数可以只提供其中一个，缺失的参数会从当前状态自动补全\n\n**示例用法**：\n```bash\n# 开启WiFi，关闭移动数据\nros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl \"{data: '{\\\"command\\\": \\\"setNetworkStatus\\\", \\\"networkStatus\\\": {\\\"wifiState\\\": \\\"on\\\", \\\"mobileDataState\\\": \\\"off\\\"}}'}\"\n\n# 关闭WiFi，开启移动数据\nros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl \"{data: '{\\\"command\\\": \\\"setNetworkStatus\\\", \\\"networkStatus\\\": {\\\"wifiState\\\": \\\"off\\\", \\\"mobileDataState\\\": \\\"on\\\"}}'}\"\n\n# 同时开启WiFi和移动数据\nros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl \"{data: '{\\\"command\\\": \\\"setNetworkStatus\\\", \\\"networkStatus\\\": {\\\"wifiState\\\": \\\"on\\\", \\\"mobileDataState\\\": \\\"on\\\"}}'}\"\n\n# 只设置WiFi状态（移动数据状态保持当前状态）\nros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl \"{data: '{\\\"command\\\": \\\"setNetworkStatus\\\", \\\"networkStatus\\\": {\\\"wifiState\\\": \\\"off\\\"}}'}\"\n\n# 只设置移动数据状态（WiFi状态保持当前状态）\nros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl \"{data: '{\\\"command\\\": \\\"setNetworkStatus\\\", \\\"networkStatus\\\": {\\\"mobileDataState\\\": \\\"on\\\"}}'}\"\n```\n\n## ⚙️ 配置参数\n\n### 网络接口配置\n```yaml\nnetwork_node:\n  ros__parameters:\n    wifi_connect_interface: \"wlp1s0\"        # WiFi接口\n    p2p_connect_interface: \"wlan1\"          # AP热点接口\n    mobile_connect_interface: \"enx00e04c6801d0\"  # 蜂窝网络接口\n    ssid: \"xiaoli51\"                        # AP热点名称\n    static_ip: \"***********\"                # 静态IP地址\n    device_type: \"yes\"                      # 设备类型\n    cellular_option: \"disable\"              # 蜂窝网络选项\n    timer_interval: 20                      # 定时器间隔(秒)\n    log_level: \"INFO\"                       # 日志级别\n```\n\n### DNS服务器配置\n```yaml\n    dns_primary_servers: [\"*********\", \"************\", \"***************\"]\n    dns_backup_servers: [\"************\", \"*******\", \"*******\"]\n```\n\n## 🚀 使用方法\n\n### 启动网络节点\n```bash\n# 方法1: 使用调试脚本（推荐）\ncd xiaoli_application_ros2\n./debug_network_node.sh --test\n\n# 方法2: 直接运行\npython3 src/network/network/network_node.py\n\n# 方法3: 使用launch文件\nros2 launch launch_package robot_launch.py\n```\n\n### 配置日志级别\n```bash\n# 命令行参数\n./debug_network_node.sh --log-level DEBUG\n\n# 环境变量\nexport ROS_LOG_LEVEL=DEBUG\n./debug_network_node.sh\n\n# 配置文件\n# 修改 src/launch_package/configs/robot_config.yaml 中的 log_level 参数\n```\n\n## 📊 网络状态字段说明\n\n### 🔧 开关状态 vs 连接状态\n\nNetwork模块v2.0.0引入了**开关状态**和**连接状态**的明确分离：\n\n| 字段名 | 类型 | 分类 | 说明 | 示例值 |\n|--------|------|------|------|--------|\n| `wifiState` | string | **开关状态** | WiFi无线电开关状态 | \"on\"/\"off\" |\n| `mobileDataState` | string | **开关状态** | 移动数据开关状态 | \"on\"/\"off\" |\n| `wifiSetStatus` | int | **配置状态** | WiFi配置状态 | 0/1 (0=未配置, 1=已配置) |\n| `isWifiConnect` | string | **连接状态** | WiFi实际连接并可用状态 | \"true\"/\"false\" |\n| `isInternetConnect` | string | **连接状态** | 整体互联网连接状态 | \"true\"/\"false\" |\n| `wifiName` | string | **连接信息** | 当前连接的WiFi名称 | \"MyWiFi\" |\n| `userBind` | string | **系统状态** | 用户是否绑定 | \"true\"/\"false\" |\n\n### 🎯 状态逻辑说明\n\n- **开关状态**: 表示硬件开关是否开启，通过`nmcli radio wifi`等命令检测\n- **连接状态**: 表示实际网络连接并能访问外网，通过接口状态+外网连通性检测\n- **独立性**: 开关开启不代表连接成功，连接成功必须开关开启\n\n### 📋 状态组合示例\n\n| WiFi开关 | WiFi连接 | 移动数据开关 | 互联网连接 | 说明 |\n|----------|----------|--------------|------------|------|\n| on | true | on | true | 双网络正常 |\n| on | false | on | true | WiFi断开，移动网络正常 |\n| off | false | on | true | WiFi关闭，移动网络正常 |\n| on | true | off | true | 移动网络关闭，WiFi正常 |\n| off | false | off | false | 所有网络关闭 |\n\n## 🧪 测试和验证\n\n### 快速测试脚本\n```bash\n# 一键检查所有网络话题状态\n./src/network/test/check_network_topics.sh\n\n# 验证订阅消息功能\npython3 src/network/test/verify_network_subscriptions.py\n\n# 测试服务和发布者功能\npython3 src/network/test/test_services_and_publishers.py\n```\n\n### 订阅消息测试\n```bash\n# 手动测试绑定控制消息\nros2 topic pub --once /andlink_network std_msgs/msg/String '{data: \"notify_userbind_start\"}'\nros2 topic pub --once /andlink_network std_msgs/msg/String '{data: \"notify_userbind_end\"}'\n\n# 验证订阅者是否正常工作\nros2 topic echo /andlink_network &\nros2 topic pub --once /andlink_network std_msgs/msg/String '{data: \"test_message\"}'\n```\n\n### 服务接口测试\n```bash\n# 检查服务状态\nros2 service list | grep -E \"(network|homi_speech)\"\n\n# 测试网络控制服务（需要JSON格式的数据）\n# 获取网络状态\nros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl \"{data: '{\\\"command\\\": \\\"getNetworkStatus\\\"}'}\"\n\n# 获取DNS状态\nros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl \"{data: '{\\\"command\\\": \\\"getDnsStatus\\\"}'}\"\n\n# 获取绑定状态\nros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl \"{data: '{\\\"command\\\": \\\"getBindStatus\\\"}'}\"\n\n# 刷新DNS服务器\nros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl \"{data: '{\\\"command\\\": \\\"refreshDnsServers\\\"}'}\"\n\n# 获取网络诊断信息\nros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl \"{data: '{\\\"command\\\": \\\"getNetworkDiagnostic\\\", \\\"interface\\\": \\\"wlp1s0\\\"}'}\"\n\n# 设置网络状态（开启/关闭WiFi或移动数据）\nros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl \"{data: '{\\\"command\\\": \\\"setNetworkStatus\\\", \\\"networkStatus\\\": {\\\"wifiState\\\": \\\"on\\\", \\\"mobileDataState\\\": \\\"off\\\"}}'}\"\n\n# 切换DNS服务器\nros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl \"{data: '{\\\"command\\\": \\\"switchDnsServer\\\", \\\"dnsServer\\\": \\\"*******\\\"}'}\"\n\n# 执行DNS健康检查\nros2 service call /homi_speech/network_service homi_speech_interface/srv/NetCtrl \"{data: '{\\\"command\\\": \\\"performDnsHealthCheck\\\", \\\"interface\\\": \\\"wlp1s0\\\"}'}\"\n\n# 测试AP控制服务（机器人控制服务）\nros2 service call /homi_speech/robot_control_service homi_speech_interface/srv/SIGCData \"{data: 'openap_notify'}\"\nros2 service call /homi_speech/robot_control_service homi_speech_interface/srv/SIGCData \"{data: 'closeap_notify'}\"\n\n# 检查服务接口定义\nros2 interface show homi_speech_interface/srv/NetCtrl\nros2 interface show homi_speech_interface/srv/SIGCData\n```\n\n### 发布者功能测试\n```bash\n# 监听网络状态话题\nros2 topic echo /internet_connect_status\n\n# 监听网络冲突信息\nros2 topic echo /internet_conflict\n\n# 监听DNS状态\nros2 topic echo /dns_status\n\n# 检查话题发布频率\nros2 topic hz /internet_connect_status\nros2 topic hz /dns_status\n```\n\n### 调试工具\n```bash\n# 查看节点状态\nros2 node list | grep network\n\n# 检查话题信息\nros2 topic info /internet_connect_status\nros2 topic info /dns_status\nros2 topic info /internet_conflict\n\n# 检查服务信息\nros2 service type /homi_speech/network_service\nros2 service type /homi_speech/robot_control_service\n\n# 查看节点详细信息\nros2 node info /network_node\n\n# 检查话题发布频率\nros2 topic hz /internet_connect_status\nros2 topic hz /dns_status\n```\n\n### 可用测试文件\n```bash\n# 测试文件目录: src/network/test/\n├── check_network_topics.sh              # 网络话题检查脚本\n├── verify_network_subscriptions.py      # 订阅消息验证\n├── test_services_and_publishers.py      # 服务和发布者测试\n├── test_network_messages.py             # 网络消息测试\n├── test_network_services.sh             # 网络服务测试脚本\n├── test_async_performance.py            # 异步性能测试\n├── test_dns_async.py                    # DNS异步测试\n├── test_interface_control.py            # 接口控制测试\n├── test_network_conflict.py             # 网络冲突测试\n├── test_state_fields.py                 # 状态字段测试\n└── test_wifi_switch.py                  # WiFi开关测试\n\n# 运行特定测试\npython3 src/network/test/test_async_performance.py\npython3 src/network/test/test_dns_async.py\npython3 src/network/test/test_interface_control.py\n```\n\n## 🔍 故障排除\n\n### 常见问题\n\n1. **网络接口未找到**\n   - 检查接口名称配置是否正确\n   - 确认硬件设备已连接\n\n2. **蜂窝模块初始化失败**\n   - 检查硬件连接\n   - 确认cellular_option配置\n   - 查看详细错误日志\n\n3. **DNS解析失败**\n   - 检查DNS服务器配置\n   - 验证网络连通性\n   - 尝试更换DNS服务器\n\n### 日志分析\n```bash\n# 查看实时日志\nros2 topic echo /rosout\n\n# 设置DEBUG级别查看详细日志\n./debug_network_node.sh --log-level DEBUG\n```\n\n## 📚 相关文档\n\n- [服务和发布者测试文档](test/SERVICE_AND_PUBLISHER_TESTING.md)\n- [调试配置指南](../../DEBUG_README.md)\n- [VS Code调试配置](../../../.vscode/launch.json)\n\n## 🔄 开发指南\n\n### 添加新功能\n1. 在相应的模块文件中添加功能代码\n2. 更新配置参数（如需要）\n3. 添加相应的测试用例\n4. 更新文档\n\n### 代码规范\n- 使用Python类型提示\n- 添加详细的文档字符串\n- 遵循ROS2编码规范\n- 使用适当的日志级别\n\n## 🔗 相关文档\n\n- [服务和发布者测试文档](test/SERVICE_AND_PUBLISHER_TESTING.md)\n- [网络话题检查脚本](test/check_network_topics.sh)\n- [订阅消息验证脚本](test/verify_network_subscriptions.py)\n- [调试配置指南](../../DEBUG_README.md)\n- [VS Code调试配置](../../../.vscode/launch.json)\n\n## 📅 版本信息\n\n- **版本**: v2.1.0 (文档更新版本)\n- **创建日期**: 2025-01-22\n- **最后更新**: 2025-07-24\n- **兼容性**: ROS2 Humble\n- **维护状态**: 🟢 活跃维护\n- **重要更新**: ✅ 节点定义和测试文档更新\n\n## 🚀 最新更新\n\n### v2.1.0 - 文档更新版本 (2025-07-24)\n- ✅ **ROS2接口文档更新**: 修正了服务名称和接口定义\n- ✅ **服务调用参数修正**: 更新了NetCtrl服务的正确JSON格式调用方式\n- ✅ **完整命令文档**: 添加了所有支持的NetCtrl命令示例，包括setNetworkStatus\n- ✅ **setNetworkStatus智能补全**: 改进了参数处理逻辑，缺失参数自动从当前状态补全\n- ✅ **setNetworkStatus详细说明**: 提供了网络状态设置的完整使用指南\n- ✅ **测试文档完善**: 添加了完整的测试文件列表和使用方法\n- ✅ **服务名称修正**: 更新了实际的服务名称映射\n- ✅ **测试脚本整理**: 提供了所有可用测试脚本的详细说明\n- ✅ **调试工具增强**: 添加了更多调试和验证命令\n\n### v2.0.0 - 异步优化版本 (2025-01-22)\n- ✅ **异步网络检查**: 网络状态检查不再阻塞ROS2主线程\n- ✅ **性能提升**: 服务响应时间显著改善\n- ✅ **线程池管理**: 使用ThreadPoolExecutor管理异步任务\n- ✅ **完整监控**: 提供异步操作的详细日志\n- ✅ **向后兼容**: 保持所有现有功能不变\n\n---\n\n**注意**:\n- 本模块是机器人系统的核心网络组件，修改时请谨慎测试\n- 异步优化已在生产环境验证通过\n- 所有ROS2接口定义已更新至最新版本\n- 建议在修改代码前运行完整的测试套件验证功能正常\n"}]}