{"sourceFile": "ble/ble/exception_manager.py", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1754305879044, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1754305879044, "name": "Commit-0", "content": "#!/usr/bin/env python3\n# -*- coding: utf-8 -*-\n\n\"\"\"\n异常管理器模块\n用于BLE和Network节点的异常处理、监控和恢复机制\n\"\"\"\n\nimport time\nimport threading\nimport traceback\nfrom enum import Enum\nfrom typing import Dict, Optional, Callable, Any\nfrom dataclasses import dataclass\nfrom datetime import datetime, timedelta\nimport json\n\n\nclass ExceptionType(Enum):\n    \"\"\"异常类型枚举\"\"\"\n    # BLE相关异常\n    BLE_SEND_ERROR = \"ble_send_error\"\n    BLE_RECEIVE_ERROR = \"ble_receive_error\"\n    BLE_CONNECTION_ERROR = \"ble_connection_error\"\n    BLE_ADVERTISEMENT_ERROR = \"ble_advertisement_error\"\n    BLE_DEVICE_ERROR = \"ble_device_error\"\n    \n    # WiFi相关异常\n    WIFI_CONNECTION_ERROR = \"wifi_connection_error\"\n    WIFI_SCAN_ERROR = \"wifi_scan_error\"\n    WIFI_AUTHENTICATION_ERROR = \"wifi_authentication_error\"\n    WIFI_INTERFACE_ERROR = \"wifi_interface_error\"\n    \n    # 网络相关异常\n    NETWORK_INTERFACE_ERROR = \"network_interface_error\"\n    NETWORK_CONNECTIVITY_ERROR = \"network_connectivity_error\"\n    DNS_RESOLUTION_ERROR = \"dns_resolution_error\"\n    \n    # 系统相关异常\n    SYSTEM_RESOURCE_ERROR = \"system_resource_error\"\n    CONFIG_FILE_ERROR = \"config_file_error\"\n    SERVICE_ERROR = \"service_error\"\n\n\nclass ExceptionSeverity(Enum):\n    \"\"\"异常严重程度\"\"\"\n    LOW = 1      # 低：不影响主要功能\n    MEDIUM = 2   # 中：影响部分功能\n    HIGH = 3     # 高：影响主要功能\n    CRITICAL = 4 # 严重：系统无法正常工作\n\n\n@dataclass\nclass ExceptionRecord:\n    \"\"\"异常记录\"\"\"\n    exception_type: ExceptionType\n    severity: ExceptionSeverity\n    message: str\n    timestamp: datetime\n    stack_trace: Optional[str] = None\n    context: Optional[Dict[str, Any]] = None\n    recovery_attempted: bool = False\n    recovery_successful: bool = False\n    count: int = 1\n\n\nclass ExceptionManager:\n    \"\"\"异常管理器\"\"\"\n    \n    def __init__(self, logger, node_name: str = \"unknown\"):\n        self.logger = logger\n        self.node_name = node_name\n        self.exceptions: Dict[str, ExceptionRecord] = {}\n        self.exception_handlers: Dict[ExceptionType, Callable] = {}\n        self.recovery_strategies: Dict[ExceptionType, Callable] = {}\n        \n        # 异常统计\n        self.stats = {\n            \"total_exceptions\": 0,\n            \"recoveries_attempted\": 0,\n            \"recoveries_successful\": 0,\n            \"critical_exceptions\": 0\n        }\n        \n        # 异常阈值配置\n        self.thresholds = {\n            ExceptionType.BLE_SEND_ERROR: 5,           # 5次发送错误\n            ExceptionType.BLE_RECEIVE_ERROR: 10,       # 10次接收错误\n            ExceptionType.WIFI_CONNECTION_ERROR: 3,    # 3次连接错误\n            ExceptionType.NETWORK_CONNECTIVITY_ERROR: 5 # 5次网络连接错误\n        }\n        \n        # 时间窗口配置（秒）\n        self.time_windows = {\n            ExceptionType.BLE_SEND_ERROR: 300,         # 5分钟\n            ExceptionType.BLE_RECEIVE_ERROR: 600,      # 10分钟\n            ExceptionType.WIFI_CONNECTION_ERROR: 1800, # 30分钟\n            ExceptionType.NETWORK_CONNECTIVITY_ERROR: 900 # 15分钟\n        }\n        \n        # 异常清理线程\n        self.cleanup_thread = threading.Thread(target=self._cleanup_old_exceptions, daemon=True)\n        self.cleanup_thread.start()\n        \n        self.logger.info(f\"异常管理器已初始化 - 节点: {node_name}\")\n\n    def register_handler(self, exception_type: ExceptionType, handler: Callable):\n        \"\"\"注册异常处理器\"\"\"\n        self.exception_handlers[exception_type] = handler\n        self.logger.debug(f\"已注册异常处理器: {exception_type.value}\")\n\n    def register_recovery_strategy(self, exception_type: ExceptionType, strategy: Callable):\n        \"\"\"注册恢复策略\"\"\"\n        self.recovery_strategies[exception_type] = strategy\n        self.logger.debug(f\"已注册恢复策略: {exception_type.value}\")\n\n    def report_exception(self, \n                        exception_type: ExceptionType, \n                        message: str,\n                        severity: ExceptionSeverity = ExceptionSeverity.MEDIUM,\n                        context: Optional[Dict[str, Any]] = None,\n                        stack_trace: Optional[str] = None) -> bool:\n        \"\"\"\n        报告异常\n        \n        Returns:\n            bool: 是否需要进行恢复操作\n        \"\"\"\n        try:\n            current_time = datetime.now()\n            exception_key = f\"{exception_type.value}_{current_time.strftime('%Y%m%d_%H')}\"\n            \n            # 更新或创建异常记录\n            if exception_key in self.exceptions:\n                record = self.exceptions[exception_key]\n                record.count += 1\n                record.timestamp = current_time\n                record.message = message  # 更新为最新消息\n                if context:\n                    record.context = context\n            else:\n                record = ExceptionRecord(\n                    exception_type=exception_type,\n                    severity=severity,\n                    message=message,\n                    timestamp=current_time,\n                    stack_trace=stack_trace,\n                    context=context\n                )\n                self.exceptions[exception_key] = record\n            \n            # 更新统计\n            self.stats[\"total_exceptions\"] += 1\n            if severity == ExceptionSeverity.CRITICAL:\n                self.stats[\"critical_exceptions\"] += 1\n            \n            # 记录日志\n            self._log_exception(record)\n            \n            # 调用异常处理器\n            if exception_type in self.exception_handlers:\n                try:\n                    self.exception_handlers[exception_type](record)\n                except Exception as e:\n                    self.logger.error(f\"异常处理器执行失败: {e}\")\n            \n            # 检查是否需要恢复\n            need_recovery = self._should_attempt_recovery(exception_type, record)\n            \n            if need_recovery:\n                self._attempt_recovery(exception_type, record)\n            \n            return need_recovery\n            \n        except Exception as e:\n            self.logger.error(f\"报告异常时发生错误: {e}\")\n            return False\n\n    def _log_exception(self, record: ExceptionRecord):\n        \"\"\"记录异常日志\"\"\"\n        severity_map = {\n            ExceptionSeverity.LOW: \"DEBUG\",\n            ExceptionSeverity.MEDIUM: \"WARN\",\n            ExceptionSeverity.HIGH: \"ERROR\",\n            ExceptionSeverity.CRITICAL: \"ERROR\"\n        }\n        \n        log_level = severity_map.get(record.severity, \"ERROR\")\n        log_message = (\n            f\"🚨 [{record.exception_type.value}] {record.message} \"\n            f\"(计数: {record.count}, 严重程度: {record.severity.name})\"\n        )\n        \n        if record.context:\n            log_message += f\" 上下文: {json.dumps(record.context, ensure_ascii=False)}\"\n        \n        if log_level == \"DEBUG\":\n            self.logger.debug(log_message)\n        elif log_level == \"WARN\":\n            self.logger.warning(log_message)\n        else:\n            self.logger.error(log_message)\n            \n        if record.stack_trace and record.severity in [ExceptionSeverity.HIGH, ExceptionSeverity.CRITICAL]:\n            self.logger.error(f\"堆栈跟踪: {record.stack_trace}\")\n\n    def _should_attempt_recovery(self, exception_type: ExceptionType, record: ExceptionRecord) -> bool:\n        \"\"\"判断是否应该尝试恢复\"\"\"\n        # 严重异常立即尝试恢复\n        if record.severity == ExceptionSeverity.CRITICAL:\n            return True\n        \n        # 检查异常计数是否超过阈值\n        threshold = self.thresholds.get(exception_type, 10)\n        if record.count >= threshold:\n            return True\n        \n        # 检查时间窗口内的异常频率\n        time_window = self.time_windows.get(exception_type, 3600)  # 默认1小时\n        cutoff_time = datetime.now() - timedelta(seconds=time_window)\n        \n        recent_count = sum(\n            1 for r in self.exceptions.values()\n            if r.exception_type == exception_type and r.timestamp > cutoff_time\n        )\n        \n        return recent_count >= threshold\n\n    def _attempt_recovery(self, exception_type: ExceptionType, record: ExceptionRecord):\n        \"\"\"尝试恢复\"\"\"\n        if record.recovery_attempted:\n            self.logger.debug(f\"异常 {exception_type.value} 已尝试过恢复，跳过\")\n            return\n        \n        record.recovery_attempted = True\n        self.stats[\"recoveries_attempted\"] += 1\n        \n        if exception_type in self.recovery_strategies:\n            try:\n                self.logger.info(f\"🔧 尝试恢复异常: {exception_type.value}\")\n                success = self.recovery_strategies[exception_type](record)\n                \n                if success:\n                    record.recovery_successful = True\n                    self.stats[\"recoveries_successful\"] += 1\n                    self.logger.info(f\"✅ 异常恢复成功: {exception_type.value}\")\n                else:\n                    self.logger.warning(f\"❌ 异常恢复失败: {exception_type.value}\")\n                    \n            except Exception as e:\n                self.logger.error(f\"恢复策略执行失败: {e}\")\n                self.logger.error(f\"恢复策略堆栈: {traceback.format_exc()}\")\n        else:\n            self.logger.warning(f\"未找到恢复策略: {exception_type.value}\")\n\n    def get_exception_stats(self) -> Dict[str, Any]:\n        \"\"\"获取异常统计信息\"\"\"\n        recent_exceptions = {}\n        cutoff_time = datetime.now() - timedelta(hours=1)\n        \n        for record in self.exceptions.values():\n            if record.timestamp > cutoff_time:\n                exception_type = record.exception_type.value\n                if exception_type not in recent_exceptions:\n                    recent_exceptions[exception_type] = 0\n                recent_exceptions[exception_type] += record.count\n        \n        return {\n            \"node_name\": self.node_name,\n            \"stats\": self.stats.copy(),\n            \"recent_exceptions\": recent_exceptions,\n            \"active_exceptions\": len(self.exceptions),\n            \"recovery_rate\": (\n                self.stats[\"recoveries_successful\"] / max(1, self.stats[\"recoveries_attempted\"])\n            ) * 100\n        }\n\n    def clear_exceptions(self, exception_type: Optional[ExceptionType] = None):\n        \"\"\"清除异常记录\"\"\"\n        if exception_type:\n            keys_to_remove = [\n                key for key, record in self.exceptions.items()\n                if record.exception_type == exception_type\n            ]\n            for key in keys_to_remove:\n                del self.exceptions[key]\n            self.logger.info(f\"已清除异常类型: {exception_type.value}\")\n        else:\n            self.exceptions.clear()\n            self.logger.info(\"已清除所有异常记录\")\n\n    def _cleanup_old_exceptions(self):\n        \"\"\"清理旧的异常记录\"\"\"\n        while True:\n            try:\n                time.sleep(3600)  # 每小时清理一次\n                cutoff_time = datetime.now() - timedelta(hours=24)  # 保留24小时内的记录\n                \n                keys_to_remove = [\n                    key for key, record in self.exceptions.items()\n                    if record.timestamp < cutoff_time\n                ]\n                \n                for key in keys_to_remove:\n                    del self.exceptions[key]\n                \n                if keys_to_remove:\n                    self.logger.debug(f\"清理了 {len(keys_to_remove)} 条过期异常记录\")\n                    \n            except Exception as e:\n                self.logger.error(f\"清理异常记录时发生错误: {e}\")\n\n\nclass ExceptionDecorator:\n    \"\"\"异常装饰器，用于自动捕获和报告异常\"\"\"\n    \n    def __init__(self, exception_manager: ExceptionManager, \n                 exception_type: ExceptionType,\n                 severity: ExceptionSeverity = ExceptionSeverity.MEDIUM):\n        self.exception_manager = exception_manager\n        self.exception_type = exception_type\n        self.severity = severity\n\n    def __call__(self, func):\n        def wrapper(*args, **kwargs):\n            try:\n                return func(*args, **kwargs)\n            except Exception as e:\n                self.exception_manager.report_exception(\n                    exception_type=self.exception_type,\n                    message=f\"函数 {func.__name__} 执行失败: {str(e)}\",\n                    severity=self.severity,\n                    stack_trace=traceback.format_exc(),\n                    context={\n                        \"function\": func.__name__,\n                        \"args\": str(args)[:200],  # 限制长度\n                        \"kwargs\": str(kwargs)[:200]\n                    }\n                )\n                raise\n        return wrapper "}]}