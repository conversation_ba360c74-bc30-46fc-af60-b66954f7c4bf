{"sourceFile": "ble/ble/ble_node.py", "activeCommit": 0, "commits": [{"activePatchIndex": 3, "patches": [{"date": 1754306061659, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1754306073931, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -56,8 +56,13 @@\n         # self.start_ble_bind_timer()\n         # self.start_ble_bind_timer()\n \n         self.user_bind_timer = self.create_timer(5.0, self.update_advertisement)  # 每5秒更新一次广播\n+        \n+        # 初始化异常管理器\n+        self.exception_manager = ExceptionManager(self.get_logger(), \"ble_node\")\n+        self.ble_exception_handler = BLEExceptionHandler(self, self.exception_manager)\n+        \n         self.get_logger().info(\"ByteMultiArrayPublisher initialized successfully.\")\n         self.config_path = '/etc/cmcc_robot/andlinkSdk.conf'  # 配置文件路径\n         self.last_user_bind = self.get_user_bind()   # 保存上一次的 user_bind 状态\n \n"}, {"date": 1754306090713, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -94,20 +94,29 @@\n         time.sleep(2)\n         self.publish_andlink_cmd_reset()\n \n     def listener_ble_cmd_callback(self, msg):\n-        self.get_logger().info(f\"Received BLE command: {list(msg.data)}\")\n-        self.get_logger().info(f'Received ByteMultiArray: {list(msg.data)}')\n-        if list(msg.data)[0] == b'U' and list(msg.data)[1] == b'U' and list(msg.data)[2] == b'\\x00':\n-            self.get_logger().info(\"Valid BLE command received.\")\n-            if list(msg.data)[3] == b'\\x09':\n-                self.get_logger().info(\"Reset command detected. Executing reset sequence.\")\n-                self.process_reset_command(\"remove_new\")\n-            elif list(msg.data)[3] == b'\\x05':\n-                self.get_logger().info('收到andlink unbroadcast消息, 关闭广播01, 改为广播03, self.andlink_reset = False.')\n-                self.andlink_reset = False\n-                self.ble_manager.update_advertisement(True)\n-            elif list(msg.data)[3] == b'\\x04':\n+        try:\n+            self.get_logger().info(f\"Received BLE command: {list(msg.data)}\")\n+            self.get_logger().info(f'Received ByteMultiArray: {list(msg.data)}')\n+            \n+            # 报告成功接收\n+            if hasattr(self, 'ble_exception_handler'):\n+                self.ble_exception_handler.report_successful_receive()\n+            \n+            if list(msg.data)[0] == b'U' and list(msg.data)[1] == b'U' and list(msg.data)[2] == b'\\x00':\n+                self.get_logger().info(\"Valid BLE command received.\")\n+                if list(msg.data)[3] == b'\\x09':\n+                    self.get_logger().info(\"Reset command detected. Executing reset sequence.\")\n+                    self.process_reset_command(\"remove_new\")\n+                elif list(msg.data)[3] == b'\\x05':\n+                    self.get_logger().info('收到andlink unbroadcast消息, 关闭广播01, 改为广播03, self.andlink_reset = False.')\n+                    self.andlink_reset = False\n+                    if self.ble_manager:\n+                        self.ble_manager.update_advertisement(True)\n+                    else:\n+                        self.ble_exception_handler.report_connection_error(\"BLE管理器未初始化\")\n+                elif list(msg.data)[3] == b'\\x04':\n                 self.get_logger().info(\"Advertisement enable command detected.\")\n                 self.get_logger().info('成功收到andlink的复位信号回复, self.andlink_reset = True.')\n                 self.andlink_reset = True\n                 self.ble_manager.update_advertisement(True)\n"}, {"date": 1754353808528, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -95,28 +95,28 @@\n         self.publish_andlink_cmd_reset()\n \n     def listener_ble_cmd_callback(self, msg):\n         try:\n-            self.get_logger().info(f\"Received BLE command: {list(msg.data)}\")\n-            self.get_logger().info(f'Received ByteMultiArray: {list(msg.data)}')\n+        self.get_logger().info(f\"Received BLE command: {list(msg.data)}\")\n+        self.get_logger().info(f'Received ByteMultiArray: {list(msg.data)}')\n             \n             # 报告成功接收\n             if hasattr(self, 'ble_exception_handler'):\n                 self.ble_exception_handler.report_successful_receive()\n             \n-            if list(msg.data)[0] == b'U' and list(msg.data)[1] == b'U' and list(msg.data)[2] == b'\\x00':\n-                self.get_logger().info(\"Valid BLE command received.\")\n-                if list(msg.data)[3] == b'\\x09':\n-                    self.get_logger().info(\"Reset command detected. Executing reset sequence.\")\n-                    self.process_reset_command(\"remove_new\")\n-                elif list(msg.data)[3] == b'\\x05':\n-                    self.get_logger().info('收到andlink unbroadcast消息, 关闭广播01, 改为广播03, self.andlink_reset = False.')\n-                    self.andlink_reset = False\n+        if list(msg.data)[0] == b'U' and list(msg.data)[1] == b'U' and list(msg.data)[2] == b'\\x00':\n+            self.get_logger().info(\"Valid BLE command received.\")\n+            if list(msg.data)[3] == b'\\x09':\n+                self.get_logger().info(\"Reset command detected. Executing reset sequence.\")\n+                self.process_reset_command(\"remove_new\")\n+            elif list(msg.data)[3] == b'\\x05':\n+                self.get_logger().info('收到andlink unbroadcast消息, 关闭广播01, 改为广播03, self.andlink_reset = False.')\n+                self.andlink_reset = False\n                     if self.ble_manager:\n-                        self.ble_manager.update_advertisement(True)\n+                self.ble_manager.update_advertisement(True)\n                     else:\n                         self.ble_exception_handler.report_connection_error(\"BLE管理器未初始化\")\n-                elif list(msg.data)[3] == b'\\x04':\n+            elif list(msg.data)[3] == b'\\x04':\n                 self.get_logger().info(\"Advertisement enable command detected.\")\n                 self.get_logger().info('成功收到andlink的复位信号回复, self.andlink_reset = True.')\n                 self.andlink_reset = True\n                 self.ble_manager.update_advertisement(True)\n"}], "date": 1754306061659, "name": "Commit-0", "content": "import rclpy\nimport struct\nimport os\nfrom rclpy.node import Node\nfrom std_msgs.msg import ByteMultiArray\nfrom std_msgs.msg import String\nfrom .ble_manager import Ble_manager\nfrom .ble_data_process import DataProcessor\nfrom homi_speech_interface.msg  import SIGCEvent\nfrom .read import DeviceConfig\nfrom homi_speech_interface.srv import SIGCData\nimport time\nimport threading\nimport configparser\nimport subprocess\nimport re\n\n# 导入异常处理相关模块\nfrom .exception_manager import ExceptionManager, ExceptionType, ExceptionSeverity\nfrom .ble_exception_handler import BLEExceptionHandler\n\nclass ByteMultiArrayPublisher(Node):\n    def __init__(self):\n        super().__init__('ble_node')\n        # 初始化完成后自动生成配置文件\n        self.generate_fac_devinfo() \n        self.get_logger().info(\"Initializing ByteMultiArrayPublisher...\")\n        self.ble_manager = None\n        self.andlink_reset = None\n        self.wifi_info = {'connected': False, 'ssid': None, 'password': None}\n        self.ble_stream_publisher_ = self.create_publisher(ByteMultiArray, 'ble_byte_stream', 10)\n        self.andlink_cmd_publisher_ = self.create_publisher(ByteMultiArray, 'andlink_cmd', 10)\n        \n        self.andlink_network_publisher = self.create_publisher(String, 'andlink_network', 10)\n        self.app_publisher = self.create_publisher(SIGCEvent, '/homi_speech/sigc_event_topic_APP', 10)\n\n        self.robot_control_service = self.create_service(SIGCData, '/homi_speech/sigc_data_service_APP', self.handle_robot_control_service_request)\n\n        self.subscription = self.create_subscription(\n            ByteMultiArray,\n            'ble_cmd',\n            self.listener_ble_cmd_callback,\n            10\n        )\n        self.subscription  # 防止垃圾回收\n        self.get_logger().info('Subscriber Node has been started.')\n\n        self.timer_period = 65.0  # 定时器周期（秒）\n        self.timer = None\n        self.timer_running = False\n        self.declare_parameter('wifi_connect_interface', \"wlan0\")\n        self.wifi_interface = self.get_parameter('wifi_connect_interface').value\n\n        self.declare_parameter('mobile_connect_interface', \"eth2\")\n        self.mobile_interface = self.get_parameter('mobile_connect_interface').value\n        # self.start_ble_bind_timer()\n        # self.start_ble_bind_timer()\n\n        self.user_bind_timer = self.create_timer(5.0, self.update_advertisement)  # 每5秒更新一次广播\n        self.get_logger().info(\"ByteMultiArrayPublisher initialized successfully.\")\n        self.config_path = '/etc/cmcc_robot/andlinkSdk.conf'  # 配置文件路径\n        self.last_user_bind = self.get_user_bind()   # 保存上一次的 user_bind 状态\n\n    def get_user_bind(self):\n        \"\"\"获取 user_bind 的值\"\"\"\n        try:\n            if os.path.exists(self.config_path):\n                self.get_logger().info(f\"Reading configuration from {self.config_path}...\")\n                device_config = DeviceConfig(self.config_path)\n                user_bind = device_config.get_config_value('userBind')\n                self.get_logger().info(f\"Current userBind value: {user_bind}\")\n                # 如果 user_bind 不等于 '1'，返回 '0'\n                return user_bind if user_bind == '1' else '0'\n            else:\n                self.get_logger().warning(f\"Configuration file {self.config_path} does not exist.\")\n                return '0'\n        except Exception as e:\n            self.get_logger().error(f\"Error occurred while getting userBind: {e}\")\n            return '0'\n\n    def process_reset_command(self, action: str = \"remove_new\"):\n        \"\"\"处理重置命令的逻辑\"\"\"\n        self.get_logger().info('发送复位信号, self.andlink_reset = False.')\n        self.andlink_reset = False\n        self.ble_manager.update_advertisement(True)\n        #self.ble_manager.disconnect_wifi(self.wifi_interface)\n        # self.ble_manager.disable_network_interface(self.mobile_interface)\n        self.ble_manager.remove_andlinkSdkConfig_file(action)\n        time.sleep(2)\n        self.publish_andlink_cmd_reset()\n\n    def listener_ble_cmd_callback(self, msg):\n        self.get_logger().info(f\"Received BLE command: {list(msg.data)}\")\n        self.get_logger().info(f'Received ByteMultiArray: {list(msg.data)}')\n        if list(msg.data)[0] == b'U' and list(msg.data)[1] == b'U' and list(msg.data)[2] == b'\\x00':\n            self.get_logger().info(\"Valid BLE command received.\")\n            if list(msg.data)[3] == b'\\x09':\n                self.get_logger().info(\"Reset command detected. Executing reset sequence.\")\n                self.process_reset_command(\"remove_new\")\n            elif list(msg.data)[3] == b'\\x05':\n                self.get_logger().info('收到andlink unbroadcast消息, 关闭广播01, 改为广播03, self.andlink_reset = False.')\n                self.andlink_reset = False\n                self.ble_manager.update_advertisement(True)\n            elif list(msg.data)[3] == b'\\x04':\n                self.get_logger().info(\"Advertisement enable command detected.\")\n                self.get_logger().info('成功收到andlink的复位信号回复, self.andlink_reset = True.')\n                self.andlink_reset = True\n                self.ble_manager.update_advertisement(True)\n\n\n    def publish_byte_stream(self, publisher, stream_data):\n        self.get_logger().info(f\"Publishing byte stream: {list(stream_data)}\")\n        self.get_logger().info(f'Preparing to publish byte stream: {list(stream_data)}')\n        msg = ByteMultiArray()\n        msg.data = [bytes([b]) for b in stream_data]\n        publisher.publish(msg)\n        self.get_logger().info(f'Published byte stream: {list(msg.data)}')\n        self.get_logger().info(\"Byte stream published successfully.\")\n    \n\n    def publish_andlink_cmd_reset(self):\n        self.get_logger().info(\"Publishing andlink reset command.\")\n        stream_data = bytes([0x55,0x55,0x00,0x02])\n        self.publish_byte_stream(self.andlink_cmd_publisher_,stream_data)\n        self.get_logger().info(\"Andlink reset command published.\")\n\n    def publish_app_cmd(self,stream_data):\n        self.sigcevent_msg=SIGCEvent()\n        self.sigcevent_msg.event = stream_data\n        self.app_publisher.publish(self.sigcevent_msg)\n\n    def publish_internet_status(self, is_connected):\n        msg = String()\n        msg.data = f'{{\"isInternetConnect\": \"{str(is_connected).lower()}\"}}'\n        self.internet_status_publisher_.publish(msg)\n        self.get_logger().info(f'Published internet status: {msg.data}')\n\n    def set_ble_manager(self,ble_manager):\n        self.ble_manager = ble_manager\n\n    def reconnect_to_wifi(self, max_retries=3):\n        \"\"\"尝试重新连接到之前保存的Wi-Fi网络，最多重试3次\"\"\"\n        if not self.wifi_info or not self.wifi_info['connected'] or not self.wifi_info['ssid']:\n            self.get_logger().warning(\"没有有效的Wi-Fi信息可用于重新连接\")\n            return False\n        \n        ssid = self.wifi_info['ssid']\n        password = self.wifi_info['password']\n        \n        self.get_logger().info(f\"尝试重新连接到Wi-Fi: {ssid}，最多重试 {max_retries} 次\")\n        self.send_userbind_notification(\"start\")\n        try:\n            # 1. 先断开当前连接\n            disconnect_cmd = \"sudo nmcli device disconnect wlan0\"\n            self.get_logger().info(f\"断开当前Wi-Fi连接: {disconnect_cmd}\")\n            disconnect_result = subprocess.run(\n                disconnect_cmd,\n                shell=True,\n                stdout=subprocess.PIPE,\n                stderr=subprocess.PIPE,\n                text=True,\n                timeout=10\n            )\n            \n            if disconnect_result.returncode != 0:\n                self.get_logger().warning(f\"断开连接失败: {disconnect_result.stderr}\")\n            else:\n                self.get_logger().info(\"成功断开当前Wi-Fi连接\")\n            \n            # 等待1秒让设备重置\n            time.sleep(1)\n        except Exception as e:\n            self.get_logger().warning(f\"断开连接时发生错误: {str(e)}\")\n            # 继续尝试连接，即使断开失败\n\n        for attempt in range(1, max_retries + 1):\n            self.get_logger().info(f\"尝试 #{attempt}/{max_retries}: 连接到 {ssid}\")\n            \n            try:\n                # 2. 尝试连接新网络\n                connect_cmd = f\"sudo nmcli dev wifi connect '{ssid}'\"\n                if password:\n                    connect_cmd += f\" password '{password}'\"\n                \n                self.get_logger().info(f\"执行连接命令: {connect_cmd}\")\n                connect_result = subprocess.run(\n                    connect_cmd, \n                    shell=True, \n                    stdout=subprocess.PIPE, \n                    stderr=subprocess.PIPE,\n                    text=True,\n                    timeout=30\n                )\n\n                if connect_result.stdout:\n                    self.get_logger().info(f\"连接命令输出: {connect_result.stdout.strip()}\")\n\n                # 3. 检查连接结果\n                if connect_result.returncode == 0:\n                    self.get_logger().info(f\"成功重新连接到Wi-Fi: {ssid}\")\n                    self.send_userbind_notification(\"end\")\n                    return True\n                else:\n                    self.get_logger().warning(f\"连接尝试 #{attempt} 失败: {connect_result.stderr}\")\n                    \n                    # 等待一段时间后重试（如果还有尝试机会）\n                    if attempt < max_retries:\n                        retry_delay = 2  # 2秒后重试\n                        self.get_logger().info(f\"等待 {retry_delay} 秒后重试...\")\n                        time.sleep(retry_delay)\n                    \n            except subprocess.TimeoutExpired as e:\n                self.get_logger().warning(f\"连接尝试 #{attempt} 超时: {str(e)}\")\n                if attempt < max_retries:\n                    self.get_logger().info(\"等待5秒后重试...\")\n                    time.sleep(5)\n            except Exception as e:\n                self.get_logger().error(f\"连接尝试 #{attempt} 发生意外错误: {str(e)}\")\n                if attempt < max_retries:\n                    self.get_logger().info(\"等待5秒后重试...\")\n                    time.sleep(5)\n        \n        self.get_logger().error(f\"所有 {max_retries} 次连接尝试均失败\")\n        self.send_userbind_notification(\"end\")\n        return False\n\n    def timer_ble_bind_callback(self):\n        self.get_logger().info(\"BLE bind timer triggered.\")\n        self.get_logger().info('Timer triggered for BLE bind.')\n        self.ble_manager.disconnect_all_devices()\n        user_bind = self.get_user_bind()\n        if user_bind is None or user_bind != '1':\n            self.get_logger().info('[绑定失败]user_bind不为1，未绑成功，同时计时器到达时间周期，重置绑定流程.')\n            #self.send_userbind_notification(\"end\")\n            #self.reconnect_to_wifi(max_retries=3)\n            self.process_reset_command(\"remove_new\")\n        self.stop_ble_bind_timer()\n        self.get_logger().info('关闭定时器.') \n        self.get_logger().info(\"BLE bind timer callback completed.\")\n\n    def stop_ble_bind_timer(self):\n        self.get_logger().info('Attempting to stop BLE bind timer.')\n        if self.timer is not None:\n            self.timer.cancel()\n            self.get_logger().info('Timer has been stopped.')\n        self.timer_running = False\n\n    def get_wifi_info(self, interface='wlan0'):\n        \"\"\"\n        获取指定无线接口的连接信息\n        :param interface: 无线接口名称 (默认为wlan0)\n        :return: 包含连接状态、SSID和密码的字典\n        \"\"\"\n        self.get_logger().info(\"[WIFI_INFO] Entering get_wifi_info\")\n        #self.send_userbind_notification(\"start\")\n        result = {\n            'connected': False,\n            'ssid': None,\n            'password': None\n        }\n        \n        # 1. 检查连接状态并获取SSID\n        try:\n            cmd = f\"iw dev {interface} link\"\n            output = subprocess.check_output(cmd, shell=True, stderr=subprocess.STDOUT, text=True)\n            \n            # 检查是否未连接\n            if \"Not connected\" in output:\n                self.get_logger().info(f\"[WIFI_INFO] Interface {interface} is not connected\")\n                self.wifi_info = result\n                return\n                \n            # 查找SSID行\n            ssid_match = re.search(r'SSID: (.+)', output)\n            if ssid_match:\n                ssid = ssid_match.group(1).strip()\n                result['connected'] = True\n                result['ssid'] = ssid\n                self.get_logger().info(f\"[WIFI_INFO] Connected to SSID: {ssid}\") \n                \n                # 2. 尝试获取密码\n                try:\n                    # 使用nmcli获取密码\n                    cmd = f\"sudo nmcli -s --show-secrets -g 802-11-wireless-security.psk connection show '{ssid}'\"\n                    password = subprocess.check_output(cmd, shell=True, text=True).strip()\n                    \n                    if password:\n                        result['password'] = password\n                        self.get_logger().info(f\"[WIFI_INFO][SUCCESS] Retrieved password for {ssid}\")\n                    else:\n                        self.get_logger().info(f\"[WIFI_INFO][WARNING] Password not found for {ssid}\")\n                        \n                except subprocess.CalledProcessError:\n                    self.get_logger().info(f\"[WIFI_INFO][ERROR] Failed to retrieve password for {ssid}\")\n                    \n            else:\n                self.get_logger().info(f\"[WIFI_INFO][WARNING] Connected but no SSID information found for {interface}\")\n                \n        except subprocess.CalledProcessError as e:\n            if \"No such device\" in e.output:\n                self.get_logger().info(f\"[WIFI_INFO][ERROR] Interface {interface} does not exist\")\n            elif \"Operation not supported\" in e.output:\n                self.get_logger().info(f\"[WIFI_INFO][ERROR] Wireless operation not supported on {interface}\")\n            else:\n                self.get_logger().info(f\"[WIFI_INFO][ERROR] Command failed: {e.output}\")\n        \n        self.wifi_info = result\n        return\n\n    def start_ble_bind_timer(self):\n        self.get_logger().info('[绑定开始]Attempting to start BLE bind timer.')\n        #wifi_thread = threading.Thread(target=self.get_wifi_info, args=('wlan0',))\n        #wifi_thread.daemon = True\n        #wifi_thread.start()\n        if not self.is_timer_stopped():\n            self.stop_ble_bind_timer()\n        self.timer = self.create_timer(self.timer_period, self.timer_ble_bind_callback)\n        self.timer_running = True\n        self.get_logger().info('Timer has been started.')\n\n    def is_timer_stopped(self):\n        return not self.timer_running\n\n    def handle_robot_control_service_request(self, req, response):\n        self.get_logger().info(f\"Handling robot control service request: {req.data}\")\n        self.get_logger().info(f\"Received service request: {req.data}\")\n        user_bind = self.get_user_bind()  # 调用独立函数获取 user_bind\n        self.get_logger().info(f\"Handling robot control service request. Current userBind: {user_bind}\")\n        if req.data == \"unbind_notify\":\n            andlink_oldconf = os.path.exists(\"/etc/cmcc_robot/andlinkSdk_old.conf\")\n            if user_bind == '0' and andlink_oldconf == False:\n                self.get_logger().info(f\"user_bind=False, andlink_oldconf=False, 当前就是APP解绑状态,不用重复处理APP解绑\")\n                response.error_code = 1\n                return response\n        if req.data == \"unbind_notify\" or req.data == \"unbind_notify_voice\":\n            self.get_logger().info(\"Unbind notify request detected. Executing unbind sequence.\")\n            self.ble_manager.disconnect_all_devices()\n            self.stop_ble_bind_timer()\n            time.sleep(5)\n            if user_bind == '1':\n                if req.data == \"unbind_notify_voice\":\n                    self.get_logger().info(\"音解绑. UserBind is still '1'. move andlinkSdk.conf to andlinkSdk_old.conf. Executing reset command.\")\n                    self.process_reset_command(\"move\")\n                if req.data == \"unbind_notify\":\n                    self.get_logger().info(\"App解绑. UserBind is still '1'. remove andlinkSdk.conf and andlinkSdk_old.conf. Executing reset command.\")\n                    self.process_reset_command(\"remove_all\")\n            elif req.data == \"unbind_notify\":\n                self.get_logger().info(\"App解绑. UserBind is not '1'. Only remove andlinkSdk_old.conf. Executing reset command.\")\n                self.process_reset_command(\"remove_old\")\n            elif req.data == \"unbind_notify_voice\":\n                self.get_logger().info(\"语音解绑. UserBind is not '1'. Only Executing reset command.\")\n                self.process_reset_command(\"remove_new\")\n            response.error_code = 0\n            return response\n        else:\n            self.get_logger().warning(f\"Unhandled service request: {req.data}\")\n            response.error_code = 1\n            return response\n    def execute_shell_command(self,command):\n        try:\n            result = subprocess.run(command, capture_output=True, text=True, check=True)\n            return result.stdout\n        except subprocess.CalledProcessError as e:\n            return f\"Error: {e.stderr}\"\n\n    def send_userbind_notification(self, status):\n        msg = String()\n        if status == \"start\":\n            msg.data = \"notify_userbind_start\"\n        elif status == \"end\":\n            msg.data = \"notify_userbind_end\"\n        else:\n            self.get_logger().warn(f\"未知的通知状态: {status}\")\n            return\n            \n        self.andlink_network_publisher.publish(msg)\n        self.get_logger().info(f\"已发送通知Network服务: {msg.data}\")\n\n    def update_advertisement(self):\n        self.get_logger().info(\"Starting advertisement update process...\")\n        try:\n            user_bind = self.get_user_bind()  # 调用独立函数获取 user_bind\n\n            # 检查 user_bind 是否与上一次状态不一致且不等于 '1'\n            if user_bind != self.last_user_bind:\n                self.get_logger().info(f\"userBind has changed from {self.last_user_bind} to {user_bind}.\")\n                if user_bind == '1' and self.last_user_bind != '1':\n                    command = ['rm', '/etc/cmcc_robot/andlinkSdk_old.conf']\n                    self.execute_shell_command(command)\n                    # self.send_userbind_notification(\"end\")\n                    self.get_logger().info(\"绑定成功，Removed andlinkSdk_old.conf\")\n                if user_bind != '1':\n                    self.get_logger().info(\"userBind is not '1'. Executing reset command.\")\n                    # self.process_reset_command()\n                else:\n                    self.ble_manager.enable_network_interface(self.mobile_interface)\n\n            # 更新 last_user_bind\n            self.last_user_bind = user_bind\n\n            if user_bind == '1':\n                self.get_logger().info(\"userBind is '1'. Disabling advertisement.\")\n                self.ble_manager.update_advertisement(False)\n            else:\n                self.get_logger().info(\"userBind is not '1'. Checking timer status...\")\n                if self.is_timer_stopped():\n                    self.get_logger().info(\"Timer is stopped. Proceeding with advertisement update.\")\n                    self.get_logger().info(\"Enabling advertisement.\")\n                    self.ble_manager.update_advertisement(True)\n                else:\n                    self.get_logger().info(\"Timer is running. Skipping advertisement update.\")\n        except Exception as e:\n            self.get_logger().error(f\"Error occurred during advertisement update: {e}\")\n        self.get_logger().info(\"Advertisement update process completed.\")\n\n    def generate_fac_devinfo(self):\n        \"\"\"生成/更新facDevinfo.conf\"\"\"\n        input_ini_path = \"/etc/cmcc_robot/cmcc_dev.ini\"\n        output_conf_path = \"/etc/cmcc_robot/facDevinfo.conf\"\n\n        try:\n            # 读取原始配置文件（逻辑保持不变）\n            config = configparser.ConfigParser()\n            config.read(input_ini_path)\n            required_keys = ['devType', 'devSn', 'devCmei', 'OVDLoginPassword']\n            if not all(config['factory'].get(key) for key in required_keys):\n                raise ValueError(\"cmcc_dev.ini缺少必要字段\")\n            \n            current_values = {\n                'deviceType': config['factory']['devType'],\n                'id': config['factory']['devSn'],\n                'sn': config['factory']['devSn'],\n                'cmei': config['factory']['devCmei'],\n                'license': config['factory']['OVDLoginPassword']\n            }\n\n            need_update = False\n            if os.path.exists(output_conf_path):\n                device_config2 = DeviceConfig(output_conf_path)                \n                # 检查字段一致性\n                for key in ['deviceType', 'id', 'sn', 'cmei', 'license']:\n                    if device_config2.get_config_value(key) != current_values[key]:\n                        self.get_logger().warning(f\"字段 {key} 不一致，触发更新\")\n                        need_update = True\n                        break\n            else:\n                need_update = True\n\n            if not need_update:\n                self.get_logger().info(f\"{output_conf_path} 已存在且字段一致，无需操作\")\n                return\n\n            # 构建新配置内容\n            conf_content = f\"\"\"deviceType={current_values['deviceType']}\nproductToken=iov0DF2snz711HMK\nandlinkToken=RhcTU7W6Pgd86uZC\ndeviceVendor=test\nid={current_values['id']}\nfirmWareVersion=v1.0\nsoftWareVersion=v2.0\ncfgPath=/etc/cmcc_robot\nmac=000C2999D25D\nwlanMac=FF0822A08E00\nsn={current_values['sn']}\ncmei={current_values['cmei']}\nlicense={current_values['license']}\notaPolicy=0\notaFragSize=2097152\notaFilePath=/tmp\notaFileName=ota.zip\n\"\"\"\n\n            # 强制写入新文件（覆盖模式）\n            os.makedirs(os.path.dirname(output_conf_path), exist_ok=True)\n            with open(output_conf_path, 'w') as f:\n                f.write(conf_content)\n            self.get_logger().info(f\"配置文件已重建: {output_conf_path}\")\n\n        except PermissionError as e:\n            self.get_logger().error(f\"权限不足: {str(e)}\")\n        except Exception as e:\n            self.get_logger().error(f\"操作失败: {str(e)}\")\n\ndef mac_to_bytes(mac):\n    try:\n        if len(mac) != 12:\n            raise ValueError(\"MAC address should be exactly 12 characters long\")\n        return bytes(int(mac[i:i+2], 16) for i in range(0, len(mac), 2))\n    except ValueError as e:\n        raise ValueError(f\"Invalid MAC address format: {mac}. Error: {e}\")\n\n\ndef main(args=None):\n    rclpy.init(args=args)\n    node = ByteMultiArrayPublisher()\n    node.get_logger().info(\"Node initialized successfully.\")\n\n    config_path = '/etc/cmcc_robot/andlinkSdk.conf'\n    device_config = DeviceConfig(config_path)\n    user_bind = device_config.get_config_value('userBind')\n    usrkey = device_config.get_config_value('usrkey')\n\n    config2_path = '/etc/cmcc_robot/facDevinfo.conf'\n    device_config2 = DeviceConfig(config2_path)\n    device_id = device_config2.get_config_value('id')\n    mac_name = device_config2.get_config_value('mac')\n    device_type = device_config2.get_config_value('deviceType')\n    deviceId = 'CMCC-'+device_type+'-'+device_config2.get_config_value('sn')\n    if user_bind is not None:\n        node.get_logger().info(\"userBind:\"+user_bind)\n    else:\n        node.get_logger().info(\"userBind:None\")\n    deviceId1=device_config.get_config_value('deviceId')\n    if deviceId1 is not None:\n        node.get_logger().info(\"deviceId1:\"+deviceId1)\n    if deviceId is not None:\n        node.get_logger().info(\"deviceId_fa:\"+deviceId)\n    mac_bytes = mac_to_bytes(mac_name)\n    node.get_logger().info(f'MAC bytes: {list(mac_bytes)}')\n\n    manuf_code, = struct.unpack('>H', mac_bytes[1::-1])\n    manuf_data = list(mac_bytes[2:])\n    if user_bind is not None and user_bind == '1' and usrkey is not None:\n        manuf_data.append(0x02)\n        node.get_logger().debug(\"manuf_data.append(0x02)\")\n    else:\n        manuf_data.append(0x01)\n        node.get_logger().debug(\"manuf_data.append(0x01)\")\n    ble_adv_localname = 'CMB'+device_type+'-'+device_id[-4:]\n\n    data_processor_t=DataProcessor(node,ble_adv_localname,manuf_code,manuf_data,deviceId,user_bind)\n\n    ble_t = Ble_manager(node, data_processor_t, True)\n    if user_bind is None or user_bind != '1':\n        if user_bind is not None:\n            ble_t.remove_andlinkSdkConfig_file()\n        #ble_t.disconnect_wifi(node.wifi_interface)\n        # time.sleep(2)\n        # node.publish_andlink_cmd_reset()\n    node.set_ble_manager(ble_t)\n    ble_t.custom_advertisement_register()\n    time.sleep(1)\n    ble_t.custom_app_register()\n    time.sleep(1)\n    ble_thread = threading.Thread(target=ble_t.ble_manager_start,args=())\n    ble_thread.daemon = True\n    ble_thread.start()\n\n\n    listener_thread = threading.Thread(target=ble_t.start_bluetooth_listener)\n    listener_thread.daemon = True\n    listener_thread.start()\n\n    try:\n        rclpy.spin(node)\n    except KeyboardInterrupt:\n        node.get_logger().info('Node stopped by user.')\n    finally:\n        node.get_logger().info('Shutting down node.')\n        node.stop_timer()  # 关闭定时器\n        node.destroy_node()\n        rclpy.shutdown()\n\nif __name__ == '__main__':\n    main()\n"}]}