{"sourceFile": "ble/ble/ble_exception_handler.py", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1754305942116, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1754305942116, "name": "Commit-0", "content": "#!/usr/bin/env python3\n# -*- coding: utf-8 -*-\n\n\"\"\"\nBLE节点异常处理器\n专门处理BLE相关的异常情况和恢复策略\n\"\"\"\n\nimport time\nimport subprocess\nimport threading\nfrom typing import Dict, Any, Optional\nfrom .exception_manager import ExceptionManager, ExceptionType, ExceptionSeverity, ExceptionRecord\n\n\nclass BLEExceptionHandler:\n    \"\"\"BLE异常处理器\"\"\"\n    \n    def __init__(self, ble_node, exception_manager: ExceptionManager):\n        self.ble_node = ble_node\n        self.exception_manager = exception_manager\n        self.logger = ble_node.get_logger()\n        \n        # BLE状态监控\n        self.ble_health_check_interval = 30  # 30秒检查一次\n        self.last_successful_send = time.time()\n        self.last_successful_receive = time.time()\n        self.send_timeout_threshold = 120  # 2分钟没有成功发送视为异常\n        self.receive_timeout_threshold = 300  # 5分钟没有接收到数据视为异常\n        \n        # 连接重试配置\n        self.max_reconnect_attempts = 3\n        self.reconnect_delay = 5  # 重连延迟（秒）\n        \n        # 注册异常处理器\n        self._register_handlers()\n        self._register_recovery_strategies()\n        \n        # 启动健康检查\n        self.health_check_thread = threading.Thread(target=self._health_check_loop, daemon=True)\n        self.health_check_thread.start()\n        \n        self.logger.info(\"BLE异常处理器已初始化\")\n\n    def _register_handlers(self):\n        \"\"\"注册异常处理器\"\"\"\n        self.exception_manager.register_handler(\n            ExceptionType.BLE_SEND_ERROR, \n            self._handle_send_error\n        )\n        self.exception_manager.register_handler(\n            ExceptionType.BLE_RECEIVE_ERROR, \n            self._handle_receive_error\n        )\n        self.exception_manager.register_handler(\n            ExceptionType.BLE_CONNECTION_ERROR, \n            self._handle_connection_error\n        )\n        self.exception_manager.register_handler(\n            ExceptionType.BLE_ADVERTISEMENT_ERROR, \n            self._handle_advertisement_error\n        )\n        self.exception_manager.register_handler(\n            ExceptionType.BLE_DEVICE_ERROR, \n            self._handle_device_error\n        )\n\n    def _register_recovery_strategies(self):\n        \"\"\"注册恢复策略\"\"\"\n        self.exception_manager.register_recovery_strategy(\n            ExceptionType.BLE_SEND_ERROR, \n            self._recover_send_error\n        )\n        self.exception_manager.register_recovery_strategy(\n            ExceptionType.BLE_RECEIVE_ERROR, \n            self._recover_receive_error\n        )\n        self.exception_manager.register_recovery_strategy(\n            ExceptionType.BLE_CONNECTION_ERROR, \n            self._recover_connection_error\n        )\n        self.exception_manager.register_recovery_strategy(\n            ExceptionType.BLE_ADVERTISEMENT_ERROR, \n            self._recover_advertisement_error\n        )\n        self.exception_manager.register_recovery_strategy(\n            ExceptionType.BLE_DEVICE_ERROR, \n            self._recover_device_error\n        )\n\n    def report_send_error(self, error_message: str, context: Optional[Dict[str, Any]] = None):\n        \"\"\"报告发送错误\"\"\"\n        self.exception_manager.report_exception(\n            exception_type=ExceptionType.BLE_SEND_ERROR,\n            message=f\"BLE发送失败: {error_message}\",\n            severity=ExceptionSeverity.MEDIUM,\n            context=context\n        )\n\n    def report_receive_error(self, error_message: str, context: Optional[Dict[str, Any]] = None):\n        \"\"\"报告接收错误\"\"\"\n        self.exception_manager.report_exception(\n            exception_type=ExceptionType.BLE_RECEIVE_ERROR,\n            message=f\"BLE接收失败: {error_message}\",\n            severity=ExceptionSeverity.MEDIUM,\n            context=context\n        )\n\n    def report_connection_error(self, error_message: str, context: Optional[Dict[str, Any]] = None):\n        \"\"\"报告连接错误\"\"\"\n        self.exception_manager.report_exception(\n            exception_type=ExceptionType.BLE_CONNECTION_ERROR,\n            message=f\"BLE连接异常: {error_message}\",\n            severity=ExceptionSeverity.HIGH,\n            context=context\n        )\n\n    def report_successful_send(self):\n        \"\"\"报告成功发送\"\"\"\n        self.last_successful_send = time.time()\n\n    def report_successful_receive(self):\n        \"\"\"报告成功接收\"\"\"\n        self.last_successful_receive = time.time()\n\n    def _handle_send_error(self, record: ExceptionRecord):\n        \"\"\"处理发送错误\"\"\"\n        self.logger.warning(f\"处理BLE发送错误: {record.message}\")\n        \n        # 检查BLE管理器状态\n        if hasattr(self.ble_node, 'ble_manager') and self.ble_node.ble_manager:\n            try:\n                # 尝试重新初始化发送缓冲区\n                if hasattr(self.ble_node.ble_manager, 'reset_send_buffer'):\n                    self.ble_node.ble_manager.reset_send_buffer()\n            except Exception as e:\n                self.logger.error(f\"重置发送缓冲区失败: {e}\")\n\n    def _handle_receive_error(self, record: ExceptionRecord):\n        \"\"\"处理接收错误\"\"\"\n        self.logger.warning(f\"处理BLE接收错误: {record.message}\")\n        \n        # 检查接收缓冲区\n        if hasattr(self.ble_node, 'ble_manager') and self.ble_node.ble_manager:\n            try:\n                if hasattr(self.ble_node.ble_manager, 'reset_receive_buffer'):\n                    self.ble_node.ble_manager.reset_receive_buffer()\n            except Exception as e:\n                self.logger.error(f\"重置接收缓冲区失败: {e}\")\n\n    def _handle_connection_error(self, record: ExceptionRecord):\n        \"\"\"处理连接错误\"\"\"\n        self.logger.error(f\"处理BLE连接错误: {record.message}\")\n        \n        # 标记需要重新连接\n        if hasattr(self.ble_node, 'ble_manager'):\n            self.ble_node.ble_manager = None\n\n    def _handle_advertisement_error(self, record: ExceptionRecord):\n        \"\"\"处理广播错误\"\"\"\n        self.logger.warning(f\"处理BLE广播错误: {record.message}\")\n\n    def _handle_device_error(self, record: ExceptionRecord):\n        \"\"\"处理设备错误\"\"\"\n        self.logger.error(f\"处理BLE设备错误: {record.message}\")\n\n    def _recover_send_error(self, record: ExceptionRecord) -> bool:\n        \"\"\"恢复发送错误\"\"\"\n        try:\n            self.logger.info(\"尝试恢复BLE发送功能...\")\n            \n            # 步骤1: 重新初始化BLE管理器\n            if self._reinitialize_ble_manager():\n                # 步骤2: 测试发送功能\n                if self._test_send_function():\n                    self.logger.info(\"BLE发送功能恢复成功\")\n                    return True\n            \n            self.logger.warning(\"BLE发送功能恢复失败\")\n            return False\n            \n        except Exception as e:\n            self.logger.error(f\"恢复发送错误时发生异常: {e}\")\n            return False\n\n    def _recover_receive_error(self, record: ExceptionRecord) -> bool:\n        \"\"\"恢复接收错误\"\"\"\n        try:\n            self.logger.info(\"尝试恢复BLE接收功能...\")\n            \n            # 重新初始化BLE管理器\n            if self._reinitialize_ble_manager():\n                self.logger.info(\"BLE接收功能恢复成功\")\n                return True\n            \n            self.logger.warning(\"BLE接收功能恢复失败\")\n            return False\n            \n        except Exception as e:\n            self.logger.error(f\"恢复接收错误时发生异常: {e}\")\n            return False\n\n    def _recover_connection_error(self, record: ExceptionRecord) -> bool:\n        \"\"\"恢复连接错误\"\"\"\n        try:\n            self.logger.info(\"尝试恢复BLE连接...\")\n            \n            for attempt in range(self.max_reconnect_attempts):\n                self.logger.info(f\"BLE重连尝试 {attempt + 1}/{self.max_reconnect_attempts}\")\n                \n                # 重启蓝牙服务\n                if self._restart_bluetooth_service():\n                    time.sleep(self.reconnect_delay)\n                    \n                    # 重新初始化BLE管理器\n                    if self._reinitialize_ble_manager():\n                        self.logger.info(\"BLE连接恢复成功\")\n                        return True\n                \n                time.sleep(self.reconnect_delay)\n            \n            self.logger.error(\"BLE连接恢复失败，已达到最大重试次数\")\n            return False\n            \n        except Exception as e:\n            self.logger.error(f\"恢复连接错误时发生异常: {e}\")\n            return False\n\n    def _recover_advertisement_error(self, record: ExceptionRecord) -> bool:\n        \"\"\"恢复广播错误\"\"\"\n        try:\n            self.logger.info(\"尝试恢复BLE广播...\")\n            \n            if hasattr(self.ble_node, 'ble_manager') and self.ble_node.ble_manager:\n                # 重新启动广播\n                if hasattr(self.ble_node.ble_manager, 'update_advertisement'):\n                    self.ble_node.ble_manager.update_advertisement(True)\n                    self.logger.info(\"BLE广播恢复成功\")\n                    return True\n            \n            # 如果BLE管理器不存在，尝试重新初始化\n            if self._reinitialize_ble_manager():\n                self.logger.info(\"BLE广播恢复成功\")\n                return True\n            \n            return False\n            \n        except Exception as e:\n            self.logger.error(f\"恢复广播错误时发生异常: {e}\")\n            return False\n\n    def _recover_device_error(self, record: ExceptionRecord) -> bool:\n        \"\"\"恢复设备错误\"\"\"\n        try:\n            self.logger.info(\"尝试恢复BLE设备...\")\n            \n            # 重启蓝牙服务\n            if self._restart_bluetooth_service():\n                time.sleep(5)  # 等待服务完全启动\n                \n                # 重新初始化\n                if self._reinitialize_ble_manager():\n                    self.logger.info(\"BLE设备恢复成功\")\n                    return True\n            \n            return False\n            \n        except Exception as e:\n            self.logger.error(f\"恢复设备错误时发生异常: {e}\")\n            return False\n\n    def _reinitialize_ble_manager(self) -> bool:\n        \"\"\"重新初始化BLE管理器\"\"\"\n        try:\n            self.logger.info(\"重新初始化BLE管理器...\")\n            \n            # 清理旧的BLE管理器\n            if hasattr(self.ble_node, 'ble_manager') and self.ble_node.ble_manager:\n                try:\n                    self.ble_node.ble_manager.cleanup()\n                except:\n                    pass\n                self.ble_node.ble_manager = None\n            \n            # 重新创建BLE管理器\n            from .ble_manager import Ble_manager\n            self.ble_node.ble_manager = Ble_manager(self.ble_node)\n            \n            self.logger.info(\"BLE管理器重新初始化成功\")\n            return True\n            \n        except Exception as e:\n            self.logger.error(f\"重新初始化BLE管理器失败: {e}\")\n            return False\n\n    def _restart_bluetooth_service(self) -> bool:\n        \"\"\"重启蓝牙服务\"\"\"\n        try:\n            self.logger.info(\"重启蓝牙服务...\")\n            \n            # 停止蓝牙服务\n            subprocess.run(['sudo', 'systemctl', 'stop', 'bluetooth'], \n                         check=False, capture_output=True)\n            time.sleep(2)\n            \n            # 启动蓝牙服务\n            result = subprocess.run(['sudo', 'systemctl', 'start', 'bluetooth'], \n                                  check=False, capture_output=True)\n            \n            if result.returncode == 0:\n                self.logger.info(\"蓝牙服务重启成功\")\n                return True\n            else:\n                self.logger.error(f\"蓝牙服务重启失败: {result.stderr.decode()}\")\n                return False\n                \n        except Exception as e:\n            self.logger.error(f\"重启蓝牙服务时发生异常: {e}\")\n            return False\n\n    def _test_send_function(self) -> bool:\n        \"\"\"测试发送功能\"\"\"\n        try:\n            # 这里可以发送一个测试消息来验证发送功能\n            # 具体实现依赖于BLE管理器的接口\n            return True\n        except Exception as e:\n            self.logger.error(f\"测试发送功能失败: {e}\")\n            return False\n\n    def _health_check_loop(self):\n        \"\"\"健康检查循环\"\"\"\n        while True:\n            try:\n                time.sleep(self.ble_health_check_interval)\n                self._perform_health_check()\n            except Exception as e:\n                self.logger.error(f\"健康检查时发生错误: {e}\")\n\n    def _perform_health_check(self):\n        \"\"\"执行健康检查\"\"\"\n        current_time = time.time()\n        \n        # 检查发送超时\n        if current_time - self.last_successful_send > self.send_timeout_threshold:\n            self.exception_manager.report_exception(\n                exception_type=ExceptionType.BLE_SEND_ERROR,\n                message=\"发送功能超时：长时间未成功发送数据\",\n                severity=ExceptionSeverity.HIGH,\n                context={\n                    \"last_send_time\": self.last_successful_send,\n                    \"timeout_threshold\": self.send_timeout_threshold\n                }\n            )\n        \n        # 检查接收超时\n        if current_time - self.last_successful_receive > self.receive_timeout_threshold:\n            self.exception_manager.report_exception(\n                exception_type=ExceptionType.BLE_RECEIVE_ERROR,\n                message=\"接收功能超时：长时间未接收到数据\",\n                severity=ExceptionSeverity.MEDIUM,\n                context={\n                    \"last_receive_time\": self.last_successful_receive,\n                    \"timeout_threshold\": self.receive_timeout_threshold\n                }\n            )\n        \n        # 检查BLE管理器状态\n        if not hasattr(self.ble_node, 'ble_manager') or self.ble_node.ble_manager is None:\n            self.exception_manager.report_exception(\n                exception_type=ExceptionType.BLE_CONNECTION_ERROR,\n                message=\"BLE管理器未初始化或已损坏\",\n                severity=ExceptionSeverity.CRITICAL\n            )\n\n    def get_health_status(self) -> Dict[str, Any]:\n        \"\"\"获取健康状态\"\"\"\n        current_time = time.time()\n        return {\n            \"send_health\": {\n                \"last_successful\": self.last_successful_send,\n                \"seconds_since_last\": current_time - self.last_successful_send,\n                \"is_healthy\": current_time - self.last_successful_send < self.send_timeout_threshold\n            },\n            \"receive_health\": {\n                \"last_successful\": self.last_successful_receive,\n                \"seconds_since_last\": current_time - self.last_successful_receive,\n                \"is_healthy\": current_time - self.last_successful_receive < self.receive_timeout_threshold\n            },\n            \"manager_health\": {\n                \"is_initialized\": hasattr(self.ble_node, 'ble_manager') and self.ble_node.ble_manager is not None\n            }\n        } "}]}