{"sourceFile": "andlink/include/andlink_node.hpp", "activeCommit": 0, "commits": [{"activePatchIndex": 2, "patches": [{"date": 1754283529877, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1754283537943, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -34,8 +34,9 @@\n     rclcpp::Subscription<std_msgs::msg::ByteMultiArray>::SharedPtr andlink_cmd_subscription_;\n     rclcpp::Publisher<std_msgs::msg::ByteMultiArray>::SharedPtr ble_cmd_publisher_;\n     rclcpp::Publisher<std_msgs::msg::String>::SharedPtr andlink_network_publisher_; \n     rclcpp::Publisher<homi_speech_interface::msg::SIGCEvent>::SharedPtr homi_player_publisher_;       \n+    rclcpp::Client<homi_speech_interface::srv::NetCtrl>::SharedPtr network_service_client_;\n     rclcpp::TimerBase::SharedPtr timer_; // 定时器成员变量\n     rclcpp::TimerBase::SharedPtr log_timer_;\n     rclcpp::Publisher<std_msgs::msg::ByteMultiArray>::SharedPtr byte_stream_publisher_;\n     rclcpp::TimerBase::SharedPtr byte_stream_timer_;\n"}, {"date": 1754283557112, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -18,8 +18,9 @@\n     void publish_ble_cmd_unbroadcast_message();\n     void publish_ble_cmd_unbind_message();\n     void publish_network_connecting_start_message();\n     void publish_network_connecting_end_message();\n+    bool connect_wifi_via_service(const std::string& ssid, const std::string& password);\n private:\n     void handle_andlink_cmd_message(const std_msgs::msg::ByteMultiArray::SharedPtr msg);\n     void handle_ble_byte_stream_message(const std_msgs::msg::ByteMultiArray::SharedPtr msg);\n     void handle_andlink_userkey_message(const std_msgs::msg::String::SharedPtr msg);\n"}], "date": 1754283529877, "name": "Commit-0", "content": "#ifndef ANDLINK_NODE_HPP\n#define ANDLINK_NODE_HPP\n\n#include \"rclcpp/rclcpp.hpp\"\n#include \"std_msgs/msg/byte_multi_array.hpp\"\n#include \"std_msgs/msg/string.hpp\"\n#include \"andlink_adapt.h\"\n#include <homi_speech_interface/msg/sigc_event.hpp>\n#include <homi_speech_interface/srv/net_ctrl.hpp>\n\nclass AndlinkNode : public rclcpp::Node\n{\npublic:\n    AndlinkNode();\n    int andlink_main();\n    int demo_startAndlinkSdk(CFG_NET_MODE_e mode, char *ifname);\n    void publish_ble_cmd_broadcast_message();\n    void publish_ble_cmd_unbroadcast_message();\n    void publish_ble_cmd_unbind_message();\n    void publish_network_connecting_start_message();\n    void publish_network_connecting_end_message();\nprivate:\n    void handle_andlink_cmd_message(const std_msgs::msg::ByteMultiArray::SharedPtr msg);\n    void handle_ble_byte_stream_message(const std_msgs::msg::ByteMultiArray::SharedPtr msg);\n    void handle_andlink_userkey_message(const std_msgs::msg::String::SharedPtr msg);\n    void handle_timer_callback(); // 新增定时器回调函数声明\n    void log_timer_callback();\n    void publish_byte_stream_message();\n    std::string to_hex(uint8_t byte);\n    void publish_json_message();\n\n    rclcpp::Subscription<std_msgs::msg::ByteMultiArray>::SharedPtr byte_stream_subscription_;\n    rclcpp::Subscription<std_msgs::msg::String>::SharedPtr andlink_userkey_subscription_;\n    rclcpp::Subscription<std_msgs::msg::ByteMultiArray>::SharedPtr andlink_cmd_subscription_;\n    rclcpp::Publisher<std_msgs::msg::ByteMultiArray>::SharedPtr ble_cmd_publisher_;\n    rclcpp::Publisher<std_msgs::msg::String>::SharedPtr andlink_network_publisher_; \n    rclcpp::Publisher<homi_speech_interface::msg::SIGCEvent>::SharedPtr homi_player_publisher_;       \n    rclcpp::TimerBase::SharedPtr timer_; // 定时器成员变量\n    rclcpp::TimerBase::SharedPtr log_timer_;\n    rclcpp::Publisher<std_msgs::msg::ByteMultiArray>::SharedPtr byte_stream_publisher_;\n    rclcpp::TimerBase::SharedPtr byte_stream_timer_;\n    rclcpp::Subscription<std_msgs::msg::String>::SharedPtr char_subscription_;\n    rclcpp::Publisher<std_msgs::msg::String>::SharedPtr json_publisher_;\n    rclcpp::TimerBase::SharedPtr json_timer_;\n};\nint andlink_dn_send_cmd_callback(RESP_MODE_e mode, dn_dev_ctrl_frame_t *ctrlFrame, char *eventType, char *respData, unsigned int respBufSize);\n\n#endif // ANDLINK_NODE_HPP\n"}]}