{"sourceFile": "andlink/src/andlink_node.cpp", "activeCommit": 0, "commits": [{"activePatchIndex": 10, "patches": [{"date": 1754283548487, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1754283572175, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1208,8 +1208,48 @@\n     andlink_network_publisher_->publish(message);\n     RCLCPP_INFO(this->get_logger(), \"发送给网络服务消息 notification: '%s'\", message.data.c_str());\n }\n \n+bool AndlinkNode::connect_wifi_via_service(const std::string& ssid, const std::string& password)\n+{\n+    // 等待服务可用\n+    if (!network_service_client_->wait_for_service(std::chrono::seconds(5))) {\n+        LOG_ERROR(\"网络服务不可用\");\n+        return false;\n+    }\n+\n+    // 构建服务请求\n+    auto request = std::make_shared<homi_speech_interface::srv::NetCtrl::Request>();\n+    \n+    // 构建JSON数据\n+    std::string json_data = \"{\\\"command\\\": \\\"connectWifi\\\", \\\"ssid\\\": \\\"\" + ssid + \"\\\", \\\"password\\\": \\\"\" + password + \"\\\"}\";\n+    request->data = json_data;\n+    \n+    LOG_INFO(\"调用网络服务连接WiFi: %s\", json_data.c_str());\n+    \n+    // 调用服务\n+    auto future = network_service_client_->async_send_request(request);\n+    \n+    // 等待响应\n+    if (rclcpp::spin_until_future_complete(this->get_node_base_interface(), future, std::chrono::seconds(30)) == \n+        rclcpp::FutureReturnCode::SUCCESS) {\n+        auto response = future.get();\n+        LOG_INFO(\"网络服务响应: 错误码=%d, 结果=%s\", response->error_code, response->result.c_str());\n+        \n+        // 检查响应是否成功\n+        if (response->error_code == 200) {\n+            LOG_INFO(\"WiFi连接服务调用成功\");\n+            return true;\n+        } else {\n+            LOG_ERROR(\"WiFi连接失败，错误码: %d\", response->error_code);\n+            return false;\n+        }\n+    } else {\n+        LOG_ERROR(\"网络服务调用超时\");\n+        return false;\n+    }\n+}\n+\n std::string AndlinkNode::to_hex(uint8_t byte)\n {\n     std::ostringstream oss;\n     oss << std::hex << std::uppercase << static_cast<int>(byte);\n"}, {"date": 1754283583603, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -69,8 +69,9 @@\n int dn_send_cmd_status = 0;\n char* g_json_str = nullptr; \n char g_ifname[16]= \"\";\n char g_p2p_ifname[16]= \"\";\n+AndlinkNode* g_andlink_node = nullptr;  // 全局指针，用于在回调函数中访问节点\n \n struct DeviceInfo {\n     char ip[INET_ADDRSTRLEN];\n     char brdAddr[INET_ADDRSTRLEN];\n"}, {"date": 1754283591887, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -826,8 +826,11 @@\n AndlinkNode::AndlinkNode() : Node(\"andlink_node\")\n {\n     RCLCPP_INFO(this->get_logger(), \"Hello, andlink Node!\");\n \n+    // 设置全局指针\n+    g_andlink_node = this;\n+\n     andlink_main();\n \n     byte_stream_subscription_ = this->create_subscription<std_msgs::msg::ByteMultiArray>(\n         \"ble_byte_stream\", 10,\n"}, {"date": 1754283613429, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -556,58 +556,22 @@\n \n         LOG_DEBUG(\"wlan0 去连接 WiFi: %s\", wificfg->ssid);\n         dn_send_cmd_status = 6;\n         sleep(2);\n-        std::string wifi_cmd;\n-        int result = 0;\n \n-        // 1. 断开 wlan0 连接\n-        wifi_cmd = \"wpa_cli -i wlan0 disconnect\";\n-        LOG_DEBUG(\"Executing command: %s\", wifi_cmd.c_str());\n-        result = system(wifi_cmd.c_str());\n-\n-        if (result == -1) {\n-            LOG_ERROR(\"Failed to execute disconnect command.\");\n+        // 使用ROS2服务调用连接WiFi\n+        bool connect_success = false;\n+        if (g_andlink_node != nullptr) {\n+            LOG_INFO(\"使用ROS2网络服务连接WiFi: %s\", wificfg->ssid);\n+            std::string ssid = wificfg->ssid;\n+            std::string password = wificfg->password ? wificfg->password : \"\";\n+            connect_success = g_andlink_node->connect_wifi_via_service(ssid, password);\n         } else {\n-            int exit_status = WEXITSTATUS(result);\n-            if (exit_status != 0) {\n-                LOG_ERROR(\"Disconnect command exited with status: %d\", exit_status);\n-            } else {\n-                LOG_DEBUG(\"Command executed disconnect successfully.\");\n-            }\n+            LOG_ERROR(\"全局AndlinkNode指针为空，无法调用ROS2服务\");\n+            dn_send_cmd_status = 7;\n+            return -1;\n         }\n-        sleep(1);\n-\n-        // 2. 重写wpa配置文件\n-        write_wpa_conf_file(wificfg);\n-        /*\n-        int user_bind = get_user_bind();\n-        if (user_bind == 0) {\n-            LOG_DEBUG(\"当前是绑定配网操作, 清除并重启网络服务\");\n-            reset_network_manager();\n-        } else {\n-            LOG_DEBUG(\"当前设备已绑定, 是恢复网络操作, 不需要清除重启网络服务\");\n-        }\n-        */\n         \n-        // 3.判断是否后台有进程，没有重启wpa_supplicant\n-        if (get_wpa_supplicant_pid() == 0) {\n-            LOG_DEBUG(\"后台没有wpa_supplicant, 启动wpa_supplicant.\");\n-            wifi_cmd = \"wpa_supplicant -i wlan0 -B -c /etc/wpa_supplicant/wpa_supplicant-wlan0.conf\";\n-            LOG_DEBUG(\"Executing command: %s\", wifi_cmd.c_str());\n-            result = system(wifi_cmd.c_str());\n-        } else {\n-            LOG_DEBUG(\"后台已经有wpa_supplicant, 执行wpa_cli -i wlan0 reconfigure.\");\n-            wifi_cmd = \"wpa_cli -i wlan0 reconfigure\"; \n-            LOG_DEBUG(\"Executing command: %s\", wifi_cmd.c_str());\n-            result = system(wifi_cmd.c_str());\n-                \n-            wifi_cmd = \"wpa_cli -i wlan0 reconnect\";\n-            LOG_DEBUG(\"Executing command: %s\", wifi_cmd.c_str()); \n-            result = system(wifi_cmd.c_str());            \n-        }\n-        sleep(5);\n-        \n         // 4. 开始连接\n         int retry_count = 0;\n         int max_retries = 20;\n         bool connect = false;\n"}, {"date": 1754283637634, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -570,52 +570,33 @@\n             dn_send_cmd_status = 7;\n             return -1;\n         }\n         \n-        // 4. 开始连接\n-        int retry_count = 0;\n-        int max_retries = 20;\n-        bool connect = false;\n-        char ipAddr[40] = { 0 };\n-        char brdAddr[40] = { 0 };\n-        \n-        if ((!isWlan0ConnectedToSSID(wificfg->ssid)) || (0 != check_device_ip_info(g_ifname, ipAddr, brdAddr))) {\n-            while (retry_count < max_retries) {\n-                /*\n-                wifi_cmd = \"wpa_cli -i wlan0 reconnect\";\n-                LOG_DEBUG(\"Connecting for exact SSID: %s (attempt %d/%d)\", wificfg->ssid, retry_count+1, max_retries); \n-                LOG_DEBUG(\"Executing command: %s\", wifi_cmd.c_str());\n-                result = system(wifi_cmd.c_str());\n-                */\n-                sleep(2);\n-                if (result == -1) {\n-                    LOG_ERROR(\"Failed to execute connect command.\");\n-                } else {\n-                    // 验证是否真正连接成功\n-                    if (isWlan0ConnectedToSSID(wificfg->ssid)) {\n-                        LOG_DEBUG(\"Wifi成功连上 %s.\", wificfg->ssid);\n-                        if (0 == check_device_ip_info(g_ifname, ipAddr, brdAddr)) {\n-                            LOG_DEBUG(\"成功获取到ip %s, ipAddr %s.\", ipAddr, brdAddr);\n-                            connect = true;\n-                            demo_set_device_ipaddr_for_callback(ipAddr, brdAddr);\n-                            break;\n-                        } else {\n-                            LOG_DEBUG(\"还未获取到ip.\"); \n-                        }\n-                    } else {\n-                        LOG_ERROR(\"Wifi还未连上 %s.\", wificfg->ssid);\n-                    }\n-                }\n-                retry_count++;\n-            }\n+        // 检查连接结果\n+        if (connect_success) {\n+            LOG_INFO(\"ROS2网络服务连接WiFi成功: %s\", wificfg->ssid);\n             \n-            if (!connect) {\n-                LOG_ERROR(\"Failed after %d attempts\", max_retries);\n-                dn_send_cmd_status = 7;\n+            // 等待一段时间让网络稳定\n+            sleep(3);\n+            \n+            // 获取IP地址信息\n+            char ipAddr[40] = { 0 };\n+            char brdAddr[40] = { 0 };\n+            if (0 == check_device_ip_info(g_ifname, ipAddr, brdAddr)) {\n+                LOG_DEBUG(\"成功获取到ip %s, 广播地址 %s\", ipAddr, brdAddr);\n+                demo_set_device_ipaddr_for_callback(ipAddr, brdAddr);\n+            } else {\n+                LOG_WARN(\"WiFi连接成功但暂未获取到IP地址\");\n+                // 使用空地址，后续可能会重新获取\n                 char empty_ip[] = \"\";\n                 demo_set_device_ipaddr_for_callback(empty_ip, empty_ip);\n-                return -1;\n             }\n+        } else {\n+            LOG_ERROR(\"ROS2网络服务连接WiFi失败: %s\", wificfg->ssid);\n+            dn_send_cmd_status = 7;\n+            char empty_ip[] = \"\";\n+            demo_set_device_ipaddr_for_callback(empty_ip, empty_ip);\n+            return -1;\n         }\n             \n         sleep(1);\n         // === 修改 network.conf ===\n"}, {"date": 1754283657015, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -598,63 +598,10 @@\n             return -1;\n         }\n             \n         sleep(1);\n-        // === 修改 network.conf ===\n-        // 该部分代码已被注释，保留原样不修改\n-\n-        // === 修改完成 ===\n-\n-        // 获取当前路由表信息\n-        FILE *fp = popen(\"route -n\", \"r\");\n-        if (!fp) {\n-            LOG_ERROR(\"Failed to run route command.\");\n-            dn_send_cmd_status = 7;\n-            return -1;\n-        }\n-\n-        char buffer[1024];\n-        std::string route_output;\n-        std::string gateway;\n-        while (fgets(buffer, sizeof(buffer), fp) != nullptr) {\n-            route_output += buffer;\n-\n-            // 提取 wlan0 的网关地址\n-            if (strstr(buffer, g_ifname) && strstr(buffer, \"UG\")) {\n-                std::istringstream iss(buffer);\n-                std::string destination, gw, genmask, flags, iface;\n-                iss >> destination >> gw >> genmask >> flags >> iface;\n-                gateway = gw;\n-            }\n-        }\n-        pclose(fp);\n+        // ROS2服务已处理网络连接，无需额外路由配置\n         \n-        wifi_cmd = std::string(\"systemctl restart systemd-resolved\");\n-        result = system(wifi_cmd.c_str());\n-        \n-        // 打印初次查询的路由表信息\n-        LOG_DEBUG(\"Initial Routing Table: \\n%s\", route_output.c_str());\n-\n-        // 检查并调整 wlan0 的 metric 值\n-        /*\n-        if (!gateway.empty()) {\n-            std::string delete_cmd = \"ip route del default via \" + gateway + \" dev wlan0\";\n-            std::string add_cmd = \"ip route add default via \" + gateway + \" dev wlan0 metric 60\";\n-            system(delete_cmd.c_str());\n-            result = system(add_cmd.c_str());\n-\n-            if (result == -1) {\n-                LOG_ERROR(\"Failed to modify wlan0 metric.\");\n-            } \n-        } else {\n-            LOG_ERROR(\"Failed to find wlan0 gateway in routing table.\");\n-            return -1;\n-        }\n-        */\n-        \n-        // 发送调试数据到服务器\n-        // 该部分代码已被注释，保留原样不修改\n-        \n         dn_send_cmd_status = 7;\n         return 0;\n     } else {\n         LOG_DEBUG(\"opt!=1, do not try reconnect wifi\");\n"}, {"date": *************, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1124,20 +1124,26 @@\n     \n     // 调用服务\n     auto future = network_service_client_->async_send_request(request);\n     \n-    // 等待响应\n-    if (rclcpp::spin_until_future_complete(this->get_node_base_interface(), future, std::chrono::seconds(30)) == \n-        rclcpp::FutureReturnCode::SUCCESS) {\n-        auto response = future.get();\n-        LOG_INFO(\"网络服务响应: 错误码=%d, 结果=%s\", response->error_code, response->result.c_str());\n-        \n-        // 检查响应是否成功\n-        if (response->error_code == 200) {\n-            LOG_INFO(\"WiFi连接服务调用成功\");\n-            return true;\n-        } else {\n-            LOG_ERROR(\"WiFi连接失败，错误码: %d\", response->error_code);\n+    // 等待响应 - 使用简单的阻塞等待\n+    auto status = future.wait_for(std::chrono::seconds(30));\n+    \n+    if (status == std::future_status::ready) {\n+        try {\n+            auto response = future.get();\n+            LOG_INFO(\"网络服务响应: 错误码=%d, 结果=%s\", response->error_code, response->result.c_str());\n+            \n+            // 检查响应是否成功\n+            if (response->error_code == 200) {\n+                LOG_INFO(\"WiFi连接服务调用成功\");\n+                return true;\n+            } else {\n+                LOG_ERROR(\"WiFi连接失败，错误码: %d\", response->error_code);\n+                return false;\n+            }\n+        } catch (const std::exception& e) {\n+            LOG_ERROR(\"网络服务调用异常: %s\", e.what());\n             return false;\n         }\n     } else {\n         LOG_ERROR(\"网络服务调用超时\");\n"}, {"date": 1754284475302, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -33,8 +33,9 @@\n #include <string>\n \n #include <array>\n #include <memory>\n+#include <future>\n #include <cstring> // for strstr\n \n // 获取带毫秒的当前时间字符串\n std::string get_current_time_string() {\n"}, {"date": 1754285027587, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1113,9 +1113,59 @@\n         LOG_ERROR(\"网络服务不可用\");\n         return false;\n     }\n \n-    // 构建服务请求\n+    // 1. 先检查WiFi状态，如果未打开则打开\n+    auto status_request = std::make_shared<homi_speech_interface::srv::NetCtrl::Request>();\n+    status_request->data = \"{\\\"command\\\": \\\"getNetworkStatus\\\"}\";\n+    \n+    LOG_INFO(\"检查网络状态...\");\n+    auto status_future = network_service_client_->async_send_request(status_request);\n+    auto status_result = status_future.wait_for(std::chrono::seconds(10));\n+    \n+    if (status_result == std::future_status::ready) {\n+        try {\n+            auto status_response = status_future.get();\n+            LOG_INFO(\"网络状态响应: %s\", status_response->result.c_str());\n+            \n+            // 解析响应，检查wifiState\n+            if (status_response->result.find(\"\\\"wifiState\\\":\\\"off\\\"\") != std::string::npos ||\n+                status_response->result.find(\"\\\"wifiState\\\": \\\"off\\\"\") != std::string::npos) {\n+                LOG_INFO(\"WiFi当前处于关闭状态，正在打开WiFi...\");\n+                \n+                // 打开WiFi\n+                auto enable_request = std::make_shared<homi_speech_interface::srv::NetCtrl::Request>();\n+                enable_request->data = \"{\\\"command\\\": \\\"setWifiState\\\", \\\"state\\\": \\\"on\\\"}\";\n+                \n+                auto enable_future = network_service_client_->async_send_request(enable_request);\n+                auto enable_result = enable_future.wait_for(std::chrono::seconds(15));\n+                \n+                if (enable_result == std::future_status::ready) {\n+                    auto enable_response = enable_future.get();\n+                    if (enable_response->error_code == 200) {\n+                        LOG_INFO(\"WiFi已成功打开\");\n+                        sleep(3); // 等待WiFi模块完全启动\n+                    } else {\n+                        LOG_ERROR(\"打开WiFi失败，错误码: %d\", enable_response->error_code);\n+                        return false;\n+                    }\n+                } else {\n+                    LOG_ERROR(\"打开WiFi操作超时\");\n+                    return false;\n+                }\n+            } else {\n+                LOG_INFO(\"WiFi已处于打开状态\");\n+            }\n+        } catch (const std::exception& e) {\n+            LOG_ERROR(\"检查WiFi状态异常: %s\", e.what());\n+            return false;\n+        }\n+    } else {\n+        LOG_ERROR(\"检查WiFi状态超时\");\n+        return false;\n+    }\n+\n+    // 2. 连接WiFi网络\n     auto request = std::make_shared<homi_speech_interface::srv::NetCtrl::Request>();\n     \n     // 构建JSON数据\n     std::string json_data = \"{\\\"command\\\": \\\"connectWifi\\\", \\\"ssid\\\": \\\"\" + ssid + \"\\\", \\\"password\\\": \\\"\" + password + \"\\\"}\";\n"}, {"date": 1754285089204, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1113,59 +1113,9 @@\n         LOG_ERROR(\"网络服务不可用\");\n         return false;\n     }\n \n-    // 1. 先检查WiFi状态，如果未打开则打开\n-    auto status_request = std::make_shared<homi_speech_interface::srv::NetCtrl::Request>();\n-    status_request->data = \"{\\\"command\\\": \\\"getNetworkStatus\\\"}\";\n-    \n-    LOG_INFO(\"检查网络状态...\");\n-    auto status_future = network_service_client_->async_send_request(status_request);\n-    auto status_result = status_future.wait_for(std::chrono::seconds(10));\n-    \n-    if (status_result == std::future_status::ready) {\n-        try {\n-            auto status_response = status_future.get();\n-            LOG_INFO(\"网络状态响应: %s\", status_response->result.c_str());\n-            \n-            // 解析响应，检查wifiState\n-            if (status_response->result.find(\"\\\"wifiState\\\":\\\"off\\\"\") != std::string::npos ||\n-                status_response->result.find(\"\\\"wifiState\\\": \\\"off\\\"\") != std::string::npos) {\n-                LOG_INFO(\"WiFi当前处于关闭状态，正在打开WiFi...\");\n-                \n-                // 打开WiFi\n-                auto enable_request = std::make_shared<homi_speech_interface::srv::NetCtrl::Request>();\n-                enable_request->data = \"{\\\"command\\\": \\\"setWifiState\\\", \\\"state\\\": \\\"on\\\"}\";\n-                \n-                auto enable_future = network_service_client_->async_send_request(enable_request);\n-                auto enable_result = enable_future.wait_for(std::chrono::seconds(15));\n-                \n-                if (enable_result == std::future_status::ready) {\n-                    auto enable_response = enable_future.get();\n-                    if (enable_response->error_code == 200) {\n-                        LOG_INFO(\"WiFi已成功打开\");\n-                        sleep(3); // 等待WiFi模块完全启动\n-                    } else {\n-                        LOG_ERROR(\"打开WiFi失败，错误码: %d\", enable_response->error_code);\n-                        return false;\n-                    }\n-                } else {\n-                    LOG_ERROR(\"打开WiFi操作超时\");\n-                    return false;\n-                }\n-            } else {\n-                LOG_INFO(\"WiFi已处于打开状态\");\n-            }\n-        } catch (const std::exception& e) {\n-            LOG_ERROR(\"检查WiFi状态异常: %s\", e.what());\n-            return false;\n-        }\n-    } else {\n-        LOG_ERROR(\"检查WiFi状态超时\");\n-        return false;\n-    }\n-\n-    // 2. 连接WiFi网络\n+    // 构建服务请求\n     auto request = std::make_shared<homi_speech_interface::srv::NetCtrl::Request>();\n     \n     // 构建JSON数据\n     std::string json_data = \"{\\\"command\\\": \\\"connectWifi\\\", \\\"ssid\\\": \\\"\" + ssid + \"\\\", \\\"password\\\": \\\"\" + password + \"\\\"}\";\n"}], "date": 1754283548487, "name": "Commit-0", "content": "#include <cstring>\n#include <regex>\n#include <netinet/in.h>\n#include <csignal> // 添加头文件\n#include \"rclcpp/rclcpp.hpp\"\n#include <string>\n#include \"std_msgs/msg/string.hpp\"\n#include \"std_msgs/msg/byte_multi_array.hpp\" // 添加头文件\n#include \"andlink_node.hpp\"\n#include <fstream>\n#include <stdio.h>\n#include <stdlib.h>\n#include <dirent.h>\n#include <unistd.h>\n#include <ctype.h>\n#include <signal.h>\n#include <algorithm>\n#include <cctype>\n#include <vector>\n#include <sstream>\n#include <homi_speech_interface/msg/sigc_event.hpp>\n#define WPA_CONF_FILE \"/etc/wpa_supplicant/wpa_supplicant-wlan0.conf\"\n// 简化宏定义\n#define LOG_INFO(format, ...) log_printf(\"INFO\", format, ##__VA_ARGS__)\n#define LOG_WARN(format, ...) log_printf(\"WARN\", format, ##__VA_ARGS__)\n#define LOG_ERROR(format, ...) log_printf(\"ERROR\", format, ##__VA_ARGS__)\n#define LOG_DEBUG(format, ...) log_printf(\"DEBUG\", format, ##__VA_ARGS__)\n\n#include <chrono>\n#include <cstdio>\n#include <iomanip>\n#include <sstream>\n#include <string>\n\n#include <array>\n#include <memory>\n#include <cstring> // for strstr\n\n// 获取带毫秒的当前时间字符串\nstd::string get_current_time_string() {\n    auto now = std::chrono::system_clock::now();\n    auto now_time = std::chrono::system_clock::to_time_t(now);\n    auto now_ms = std::chrono::duration_cast<std::chrono::milliseconds>(\n                    now.time_since_epoch()) % 1000;\n    \n    std::stringstream ss;\n    ss << \"[\" << std::put_time(std::localtime(&now_time), \"%Y-%m-%d %H:%M:%S\")\n       << \".\" << std::setfill('0') << std::setw(3) << now_ms.count() << \"]\";\n    return ss.str();\n}\n\n// 带时间戳的日志函数\nvoid log_printf(const char* prefix, const char* format, ...) {\n    // 获取当前时间\n    std::string time_str = get_current_time_string();\n    \n    // 构造完整格式\n    char full_format[512];\n    snprintf(full_format, sizeof(full_format), \"%s [%s] %s\\n\", time_str.c_str(), prefix, format);\n    \n    // 打印日志\n    va_list args;\n    va_start(args, format);\n    vprintf(full_format, args);\n    va_end(args);\n}\n\n// static int s_quit = 0;\nint dn_send_cmd_status = 0;\nchar* g_json_str = nullptr; \nchar g_ifname[16]= \"\";\nchar g_p2p_ifname[16]= \"\";\n\nstruct DeviceInfo {\n    char ip[INET_ADDRSTRLEN];\n    char brdAddr[INET_ADDRSTRLEN];\n};\n\nnamespace {\n\nbool is_hex_digit(char c) {\n    return (c >= '0' && c <= '9') || \n           (c >= 'a' && c <= 'f') || \n           (c >= 'A' && c <= 'F');\n}\n\n// 增强的SSID解码函数，处理各种转义序列\nstd::string decode_ssid(const std::string& input) {\n    std::string result;\n    size_t i = 0;\n    size_t len = input.size();\n\n    while (i < len) {\n        // 处理 \\xXX 转义序列（中文等非ASCII字符）\n        if (i + 3 < len && input[i] == '\\\\' && input[i+1] == 'x' &&\n            is_hex_digit(input[i+2]) && is_hex_digit(input[i+3])) {\n            \n            int hex_value;\n            std::stringstream ss;\n            ss << std::hex << input.substr(i+2, 2);\n            ss >> hex_value;\n            \n            result += static_cast<char>(hex_value);\n            i += 4;\n        }\n        // 处理其他转义序列（如\\t, \\n, \\\\ 等）\n        else if (i + 1 < len && input[i] == '\\\\') {\n            switch (input[i+1]) {\n                case '\\\\': result += '\\\\'; break;\n                case '\"':  result += '\"';  break;\n                case '\\'': result += '\\''; break;\n                case 't':  result += '\\t'; break;\n                case 'n':  result += '\\n'; break;\n                case 'r':  result += '\\r'; break;\n                default:   // 未知转义序列，保留原样\n                    result += '\\\\';\n                    result += input[i+1];\n                    break;\n            }\n            i += 2;\n        }\n        // 普通字符\n        else {\n            result += input[i];\n            i++;\n        }\n    }\n    return result;\n}\n\n// 安全提取SSID行\nstd::string extract_ssid_line(const std::string& line) {\n    size_t pos = line.find(\"SSID: \");\n    if (pos == std::string::npos) {\n        return \"\";\n    }\n    \n    std::string ssid_line = line.substr(pos + 6); // \"SSID: \" 长度为6\n    \n    // 移除行尾的换行符和空白字符\n    size_t endpos = ssid_line.find_last_not_of(\" \\r\\n\\t\");\n    if (endpos != std::string::npos) {\n        ssid_line = ssid_line.substr(0, endpos + 1);\n    } else {\n        ssid_line.clear();\n    }\n    \n    return ssid_line;\n}\n\n} // 匿名命名空间\n\nbool isWlan0ConnectedToSSID(const std::string& ssid) {\n    std::string cmd = \"iw dev \" + std::string(g_ifname) + \" link\";\n    LOG_DEBUG(\"Executing command: %s\", cmd.c_str());\n    \n    std::array<char, 512> buffer; // 使用更大的缓冲区处理长SSID\n    std::unique_ptr<FILE, decltype(&pclose)> pipe(popen(cmd.c_str(), \"r\"), pclose);\n    \n    if (!pipe) {\n        LOG_ERROR(\"Failed to execute iw command\");\n        return false;\n    }\n\n    bool is_connected = false;\n    bool found_not_connected = false;\n    std::string raw_ssid = \"\";\n\n    while (fgets(buffer.data(), buffer.size(), pipe.get()) != nullptr) {\n        std::string line(buffer.data());\n        \n        // 检查是否包含 \"Not connected\" 信息\n        if (line.find(\"Not connected\") != std::string::npos) {\n            found_not_connected = true;\n        }\n        \n        // 查找 SSID 行\n        if (line.find(\"SSID: \") != std::string::npos) {\n            LOG_DEBUG(\"Found SSID field in command output\");\n            is_connected = true;\n            \n            raw_ssid = extract_ssid_line(line);\n            if (raw_ssid.empty()) {\n                LOG_WARN(\"SSID field found but could not extract value\");\n                continue;\n            }\n            \n            // 处理转义序列\n            std::string actual_ssid = decode_ssid(raw_ssid);\n            \n            LOG_DEBUG(\"Raw SSID from command: '%s'\", raw_ssid.c_str());\n            LOG_DEBUG(\"Decoded SSID: '%s'\", actual_ssid.c_str());\n            LOG_DEBUG(\"Target SSID: '%s'\", ssid.c_str());\n            \n            // 比较处理后的SSID\n            if (actual_ssid == ssid) {\n                LOG_INFO(\"Already connected to same SSID '%s'\", ssid.c_str());\n                return true;\n            }\n            \n            LOG_WARN(\"Connected to different SSID '%s' (expected '%s')\", \n                   actual_ssid.c_str(), ssid.c_str());\n            return false;\n        }\n    }\n\n    if (found_not_connected) {\n        LOG_INFO(\"Interface %s is not connected to any network\", g_ifname);\n    } else if (is_connected) {\n        LOG_INFO(\"Interface %s is connected but no SSID information found\", g_ifname);\n    } else {\n        LOG_INFO(\"Interface %s status unknown from command output\", g_ifname);\n    }\n    \n    return false;\n}\n\nbool scanForExactSSID(const std::string &ssid) {\n    LOG_DEBUG(\"Starting scan for exact SSID: '%s'\", ssid.c_str());\n    \n    FILE *fp = popen(\"nmcli device wifi list --rescan yes\", \"r\");\n    if (!fp) {\n        LOG_ERROR(\"Failed to execute nmcli scan command.\");\n        return false;\n    }\n    LOG_DEBUG(\"Scan command executed successfully\");\n\n    char buffer[512];\n    std::regex ssid_regex(R\"((?:\\*|\\s)\\s+([^\\s]+(?:\\s[^\\s]+)*)\\s+Infra)\");\n    int line_num = 0;\n    int match_count = 0;\n\n    while (fgets(buffer, sizeof(buffer), fp) != nullptr) {\n        line_num++;\n        std::string line(buffer);\n        LOG_DEBUG(\"Processing line %d (raw): %s\", line_num, line.c_str());\n\n        std::smatch match;\n        if (std::regex_search(line, match, ssid_regex)) {\n            match_count++;\n            std::string current_ssid = match[1].str();\n            // 去除首尾空格\n            auto start = current_ssid.find_first_not_of(\" \");\n            auto end = current_ssid.find_last_not_of(\" \");\n            current_ssid = (start == std::string::npos) ? \"\" : current_ssid.substr(start, end - start + 1);\n            \n            LOG_DEBUG(\"[MATCH %d] Detected SSID: '%s' (Raw capture: '%s')\", \n                     match_count, current_ssid.c_str(), match[1].str().c_str());\n\n            if (current_ssid == ssid) {\n                LOG_INFO(\"Exact match found on line %d!\", line_num);\n                pclose(fp);\n                return true;\n            }\n        }\n    }\n\n    LOG_INFO(\"Scan completed. Total lines: %d, Total matches: %d. Target SSID not found.\", \n             line_num, match_count);\n    pclose(fp);\n    return false;\n}\n\nvoid reset_network_manager() {\n    std::string wifi_cmd;\n    int result = 0;\n    // 2. 删除所有 NetworkManager 配置并重启服务\n    LOG_INFO(\"Removing all NetworkManager configurations...\");\n    wifi_cmd = \"sudo rm -rf /etc/NetworkManager/system-connections/*\";\n    result = system(wifi_cmd.c_str());\n\n    if (result == -1) {\n        LOG_ERROR(\"Failed to remove NetworkManager configurations\");\n    } else {\n        int exit_status = WEXITSTATUS(result);\n        if (exit_status != 0) {\n            LOG_ERROR(\"Remove command exited with status: %d\", exit_status);\n        } else {\n            LOG_DEBUG(\"NetworkManager configurations removed successfully\");\n        }\n    }\n\n    LOG_INFO(\"Restarting NetworkManager service...\");\n    wifi_cmd = \"sudo systemctl restart NetworkManager.service\";\n    result = system(wifi_cmd.c_str());\n\n    if (result == -1) {\n        LOG_ERROR(\"Failed to restart NetworkManager service\");\n    } else {\n        int exit_status = WEXITSTATUS(result);\n        if (exit_status != 0) {\n            LOG_ERROR(\"Restart command exited with status: %d\", exit_status);\n        } else {\n            LOG_DEBUG(\"NetworkManager service restarted successfully\");\n        }\n    }\n    \n    // 3. 等待服务完全启动\n    sleep(4);\n}\n\nint get_user_bind() {\n    const std::string config_path = \"/etc/cmcc_robot/andlinkSdk.conf\";  // 配置文件路径\n    \n    try {\n        // 1. 检查配置文件是否存在\n        std::ifstream file(config_path);\n        if (!file.is_open()) {\n            LOG_ERROR(\"Configuration file %s does not exist.\", config_path.c_str());\n            return 0;\n        }\n        \n        // 2. 逐行读取配置文件\n        std::string line;\n        while (std::getline(file, line)) {\n            // 跳过空行和注释行\n            if (line.empty() || line[0] == '#') continue;\n            \n            // 查找 userBind 配置项\n            if (line.find(\"userBind\") != std::string::npos) {\n                // 提取键值对\n                size_t eq_pos = line.find('=');\n                if (eq_pos != std::string::npos) {\n                    // 提取值部分\n                    std::string value = line.substr(eq_pos + 1);\n                    \n                    // 清除值两端的空白字符\n                    size_t start = value.find_first_not_of(\" \\t\");\n                    if (start != std::string::npos) {\n                        size_t end = value.find_last_not_of(\" \\t\");\n                        value = value.substr(start, end - start + 1);\n                    }\n                    \n                    LOG_INFO(\"Found userBind value: %s\", value.c_str());\n                    \n                    // 如果值为 \"1\" 返回 1，否则返回 0\n                    return (value == \"1\") ? 1 : 0;\n                }\n            }\n        }\n        \n        // 3. 如果未找到 userBind 配置项\n        LOG_ERROR(\"userBind configuration not found\");\n        return 0;\n        \n    } catch (...) {\n        // 捕获所有异常并返回 0\n        LOG_ERROR(\"Exception occurred while reading userBind configuration\");\n        return 0;\n    }\n}\n\nbool write_wpa_conf_file(const wifi_cfg_info_t* wificfg) {\n    std::ofstream fd(WPA_CONF_FILE);\n    if (!fd.is_open()) {\n        LOG_ERROR(\"Failed to create/open file: %s\", WPA_CONF_FILE);\n        return false;\n    }\n\n    try {\n        // 写入文件头和使用通用配置\n        fd << \"# Auto-generated wpa_supplicant configuration\\n\"\n           << \"ctrl_interface=/var/run/wpa_supplicant\\n\"\n           << \"ap_scan=1\\n\"\n           << \"update_config=1\\n\"\n           << \"country=CN\\n\\n\";\n\n        // 验证必要参数\n        if (!wificfg->ssid || !wificfg->password || \n            !wificfg->ssid[0] || !wificfg->password[0]) {\n            throw std::invalid_argument(\"Invalid SSID or password\");\n        }\n\n        // 构建网络配置块\n        std::string network_block = \"network={\\n\";\n        network_block += \"    ssid=\\\"\" + std::string(wificfg->ssid) + \"\\\"\\n\";\n        \n        // 添加 BSSID（如果存在且有效）\n        if (wificfg->mac && wificfg->mac[0]) {\n            network_block += \"    bssid=\" + std::string(wificfg->mac) + \"\\n\";\n        }\n        \n        // 添加 password 和扫描设置\n        network_block += \"    psk=\\\"\" + std::string(wificfg->password) + \"\\\"\\n\";\n        network_block += \"    scan_ssid=1\\n\";\n        \n        // 添加 key_mgmt=WPA-PSK 设置（解决兼容性问题）\n        network_block += \"    key_mgmt=WPA-PSK\\n\";  // 明确指定密钥管理方式\n        \n        network_block += \"}\\n\";\n\n        // 写入网络配置块\n        fd << network_block;\n        \n        // 添加调试信息\n        LOG_DEBUG(\"Generated wpa_supplicant configuration:\\n%s\", network_block.c_str());\n        \n        return true;\n    } \n    catch (const std::exception& e) {\n        LOG_ERROR(\"Configuration error: %s\", e.what());\n        // 删除部分写入的文件\n        std::remove(WPA_CONF_FILE);\n        return false;\n    }\n}\n\nint get_wpa_supplicant_pid() {\n    const char* TARGET_NAME = \"wpa_supplicant\";\n    // 必须匹配的参数\n    std::vector<std::string> REQUIRED_ARGS = {\n        \"-i\", \"wlan0\",\n        \"-c\", \"/etc/wpa_supplicant/wpa_supplicant-wlan0.conf\"\n    };\n    int target_pid = 0;\n    int killed_processes = 0;\n\n    LOG_DEBUG(\"开始搜索目标 wpa_supplicant 进程...\");\n    \n    std::string debug_msg = \"目标参数要求: \";\n    for (const auto& arg : REQUIRED_ARGS) {\n        debug_msg += arg + \" \";\n    }\n    LOG_DEBUG(\"%s\", debug_msg.c_str());\n\n    DIR *dir = opendir(\"/proc\");\n    if (!dir) {\n        LOG_ERROR(\"无法打开 /proc 目录\");\n        return 0;\n    }\n\n    struct dirent *entry;\n    while ((entry = readdir(dir))) {\n        if (entry->d_type != DT_DIR) continue;\n        \n        char *endptr;\n        long pid = strtol(entry->d_name, &endptr, 10);\n        if (*endptr != '\\0') continue;\n        \n        char cmdline_path[256];\n        snprintf(cmdline_path, sizeof(cmdline_path), \"/proc/%ld/cmdline\", pid);\n        \n        FILE *cmdline_file = fopen(cmdline_path, \"r\");\n        if (!cmdline_file) continue;\n        \n        char cmdline[1024] = {0};\n        if (fgets(cmdline, sizeof(cmdline) - 1, cmdline_file)) {\n            // 改进的参数解析逻辑\n            std::vector<std::string> args;\n            char* p = cmdline;\n            while (*p != '\\0') {\n                args.push_back(p);\n                p += strlen(p) + 1;\n            }\n            \n            // 检查程序名是否匹配\n            bool is_wpa = false;\n            for (const auto& arg : args) {\n                if (arg.find(TARGET_NAME) != std::string::npos) {\n                    is_wpa = true;\n                    break;\n                }\n            }\n            \n            if (!is_wpa) {\n                fclose(cmdline_file);\n                continue;\n            }\n            \n            LOG_DEBUG(\"找到 wpa_supplicant 进程 PID: %ld\", pid);\n            \n            debug_msg = \"完整命令行: \";\n            for (const auto& arg : args) {\n                debug_msg += arg + \" \";\n            }\n            LOG_DEBUG(\"%s\", debug_msg.c_str());\n            \n            // 检查是否包含所有必需参数\n            bool is_target = true;\n            for (const auto& req_arg : REQUIRED_ARGS) {\n                bool found = false;\n                for (const auto& arg : args) {\n                    if (arg == req_arg) {\n                        found = true;\n                        break;\n                    }\n                }\n                if (!found) {\n                    is_target = false;\n                    LOG_DEBUG(\"缺失参数: %s\", req_arg.c_str());\n                    break;\n                }\n            }\n            \n            // 检查是否包含后台参数 (-B)\n            bool has_B_option = false;\n            for (const auto& arg : args) {\n                if (arg == \"-B\") {\n                    has_B_option = true;\n                    break;\n                }\n            }\n            \n            // 关键修改：只有配置文件路径匹配才是目标进程\n            if (is_target) {\n                LOG_INFO(\"找到目标进程 PID: %ld (参数完全匹配)\", pid);\n                target_pid = (int)pid;\n            } \n            // 关键修改：移除 has_B_option 分支\n            else {\n                LOG_WARN(\"杀死非目标进程 PID: %ld (参数不匹配)\", pid);\n                if (kill(pid, SIGTERM) == 0) {\n                    LOG_INFO(\"已发送 SIGTERM 到 PID: %ld\", pid);\n                    killed_processes++;\n                } else {\n                    LOG_ERROR(\"杀死进程失败: %s\", strerror(errno));\n                }\n            }\n        }\n        fclose(cmdline_file);\n    }\n    closedir(dir);\n    \n    if (target_pid == 0) {\n        if (killed_processes > 0) {\n            LOG_INFO(\"已杀死 %d 个不匹配的 wpa_supplicant 进程\", killed_processes);\n            sleep(5);  // 确保进程完全终止\n        } else {\n            LOG_INFO(\"未找到运行的目标 wpa_supplicant 进程\");\n        }\n    } else {\n        LOG_INFO(\"返回目标 PID: %d\", target_pid);\n    }\n    \n    return target_pid;\n}\n\nint andlink_ctrl_wifi_callback(WIFI_CTRL_OPT_e opt, wifi_cfg_info_t *wificfg, char *outMsg, unsigned int msgBufSize)\n{   \n    LOG_DEBUG(\"enter andlink_ctrl_wifi_callback\");\n\n    if (opt == 1) {\n\n        // 禁止连接移动闪连\n        if (strcmp(wificfg->ssid, \"CMCC-GUIDE-LINK\") == 0) {\n            LOG_DEBUG(\"连接移动闪连, 禁止\");\n            return -1;\n        }\n\n        // 检查 wlan0 是否已连接到目标 WiFi\n        if (isWlan0ConnectedToSSID(wificfg->ssid)) {\n            LOG_DEBUG(\"wlan0 已经连接到 WiFi: %s\", wificfg->ssid);\n            return 0;\n        }\n\n        LOG_DEBUG(\"wlan0 去连接 WiFi: %s\", wificfg->ssid);\n        dn_send_cmd_status = 6;\n        sleep(2);\n        std::string wifi_cmd;\n        int result = 0;\n\n        // 1. 断开 wlan0 连接\n        wifi_cmd = \"wpa_cli -i wlan0 disconnect\";\n        LOG_DEBUG(\"Executing command: %s\", wifi_cmd.c_str());\n        result = system(wifi_cmd.c_str());\n\n        if (result == -1) {\n            LOG_ERROR(\"Failed to execute disconnect command.\");\n        } else {\n            int exit_status = WEXITSTATUS(result);\n            if (exit_status != 0) {\n                LOG_ERROR(\"Disconnect command exited with status: %d\", exit_status);\n            } else {\n                LOG_DEBUG(\"Command executed disconnect successfully.\");\n            }\n        }\n        sleep(1);\n\n        // 2. 重写wpa配置文件\n        write_wpa_conf_file(wificfg);\n        /*\n        int user_bind = get_user_bind();\n        if (user_bind == 0) {\n            LOG_DEBUG(\"当前是绑定配网操作, 清除并重启网络服务\");\n            reset_network_manager();\n        } else {\n            LOG_DEBUG(\"当前设备已绑定, 是恢复网络操作, 不需要清除重启网络服务\");\n        }\n        */\n        \n        // 3.判断是否后台有进程，没有重启wpa_supplicant\n        if (get_wpa_supplicant_pid() == 0) {\n            LOG_DEBUG(\"后台没有wpa_supplicant, 启动wpa_supplicant.\");\n            wifi_cmd = \"wpa_supplicant -i wlan0 -B -c /etc/wpa_supplicant/wpa_supplicant-wlan0.conf\";\n            LOG_DEBUG(\"Executing command: %s\", wifi_cmd.c_str());\n            result = system(wifi_cmd.c_str());\n        } else {\n            LOG_DEBUG(\"后台已经有wpa_supplicant, 执行wpa_cli -i wlan0 reconfigure.\");\n            wifi_cmd = \"wpa_cli -i wlan0 reconfigure\"; \n            LOG_DEBUG(\"Executing command: %s\", wifi_cmd.c_str());\n            result = system(wifi_cmd.c_str());\n                \n            wifi_cmd = \"wpa_cli -i wlan0 reconnect\";\n            LOG_DEBUG(\"Executing command: %s\", wifi_cmd.c_str()); \n            result = system(wifi_cmd.c_str());            \n        }\n        sleep(5);\n        \n        // 4. 开始连接\n        int retry_count = 0;\n        int max_retries = 20;\n        bool connect = false;\n        char ipAddr[40] = { 0 };\n        char brdAddr[40] = { 0 };\n        \n        if ((!isWlan0ConnectedToSSID(wificfg->ssid)) || (0 != check_device_ip_info(g_ifname, ipAddr, brdAddr))) {\n            while (retry_count < max_retries) {\n                /*\n                wifi_cmd = \"wpa_cli -i wlan0 reconnect\";\n                LOG_DEBUG(\"Connecting for exact SSID: %s (attempt %d/%d)\", wificfg->ssid, retry_count+1, max_retries); \n                LOG_DEBUG(\"Executing command: %s\", wifi_cmd.c_str());\n                result = system(wifi_cmd.c_str());\n                */\n                sleep(2);\n                if (result == -1) {\n                    LOG_ERROR(\"Failed to execute connect command.\");\n                } else {\n                    // 验证是否真正连接成功\n                    if (isWlan0ConnectedToSSID(wificfg->ssid)) {\n                        LOG_DEBUG(\"Wifi成功连上 %s.\", wificfg->ssid);\n                        if (0 == check_device_ip_info(g_ifname, ipAddr, brdAddr)) {\n                            LOG_DEBUG(\"成功获取到ip %s, ipAddr %s.\", ipAddr, brdAddr);\n                            connect = true;\n                            demo_set_device_ipaddr_for_callback(ipAddr, brdAddr);\n                            break;\n                        } else {\n                            LOG_DEBUG(\"还未获取到ip.\"); \n                        }\n                    } else {\n                        LOG_ERROR(\"Wifi还未连上 %s.\", wificfg->ssid);\n                    }\n                }\n                retry_count++;\n            }\n            \n            if (!connect) {\n                LOG_ERROR(\"Failed after %d attempts\", max_retries);\n                dn_send_cmd_status = 7;\n                char empty_ip[] = \"\";\n                demo_set_device_ipaddr_for_callback(empty_ip, empty_ip);\n                return -1;\n            }\n        }\n            \n        sleep(1);\n        // === 修改 network.conf ===\n        // 该部分代码已被注释，保留原样不修改\n\n        // === 修改完成 ===\n\n        // 获取当前路由表信息\n        FILE *fp = popen(\"route -n\", \"r\");\n        if (!fp) {\n            LOG_ERROR(\"Failed to run route command.\");\n            dn_send_cmd_status = 7;\n            return -1;\n        }\n\n        char buffer[1024];\n        std::string route_output;\n        std::string gateway;\n        while (fgets(buffer, sizeof(buffer), fp) != nullptr) {\n            route_output += buffer;\n\n            // 提取 wlan0 的网关地址\n            if (strstr(buffer, g_ifname) && strstr(buffer, \"UG\")) {\n                std::istringstream iss(buffer);\n                std::string destination, gw, genmask, flags, iface;\n                iss >> destination >> gw >> genmask >> flags >> iface;\n                gateway = gw;\n            }\n        }\n        pclose(fp);\n        \n        wifi_cmd = std::string(\"systemctl restart systemd-resolved\");\n        result = system(wifi_cmd.c_str());\n        \n        // 打印初次查询的路由表信息\n        LOG_DEBUG(\"Initial Routing Table: \\n%s\", route_output.c_str());\n\n        // 检查并调整 wlan0 的 metric 值\n        /*\n        if (!gateway.empty()) {\n            std::string delete_cmd = \"ip route del default via \" + gateway + \" dev wlan0\";\n            std::string add_cmd = \"ip route add default via \" + gateway + \" dev wlan0 metric 60\";\n            system(delete_cmd.c_str());\n            result = system(add_cmd.c_str());\n\n            if (result == -1) {\n                LOG_ERROR(\"Failed to modify wlan0 metric.\");\n            } \n        } else {\n            LOG_ERROR(\"Failed to find wlan0 gateway in routing table.\");\n            return -1;\n        }\n        */\n        \n        // 发送调试数据到服务器\n        // 该部分代码已被注释，保留原样不修改\n        \n        dn_send_cmd_status = 7;\n        return 0;\n    } else {\n        LOG_DEBUG(\"opt!=1, do not try reconnect wifi\");\n        dn_send_cmd_status = 7;\n        return 0;\n    }\n}\n\nint andlink_get_device_ipaddr_callback(char *outip, char *outbrdAddr)\n{\n    DeviceInfo s_deviceInfo = {\"\", \"\"};\n    int ret = -1;\n\n    LOG_DEBUG(\"enter ysc_get_device_ipaddr_callback\");\n    \n\n    // 如果 IP 和广播地址还未获取，使用 getifaddrs 获取\n    if (strlen(s_deviceInfo.ip) == 0 || strlen(s_deviceInfo.brdAddr) == 0) {\n        struct ifaddrs *interfaces = nullptr;\n        struct ifaddrs *tempAddr = nullptr;\n\n        // 获取本机的所有网络接口信息\n        if (getifaddrs(&interfaces) == 0) {\n            tempAddr = interfaces;\n\n            while (tempAddr != nullptr) {\n                if (tempAddr->ifa_addr && tempAddr->ifa_addr->sa_family == AF_INET) { // 只处理IPv4地址\n                    // 匹配特定网口，例如 \"wlan0\"\n                    if (strcmp(tempAddr->ifa_name, g_ifname) == 0) {\n                        // 获取 IP 地址\n                        struct sockaddr_in *sockaddr_ipv4 = (struct sockaddr_in *)tempAddr->ifa_addr;\n                        inet_ntop(AF_INET, &sockaddr_ipv4->sin_addr, outip, INET_ADDRSTRLEN);\n\n                        // 获取广播地址\n                        if (tempAddr->ifa_broadaddr) {\n                            struct sockaddr_in *sockaddr_brd = (struct sockaddr_in *)tempAddr->ifa_broadaddr;\n                            inet_ntop(AF_INET, &sockaddr_brd->sin_addr, outbrdAddr, INET_ADDRSTRLEN);\n                            LOG_DEBUG(\"Found IP address: %s, Broadcast address: %s\", outip, outbrdAddr);\n                        } else {\n                            LOG_DEBUG(\"Found IP address: %s (no broadcast address)\", outip);\n                        }\n                        ret = 0;\n                        break; // 找到指定接口后退出循环\n                    }\n                }\n                tempAddr = tempAddr->ifa_next;\n            }\n        } else {\n            LOG_ERROR(\"Failed to get network interfaces information\");\n        }\n\n        // 释放内存\n        if (interfaces != nullptr) {\n            freeifaddrs(interfaces);\n        }\n    } else {\n        LOG_DEBUG(\"Using cached IP and broadcast address\");\n    }\n\n    // 返回 IP 地址\n    if (outip) {\n        if (strlen(s_deviceInfo.ip)) {\n            memcpy(outip, s_deviceInfo.ip, sizeof(s_deviceInfo.ip) - 1);\n            LOG_DEBUG(\"Returning cached IP address: %s\", outip);\n            ret = 0;\n        }\n    }\n\n    // 返回广播地址\n    if (outbrdAddr) {\n        if (strlen(s_deviceInfo.brdAddr)) {\n            memcpy(outbrdAddr, s_deviceInfo.brdAddr, sizeof(s_deviceInfo.brdAddr) - 1);\n            LOG_DEBUG(\"Returning cached broadcast address: %s\", outbrdAddr);\n            ret = 0;\n        }\n    }\n\n    if (ret != 0) {\n        LOG_ERROR(\"Failed to retrieve IP or broadcast address\");\n    }\n    ret = 1;\n    return ret;\n}\n\nvoid wait_for_subscriber(\n    rclcpp::Publisher<std_msgs::msg::ByteMultiArray>::SharedPtr publisher,\n    int timeout_seconds = 15)\n{\n    auto start = std::chrono::steady_clock::now();\n    \n    while (rclcpp::ok() && \n          (publisher->get_subscription_count() == 0) &&\n          (std::chrono::steady_clock::now() - start < std::chrono::seconds(timeout_seconds)))\n    {\n        LOG_INFO(\"等待订阅者连接...\");\n        std::this_thread::sleep_for(std::chrono::milliseconds(500)); // 每500ms检查一次\n    }\n    \n    if (publisher->get_subscription_count() > 0) {\n        LOG_INFO(\"订阅者已连接\");\n    } else {\n        LOG_WARN(\"订阅者已超时 (等待时间: %d 秒)\", timeout_seconds);\n    }\n}\nvoid AndlinkNode::handle_andlink_userkey_message(const std_msgs::msg::String::SharedPtr msg) {\n    // 从msg中提取字符串（std_msgs::msg::String的成员是`data`）\n    std::string userkey = msg->data;    \n    // 打印或处理字符串\n    RCLCPP_INFO(this->get_logger(), \"Received userkey: %s\", userkey.c_str());\n    // setScanCodeBindConfigInfo(userkey, NULL, NULL);\n    adlDeviceBinding(const_cast<char*>(userkey.c_str()), NULL);\n}\n\nAndlinkNode::AndlinkNode() : Node(\"andlink_node\")\n{\n    RCLCPP_INFO(this->get_logger(), \"Hello, andlink Node!\");\n\n    andlink_main();\n\n    byte_stream_subscription_ = this->create_subscription<std_msgs::msg::ByteMultiArray>(\n        \"ble_byte_stream\", 10,\n        std::bind(&AndlinkNode::handle_ble_byte_stream_message, this, std::placeholders::_1));\n\n    andlink_userkey_subscription_ = this->create_subscription<std_msgs::msg::String>(\n        \"andlink_userkey\",  // \n        10,                 // 队列长度\n        std::bind(&AndlinkNode::handle_andlink_userkey_message, this, std::placeholders::_1)\n    );\n\n    andlink_cmd_subscription_ = this->create_subscription<std_msgs::msg::ByteMultiArray>(\n        \"andlink_cmd\", 10,\n        std::bind(&AndlinkNode::handle_andlink_cmd_message, this, std::placeholders::_1));\n\n    ble_cmd_publisher_ = this->create_publisher<std_msgs::msg::ByteMultiArray>(\"ble_cmd\", 10);\n    wait_for_subscriber(ble_cmd_publisher_);\n    andlink_network_publisher_ = this->create_publisher<std_msgs::msg::String>(\"andlink_network\", 10);\n    homi_player_publisher_ = this->create_publisher<homi_speech_interface::msg::SIGCEvent>(\"/homi_speech/sigc_event_topic\", 10);\n    \n    // 创建网络服务客户端\n    network_service_client_ = this->create_client<homi_speech_interface::srv::NetCtrl>(\"/homi_speech/network_service\");\n    // 初始化1秒定时器\n    timer_ = this->create_wall_timer(\n        std::chrono::seconds(1),\n        std::bind(&AndlinkNode::handle_timer_callback, this));\n\n    // 新增定时器\n    // log_timer_ = this->create_wall_timer(\n    //     std::chrono::seconds(2),\n    //     std::bind(&AndlinkNode::log_timer_callback, this));\n\n    // 新增字节流发布器\n    // byte_stream_publisher_ = this->create_publisher<std_msgs::msg::ByteMultiArray>(\"ble_byte_stream\", 10);\n    // byte_stream_timer_ = this->create_wall_timer(\n    //     std::chrono::seconds(10),\n    //     std::bind(&AndlinkNode::publish_byte_stream_message, this));\n\n    // 新增发布器\n    // json_publisher_ = this->create_publisher<std_msgs::msg::String>(\"json_topic\", 10);\n\n    // // 新增定时器，用于定期发送 JSON 格式字符串\n    // json_timer_ = this->create_wall_timer(\n    //     std::chrono::seconds(5),\n    //     std::bind(&AndlinkNode::publish_json_message, this));\n}\n\nvoid AndlinkNode::handle_timer_callback()\n{\n    if(dn_send_cmd_status == 1)\n    {\n        dn_send_cmd_status = 0;\n        RCLCPP_INFO(this->get_logger(), \"Timer triggered dn_send_cmd_status == 1.\");\n        publish_ble_cmd_unbind_message();\n    }\n    else if(dn_send_cmd_status == 2)\n    {\n        dn_send_cmd_status = 0;\n        RCLCPP_INFO(this->get_logger(), \"Timer triggered dn_send_cmd_status == 2.\");\n        publish_ble_cmd_broadcast_message();\n    }\n    else if(dn_send_cmd_status == 3)\n    {\n        dn_send_cmd_status = 0;\n        RCLCPP_INFO(this->get_logger(), \"Timer triggered dn_send_cmd_status == 3.\");\n        publish_ble_cmd_unbroadcast_message();\n    }\n    else if(dn_send_cmd_status == 4)\n    {\n        dn_send_cmd_status = 0;\n        RCLCPP_INFO(this->get_logger(), \"Timer triggered dn_send_cmd_status == 4.\");\n        // publish_ble_cmd_unbroadcast_message();\n    }\n    else if(dn_send_cmd_status == 6)\n    {\n        dn_send_cmd_status = 0;\n        RCLCPP_INFO(this->get_logger(), \"Andlink开始联网, dn_send_cmd_status == 6.\");\n        publish_network_connecting_start_message();\n    }\n    else if(dn_send_cmd_status == 7)\n    {\n        dn_send_cmd_status = 0;\n        RCLCPP_INFO(this->get_logger(), \"Andlink结束联网, dn_send_cmd_status == 6.\");\n        publish_network_connecting_end_message();\n    }\n    else if(dn_send_cmd_status == 8)\n    {\n        dn_send_cmd_status = 0;\n        RCLCPP_INFO(this->get_logger(), \"收到绘本管控指令, dn_send_cmd_status == 8.\");\n        if (g_json_str) {\n            auto msg = homi_speech_interface::msg::SIGCEvent();\n            msg.event = g_json_str;\n            homi_player_publisher_->publish(msg);\n            free(g_json_str);\n            g_json_str = nullptr;\n        }\n        \n    }\n    // else\n    // {\n    //     RCLCPP_INFO(this->get_logger(), \"Timer triggered dn_send_cmd_status == 1.\");\n    // }\n    // 在此处添加定时器触发时的处理逻辑\n}\n\nint AndlinkNode::andlink_main()\n{\n    CFG_NET_MODE_e mode = NETWOKR_MODE_BT;\n\n    this->declare_parameter<std::string>(\"wifi_connect_interface\", \"wlan0\");\n    std::string  wifi_connect_interface = this->get_parameter(\"wifi_connect_interface\").as_string();\n    RCLCPP_INFO(rclcpp::get_logger(\"andlink_node\"), \"wifi_connect_interface: %s\", wifi_connect_interface.c_str());\n\n    this->declare_parameter<std::string>(\"p2p_connect_interface\", \"p2p0\");\n    std::string  p2p_connect_interface = this->get_parameter(\"p2p_connect_interface\").as_string();\n    RCLCPP_INFO(rclcpp::get_logger(\"andlink_node\"), \"p2p_connect_interface: %s\", p2p_connect_interface.c_str());\n\n\n    strcpy(g_ifname,wifi_connect_interface.c_str());\n    strcpy(g_p2p_ifname,p2p_connect_interface.c_str());\n\n\n\n\n    /************ 2.加载Andlink SDK ************/\n    if (0 != demo_startAndlinkSdk(mode, (char *)g_ifname))\n    {\n        DEMO_DBG_PRINT(\"demo_startAndlinkSdk failed\\r\\n\");\n    }\n    return 0;\n}\nint AndlinkNode::demo_startAndlinkSdk(CFG_NET_MODE_e mode, char *ifname)\n{\n    // 1.设置设备andlink基本信息\n    static adl_dev_attr_t devAttr;\n    devAttr.cfgNetMode = NETWOKR_MODE_BT;\n    devAttr.deviceVendor = const_cast<char*>(\"\");\n    devAttr.deviceType = const_cast<char*>(\"\");\n    devAttr.id = const_cast<char*>(\"\");\n    devAttr.andlinkToken = const_cast<char*>(\"\");\n    devAttr.productToken = const_cast<char*>(\"\");\n    devAttr.firmWareVersion = const_cast<char*>(\"\");\n    devAttr.softWareVersion = const_cast<char*>(\"\");\n    devAttr.cfgPath = const_cast<char*>(\"\");\n\n    // 恢复厂商设备配置文件,一旦恢复失败,重新构造\n    if (0 != recoverFacDevinfoCfg(&devAttr))\n    {\n        DEMO_DBG_PRINT(\"recoverFacDevinfoCfg failed\\n\");\n        buildFacDevinfoCfgFile(ifname, &devAttr);\n    }\n    devAttr.version = ADL_EXPORT_API_VERSION;\n    devAttr.cfgNetMode = mode;\n\n    RCLCPP_INFO(this->get_logger(), \"[enter]demo_startAndlinkSdk,cfgNetMode =%d,ifname =%s\\n\", devAttr.cfgNetMode, ifname);\n\n    // 2.设置andlink回调接口\n    static adl_dev_callback_t devCbs;\n    devCbs.version = ADL_EXPORT_API_VERSION;\n    devCbs.scan_wifi_callback = demo_scan_wifi_callback;\n    // devCbs.ctrl_wifi_callback = demo_ctrl_wifi_callback;\n    devCbs.ctrl_wifi_callback = andlink_ctrl_wifi_callback;\n    devCbs.set_andlink_status_callback = demo_set_andlink_status_callback;\n    devCbs.set_extfunc_notify_callback = NULL;\n    devCbs.dn_send_cmd_callback = demo_dn_send_cmd_callback;\n    devCbs.dev_paramsSync_callback = demo_dev_paramsSync_callback;\n    devCbs.download_upgrade_version_callback = NULL;\n    // devCbs.download_upgrade_version_callback = demo_download_upgrade_version_callback;\n    devCbs.upgrade_version_callback = demo_upgrade_version_callback;\n    // devCbs.get_device_ipaddr = demo_get_device_ipaddr_callback;\n    devCbs.get_device_ipaddr = andlink_get_device_ipaddr_callback;\n    devCbs.reset_device_Ipaddr = demo_reset_device_Ipaddr_callback;\n    devCbs.getCfg_callback = NULL;\n    devCbs.setCfg_callback = NULL;\n    devCbs.get_dmInfo_callback = demo_get_dmInfo_callback;\n    devCbs.get_extInfo_callback = demo_get_extInfo_callback;\n    devCbs.set_voice_notify_callback = NULL;\n    devCbs.ble_start_server_callback = demo_ble_start_server_callback;\n    devCbs.ble_stop_server_callback = demo_ble_stop_server_callback;\n    devCbs.ble_send_callback = demo_ble_send_callback;\n    devCbs.ctrl_scanCode_callback = NULL;\n\n    // 3.检查设备是否联网\n    char ipAddr[40] = { 0 };\n    char brdAddr[40] = { 0 };\n    if (0 != check_device_ip_info(ifname, ipAddr, brdAddr))\n    {\n        RCLCPP_INFO(this->get_logger(),\"device has NOT been connected to the network!!!\\n\");\n        char empty_ip[] = \"\";\n        demo_set_device_ipaddr_for_callback(empty_ip, empty_ip);\n    }\n    else\n    {\n        RCLCPP_INFO(this->get_logger(),\"device has been connected to the network!\\n\");\n        demo_set_device_ipaddr_for_callback(ipAddr, brdAddr);\n    }\n\n    RCLCPP_INFO(this->get_logger(),\"andlink_init========start\");\n    RCLCPP_INFO(this->get_logger(),\"##############################################################################\");\n    RCLCPP_INFO(this->get_logger(),\"andlinksdk demo   version:%s\", adlDemoVersionGet());\n    RCLCPP_INFO(this->get_logger(),\"andlinksdk lib    version:%s\", getAndlinkVersion());\n    RCLCPP_INFO(this->get_logger(),\"andlinksdk DM lib version:%s\", getAndlinkVersion());\n    RCLCPP_INFO(this->get_logger(),\"##############################################################################\");\n\n    // 4.设置特殊的启动选项\n    // 4.1设置andlink单个日志文件阈值;若不设置,默认单个日志文件最大512KB,即andlink诊断功能的日志文件占用空间最大为512KB*2;\n    // setAndlinkLogMaxSize(0x80000);\n\n    // 4.2设置andlink 日志文件存储路径;若不设置,默认是存储在/tmp/andlink目录下.\n    // setAndlinkLogFilePath(\"/tmp/andlink1\");\n\n    // 4.4若是扫码绑定的设备,可以关闭APP发现服务;\n    disableAdlFunc(ADL_APP_SEARCH_SERVICE);\n\n    // 4.5若存在离线解绑后,需要立马绑定的场景时,可以禁止自动默认用户注册功能;\n    disableAdlFunc(ADL_OFFLINE_UNBIND_AUTO_REBOOTSTRAP);\n\n    // 4.6若是可视门锁、守护台灯等设备,使用扫码配网,可以调用下列接口禁止闪联配网和手工SoftAP或BLE配网\n    disableAdlFunc(ADL_FLASHLINK_CONFIG_NET);\n    // disableAdlFunc(ADL_MANUAL_CONFIG_NET);\n\n    // 4.7业务连接优先使能,若是泛摄像头品类需要使能此接口\n    // enableAdlFunc(ADL_BUSINESS_CONN_FIRST)\n\n    // 4.8设置特定地域服务器地址,比如设置非大陆Andlink服务地址,测试环境地址等;\n    // 若产品需要销往非大陆地区,需要调用此接口设置非大陆地区的Andlink服务器地址.\n    // setAndlinkServAddress(\"http://************:8085\");\n\n    // 4.9设置OTA 文件下载路径及文件名称;设置OTA下载策略(1分片下载;0整包下载)\n    // setOtaStoragePathAndFilename(\"/tmp\", \"ota.zip\");\n    // setOtaDownloadPolicy(1, 2*1024*1024);\n\n    // 4.10设置诊断服务器地址,SDK使用者一般无需关心\n    // setAndlinkDgsServUrl(\"http://************:8085/dev/anddgs/chkdgs\");\n\n    /*\n    5.启动andlink SDK(立即返回,内部不会阻塞)\n    注意:sdk不会申请内存存储devAttr和devCbs,只会保存其指针;\n    因此入参所需要的空间需要调用者申请且SDK运行期间不能释放这两个指针;假设用户需要调用andlink_destroy使SDK消亡时,需要释放这两个指针;\n    */\n    return andlink_init(&devAttr, &devCbs);\n}\n\n// private:\nvoid AndlinkNode::handle_andlink_cmd_message(const std_msgs::msg::ByteMultiArray::SharedPtr msg)\n{\n    // int ret = 0;\n    // 获取数据指针\n    uint8_t* data_ptr = msg->data.data();\n    size_t data_size = msg->data.size();\n    RCLCPP_INFO(this->get_logger(), \"Received byte stream of size: %zu\", data_size);\n\n    if(data_size >= 4)\n    {\n        if((data_ptr[0] == 0x55) && (data_ptr[1] == 0x55) && (data_ptr[2] == 0x00) )\n        {\n            if(data_ptr[3] == 0x02)\n            {\n                // 使用子线程调用 devReset\n                std::thread([this]() {\n                    devReset();\n                    // RCLCPP_INFO(this->get_logger(), \"devReset : %d\", ret);\n                }).detach();\n            }\n        }\n    }\n\n    // 打印数据\n    for (size_t i = 0; i < data_size; ++i) {\n        printf(\"%02X \", data_ptr[i]);\n    }\n    printf(\"\\n\");\n}\n\nvoid AndlinkNode::handle_ble_byte_stream_message(const std_msgs::msg::ByteMultiArray::SharedPtr msg)\n{\n    // int ret = 0;\n\n    // 获取数据指针\n    uint8_t* data_ptr = msg->data.data();\n    size_t data_size = msg->data.size();\n    RCLCPP_INFO(this->get_logger(), \"Received byte stream of size: %zu\", data_size);\n\n    std::ostringstream oss;\n    for (size_t i = 0; i < data_size; ++i) {\n        oss << \"0x\" << std::hex << std::uppercase << static_cast<int>(data_ptr[i]) << \" \";\n    }\n    RCLCPP_INFO(this->get_logger(), \"Byte stream content: %s\", oss.str().c_str());\n\n    adlBleRecvHandler(data_ptr, data_size);\n}\n\nvoid AndlinkNode::log_timer_callback()\n{\n    RCLCPP_INFO(this->get_logger(), \"Log timer triggered every 2 seconds.\");\n}\n\n// void AndlinkNode::publish_byte_stream_message()\n// {\n//     auto message = std_msgs::msg::ByteMultiArray();\n//     // 填充字节流数据\n//     message.data = {0x01, 0x02, 0x03, 0x04, 0x05};\n//     RCLCPP_INFO(this->get_logger(), \"Publishing byte stream of size: %zu\", message.data.size());\n    \n//     // 打印字节流内容\n//     std::string byte_stream;\n//     for (const auto &byte : message.data) {\n//         byte_stream += \"0x\" + to_hex(byte) + \" \";\n//     }\n//     RCLCPP_INFO(this->get_logger(), \"Byte stream content: %s\", byte_stream.c_str());\n\n//     byte_stream_publisher_->publish(message);\n// }\n\n\nvoid AndlinkNode::publish_ble_cmd_broadcast_message()\n{\n    auto message = std_msgs::msg::ByteMultiArray();\n    // 填充字节流数据\n    message.data = {0x55, 0x55, 0x00, 0x04};\n    RCLCPP_INFO(this->get_logger(), \"publish_ble_cmd_broadcast_message of size: %zu\", message.data.size());\n    \n    // 打印字节流内容\n    std::string byte_stream;\n    for (const auto &byte : message.data) {\n        byte_stream += \"0x\" + to_hex(byte) + \" \";\n    }\n    RCLCPP_INFO(this->get_logger(), \"Byte stream content: %s\", byte_stream.c_str());\n\n    ble_cmd_publisher_->publish(message);\n}\n\nvoid AndlinkNode::publish_ble_cmd_unbroadcast_message()\n{\n    auto message = std_msgs::msg::ByteMultiArray();\n    // 填充字节流数据\n    message.data = {0x55, 0x55, 0x00, 0x05};\n    RCLCPP_INFO(this->get_logger(), \"publish_ble_cmd_unbroadcast_message of size: %zu\", message.data.size());\n    \n    // 打印字节流内容\n    std::string byte_stream;\n    for (const auto &byte : message.data) {\n        byte_stream += \"0x\" + to_hex(byte) + \" \";\n    }\n    RCLCPP_INFO(this->get_logger(), \"Byte stream content: %s\", byte_stream.c_str());\n\n    ble_cmd_publisher_->publish(message);\n}\n\nvoid AndlinkNode::publish_ble_cmd_unbind_message()\n{\n    auto message = std_msgs::msg::ByteMultiArray();\n    // 填充字节流数据\n    message.data = {0x55, 0x55, 0x00, 0x03};\n    RCLCPP_INFO(this->get_logger(), \"publish_ble_cmd_unbind_message of size: %zu\", message.data.size());\n    \n    // 打印字节流内容\n    std::string byte_stream;\n    for (const auto &byte : message.data) {\n        byte_stream += \"0x\" + to_hex(byte) + \" \";\n    }\n    RCLCPP_INFO(this->get_logger(), \"Byte stream content: %s\", byte_stream.c_str());\n\n    ble_cmd_publisher_->publish(message);\n}\n\nvoid AndlinkNode::publish_network_connecting_start_message()\n{\n    auto message = std_msgs::msg::String();\n    message.data = \"notify_userbind_start\";\n    andlink_network_publisher_->publish(message);\n    RCLCPP_INFO(this->get_logger(), \"发送给网络服务消息 notification: '%s'\", message.data.c_str());\n}\n\nvoid AndlinkNode::publish_network_connecting_end_message()\n{\n    auto message = std_msgs::msg::String();\n    message.data = \"notify_userbind_end\";\n    andlink_network_publisher_->publish(message);\n    RCLCPP_INFO(this->get_logger(), \"发送给网络服务消息 notification: '%s'\", message.data.c_str());\n}\n\nstd::string AndlinkNode::to_hex(uint8_t byte)\n{\n    std::ostringstream oss;\n    oss << std::hex << std::uppercase << static_cast<int>(byte);\n    return oss.str();\n}\n\n// 新增方法：发布 JSON 格式字符串\nvoid AndlinkNode::publish_json_message()\n{\n    auto message = std_msgs::msg::String();\n    message.data = R\"({\"key1\": \"value1\", \"key2\": 123, \"key3\": true})\"; // 示例 JSON 数据\n    RCLCPP_INFO(this->get_logger(), \"Publishing JSON: '%s'\", message.data.c_str());\n    json_publisher_->publish(message);\n}\n\n"}]}