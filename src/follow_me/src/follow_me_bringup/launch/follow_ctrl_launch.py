from launch import LaunchDescription
from launch_ros.actions import Node
from ament_index_python.packages import get_package_share_directory

def generate_launch_description():

    rcs_pkg_share_dir = get_package_share_directory('follow_rcs')

    follow_rcs = Node(
        package="follow_rcs",
        executable="follow_rcs",
        parameters=[rcs_pkg_share_dir + '/configs/config.yaml'],
    )
    
    control_catch_node = Node(
        package="follow_strategy",
        executable="control_catch_turtle_v1",
        name="control_catch_turtle_v1"
    )

    target_twist_estimate_node = Node(
        package="follow_strategy",
        executable="target_twist_estimate",
        name="target_twist_estimate"
    )

    pixel2pose_node = Node(
        package="pixel2world_pose",
        executable="pixel",
        name="pixel"
    )
    return LaunchDescription([
        follow_rcs,
        control_catch_node,
        target_twist_estimate_node,
        pixel2pose_node
    ])
