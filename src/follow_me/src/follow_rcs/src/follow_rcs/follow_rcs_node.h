//
/*****websocket通讯服务端节点******/
/**********默认端口为19002********/
//

#include <rclcpp/rclcpp.hpp>
#include <rclcpp_action/rclcpp_action.hpp>
#include <std_msgs/msg/string.hpp>
#include <geometry_msgs/msg/pose_stamped.hpp>
#include <geometry_msgs/msg/twist_stamped.hpp>
#include <geometry_msgs/msg/transform_stamped.hpp>
#include "tf2_ros/transform_broadcaster.h"
#include <nav_msgs/msg/odometry.hpp>
#include <nav_msgs/msg/path.hpp>
#include <sensor_msgs/msg/image.hpp>
#include <sensor_msgs/msg/camera_info.hpp>
#include <std_msgs/msg/bool.hpp>

#include <tf2_ros/transform_broadcaster.h>
#include <tf2/LinearMath/Quaternion.h>
#include <tf2/LinearMath/Matrix3x3.h>        
#include <Eigen/Dense>

#include <chrono>
#include <string>
#include <map>
#include <iostream>
#include <queue>
#include <list>
#include <mutex>

#include <cv_bridge/cv_bridge.h>
#include <jsoncpp/json/json.h>
#include <curl/curl.h>
#include <opencv2/core/core.hpp>
#include <opencv2/highgui/highgui.hpp>
#include <openssl/bio.h>
#include <openssl/evp.h>
#include <openssl/sha.h>
#include <openssl/pem.h>
#include <openssl/bio.h>
#include <openssl/evp.h>

#include <stdlib.h>  
#include "follow_msgs/msg/trakerimageinfo.hpp"
#include "follow_msgs/msg/follow_image_info.hpp"
#include "follow_msgs/action/followcfg.hpp"
#include <mutex>
#define CAM_INFO_WIDTH   1280
#define CAM_INFO_HEIGHT  720 

#define CAM_INFO_FX      866.67
#define CAM_INFO_FY      866.67 
#define CAM_INFO_CX      640
#define CAM_INFO_CY      360

using namespace std;
//#define CLIENT_LAUNCHER  "launcher"
//#define CLIENT_TEMI_CTRL  "ctrl"

#define FOLLOW_PATH_MAX_CNT          100000
#define CATCH_POSE_MAX_CNT           1000
#define TRACKER_SEARCH_TIMEOUT_MS    10000
#define KEEP_WAKEUP_TIMEOUT_MS       60000

/**************************************************************************************************************/

/***************************************************************************************************************
 * 跟随节点类实现
/// @brief 
 * ************************************************************************************************************/
#define FIRSTBASE 10000
enum class FollowStatusCode : int {
    FollowNodeStarted = 4000,
    FollowNodeStopped = 4001,
    FollowFunctionInitialized = 4002,
    FollowFunctionPaused = 4003,
    TargetDetected = 4004,
    InvalidMessageFormat = 4100,
    NoUWBData = 4101,
    NOUWBDataFISRT =FIRSTBASE+4101,
    NoCameraData = 4102,
    NoCameraDataFIRST = FIRSTBASE+4102,
    TargetLostDuringTracking = 4103,
    TargetLostDuringObstacleAvoidance = 4104,
    NoFollowTargetDetectedFirstTime = 4105,
    NoFollowTargetDetected = 4106,
    TargetLostDuringObstacleAvoidanceFirst = 4107,
    FollowNodeFailedToStart = 4200,
    FollowNodeCrashed = 4201,
    ConflictingInstructionInFollowState = 4202,
    FollowStartInOtherState = 4203,
    InvalidFollowCommandInNonFollowState = 4204
};
class FollowRcsNode : public rclcpp::Node {

  using Followcfg = follow_msgs::action::Followcfg;
  using GoalHandleFollowcfg = rclcpp_action::ServerGoalHandle<Followcfg>;

public:
    explicit FollowRcsNode(const rclcpp::NodeOptions & options = rclcpp::NodeOptions());
    ~FollowRcsNode();
    FollowRcsNode* getNode();
    void init();
    void setCameraInfo();
    void stopFollow();
    void ctrlTiltBy(int degrees);
    void parseRecActionMsgs(const string& msg, string& feedBackMsg);

    rclcpp_action::GoalResponse handle_goal(const rclcpp_action::GoalUUID & uuid,
    std::shared_ptr<const Followcfg::Goal> goal);
    rclcpp_action::CancelResponse handle_cancel(const std::shared_ptr<GoalHandleFollowcfg> goal_handle);
    void handle_accepted(const std::shared_ptr<GoalHandleFollowcfg> goal_handle);
    void execute(const std::shared_ptr<GoalHandleFollowcfg> goal_handle);

    void pubServerMsg(const std::string& strMsg);
    void pubCatchPos(geometry_msgs::msg::PoseStamped pos);
    void pubTargettPostoCalibration(geometry_msgs::msg::PoseStamped pos); 
    void pubTargetPixel(geometry_msgs::msg::PoseStamped pos);
    void pubCatchTwist(geometry_msgs::msg::TwistStamped twist);
    void pubTargetTwist(geometry_msgs::msg::TwistStamped twist);
    void followMeWarning(const std_msgs::msg::String::SharedPtr msg);
private:
    void subTrakerimageinfo(const follow_msgs::msg::Trakerimageinfo::SharedPtr msg);
    void subUwbDataInfo(const geometry_msgs::msg::Twist::SharedPtr msg);
    void subUwbTrack(const geometry_msgs::msg::Twist::SharedPtr msg);

    void subCatchPosCallback(const geometry_msgs::msg::Twist::SharedPtr msg); 
    void subInitStatus(const std_msgs::msg::String::SharedPtr msg);
    void subRbtServerInfo(const std_msgs::msg::String::SharedPtr msg);
    void tripsigHandle(const std_msgs::msg::String::SharedPtr msg);
    void subTargetCalibrationReq(const geometry_msgs::msg::PoseStamped::SharedPtr msg);
    void velCmdCallback(const geometry_msgs::msg::Twist::SharedPtr msg); 
    void robotPositionTimerCallback();
    void watchDogforsubTargetCalibrationReq();
    void sendplayTtsToBrocast(const std::string &message);
    void pull_up_down_processs(int switchKey);
    std::string asembleWarningBody(int code,const std::string &alarmName,std::string &alarmDesc);
    void navCallback(const std_msgs::msg::String::SharedPtr msg);
private:
    rclcpp_action::Server<Followcfg>::SharedPtr action_server_;
    //flag: start server, defalut is false
    bool bStartServer_ = false;
    bool bContinue_ = false;
    uint8_t nSearchErrorCount = 0;
    uint8_t nTrackerCount = 0;
    bool bOnlyUwbMode_ = false;
    FollowStatusCode status=FollowStatusCode::FollowNodeStopped;
    uint64_t searchRobotTimeStamp_ = 0;
    sensor_msgs::msg::Image rgbImg_;
    sensor_msgs::msg::Image depthImg_;
    std::shared_ptr<tf2_ros::TransformBroadcaster> tf_broadcaster_ = std::make_shared<tf2_ros::TransformBroadcaster>(this);
    sensor_msgs::msg::CameraInfo::SharedPtr cam_info_ = std::make_shared<sensor_msgs::msg::CameraInfo>();
    //current robot pos
    std::shared_ptr<geometry_msgs::msg::PoseStamped> robot_pos_ = nullptr;
    //current line speed m/s
    float robot_line_speed_ = 0.0;
    //current head angle  rad/s
    float robot_head_angle_ = 0.0;
    //头部当前角度
    int robot_cur_head_angle_ = 0;
    std_msgs::msg::String ctrlMsgs_;
    rclcpp::Publisher<std_msgs::msg::String>::SharedPtr pubServer_ = nullptr;
    //和跟随算法的节点交互
    rclcpp::Publisher<geometry_msgs::msg::PoseStamped>::SharedPtr pubCatchPos_ = nullptr;
    rclcpp::Publisher<geometry_msgs::msg::PoseStamped>::SharedPtr pubTargetPos_ = nullptr;
    rclcpp::Publisher<geometry_msgs::msg::TwistStamped>::SharedPtr pubCatchTwist_ = nullptr;
    rclcpp::Publisher<geometry_msgs::msg::TwistStamped>::SharedPtr pubTargetTwist_ = nullptr;
    //Calibration node 
    rclcpp::Publisher<geometry_msgs::msg::PoseStamped>::SharedPtr pubCatchPostoCalibration_ = nullptr;
    rclcpp::Publisher<geometry_msgs::msg::PoseStamped>::SharedPtr pubTargettPostoCalibration_ = nullptr;
    rclcpp::Publisher<geometry_msgs::msg::PoseStamped>::SharedPtr pubTargetPixel_ = nullptr;
    /*traker type */
    rclcpp::Publisher<std_msgs::msg::String>::SharedPtr pubTrackerStart_ = nullptr; 
    rclcpp::Publisher<std_msgs::msg::String>::SharedPtr pubTrackerEnd_ = nullptr;
    rclcpp::Publisher<sensor_msgs::msg::Image>::SharedPtr pubTrackerImageResult_ = nullptr;
    rclcpp::Publisher<follow_msgs::msg::FollowImageInfo>::SharedPtr pubFollowImgInfo_ = nullptr;
    rclcpp::Publisher<sensor_msgs::msg::CameraInfo>::SharedPtr pubCameraInfo_ = nullptr;
    rclcpp::Publisher<std_msgs::msg::String>::SharedPtr pullUpProcessPub_ = nullptr; 
    rclcpp::Publisher<std_msgs::msg::String>::SharedPtr followWarnPub_ = nullptr; 
    rclcpp::Subscription<std_msgs::msg::String>::SharedPtr navPositionSub_ = nullptr; 

    //订阅节点
    rclcpp::Subscription<sensor_msgs::msg::Image>::SharedPtr subdepthRawImage_ = nullptr;
    rclcpp::Subscription<sensor_msgs::msg::Image>::SharedPtr subRgbRawImage_ = nullptr;
    rclcpp::Subscription<sensor_msgs::msg::CameraInfo>::SharedPtr subCameraInfo_ = nullptr;
    rclcpp::Subscription<follow_msgs::msg::Trakerimageinfo>::SharedPtr subTrackingResult_ = nullptr;
    rclcpp::Subscription<std_msgs::msg::String>::SharedPtr subInitStatus_  = nullptr;
    rclcpp::Subscription<std_msgs::msg::String>::SharedPtr subPubRbtSever_  = nullptr;
    rclcpp::Subscription<std_msgs::msg::String>::SharedPtr tripsigSub_  = nullptr;
    rclcpp::Subscription<geometry_msgs::msg::Twist>::SharedPtr subCtrlInstruct_  = nullptr;
    rclcpp::Subscription<geometry_msgs::msg::PoseStamped>::SharedPtr subTargetCalibrationReq_  = nullptr;
    rclcpp::Subscription<geometry_msgs::msg::Twist>::SharedPtr subUwbData_ = nullptr;
    rclcpp::Subscription<geometry_msgs::msg::Twist>::SharedPtr subUwbTrack_ = nullptr;
    rclcpp::Subscription<geometry_msgs::msg::Twist>::SharedPtr velCmd_  = nullptr;
    rclcpp::Subscription<std_msgs::msg::String>::SharedPtr abnormal_Collect_  = nullptr;
    geometry_msgs::msg::PoseStamped lastPose;
    //设备状态信息发布
    rclcpp::TimerBase::SharedPtr robotPositionTimer_ = nullptr;
    rclcpp::TimerBase::SharedPtr watchdogTimer_ = nullptr;
    rclcpp::Time lastTargetCaliReqTime;

private:
    float f_max_tracking_distanse = 5.0;
    float f_min_tracking_distanse = 0.01;
    std::mutex mutex_;
    size_t message_count_;
    int alreadyStarted_=0;
};



