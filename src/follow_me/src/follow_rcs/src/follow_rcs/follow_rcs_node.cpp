#include "follow_rcs_node.h"
#include "xiaoli_com/xiaoli_pub_def.h"
#include <homi_com/homi_utils.hpp>

FollowRcsNode::FollowRcsNode(const rclcpp::NodeOptions & options) 
: Node("follow_rcs", options) {
    using namespace std::placeholders;

    this->action_server_ = rclcpp_action::create_server<Followcfg>(
      this, "follow_me/cfgAction",
      std::bind(&FollowRcsNode::handle_goal, this, _1, _2),
      std::bind(&FollowRcsNode::handle_cancel, this, _1),
      std::bind(&FollowRcsNode::handle_accepted, this, _1));

    searchRobotTimeStamp_ = base::homiUtils::getCurrentTimeStamp();
}

FollowRcsNode::~FollowRcsNode(){
    RCLCPP_INFO(rclcpp::get_logger("follow_rcs"), "destroy");
}

FollowRcsNode* FollowRcsNode::getNode(){
    return this;
}

void FollowRcsNode::init(){  

    RCLCPP_INFO(get_logger(), "init");    
    robot_pos_ = std::make_shared<geometry_msgs::msg::PoseStamped>();
    this->declare_parameter<double>("max_tracking_distanse", 5.0); 
    this->declare_parameter<double>("min_tracking_distanse", 0.01); 

    f_max_tracking_distanse = this->get_parameter("max_tracking_distanse").as_double();
    f_min_tracking_distanse = this->get_parameter("min_tracking_distanse").as_double();
    pubCameraInfo_ = this->create_publisher<sensor_msgs::msg::CameraInfo>("/follow_me/camera_info", 10);
    pubServer_ = this->create_publisher<std_msgs::msg::String>("follow_me/from_rcs", 10); 
    pubCatchPos_ = this->create_publisher<geometry_msgs::msg::PoseStamped>("catch_turtle/posestamped", 10);
    pubTargetPos_ = this->create_publisher<geometry_msgs::msg::PoseStamped>("/target_turtle/posestamped", 10);
    pubCatchTwist_ = this->create_publisher<geometry_msgs::msg::TwistStamped>("catch_turtle/twiststamped", 10);
    pubTargetPixel_ = this->create_publisher<geometry_msgs::msg::PoseStamped>("/target_image/posestamped", 10);
    pubTargettPostoCalibration_ = this->create_publisher<geometry_msgs::msg::PoseStamped>("target_calibration/pose", 10);
    pubCatchPostoCalibration_ = this->create_publisher<geometry_msgs::msg::PoseStamped>("catch_calibration/posestamped", 10);
    pullUpProcessPub_=this->create_publisher<std_msgs::msg::String>("/follow_me_control", 10);
    followWarnPub_=this->create_publisher<std_msgs::msg::String>("/device_alarm_report", 10);
    
    velCmd_ = this->create_subscription<geometry_msgs::msg::Twist>("/catch_turtle/ctrl_instruct_modify", 100, 
        std::bind(&FollowRcsNode::velCmdCallback, this, std::placeholders::_1));
    
    subPubRbtSever_ = this->create_subscription<std_msgs::msg::String>("/follow_me/from_control", 10, 
        std::bind(&FollowRcsNode::subRbtServerInfo, this, std::placeholders::_1));

    tripsigSub_ = this->create_subscription<std_msgs::msg::String>("/navigation_control", 10, 
        std::bind(&FollowRcsNode::tripsigHandle, this, std::placeholders::_1));
        
    navPositionSub_ = this->create_subscription<std_msgs::msg::String>("/navigation_position", 
        10, std::bind(&FollowRcsNode::navCallback, this, std::placeholders::_1));

    subCtrlInstruct_ = this->create_subscription<geometry_msgs::msg::Twist>("/catch_turtle/current_twist", 10, 
       std::bind(&FollowRcsNode::subCatchPosCallback, this, std::placeholders::_1));

    subTargetCalibrationReq_ = this->create_subscription<geometry_msgs::msg::PoseStamped>("/target_calibration_req/pose", 100,
        std::bind(&FollowRcsNode::subTargetCalibrationReq, this, std::placeholders::_1));

    abnormal_Collect_ = this->create_subscription<std_msgs::msg::String>("/task_status", 10,
        std::bind(&FollowRcsNode::followMeWarning, this, std::placeholders::_1));

    // 创建一个33ms定时器发布机器人
    robotPositionTimer_ = this->create_wall_timer(std::chrono::milliseconds(33), 
        std::bind(&FollowRcsNode::robotPositionTimerCallback, this));
    //接收指令后的看门狗，防止消息持续发送
    watchdogTimer_ = this->create_wall_timer(std::chrono::milliseconds(20), 
        std::bind(&FollowRcsNode::watchDogforsubTargetCalibrationReq, this));

}

rclcpp_action::GoalResponse FollowRcsNode::handle_goal( const rclcpp_action::GoalUUID & uuid,
    std::shared_ptr<const Followcfg::Goal> goal) {

    RCLCPP_INFO(this->get_logger(), "Received goal request with order %s", goal->request);
    (void)uuid;
    return rclcpp_action::GoalResponse::ACCEPT_AND_EXECUTE;
}

rclcpp_action::CancelResponse FollowRcsNode::handle_cancel(const std::shared_ptr<GoalHandleFollowcfg> goal_handle) {

    RCLCPP_INFO(this->get_logger(), "Received request to cancel goal");
    (void)goal_handle;
    return rclcpp_action::CancelResponse::ACCEPT;
}

void FollowRcsNode::handle_accepted(const std::shared_ptr<GoalHandleFollowcfg> goal_handle) {
    using namespace std::placeholders;
    std::thread{ std::bind(&FollowRcsNode::execute, this, _1), goal_handle }.detach();
}

void FollowRcsNode::execute(const std::shared_ptr<GoalHandleFollowcfg> goal_handle) {
    
    RCLCPP_INFO(this->get_logger(), "Executing goal");
    //rclcpp::Rate loop_rate(1);
    const auto goal = goal_handle->get_goal();
    auto feedback = std::make_shared<Followcfg::Feedback>();
    auto &sequence = feedback->feedback;
    auto result = std::make_shared<Followcfg::Result>();
    //获取到的请求信息
    auto request = goal->request;
    RCLCPP_INFO(this->get_logger(), "get request: %s", request.c_str());

    if(!rclcpp::ok()){
        return;
    }
    // Check if there is a cancel request
    if (goal_handle->is_canceling()) {
        result->result = sequence;
        goal_handle->canceled(result);
        RCLCPP_INFO(this->get_logger(), "Goal canceled");
        return;
    }
    parseRecActionMsgs(request, sequence);
    //sequence 为客户端需要获取到的数据
    // Update sequence
    // Publish feedback
    if(!sequence.empty()) {
        goal_handle->publish_feedback(feedback);
        RCLCPP_INFO(this->get_logger(), "Publish feedback");
    }
    // Check if goal is done
    if (rclcpp::ok()) {
        //result->result = "goal succeeded";
        //goal_handle->succeed(result);
        RCLCPP_INFO(this->get_logger(), "Goal succeeded");
    }
}
void FollowRcsNode::sendplayTtsToBrocast(const std::string &message) {
        RCLCPP_INFO(get_logger(), "sendplayTtsToBrocast msg is %s",message.c_str());
        nTrackerCount = 0;
        searchRobotTimeStamp_ = base::homiUtils::getCurrentTimeStamp();
        Json::Value value;
        Json::Value params;
        value["action"] = "playTts";
        params["text"] = message;
        value["params"] = params; 
        pubServerMsg(value.toStyledString().c_str());
}
std::string FollowRcsNode::asembleWarningBody(int code,const std::string &alarmName,std::string &alarmDesc)
{
    Json::Value body;
    body["alarmCode"] = code;
    body["alarmName"] = alarmName;
    body["alarmLevel"] = 1;
    body["alarmType"] = "主动跟随告警";
    Json::Value notifyWay1(Json::arrayValue);
    notifyWay1.append(1);
    notifyWay1.append(3);
    Json::Value notifyWay2(Json::arrayValue);
    notifyWay2.append(1);
    notifyWay2.append(2);
    body["notifystrategy"] = Json::Value(Json::arrayValue);
    body["alarmDesc"] = alarmDesc;
    body["launcherModel"] = "FollowMe";
    Json::StreamWriterBuilder writer;
    return Json::writeString(writer, body);
}

void FollowRcsNode::followMeWarning(const std_msgs::msg::String::SharedPtr msg)
{
    Json::Reader reader;
    Json::Value json_msg;
    if (!reader.parse(msg->data, json_msg))
    {
        RCLCPP_ERROR(this->get_logger(), "Failed to parse JSON message");
        return;
    }
    if (!bStartServer_)
        return;    
    int code = json_msg["code"].asInt();
    std::string message = json_msg["msg"]["msg"].asString();
    switch (code)
    {
        case static_cast<int>(FollowStatusCode::FollowNodeStarted):
        {
            bOnlyUwbMode_ = false;
            int need_rotate_angle = 10 - robot_cur_head_angle_ ;
            ctrlTiltBy(need_rotate_angle);
            pubCameraInfo_->publish(*cam_info_.get());
            RCLCPP_INFO(rclcpp::get_logger("follow_rcs"), "Ready to start follow ,status=%d", code);
            nTrackerCount = 0;
            searchRobotTimeStamp_ = base::homiUtils::getCurrentTimeStamp();
            sendplayTtsToBrocast("算法加载成功，正在检测跟随目标");
            alreadyStarted_=1;
            RCLCPP_INFO(rclcpp::get_logger("follow_rcs"), "alreadyStarted_=%d", alreadyStarted_);
            break;
        }
        case static_cast<int>(FollowStatusCode::FollowNodeStopped):
            break;
        case static_cast<int>(FollowStatusCode::FollowFunctionInitialized):
            break;
        case static_cast<int>(FollowStatusCode::FollowFunctionPaused):
            break;
        case static_cast<int>(FollowStatusCode::TargetDetected):
        {
            RCLCPP_INFO(rclcpp::get_logger("follow_rcs"), "Detect the target.code=%d", code);
            sendplayTtsToBrocast("哈哈，我已经锁定你啦，我们出发吧");
            break;
        }
        case static_cast<int>(FollowStatusCode::InvalidMessageFormat):
            break;
        case static_cast<int>(FollowStatusCode::NoUWBData):
        {
            std::string errorMsg = json_msg["msg"]["error"].asString();
            RCLCPP_INFO(rclcpp::get_logger("follow_rcs"), "No UWB Data.code=%d", code);
            sendplayTtsToBrocast("哎呀，没有检测到U W B信号，请你站到我的正向前一米处，我将在你的身后跟着你哦");
            // std::string retStr;
            // if (alreadyStarted_==0)
            //     retStr=asembleWarningBody(static_cast<int>(FollowStatusCode::NOUWBDataFISRT),"NoUWBData",errorMsg);
            // else
            //     retStr=asembleWarningBody(static_cast<int>(FollowStatusCode::NoUWBData),"NoUWBData",errorMsg);
            // bContinue_=false;
            // stopFollow();
            // std_msgs::msg::String bodyStr;
            // bodyStr.data=retStr;
            // followWarnPub_->publish(bodyStr);
            RCLCPP_INFO(rclcpp::get_logger("follow_rcs"), "NoUWBData,change follow ");
            break;
        }
        case static_cast<int>(FollowStatusCode::NoCameraData):
        {
            RCLCPP_INFO(rclcpp::get_logger("follow_rcs"), "No Camera Data.code=%d", code);
            std::string errorMsg = json_msg["msg"]["error"].asString();
            sendplayTtsToBrocast("相机出问题了，我找不到您，请稍后重试");
            std::string retStr;
            if (alreadyStarted_==0)
                retStr=asembleWarningBody(static_cast<int>(FollowStatusCode::NoCameraDataFIRST),"NoCameraData",errorMsg);
            else
                retStr=asembleWarningBody(static_cast<int>(FollowStatusCode::NoCameraData),"NoCameraData",errorMsg);
            bContinue_=false;
            stopFollow();
            std_msgs::msg::String bodyStr;
            bodyStr.data=retStr;
            followWarnPub_->publish(bodyStr);
            RCLCPP_INFO(rclcpp::get_logger("follow_rcs"), "Send NoCameraData warning msg %s", retStr.c_str());
            break;
        }
        case static_cast<int>(FollowStatusCode::TargetLostDuringTracking):
        {
            {
                std::lock_guard<std::mutex> lock(mutex_);
                message_count_++;
            }
            if (message_count_ % 90 == 0) {
                sendplayTtsToBrocast("哎呀我看不到你啦，请来我的正前方好吗”");
            }
            bContinue_=false;
            Json::Value valuemsg;
            Json::Value params;
            valuemsg["action"] = "motionArcWithObstacles";
            params["lineSpeed"] = 0.0;
            RCLCPP_INFO(rclcpp::get_logger("follow_rcs"), "last Target pose.y is %f", lastPose.pose.position.y);
            params["angularSpeed"] = (lastPose.pose.position.y>0)?-0.5:0.5;
            valuemsg["params"] = params; 
            RCLCPP_INFO(rclcpp::get_logger("follow_rcs"), "Target lost,rotate with speed %s", valuemsg.toStyledString().c_str());
            std::string errorMsg = json_msg["msg"]["error"].asString();
            pubServerMsg(valuemsg.toStyledString().c_str());
            auto elapsed_time = this->now() - lastTargetCaliReqTime;
            RCLCPP_INFO(rclcpp::get_logger("follow_rcs"), "this->now()=%lld, lastTargetCaliReqTime=%lld,elapsed_time=%lld",this->now(),lastTargetCaliReqTime, elapsed_time);
            if (elapsed_time > std::chrono::seconds(15)) {
                RCLCPP_INFO(rclcpp::get_logger("follow_rcs"),"Lost Target  for more than 30 seconds. Stop follow.");
                bContinue_ = false; 
                sendplayTtsToBrocast("我还是找不到你，现在进入安全状态，退出跟随");
                stopFollow();
                auto retStr=asembleWarningBody(static_cast<int>(FollowStatusCode::TargetLostDuringTracking),"TargetLostDuringTracking",errorMsg);
                std_msgs::msg::String bodyStr;
                bodyStr.data=retStr;
                followWarnPub_->publish(bodyStr);
                RCLCPP_INFO(rclcpp::get_logger("follow_rcs"), "Send warning msg %s", retStr.c_str());
            }
            break;
        }
        case static_cast<int>(FollowStatusCode::TargetLostDuringObstacleAvoidanceFirst):
        {
            RCLCPP_INFO(rclcpp::get_logger("follow_rcs"), "Target Lost During Obstacle Avoidance.code=%d", code);
            sendplayTtsToBrocast("哎呀，前面的障碍物挡住我啦，请使用爱家APP远程遥控我走过去吧");
            break;
        }
        case static_cast<int>(FollowStatusCode::TargetLostDuringObstacleAvoidance):
        {
            RCLCPP_INFO(rclcpp::get_logger("follow_rcs"), "Target Lost During Obstacle Avoidance END.code=%d", code);
            std::string errorMsg = json_msg["msg"]["error"].asString();
            sendplayTtsToBrocast("我还是过不去呢，为了安全现在我要退出跟随模式");
            bContinue_=false;
            stopFollow();
            auto retStr=asembleWarningBody(static_cast<int>(FollowStatusCode::TargetLostDuringObstacleAvoidance),"TargetLostDuringObstacleAvoidance",errorMsg);
            std_msgs::msg::String bodyStr;
            bodyStr.data=retStr;
            followWarnPub_->publish(bodyStr);
            RCLCPP_INFO(rclcpp::get_logger("follow_rcs"), "Send TargetLostDuringObstacleAvoidance warning msg %s", retStr.c_str());
            break;
        }
        case static_cast<int>(FollowStatusCode::NoFollowTargetDetectedFirstTime):
        {
            RCLCPP_INFO(rclcpp::get_logger("follow_rcs"), "No Follow Target Detected first time.code=%d", code);
            sendplayTtsToBrocast("哎呀，我怎么找不到你啊，请到我的正前方来");
            break;
        }
        case static_cast<int>(FollowStatusCode::NoFollowTargetDetected):
        {
            RCLCPP_INFO(rclcpp::get_logger("follow_rcs"), "No Follow Target Detected=%d", code);
            std::string errorMsg = json_msg["msg"]["error"].asString();
            sendplayTtsToBrocast("主人，我的正前方还是找不到您，我先退出跟随了");
            bContinue_=false;
            stopFollow();
            auto retStr=asembleWarningBody(static_cast<int>(FollowStatusCode::NoFollowTargetDetected),"NoFollowTargetDetected",errorMsg);
            std_msgs::msg::String bodyStr;
            bodyStr.data=retStr;
            followWarnPub_->publish(bodyStr);
            RCLCPP_INFO(rclcpp::get_logger("follow_rcs"), "Send NoCameraData warning msg %s", retStr.c_str());
            break;
        }
        case static_cast<int>(FollowStatusCode::FollowNodeFailedToStart):
            break;
        case static_cast<int>(FollowStatusCode::FollowNodeCrashed):
            break;
        case static_cast<int>(FollowStatusCode::ConflictingInstructionInFollowState):
            break;
        case static_cast<int>(FollowStatusCode::FollowStartInOtherState):
            break;
        case static_cast<int>(FollowStatusCode::InvalidFollowCommandInNonFollowState):
            break;
        default:
            break;

    }
}

void FollowRcsNode::stopFollow() {
    RCLCPP_INFO(rclcpp::get_logger("follow_rcs"), "stop follow");
    {
        Json::Value value;
        Json::Value params;
        value["action"] = "motionArcWithObstacles";
        params["lineSpeed"] = 0.0;
        params["angularSpeed"] = 0.0;
        value["params"] = params; 
        RCLCPP_INFO(rclcpp::get_logger("follow_rcs"), "Stop follow with speed%s", value.toStyledString().c_str());
        pubServerMsg(value.toStyledString().c_str());
    }
    //rclcpp::sleep_for(std::chrono::milliseconds(50));
    {
        Json::Value value;
        value["action"] = "endFollow";
        RCLCPP_INFO(rclcpp::get_logger("follow_rcs"), "%s", value.toStyledString().c_str());
        pubServerMsg(value.toStyledString().c_str());
    }
    pull_up_down_processs(11);
    bStartServer_ = false;
    bContinue_=false;
    nSearchErrorCount = 0;
    alreadyStarted_=0;
}

void FollowRcsNode::ctrlTiltBy(int degrees) {    
    Json::Value value;
    value["action"] = "tiltBy";
    Json::Value params;
    params["degrees"] = degrees;
    value["params"] = params; 
    pubServerMsg(value.toStyledString().c_str());
    RCLCPP_INFO(rclcpp::get_logger("follow_rcs"), "%s", value.toStyledString().c_str());
}

void FollowRcsNode::parseRecActionMsgs(const string& msg, string& feedBackMsg) {
    Json::Reader reader;
    Json::Value value;
    if(!reader.parse(msg.c_str(), value)){
        cout << "error" << endl;
        return; 
    }
    string strActionType = value["actionType"].asString();
    if(strActionType == "rcs_param_req") {
        Json::Value jsonReq;
        jsonReq["actionType"] = "rcs_param_resp";
        Json::Value jsonReqParams;
        jsonReqParams["max_tracking_distanse"] = f_max_tracking_distanse;
        jsonReqParams["min_tracking_distanse"] = f_min_tracking_distanse;
        jsonReq["params"] = jsonReqParams;
        feedBackMsg = jsonReq.toStyledString();
    } 
    else if (strActionType == "strategy_param_req") {
        Json::Value jsonReq;
        jsonReq["actionType"] = "strategy_param_resp";
        Json::Value jsonReqParams;
        jsonReq["params"] = jsonReqParams;
        feedBackMsg = jsonReq.toStyledString();
    }
    else if(strActionType == "update_rcs_param") {
        Json::Value params = value["params"];
        f_max_tracking_distanse = params["max_tracking_distanse"].asFloat();
        f_min_tracking_distanse = params["min_tracking_distanse"].asFloat();
    } else if (strActionType == "update_strategy_param") {
    }
}



void FollowRcsNode::pubServerMsg(const std::string& strMsg) {
    if (pubServer_  && bStartServer_){
        ctrlMsgs_.data = strMsg;
        pubServer_->publish(ctrlMsgs_);
    }
}

void FollowRcsNode::pubCatchPos(geometry_msgs::msg::PoseStamped pos) {
    if (pubCatchPos_  && bStartServer_){
        pubCatchPos_->publish(pos);
    }
}

void FollowRcsNode::pubTargettPostoCalibration(geometry_msgs::msg::PoseStamped pos) {
    if (!bStartServer_){
        return;
    }
    geometry_msgs::msg::PoseStamped tmpPose;
    tmpPose.header = robot_pos_->header;
    tmpPose.pose = robot_pos_->pose;
    if (pubCatchPostoCalibration_) {
        //RCLCPP_INFO(rclcpp::get_logger("rcs"), "pubCatchPostoCalibration_, %f, %f", 
        //tmpPose.pose.position.x, tmpPose.pose.position.y);
        pubCatchPostoCalibration_->publish(tmpPose);
    }

    if(pubTargettPostoCalibration_) {
        pubTargettPostoCalibration_->publish(pos);
    }
}

void FollowRcsNode::pubTargetPixel(geometry_msgs::msg::PoseStamped pos) {
    if (pubTargetPixel_ && bStartServer_) {
        pubTargetPixel_->publish(pos);
    }
}

void FollowRcsNode::pubCatchTwist(geometry_msgs::msg::TwistStamped twist){
    if (pubCatchTwist_  && bStartServer_){
        pubCatchTwist_->publish(twist);
    }
}

void FollowRcsNode::pubTargetTwist(geometry_msgs::msg::TwistStamped twist){
    if (pubTargetTwist_ && bStartServer_){      
        pubTargetTwist_->publish(twist);        
    }
}

void FollowRcsNode::subCatchPosCallback(const std::shared_ptr<geometry_msgs::msg::Twist> msg){
    if (!bStartServer_){
        return;
    }
    geometry_msgs::msg::TwistStamped twistStamped;
    twistStamped.twist = *msg;
    twistStamped.twist.linear.x = robot_line_speed_;
    twistStamped.header.stamp = this->get_clock()->now();
    pubCatchTwist(twistStamped);
}


void FollowRcsNode::pull_up_down_processs(int switchKey){
    Json::Value json_msg;
    json_msg["action"]=switchKey;
    Json::StreamWriterBuilder writer;
    std::string json_str = Json::writeString(writer, json_msg);
    std_msgs::msg::String msg;
    msg.data = json_str;
    pullUpProcessPub_->publish(msg);
    RCLCPP_INFO(this->get_logger(), "pull_up_processs Sent message: '%s'", msg.data.c_str());
}

void FollowRcsNode::subRbtServerInfo(const std_msgs::msg::String::SharedPtr msg){
    std::string strSendMsg = msg->data;
    Json::Reader reader;
    Json::Value value;
    if(!reader.parse(strSendMsg, value)){
        cout << "error" << endl;
        return; 
    }
    string strAction = value["action"].asString();
    if("velocity" == strAction){
        Json::Value params = value["params"];
        float speed = params["speed"].asDouble();
        robot_line_speed_ = speed;
    }
    else if ("wakeup" == strAction){
        //TODO
    }
    else if ("startFollow" == strAction){
        bStartServer_ = true;
        pull_up_down_processs(10);
    }
    else if ("endFollow" == strAction){
        bOnlyUwbMode_ = false;
        RCLCPP_INFO(rclcpp::get_logger("follow_rcs"), "get robot msg : %s", msg->data.c_str());
        stopFollow();
        nTrackerCount = 0;
        RCLCPP_INFO(rclcpp::get_logger("follow_rcs"), "endFollow, tracker start");
    }
    else
        RCLCPP_INFO(rclcpp::get_logger("follow_rcs"), "Unknow action from websocketNvidia");

}
void FollowRcsNode::navCallback(const std_msgs::msg::String::SharedPtr msg)
{
    std::string strMsg = msg->data;
    Json::Reader reader;
    Json::Value value;
    reader.parse(strMsg, value);
    if (value["status"].asInt()==2){
        RCLCPP_INFO(rclcpp::get_logger("navCallback"), "Obtain information from the navigation interface: the end point has been reached: %s", msg->data.c_str());
        bStartServer_=false;
    }

}
void FollowRcsNode::tripsigHandle(const std_msgs::msg::String::SharedPtr msg){
    std::string strSendMsg = msg->data;
    Json::Reader reader;
    Json::Value value;
    if(!reader.parse(strSendMsg, value)){
        RCLCPP_ERROR(rclcpp::get_logger("follow_rcs"), "reader.parse error in tripsigHandle");
        return; 
    }
    if (!value.isMember("mode")||value["mode"].asUInt()!=1)
        return;    
    if (value.isMember("action")&&value["action"].asUInt()==1)
    {
        bOnlyUwbMode_ = false;
        int need_rotate_angle = 10 - robot_cur_head_angle_ ;
        ctrlTiltBy(need_rotate_angle);
        pubCameraInfo_->publish(*cam_info_.get());
        RCLCPP_INFO(rclcpp::get_logger("follow_rcs"), "get robot msg : %s", msg->data.c_str());
        nTrackerCount = 0;
        bStartServer_ = true;
        searchRobotTimeStamp_ = base::homiUtils::getCurrentTimeStamp();
    }else if(value.isMember("action")&&value["action"].asUInt()==12){
        bOnlyUwbMode_ = false;
        RCLCPP_INFO(rclcpp::get_logger("follow_rcs"), "get robot msg : %s", msg->data.c_str());
        bStartServer_ = false;
        nTrackerCount = 0;
        RCLCPP_INFO(rclcpp::get_logger("follow_rcs"), "END TRIP");
    }
}

void FollowRcsNode::subTargetCalibrationReq(const geometry_msgs::msg::PoseStamped::SharedPtr msg){
    geometry_msgs::msg::PoseStamped pos;
    pos.header.stamp = this->get_clock()->now();
    pos.pose = msg->pose;
    lastPose=pos;
    pubTargetPos_->publish(pos);
    lastTargetCaliReqTime=this->now();
    bContinue_=true;
    RCLCPP_INFO_THROTTLE(this->get_logger(),*this->get_clock(),5000,"[Target POS] target_calibration_req target pos: %f, %f", pos.pose.position.x, pos.pose.position.y);
}

void FollowRcsNode::velCmdCallback(const geometry_msgs::msg::Twist::SharedPtr msg) {
    if(fabs(msg->linear.x) < 0.05) {
        // RCLCPP_INFO(this->get_logger(), "recv robot cmd l < 0.01");
        msg->linear.x  = 0;
    }
    if(fabs(msg->angular.z) < 0.05) {
        // RCLCPP_INFO(this->get_logger(), "recv robot cmd  a < 0.01");
        msg->angular.z  = 0;
    }

    if ((!bStartServer_)||(!bContinue_)){
	RCLCPP_INFO(this->get_logger(), "bStartServer_ or bContinue_ is false ");
        return;
    }
    string strMsgs;
    Json::Value value;
    Json::Value params;
    string strAction;
    strAction = "motionArcWithObstacles";
    value["action"] = strAction;
    params["lineSpeed"] = msg->linear.x;
    params["angularSpeed"] = msg->angular.z;
    value["params"] = params; 
    strMsgs = value.toStyledString().c_str();
    pubServerMsg(strMsgs.c_str());
    //RCLCPP_INFO(rclcpp::get_logger("follow_rcs"), "send msg: %s", strMsgs.c_str());
    
    geometry_msgs::msg::TwistStamped twistStamped;
    twistStamped.twist = *msg;
    twistStamped.twist.linear.x = robot_line_speed_;
    twistStamped.header.stamp = this->get_clock()->now();
    pubCatchTwist(twistStamped);
}


void FollowRcsNode::robotPositionTimerCallback(){
    if (!bStartServer_||!bContinue_){
        return;
    }
    if (!robot_pos_.get()){
        return;
    }
    geometry_msgs::msg::PoseStamped pos;
    auto stamped = this->get_clock()->now();
    pos.header.stamp = stamped;
    pos.pose.position.x = robot_pos_->pose.position.x;
    pos.pose.position.y = robot_pos_->pose.position.y;
    pos.pose.position.z = 0;
    pos.pose.orientation.w = robot_pos_->pose.orientation.w;
    pos.pose.orientation.x = robot_pos_->pose.orientation.x;
    pos.pose.orientation.y = robot_pos_->pose.orientation.y;
    pos.pose.orientation.z = robot_pos_->pose.orientation.z;
    pubCatchPos(pos);
}

void FollowRcsNode::watchDogforsubTargetCalibrationReq(){
    if (!bStartServer_){
        return;
    }
    if (bContinue_) {
        auto now = this->now();
        auto elapsed_time = now - lastTargetCaliReqTime;
        if (elapsed_time > std::chrono::seconds(2)) {
            RCLCPP_INFO(rclcpp::get_logger("follow_rcs"),"No Target Calibration Req message for more than two seconds. Stop Move.");
            bContinue_ = false; // 停止监测
        }
    }
}

