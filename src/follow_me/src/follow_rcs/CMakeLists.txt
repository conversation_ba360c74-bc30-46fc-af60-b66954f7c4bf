cmake_minimum_required(VERSION 3.8)
project(follow_rcs)

if("${CMAKE_BUILD_TYPE}" STREQUAL "Release")
  set(CMAKE_C_FLAGS_RELEASE "${CMAKE_C_FLAGS_RELEASE} -s")
  set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} -s")
endif()

# Default to C99
if(NOT CMAKE_C_STANDARD)
  set(CMAKE_C_STANDARD 99)
endif()

# Default to C++14
if(NOT CMAKE_CXX_STANDARD)
  set(CMAKE_CXX_STANDARD 11)
  set(CMAKE_CXX_STANDARD 14)
endif()

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

get_filename_component(SRC_DIR ${CMAKE_CURRENT_SOURCE_DIR} DIRECTORY)
get_filename_component(MAIN_DIR ${SRC_DIR} DIRECTORY)
message(${MAIN_DIR})

# find dependencies
find_package(ament_cmake REQUIRED)
find_package(rclcpp REQUIRED)
find_package(rclcpp_action REQUIRED)
find_package(std_msgs REQUIRED)
find_package(geometry_msgs REQUIRED)
find_package(sensor_msgs REQUIRED)
find_package(OpenCV REQUIRED)
find_package(cv_bridge REQUIRED)
find_package(image_transport REQUIRED)
find_package(tf2_ros REQUIRED)
find_package(tf2_geometry_msgs REQUIRED)
find_package(nav_msgs REQUIRED)
find_package(rosidl_default_generators REQUIRED)
find_package(follow_msgs REQUIRED)
find_package(homi_speech_interface REQUIRED)

message(${CMAKE_CURRENT_SOURCE_DIR})
message(${OpenCV_INCLUDE_DIRS})
#message(${OpenCV_LIBS})
message("----------------")

include_directories(
 ${CMAKE_CURRENT_SOURCE_DIR}
 ${CMAKE_CURRENT_SOURCE_DIR}/include
 ${CMAKE_CURRENT_SOURCE_DIR}/src
 ${OpenCV_INCLUDE_DIRS}
 ${follow_msgs_INCLUDE_DIRS}
 ${MAIN_DIR}/../../include
)

SET(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -lcurl")
add_executable(follow_rcs 
  src/follow_rcs.cpp
  src/follow_rcs/follow_rcs_node.cpp
  src/follow_rcs/follow_rcs_node.h )
# target_include_directories(follow_rcs PRIVATE
#   ${CMAKE_CURRENT_SOURCE_DIR}/../../../../include
# )
# message( "CMAKE_SOURCE_DIR: ${CMAKE_SOURCE_DIR}")

target_compile_features(follow_rcs PUBLIC c_std_99 cxx_std_17)  # Require C99 and C++17
ament_target_dependencies(follow_rcs
  rclcpp 
  rclcpp_action
  std_msgs
  geometry_msgs 
  OpenCV 
  cv_bridge 
  image_transport 
  sensor_msgs 
  tf2_ros 
  tf2_geometry_msgs 
  nav_msgs 
  follow_msgs
  homi_speech_interface
  )

target_link_libraries(follow_rcs
 curl 
 jsoncpp
 crypto
 avutil
 )

install(TARGETS follow_rcs DESTINATION lib/${PROJECT_NAME})

if(BUILD_TESTING)
  find_package(ament_lint_auto REQUIRED)
  # the following line skips the linter which checks for copyrights
  # comment the line when a copyright and license is added to all source files
  set(ament_cmake_copyright_FOUND TRUE)
  # the following line skips cpplint (only works in a git repo)
  # comment the line when this package is in a git repo and when
  # a copyright and license is added to all source files
  set(ament_cmake_cpplint_FOUND TRUE)
  ament_lint_auto_find_test_dependencies()
endif()

ament_package()