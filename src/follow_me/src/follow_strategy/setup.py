from setuptools import find_packages, setup

package_name = 'follow_strategy'

setup(
    name=package_name,
    version='0.0.0',
    packages=find_packages(exclude=['test']),
    data_files=[
        ('share/ament_index/resource_index/packages',
            ['resource/' + package_name]),
        ('share/' + package_name, ['package.xml']),
    ],
    install_requires=['setuptools'],
    zip_safe=True,
    maintainer='robot',
    maintainer_email='<EMAIL>',
    description='TODO: Package description',
    license='TODO: License declaration',
    tests_require=['pytest'],
    entry_points={
        'console_scripts': [
            "spawn_target_turtle = follow_strategy.spawn_target_turtle:main",
            "control_target_turtle = follow_strategy.control_target_turtle:main",
            "control_catch_turtle_v1 = follow_strategy.control_catch_turtle_v1:main",
            "path_handler_test = follow_strategy.path_handler_test:main",
            "path_recorder = follow_strategy.path_recorder:main",
            "target_twist_estimate = follow_strategy.target_twist_estimate:main",
            "path_data_gether = follow_strategy.path_data_gether:main",
            "path_data_reader = follow_strategy.path_data_reader:main"


        ],
    },
)
