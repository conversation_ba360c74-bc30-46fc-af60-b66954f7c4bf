#! /usr/bin/env python3
import dis
from re import S
import sys
import math
import numpy as np
from geometry_msgs.msg import TwistStamped, Twist, PoseStamped, Point, Pose, Quaternion
from nav_msgs.msg import Path
from .geometry_utils import clamp, calculate_curvature, get_yaw_angle, is_goal_reached, mock_path, shortest_angular_distance
from .kalman_filter import Ka<PERSON><PERSON><PERSON>er
# from follow_msgs.msg import TargetVelInfo # 新建
import logging
logging.basicConfig(level=logging.INFO)

class TargetPursuitController:
    class OperationMode:
        FOLLOW_MODE=0  
        TRIP_MODE=1
        ACCOMPANY_MODE=2

    def __init__(self) -> None:
        '''
        Initializations. Default length unit is cm.
        '''
        self.logger = logging.getLogger(__name__)
        self.desired_linear_vel_ = 0.5
        self.max_linear_vel_ = 1.3
        self.min_linear_vel = 0.01
        self.max_angular_vel_ = 0.8
        self.min_angular_vel = 0.01
        self.max_angular_accel_ = 5.0
        self.rotate_to_heading_angular_vel_ = 0.7
        self.angular_proportion_control_value_ = 1.0
        self.use_regulated_linear_velocity_scaling_ = True
        self.regulated_linear_scaling_min_radius_ = 1.0
        self.regulated_linear_scaling_min_speed_ = 0.3
        self.approach_velocity_scaling_dist_ = 0.5
        self.min_approach_linear_velocity_ = 0.1
        self.control_frequency_ = 5
        self.control_duration_ = 1.0 / self.control_frequency_
        self.goal_dist_tol_ = 0.7  # 最小跟随距离
        self.goal_vel_tol_ = 0.25
        self.use_rotate_to_goal_ = True
        self.use_rotate_to_goal_heading_ = False
        self.rotate_to_heading_min_angle_ = math.pi / 6.0 #90
        self.use_fixed_curvature_lookahead_ = False
        self.allow_reversing_ = True
        self.last_target_pose_ = None
        self.timestamp_ = 0.0 # in seconds
        self.valid_time_interval_ = 0.5 # in seconds
        self.dt_ = 1.0/30
        self.dt2_ = self.dt_ * self.dt_
        self.F_ = np.array([[1, 0, self.dt_, 0, 0.5*self.dt2_, 0], [0, 1, 0, self.dt_, 0, 0.5*self.dt2_], [0, 0, 1, 0, self.dt_, 0], [0, 0, 0, 1, 0, self.dt_], [0, 0, 0, 0, 1, 0], [0, 0, 0, 0, 0, 1]])
        self.H_ = np.array([[1, 0, 0, 0, 0, 0], [0, 1, 0, 0, 0, 0]])
        self.Q_ = np.eye(self.F_.shape[0]) * 0.2
        self.R_ = np.eye(self.H_.shape[0]) * 0.2
        self.kf_ = KalmanFilter(F = self.F_, H = self.H_, Q = self.Q_, R = self.R_)
        self.target_pos_ = None
        self.kp = 0.5
        self.ki = 0.01  
        self.kd = 0.0
        self.integral_error = 0.0
        self.prev_error = 0.0
        self.current_mode = self.OperationMode.TRIP_MODE
        self.MIN_DIST_TOL = 1.0
        self.MAX_DIST_TOL = 3.0
        self.DIST_ADJUST_STEP = 0.5
        self.is_rotating = False

    def set_current_mode(self, mode):
        self.current_mode = mode
        self.integral_error = 0.0
        self.prev_error = 0.0
        if mode == self.OperationMode.FOLLOW_MODE:
            self.logger.info("Current mode is FOLLOW_MODE")
            self.goal_dist_tol_=1.5
        if mode == self.OperationMode.ACCOMPANY_MODE:
            self.logger.info("Current mode is ACCOMAPY MODE")
            self.goal_dist_tol_=0.3
        if mode == self.OperationMode.TRIP_MODE:
            self.logger.info("Current mode is TRIP MODE")
            self.goal_dist_tol_=0.7
    def adjust_distance_tolerance(self, command):
        """根据命令调整距离容差值"""
        if self.current_mode != self.OperationMode.FOLLOW_MODE:
            self.logger.info("Current mode is NOT FOLLOW MODE,Do not need adjust")
            return self.goal_dist_tol_
        command = command.lower().strip()
        if command in ["comeclose", "靠近", "过来"]:
            self.goal_dist_tol_ -= self.DIST_ADJUST_STEP
        elif command in ["gofar", "走远"]:
            self.goal_dist_tol_ += self.DIST_ADJUST_STEP
        self.goal_dist_tol_ = max(self.MIN_DIST_TOL, min(self.MAX_DIST_TOL, self.goal_dist_tol_))
        return self.goal_dist_tol_

    def computeVelocityCommands(self, pose: PoseStamped, velocity: TwistStamped):
        '''
        Compute the best command given the current pose and velocity of the catcher and the velocity of target, with possible debug information
        Parameters
        ----------
        pose: PoseStamped
            Current robot pose
        velocity: TwistStamped
            Current robot velocity
        Returns
        ----------
        TwistStamped
            Best command
        '''
        called_rotate = False
        cmd_vel = TwistStamped()
        cmd_vel.header = pose.header
        
        if self.target_pos_ == None:
            return cmd_vel,called_rotate

        lookahead_curvature = calculate_curvature(self.target_pos_.pose.position)
        # print("look ahead curvature = {}".format(lookahead_curvature))
        # Setting the velocity direction
        sign = 1.0
        if math.hypot(self.target_pos_.pose.position.x, self.target_pos_.pose.position.y) < self.goal_dist_tol_:
            sign = -1.0


        # Make sure we're in compliance with basic constraints
        # double angle_to_heading;
        linear_vel = self.desired_linear_vel_
        angular_vel = 0.0
        # If the target is static and the catcher is closer enough to the target, then rotate to goal heading
        # If the catcher is closer enough to the target
        if self.isGoalReached(carrot_pose=self.target_pos_):
            # print("Goal is reached.")
            # if self.isTargetStatic(velocity=target_velocity) and self.shouldRotateToGoalHeading(carrot_pose):
            angle_to_goal = 0.0
            # rotate to goal heading
            if self.use_rotate_to_goal_heading_:
                angle_to_goal = self.computeAngleToGoalHeading(goal_pose=self.target_pos_)
            # rotate to goal
            elif self.use_rotate_to_goal_:
                angle_to_goal = self.computeAngleToGoal(goal_pose=self.target_pos_)
            (linear_vel, angular_vel) = self.rotateToHeading(angle_to_goal, velocity.twist)
            called_rotate = True
            self.logger.info("Goal is reached. angle_to_goal = {}, linear_vel = {}, angular_vel = {}.".format(angle_to_goal, linear_vel, angular_vel))
        else:
            # print("Don't need to rotate to goal heading.")
            angle_to_path = 0.0
            flag = None
            (angle_to_path, flag) = self.shouldRotateToPath(self.target_pos_)
            # print("Should rotate to path: {}, angle_to_path = {}".format(flag, angle_to_path))
            if flag == True:
                (linear_vel, angular_vel) = self.rotateToHeading(angle_to_path, velocity.twist)
                called_rotate = True
                self.logger.info("Rotate to heading. linear_vel = {}, angular_vel = {}".format(linear_vel, angular_vel))
            else:
                if self.current_mode != self.OperationMode.TRIP_MODE:
                    current_dist = math.hypot(self.target_pos_.pose.position.x, self.target_pos_.pose.position.y)
                    error = current_dist - self.goal_dist_tol_
                    self.integral_error += error * self.dt_
                    derivative = (error - self.prev_error) / self.dt_
                    self.prev_error = error  
                    if abs(self.integral_error) > 2.0:
                        self.integral_error = 0.9 * self.integral_error
                    linear_vel = (self.kp * error + self.ki * self.integral_error +self.kd * derivative)
                    self.logger.info("PID PID PID linear_vel={},angular_vel={},error={},integral_error={},derivative={},target.x={},target.y={}".format(linear_vel, angular_vel,error,self.integral_error,derivative,self.target_pos_.pose.position.x,self.target_pos_.pose.position.y))
                # Apply velocity proportion control
                linear_vel = self.applyConstraints(lookahead_curvature, linear_vel, sign)
                # Apply curvature to angular velocity after constraining linear velocity
                angular_vel = linear_vel * lookahead_curvature * self.angular_proportion_control_value_
                if angular_vel >= 0:
                    angular_vel = clamp(angular_vel, self.min_angular_vel, self.max_angular_vel_)
                else:
                    angular_vel = clamp(angular_vel, -self.max_angular_vel_, -self.min_angular_vel)
                self.logger.info("After Apply Constraints. linear_vel = {}, angular_vel = {},target.x={},target.y={}".format(linear_vel, angular_vel,self.target_pos_.pose.position.x,self.target_pos_.pose.position.y))

        angle_radians = math.atan2(self.target_pos_.pose.position.y,self.target_pos_.pose.position.x)
        angle_degrees = math.degrees(angle_radians)
        cmd_vel.twist.angular.z = -angular_vel
        # if -30 <= angle_degrees <= 30:
        # #if(angle_degrees>self.my_angle_threshold_value):
        #     cmd_vel.twist.linear.x = linear_vel
        # else:
        #     cmd_vel.twist.linear.x = 0.0
        #     self.logger.info("angle is more than 30.Rotate but not move")


        cmd_vel.twist.linear.x = linear_vel
        cmd_vel.twist.angular.z = -angular_vel
        return cmd_vel, called_rotate

    def isTargetStatic(self, velocity: TwistStamped):
        '''
        Check whether the target is static or not.
        Parameters
        ----------
        velocity: TwistStamped
            The target's velocity
        Returns
        ----------
        bool
            True if the linear velocity is smaller than the threshold and False otherwise.
        '''
        linear_vel = math.hypot(velocity.twist.linear.x, velocity.twist.linear.y, velocity.twist.linear.z)
        if linear_vel < self.goal_vel_tol_:
            # print("Target is static.")
            return True
        else:
            # print("Target is moving.")
            return False
    
    def addPose(self, pose: PoseStamped):
        '''
        Add new pose to path
        Parameters
        ----------
        pose: PoseStamped
            the pose to add
        '''
        new_timestamp = np.double(pose.header.stamp.sec) + np.double(pose.header.stamp.nanosec) / 1e9
        if self.timestamp_ == 0.0:
            self.timestamp_ = new_timestamp
            self.dt_ = 1.0 / 30
        else:
            self.dt_ = new_timestamp - self.timestamp_
        self.dt_=clamp(self.dt_,0.01,0.5)
        # update motion model of kalman filter
        self.dt2_ = self.dt_ * self.dt_
        self.F_ = np.array([[1, 0, self.dt_, 0, 0.5*self.dt2_, 0], [0, 1, 0, self.dt_, 0, 0.5*self.dt2_], [0, 0, 1, 0, self.dt_, 0], [0, 0, 0, 1, 0, self.dt_], [0, 0, 0, 0, 1, 0], [0, 0, 0, 0, 0, 1]])
        self.kf_.updateModel(self.F_)

        # predict new state using kalman filter
        self.kf_.predict()
        # update kalman filter's state matrix
        pos_observe = np.array([pose.pose.position.x, pose.pose.position.y])
        pos_filter = self.kf_.update(pos_observe)

        target_pose = pose
        if self.dt_ < self.valid_time_interval_:
            target_pose.pose.position.x = pos_filter[0]
            target_pose.pose.position.y = pos_filter[1]

        self.target_pos_ = target_pose
        self.timestamp_ = new_timestamp

    def setSpeedLimit(self, speed_limit: float, percentage: bool):
        '''
        Limits the maximum linear speed of the robot.
        Parameters
        ----------
        speed_limit: float
            expressed in absolute value (in m/s) or in percentage from maximum robot speed.
        percentage: bool
            Setting speed limit in percentage if true or in absolute values in false case.
        '''
        if percentage:
            # Speed limit is expressed in % from maximum speed of robot
            self.max_linear_vel_ = self.max_linear_vel_ * speed_limit / 100.0
        else:
            # Speed limit is expressed in absolute value
            self.max_linear_vel_ = speed_limit

    def shouldRotateToPath(self, carrot_pose: PoseStamped):
        x = carrot_pose.pose.position.x
        y = carrot_pose.pose.position.y
        if abs(x) < 0.05:
            sign = 1.0 if x >= 0 else -1.0
            x = sign * 0.05
        angle_to_path = math.atan2(y, x)
        flag = abs(angle_to_path) > self.rotate_to_heading_min_angle_
        return (angle_to_path, flag)
    
    def computeAngleToGoal(self, goal_pose: PoseStamped):
        '''
        Compute the angle the robot should rotate to goal
        Parameters
        ----------
        goal_pose: PoseStamped
            the final pose of the target
        Returns
        ----------
        bool
            Whether should rotate to path heading
        '''
        # Whether we should rotate robot to rough path heading
        angle_to_goal = math.atan2(goal_pose.pose.position.y, goal_pose.pose.position.x if goal_pose.pose.position.x > 0.05 else 0.05)
        # print("angle_to_path: {}, x: {}, y: {}".format(angle_to_path, carrot_pose.pose.position.x, carrot_pose.pose.position.y))
        return angle_to_goal

    def shouldRotateToGoalHeading(self, carrot_pose: PoseStamped):
        '''
        Whether robot should rotate to final goal orientation when the robot almost reach the goal
        Parameters
        ----------
        carrot_pose: PoseStamped
            current lookahead point
        Returns
        ----------
        bool
            Whether should rotate to goal heading
        '''
        # Whether we should rotate robot to goal heading
        # If 
        dist_to_goal = math.hypot(carrot_pose.pose.position.x, carrot_pose.pose.position.y)
        return self.use_rotate_to_goal_heading_ and dist_to_goal < self.goal_dist_tol_

    def isGoalReached(self, carrot_pose: PoseStamped):
        '''
        Check whether the goal is reached according to the distance between robot and target
        Parameters
        ----------
        carrot_pose: PoseStamped
            current lookahead point
        Returns
        ----------
        bool
            True for goal is reached otherwise False
        '''
        # Whether we should rotate robot to goal heading
        # If 
        dist_to_goal = math.hypot(carrot_pose.pose.position.x, carrot_pose.pose.position.y)
        return dist_to_goal <= self.goal_dist_tol_
    
    def computeAngleToGoalHeading(self, goal_pose: PoseStamped):
        '''
        Whether robot should rotate to final goal orientation when the robot almost reach the goal
        Parameters
        ----------
        carrot_pose: PoseStamped
            current lookahead point
        Returns
        ----------
        bool
            Whether should rotate to goal heading
        '''
        # Compute goal heading angle w.r.t robot's orientation
        goal_angle = get_yaw_angle(goal_pose.pose.orientation)
        # goal_angle = get_yaw_angle(transformed_path.poses[-1].pose.orientation)
        # robot_angle = get_yaw_angle(pose.pose.orientation)
        # quaternion = Quaternion()
        # (quaternion.x, quaternion.y, quaternion.z, quaternion.w) = quaternion_from_euler(0, 0, 45)
        # robot_angle = get_yaw_angle(quaternion)
        # angle_to_goal = goal_angle - robot_angle
        angle_to_goal = shortest_angular_distance(start=0, end=goal_angle)
        print("angle_to_goal = {}".format(angle_to_goal))
        return angle_to_goal

    def rotateToHeading(self, angle_to_path: float, curr_speed: Twist):
        '''
        Create a smooth and kinematically smoothed rotation command
        Parameters
        ----------
        angle_to_path: float
            angle of robot output relate to carrot marker
        curr_speed: Twist
            the current robot speed
        Returns
        ----------
        float
            linear_vel
        float
            angular_vel
        '''
        # Rotate in place using max angular velocity / acceleration possible
        linear_vel = 0.0
        # sign = 1.0 if angle_to_path > 0.0 else -1.0
        sign = math.copysign(1.0, angle_to_path)
        angular_vel = sign * min(self.rotate_to_heading_angular_vel_, abs(angle_to_path))

        min_feasible_angular_speed = curr_speed.angular.z - self.max_angular_accel_ * self.control_duration_
        max_feasible_angular_speed = curr_speed.angular.z + self.max_angular_accel_ * self.control_duration_
        # print("max_angular_vel = {}, min_angular_vel = {}".format(max_feasible_angular_speed, min_feasible_angular_speed))
        angular_vel = clamp(angular_vel, min_feasible_angular_speed, max_feasible_angular_speed)
        max_feasible_angular_speed = min(max_feasible_angular_speed, self.max_angular_vel_)
        if abs(angular_vel) < 0.01:
            angular_vel = 0.0
        return (linear_vel, angular_vel)
    
    def curvatureConstraint(self, raw_linear_vel: float, curvature: float, min_radius: float):
        '''
        Apply curvature constraint regulation on the linear velocity. The larger the curvature is and the smaller the linear velocity is.
        Parameters
        ----------
        raw_linear_velocity: float
            Raw linear velocity desired
        curvature: float
            Curvature of the current command to follow the path
        min_radius: float
            Minimum path radius to apply the heuristic
        Returns
        ----------
        float
            Velocity after applying the curvature constraint
        '''
        # get avoid of division by zero
        curvature += 0.000001 if curvature == 0.0 else 0.0
        radius = abs(1.0 / curvature)
        if radius < min_radius:
            return raw_linear_vel * (1.0 - (abs(radius - min_radius) / min_radius))
        else:
            return raw_linear_vel

    def approachVelocityScalingFactor(self, remaining_distance: float):
        '''
        Waiting to apply the threshold based on integrated distance ensures we don't erroneously apply approach scaling on curvy paths that are contained in a large local cost map.
        Parameters
        ----------
        remaining_distance: float
            Length of the remaining path
        '''
        if remaining_distance < self.approach_velocity_scaling_dist_ + self.goal_dist_tol_:
                # dist = math.hypot(last_pose.pose.position.x, last_pose.pose.position.y)
                # print("Approaching dist = {}".format(dist))
                # add 0.1 to prevent zero velocity when approaching goal distance tolerance
                return abs(remaining_distance - self.goal_dist_tol_) / self.approach_velocity_scaling_dist_
                # return remaining_distance / self.approach_velocity_scaling_dist_
        else:
            return 1.0
    
    def approachVelocityConstraint(self, linear_vel: float):
        '''
        Velocity on approach to goal heuristic regulation term constrained_linear_vel Linear velocity already constrained by heuristics
        Parameters
        ----------
        linear_vel: float
            The input velocity to constraint.
        Returns
        ----------
        float
            Velocity after regulation via approach to goal slow-down
        '''
        # use the distance to goal to compute remaining distance
        remaining_distance = math.hypot(self.target_pos_.pose.position.x, self.target_pos_.pose.position.y)
        # print("remaining_dist = {}".format(remaining_distance))
        velocity_scaling = self.approachVelocityScalingFactor(remaining_distance=remaining_distance)
        # print("velocity_scaling = {}".format(velocity_scaling))
        approach_vel = linear_vel * velocity_scaling
        # print("approach_vel = {}".format(approach_vel))

        # if approach_vel < self.min_approach_linear_velocity_:
        #     approach_vel = self.min_approach_linear_velocity_

        return min(linear_vel, approach_vel)

    def applyConstraints(self, curvature: float, linear_vel: float, sign: float):
        '''
        Apply regulation constraints to the system
        Parameters
        ----------
        linear_vel: double
            robot command linear velocity input
        curvature: double
            curvature of path
        speed: Twist
            speed of robot
        pose_cost: double
            cost at this pose
        '''
        # limit the linear velocity by curvature
        curvature_vel = linear_vel
        # print("Test vel: {}".format(curvature_vel))
        if self.use_regulated_linear_velocity_scaling_:
            curvature_vel = self.curvatureConstraint(linear_vel, curvature, self.regulated_linear_scaling_min_radius_)

        # limit the linear velocity by proximity to obstacles
        # if (params_->use_cost_regulated_linear_velocity_scaling) {
        # cost_vel = heuristics::costConstraint(linear_vel, pose_cost, costmap_ros_, params_);
        # }

        # Use the lowest of the 2 constraints, but above the minimum translational speed
        linear_vel = min(linear_vel, curvature_vel)
        linear_vel = max(linear_vel, self.regulated_linear_scaling_min_speed_)

        # Apply constraint to reduce speed on approach to the final goal pose
        # linear_vel = self.approachVelocityConstraint(linear_vel)

        # Limit linear velocities to be valid
        linear_vel = clamp(abs(linear_vel), self.min_linear_vel, self.max_linear_vel_)
        linear_vel = sign * linear_vel
        return linear_vel

    def circleSegmentIntersection(self, p1: Point, p2: Point, radius: float):
        '''
        Find the intersection a circle and a line segment.
        This assumes the circle is centered at the origin.
        If no intersection is found, a floating point error will occur.
        Parameters
        ----------
        p1: Point
            first endpoint of line segment
        p2: Point
            second endpoint of line segment
        radius: float
            radius of circle
        Returns
        ----------
        Point
            point of intersection
        '''
        # Formula for intersection of a line with a circle centered at the origin, modified to always return the point that is on the segment between the two points.
        # https://mathworld.wolfram.com/Circle-LineIntersection.html
        # This works because the poses are transformed into the robot frame. This can be derived from solving the system of equations of a line and a circle, which results in something that is just a reformulation of the quadratic formula. Interactive illustration in doc/circle-segment-intersection.ipynb as well as at https://www.desmos.com/calculator/td5cwbuocd
        x1 = p1.x
        x2 = p2.x
        y1 = p1.y
        y2 = p2.y

        dx = x2 - x1
        dy = y2 - y1
        dr2 = dx * dx + dy * dy
        D = x1 * y2 - x2 * y1

        # Augmentation to only return point within segment
        d1 = x1 * x1 + y1 * y1
        d2 = x2 * x2 + y2 * y2
        dd = d2 - d1

        p = Point()
        sqrt_term = math.sqrt(radius * radius * dr2 - D * D)
        p.x = (D * dy + math.copysign(1.0, dd) * dx * sqrt_term) / dr2
        p.y = (-D * dx + math.copysign(1.0, dd) * dy * sqrt_term) / dr2
        return p
