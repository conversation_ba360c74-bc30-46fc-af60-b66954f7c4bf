#!/usr/bin/env python3
import rclpy
from rclpy.node import Node
from nav_msgs.msg import Path
from geometry_msgs.msg import PoseStamped, TwistStamped
from tf_transformations import quaternion_matrix, euler_from_quaternion
from .path_handler import PathHandler
from .regulated_pure_pursuit_controller import RegulatedPurePursuitController

class PathHandlerTestNode(Node):

    def __init__(self):
        super().__init__('path_handler_test')
        self.path_handler_ = PathHandler(max_robot_pose_search_dist=1.0, max_costmap_extent=2.0)
        self.controller_ = RegulatedPurePursuitController()
        self.path_handler_.setPath(path=Path())
        # Subscribe to a turtle{1}{2}/pose topic and call handle_turtle_pose callback function on each message
        self.target_pose_subscriber_ = self.create_subscription(PoseStamped, '/{}/posestamped'.format('target_turtle'), self.target_pose_callback, 10)
        self.catch_pose_subscriber_ = self.create_subscription(PoseStamped, '/{}/posestamped'.format('catch_turtle'), self.catch_pose_callback, 10)

        self.target_twist_subscriber_ = self.create_subscription(TwistStamped, '/{}/twiststamped'.format('target_turtle'), self.target_twist_callback, 10)
        self.catch_twist_subscriber_ = self.create_subscription(TwistStamped, '/{}/twiststamped'.format('catch_turtle'), self.catch_twist_callback, 10)
        # self.subscription  # prevent unused variable warning

        self.catch_pose_ = PoseStamped()
        self.catch_twist_ = TwistStamped()

    def target_pose_callback(self, msg):
        # self.path_handler_.addPose(msg)
        self.controller_.addPose(pose=msg)

        # # Test Pose Transform
        # catch_pose = self.path_handler_.getLastPose()
        # if catch_pose != None:
        #     # transformed_catch_pose = self.path_handler_.transformPose(in_pose=catch_pose, base_pose=msg)
        #     # self.get_logger().info(str(transformed_catch_pose))
        #     transform_pose = self.path_handler_.getTransformPose(pose=msg)
        #     transformed_catch_pose_new = self.path_handler_.transformPoseTest(in_pose=catch_pose, transform=transform_pose)
        #     self.get_logger().info(str(transformed_catch_pose_new))
        #     # (roll, pitch, yaw) = euler_from_quaternion([transformed_catch_pose.pose.orientation.x, transformed_catch_pose.pose.orientation.y, transformed_catch_pose.pose.orientation.z, transformed_catch_pose.pose.orientation.w])
        #     # self.get_logger().info('yaw = {}'.format(yaw))
        #     # rotate_matrix = quaternion_matrix([msg.pose.orientation.x, msg.pose.orientation.y, msg.pose.orientation.z, msg.pose.orientation.w])
        #     # self.get_logger().info('rotate_matrix = {}'.format(rotate_matrix[0:3, 0:3]))

    def catch_pose_callback(self, msg):
        # # Test Pose Transform
        # if len(self.path_handler_.getPath().poses) == 0:
        #     self.path_handler_.addPose(msg)
        # else:
        #     last_pose = self.path_handler_.getPath().poses[-1]
        #     self.get_logger().info("last_pose = {}".format(last_pose))
        #     self.get_logger().info("msg = {}".format(msg))
        #     # if abs(last_pose.pose.position.x - msg.pose.position.x) > 0.00001 or abs(last_pose.pose.position.y - msg.pose.position.y) > 0.00001 or abs(last_pose.pose.position.z - msg.pose.position.z) > 0.00001:
        #     #     self.path_handler_.addPose(msg)
        # Test Start End Index

        # Test start and end index
        # (start, end) = self.path_handler_.findTransformStartEnd(robot_pose=msg)
        # self.get_logger().info("length = {}, start = {}, end = {}".format(len(self.path_handler_.getPath().poses), start, end))

        self.catch_pose_ = msg
    
    def target_twist_callback(self, msg):
        # lookahead_dist = self.controller_.getLookAheadDistance(speed=msg.twist)
        # self.get_logger().info("lookahead_dist = {}".format(lookahead_dist))
        pass
    
    def catch_twist_callback(self, msg):
        self.catch_twist_ = msg
        # Test global path transform
        self.controller_.computeVelocityCommands(pose=self.catch_pose_, velocity=self.catch_twist_)

def main():
    rclpy.init()
    node = PathHandlerTestNode()
    try:
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass

    rclpy.shutdown()