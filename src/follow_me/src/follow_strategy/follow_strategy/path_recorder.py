#!/usr/bin/env python3
import rclpy
# import numpy as np
import time
from rclpy.node import Node
from nav_msgs.msg import Path
from geometry_msgs.msg import PoseStamped

class PathRecorderNode(Node):

    def __init__(self):
        super().__init__('path_recorder')
        # Subscribe to a turtle{1}{2}/pose topic and call handle_turtle_pose callback function on each message
        self.declare_parameter("topic_to_record", "/target_turtle/posestamped")
        topic_to_record = self.get_parameter("topic_to_record").get_parameter_value().string_value
        self.get_logger().info("Topic to record: " + topic_to_record)
        self.pose_subscriber_ = self.create_subscription(PoseStamped, topic_to_record, self.pose_callback, 10)
        self.path_ = Path()
        self.file_name_ = time.strftime("%Y-%m-%d %H:%M:%S") + ".csv"
        self.file_path_ = "/home/<USER>/follow_me_ws/src/follow_strategy/sample/"
        self.file_ = open(self.file_path_+self.file_name_, 'w')
        self.get_logger().info("Open record file: " + self.file_path_ + self.file_name_)

    def pose_callback(self, msg):
        # self.controller_.addPose(pose=msg)
        # self.get_logger().info("pose: " + str(msg))

        # [-1.005347728729248, -1.2390522956848145, 0.0],
        pose = "{}, {}, {}\n".format(msg.pose.position.x, msg.pose.position.y, msg.pose.position.z)
        self.get_logger().info(pose)
        self.file_.write(pose)
        # self.path_.poses.append(msg)
        # self.get_logger().info("path: " + str(self.path_.poses))
    

def main():
    rclpy.init()
    node = PathRecorderNode()
    try:
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass

    rclpy.shutdown()