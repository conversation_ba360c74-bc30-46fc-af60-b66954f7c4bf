import numpy as np

class KalmanFilter(object):
    def __init__(self, F = None, B = None, H = None, Q = None, R = None, P = None, x0 = None):

        if(F is None or H is None):
            raise ValueError("Set proper system dynamics.")

        self.n = F.shape[1]
        self.m = H.shape[1]

        self.F = F
        self.H = H
        self.B = 0 if B is None else B
        self.Q = np.eye(self.n) if Q is None else Q
        self.R = np.eye(self.n) if R is None else R
        self.P = np.eye(self.n) if P is None else P
        self.x = np.zeros((self.n, 1)) if x0 is None else x0
        self.outlier_dist_ = 1.5
#         self.mean_dist_ = 0.0
#         self.sigma_dist_ = 0.0
#         self.count_ = 0

    def updateModel(self, F = None):
        if F is not None:
            self.F = F

    # 预测步骤
    # x_pred = A @ x             # 预测状态
    # P_pred = A @ P @ A.T + q   # 预测状态协方差矩阵
    def predict(self, u = 0):
        self.x = np.dot(self.F, self.x) + np.dot(self.B, u)
        self.P = np.dot(np.dot(self.F, self.P), self.F.T) + self.Q
        return self.x
    # 更新步骤
    # K = P_pred @ H.T / (H @ P_pred @ H.T + r)   # 卡尔曼增益
    # x = x_pred + K * (measurement - H @ x_pred)  # 校正状态
    # P = (np.eye(2) - K @ H) @ P_pred            # 校正状态协方差矩阵
    def update(self, z):
#         self.count_ = self.count_ + 1
        # print(z)
        y = z - np.dot(self.H, self.x)
#         print(y[0])
#         print(math.hypot(y[0,0],y[0,1]))
#         if self.count_ > 5 and math.hypot(y[0,0],y[0,1]) < self.outlier_dist_:
#         self.mean_dist_ = self.mean_dist_+ math.hypot(y[0,0],y[0,1])
#         self.mean_dist_ = self.mean_dist_ / self.count_
#         print(self.mean_dist_)
        S = self.R + np.dot(self.H, np.dot(self.P, self.H.T))
        K = np.dot(np.dot(self.P, self.H.T), np.linalg.inv(S))
        self.x = self.x + np.dot(K, y)
        I = np.eye(self.n)
        self.P = np.dot(np.dot(I - np.dot(K, self.H), self.P), (I - np.dot(K, self.H)).T) + np.dot(np.dot(K, self.R), K.T)
        return np.dot(self.H,  self.x)[0]