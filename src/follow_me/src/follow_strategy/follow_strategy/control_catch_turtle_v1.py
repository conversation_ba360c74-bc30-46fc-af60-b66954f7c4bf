#!/usr/bin/env python3
import rclpy
#from rclpy.qos import QoSProfile
from rclpy.node import Node
from geometry_msgs.msg import PoseStamped, TwistStamped, Twist
from std_msgs.msg import Bool, String
from .target_pursuit_controller import TargetPursuitController
from .top_controller import TopController
from .control_strategy_enum import ControlStrategy
from .geometry_utils import getTransformPose, get_yaw_angle,euclidean_distance
from sensor_msgs.msg import CameraInfo
from std_msgs.msg import UInt8  
import numpy as np
from numpy import double

# from follow_msgs.msg import TargetVelInfo  # 最大线速度等参数信息

class PurePursuitControllerNode(Node):

    def __init__(self):
        super().__init__('pure_pursuit_controller')
        self.control_strategy_ = ControlStrategy.Wait
        self.top_controller_ = TopController() # 这个地方用到了期望角速度
        self.target_controller_ = TargetPursuitController() # 在这个地方用到了期望速度等

        # 在此处首先把初始化的最大线速度等参数信息发布到qt界面上
        # self.targetVel_publisher_ = self.create_publisher(TargetVelInfo, '/follow_me/targetVel_info', 10)
        #self.publish_target_vel_info() #直接发布消息
        #self.timer = self.create_timer(10.0, self.publish_target_vel_info) # 定时发布消息(每隔十秒)

        # ck: 订阅最大线速度等参数信息，使用self.target_vel_callback作为回调函数，队列大小为10
        # self.target_get_params = self.create_subscription(
        #     Bool,
        #     '/follow_me/get_params',
        #     self.publish_target_vel_info,
        #     10)

        # ck: 订阅最大线速度等参数信息，使用self.target_vel_callback作为回调函数，队列大小为10
        # self.target_vel_subscriber_ = self.create_subscription(
        #     TargetVelInfo,
        #     '/follow_me/pub_TargetVel',
        #     self.target_vel_callback,
        #     10)

        #订阅的目标位置’/target_turtle/posestamped‘
        #订阅的机器人位置‘/catch_turtle/posestamped’
        self.target_pose_subscriber_ = self.create_subscription(PoseStamped, '/target_turtle/posestamped', self.target_pose_callback, 10)
        self.catch_pose_subscriber_ = self.create_subscription(PoseStamped, '/catch_turtle/posestamped', self.catch_pose_callback, 10)
        #订阅的目标速度’/target_turtle/twiststamped/estimate‘
        #订阅的机器人速度‘/catch_turtle/twiststamped’
        self.target_twist_subscriber_ = self.create_subscription(TwistStamped, '/target_turtle/twiststamped/estimate', self.target_twist_callback, 10)
        self.catch_twist_subscriber_ = self.create_subscription(TwistStamped, '/catch_turtle/twiststamped', self.catch_twist_callback, 10)  #未使用
        #发布的机器人角速度线速度
        self.catch_ctrl_publisher_ = self.create_publisher(Twist, '/catch_turtle/ctrl_instruct_nvidia', 100)

        self.target_image_subscriber_ = self.create_subscription(PoseStamped, '/target_image/posestamped', self.target_image_callback, 10)
        self.catch_cmd_publisher_ = self.create_publisher(Twist, '/catch_turtle/cmd_vel', 10)

        self.mode_sub = self.create_subscription(UInt8,'/operation_mode',self.mode_callback,10)
        self.distance_sub = self.create_subscription(String,'/adjust_distance',self.distance_callback,10)

        #qos_profile = QoSProfile(depth=10)
        # 设置可靠性
        #qos_profile.reliable() 
        self.follow_end_subscriber_ = self.create_subscription(String, 'follow_me/end', self.follow_end_callback, 1)
        self.follow_start_subscriber_ = self.create_subscription(String, 'follow_me/start', self.follow_start_callback, 1)

        self.cam_info_subscriber_ = self.create_subscription(CameraInfo, '/follow_me/camera_info', self.cam_info_callback, 10)

         # 定时器设置为 1 秒
        self.timer = self.create_timer(1.0, self.timer_callback)
        # 记录最后一次处理的时间戳
        self.last_timestamp = self.get_clock().now()
        # 设置超时阈值为 5 秒
        self.timeout_threshold = rclpy.time.Duration(seconds = 5.0)
        self.timeout_start_threshold = rclpy.time.Duration(seconds = 60.0)

        self.catch_pose_ = PoseStamped()
        self.last_catch_pose_ = None
        self.startFlag = Bool()
        self.startFlag = False
        self.image_pose_ = PoseStamped()
        self.catch_twist_ = TwistStamped()
        self.target_twist_ = TwistStamped()
        self.last_cmd_vel_ = Twist()
        self.last_cmd_vel_.linear.x = 0.0
        self.last_cmd_vel_.angular.z = 0.0
        self.cmd_liner_vel_smooth_ratio_ = 0.2
        self.cmd_angular_vel_smooth_ratio_ = 0.1
        self.only_uwb_data_ = Bool()
        self.only_uwb_data_ = False
        self.last_log_time = self.get_clock().now() 

    def timer_callback(self):
        current_time = self.get_clock().now()
        if (((current_time - self.last_timestamp) > self.timeout_threshold) and (True == self.startFlag)): 
            #self.get_logger().info('Timeout start Search')
            self.last_timestamp = current_time
            self.control_strategy_ == ControlStrategy.Search
        if (current_time - self.last_timestamp) > self.timeout_start_threshold:
            #self.get_logger().info('Timeout start')
            self.last_timestamp = current_time
            self.control_strategy_ = ControlStrategy.Wait
            self.last_cmd_vel_.linear.x = 0.0
            self.last_cmd_vel_.angular.z = 0.0
            self.startFlag = False

    # ck:发布函数(发布到界面上)
    # def publish_target_vel_info(self, data):
    #     # 构造消息
    #     msg = TargetVelInfo()
    #     msg.max_linear_vel = self.target_controller_.max_linear_vel_
    #     msg.max_angular_vel = self.target_controller_.max_angular_vel_
    #     msg.target_linear_vel = self.target_controller_.desired_linear_vel_
    #     msg.target_angular_vel = self.top_controller_.desired_angular_vel_
    #     msg.goal_dist_tol = self.target_controller_.goal_dist_tol_
    #     self.get_logger().info("publish_target_vel_info: {}, {}, {}, {}, {}".format(msg.max_linear_vel, msg.max_angular_vel,msg.target_linear_vel,msg.target_angular_vel,msg.goal_dist_tol))
    #     # 发布消息
    #     self.targetVel_publisher_.publish(msg)

    # ck：回调函数
    # def target_vel_callback(self, msg):
    #     self.target_controller_.max_linear_vel_ = msg.max_linear_vel
    #     self.target_controller_.max_angular_vel_ = msg.max_angular_vel
    #     self.target_controller_.desired_linear_vel_ = msg.target_linear_vel
    #     self.top_controller_.desired_angular_vel_ = msg.target_angular_vel
    #     self.target_controller_.goal_dist_tol_  = msg.goal_dist_tol
    #     self.get_logger().info("target_vel_callback: {}, {}, {}, {}, {}".format(msg.max_linear_vel, msg.max_angular_vel,msg.target_linear_vel,msg.target_angular_vel,msg.goal_dist_tol))
    def _get_mode_name(self, mode_value):
        if mode_value == TargetPursuitController.OperationMode.FOLLOW_MODE:
            return "Speed Adjustment Follow Mode"
        elif mode_value == TargetPursuitController.OperationMode.TRIP_MODE:
            return "Constant Speed trip Mode"
        elif mode_value == TargetPursuitController.OperationMode.ACCOMPANY_MODE:
            return "Constant Speed ACCOMPANY Mode"
        else:
            return f"Unknown Mode ({mode_value})"

    def mode_callback(self, msg):
        try:
            self.target_controller_.set_current_mode(msg.data)
            mode_name = self._get_mode_name(msg.data)
            self.get_logger().info(f'Controller mode switched to: {mode_name}')
        except Exception as e:
            self.get_logger().error(f'Error handling mode change: {str(e)}')

    def distance_callback(self, msg):
        """处理距离调整命令"""
        try:
            current_dist = self.target_controller_.goal_dist_tol_
            new_dist = self.target_controller_.adjust_distance_tolerance(msg.data)
            action = "靠近" if new_dist < current_dist else "远离" if new_dist > current_dist else "不变"
            self.get_logger().info(
                f'距离容差调整: {action} | '
                f'新值: {new_dist:.2f}m | '
                f'命令: "{msg.data}"')
        except Exception as e:
            self.get_logger().error(f'距离调整错误: {str(e)}')
  
    def follow_end_callback(self, msg):
        self.get_logger().info('follow_start_callback end')
        self.startFlag = False
        self.control_strategy_ = ControlStrategy.Wait
        self.last_cmd_vel_.linear.x = 0.0
        self.last_cmd_vel_.angular.z = 0.0

    def follow_start_callback(self, msg):
        self.get_logger().info("follow_start_callback start: {}".format(msg.data))
        self.startFlag = True
        if msg.data == "startUwb":
            self.only_uwb_data_ = True
        else:
            self.only_uwb_data_ = False



    def cam_info_callback(self, cam_info:CameraInfo):
        self.top_controller_.setCamInfo(cam_info.width, cam_info.height)


    def target_pose_callback(self, msg):
        # transform post to robot frame
        # self.get_logger().info("target_pose: {}, {}".format(msg.pose.position.x, msg.pose.position.y))
        # pose_trans = getTransformPose(base_pose=self.catch_pose_, in_pose=msg)
        self.target_controller_.addPose(pose=msg)
       # if True == self.only_uwb_data_:
        self.control_strategy_ = ControlStrategy.Pursuit
        # self.get_logger().info('target_pose_callback and set self.control_strategy_ = ControlStrategy.Pursuit')

    def target_twist_callback(self, msg):
        # self.get_logger().info("target_twist: {}".format(msg.twist.linear.x))
        self.target_twist_ = msg
        self.last_timestamp = self.get_clock().now()


    # 原代码在此处执行速度发布
    def catch_pose_callback(self, msg):
        twist_stamped = TwistStamped()
        if self.control_strategy_ == ControlStrategy.Wait:
            pass
        elif self.control_strategy_ == ControlStrategy.Search:
            twist_stamped = self.top_controller_.computeVelocityCommands()
            self.publish_vel_cmd(twist=twist_stamped.twist)
        elif self.control_strategy_ == ControlStrategy.Adjust:
            twist_stamped = self.top_controller_.computeAdjustVelocityCommands(pose=self.image_pose_)
            self.publish_vel_cmd(twist=twist_stamped.twist)
        elif (self.control_strategy_ == ControlStrategy.Pursuit):
            twist_stamped,called_rotate = self.target_controller_.computeVelocityCommands(pose=self.catch_pose_, velocity=self.catch_twist_)
            # self.get_logger().info("After Compute Velocity Commands publish linear.x = {}, angular.z={}".format(twist_stamped.twist.linear.x,twist_stamped.twist.angular.z))
            self.publish_vel_cmd(twist=twist_stamped.twist,skip_smoothing=called_rotate)
            self.catch_cmd_publisher_.publish(twist_stamped.twist)
            current_time = self.get_clock().now()
            if (current_time - self.last_log_time).nanoseconds >= 1e9:  
                self.get_logger().info("strategy: {}, linear.x = {}, angular.z={}".format(
                    self.control_strategy_, 
                    twist_stamped.twist.linear.x, 
                    twist_stamped.twist.angular.z))
                self.last_log_time = current_time          
        elif self.control_strategy_ == ControlStrategy.Company:
            # TODO: in the future
            pass
        else:
            pass
        #self.get_logger().info("strategy: {}, linear.x = {}, angular.z={}".format(self.control_strategy_, twist_stamped.twist.linear.x, twist_stamped.twist.angular.z))
    
    def target_image_callback(self, msg):
        self.image_pose_ = msg
        self.top_controller_.addPose(pose=msg)
        ctrl_strategy = self.top_controller_.getControlStrategy()
        self.control_strategy_ = ctrl_strategy
    
    def publish_vel_cmd(self, twist: Twist,skip_smoothing=False):
        vel_cmd = twist
        sign = 1.0 if vel_cmd.angular.z > 0 else -1.0
        vel_cmd.angular.z = sign * abs(vel_cmd.angular.z)
        sign = 1.0 if vel_cmd.linear.x > 0 else -1.0
        vel_cmd.linear.x = sign * abs(vel_cmd.linear.x)
        # self.get_logger().info("skip_smoothing {}".format(skip_smoothing))
        if not skip_smoothing and hasattr(self, 'last_cmd_vel_') and self.last_cmd_vel_ is not None:
            if abs(vel_cmd.linear.x - self.last_cmd_vel_.linear.x) > 0.2:
                vel_cmd.linear.x = self.last_cmd_vel_.linear.x * self.cmd_liner_vel_smooth_ratio_ + \
                                vel_cmd.linear.x * (1 - self.cmd_liner_vel_smooth_ratio_)
            if abs(vel_cmd.angular.z - self.last_cmd_vel_.angular.z) > 0.2:
                vel_cmd.angular.z = self.last_cmd_vel_.angular.z * self.cmd_angular_vel_smooth_ratio_ + \
                                vel_cmd.angular.z * (1 - self.cmd_angular_vel_smooth_ratio_)
                self.get_logger().info("Speed changes too much linear.x = {}, angular.z = {}, last_cmd_vel_.linear.x = {}, last_cmd_vel_.angular.z = {}".format(vel_cmd.linear.x,vel_cmd.angular.z,self.last_cmd_vel_.linear.x,self.last_cmd_vel_.angular.z))
        self.last_cmd_vel_ = vel_cmd
        self.catch_cmd_publisher_.publish(vel_cmd)
        self.catch_ctrl_publisher_.publish(vel_cmd)
        #self.get_logger().info("strategy: {}, linear.x = {}, angular.z={}".format(self.control_strategy_, vel_cmd.linear.x, vel_cmd.angular.z))


    def catch_twist_callback(self, msg):
        # self.get_logger().info("catch_twist: {}".format(msg.twist.linear.x))
        self.catch_twist_ = msg

def main():
    rclpy.init()
    node = PurePursuitControllerNode()
    try:
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass

    rclpy.shutdown()
