import rclpy
from rclpy.node import Node
from geometry_msgs.msg import PoseStamped
from rclpy.time import Time
from nav_msgs.msg import Path
from pyquaternion import Quaternion
import numpy as np
from turtlesim.msg import Pose
from tf_transformations import quaternion_from_euler

import csv
 
class PathCSVReaderNode(Node):

    def __init__(self):
        super().__init__('path_data_reader')
        self.get_logger().info("path_data_reader init")
        self.publisherTarget_ = self.create_publisher(PoseStamped, 'follow_me/path_target_pose', 10)
        self.publisherCatch_ = self.create_publisher(PoseStamped, 'follow_me/path_catch_pose', 10)
        self.timer = self.create_timer(0.02, self.publish_csv_data)
        self.csv_data = self.read_csv('/home/<USER>/code/follow_me_ws/src/follow_strategy/sample/data.csv')
        self.count = 0
        self.pubCatchTrajectory_ = self.create_publisher(Path, 'follow_me/catch_path', 10) 
        self.pubTargetTrajectory_ = self.create_publisher(Path, 'follow_me/target_pose', 10)
        self.catchTrajectoryPath = Path()
        self.targetTrajectoryPath = Path()
        self.catchTrajectoryPath.header.stamp = self.get_clock().now().to_msg()
        self.catchTrajectoryPath.header.frame_id = 'catch'
        self.targetTrajectoryPath.header.stamp = self.get_clock().now().to_msg()
        self.targetTrajectoryPath.header.frame_id = 'target'

    def rpy2quaternion(rad_roll, rad_pitch, rad_yaw):
        # 构建旋转向量
        rotation_vector = [np.sin(rad_roll/2)*np.cos(rad_pitch/2)*np.cos(rad_yaw/2)-np.cos(rad_roll/2)*np.sin(rad_pitch/2)*np.sin(rad_yaw/2),
                        np.cos(rad_roll/2)*np.sin(rad_pitch/2)*np.cos(rad_yaw/2)+np.sin(rad_roll/2)*np.cos(rad_pitch/2)*np.sin(rad_yaw/2),
                        np.cos(rad_roll/2)*np.cos(rad_pitch/2)*np.sin(rad_yaw/2)-np.sin(rad_roll/2)*np.sin(rad_pitch/2)*np.cos(rad_yaw/2)]
        # 创建Quaternion对象并计算四元数
        q = Quaternion([1] + rotation_vector).normalized()
        return q
 
    def read_csv(self, filename):
        data = []
        with open(filename, 'r', newline= '') as csvfile:
            reader = csv.DictReader(csvfile)
            for row in reader:
                data.append(row)
        return data
 
    def publish_csv_data(self):
        if self.count < len(self.csv_data):
            curTime = self.get_clock().now().to_msg()
            msgT = PoseStamped()
            msgT.header.stamp = curTime
            msgT.header.frame_id = 'map'
            msgT.pose.position.x = float(self.csv_data[self.count]['robot X m'])
            msgT.pose.position.y = float(self.csv_data[self.count]['robot Y m'])
            msgT.pose.position.z = 0.0
            msgT.pose.orientation.x = float(self.csv_data[self.count]['q X'])
            msgT.pose.orientation.y = float(self.csv_data[self.count]['q Y'])
            msgT.pose.orientation.z = float(self.csv_data[self.count]['q Z'])
            msgT.pose.orientation.w = float(self.csv_data[self.count]['q W'])

            self.targetTrajectoryPath.poses.append(msgT)
            self.targetTrajectoryPath.header.stamp = curTime
            self.targetTrajectoryPath.header.frame_id = 'map'
            self.pubTargetTrajectory_.publish(self.targetTrajectoryPath)

            msgC = PoseStamped()
            msgC.header.stamp = curTime
            msgC.header.frame_id = 'map'
            msgC.pose.position.x = float(self.csv_data[self.count]['target X m'])
            msgC.pose.position.y = float(self.csv_data[self.count]['target Y m'])
            q = quaternion_from_euler(0, 0, 0)
            msgC.pose.orientation.x = q[0]
            msgC.pose.orientation.y = q[1]
            msgC.pose.orientation.z = q[2]
            msgC.pose.orientation.w = q[3]
            self.catchTrajectoryPath.poses.append(msgC)
            self.catchTrajectoryPath.header.stamp = curTime
            self.catchTrajectoryPath.header.frame_id = 'map'
            self.pubCatchTrajectory_.publish(self.catchTrajectoryPath)

            self.get_logger().info('Publishing T Pose: x=%f, y=%f' % (msgT.pose.position.x, msgT.pose.position.y))
            self.get_logger().info('Publishing C Pose: x=%f, y=%f' % (msgC.pose.position.x, msgC.pose.position.y))
            self.count += 1
        else:
            self.get_logger().info('CSV data published.')
 
def main():
    rclpy.init()
    node = PathCSVReaderNode()
    try:
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    rclpy.shutdown()