#!/usr/bin/env python3
import sys
from nav_msgs.msg import Path
from geometry_msgs.msg import PoseStamped, TransformStamped, Pose
from .geometry_utils import euclidean_distance, is_same_pose
from tf2_geometry_msgs import do_transform_pose_stamped

class PathHandler:
    '''
    Handles input paths to transform them to local frames required
    '''
    def __init__(self, max_robot_pose_search_dist, max_costmap_extent) -> None:
        self.global_path_ = Path() 
        self.last_pose_transformed_ = None
        self.max_robot_pose_search_dist_ = max_robot_pose_search_dist
        self.max_costmap_extent_ = max_costmap_extent
    
    def findTransformStartEnd(self, robot_pose: PoseStamped):
        '''
        Find the start and end pose in global path to transform
        Parameters
        ----------
        robot_pose: PoseStamped
            the robot' pose in frame of global path
        Returns
        ----------
        int
            the index of start pose
        int
            the index of end pose
        '''
        search_dist = 0.0
        min_dist = sys.float_info.max
        start_index = -1
        end_index = 0

        if len(self.global_path_.poses) == 0:
            return (0, 0)
        elif len(self.global_path_.poses) == 1:
            return (0, 1)
        elif len(self.global_path_.poses) == 2:
            return (0, 2)
        
        for index in range(0, len(self.global_path_.poses)-1):
            # Compute distance between each pose and robot pose
            robot_dist = euclidean_distance(self.global_path_.poses[index].pose, robot_pose.pose)
            # Find the nearest pose as start pose
            if robot_dist < min_dist:
                min_dist = robot_dist
                start_index = index
                search_dist = 0.0
            search_dist += euclidean_distance(self.global_path_.poses[index].pose, self.global_path_.poses[index+1].pose)
            # If search distance is larger than the max search distance, just return
            if search_dist > self.max_robot_pose_search_dist_:
                end_index = index + 1
                break
            # If search distance is larger than the max costmap extent, just return
            if search_dist > self.max_costmap_extent_:
                end_index = index + 1
                break

        if end_index == 0:
            end_index = len(self.global_path_.poses)-1
        
        return(start_index, end_index)
    
    def transformGlobalPath(self, robot_pose: PoseStamped):
        '''
        Transform the global path to the robot's frame
        Parameters
        ----------
        robot_pose: PoseStamped
            the robot' pose in its own frame
        Returns
        ----------
        Path
            the transformed path
        '''
        if self.global_path_ == None:
            return None
        # The robot pose and the global path are in same frame
        (start_index, end_index) = self.findTransformStartEnd(robot_pose=robot_pose)
        if start_index == 0 and end_index == 0:
            return None

        # Transform
        # transform = TransformStamped()
        # transform.header = robot_pose.header
        # transform.transform.translation.x = robot_pose.pose.position.x
        # transform.transform.translation.y = robot_pose.pose.position.y
        # transform.transform.translation.z = robot_pose.pose.position.z
        # transform.transform.rotation.x = robot_pose.pose.orientation.x
        # transform.transform.rotation.y = robot_pose.pose.orientation.y
        # transform.transform.rotation.z = robot_pose.pose.orientation.z
        # transform.transform.rotation.w = robot_pose.pose.orientation.w
        # transformed_path = Path()
        # transformed_path.header = self.global_path_.header
        # for index in range(start_index, end_index):
        #     transformed_pose = self.transformPose(self.global_path_.poses[index], transform=transform)
        #     transformed_path.poses.append(transformed_pose)

        # Transform path
        transformed_path = Path()
        transform_pose = self.getTransformPose(pose=robot_pose)
        # Transform last pose
        if len(self.global_path_.poses) > 0:
            self.last_pose_transformed_ = self.transformPose(self.global_path_.poses[-1], transform=transform_pose)
        else:
            self.last_pose_transformed_ = PoseStamped()
        
        for index in range(start_index, end_index):
            transformed_pose = self.transformPose(self.global_path_.poses[index], transform=transform_pose)
            transformed_path.poses.append(transformed_pose)

        # Remove the portion of the global plan that we've already passed so we don't process it on the next iteration (this is called path pruning)
        del self.global_path_.poses[0:start_index]
        return transformed_path
    
    def getTransformPose(self, pose: PoseStamped):
        '''
        Get transform pose according to the input pose.
        Parameters
        ----------
        pose: PoseStamped
            pose input to transform
        Returns
        ----------
        TransformStamped
            the transformed pose
        '''
        transform = TransformStamped()
        transform.header = pose.header
        transform.transform.translation.x = 0.0
        transform.transform.translation.y = 0.0
        transform.transform.translation.z = 0.0
        transform.transform.rotation.x = pose.pose.orientation.x
        transform.transform.rotation.y = pose.pose.orientation.y
        transform.transform.rotation.z = pose.pose.orientation.z
        transform.transform.rotation.w = -pose.pose.orientation.w
        new_pose = PoseStamped()
        new_pose.pose.position.x = -pose.pose.position.x
        new_pose.pose.position.y = -pose.pose.position.y
        new_pose.pose.position.z = -pose.pose.position.z
        new_pose.header = pose.header
        new_pose.pose.orientation = pose.pose.orientation
        out_pose = do_transform_pose_stamped(pose=new_pose, transform=transform)
        transform.transform.translation.x = out_pose.pose.position.x
        transform.transform.translation.y = out_pose.pose.position.y
        transform.transform.translation.z = out_pose.pose.position.z

        return transform
        
    def transformPose(self, in_pose: PoseStamped, transform: TransformStamped):
        '''
        Transform a pose to another frame.
        Parameters
        ----------
        in_pose: PoseStamped
            pose input to transform
        transform: TransformStamped
            reference pose for transformation
        Returns
        ----------
        PoseStamped
            transformed pose
        '''
        out_pose = do_transform_pose_stamped(pose=in_pose, transform=transform)
        # out_pose.point.x = in_pose.point.x - base_pose.point.x
        # out_pose.point.y = in_pose.point.y - base_pose.point.y
        # out_pose.point.z = in_pose.point.z - base_pose.point.z
        return out_pose

    def transformPoseOLD(self, in_pose: PoseStamped, base_pose: PoseStamped):
        '''
        Transform a pose to another frame.
        Parameters
        ----------
        in_pose: PoseStamped
            pose input to transform
        base_pose: PoseStamped
            reference pose for transformation
        Returns
        ----------
        PoseStamped
            transformed pose
        '''
        transform = TransformStamped()
        transform.header = base_pose.header
        transform.transform.translation.x = 0.0
        transform.transform.translation.y = 0.0
        transform.transform.translation.z = 0.0
        transform.transform.rotation.x = base_pose.pose.orientation.x
        transform.transform.rotation.y = base_pose.pose.orientation.y
        transform.transform.rotation.z = base_pose.pose.orientation.z
        transform.transform.rotation.w = -base_pose.pose.orientation.w
        new_pose = PoseStamped()
        new_pose.pose.position.x = in_pose.pose.position.x - base_pose.pose.position.x
        new_pose.pose.position.y = in_pose.pose.position.y - base_pose.pose.position.y
        new_pose.pose.position.z = in_pose.pose.position.z - base_pose.pose.position.z
        new_pose.header = in_pose.header
        new_pose.pose.orientation = in_pose.pose.orientation
        out_pose = do_transform_pose_stamped(pose=new_pose, transform=transform)
        # out_pose.point.x = in_pose.point.x - base_pose.point.x
        # out_pose.point.y = in_pose.point.y - base_pose.point.y
        # out_pose.point.z = in_pose.point.z - base_pose.point.z
        return out_pose

    def addPose(self, pose: PoseStamped):
        '''
        Add new pose to path
        Parameters
        ----------
        pose: PoseStamped
            the pose to add
        '''
        if len(self.global_path_.poses) == 0:
            self.global_path_.poses.append(pose)
        else:
            last_pose = self.global_path_.poses[-1]
            # Don't add pose if it is similar with the last one
            if not is_same_pose(pose1=last_pose.pose, pose2=pose.pose, position_tolerance=0.0001, orientation_tolerance=0.0001):
                self.global_path_.poses.append(pose)
            else:
                # print("Same position and do not add: {}".format(pose))
                pass

    def deletePose(self, max_path_poses: int):
        '''
        Delete the oldest posed when the pose count is larger than max_path_poses.
        Parameters
        ----------
        max_path_poses: int
            maximum poses of a path
        '''
        poses =  len(self.global_path_.poses)
        if poses > max_path_poses:
            del self.global_path_.poses[0:poses-max_path_poses]

    def setPath(self, path: Path):
        '''
        Set path
        Parameters
        ----------
        path: Path
            the input path
        '''
        self.global_path_ = path

    def getPath(self):
        '''
        Return path
        '''
        return self.global_path_
    
    def getLastPose(self):
        '''
        Return last pose
        Returns
        ----------
        Pose
            The last pose
        '''
        # if len(self.global_path_.poses) > 0:
        #     return self.global_path_.poses[-1]
        # else:
        #     return PoseStamped()
        return self.last_pose_transformed_
    
    def calcPathLength(self, start_index: int = 0):
        '''E
        Calculate the length of the path, starting at the provided index
        ParametersEE
        ---------
        start_index: in2
            Optional argument specifying the starting index for the calculation of path length. Provide this if you want to calculate length of a subset of the path.
        Returns
        ----------
        float
            Path length
        '''
        if start_index >= len(self.global_path_.poses):
            return 0.0
        else:
            path_length = 0.0
        for index in range(start_index, len(self.global_path_.poses)-1):
            path_length += euclidean_distance(self.global_path_.poses[index].pose, self.global_path_.poses[index + 1].pose)
        return path_length