#!/usr/bin/env python3
import math
import numpy as np
from numpy import double
from geometry_msgs.msg import Pose, Point, Quaternion, PoseStamped, TransformStamped
from tf_transformations import quaternion_from_euler, euler_from_quaternion


from nav_msgs.msg import Path
from tf2_geometry_msgs import do_transform_pose_stamped

def euclidean_distance(pos1: Pose, pos2: Pose):
    '''
    Get the L2 distance between 2 geometry_msgs::Pose
    Parameters
    ----------
    pos1: Pose
        First pose
    pos2: Pose
        Second pose
    Returns
    ----------
    float
        L2 distance
    '''
    dx = pos1.position.x - pos2.position.x
    dy = pos1.position.y - pos2.position.y
    dz = pos1.position.z - pos2.position.z
    return math.hypot(dx, dy, dz)

def clamp(x: float, minValue: float, maxValue: float):
    '''
    Clamp input value x in [min, max]
    Parameters
    ----------
    x: float
        The input value to clamp
    minValue: float
        The minimal value
    maxValue: float
        The maximum value
    Returns
    ----------
    float
        clamped value
    '''
    if x < minValue:
        return minValue
    elif x > maxValue:
        return maxValue
    else:
        return x

def calculate_curvature(lookahead_point: Point):
    '''
    Calculate curvature at lookahead point
    Parameters
    ----------
    lookahead_point: Point
        The lookahead point in path
    Returns
    ----------
    float
        curvature value
    '''

    # Find distance^2 to look ahead point (carrot) in robot base frame
    # This is the chord length of the circle
    carrot_dist2 = (lookahead_point.x * lookahead_point.x) + (lookahead_point.y * lookahead_point.y)

    # Find curvature of circle (k = 1 / R)
    if carrot_dist2 > 0.001:
        return 2.0 * lookahead_point.y / carrot_dist2
    else:
        return 0.0

def orientation_around_zaxis(angle: float):
    '''
    Get a geometry_msgs Quaternion from a yaw angle
    Parameter
    ----------
    angle: float
        Yaw angle to generate a quaternion
    Returns
    ----------
    Quaternion
        geometry_msgs Quaternion
    '''
    q = Quaternion()
    (q.x, q.y, q.z, q.w) = quaternion_from_euler(0, 0, angle)
    return q

def normalize_angle(angle: float):
    '''
    Normalizes the angle to be -pi circle to +pi circle. It takes and returns radians.
    Parameters
    ----------
    angle: float
        angle to normalize
    Returns
    ----------
    float
        normalized angle
    '''
    theta = math.fmod(angle + math.pi, 2.0 * math.pi)
    return theta + math.pi if theta <= 0.0 else theta - math.pi

def shortest_angular_distance(start: float, end: float):
    '''
    Compute the shortest angle distance in -pi to +pi. It takes and returns radians.
    Parameters
    ----------
    start: float
        start angle in radian
    end: float
        end angle in radian
    Returns
    ----------
    float
        shortest angle distance
    '''
    return normalize_angle(end - start)

def is_goal_reached(query_pose: Pose, goal_pose: Pose, dist_goal_tolerance: float, yaw_goal_tolerance: float):
    '''
    Check whether reached the goal or not
    Parameters
    ----------
    query_pose: Pose
        current pose to query
    goal_pose: Pose
        goal pose
    Returns
    ----------
    bool
        True for goal reached and False for goal not reached
    '''
    dx = query_pose.position.x - goal_pose.position.x
    dy = query_pose.position.y - goal_pose.position.y
    # First check distance, if bigger than the tolerance just return False
    if math.sqrt(dx * dx + dy * dy) > dist_goal_tolerance:
        return False
    # Then check angle,
    else:
        (query_roll, query_pitch, query_yaw) = euler_from_quaternion(query_pose.orientation)
        (goal_roll, goal_pitch, goal_yaw) = euler_from_quaternion(query_pose.orientation)
        yaw_diff = shortest_angular_distance(query_yaw, goal_yaw)
        return abs(yaw_diff) < yaw_goal_tolerance

def get_yaw_angle(orientation: Quaternion):
    '''
    Check whether reached the goal or not
    Parameters
    ----------
    orientation: Quaternion
        the orientation of a pose
    Returns
    ----------
    float
        the yaw angle in radian
    '''
    (query_roll, query_pitch, query_yaw) = euler_from_quaternion([orientation.x, orientation.y, orientation.z, orientation.w])
    return query_yaw

def is_same_pose(pose1: Pose, pose2: Pose, position_tolerance: float, orientation_tolerance: float):
    '''
    Check whether the two input pose are same under the tolerance
    Parameters
    ----------
    pose1: Pose
        the input pose one
    pose2: Pose
        the input pose two
    position_tolerance: float
        the position tolerance of two pose in Point(x, y, z)
    orientation_tolerance: float
        the orientation tolerance of two pose in Quaternion(x, y, z, w)
    Returns
    ----------
    bool
        True for same pose and False for not same
    '''
    result = False
    if abs(pose1.position.x - pose2.position.x) < position_tolerance and \
        abs(pose1.position.y - pose2.position.y) < position_tolerance and \
        abs(pose1.position.z - pose2.position.z) < position_tolerance and \
        abs(pose1.orientation.x - pose2.orientation.x) < orientation_tolerance and \
        abs(pose1.orientation.y - pose2.orientation.y) < orientation_tolerance and \
        abs(pose1.orientation.z - pose2.orientation.z) < orientation_tolerance and \
        abs(pose1.orientation.w - pose2.orientation.w) < orientation_tolerance:
        result = True
    
    return result

def mock_path(data: np.array):
    '''
    Mock path using the input data
    Parameters
    ----------
    data: np.array
        the input path data
    Returns
    ----------
    Path
        The mocked path
    '''
    path = Path()
    for index in range(0, data.shape[0]):
        pose = PoseStamped()
        pose.pose.position.x = data[index, 0]
        pose.pose.position.y = data[index, 1]
        pose.pose.position.z = data[index, 2]
        path.poses.append(pose)
    return path

def getTransformPose(base_pose: PoseStamped, in_pose: PoseStamped):
    '''
    Get transform pose according to the input pose.
    Parameters
    ----------
    pose: PoseStamped
        pose input to transform
    Returns
    ----------
    TransformStamped
        the transformed pose
    '''
    transform = TransformStamped()
    transform.header = base_pose.header
    transform.transform.translation.x = 0.0
    transform.transform.translation.y = 0.0
    transform.transform.translation.z = 0.0
    transform.transform.rotation.x = base_pose.pose.orientation.x
    transform.transform.rotation.y = base_pose.pose.orientation.y
    transform.transform.rotation.z = base_pose.pose.orientation.z
    transform.transform.rotation.w = -base_pose.pose.orientation.w
    new_pose = PoseStamped()
    new_pose.pose.position.x = -base_pose.pose.position.x
    new_pose.pose.position.y = -base_pose.pose.position.y
    new_pose.pose.position.z = -base_pose.pose.position.z
    new_pose.header = base_pose.header
    new_pose.pose.orientation = base_pose.pose.orientation
    out_pose = do_transform_pose_stamped(pose=new_pose, transform=transform)
    transform.transform.translation.x = out_pose.pose.position.x
    transform.transform.translation.y = out_pose.pose.position.y
    transform.transform.translation.z = out_pose.pose.position.z

    out_pose = do_transform_pose_stamped(pose=in_pose, transform=transform)
    return out_pose


def getLinerVecAndAngelVec(startPose: PoseStamped, endPose: PoseStamped):
    linear_vel = 0.0
    angular_vel = 0.0
    startTimestamp = double(startPose.header.stamp.sec) + double(startPose.header.stamp.nanosec) / 1e9
    endTimestamp = double(endPose.header.stamp.sec) + double(endPose.header.stamp.nanosec) / 1e9
    dt_ = endTimestamp - startTimestamp
    dt_ = dt_ if dt_  > 0.001 else 0.001 
    startYaw = get_yaw_angle(startPose.pose.orientation)
    endYaw = get_yaw_angle(endPose.pose.orientation)
    print(dt_)
    angular_vel = (endYaw - startYaw) / dt_
    linear_vel = euclidean_distance(startPose.pose, endPose.pose) / dt_
    print("getLinerVecAndAngelVec linear_vel={},angular_vel={}, startYaw={}, endYaw={}".format(linear_vel, angular_vel, startYaw, endYaw))
    return linear_vel, angular_vel
