#! /usr/bin/env python3
import dis
import sys
import math
import numpy as np
from geometry_msgs.msg import TwistStamped, Twist, PoseStamped, Point, Pose, Quaternion
from nav_msgs.msg import Path
from .path_handler import <PERSON><PERSON><PERSON><PERSON>
from .geometry_utils import clamp, calculate_curvature, get_yaw_angle, is_goal_reached, mock_path, shortest_angular_distance, is_same_pose
from .control_strategy_enum import ControlStrategy

class TopController:
    def __init__(self) -> None:
        '''
        Initializations. Default length unit is m.
        '''
        self.image_width_ = 640
        self.image_height_ = 480
        self.desired_linear_vel_ = 0.0
        self.desired_angular_vel_ = 0.5
        self.max_linear_vel_ = 1.0
        self.max_angular_vel_ = 2.0
        self.valid_frame_threshold_ = 5
        self.total_frame_threshold_ = 100
        self.last_valid_pose_ = None
        self.image_path_ = Path()
    
    def setCamInfo(self, width,  height):
        self.image_width_ = width
        self.image_height_ = height


    def getControlStrategy(self):
        '''
        Get control strategy according to the detection result in image frame.
        Parameters
        ----------
        Returns
        ----------
        ControlStrategy
            the control strategy
        '''
        frames = len(self.image_path_.poses)
        # If frames is less than valid_frame_threshold_, then return Wait
        if frames < self.valid_frame_threshold_:
            return ControlStrategy.Wait
        # If frames is more than valid_frame_threshold_, then return other strategy
        else:
            poses = self.image_path_.poses[-self.valid_frame_threshold_:]
            # First, check whether the target is lost
            is_target_lost = True
            for pose in poses:
                # x == -1.0 and y == -1.0 if target is lost, otherwise x > 0.0 and y > 0.0.
                if pose.pose.position.x > 0.0 and pose.pose.position.y > 0.0:
                    is_target_lost = False
                    break
            if is_target_lost == True:
                return ControlStrategy.Search
            else:
                return ControlStrategy.Pursuit


    def computeVelocityCommands(self):
        '''
        Compute the best command given in ControlStrategy.Search
        Parameters
        ----------
        Returns
        ----------
        TwistStamped
            Best command
        '''

        # populate and return message
        cmd_vel = TwistStamped()
        cmd_vel.twist.linear.x = self.desired_linear_vel_
        sign = 1.0 # default value
        if self.last_valid_pose_ != None and self.last_valid_pose_.pose.position.x > self.image_width_ / 2:
            sign = -1.0
        cmd_vel.twist.angular.z = sign * self.desired_angular_vel_
        return cmd_vel
    
    def addPose(self, pose: PoseStamped):
        '''
        Add new pose to path
        Parameters
        ----------
        pose: PoseStamped
            the pose to add
        '''
        self.image_path_.poses.append(pose)
        if pose.pose.position.x > 0.0 and pose.pose.position.y > 0.0:
            self.last_valid_pose_ = pose
        frames = len(self.image_path_.poses)
        if frames > self.total_frame_threshold_:
            del self.image_path_.poses[0]
#! /usr/bin/env python3
import dis
import sys
import math
import numpy as np
from geometry_msgs.msg import TwistStamped, Twist, PoseStamped, Point, Pose, Quaternion
from nav_msgs.msg import Path
from .path_handler import PathHandler
from .geometry_utils import clamp, calculate_curvature, get_yaw_angle, is_goal_reached, mock_path, shortest_angular_distance, is_same_pose
from .control_strategy_enum import ControlStrategy

class TopController:
    def __init__(self) -> None:
        '''
        Initializations. Default length unit is m.
        '''
        self.image_width_ = 300
        self.image_height_ = 300
        self.desired_linear_vel_ = 0.0
        self.desired_angular_vel_ = 0.8
        self.max_linear_vel_ = 1.0
        self.max_angular_vel_ = 2.0
        self.valid_frame_threshold_ = 5
        self.total_frame_threshold_ = 100
        self.last_valid_pose_ = None
        self.image_path_ = Path()


    def setCamInfo(self, width,  height):
        self.image_width_ = width
        self.image_height_ = height


    def getControlStrategy(self):
        '''
        Get control strategy according to the detection result in image frame.
        Parameters
        ----------
        Returns
        ----------
        ControlStrategy
            the control strategy
        '''
        frames = len(self.image_path_.poses)
        # If frames is less than valid_frame_threshold_, then return Wait
        if frames < self.valid_frame_threshold_:
            return ControlStrategy.Wait
        # If frames is more than valid_frame_threshold_, then return other strategy
        else:
            poses = self.image_path_.poses[-self.valid_frame_threshold_:]
            # First, check whether the target is lost
            is_target_lost = True
            for pose in poses:
                # x == -1.0 and y == -1.0 if target is lost, otherwise x > 0.0 and y > 0.0.
                if pose.pose.position.x > 0.0 and pose.pose.position.y > 0.0:
                    is_target_lost = False
                    break
            if is_target_lost == True:
                return ControlStrategy.Search
            else:
                return ControlStrategy.Pursuit


    def computeVelocityCommands(self):
        '''
        Compute the best command given in ControlStrategy.Search
        Parameters
        ----------
        Returns
        ----------
        TwistStamped
            Best command
        '''

        # populate and return message
        cmd_vel = TwistStamped()
        cmd_vel.twist.linear.x = self.desired_linear_vel_
        sign = 1.0 # default value
        if self.last_valid_pose_ != None and self.last_valid_pose_.pose.position.x > self.image_width_ / 2:
            sign = -1.0
        cmd_vel.twist.angular.z = sign * self.desired_angular_vel_
        return cmd_vel
    
    def addPose(self, pose: PoseStamped):
        '''
        Add new pose to path
        Parameters
        ----------
        pose: PoseStamped
            the pose to add
        '''
        self.image_path_.poses.append(pose)
        if pose.pose.position.x > 0.0 and pose.pose.position.y > 0.0:
            self.last_valid_pose_ = pose
        frames = len(self.image_path_.poses)
        if frames > self.total_frame_threshold_:
            del self.image_path_.poses[0]
#! /usr/bin/env python3
import dis
import sys
import math
import numpy as np
from geometry_msgs.msg import TwistStamped, Twist, PoseStamped, Point, Pose, Quaternion
from nav_msgs.msg import Path
from .path_handler import PathHandler
from .geometry_utils import clamp, calculate_curvature, get_yaw_angle, is_goal_reached, mock_path, shortest_angular_distance, is_same_pose
from .control_strategy_enum import ControlStrategy

class TopController:
    def __init__(self) -> None:
        '''
        Initializations. Default length unit is m.
        '''
        self.image_width_ = 300
        self.image_height_ = 300
        self.image_partition_threshold_ = 40
        self.camera_fov_h_	= 69.4/180*math.pi #horizontal
        self.camera_fov_v_	= 42.5/180*math.pi #vertical
        self.camera_fov_d_	= 77/180*math.pi #diagonal 
        self.desired_linear_vel_ = 0.0
        self.desired_angular_vel_ = 0.6
        self.max_linear_vel_ = 0.5
        self.max_angular_vel_ = 1.0
        self.min_angular_vel_ = 0.05
        self.valid_frame_threshold_ = 5
        self.total_frame_threshold_ = 100
        self.vel_smooth_ratio_ = 0.5
        self.last_valid_pose_ = None
        self.last_angular_vel_ = None
        self.last_top_strategy_ = ControlStrategy.Wait
        self.image_path_ = Path()

    def setCamInfo(self, width,  height):
        self.image_width_ = width
        self.image_height_ = height


    def getControlStrategy(self):
        '''
        Get control strategy according to the detection result in image frame.
        Parameters
        ----------
        Returns
        ----------
        ControlStrategy
            the control strategy
        '''
        frames = len(self.image_path_.poses)
        # If frames is less than valid_frame_threshold_, then return Wait
        if frames < self.valid_frame_threshold_:
            self.last_top_strategy_ = ControlStrategy.Wait
            return ControlStrategy.Wait
        # If frames is more than valid_frame_threshold_, then return other strategy
        else:
            poses = self.image_path_.poses[-self.valid_frame_threshold_:]
            # First, check whether the target is lost
            is_target_lost = True
            for pose in poses:
                # x == -1.0 and y == -1.0 if target is lost, otherwise x > 0.0 and y > 0.0.
                if pose.pose.position.x > 0.0 and pose.pose.position.y > 0.0:
                    is_target_lost = False
                    break
            if is_target_lost == True:
                self.last_top_strategy_ = ControlStrategy.Search
                return ControlStrategy.Search
            else:
                if self.last_top_strategy_ == ControlStrategy.Search or self.last_top_strategy_ == ControlStrategy.Adjust:
                    # Check whether need to adjust
                    is_need_adjust = self.isNeedAdjust(self.image_path_.poses[-1])
                    if is_need_adjust == True:
                        self.last_top_strategy_ = ControlStrategy.Adjust
                        return ControlStrategy.Adjust
                    else:
                        self.last_top_strategy_ = ControlStrategy.Pursuit
                        return ControlStrategy.Pursuit
                else:
                    self.last_top_strategy_ = ControlStrategy.Pursuit
                    return ControlStrategy.Pursuit


    def computeVelocityCommands(self):
        '''
        Compute the best command given in ControlStrategy.Search
        Parameters
        ----------
        Returns
        ----------
        TwistStamped
            Best command
        '''

        # populate and return message
        cmd_vel = TwistStamped()
        cmd_vel.twist.linear.x = self.desired_linear_vel_
        sign = 1.0 # default value
        #if self.last_valid_pose_ != None and abs(self.last_valid_pose_.pose.position.x -self.image_width_ / 2 ) < self.image_partition_threshold_:
        #    sign = 0.0
        if self.last_valid_pose_ != None and self.last_valid_pose_.pose.position.x > self.image_width_ / 2:
            sign = -1.0
        cmd_vel.twist.angular.z = -sign * self.desired_angular_vel_
        return cmd_vel
    
    def addPose(self, pose: PoseStamped):
        '''
        Add new pose to path
        Parameters
        ----------
        pose: PoseStamped
            the pose to add
        '''
        self.image_path_.poses.append(pose)
        if pose.pose.position.x > 0.0 and pose.pose.position.y > 0.0:
            self.last_valid_pose_ = pose
        frames = len(self.image_path_.poses)
        if frames > self.total_frame_threshold_:
            del self.image_path_.poses[0]

    def isNeedAdjust(self, pose: PoseStamped):
        '''
        If the target pose is in the middle, then don't need to adjust. Otherwise, adjust to middle.
        Parameters
        ----------
        pose: PoseStamped
            the pose to check whether the target is in the middle.
        Returns
        ----------
        TwistStamped
            Best command
        '''
        is_need_adjust = False
        if pose.pose.position.x < self.image_partition_threshold_ or self.image_width_ - pose.pose.position.x < self.image_partition_threshold_:
            is_need_adjust = True
        return is_need_adjust

    def computeAdjustVelocityCommands(self, pose: PoseStamped):
        '''
        If the target pose is in the middle, then don't need to adjust. Otherwise, adjust to middle.
        Parameters
        ----------
        pose: PoseStamped
            the pose to check whether the target is in the middle.
        Returns
        ----------
        TwistStamped
            Best command
        '''
        cmd_vel = TwistStamped()
        cmd_vel.twist.linear.x = self.desired_linear_vel_
        pixel_dist = self.image_width_ / 2.0 - pose.pose.position.x
        angular_vel = pixel_dist/self.image_width_*self.camera_fov_h_
        sign = math.copysign(1.0, angular_vel)
        if abs(angular_vel) > self.max_angular_vel_:
            angular_vel = sign * self.max_angular_vel_
        # if self.last_angular_vel_ != None:
        #     angular_vel = self.last_angular_vel_ * self.vel_smooth_ratio_ + angular_vel * (1-self.vel_smooth_ratio_)

        elif abs(angular_vel) < self.min_angular_vel_:
            angular_vel = 0.0
        self.last_angular_vel_ = angular_vel
        cmd_vel.twist.angular.z = -angular_vel
        return cmd_vel
