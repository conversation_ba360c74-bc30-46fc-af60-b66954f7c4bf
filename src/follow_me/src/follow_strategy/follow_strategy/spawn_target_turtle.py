#!/usr/bin/env python3
from numpy import uint8
import rclpy
import random
from rclpy.node import Node
from turtlesim.srv import Spawn, SetPen
from functools import partial

class SpawnTargetTurtleNode(Node):
    def __init__(self):
        super().__init__("spawn_target_turtle_node")
        self.spawn_client_ = self.create_client(Spawn,"spawn")
        self.target_set_pen_client_ = self.create_client(SetPen, "target_turtle/set_pen")
        self.catch_set_pen_client_ = self.create_client(SetPen, "catch_turtle/set_pen")
        x = random.uniform(1.0, 10.0)
        y = random.uniform(1.0, 10.0)
        self.call_spawn_turtle_service(x=x, y=y)
        self.call_set_pen_service(set_pen_client=self.target_set_pen_client_, r=0, g=0, b=0, width=0, off=1)
        self.call_set_pen_service(set_pen_client=self.catch_set_pen_client_, r=0, g=0, b=0, width=0, off=1)
        self.get_logger().info("SpawnTargetTurtleNode has been started.")

    def call_spawn_turtle_service(self, x, y):
        # wait for server
        while not self.spawn_client_.wait_for_service(1.0):
            self.get_logger().warn("Waiting for spawn service server to be ready.")
        self.get_logger().info("Spawn service server is ready.")

        # call server
        request = Spawn.Request()
        request.x = x
        request.y = y
        request.theta = 0.0
        request.name = "target_turtle"
        future = self.spawn_client_.call_async(request=request)
        future.add_done_callback(partial(self.spawn_turtle_callback))
    
    def call_set_pen_service(self, set_pen_client, r: uint8, g: uint8, b: uint8, width: uint8, off: uint8):
        # wait for server
        while not set_pen_client.wait_for_service(1.0):
            self.get_logger().warn("Waiting for set pen service server to be ready.")
        self.get_logger().info("Set pen service server is ready.")

        # call server
        request = SetPen.Request()
        request.r = r
        request.g = g
        request.b = b
        request.width = width
        request.off = off
        future = set_pen_client.call_async(request=request)
        future.add_done_callback(partial(self.set_pen_callback))

    def spawn_turtle_callback(self, future):
        try:
            response = future.result()
            self.get_logger().info("Spawn turtle " + str(response.name) + " succeed.")
        except Exception as e:
            self.get_logger().error("Service call failed %r" % (e,))
    
    def set_pen_callback(self, future):
        try:
            response = future.result()
            self.get_logger().info("Set pen succeed.")
        except Exception as e:
            self.get_logger().error("Service call failed %r" % (e,))

def main(args=None):
    rclpy.init(args=args)
    node = SpawnTargetTurtleNode()
    rclpy.spin(node=node)
    rclpy.shutdown()