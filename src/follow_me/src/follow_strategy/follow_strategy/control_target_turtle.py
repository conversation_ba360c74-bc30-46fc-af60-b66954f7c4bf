#!/usr/bin/env python3
import rclpy
from rclpy.node import Node
from sensor_msgs.msg import Joy
from geometry_msgs.msg import Twist

class JoyNode(Node):
    def __init__(self):
        super().__init__("control_target_turtle")
        self.joy_subscriber_ = self.create_subscription(Joy, "joy", self.joy_callback, 10)
        self.turtle_cmd_vel_publisher_ = self.create_publisher(Twist, "target_turtle/cmd_vel", 10)
        self.get_logger().info("Joy node has been started.")
    
    def joy_callback(self, msg):
        # [left:-1/right:1, up:-1/down:1]
        # self.get_logger().info("Joy axes: " + str(msg.axes))
        # [space, left_shift, right_shift]
        # self.get_logger().info("Joy buttons: " + str(msg.buttons))

        cmd_vel = Twist()
        base_vel = 1.0
        if msg.buttons[0] > 0:
            base_vel = 0.5
        elif msg.buttons[1] > 0 or msg.buttons[2] > 0:
            base_vel = 2.0
        cmd_vel.linear.x = -base_vel * msg.axes[1]
        cmd_vel.angular.z = -base_vel * msg.axes[0]
        self.get_logger().info("Turtle command velocity: " + str(cmd_vel))
        self.turtle_cmd_vel_publisher_.publish(msg=cmd_vel)

def main(args=None):
    rclpy.init(args=args)
    node = JoyNode()
    rclpy.spin(node=node)
    rclpy.shutdown()