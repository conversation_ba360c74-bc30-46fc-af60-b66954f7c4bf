import rclpy
from rclpy.node import Node
from geometry_msgs.msg import Twist, PoseStamped,TwistStamped

import csv
import time

class PathDataGetherNode(Node):
    def __init__(self):
        super().__init__('path_data_reader')

        # 订阅话题并设置回调函数
        self.pose_subscription = self.create_subscription(
            PoseStamped,
            '/catch_turtle/posestamped',
            self.pose_callback,
            10
        )
        self.cmd_vel_subscription = self.create_subscription(
            TwistStamped,
            '/catch_turtle/twiststamped',
            self.cmd_vel_callback,
            10
        )
        
        self.target_pose_subscription = self.create_subscription(
            PoseStamped,
            '/target_calibration_req/pose',
            self.target_pose_callback,
            10
        )

        self.target_image_pos_subscription = self.create_subscription(
            PoseStamped,
            'target_calibration/pose',
            self.target_image_pose_callback,
            10
        )

        # 创建CSV文件并准备写入数据
        self.file_name_ = "gether_data_" + time.strftime("%Y-%m-%d %H:%M:%S") + ".csv"
        self.csv_file = open(self.file_name_, 'w', newline='')
        self.csv_writer = csv.writer(self.csv_file)
        self.pose_data=[0,0,0,0,0,0]
        self.cmd_vel_data = [0,0]
        self.target_pose_data = [0,0]
        self.target_image_pose_data = [0,0,0]
        # 写入CSV文件的表头
        self.csv_writer.writerow(['Timestamp', 'robot X m', 'robot Y m', 'q X',
                                  'q Y', 'q Z','q W','robot linear m/s','robot angular rad/s', 'target X m', 'target Y m', 'pixelX', 'pixelY', 'Distacne'])

    def pose_callback(self, msg:PoseStamped):
        self.pose_data = [msg.pose.position.x,msg.pose.position.y,
                          msg.pose.orientation.x,msg.pose.orientation.y,msg.pose.orientation.z,msg.pose.orientation.w]
        
        self.get_logger().info("write")
        timestamp = self.get_clock().now().to_msg().sec
        self.csv_writer.writerow([timestamp] + self.pose_data + self.cmd_vel_data + self.target_pose_data + self.target_image_pose_data)
    

    def cmd_vel_callback(self, msg:TwistStamped):
        self.cmd_vel_data = [msg.twist.linear.x, msg.twist.angular.z]
        # # 获取时间戳
        # timestamp = self.get_clock().now().to_msg().sec

        # # 写入CSV文件
        # self.csv_writer.writerow([timestamp] + self.pose_data + self.cmd_vel_data)
        # print("in1")
        
    def target_pose_callback(self, msg:PoseStamped):
        self.target_pose_data = [msg.pose.position.x,msg.pose.position.y]
        # # 获取时间戳
        # timestamp = self.get_clock().now().to_msg().sec

        # # 写入CSV文件
        # self.csv_writer.writerow([timestamp] + self.pose_data + self.target_pose_data)
        # print("write")

    def target_image_pose_callback(self, msg:PoseStamped):
        self.target_image_pose_data = [msg.pose.position.x,msg.pose.position.y,msg.pose.position.z]
        # # 获取时间戳
        # timestamp = self.get_clock().now().to_msg().sec

        # # 写入CSV文件
        # self.csv_writer.writerow([timestamp] + self.pose_data + self.target_pose_data)
        # print("write")
        

    def __del__(self):
        # 关闭CSV文件
        self.csv_file.close()


def main():
    rclpy.init()
    node = PathDataGetherNode()
    try:
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass

    rclpy.shutdown()
