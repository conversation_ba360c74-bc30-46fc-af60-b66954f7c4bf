#!/usr/bin/env python3
from numpy import double
import rclpy
import math
import numpy as np
from rclpy.node import Node
from geometry_msgs.msg import PoseStamped, TwistStamped
from .kalman_filter import KalmanFilter

class TargetTwistEstimateNode(Node):
    def __init__(self):
        super().__init__('target_twist_estimate')
        # Subscribe to a turtle{1}{2}/pose topic and call handle_turtle_pose callback function on each message
        self.declare_parameter("topic_to_record", "/target_turtle/posestamped")
        topic_to_record = self.get_parameter("topic_to_record").get_parameter_value().string_value
        self.get_logger().info("Topic to record: " + topic_to_record)
        self.pose_subscriber_ = self.create_subscription(PoseStamped, topic_to_record, self.pose_callback, 10)
        self.twist_publisher_ = self.create_publisher(TwistStamped, '/target_turtle/twiststamped/estimate', 10)

        # Init Kalman Filter to estimate twist
        self.timestamp_ = 0.0 # in seconds
        self.dt_ = 1.0/6.25
        self.dt2_ = self.dt_ * self.dt_
        self.F_ = np.array([[1, 0, self.dt_, 0, 0.5*self.dt2_, 0], [0, 1, 0, self.dt_, 0, 0.5*self.dt2_], [0, 0, 1, 0, self.dt_, 0], [0, 0, 0, 1, 0, self.dt_], [0, 0, 0, 0, 1, 0], [0, 0, 0, 0, 0, 1]])
        self.H_ = np.array([[1, 0, 0, 0, 0, 0], [0, 1, 0, 0, 0, 0]])
        self.Q_ = np.eye(self.F_.shape[0]) * 0.1
        self.R_ = np.eye(self.H_.shape[0]) * 0.1
        self.kf_ = KalmanFilter(F = self.F_, H = self.H_, Q = self.Q_, R = self.R_)

        # estimate state
        self.pos_pred_ = np.array([0.0, 0.0])
        self.vel_angle_1_ = 0.0
        self.vel_angle_2_ = 0.0
        self.vel_angle_3_ = 0.0
        self.vel_angle_4_ = 0.0
        self.vel_angle_5_ = 0.0

    def pose_callback(self, msg):
        # self.controller_.addPose(pose=msg)
        # self.get_logger().info("pose: " + str(msg))
        new_timestamp = double(msg.header.stamp.sec) + double(msg.header.stamp.nanosec) / 1e9
        self.dt_ = new_timestamp - self.timestamp_

        # update motion model of kalman filter
        self.dt2_ = self.dt_ * self.dt_
        self.F_ = np.array([[1, 0, self.dt_, 0, 0.5*self.dt2_, 0], [0, 1, 0, self.dt_, 0, 0.5*self.dt2_], [0, 0, 1, 0, self.dt_, 0], [0, 0, 0, 1, 0,     self.dt_], [0, 0, 0, 0, 1, 0], [0, 0, 0, 0, 0, 1]])
        self.kf_.updateModel(self.F_)

        # update kalman filter's state matrix
        pos_observe = np.array([msg.pose.position.x, msg.pose.position.y])
        self.kf_.update(pos_observe)

        # predict new state using kalman filter
        new_pos_pred = np.dot(self.H_,  self.kf_.predict())[0]
        new_pos_pred = new_pos_pred.reshape(1, 2)
        new_vel_estimate = (new_pos_pred - self.pos_pred_) / self.dt_

        # compute twist using new vel
        if abs(new_vel_estimate[0, 0]) < 0.001 and abs(new_vel_estimate[0, 1]) < 0.001:
            new_vel_angle = self.vel_angle_1_
        else:
            new_vel_angle = math.atan2(new_vel_estimate[0, 1], new_vel_estimate[0, 0])
        
        twist_stamped = TwistStamped()
        twist_stamped.header.frame_id = "target_turtle"

        angle_dist = new_vel_angle - self.vel_angle_5_
        if angle_dist > math.pi:
            angle_dist = angle_dist - math.pi * 2
        elif angle_dist < -math.pi:
            angle_dist = angle_dist + math.pi * 2
        twist_stamped.twist.angular.z = angle_dist / self.dt_ / 5
        # self.get_logger().info("vel_angle: {}, angle_new: {}, angle_old:{}".format(twist_stamped.twist.angular.z, new_vel_angle, self.vel_angle_5_))
        twist_stamped.twist.linear.x = math.hypot(new_vel_estimate[0, 0], new_vel_estimate[0, 1])
        self.twist_publisher_.publish(twist_stamped)
        #v self.get_logger().info("vel_linear: {}".format(twist_stamped.twist.linear.x))

        # save states
        self.vel_estimate_ = new_vel_estimate
        self.vel_angle_1_ = new_vel_angle
        self.vel_angle_2_ = self.vel_angle_1_
        self.vel_angle_3_ = self.vel_angle_2_
        self.vel_angle_4_ = self.vel_angle_3_
        self.vel_angle_5_ = self.vel_angle_4_
        self.pos_pred_ = new_pos_pred
        self.timestamp_ = new_timestamp
        # self.get_logger().info("vel: {}, angular.z: {}, linear.x{}, timestamp: {}".format(self.vel_estimate_, twist_stamped.twist.angular.z, twist_stamped.twist.linear.x, new_timestamp))

    

def main():
    rclpy.init()
    node = TargetTwistEstimateNode()
    try:
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass

    rclpy.shutdown()