#!/usr/bin/env python3
from enum import Enum

class ControlStrategy(Enum):
    Wait = 0
    Search = 1
    Pursuit = 2
    Company = 3#!/usr/bin/env python3
from enum import Enum

class ControlStrategy(Enum):
    Wait = 0
    Search = 1
    Pursuit = 2
    Company = 3#!/usr/bin/env python3
from enum import Enum

class ControlStrategy(Enum):
    Wait = 0
    Search = 1
    Adjust = 2
    Pursuit = 3
    Company = 4