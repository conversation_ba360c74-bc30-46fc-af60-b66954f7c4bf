import rclpy
import sys
import os
package_path = '/home/<USER>/ck_code/follow_me_deeprobots/src/follow_strategy'
if package_path not in sys.path:
    sys.path.append(package_path)
package_path_1 = '/home/<USER>/ck_code/follow_me_deeprobots/src/follow_msgs'
if package_path_1 not in sys.path:
    sys.path.append(package_path_1)

from follow_strategy.control_catch_turtle_v1 import PurePursuitControllerNode
from follow_msgs.msg import TargetVelInfo  # 最大线速度等参数信息
def main():
    rclpy.init()
    node = PurePursuitControllerNode()
    try:
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass

    rclpy.shutdown()

if __name__ == '__main__':
    main()