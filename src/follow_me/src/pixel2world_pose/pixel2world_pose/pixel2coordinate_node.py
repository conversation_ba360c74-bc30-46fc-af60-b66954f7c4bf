import rclpy
from rclpy.node import Node
from geometry_msgs.msg import Po<PERSON>,PoseStamped
from sensor_msgs.msg import CameraInfo

from .pose2transform import Pose2Transform
from .pixel2pose import Pixel2Pose

import numpy as np

# 调试打印标准
debug = True

# 订阅话题：像素坐标
sub_pixel_topic_type = PoseStamped
sub_pixel_topic_name = "target_calibration/pose"
sub_pixel_topic_number = 10

# 订阅话题：世界坐标系下机器人的实际位置
sub_robot_pose_topic_type = PoseStamped
sub_robot_pose_topic_name = "/catch_turtle/posestamped"
sub_robot_pose_topic_number = 10

# 订阅话题：cam info
sub_cam_info_topic_type = CameraInfo
sub_cam_info_topic_name = "/follow_me/camera_info"
sub_cam_info_topic_number = 10

# 发布话题：世界坐标系下目标的实际位置
pub_target_world_pose_topic_type = PoseStamped
pub_target_world_pose_topic_name = "target_calibration_req/pose"
pub_target_world_pose_topic_number = 10

# 相机内参
#cam_width = 300
#cam_height = 300
#cam_K = np.array([[455.925, 0.0, 150.000],
#                  [0.0, 455.925, 150.000],
#                  [0.0, 0.0, 1.0]])
#cam_D = [0.0, 0.0, 0.0, 0.0, 0.0]

# 相机坐标转基座坐标
cam2base_transform = np.array([ [ 0.95105652,   0,          0.30901699,         0,       ],
                                [ 0,            1,          0,                  0,       ],
                                [ -0.30901699,   0,         0.95105652,         0,       ],
                                [ 0,            0,          0,                  1        ]])

transform = np.array([[1,0,0,0],
                      [0,1,0,0],
                      [0,0,1,0],
                      [0,0,0,1]])

# 像素坐标转实际坐标类
class PixelToCoordinateNode(Node):
    # 初始化
    def __init__(self, name: str):
        super().__init__(name)
        self.get_logger().info("节点启动：%s " % self.get_name())
        
        # # 创建定时器定时发布目标位置
        # self.timer = self.create_timer(1, self.timer_callback)
        
        # 初始化订阅话题：机器人坐标
        self.subscription = self.create_subscription(
            sub_robot_pose_topic_type,
            sub_robot_pose_topic_name,
            self.robot_pose_callback,
            sub_robot_pose_topic_number)
        
        # 初始化订阅话题：像素坐标
        self.subscription = self.create_subscription(
            sub_pixel_topic_type,
            sub_pixel_topic_name,
            self.pixel_callback,
            sub_pixel_topic_number)

        # 初始化订阅话题：像素坐标
        self.subscription = self.create_subscription(
            sub_cam_info_topic_type,
            sub_cam_info_topic_name,
            self.cam_info_callback,
            sub_cam_info_topic_number)


        # 初始化
        self.robot2world_transform = []
        self.robot_pose_flag = False
        self.pixel_flag = False

        self.coordinate_publisher = self.create_publisher(pub_target_world_pose_topic_type,
                                                          pub_target_world_pose_topic_name,
                                                          pub_target_world_pose_topic_number)
        
        self.cam_info = CameraInfo()
        self.cam_info.width = 300
        self.cam_info.height = 300
        self.cam_info.k = [525.0, 0.0, 150.0, 0.0, 525.0, 150.0, 0.0, 0.0, 1.0]
        self.cam_info.d = [0.0, 0.0, 0.0, 0.0, 0.0]
        self.cam_info.r = [1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0]

        # 像素转实际坐标类
        cam_K = np.reshape(self.cam_info.k, (3, 3)) 
        cam_D = self.cam_info.d
        self._pixel2pose = Pixel2Pose(self.cam_info.width, self.cam_info.height,cam_K,cam_D)

  
    def publish_coordinate(self, base_pose):
        """话题发布实际坐标

        Args:
            base_pose (PoseStamped): 实际坐标
        """
        # 发布实际坐标
        self.coordinate_publisher.publish(base_pose)


    def cam_info_callback(self, cam_info:sub_cam_info_topic_type):
        self.cam_info = cam_info
         # 转换为 3x3 矩阵
        cam_K = np.reshape(cam_info.k, (3, 3)) 
        cam_D = cam_info.d
        self._pixel2pose.set_cam_param(cam_info.width, cam_info.height, cam_K, cam_D)
        print(f"相机内参：{cam_info}")



    def robot_pose_callback(self, robot_pose:sub_robot_pose_topic_type):
        """ 机器人坐标订阅回调函数
            获取机器人坐标

        Args:
            pose (PoseStamped): 机器人坐标
        """
        # 获取机器人坐标
        self.robot_pose = robot_pose.pose
        # 坐标转化为变换矩阵
        p2trans = Pose2Transform(self.robot_pose)
        
        self.robot2world_transform = p2trans.pose2transform()
        self.robot_pose_flag = True
            

    def pixel_callback(self, pixel: sub_pixel_topic_type):
        """ 像素坐标转实际坐标回调函数
            获取像素坐标和深度，转换成实际坐标后发布

        Args:
            pixel (sub_pixel_topic_type): 像素坐标
        """
            
        print(f"像素坐标：{pixel.pose.position}")
        # 像素坐标和深度
        self.pixel_x = pixel.pose.position.x
        self.pixel_y = pixel.pose.position.y
        self.pixel_depth = pixel.pose.position.z
        
        #获取相机坐标系下的实际坐标
        self.cam_pose = self._pixel2pose.GetPixel(self.pixel_x,self.pixel_y,self.pixel_depth)
        
        if debug:
            cpose = self.cam_pose.reshape(-1)[:3]
            print("robot下坐标:{:.2f} {:.2f} {:.2f} ".format(cpose[0],cpose[1],cpose[2]))
            
        # self.get_logger().info(f"定时器")
                 
        # if self.robot_pose_flag != True:
        #     # print("等待机器人坐标数据")
        #     return 
        # else:
        #     self.robot_pose_flag = False   
            # print(f"{self.robot_pose.position}")        
            
        # 基座坐标系下的实际坐标self.robot2world_transform @ 
        self.base_pose = cam2base_transform @ self.cam_pose

        self.pub_pose = pub_target_world_pose_topic_type()
        self.pub_pose.header.frame_id = "robot"
        self.pub_pose.header.stamp = self.get_clock().now().to_msg()
        self.pub_pose.pose.position.x = float(self.base_pose[0])
        self.pub_pose.pose.position.y = float(self.base_pose[1])
        self.pub_pose.pose.position.z = 0.0
        self.pub_pose.pose.orientation.w = 1.0

        # 发布实际坐标
        self.publish_coordinate(self.pub_pose)
            
    # def timer_callback(self):
    #     self.get_logger().info("time\r\n")
    #     # if debug:
    #     #     wpose = self.base_pose.reshape(-1)[:3]
    #     #     # self.get_logger().info("")
    #     #     print(f"机器人坐标：{self.robot_pose.position}")
    #     #     print("world下坐标: {:.2f} {:.2f} {:.2f} ".format(wpose[0],wpose[1],wpose[2]))


def main(args=None):
    rclpy.init(args=args)
    pixel_to_coordinate_node = PixelToCoordinateNode("target_calibration_req")
    rclpy.spin(pixel_to_coordinate_node)
    pixel_to_coordinate_node.destroy_node()
    rclpy.shutdown()


if __name__ == '__main__':
    main()
    
        
