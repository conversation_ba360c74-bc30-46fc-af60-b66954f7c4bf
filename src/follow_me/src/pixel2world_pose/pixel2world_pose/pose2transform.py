import numpy as np
from geometry_msgs.msg import Po<PERSON>
from scipy.spatial.transform import Rotation as R
class Pose2Transform:
    def __init__(self, pose_:Pose):
        self._pose = pose_
            
        
    # 二维位姿转变换矩阵
    def pose2transform(self):
        q = np.array([self._pose.orientation.x, self._pose.orientation.y, self._pose.orientation.z,self._pose.orientation.w])
        
        rx,ry,rz = self.quaternion_to_euler(q)
        r_mat = self.euler_angles_to_rotation_matrix(rx, ry, rz)
        
        t_vec = np.array([self._pose.position.x,self._pose.position.y,0])
        
        return self.rotation_translation_to_homogeneous(r_mat,t_vec)
        

    # 输入:三轴旋转角度(rx, ry, rz),单位弧度
    # 返回：R 旋转矩阵(3X3)
    def euler_angles_to_rotation_matrix(self,rx, ry, rz):
        # 计算旋转矩阵
        Rx = np.array([[1, 0, 0],
                    [0, np.cos(rx), -np.sin(rx)],
                    [0, np.sin(rx), np.cos(rx)]])

        Ry = np.array([[np.cos(ry), 0, np.sin(ry)],
                    [0, 1, 0],
                    [-np.sin(ry), 0, np.cos(ry)]])

        Rz = np.array([[np.cos(rz), -np.sin(rz), 0],
                    [np.sin(rz), np.cos(rz), 0],
                    [0, 0, 1]])

        Rzyx = Rz @ Ry @ Rx  # 先绕 z轴旋转 再绕y轴旋转  最后绕x轴旋转
        return Rzyx

    # 输入：旋转矩阵
    # 返回：四元数
    def quaternion_to_euler(self,q):
        # 将旋转矩阵转换为四元数
        r = R.from_quat(q)
        euler = r.as_euler('xyz',False)
        return euler


    def rotation_translation_to_homogeneous(self,rotation, translation):
        H = np.eye(4)
        H[:3, :3] = rotation
        H[:3, 3] = translation.reshape(-1)

        return H
