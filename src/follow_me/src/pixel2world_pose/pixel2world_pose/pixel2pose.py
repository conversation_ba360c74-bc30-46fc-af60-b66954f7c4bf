import numpy as np

class Pixel2Pose:
    def __init__(self,cam_width:int,cam_height:int,cam_K:None,cam_D:None):
        
        self._cam_width=cam_width
        self._cam_height=cam_height
        self._cam_K=cam_K
        self._cam_D=cam_D
    
    def set_cam_param(self,cam_width,cam_height,cam_K,cam_D):
        """
            设置相机参数：
        """
        self._cam_width=cam_width
        self._cam_height=cam_height
        self._cam_K=cam_K
        self._cam_D=cam_D
    
    def GetPixel(self,u, v,depth):
        # 将像素坐标转换为齐次坐标
        pixel_Vector3= np.array([[u],[v],[1]]).reshape(3,1)

        # 计算相机坐标系下的坐标
        pose_Vector3 = np.linalg.inv(self._cam_K) @ pixel_Vector3
        # 归一化
        sum = 1.0 / np.sqrt(pose_Vector3[0]**2+pose_Vector3[1]**2+pose_Vector3[2]**2)
        # print(f"相机坐标{pose_Vector3} {sum}")
        pose_Vector3 = np.dot(pose_Vector3,sum)
        
        # 将相机坐标系下的坐标转换为三维坐标（包含深度值）
        P = np.dot([ pose_Vector3[2],
                                       -pose_Vector3[0],
                                       -pose_Vector3[1]], depth)
        P = np.append(P,np.array([1])).reshape(4,1)
        return P


    
    
    
    
    