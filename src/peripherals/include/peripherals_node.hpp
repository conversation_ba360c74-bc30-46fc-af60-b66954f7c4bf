#ifndef PERIPHERALS_NODE_HPP
#define PERIPHERALS_NODE_HPP

#include "std_msgs/msg/string.hpp"
#include <filesystem>
#include <jsoncpp/json/json.h>
#include "rclcpp/rclcpp.hpp"
#include "homi_speech_interface/srv/peripherals_ctrl.hpp"
#include "homi_speech_interface/srv/peripherals_status.hpp"
#include "rcl_interfaces/msg/parameter.hpp"
#include "rcl_interfaces/msg/parameter_value.hpp"
#include "rcl_interfaces/msg/parameter_type.hpp"
#include "rcl_interfaces/srv/set_parameters.hpp"
#include <algorithm>
#include <map>
using SetP = rcl_interfaces::srv::SetParameters;

class PeripheralsNode : public rclcpp::Node {
public:
    PeripheralsNode();

private:
    void handle_peripherals_ctrl(
        const std::shared_ptr<homi_speech_interface::srv::PeripheralsCtrl::Request> request,
        std::shared_ptr<homi_speech_interface::srv::PeripheralsCtrl::Response> response);

    void handle_peripherals_status(
        const std::shared_ptr<homi_speech_interface::srv::PeripheralsStatus::Request> request,
        std::shared_ptr<homi_speech_interface::srv::PeripheralsStatus::Response> response);

    void send_peripherals_ctrl_request(std::string command, int key1, int key2);
    void send_peripherals_status_request(std::string command, int key1, int key2);
    void publish_peripherals_monitor_message();
    void peripherals_ctrl_request_callback();
    void peripherals_status_request_callback();
    void handle_peripherals_monitor_message(const std_msgs::msg::String::SharedPtr msg);
    void handle_touch_gpio_state_message(const std_msgs::msg::String::SharedPtr msg);
    void update_pwm_touch_parameters(const std::string color, const int duty_value);
    std::shared_ptr<SetP::Response> call_set_parameters(rcl_interfaces::msg::Parameter &parameter);
    // void send_rgb_control_command(const Json::Value& rgb_config);

    bool last_touch_status_ = false;
    
    // 触摸状态跟踪
    std::map<std::string, bool> gpio_high_level_status_;  // 记录每个GPIO的高电平状态
    std::string last_rising_edge_pin_;  // 记录最后一个上升沿的GPIO引脚
    bool has_active_high_level_ = false;  // 是否有活跃的高电平状态
    int touch_event_count_ = 0;  // 触摸事件发布次数统计

    rclcpp::Service<homi_speech_interface::srv::PeripheralsCtrl>::SharedPtr service_;
    rclcpp::Service<homi_speech_interface::srv::PeripheralsStatus>::SharedPtr status_service_;
    rclcpp::Client<homi_speech_interface::srv::PeripheralsCtrl>::SharedPtr peripherals_ctrl_client_;
    rclcpp::Client<homi_speech_interface::srv::PeripheralsStatus>::SharedPtr peripherals_status_client_;
    rclcpp::Publisher<std_msgs::msg::String>::SharedPtr peripherals_monitor_pub_;
    rclcpp::Subscription<std_msgs::msg::String>::SharedPtr peripherals_monitor_sub_;
    rclcpp::Subscription<std_msgs::msg::String>::SharedPtr gpio_state_sub_;
    // rclcpp::Client<std_srvs::srv::SetBool>::SharedPtr rgb_control_client_;
    rclcpp::Client<rcl_interfaces::srv::SetParameters>::SharedPtr pwm_touch_param_client_;
    rclcpp::TimerBase::SharedPtr timer_;
    rclcpp::TimerBase::SharedPtr status_timer_;
    rclcpp::TimerBase::SharedPtr monitor_timer_;
    rclcpp::TimerBase::SharedPtr rgb_timer_;
    rclcpp::TimerBase::SharedPtr pwm_touch_param_timer_;
};

#endif
