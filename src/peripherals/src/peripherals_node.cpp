#include "peripherals_node.hpp"

PeripheralsNode::PeripheralsNode() : Node("peripherals_node") {
    RCLCPP_INFO(this->get_logger(), "PeripheralsNode has been started.");

    service_ = this->create_service<homi_speech_interface::srv::PeripheralsCtrl>(
        "robdog_control/peripherals_ctrl",
        std::bind(&PeripheralsNode::handle_peripherals_ctrl, this, std::placeholders::_1, std::placeholders::_2)
    );

    peripherals_monitor_pub_ = this->create_publisher<std_msgs::msg::String>(
        "/robdog_control/peripherals_monitor", 10
    );

    gpio_state_sub_ = this->create_subscription<std_msgs::msg::String>(
        "touch_status", 10,
        std::bind(&PeripheralsNode::handle_touch_gpio_state_message, this, std::placeholders::_1)
    );

    pwm_touch_param_client_ = this->create_client<SetP>("/pwm_touch_node/set_parameters");
}

void PeripheralsNode::handle_peripherals_ctrl(
    const std::shared_ptr<homi_speech_interface::srv::PeripheralsCtrl::Request> request,
    std::shared_ptr<homi_speech_interface::srv::PeripheralsCtrl::Response> response) {
    Json::CharReaderBuilder reader;
    Json::Value root;
    std::string errors;

    std::istringstream stream(request->data);
    if (Json::parseFromStream(reader, stream, &root, &errors)) {
        response->error_code = true;
        response->result = root.toStyledString().c_str();
        RCLCPP_INFO(get_logger(), "收到外设控制请求的原始数据: %s", response->result.c_str());

        if (root.isMember("command")) {
            std::string command = root["command"].asString();
            RCLCPP_INFO(get_logger(), "处理外设控制命令: %s", command.c_str());
            if (command == "set_led_mode") {
                if (!root.isMember("mode")) {
                    RCLCPP_ERROR(get_logger(), "设置LED模式失败：缺少mode参数");
                    response->error_code = false;
                    response->result = R"({"status": "error", "message": "missing mode parameter"})";
                    return;
                }

                auto request = std::make_shared<SetP::Request>();
                std::string mode = root["mode"].asString();
                RCLCPP_INFO(get_logger(), "设置LED模式: %s", mode.c_str());

                // 添加模式参数
                auto mode_param = rcl_interfaces::msg::Parameter();
                mode_param.name = "mode";
                auto mode_value = rcl_interfaces::msg::ParameterValue();
                mode_value.type = rcl_interfaces::msg::ParameterType::PARAMETER_STRING;
                mode_value.string_value = mode;
                mode_param.value = mode_value;


                // 如果有颜色参数，添加颜色参数
                if (root.isMember("color")) {
                    std::string color = root["color"].asString();
                    RCLCPP_DEBUG(get_logger(), "设置LED颜色: %s", color.c_str());
                    
                    auto color_param = rcl_interfaces::msg::Parameter();
                    color_param.name = "color";
                    auto color_value = rcl_interfaces::msg::ParameterValue();
                    color_value.type = rcl_interfaces::msg::ParameterType::PARAMETER_STRING;
                    color_value.string_value = color;
                    color_param.value = color_value;
                    request->parameters.push_back(color_param);
                }
                
                // 如果有次要颜色参数（用于渐变等效果）
                if (root.isMember("secondary_color")) {
                    std::string sec_color = root["secondary_color"].asString();
                    RCLCPP_DEBUG(get_logger(), "设置LED次要颜色: %s", sec_color.c_str());
                    
                    auto sec_color_param = rcl_interfaces::msg::Parameter();
                    sec_color_param.name = "secondary_color";
                    auto sec_color_value = rcl_interfaces::msg::ParameterValue();
                    sec_color_value.type = rcl_interfaces::msg::ParameterType::PARAMETER_STRING;
                    sec_color_value.string_value = sec_color;
                    sec_color_param.value = sec_color_value;
                    request->parameters.push_back(sec_color_param);
                }
                
                // 如果有效果速度参数
                if (root.isMember("effect_speed")) {
                    double speed = root["effect_speed"].asDouble();
                    RCLCPP_DEBUG(get_logger(), "设置LED效果速度: %.2f", speed);
                    
                    auto speed_param = rcl_interfaces::msg::Parameter();
                    speed_param.name = "effect_speed";
                    auto speed_value = rcl_interfaces::msg::ParameterValue();
                    speed_value.type = rcl_interfaces::msg::ParameterType::PARAMETER_DOUBLE;
                    speed_value.double_value = speed;
                    speed_param.value = speed_value;
                    request->parameters.push_back(speed_param);
                }
                
                // 如果有亮度参数
                if (root.isMember("brightness")) {
                    double brightness = root["brightness"].asDouble();
                    RCLCPP_DEBUG(get_logger(), "设置LED亮度: %.2f", brightness);
                    
                    auto brightness_param = rcl_interfaces::msg::Parameter();
                    brightness_param.name = "brightness";
                    auto brightness_value = rcl_interfaces::msg::ParameterValue();
                    brightness_value.type = rcl_interfaces::msg::ParameterType::PARAMETER_DOUBLE;
                    brightness_value.double_value = brightness;
                    brightness_param.value = brightness_value;
                    request->parameters.push_back(brightness_param);
                }
                
                request->parameters.push_back(mode_param);
                // 等待服务可用
                while (!pwm_touch_param_client_->wait_for_service(std::chrono::seconds(1))) {
                    if (!rclcpp::ok()) {
                        RCLCPP_ERROR(get_logger(), "等待服务时被中断");
                        response->error_code = false;
                        response->result = R"({"status": "error", "message": "service interrupted"})";
                        return;
                    }
                    RCLCPP_INFO(get_logger(), "等待参数设置服务端上线中");
                }

                // 发送参数更新请求
                pwm_touch_param_client_->async_send_request(request, 
                    [this](rclcpp::Client<SetP>::SharedFuture future) {
                        try {
                            auto result = future.get();
                            bool all_success = true;
                            std::string error_msg;
                            
                            for (const auto& param_result : result->results) {
                                if (!param_result.successful) {
                                    all_success = false;
                                    error_msg += param_result.reason + "; ";
                                }
                            }
                            
                            if (all_success) {
                                RCLCPP_INFO(get_logger(), "LED模式和参数更新成功");
                            } else {
                                RCLCPP_ERROR(get_logger(), "LED参数更新失败: %s", error_msg.c_str());
                            }
                        } catch (const std::exception& e) {
                            RCLCPP_ERROR(get_logger(), "LED参数更新异常: %s", e.what());
                        }
                    });
            } else if (command == "set_fan_speed") {
                if (!root.isMember("speed")) {
                    RCLCPP_ERROR(get_logger(), "设置风扇转速失败：缺少speed参数");
                    response->error_code = false;
                    response->result = R"({"status": "error", "message": "missing speed parameter"})";
                    return;
                }

                auto request = std::make_shared<SetP::Request>();
                int speed = root["speed"].asInt();
                RCLCPP_INFO(get_logger(), "设置风扇转速: %d", speed);

                // 添加模式参数
                auto fan_speed_param = rcl_interfaces::msg::Parameter();
                fan_speed_param.name = "fan_speed";
                auto fan_speed_value = rcl_interfaces::msg::ParameterValue();
                fan_speed_value.type = rcl_interfaces::msg::ParameterType::PARAMETER_INTEGER;
                fan_speed_value.integer_value = speed;
                fan_speed_param.value = fan_speed_value;

                request->parameters.push_back(fan_speed_param);
                // 等待服务可用
                while (!pwm_touch_param_client_->wait_for_service(std::chrono::seconds(1))) {
                    if (!rclcpp::ok()) {
                        RCLCPP_ERROR(get_logger(), "等待服务时被中断");
                        response->error_code = false;
                        response->result = R"({"status": "error", "message": "service interrupted"})";
                        return;
                    }
                    RCLCPP_INFO(get_logger(), "等待参数设置服务端上线中");
                }

                // 发送参数更新请求
                pwm_touch_param_client_->async_send_request(request,
                    [this](rclcpp::Client<SetP>::SharedFuture future) {
                        try {
                            auto result = future.get();
                            bool all_success = true;
                            std::string error_msg;

                            for (const auto& param_result : result->results) {
                                if (!param_result.successful) {
                                    all_success = false;
                                    error_msg += param_result.reason + "; ";
                                }
                            }

                            if (all_success) {
                                RCLCPP_INFO(get_logger(), "风扇转速更新成功");
                            } else {
                                RCLCPP_ERROR(get_logger(), "风扇转速更新失败: %s", error_msg.c_str());
                            }
                        } catch (const std::exception& e) {
                            RCLCPP_ERROR(get_logger(), "风扇转速更新异常: %s", e.what());
                        }
                    });
            } else {
                RCLCPP_WARN(get_logger(), "收到不支持的命令: %s", command.c_str());
                response->error_code = false;
                response->result = R"({"status": "error", "message": "unsupported command"})";
                return;
            }
        } else {
            RCLCPP_ERROR(get_logger(), "请求数据缺少command字段");
            response->error_code = false;
            response->result = R"({"status": "error", "message": "missing command field"})";
            return;
        }
    } else {
        RCLCPP_ERROR(get_logger(), "JSON解析失败: %s", errors.c_str());
        response->error_code = false;
        response->result = R"({"status": "error", "message": "json parse error"})";
    }
}

void PeripheralsNode::handle_touch_gpio_state_message(const std_msgs::msg::String::SharedPtr msg) 
{
    const std::string& raw_msg = msg->data;
    RCLCPP_DEBUG(this->get_logger(), "接收到触摸数据: %s", raw_msg.c_str());
    
    // 解析JSON格式数据
    Json::CharReaderBuilder reader;
    Json::Value root;
    std::string errors;
    
    std::istringstream stream(raw_msg);
    if (Json::parseFromStream(reader, stream, &root, &errors)) {
        // 检查是否包含必要字段
        if (!root.isMember("command") || !root.isMember("data")) {
            RCLCPP_ERROR(this->get_logger(), "触摸数据缺少必要字段(command/data)");
            return;
        }
        
        std::string command = root["command"].asString();
        

        
        // 只处理上升沿事件进行转发
        if (command == "touch_rising_edge") {
            // 获取触发上升沿的GPIO引脚
            if (!root.isMember("rising_edge_pin")) {
                RCLCPP_ERROR(this->get_logger(), "上升沿事件缺少rising_edge_pin字段");
                return;
            }
            std::string rising_pin = root["rising_edge_pin"].asString();
            
            // 检查是否有其他GPIO处于高电平状态
            bool has_other_high = false;
            std::string other_high_pin;
            for (const auto& [pin, is_high] : gpio_high_level_status_) {
                if (is_high && pin != rising_pin) {
                    has_other_high = true;
                    other_high_pin = pin;
                    break;
                }
            }
            
            // 如果有其他GPIO处于高电平状态，则忽略此事件
            if (has_other_high) {
                RCLCPP_INFO(this->get_logger(), "忽略GPIO %s 的上升沿事件，因为已有其他GPIO处于高电平状态 (GPIO %s) [当前已发布%d次]", 
                           rising_pin.c_str(), other_high_pin.c_str(), touch_event_count_);
                return;
            }
            
            // 更新最后上升沿引脚
            if (gpio_high_level_status_[rising_pin]) {
                last_rising_edge_pin_ = rising_pin;
                RCLCPP_INFO(this->get_logger(), "设置活跃高电平状态，GPIO: %s", rising_pin.c_str());
            }
            
            // 创建转发消息
            Json::Value forward_msg;
            forward_msg["command"] = "touch_event";
            
            // 复制触摸数据
            forward_msg["data"] = root["data"];
            
            // 添加时间戳
            if (root.isMember("timestamp")) {
                forward_msg["timestamp"] = root["timestamp"];
            } else {
                forward_msg["timestamp"] = static_cast<Json::UInt>(this->now().seconds());
            }
            
            // 添加触发引脚信息
            forward_msg["trigger_pin"] = rising_pin;
            forward_msg["event_type"] = "rising_edge";
            
            // 序列化为字符串
            Json::StreamWriterBuilder writer;
            std::string json_str = Json::writeString(writer, forward_msg);
            
            // 发布消息
            auto json_msg = std::make_shared<std_msgs::msg::String>();
            json_msg->data = json_str;
            peripherals_monitor_pub_->publish(*json_msg);
            
            // 增加发布次数统计
            touch_event_count_++;
            RCLCPP_INFO(this->get_logger(), "发布触摸事件 (第%d次): %s", touch_event_count_, json_str.c_str());
            
        } else if (command == "touch_falling_edge") {
            // 下降沿事件只更新状态，不转发
            RCLCPP_DEBUG(this->get_logger(), "处理下降沿事件，仅更新GPIO状态，不转发");
            
        } else {
            RCLCPP_WARN(this->get_logger(), "收到未知的触摸事件类型: %s", command.c_str());
        }
        
    } else {
        RCLCPP_ERROR(this->get_logger(), "触摸数据JSON解析失败: %s", errors.c_str());
    }
    // 首先更新GPIO状态（无论是什么事件）
    Json::Value data = root["data"];
    for (const auto& pin_name : data.getMemberNames()) {
        bool is_high = data[pin_name].asBool();
        gpio_high_level_status_[pin_name] = is_high;
        RCLCPP_DEBUG(this->get_logger(), "更新GPIO %s 状态: %s", pin_name.c_str(), is_high ? "高电平" : "低电平");
    }
    
    // 检查当前是否有活跃的高电平状态
    bool current_has_high = false;
    std::string high_pin;
    for (const auto& [pin, is_high] : gpio_high_level_status_) {
        if (is_high) {
            current_has_high = true;
            high_pin = pin;
            break;
        }
    }
    
    // 更新活跃状态
    has_active_high_level_ = current_has_high;
    if (!current_has_high) {
        last_rising_edge_pin_.clear();
        RCLCPP_INFO(this->get_logger(), "重置活跃高电平状态，所有GPIO都是低电平");
    }
}

// void PeripheralsNode::update_pwm_touch_parameters(const std::string color, const int duty_value) {
//     auto param = rcl_interfaces::msg::Parameter();
//     param.name = color + "_duty";
//     auto para_value = rcl_interfaces::msg::ParameterValue();
//     para_value.type = rcl_interfaces::msg::ParameterType::PARAMETER_INTEGER;
//     para_value.integer_value = duty_value;

//     param.value = para_value;

//     while (!pwm_touch_param_client_->wait_for_service(std::chrono::seconds(1))) {
//         if (!rclcpp::ok()) {
//             RCLCPP_ERROR(this->get_logger(), "等待服务的过程中被打断...");
//             return;
//         }
//         RCLCPP_INFO(this->get_logger(), "等待参数设置服务端上线中");
//     }

//     auto request = std::make_shared<SetP::Request>();
//     request->parameters.push_back(param);

//     pwm_touch_param_client_->async_send_request(request, [this, param](rclcpp::Client<SetP>::SharedFuture future) {
//         try {
//             auto response = future.get();
//             for (const auto& result : response->results) {
//                 if (result.successful) {
//                     RCLCPP_INFO(this->get_logger(), "Parameter %s updated successfully.", param.name.c_str());
//                 } else {
//                     RCLCPP_ERROR(this->get_logger(), "Failed to update parameter %s: %s", param.name.c_str(), result.reason.c_str());
//                 }
//             }
//         } catch (const std::exception& e) {
//             RCLCPP_ERROR(this->get_logger(), "异步调用失败: %s", e.what());
//         }
//     });
// }
