cmake_minimum_required(VERSION 3.5)
project(peripherals)

# Default to C99
if(NOT CMAKE_C_STANDARD)
  set(CMAKE_C_STANDARD 99)
endif()

# Default to C++14
if(NOT CMAKE_CXX_STANDARD)
  set(CMAKE_CXX_STANDARD 14)
endif()

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# find dependencies
find_package(ament_cmake REQUIRED)
find_package(rclcpp REQUIRED)
find_package(std_msgs REQUIRED)
find_package(std_srvs REQUIRED)
find_package(jsoncpp REQUIRED)
find_package(homi_speech_interface REQUIRED) # 确保依赖已添加

# 添加 include 目录
include_directories(include)
include_directories(${jsoncpp_INCLUDE_DIRS})

add_executable(peripherals_node src/main.cpp src/peripherals_node.cpp)

# 链接依赖
ament_target_dependencies(peripherals_node rclcpp std_srvs std_msgs homi_speech_interface)
target_link_libraries(peripherals_node jsoncpp)

install(TARGETS peripherals_node
  DESTINATION lib/${PROJECT_NAME})

if(BUILD_TESTING)
  find_package(ament_lint_auto REQUIRED)
  # the following line skips the linter which checks for copyrights
  # uncomment the line when a copyright and license is not present in all source files
  #set(ament_cmake_copyright_FOUND TRUE)
  # the following line skips cpplint (only works in a git repo)
  # uncomment the line when this package is not in a git repo
  #set(ament_cmake_cpplint_FOUND TRUE)
  ament_lint_auto_find_test_dependencies()
endif()

ament_package()
