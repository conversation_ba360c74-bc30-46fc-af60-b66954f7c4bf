ALL_PROXY=socks5h://127.0.0.1:7890
AMENT_CURRENT_PREFIX=/opt/ros/foxy
AMENT_PREFIX_PATH=/opt/ros/unitree_ros2/cyclonedds_ws/install/unitree_hg:/opt/ros/unitree_ros2/cyclonedds_ws/install/unitree_go:/opt/ros/unitree_ros2/cyclonedds_ws/install/unitree_api:/opt/ros/unitree_ros2/cyclonedds_ws/install/rmw_cyclonedds_cpp:/opt/ros/foxy
ANTHROPIC_AUTH_TOKEN=sk-bIrPf7D0AFcc66DlrzOF2T4Rop4TbuQC1WUry1mczY3Iz3YL
ANTHROPIC_BASE_URL=https://anyrouter.top
BROWSER=/root/.cursor-server/cli/servers/Stable-68b8fe7396ea37d8acdaaaa08ba316ba359a4160/server/bin/helpers/browser.sh
BUNDLED_DEBUGPY_PATH=/root/.cursor-server/extensions/ms-python.debugpy-2025.6.0-linux-arm64/bundled/libs/debugpy
CLAUDE_CODE_SSE_PORT=22023
CMAKE_PREFIX_PATH=/opt/ros/unitree_ros2/cyclonedds_ws/install/unitree_hg:/opt/ros/unitree_ros2/cyclonedds_ws/install/unitree_go:/opt/ros/unitree_ros2/cyclonedds_ws/install/unitree_api:/opt/ros/unitree_ros2/cyclonedds_ws/install/rmw_cyclonedds_cpp:/opt/ros/unitree_ros2/cyclonedds_ws/install/cyclonedds
COLCON=1
COLCON_PREFIX_PATH=/opt/ros/unitree_ros2/cyclonedds_ws/install
COLORTERM=truecolor
CYCLONEDDS_URI=<CycloneDDS><Domain><General><Interfaces>
                            <NetworkInterface name="eth0" priority="default" multicast="default" />
                        </Interfaces><AllowMulticast>spdp</AllowMulticast></General></Domain></CycloneDDS>
DBUS_SESSION_BUS_ADDRESS=unix:path=/run/user/0/bus
ENABLE_IDE_INTEGRATION=true
GIT_ASKPASS=/root/.cursor-server/cli/servers/Stable-68b8fe7396ea37d8acdaaaa08ba316ba359a4160/server/extensions/git/dist/askpass.sh
GIT_PAGER=cat
HOME=/root
HTTPS_PROXY=http://127.0.0.1:7890
HTTP_PROXY=http://127.0.0.1:7890
LANG=en_US.UTF-8
LC_ALL=en_US.UTF-8
LD_LIBRARY_PATH=/opt/ros/unitree_ros2/cyclonedds_ws/install/unitree_hg/lib:/opt/ros/unitree_ros2/cyclonedds_ws/install/unitree_go/lib:/opt/ros/unitree_ros2/cyclonedds_ws/install/unitree_api/lib:/opt/ros/unitree_ros2/cyclonedds_ws/install/rmw_cyclonedds_cpp/lib:/opt/ros/unitree_ros2/cyclonedds_ws/install/cyclonedds/lib:/opt/ros/foxy/opt/yaml_cpp_vendor/lib:/opt/ros/foxy/opt/rviz_ogre_vendor/lib:/opt/ros/foxy/lib/aarch64-linux-gnu:/opt/ros/foxy/lib
LESS=-FX
LESSCLOSE=/usr/bin/lesspipe %s %s
LESSOPEN=| /usr/bin/lesspipe %s
LOGNAME=root
LS_COLORS=rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:
MOTD_SHOWN=pam
NO_PROXY=localhost,127.0.0.1,::1
OLDPWD=/mine/worktrees
PAGER=cat
PATH=/opt/ros/unitree_ros2/cyclonedds_ws/install/cyclonedds/bin:/opt/ros/foxy/bin:/root/.cursor-server/cli/servers/Stable-68b8fe7396ea37d8acdaaaa08ba316ba359a4160/server/bin/remote-cli:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/root/.cursor-server/extensions/ms-python.debugpy-2025.6.0-linux-arm64/bundled/scripts/noConfigScripts
PKG_CONFIG_PATH=/opt/ros/unitree_ros2/cyclonedds_ws/install/cyclonedds/lib/aarch64-linux-gnu/pkgconfig:/opt/ros/unitree_ros2/cyclonedds_ws/install/cyclonedds/lib/pkgconfig
PWD=/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/build/expression
PYDEVD_DISABLE_FILE_VALIDATION=1
PYTHONPATH=/opt/ros/unitree_ros2/cyclonedds_ws/install/unitree_hg/lib/python3.8/site-packages:/opt/ros/unitree_ros2/cyclonedds_ws/install/unitree_go/lib/python3.8/site-packages:/opt/ros/unitree_ros2/cyclonedds_ws/install/unitree_api/lib/python3.8/site-packages:/opt/ros/foxy/lib/python3.8/site-packages
RMW_IMPLEMENTATION=rmw_cyclonedds_cpp
ROS_LOCALHOST_ONLY=0
ROS_PYTHON_VERSION=3
ROS_VERSION=2
SHELL=/usr/bin/bash
SHLVL=2
SSH_CLIENT=************** 56501 22
SSH_CONNECTION=************** 56501 ************** 22
SSL_CERT_DIR=/usr/lib/ssl/certs
SSL_CERT_FILE=/usr/lib/ssl/certs/ca-certificates.crt
TERM=xterm-256color
TERM_PROGRAM=vscode
TERM_PROGRAM_VERSION=1.3.6
USER=root
VSCODE_DEBUGPY_ADAPTER_ENDPOINTS=/root/.cursor-server/extensions/ms-python.debugpy-2025.6.0-linux-arm64/.noConfigDebugAdapterEndpoints/endpoint-0f447d4f645154f6.txt
VSCODE_GIT_ASKPASS_EXTRA_ARGS=
VSCODE_GIT_ASKPASS_MAIN=/root/.cursor-server/cli/servers/Stable-68b8fe7396ea37d8acdaaaa08ba316ba359a4160/server/extensions/git/dist/askpass-main.js
VSCODE_GIT_ASKPASS_NODE=/root/.cursor-server/cli/servers/Stable-68b8fe7396ea37d8acdaaaa08ba316ba359a4160/server/node
VSCODE_GIT_IPC_HANDLE=/run/user/0/vscode-git-e7e8394b8f.sock
VSCODE_IPC_HOOK_CLI=/run/user/0/vscode-ipc-e8e913eb-41ab-4f24-a16b-d193027fa16b.sock
XDG_RUNTIME_DIR=/run/user/0
XDG_SESSION_CLASS=user
XDG_SESSION_ID=2
XDG_SESSION_TYPE=tty
_=/usr/bin/colcon
all_proxy=socks5h://127.0.0.1:7890
http_proxy=http://127.0.0.1:7890
https_proxy=http://127.0.0.1:7890
no_proxy=localhost,127.0.0.1,::1
