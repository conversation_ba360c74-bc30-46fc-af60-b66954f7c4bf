# generated from ament/cmake/core/templates/nameConfig.cmake.in

# prevent multiple inclusion
if(_expression_CONFIG_INCLUDED)
  # ensure to keep the found flag the same
  if(NOT DEFINED expression_FOUND)
    # explicitly set it to FALSE, otherwise CMake will set it to TRUE
    set(expression_FOUND FALSE)
  elseif(NOT expression_FOUND)
    # use separate condition to avoid uninitialized variable warning
    set(expression_FOUND FALSE)
  endif()
  return()
endif()
set(_expression_CONFIG_INCLUDED TRUE)

# output package information
if(NOT expression_FIND_QUIETLY)
  message(STATUS "Found expression: 0.0.0 (${expression_DIR})")
endif()

# warn when using a deprecated package
if(NOT "" STREQUAL "")
  set(_msg "Package 'expression' is deprecated")
  # append custom deprecation text if available
  if(NOT "" STREQUAL "TRUE")
    set(_msg "${_msg} ()")
  endif()
  # optionally quiet the deprecation message
  if(NOT ${expression_DEPRECATED_QUIET})
    message(DEPRECATION "${_msg}")
  endif()
endif()

# flag package as ament-based to distinguish it after being find_package()-ed
set(expression_FOUND_AMENT_PACKAGE TRUE)

# include all config extra files
set(_extras "")
foreach(_extra ${_extras})
  include("${expression_DIR}/${_extra}")
endforeach()
