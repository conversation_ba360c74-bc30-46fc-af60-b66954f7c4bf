Performing C SOURCE FILE Test CMAKE_HAVE_LIBC_PTHREAD failed with the following output:
Change Dir: /mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/build/expression/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/make cmTC_01861/fast && /usr/bin/make -f CMakeFiles/cmTC_01861.dir/build.make CMakeFiles/cmTC_01861.dir/build
make[1]: Entering directory '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/build/expression/CMakeFiles/CMakeTmp'
Building C object CMakeFiles/cmTC_01861.dir/src.c.o
/usr/bin/cc   -DCMAKE_HAVE_LIBC_PTHREAD   -o CMakeFiles/cmTC_01861.dir/src.c.o   -c /mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/build/expression/CMakeFiles/CMakeTmp/src.c
Linking C executable cmTC_01861
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_01861.dir/link.txt --verbose=1
/usr/bin/cc  -DCMAKE_HAVE_LIBC_PTHREAD    CMakeFiles/cmTC_01861.dir/src.c.o  -o cmTC_01861 
/usr/bin/ld: CMakeFiles/cmTC_01861.dir/src.c.o: in function `main':
src.c:(.text+0x48): undefined reference to `pthread_create'
/usr/bin/ld: src.c:(.text+0x50): undefined reference to `pthread_detach'
/usr/bin/ld: src.c:(.text+0x5c): undefined reference to `pthread_join'
collect2: error: ld returned 1 exit status
make[1]: *** [CMakeFiles/cmTC_01861.dir/build.make:87: cmTC_01861] Error 1
make[1]: Leaving directory '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/build/expression/CMakeFiles/CMakeTmp'
make: *** [Makefile:121: cmTC_01861/fast] Error 2


Source file was:
#include <pthread.h>

void* test_func(void* data)
{
  return data;
}

int main(void)
{
  pthread_t thread;
  pthread_create(&thread, NULL, test_func, NULL);
  pthread_detach(thread);
  pthread_join(thread, NULL);
  pthread_atfork(NULL, NULL, NULL);
  pthread_exit(NULL);

  return 0;
}

Determining if the function pthread_create exists in the pthreads failed with the following output:
Change Dir: /mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/build/expression/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/make cmTC_13066/fast && /usr/bin/make -f CMakeFiles/cmTC_13066.dir/build.make CMakeFiles/cmTC_13066.dir/build
make[1]: Entering directory '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/build/expression/CMakeFiles/CMakeTmp'
Building C object CMakeFiles/cmTC_13066.dir/CheckFunctionExists.c.o
/usr/bin/cc   -DCHECK_FUNCTION_EXISTS=pthread_create   -o CMakeFiles/cmTC_13066.dir/CheckFunctionExists.c.o   -c /usr/share/cmake-3.16/Modules/CheckFunctionExists.c
Linking C executable cmTC_13066
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_13066.dir/link.txt --verbose=1
/usr/bin/cc  -DCHECK_FUNCTION_EXISTS=pthread_create    CMakeFiles/cmTC_13066.dir/CheckFunctionExists.c.o  -o cmTC_13066  -lpthreads 
/usr/bin/ld: cannot find -lpthreads
collect2: error: ld returned 1 exit status
make[1]: *** [CMakeFiles/cmTC_13066.dir/build.make:87: cmTC_13066] Error 1
make[1]: Leaving directory '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/build/expression/CMakeFiles/CMakeTmp'
make: *** [Makefile:121: cmTC_13066/fast] Error 2



