{"artifacts": [{"path": "expression_node"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "install", "target_link_libraries", "ament_target_dependencies", "add_compile_options", "include_directories", "target_include_directories"], "files": ["CMakeLists.txt", "/opt/ros/foxy/share/ament_cmake_target_dependencies/cmake/ament_target_dependencies.cmake"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 36, "parent": 0}, {"command": 1, "file": 0, "line": 43, "parent": 0}, {"command": 3, "file": 0, "line": 37, "parent": 0}, {"command": 2, "file": 1, "line": 145, "parent": 3}, {"command": 2, "file": 0, "line": 39, "parent": 0}, {"command": 4, "file": 0, "line": 20, "parent": 0}, {"command": 5, "file": 0, "line": 30, "parent": 0}, {"command": 6, "file": 1, "line": 141, "parent": 3}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": " "}, {"backtrace": 6, "fragment": "-Wall"}, {"backtrace": 6, "fragment": "-Wextra"}, {"backtrace": 6, "fragment": "-Wpedantic"}, {"fragment": "-std=gnu++14"}], "defines": [{"backtrace": 5, "define": "BOOST_ALL_NO_LIB"}, {"backtrace": 5, "define": "BOOST_FILESYSTEM_DYN_LINK"}, {"backtrace": 4, "define": "DEFAULT_RMW_IMPLEMENTATION=rmw_fastrtps_cpp"}, {"backtrace": 4, "define": "RCUTILS_ENABLE_FAULT_INJECTION"}, {"backtrace": 4, "define": "SPDLOG_COMPILED_LIB"}], "includes": [{"backtrace": 7, "path": "/mine/worktrees/unitree-debug/xiaoli_application_ros2/src/expression/include"}, {"backtrace": 8, "isSystem": true, "path": "/opt/ros/foxy/include"}], "language": "CXX", "sourceIndexes": [0]}], "id": "expression_node::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 2, "path": "lib/expression"}], "prefix": {"path": "/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install/expression"}}, "link": {"commandFragments": [{"fragment": "", "role": "flags"}, {"fragment": "-Wl,-r<PERSON>,/opt/ros/foxy/lib:", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/foxy/lib/librclcpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/foxy/lib/libament_index_cpp.so", "role": "libraries"}, {"backtrace": 5, "fragment": "/usr/lib/aarch64-linux-gnu/libboost_filesystem.so.1.71.0", "role": "libraries"}, {"fragment": "-lj<PERSON><PERSON><PERSON>", "role": "libraries"}, {"fragment": "/opt/ros/foxy/lib/liblibstatistics_collector.so", "role": "libraries"}, {"fragment": "/opt/ros/foxy/lib/liblibstatistics_collector_test_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"fragment": "/opt/ros/foxy/lib/liblibstatistics_collector_test_msgs__rosidl_generator_c.so", "role": "libraries"}, {"fragment": "/opt/ros/foxy/lib/liblibstatistics_collector_test_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"fragment": "/opt/ros/foxy/lib/liblibstatistics_collector_test_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/foxy/lib/liblibstatistics_collector_test_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/foxy/lib/libstd_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/foxy/lib/libstd_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/foxy/lib/libstd_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/foxy/lib/libstd_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/foxy/lib/libstd_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/foxy/lib/librcl.so", "role": "libraries"}, {"fragment": "/opt/ros/foxy/lib/librcl_interfaces__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"fragment": "/opt/ros/foxy/lib/librcl_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"fragment": "/opt/ros/foxy/lib/librcl_interfaces__rosidl_typesupport_c.so", "role": "libraries"}, {"fragment": "/opt/ros/foxy/lib/librcl_interfaces__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/foxy/lib/librcl_interfaces__rosidl_typesupport_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/foxy/lib/librmw_implementation.so", "role": "libraries"}, {"fragment": "/opt/ros/foxy/lib/librmw.so", "role": "libraries"}, {"fragment": "/opt/ros/foxy/lib/librcl_logging_spdlog.so", "role": "libraries"}, {"fragment": "/usr/lib/aarch64-linux-gnu/libspdlog.so.1.5.0", "role": "libraries"}, {"fragment": "-l<PERSON><PERSON><PERSON>", "role": "libraries"}, {"fragment": "/opt/ros/foxy/lib/librcl_yaml_param_parser.so", "role": "libraries"}, {"fragment": "/opt/ros/foxy/lib/libyaml.so", "role": "libraries"}, {"fragment": "/opt/ros/foxy/lib/librosgraph_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"fragment": "/opt/ros/foxy/lib/librosgraph_msgs__rosidl_generator_c.so", "role": "libraries"}, {"fragment": "/opt/ros/foxy/lib/librosgraph_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"fragment": "/opt/ros/foxy/lib/librosgraph_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/foxy/lib/librosgraph_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/foxy/lib/libstatistics_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"fragment": "/opt/ros/foxy/lib/libstatistics_msgs__rosidl_generator_c.so", "role": "libraries"}, {"fragment": "/opt/ros/foxy/lib/libstatistics_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"fragment": "/opt/ros/foxy/lib/libstatistics_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/foxy/lib/libstatistics_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/foxy/lib/libbuiltin_interfaces__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"fragment": "/opt/ros/foxy/lib/libbuiltin_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"fragment": "/opt/ros/foxy/lib/libbuiltin_interfaces__rosidl_typesupport_c.so", "role": "libraries"}, {"fragment": "/opt/ros/foxy/lib/libbuiltin_interfaces__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/foxy/lib/librosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/foxy/lib/librosidl_typesupport_introspection_c.so", "role": "libraries"}, {"fragment": "/opt/ros/foxy/lib/libbuiltin_interfaces__rosidl_typesupport_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/foxy/lib/librosidl_typesupport_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/foxy/lib/librosidl_typesupport_c.so", "role": "libraries"}, {"fragment": "/opt/ros/foxy/lib/librcpputils.so", "role": "libraries"}, {"fragment": "/opt/ros/foxy/lib/librosidl_runtime_c.so", "role": "libraries"}, {"fragment": "/opt/ros/foxy/lib/librcutils.so", "role": "libraries"}, {"fragment": "-ldl", "role": "libraries"}, {"fragment": "/opt/ros/foxy/lib/libtracetools.so", "role": "libraries"}], "language": "CXX"}, "name": "expression_node", "nameOnDisk": "expression_node", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "src/expression_node.cpp", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}