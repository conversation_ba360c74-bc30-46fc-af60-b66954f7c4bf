{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "minimumCMakeVersion": {"string": "3.5"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}], "name": "", "projects": [{"directoryIndexes": [0], "name": "expression", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "expression_node::@6890427a1f51a3e7e1df", "jsonFile": "target-expression_node-48fe9cfc21b5efb10bf7.json", "name": "expression_node", "projectIndex": 0}, {"directoryIndex": 0, "id": "expression_uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-expression_uninstall-5bd97126324a6533a6d2.json", "name": "expression_uninstall", "projectIndex": 0}, {"directoryIndex": 0, "id": "uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-uninstall-c6c34abbea80df785124.json", "name": "uninstall", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/build/expression", "source": "/mine/worktrees/unitree-debug/xiaoli_application_ros2/src/expression"}, "version": {"major": 2, "minor": 0}}