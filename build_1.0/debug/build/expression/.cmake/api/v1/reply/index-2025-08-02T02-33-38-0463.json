{"cmake": {"generator": {"name": "Unix Makefiles"}, "paths": {"cmake": "/usr/bin/cmake", "cpack": "/usr/bin/cpack", "ctest": "/usr/bin/ctest", "root": "/usr/share/cmake-3.16"}, "version": {"isDirty": false, "major": 3, "minor": 16, "patch": 3, "string": "3.16.3", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-9d0e3ab53573612d291c.json", "kind": "codemodel", "version": {"major": 2, "minor": 0}}], "reply": {"client-colcon-cmake": {"codemodel-v2": {"jsonFile": "codemodel-v2-9d0e3ab53573612d291c.json", "kind": "codemodel", "version": {"major": 2, "minor": 0}}}}}