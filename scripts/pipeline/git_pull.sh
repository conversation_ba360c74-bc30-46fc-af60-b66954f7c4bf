g_work_dir=$(pwd)
g_root_of_script=$(dirname $(readlink -f "$0"))

g_min_hour=23
g_min_minute=40
g_cur_day=""
g_cur_hour=""
g_cur_minute=""
g_last_day=""
g_start_pipeline="n"

function func_check_condition()
{
    g_cur_date=$(date "+%Y%m%d%H%M%S")
    g_cur_day=${g_cur_date:6:2}
    g_cur_hour=${g_cur_date:8:2}
    g_cur_minute=${g_cur_date:10:2}
    echo "[$LINENO][NOTE ]g_cur_date: ${g_cur_date}"
    echo "[$LINENO][NOTE ]g_last_day: ${g_last_day}"
    echo "[$LINENO][NOTE ]g_cur_day: ${g_cur_day}"
    echo "[$LINENO][NOTE ]g_cur_hour: ${g_cur_hour}"
    echo "[$LINENO][NOTE ]g_cur_minute: ${g_cur_minute}"
    if [ "${g_last_day}" == "${g_cur_day}" ]; then
        echo "[$LINENO][WARN ]only once per day"
        g_start_pipeline="n"
        return
    fi
    if [ ${g_cur_hour} -gt ${g_min_hour} ]; then
        echo "[$LINENO][NOTE ]reach hour, start pipeline"
        g_start_pipeline="y"
        g_last_day=${g_cur_day}
        return
    fi
    if [ ${g_cur_hour} -ge ${g_min_hour} -a ${g_cur_minute} -ge ${g_min_minute} ]; then
        echo "[$LINENO][NOTE ]reach minute, start pipeline"
        g_start_pipeline="y"
        g_last_day=${g_cur_day}
    fi
}

function func_git_pull()
{
    git -C ${g_root_of_script}/../robot-application pull origin
    echo "$?"
    if [ $? == 0 ]; then
        echo "[$LINENO][NOTE ]pull success"
        echo "${g_cur_date}" > ${g_root_of_script}/pipeline_tag
    else
        echo "[$LINENO][ERROR]pull failed"
    fi
}

# 20250228153634
while true
do
    func_check_condition
    if [ "${g_start_pipeline}" == "y" ]; then
        func_git_pull
        g_start_pipeline="n"
    fi
    sleep 10
done
