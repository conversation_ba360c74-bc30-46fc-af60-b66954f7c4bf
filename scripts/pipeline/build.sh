g_work_dir=$(pwd)
g_root_of_script=$(dirname $(readlink -f "$0"))

function func_start_pipeline()
{
    if [ ! -f ${g_root_of_script}/pipeline_tag ]; then
        echo "[$LINENO][ERROR]pipeline tag not exist"
        return
    fi
    echo "[$LINENO][NOTE ]pipeline tag exist"
    cd ${g_root_of_script}/../robot-application
    git reset --hard
    if [ $? == 0 ]; then
        echo "[$LINENO][NOTE ]reset success"
    else
        echo "[$LINENO][ERROR]reset failed"
        cd ${g_root_of_script}
        rm -rf ${g_root_of_script}/pipeline_tag
        return
    fi
    cd xiaoli_application_ros2
    bash build.sh --no=1.0
    cd ${g_root_of_script}
    rm -rf ${g_root_of_script}/pipeline_tag
}

while true
do
    func_start_pipeline
    sleep 10
done
