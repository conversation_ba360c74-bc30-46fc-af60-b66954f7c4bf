#!/bin/bash

g_work_dir=$(pwd)
g_root_of_script=$(dirname $(readlink -f "$0"))

g_install_status="success"
g_install_time=$(date +%Y%m%d%H%M%S)
g_target_board="alg"
g_alg_file="RobotAlgorithmGen1"
g_install_target_prefix=${g_target_board}_${g_install_time}
g_install_quick_path=${g_root_of_script}/${g_install_target_prefix}
g_install_zip_name=${g_alg_file}.tar.gz
g_install_zip_path=${g_install_quick_path}/${g_install_zip_name}
echo "[$LINENO][NOTE ]time: ${g_install_time}"
echo "[$LINENO][NOTE ]path: ${g_install_quick_path}"
echo "[$LINENO][NOTE ]file: ${g_install_zip_name}"

function func_del_dir_or_file_safe()
{
    local arg_to_del="$1"
    local arg_is_dir="$2"
    local arg_status=""
    local arg_timeout=1
    while true
    do
        if [ ${arg_timeout} -gt 3 ]; then
            echo "[$LINENO][ERROR][${arg_to_del}]del timeout, ${arg_timeout}"
            g_install_status="func_del_dir_or_file_safe"
            break
        fi
        if [ "${arg_is_dir}" == "y" ]; then
            if [ ! -d ${arg_to_del} ]; then
                echo "[$LINENO][NOTE ][${arg_to_del}]not exist"
                break
            fi
        else
            if [ ! -f ${arg_to_del} ]; then
                echo "[$LINENO][NOTE ][${arg_to_del}]not exist"
                break
            fi
        fi
        rm -rf ${arg_to_del}
        if [ "${arg_is_dir}" == "y" ]; then
            if [ ! -d ${arg_to_del} ]; then
                echo "[$LINENO][NOTE ][${arg_to_del}]del ok"
                break
            fi
        else
            if [ ! -f ${arg_to_del} ]; then
                echo "[$LINENO][NOTE ][${arg_to_del}]del ok"
                break
            fi
        fi
        arg_timeout=$(($arg_timeout+1))
        sleep 5
    done
}

g_update_finish_record=/tmp/cmcc_robot_ota_finish
g_update_status_record=/tmp/cmcc_robot_ota_status.ini
function func_clear_install_flag()
{
    echo "[$LINENO][NOTE ]----- clear install flag"
    func_del_dir_or_file_safe "${g_update_finish_record}" "n"
    if [ "${g_install_status}" != "success" ]; then
        return
    fi
    func_del_dir_or_file_safe "${g_update_status_record}" "n"
    if [ "${g_install_status}" != "success" ]; then
        return
    fi
}

function func_extract_zip()
{
    echo "[$LINENO][NOTE ]----- extract zip"
    func_del_dir_or_file_safe "${g_install_quick_path}" "y" "n"
    if [ "${g_install_status}" != "success" ]; then
        return
    fi
    mkdir -p ${g_install_quick_path}
    sed -n -e '1,/^exit 0$/!p' $0 > "${g_install_zip_path}" 2>/dev/null
    if [ -f ${g_install_zip_path} ]; then
        echo "[$LINENO][NOTE ]extract [${g_install_zip_path}] success"
    else
        echo "[$LINENO][ERROR]extract [${g_install_zip_path}] failed"
        g_install_status="func_extract_zip"
        return
    fi
}

function func_update_finish()
{
    echo "[$LINENO][NOTE ]----- update finish"
    echo "finish" > ${g_update_finish_record}
}

function func_update_all()
{
    echo "[$LINENO][NOTE ]----- update all"
    local arg_user="nvidia"
    local arg_pwd="nvidia"
    local arg_file=${g_install_zip_path}
    local arg_ip="*************"
    local arg_update_result=""
    local arg_update_log=""
    arg_update_log=/tmp/${g_install_target_prefix}_install.log
    echo "[$LINENO][NOTE ]usr:  ${arg_user}"
    echo "[$LINENO][NOTE ]pwd:  ${arg_pwd}"
    echo "[$LINENO][NOTE ]ip:   ${arg_ip}"
    echo "[$LINENO][NOTE ]file: ${arg_file}"
    echo "[$LINENO][NOTE ]log:  ${arg_update_log}"
    if [ ! -f ${arg_file} ]; then
        echo "[$LINENO][ERROR][${arg_file}]lost"
        g_install_status="func_update_all_for_${g_target_board}"
        return
    fi
    echo "[$LINENO][NOTE ]sending ${arg_file} ..."
    sshpass -p "${arg_pwd}" rsync -av ${arg_file} "${arg_user}"@"${arg_ip}":/tmp
    echo "[$LINENO][NOTE ]update ${arg_file} ..."
    sshpass -p "${arg_pwd}" ssh -tt "${arg_user}"@"${arg_ip}" "cd /tmp && rm -rf ${g_alg_file} && tar -xf ${g_alg_file}.tar.gz && cd ${g_alg_file} && bash ota_install.sh" > ${arg_update_log}
    arg_update_result=$(grep "install_${g_target_board}_success" ${arg_update_log})
    if [ "${arg_update_result}" == "" -o "${arg_update_result}" == " " ]; then
        echo "[$LINENO][ERROR][${g_target_board}]update failed"
        g_install_status="func_update_all_for_${g_target_board}"
        return
    else
        echo "[$LINENO][NOTE ][${g_target_board}]update success"
    fi
}

g_update_target="robbody_${g_target_board}_status"
function func_record_update_status()
{
    echo "[$LINENO][NOTE ]----- record update status"
    if [ ! -f ${g_update_status_record} ]; then
        if [ "${g_install_status}" == "success" ]; then
            echo "${g_update_target}=success" > ${g_update_status_record}
        else
            echo "${g_update_target}=failed" > ${g_update_status_record}
        fi
        return
    fi
    local arg_result=""
    arg_result=$(grep "${g_update_target}=" ${g_update_status_record})
    if [ "${arg_result}" == "" -o "${arg_result}" == " " ]; then
        if [ "${g_install_status}" == "success" ]; then
            echo "${g_update_target}=success" >> ${g_update_status_record}
        else
            echo "${g_update_target}=failed" >> ${g_update_status_record}
        fi
        return
    fi
    if [ "${g_install_status}" == "success" ]; then
        sed -i "s|^${g_update_target}=.*|${g_update_target}=success|" ${g_update_status_record}
    else
        sed -i "s|^${g_update_target}=.*|${g_update_target}=failed|" ${g_update_status_record}
    fi
}

while true
do
    g_install_status="success"
    func_clear_install_flag
    if [ "${g_install_status}" != "success" ]; then
        break
    fi

    g_install_status="success"
    func_extract_zip
    if [ "${g_install_status}" != "success" ]; then
        break
    fi

    g_install_status="success"
    func_update_all
    break
done

if [ "${g_install_status}" != "success" ]; then
    echo "[$LINENO][ERROR][${g_install_status}]install_${g_target_board}_failed"
else
    echo "[$LINENO][NOTE ][${g_install_status}]install_${g_target_board}_success"
fi
func_record_update_status
func_del_dir_or_file_safe "${g_install_quick_path}" "y" "n"
func_update_finish
sync
exit 0
