#!/bin/bash

g_work_dir=$(pwd)
g_root_of_script=$(dirname $(readlink -f "$0"))

g_install_time=$(date +%Y%m%d%H%M%S)
g_install_script=${g_root_of_script}/install_alg.sh
g_alg_zip_path=${g_root_of_script}/RobotAlgorithmGen1.tar.gz
g_pack_bin_name=RobotAlgorithmGen1_${g_install_time}.bin
g_pack_bin_path=${g_root_of_script}/${g_pack_bin_name}
echo "[$LINENO][NOTE ]time: ${g_install_time}"
echo "[$LINENO][NOTE ]bin:  ${g_pack_bin_name}"
function func_make_bin()
{
    echo "[$LINENO][NOTE ]----- make bin"
    if [ -f ${g_pack_bin_path} ]; then
        echo "[$LINENO][NOTE ]clear old bin"
        rm -rf ${g_pack_bin_path}
    fi
    cat ${g_install_script} ${g_alg_zip_path} > ${g_pack_bin_path}
    if [ -f ${g_pack_bin_path} ]; then
        chmod 0777 ${g_pack_bin_path}
        echo "[$LINENO][NOTE ]make [${g_pack_bin_name}] success"
    else
        echo "[$LINENO][ERROR]make [${g_pack_bin_name}] failed"
        g_install_status="func_make_bin"
    fi
}

func_make_bin
sync
