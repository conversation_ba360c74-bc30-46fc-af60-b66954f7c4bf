# 设置默认输出设备为 g1-play-card
pcm.!default {
    type plug
    slave.pcm "g1-play-card"
}

pcm.g1-record-card {
    type asym
    playback.pcm {
        type plug
        slave.pcm "hw:0,0"
    }
    capture.pcm {
        type plug
        slave.pcm {
            type multi
            slaves {
                a { pcm "hw:0,0" channels 2 }
                b { pcm "hw:1,0" channels 4 }
            }
            bindings {
                0 { slave b channel 0 }
                1 { slave b channel 1 }
                2 { slave b channel 2 }
                3 { slave b channel 3 }
                4 { slave a channel 0 }
                5 { slave a channel 1 }
            }
        }
        ttable.0.0 1
        ttable.1.1 1
        ttable.2.2 1
        ttable.3.3 1
        ttable.4.4 1
        ttable.5.5 1
    }
}

# pcm.g1-play-card {
#     type dmix
#     ipc_key 1024
#     ipc_key_add_uid true
#     slave {
#         pcm "hw:0,0"
#         rate 16000
#         channels 2
#         period_time 0
#         period_size 1024
#         buffer_size 8192
#     }
# }

pcm.g1-play-card {
    type plug  # 添加 plug 插件层
    slave.pcm {
        type dmix
        ipc_key 1024
        ipc_key_add_uid true
        slave {
            pcm "hw:0,0"
            rate 16000
            channels 2  # 硬件输出固定为立体声
            period_time 0
            period_size 1024
            buffer_size 16384
        }
    }
    # 自动处理单声道到立体声的转换
    ttable.0.0 1.0  # 单声道映射到左声道
    ttable.0.1 1.0  # 单声道映射到右声道
}
