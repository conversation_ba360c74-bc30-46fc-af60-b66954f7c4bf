#!/bin/bash

g_root_of_script=$(dirname $(readlink -f "$0"))
g_install_path=/usr/bin/cmcc_robot/install

g_unitree_pwd="123" # 设置 sudo 密码
LOG_DIR="$HOME/log"
echo "设置日志目录: $LOG_DIR"
INTERVAL_HOURS=2

# 定义需要收集日志的服务列表
SERVICES=(
    run_xiaoli_server
    accompany
    follow_console
    follow_start
    nav_outdoor
    start_rtk
    unitree_transfer
    uwb_loc
    voa_ros2
    run_robdog_service
)

# 定义 sudo 执行函数
function run_sudo_command() {
    local password="$1"
    local command="$2"
    echo "$password" | sudo -S --prompt="" sh -c "$command" > /dev/null 2>&1
    if [ $? -ne 0 ]; then
        error_message=$( { echo "$password"; echo "$command"; } | sudo -S --prompt="" sh -c "$command" 2>&1 )
        echo "$(date '+%Y-%m-%d %H:%M:%S') 错误: $error_message" >> "$LOG_DIR/error.log"
        return 1
    fi
    return 0
}

# 确保日志目录存在
if ! echo "$g_unitree_pwd" | sudo -S --prompt="" test -d "$LOG_DIR" 2>/dev/null; then
    echo "日志目录 $LOG_DIR 不存在，将创建"
    # 创建日志目录并设置权限
    run_sudo_command "$g_unitree_pwd" "mkdir -p \"$LOG_DIR\""
    run_sudo_command "$g_unitree_pwd" "chown unitree:unitree \"$LOG_DIR\""
    run_sudo_command "$g_unitree_pwd" "chmod 755 \"$LOG_DIR\""
else
    echo "日志目录 $LOG_DIR 已存在"
fi

# 检查服务是否存在
function service_exists() {
    local service_name="$1"
    
    # 完整的服务单元名称（添加 .service 后缀）
    local full_service_name="${service_name}.service"
    
    # 检查单元文件是否存在
    if ! systemctl list-unit-files --full --no-legend --type=service \
        | grep -q "^${full_service_name}[[:space:]]"; then
        return 1
    fi
    return 0
}

# 收集服务日志
function collect_service_log() {
    local service_name="$1"
    local output_file="$2"
    
    # 完整的服务单元名称（添加 .service 后缀）
    local full_service_name="${service_name}.service"
    
    LOG_CMD="journalctl -u ${full_service_name} --since \"$START_TIME\" --until \"$END_TIME\" --no-pager > \"$output_file\""
    run_sudo_command "$g_unitree_pwd" "$LOG_CMD"
    if [ $? -ne 0 ]; then
        echo "$(date '+%Y-%m-%d %H:%M:%S') 警告: 收集 ${service_name} 日志失败" >> "$LOG_DIR/error.log"
    fi
}

# 主循环
while true; do
    # 生成时间相关变量
    CURRENT_TIME=$(date +"%Y%m%d_%H%M%S")
    START_TIME=$(date -d "-${INTERVAL_HOURS} hours" +"%Y-%m-%d %H:%M:%S")
    END_TIME=$(date +"%Y-%m-%d %H:%M:%S")
    
    # 清空日志目录（保留目录本身和 call_*.txt 文件）
    run_sudo_command "$g_unitree_pwd" "find \"$LOG_DIR\" -mindepth 1 -not -name 'call_*.txt' -delete"

    # 收集所有服务的日志
    for service in "${SERVICES[@]}"; do
        if service_exists "$service"; then
            FILENAME="${service}_${CURRENT_TIME}.log"
            OUTPUT_FILE="${LOG_DIR}/${FILENAME}"
            
            # 收集单个服务日志
            echo "收集服务日志: $service"
            collect_service_log "$service" "$OUTPUT_FILE"
            
            # 设置文件权限
            if [ -f "$OUTPUT_FILE" ]; then
                run_sudo_command "$g_unitree_pwd" "chown unitree:unitree \"$OUTPUT_FILE\""
                run_sudo_command "$g_unitree_pwd" "chmod 640 \"$OUTPUT_FILE\""
                echo "$(date '+%Y-%m-%d %H:%M:%S') 服务 $service 日志已保存至: ${OUTPUT_FILE}"
            fi
        else
            echo "$(date '+%Y-%m-%d %H:%M:%S') 警告: 服务 $service 不存在，跳过日志收集" >> "$LOG_DIR/error.log"
        fi
    done

    echo "$(date '+%Y-%m-%d %H:%M:%S') 所有服务日志收集完成"

    # 等待3分钟
    sleep 180
done