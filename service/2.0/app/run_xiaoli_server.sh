#!/bin/bash

# 设置 CPU 亲和性（绑定到核心 0,1,4,5）
taskset -pc 0,1,4,5 $$
g_root_of_script=$(dirname $(readlink -f "$0"))
g_install_path=/usr/bin/cmcc_robot/install
g_target_user=""
g_target_pwd=""
g_target_is_test=""

MAX_RETRIES=60       # 最大重试次数
RETRY_INTERVAL=1     # 重试间隔（秒）
TIMEOUT=$((MAX_RETRIES * RETRY_INTERVAL))  # 总超时时间（秒）

current_time=$(date +"%Y-%m-%d %H:%M:%S")
echo "xiaoli server 进入时间：$current_time"

check_ros2_init() {
  # 1. 检查 ros2 daemon 进程
  if ! pgrep -x "_ros2_daemon" > /dev/null; then
    echo "错误:ros2_daemon 未运行"
    echo "尝试启动 daemon..."
    ros2 daemon start
    sleep 2  # 等待 daemon 启动
    if ! pgrep -x "_ros2_daemon" > /dev/null; then
	    echo "错误:ros2_daemon 仍未运行"
    	    return 1
    else 
	    echo "ros2_daemon 已经运行"
    fi
  else
    echo "ros2_daemon 正在运行"
  fi
  
  # 2. 检查 ros2 命令可用性
  if ! ros2 -h &> /dev/null; then
    echo "错误:ros2 命令不可用"
    return 1
  else 
    echo "ros2 命令可用"
  fi
  
  echo "ROS 2 初始化完成"
  return 0
}

function func_run_sudo_command()
{
    local arg_command="$1"
    # echo "[$LINENO][NOTE ]pwd: ${g_target_pwd}, cmd: ${arg_command}"
    echo "${g_target_pwd}" | sudo -S bash -c "${arg_command}"
    if [ $? -ne 0 ]; then
        echo "[$LINENO][ERROR][${arg_command}]failed"
        g_cmd_exe_status="func_run_sudo_command"
        return
    fi
}

g_install_status="success"
g_robot_type=""
g_robot_info_sn=""
g_dev_cfg_path=/etc/cmcc_robot/cmcc_dev.ini
function func_get_sub_robot_info()
{
    local arg_item=$1
    local arg_result=""
    arg_result=$(grep "${arg_item}=" ${g_dev_cfg_path})
    if [ "${arg_result}" == "" -o "${arg_result}" == " " ]; then
        echo "[$LINENO][ERROR][${arg_item}]lost"
        g_install_status="func_get_sub_robot_info"
        return
    fi
    arg_result=${arg_result#*=}
    if [ "${arg_result}" == "" -o "${arg_result}" == " " ]; then
        echo "[$LINENO][ERROR][${arg_item}]invalid"
        g_install_status="func_get_sub_robot_info"
        return
    fi
    if [ "${arg_item}" == "devType" ]; then
        g_robot_type=${arg_result}
        return
    fi
    if [ "${arg_item}" == "devSn" ]; then
        g_robot_info_sn=${arg_result}
        return
    fi
}

function func_get_robot_info()
{
    echo "[$LINENO][NOTE ]----- get robot info"
    if [ ! -f ${g_dev_cfg_path} ]; then
        echo "[$LINENO][ERROR][${g_dev_cfg_path}]lost"
        g_install_status="func_get_robot_info"
        return
    fi
    while true
    do
        g_install_status="success"
        func_get_sub_robot_info "devType"
        if [ "${g_install_status}" != "success" ]; then
            g_robot_type="591884"
        fi
        g_install_status="success"
        func_get_sub_robot_info "devSn"
        if [ "${g_install_status}" != "success" ]; then
            break
        fi
        g_install_status="success"
        break
    done

    echo "[$LINENO][NOTE ]type: ${g_robot_type}"
    echo "[$LINENO][NOTE ]sn:   ${g_robot_info_sn}"
}

function func_update_factory_info()
{
    func_get_robot_info
    if [ "${g_install_status}" != "success" ]; then
        echo "[$LINENO][ERROR]get robot info failed"
        return
    fi
    local arg_hostname=""
    local arg_sn_suffix_four=""
    local arg_hostname_path=/etc/hostname
    local arg_hosts_path=/etc/hosts
    arg_sn_suffix_four=${g_robot_info_sn:0-4:4}
    arg_hostname=CMB${g_robot_type}-${arg_sn_suffix_four}
    echo "[$LINENO][NOTE ]hostname: ${arg_hostname}"
    if cat ${arg_hostname_path} | grep -q "${arg_hostname}"; then
        echo "[$LINENO][NOTE ]not update hostname"
    else
        echo "[$LINENO][NOTE ]need update hostname"
        func_run_sudo_command "echo ${arg_hostname} > ${arg_hostname_path}"
    fi
    if cat ${arg_hosts_path} | grep -q "${arg_hostname}"; then
        echo "[$LINENO][NOTE ]not update hosts"
    else
        echo "[$LINENO][NOTE ]need update hosts"
        func_run_sudo_command "echo 127.0.0.1 ${arg_hostname} >> ${arg_hosts_path}"
    fi
}

# 设置默认播放设备,当设备插拔时，或者程序启动时调用
# 优先级: [USB Audio Device]  
# 指定USB设备名
usb_device_name="USB_Audio_Device"

# 通过USB设备名获取声卡ID
#soundcard_id=$(aplay -l | grep "${usb_device_name}" -A 2 | grep "card" | awk '{print $2}' | tr -d ':' | head -n 1)
#name: <alsa_output.usb-C-Media_Electronics_Inc._USB_Audio_Device-00.analog-stereo>
padevname=$(pacmd list-sinks | grep -e name: | grep -e ${usb_device_name} | head -n 1 | sed 's/.*<\(.*\)>.*/\1/')

if [ "${padevname}" = "" ]; then
echo "no playback card named: \"${usb_device_name}\"!"
else 
# 通过pulseaudio 设置默认声卡
pacmd set-default-sink ${padevname}
echo "set default sink \"${padevname}\""
fi

if [ -d ${g_install_path}/env/factory ]; then
    echo "[$LINENO][NOTE ]start update factory ..."
    func_run_sudo_command "${g_install_path}/env/factory/factory_update"
    echo "[$LINENO][NOTE ]start update factory over"
fi

echo "[$LINENO][NOTE ]start update factory info ..."
func_update_factory_info
echo "[$LINENO][NOTE ]start update factory info over"

if [ -d ${g_install_path}/env/ObuVoice ]; then
    echo "[$LINENO][NOTE ]start ifly ..."
    bash ${g_install_path}/env/ObuVoice/pkt/ifly-init.sh &
    echo "[$LINENO][NOTE ]start ifly over"
fi

echo "[$LINENO][NOTE ]start and get unitree history log ..."
bash ${g_install_path}/env/service/app/log_unitree.sh &
echo "[$LINENO][NOTE ] get unitree history log  over"

source /opt/ros/foxy/setup.bash
source ${g_install_path}/setup.bash
source /opt/ros/unitree_ros2/cyclonedds_ws/install/setup.bash
export RMW_IMPLEMENTATION=rmw_cyclonedds_cpp
export CYCLONEDDS_URI='<CycloneDDS><Domain><General><Interfaces>
                            <NetworkInterface name="eth0" priority="default" multicast="default" />
                        </Interfaces><AllowMulticast>spdp</AllowMulticast></General></Domain></CycloneDDS>'

# 检查并安装系统依赖：libcurl4-openssl-dev
if ! dpkg -s libcurl4-openssl-dev >/dev/null 2>&1; then
    echo "安装系统依赖: libcurl4-openssl-dev..."
    if [ -n "${g_target_pwd}" ]; then
        func_run_sudo_command "apt-get update"
        func_run_sudo_command "apt-get install -y libcurl4-openssl-dev"
    else
        echo "[警告] 未设置g_target_pwd，跳过系统依赖安装"
    fi
else
    echo "系统依赖 libcurl4-openssl-dev 已安装"
fi

# 检查并安装Python依赖：pycurl
if ! python3 -c "import pycurl" 2>/dev/null; then
    echo "安装Python依赖: pycurl..."
    # 确保pip可用
    if ! command -v pip3 >/dev/null 2>&1; then
        echo "pip3不可用，安装pip..."
        func_run_sudo_command "apt-get install -y python3-pip"
    fi
    
    # 使用安装环境对应的pip
    PYTHON_USER_BASE=$(python3 -m site --user-base)
    PIP_PATH="${PYTHON_USER_BASE}/bin/pip"
    if [ -x "${PIP_PATH}" ]; then
        echo "使用本地pip安装: ${PIP_PATH}"
        ${PIP_PATH} install pycurl
    else
        echo "使用系统pip安装"
        pip3 install pycurl
    fi
else
    echo "Python依赖 pycurl 已安装"
fi

# 检测mpv进程是否存在，不存在则启动expression节点并等待90秒
echo "[$LINENO][NOTE ]检测mpv进程..."
if ! pgrep -x "mpv" > /dev/null; then
    echo "[$LINENO][NOTE ]mpv进程不存在，启动expression节点..."
    ros2 run expression expression_node &
    expression_pid=$!
    echo "[$LINENO][NOTE ]等待15秒进行开机动画演示..."
    sleep 30
    echo "[$LINENO][NOTE ]表情演示完成，继续启动主程序"
else
    echo "[$LINENO][NOTE ]mpv进程已存在，无需启动expression节点"
fi

# 检测ros2、dds环境是否ok
echo "开始检测 ROS 2 初始化状态，超时时间 $TIMEOUT 秒..."
start_time=$(date +%s)
elapsed_time=0
retry_count=0

while [ $elapsed_time -lt $TIMEOUT ]; do
  echo "--- 尝试 $((retry_count+1))/$(($MAX_RETRIES)) ---"
  
  if check_ros2_init; then
    echo "ROS 2 已成功初始化,耗时 $elapsed_time 秒"
    break
  fi
  
  retry_count=$((retry_count+1))
  elapsed_time=$(($(date +%s) - $start_time))
  
  if [ $elapsed_time -lt $TIMEOUT ]; then
    remaining_time=$(($TIMEOUT - $elapsed_time))
    echo "等待 $RETRY_INTERVAL 秒后重试，剩余时间 $remaining_time 秒..."
    sleep $RETRY_INTERVAL
  fi
done

current_time=$(date +"%Y-%m-%d %H:%M:%S")
echo "ros2 初始化完成时间：$current_time"

export GST_PLUGIN_PATH=/usr/lib/aarch64-linux-gnu/gstreamer-1.0/
if [ -d ${g_install_path}/env/lib ]; then
    echo "[$LINENO][NOTE ]export lib"
    export LD_LIBRARY_PATH=${g_install_path}/env/lib:$LD_LIBRARY_PATH
    if [ "${g_target_is_test}" == "y" ]; then
        if [ -d ${g_install_path}/env/lib/dev ]; then
            echo "[$LINENO][NOTE ]export lib dev"
            export LD_LIBRARY_PATH=${g_install_path}/env/lib/dev:$LD_LIBRARY_PATH
        fi
    else
        if [ -d ${g_install_path}/env/lib/pro ]; then
            echo "[$LINENO][NOTE ]export lib pro"
            export LD_LIBRARY_PATH=${g_install_path}/env/lib/pro:$LD_LIBRARY_PATH
        fi
    fi
fi
echo "start ros2 launch launch_package unitree_ctrl_robdog.py"
ros2 launch launch_package unitree_ctrl_robdog.py
