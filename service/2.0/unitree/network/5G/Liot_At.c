#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <pthread.h>
#include <sys/time.h>
#include <dirent.h>
#include <sys/ioctl.h>
#include <linux/usbdevice_fs.h>
#include <linux/usb/ch9.h>
#include <signal.h>
#include <ctype.h>
#include <errno.h>
#include <time.h>
#include <fcntl.h>
#include <stdint.h>

#define USB_DEV_DIR "/dev/bus/usb"

// 命令状态枚举
typedef enum {
    CMD_PENDING,
    CMD_SUCCESS,
    CMD_TIMEOUT,
    CMD_ERROR
} CommandStatus;

// 响应缓冲区结构
struct ResponseBuffer {
    char data[4096];             // 缓冲区数据
    size_t length;               // 当前数据长度
    char currentCmd[256];        // 当前处理的命令
    CommandStatus status;        // 命令状态
    struct timespec startTime;   // 命令开始时间
    pthread_mutex_t mutex;       // 互斥锁
    pthread_cond_t cond;         // 条件变量
};

// USB设备ID结构
struct usb_device_id {
    uint16_t matchFlags;
    uint16_t idVendor;
    uint16_t idProduct;
    uint8_t bInterfaceNumber;
    uint8_t bInterfaceClass;
    uint8_t bInterfaceSubClass;
    uint8_t bInterfaceProtocol;
};

// USB设备ID匹配标志
#define USB_DEVICE_ID_MATCH_VENDOR          0x0001
#define USB_DEVICE_ID_MATCH_PRODUCT         0x0002
#define USB_DEVICE_ID_MATCH_INT_NUMBER      0x0004
#define USB_DEVICE_ID_MATCH_INT_CLASS       0x0008
#define USB_DEVICE_ID_MATCH_INT_SUBCLASS    0x0010
#define USB_DEVICE_ID_MATCH_INT_PROTOCOL    0x0020

// USB设备ID宏
#define USB_DEVICE_INTERFACE_NUMBER(vend, prod, num) \
    .matchFlags = USB_DEVICE_ID_MATCH_VENDOR | \
            USB_DEVICE_ID_MATCH_PRODUCT | \
            USB_DEVICE_ID_MATCH_INT_NUMBER, \
    .idVendor = (vend), \
    .idProduct = (prod), \
    .bInterfaceNumber = (num)

#define USB_VENDOR_AND_INTERFACE_INFO(vend, cl, sc, pr) \
    .matchFlags = (USB_DEVICE_ID_MATCH_VENDOR | \
            USB_DEVICE_ID_MATCH_INT_CLASS | \
            USB_DEVICE_ID_MATCH_INT_SUBCLASS | \
            USB_DEVICE_ID_MATCH_INT_PROTOCOL), \
    .idVendor = (vend), \
    .bInterfaceClass = (cl), \
    .bInterfaceSubClass = (sc), \
    .bInterfaceProtocol = (pr)

// 设备ID列表
static const struct usb_device_id liotIds[] = 
{
    {USB_DEVICE_INTERFACE_NUMBER(0x3505, 0x1001, 4)},
    {USB_DEVICE_INTERFACE_NUMBER(0x3505, 0x1002, 4)},
    {USB_VENDOR_AND_INTERFACE_INFO(0x3505, 0xff, 0x03, 0x12)},
};

// 全局变量
static char g_devPath[128] = {0};
static int g_epIn;
static int g_epOut;
static int g_epLen;
static int g_interface;
static int g_fd = -1;
static volatile int g_running = 1;
static struct ResponseBuffer g_response = {0};
static pthread_t g_readThread;

// 匹配设备ID
int MatchId(struct usb_device_id *deviceId)
{
    for(int i = 0; i < sizeof(liotIds) / sizeof(struct usb_device_id); i++) {
        if(liotIds[i].matchFlags & USB_DEVICE_ID_MATCH_VENDOR) {
            if(deviceId->idVendor != liotIds[i].idVendor) continue;
        }
        if(liotIds[i].matchFlags & USB_DEVICE_ID_MATCH_PRODUCT) {
            if(deviceId->idProduct != liotIds[i].idProduct) continue;
        }
        if(liotIds[i].matchFlags & USB_DEVICE_ID_MATCH_INT_NUMBER) {
            if(deviceId->bInterfaceNumber != liotIds[i].bInterfaceNumber) continue;
        }
        if(liotIds[i].matchFlags & USB_DEVICE_ID_MATCH_INT_CLASS) {
            if(deviceId->bInterfaceClass != liotIds[i].bInterfaceClass) continue;
        }
        if(liotIds[i].matchFlags & USB_DEVICE_ID_MATCH_INT_SUBCLASS) {
            if(deviceId->bInterfaceSubClass != liotIds[i].bInterfaceSubClass) continue;
        }
        if(liotIds[i].matchFlags & USB_DEVICE_ID_MATCH_INT_PROTOCOL) {
            if(deviceId->bInterfaceProtocol != liotIds[i].bInterfaceProtocol) continue;
        }
        return 1;
    }
    return 0;
}

// 获取设备描述符
int Liot_GetDesc(char *devPath)
{
    int fd = open(devPath, O_RDWR | O_NOCTTY);
    if(fd < 0) {
        printf("%s open failed: %s\n", devPath, strerror(errno));
        return -1;
    }

    uint8_t *desc = (uint8_t *)malloc(2048);
    if(!desc) {
        printf("malloc failed\n");
        close(fd);
        return -1;
    }
    memset(desc, 0, 2048);

    int descLen = read(fd, desc, 2048);
    int len = 0;
    struct usb_device_id deviceId = {0};

    while(len < descLen) {
        struct usb_descriptor_header *descHeader = (struct usb_descriptor_header *)(desc + len);
        if(descHeader->bLength == 0) break;
        
        if((descHeader->bLength == USB_DT_DEVICE_SIZE) && 
           (descHeader->bDescriptorType == USB_DT_DEVICE)) {
            struct usb_device_descriptor *DevDesc = (struct usb_device_descriptor *)descHeader;
            deviceId.idVendor = DevDesc->idVendor;
            deviceId.idProduct = DevDesc->idProduct;
        }
        else if((descHeader->bLength == USB_DT_INTERFACE_SIZE) && 
                (descHeader->bDescriptorType == USB_DT_INTERFACE)) {
            struct usb_interface_descriptor *interfaceDesc = (struct usb_interface_descriptor *)descHeader;
            deviceId.bInterfaceNumber = interfaceDesc->bInterfaceNumber;
            deviceId.bInterfaceClass = interfaceDesc->bInterfaceClass;
            deviceId.bInterfaceSubClass = interfaceDesc->bInterfaceSubClass;
            deviceId.bInterfaceProtocol = interfaceDesc->bInterfaceProtocol;
        }
        else if((descHeader->bLength == USB_DT_ENDPOINT_SIZE) && 
                (descHeader->bDescriptorType == USB_DT_ENDPOINT)) {
            struct usb_endpoint_descriptor *epDesc = (struct usb_endpoint_descriptor *)descHeader;
            if(MatchId(&deviceId)) {
                if((epDesc->bmAttributes & USB_ENDPOINT_XFERTYPE_MASK) == USB_ENDPOINT_XFER_BULK) {
                    if(epDesc->bEndpointAddress & USB_ENDPOINT_DIR_MASK) {
                        g_epIn = epDesc->bEndpointAddress;
                    } else {
                        g_epOut = epDesc->bEndpointAddress;
                        g_epLen = epDesc->wMaxPacketSize;
                        g_interface = deviceId.bInterfaceNumber;
                        free(desc);
                        close(fd);
                        return 0;
                    }
                }
            }
        }
        len += descHeader->bLength;
    }
    
    free(desc);
    close(fd);
    return -1;
}

// 查找设备
int Liot_FindDevice(const char *dirPath)
{
    DIR *dir = opendir(dirPath);
    if(dir == NULL) {
        printf("%s open failed: %s\n", dirPath, strerror(errno));
        return -1;
    }

    struct dirent *entry;
    char pathTemp[256];
    char devPath[128];

    while((entry = readdir(dir)) != NULL) {
        if(!isdigit(entry->d_name[0])) continue;
        
        if(entry->d_type == DT_DIR) {
            snprintf(pathTemp, sizeof(pathTemp), "%s/%s", dirPath, entry->d_name);
            if(!Liot_FindDevice(pathTemp)) {
                closedir(dir);
                return 0;
            }
        } else {
            snprintf(devPath, sizeof(devPath), "%s/%s", dirPath, entry->d_name);
            if(!Liot_GetDesc(devPath)) {
                strcpy(g_devPath, devPath);
                closedir(dir);
                return 0;
            }
        }
    }
    
    closedir(dir);
    return -1;
}

// 控制设置
void ControlSetup(int fd)
{
    struct usbdevfs_ctrltransfer control = {
        .bRequestType = 0x21,
        .bRequest = 0x22,
        .wValue = 0x0003,
        .wIndex = 2,
        .wLength = 0,
        .data = NULL
    };

    ioctl(fd, USBDEVFS_CONTROL, &control);
}

// 打开设备
int Liot_OpenDev(int *fd)
{
    struct usbdevfs_getdriver usbDev = {.interface = g_interface};

    *fd = open(g_devPath, O_RDWR | O_NOCTTY);
    if(*fd < 0) {
        printf("USB dev open fail: %s\n", strerror(errno));
        return -1;
    }

    int ret = ioctl(*fd, USBDEVFS_GETDRIVER, &usbDev);
    if(ret && errno != ENODATA) {
        printf("USBDEVFS_GETDRIVER error: %s\n", strerror(errno));
        close(*fd);
        return -1;
    } else if(ret == 0) {
        struct usbdevfs_ioctl operate = {
            .data = NULL,
            .ifno = g_interface,
            .ioctl_code = USBDEVFS_DISCONNECT
        };

        ret = ioctl(*fd, USBDEVFS_IOCTL, &operate);
        if(ret) {
            printf("USBDEVFS_DISCONNECT error: %s\n", strerror(errno));
            close(*fd);
            return -1;
        }
    }

    ret = ioctl(*fd, USBDEVFS_CLAIMINTERFACE, &g_interface);
    if(ret) {
        printf("USBDEVFS_CLAIMINTERFACE error: %s\n", strerror(errno));
        close(*fd);
        return -1;
    }

    ControlSetup(*fd);
    return 0;
}

// 关闭设备
void Liot_CloseDev(int *fd)
{
    if(*fd >= 0) {
        close(*fd);
        *fd = -1;
    }
}

// 批量输出
int BulkOut(int fd, const uint8_t *data, int len)
{
    struct usbdevfs_bulktransfer bulk = {
        .ep = g_epOut,
        .len = len,
        .data = (void *)data,
        .timeout = 5000  // 5 seconds
    };

    int ret = ioctl(fd, USBDEVFS_BULK, &bulk);
    if(ret != len) {
        if(ret < 0) {
            printf("Bulk out error: %s\n", strerror(errno));
        } else {
            printf("Bulk out partial: sent %d of %d bytes\n", ret, len);
        }
        return -1;
    }
    return 0;
}

// 批量输入
int BulkIn(int fd, uint8_t *data, int len, int timeout)
{
    struct usbdevfs_bulktransfer bulk = {
        .ep = g_epIn | 0x80,
        .len = len,
        .data = data,
        .timeout = timeout
    };

    int ret = ioctl(fd, USBDEVFS_BULK, &bulk);
    if(ret < 0) {
        if(errno != ETIMEDOUT && errno != EAGAIN) {
            perror("Bulk in error");
        }
        return -1;
    }
    return ret;
}

// 初始化响应缓冲区
void InitResponseBuffer(struct ResponseBuffer *buf)
{
    memset(buf->data, 0, sizeof(buf->data));
    buf->length = 0;
    memset(buf->currentCmd, 0, sizeof(buf->currentCmd));
    buf->status = CMD_PENDING;
    pthread_mutex_init(&buf->mutex, NULL);
    pthread_cond_init(&buf->cond, NULL);
}

// 重置响应缓冲区
void ResetResponseBuffer(struct ResponseBuffer *buf)
{
    pthread_mutex_lock(&buf->mutex);
    buf->data[0] = '\0';
    buf->length = 0;
    buf->currentCmd[0] = '\0';
    buf->status = CMD_PENDING;
    pthread_mutex_unlock(&buf->mutex);
}

// 添加数据到响应缓冲区
void AppendToResponseBuffer(struct ResponseBuffer *buf, const char *data, size_t length)
{
    pthread_mutex_lock(&buf->mutex);
    
    // 确保缓冲区有足够空间
    size_t space_remaining = sizeof(buf->data) - buf->length - 1;
    if(length > space_remaining) {
        length = space_remaining;
    }
    
    // 追加数据
    if(length > 0) {
        memcpy(buf->data + buf->length, data, length);
        buf->length += length;
        buf->data[buf->length] = '\0';
        
        // 检查响应状态
        char *okPos = strstr(buf->data, "\r\nOK\r\n");
        char *errorPos = strstr(buf->data, "\r\nERROR\r\n");
        char *cmdEnd = strstr(buf->data, buf->currentCmd);
        
        // 确保响应是针对当前命令
        if(cmdEnd != NULL) {
            // 找到命令响应结束标记
            if(okPos != NULL && (okPos - buf->data) > (cmdEnd - buf->data)) {
                buf->status = CMD_SUCCESS;
                pthread_cond_signal(&buf->cond);
            }
            else if(errorPos != NULL && (errorPos - buf->data) > (cmdEnd - buf->data)) {
                buf->status = CMD_ERROR;
                pthread_cond_signal(&buf->cond);
            }
        }
    }
    
    pthread_mutex_unlock(&buf->mutex);
}

// 读取线程 - 处理接收数据
void *ReadThread(void *arg)
{
    uint8_t buffer[1024];
    char filtered[1024];
    char *saveptr = NULL;
    
    while(g_running) {
        int bytesRead = BulkIn(g_fd, buffer, sizeof(buffer) - 1, 500);
        
        if(bytesRead > 0) {
            buffer[bytesRead] = '\0';
            char *filterPtr = filtered;
            *filterPtr = '\0';
            
            // 逐行处理输入
            char *line = strtok_r((char *)buffer, "\n", &saveptr);
            while(line != NULL) {
                // 去除回车符
                char *cleanLine = line;
                while(*cleanLine == '\r') cleanLine++;
                
                // 过滤 RSSI 报告
                if(strncmp(cleanLine, "^RSSI:", 6) == 0) {
                    // 完全跳过 RSSI 行
                }
                // 保留所有其他数据
                else if(strlen(cleanLine) > 0) {
                    // 添加到过滤后缓冲区
                    size_t lineLen = strlen(cleanLine);
                    size_t remaining = sizeof(filtered) - (filterPtr - filtered) - 2; // 为换行符预留空间
                    
                    if(remaining > lineLen) {
                        strcpy(filterPtr, cleanLine);
                        filterPtr += lineLen;
                        *filterPtr++ = '\n';
                        *filterPtr = '\0';
                    }
                }
                
                line = strtok_r(NULL, "\n", &saveptr);
            }
            
            // 显示过滤后的数据
            if(strlen(filtered) > 0) {
                printf("%s", filtered);
                fflush(stdout);
                
                // 添加到响应缓冲区
                AppendToResponseBuffer(&g_response, filtered, filterPtr - filtered);
            }
        }
        else if(bytesRead < 0 && errno != ETIMEDOUT && errno != EAGAIN) {
            break;
        }
    }
    
    return NULL;
}

// 发送AT命令
int SendAtCmd(const char *atCmd, int timeout_ms)
{
    // 空命令处理 - 直接返回成功
    if(atCmd == NULL || *atCmd == '\0') {
        return 0;
    }
    
    // 准备完整的命令
    char fullCmd[256];
    snprintf(fullCmd, sizeof(fullCmd), "%s\r", atCmd);
    size_t cmdLen = strlen(fullCmd);
    
    printf("\033[0;36mSending: %s\033[0m\n", fullCmd);
    
    // 重置缓冲区状态
    ResetResponseBuffer(&g_response);
    strncpy(g_response.currentCmd, atCmd, sizeof(g_response.currentCmd) - 1);
    clock_gettime(CLOCK_REALTIME, &g_response.startTime);
    
    // 发送命令
    if(BulkOut(g_fd, (const uint8_t *)fullCmd, cmdLen) != 0) {
        printf("\033[0;31mFailed to send AT command\033[0m\n");
        return -1;
    }
    
    struct timespec ts;
    clock_gettime(CLOCK_REALTIME, &ts);
    ts.tv_sec += timeout_ms / 1000;
    ts.tv_nsec += (timeout_ms % 1000) * 1000000;
    if(ts.tv_nsec >= 1000000000) {
        ts.tv_sec++;
        ts.tv_nsec -= 1000000000;
    }
    
    pthread_mutex_lock(&g_response.mutex);
    g_response.status = CMD_PENDING;
    
    // 等待响应完成或超时
    while(g_response.status == CMD_PENDING && g_running) {
        int ret = pthread_cond_timedwait(&g_response.cond, &g_response.mutex, &ts);
        if(ret == ETIMEDOUT) {
            g_response.status = CMD_TIMEOUT;
            break;
        }
    }
    
    CommandStatus result = g_response.status;
    pthread_mutex_unlock(&g_response.mutex);
    
    // 处理结果
    switch(result) {
        case CMD_SUCCESS:
            // 输出响应内容
            printf("%s", g_response.data);
            printf("\033[0;32mCommand succeeded\033[0m\n");
            return 0;
        case CMD_ERROR:
            // 输出错误信息
            printf("%s", g_response.data);
            printf("\033[0;31mCommand returned ERROR\033[0m\n");
            return -1;
        case CMD_TIMEOUT:
            printf("\033[0;33mTimeout waiting for response\033[0m\n");
            return -1;
        case CMD_PENDING:
        default:
            printf("\033[0;33mCommand status unknown\033[0m\n");
            return -1;
    }
}

// 信号处理
void SignalHandler(int signum)
{
    printf("\nReceived signal %d, exiting...\n", signum);
    g_running = 0;
}

// 主函数
int main(void)
{
    // 设置信号处理
    signal(SIGINT, SignalHandler);
    signal(SIGTERM, SignalHandler);
    
    printf("Starting USB AT Commander...\n");
    
    // 查找设备
    if(Liot_FindDevice(USB_DEV_DIR)) {
        printf("\033[0;31mDevice not found!\033[0m\n");
        return 1;
    }
    
    // 打开设备
    if(Liot_OpenDev(&g_fd)) {
        printf("\033[0;31mFailed to open device\033[0m\n");
        return 1;
    }
    printf("Device opened successfully\n");
    
    // 初始化响应缓冲区
    InitResponseBuffer(&g_response);
    
    // 创建读取线程
    if(pthread_create(&g_readThread, NULL, ReadThread, NULL) != 0) {
        perror("\033[0;31mFailed to create read thread\033[0m");
        Liot_CloseDev(&g_fd);
        return 1;
    }
    printf("Read thread started\n");
    
    // 等待读取线程初始化
    sleep(1);
    
    // 主命令循环
    char input[256];
    int exitFlag = 0;
    
    while(g_running && !exitFlag) {
        printf("\033[0;33mAT>\033[0m ");
        fflush(stdout);
        
        // 读取用户输入
        if(fgets(input, sizeof(input), stdin) == NULL) {
            break; // EOF或错误
        }
        
        // 移除换行符
        input[strcspn(input, "\r\n")] = '\0';
        
        // 空输入处理
        if(input[0] == '\0') {
            printf("\n");
            continue;
        }
        
        // 检查退出命令
        if(strcmp(input, "exit") == 0 || strcmp(input, "quit") == 0) {
            exitFlag = 1;
            continue;
        }
        
        // 发送命令
        int result = SendAtCmd(input, 5000); // 5秒超时
        
        if(result != 0) {
            // 失败时不立即退出，允许用户重新尝试或输入其他命令
            printf("\033[0;31mCommand execution failed: %s\033[0m\n", input);
        }
    }
    
    // 清理
    g_running = 0;
    pthread_join(g_readThread, NULL);
    Liot_CloseDev(&g_fd);
    
    printf("\nUSB AT Commander exited cleanly\n");
    return 0;
}