#!/bin/bash

# 定义要操作的GPIO引脚列表
PINS=(4 24 27 29 23)

# 导出所有GPIO引脚
for pin in "${PINS[@]}"; do
    if [ ! -d "/sys/class/gpio/gpio${pin}" ]; then
        echo "导出 GPIO${pin}"
        echo "${pin}" > /sys/class/gpio/export
        sleep 0.1  # 等待系统创建目录
    fi
done

# 设置GPIO方向为输出
for pin in "${PINS[@]}"; do
    echo "设置 GPIO${pin} 为输出模式"
    echo "out" > /sys/class/gpio/gpio${pin}/direction
done

# 设置初始电平状态
echo "设置初始电平："
echo "GPIO27 -> 高电平"
echo 1 > /sys/class/gpio/gpio27/value

for pin in 24 29 23; do
    echo "GPIO${pin} -> 低电平"
    echo 0 > /sys/class/gpio/gpio${pin}/value
done

# 控制GPIO4脉冲
echo "生成GPIO4脉冲..."
echo 1 > /sys/class/gpio/gpio4/value
sleep 0.1
echo 0 > /sys/class/gpio/gpio4/value

echo "操作完成"
