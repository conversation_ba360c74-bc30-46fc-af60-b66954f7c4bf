#!/bin/bash

# filepath: /mine/code/robot-application/send_peripherals_ctrl.sh
source /opt/ros/foxy/setup.bash
source install/setup.bash

# 定义服务名称
SERVICE_NAME="robdog_control/peripherals_ctrl"

# 定义服务类型
SERVICE_TYPE="homi_speech_interface/srv/PeripheralsCtrl"

# 定义请求数据
REQUEST_DATA='{"data": "{\"command\": \"update_pwm\", \"data\": {\"red\": 7550, \"green\": 8280, \"blue\": 64}}"}'

# 调用服务
ros2 service call $SERVICE_NAME $SERVICE_TYPE "$REQUEST_DATA"