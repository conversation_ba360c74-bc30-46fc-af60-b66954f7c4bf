#!/bin/bash

g_root_of_script=$(dirname $(readlink -f "$0"))
g_install_path=/usr/bin/cmcc_robot/install

# 检查是否由systemd启动
if [ -n "$INVOCATION_ID" ] || [ -n "$JOURNAL_STREAM" ]; then
    echo "脚本是由systemd运行的。"
else
    echo "脚本是手动运行的。"
fi

SCRIPT_DIR=$(dirname "$0")
# 将目录名转换为绝对路径
SCRIPT_DIR=$(cd "$SCRIPT_DIR" && pwd)
echo "$SCRIPT_DIR"

PARENT_DIR=$(dirname "$SCRIPT_DIR")
GRANDPARENT_DIR=$(dirname "$PARENT_DIR")

echo "当前脚本所在目录: $SCRIPT_DIR"
echo "上上级文件夹: $GRANDPARENT_DIR"

source /opt/ros/foxy/setup.bash
source ${g_install_path}/setup.bash

echo "start ros2 run pwm_touch_pkg pwm_touch_node"
ros2  ros2 run pwm_touch_pkg pwm_touch_node

echo "start ros2 run peripherals_node peripherals_node"
ros2 run peripherals_node peripherals_node