#!/bin/bash

g_install_path=/usr/bin/cmcc_robot/install
g_install_dst_srv_path=${g_install_path}/env/service/andlink

sleep 5
${g_install_dst_srv_path}/board_resources/wifi/wifi_ctl.sh enable
sleep 1
# 循环检查 /sys/class/net/wlan0 是否存在
while true; do
    if [ -d "/sys/class/net/wlan0" ]; then
        echo "wlan0 启动成功 "
        break
    else
	    ${g_install_dst_srv_path}/board_resources/wifi/wifi_ctl.sh disable
	    sleep 1
	    echo "wlan0 重启"
	    ${g_install_dst_srv_path}/board_resources/wifi/wifi_ctl.sh enable
    fi
    sleep 1  # 等待一秒
done

