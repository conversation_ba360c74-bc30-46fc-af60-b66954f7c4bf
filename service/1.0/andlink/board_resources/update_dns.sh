#!/bin/bash

# 获取当前时间，用于备份文件命名
timestamp=$(date +%Y-%m-%d-%H-%M-%S)
echo "$timestamp"

function run_sudo_command() {
    local password="$1"
    local command="$2"
    echo "$password" | sudo -S bash -c "$command"
    if [ $? -ne 0 ]; then
        error_message=$(echo "$password" | sudo -S bash -c "$command" 2>&1)
        echo "An error occurred: $error_message"
    else
        echo "Command executed successfully."
    fi
}

sudoPassword="temppwd"

# 备份 /etc/hosts 文件
run_sudo_command "$sudoPassword" "cp /etc/hosts ./hosts.bak"
echo "已备份/etc/hosts为当前路径下的./hosts.bak"

# 禁用并停止systemd-resolved服务
echo "禁用并停止systemd-resolved服务..."
run_sudo_command "$sudoPassword" "systemctl disable systemd-resolved"
sleep 2
run_sudo_command "$sudoPassword" "systemctl stop systemd-resolved"
sleep 2

# 修改/etc/NetworkManager/NetworkManager.conf配置文件
echo "修改/etc/NetworkManager/NetworkManager.conf配置文件..."
run_sudo_command "$sudoPassword" 'cat > /etc/NetworkManager/NetworkManager.conf <<EOF
[main]
plugins=ifupdown,keyfile
dns=default
[ifupdown]
managed=false
[device]
wifi.scan-rand-mac-address=no
EOF'

echo "删除/etc/resolv.conf"
run_sudo_command "$sudoPassword" "unlink /etc/resolv.conf"
echo " touch /etc/resolv.conf"
run_sudo_command "$sudoPassword" "touch /etc/resolv.conf"

# 重启NetworkManager服务
echo "重启NetworkManager服务..."
run_sudo_command "$sudoPassword" "systemctl restart NetworkManager"
sleep 2

# 检查/etc/resolv.conf中的DNS是否为*********
echo "检查/etc/resolv.conf中的DNS设置..."
if grep -q "nameserver *********" /etc/resolv.conf; then
    echo "DNS设置正确"
else
    echo "DNS 更新失败"
fi

# 获取主机名，并将其与*********一起写入/etc/hosts
hostname=$(cat /etc/hostname)
echo "获取到的主机名为: $hostname"

# 还原 /etc/hosts 文件
run_sudo_command "$sudoPassword" "cp ./hosts.bak /etc/hosts"
echo "/etc/hosts 已从备份恢复"

run_sudo_command "$sudoPassword" "echo '********* $hostname' >> /etc/hosts"

# 再次重启NetworkManager服务以确保所有更改生效
echo "再次重启NetworkManager服务以确保所有更改生效..."
run_sudo_command "$sudoPassword" "systemctl restart NetworkManager"
sleep 2

echo "已将 '********* $hostname' 添加到/etc/hosts文件末尾"
echo "脚本执行完毕！"