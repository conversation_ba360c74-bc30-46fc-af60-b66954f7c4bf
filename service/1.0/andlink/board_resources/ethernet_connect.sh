#!/bin/bash

# 检查yq是否安装
if ! command -v yq &>/dev/null; then
    echo "错误：yq 未安装，请使用以下命令安装："
    echo "pip3方式：sudo pip3 install yq"
    echo "或手动安装：访问 https://github.com/mikefarah/yq/releases"
    exit 1
fi

# 检查netplan配置文件
CONFIG_FOUND=0
for FILE in /etc/netplan/*.yaml; do
    if [ -f "$FILE" ]; then
        # 检查是否存在eth1的静态配置
        if yq -e '.network.ethernets.eth1.addresses[] | select(. == "*************/24")' "$FILE" >/dev/null 2>&1 && \
           yq -e '.network.ethernets.eth1.nameservers.addresses[] | select(. == "*********")' "$FILE" >/dev/null 2>&1; then

            CONFIG_FOUND=1
            echo "发现符合条件的配置，正在修改文件: $FILE"

            # 备份原文件
            BACKUP_FILE="${FILE}.bak_$(date +%s)"
            sudo cp "$FILE" "$BACKUP_FILE"

            # 使用yq删除静态配置并启用DHCP
            sudo yq -iy '
                del(.network.ethernets.eth1.addresses) |
                del(.network.ethernets.eth1.nameservers) |
                .network.ethernets.eth1.dhcp4 = true
            ' "$FILE"


            # 应用网络配置
            sudo netplan apply
            echo "已成功应用新的网络配置"

            # 检测并添加iptables规则（新增检测逻辑）
            if ! sudo iptables -t nat -C POSTROUTING -s ***********/24 -o eth1 -j MASQUERADE 2>/dev/null; then
                sudo iptables -t nat -A POSTROUTING -s ***********/24 ! -d ***********/24 -j MASQUERADE
                sudo iptables -t nat -A POSTROUTING -s ***********/24 -o eth1 -j MASQUERADE
                sysctl -w net.ipv4.ip_forward=1
                echo "已添加iptables NAT规则，并开启三层转发"
            else
                echo "iptables规则已存在，无需重复添加"
            fi
        fi
    fi
done

# 结果反馈
if [ $CONFIG_FOUND -eq 0 ]; then
    echo "未找到包含以下配置的netplan文件："
    echo "ethernets:"
    echo "  eth1:"
    echo "    addresses: [*************/24]"
    echo "    nameservers: {addresses: [***************]}"
fi
