#!/bin/bash

# 检查是否传入参数
if [ $# -lt 1 ]; then
    echo "错误：必须指定AP接口名称作为参数。"
    echo "用法：$0 <AP_INTERFACE>"
    exit 1
fi

# developer mode default value is 1 TODO: we should set default value to 0
developer_mode=1
if [ -f /etc/cmcc_cfgs_save/developer_mode ];then
    developer_mode=$(cat /etc/cmcc_cfgs_save/developer_mode)
fi
if [ "$developer_mode" != 1 ];then
    echo "developer mode: off, do not start ap"
    exit 0
fi

AP_INTERFACE="$1"
SCRIPT_PATH=$(dirname "$(readlink -f "$0")")
password="'"  # 新增密码变量

# 新增 sudo 执行函数 
run_sudo_command() {
    local password="$1"
    local command="$2"
    
    # 执行命令并捕获输出
    if ! echo "$password" | sudo -S sh -c "$command" 2>/dev/null; then
        # 失败时显示详细错误
        error_message=$( { echo "$password"; echo "$command"; } | sudo -S sh -c "$command" 2>&1 )
        echo "执行失败: $error_message"
        return 1
    else
        echo "命令执行成功: $command"
        return 0
    fi
}

sleep 15

get_ssid() {
    local interface=$1
    local ssid=$(iw dev "$interface" info | grep "ssid" | awk '{print $2}')
    
    if [ -n "$ssid" ]; then
        echo "$ssid"
        return 0
    else
        echo "ERROR: No SSID found for interface $interface"
        return 1
    fi
}

get_interface_ip() {
    local interface=$1
    local ip_address=$(ip -4 addr show $interface | grep -oP '(?<=inet\s)\d+(\.\d+){3}')
    
    if [ -n "$ip_address" ]; then
        echo "$ip_address"
        return 0
    else
        echo "ERROR: No IP address found for interface $interface"
        return 1
    fi
}

get_interface_status() {
    local interface=$1
    local status=$(ip link show $interface | grep -oP '(?<=state )\w+')
    
    if [ "$status" = "UP" ]; then
        echo "UP"
        return 0
    else
        echo "ERROR: Interface $interface is $status"
        return 1
    fi
}

nmcli dev set "$AP_INTERFACE" managed no
iw dev "$AP_INTERFACE" set type __ap

ap_status=$(get_interface_status "$AP_INTERFACE")
if [ $? -eq 1 ]; then
    bash "$SCRIPT_PATH/wifi/ap_start.sh" start 5G "$AP_INTERFACE"
fi

TXPOWER=$(iw dev "$AP_INTERFACE" info | grep -oiP 'txpower\s*\K[\d.-]+' | head -n 1)

if [[ -z "$TXPOWER" ]]; then
    echo "未能获取发射功率值,请检查${AP_INTERFACE}网络接口状态。"
fi

if (( $(echo "$TXPOWER >= 0 && $TXPOWER <= 30" | bc -l) )); then
    echo "发射功率正常：$TXPOWER dBm,${AP_INTERFACE} 创建成功。"
else
    echo "发射功率异常：$TXPOWER dBm,将尝试重新配置 AP..."
    bash "$SCRIPT_PATH/wifi/ap_start.sh" start 24G "$AP_INTERFACE"
fi

echo "${AP_INTERFACE} txpower:$TXPOWER"

while true
do
    ap_ssid=$(get_ssid "$AP_INTERFACE")
    if [ $? -eq 0 ]; then
        echo "ap_ssid is $ap_ssid"
        ap_addr=$(get_interface_ip "$AP_INTERFACE")
        if [ $? -eq 1 ]; then
            bash "$SCRIPT_PATH/wifi/p2p_ip_set.sh" "$AP_INTERFACE"
        else
            echo "ap_ip is $ap_addr"
            break
        fi
    else
        echo "ap ssid is not set"
    fi
    echo "循环执行中..."
    sleep 5
done

#dnsmasq.conf是否存在并且可以动态配置interface，iptables规则两条（动态获取wlan0的ip对应的网段），三层转发
CONF_SOURCE="${SCRIPT_PATH}/wifi/dnsmasq.conf"
TARGET_CONF="/etc/dnsmasq.conf"

# 检查并复制配置文件（如果不存在）
if [ ! -f "$TARGET_CONF" ]; then
    echo "检测到目标配置文件不存在，开始部署..."
    run_sudo_command "$password" "cp -v \"$CONF_SOURCE\" \"$TARGET_CONF\"" || exit 1
fi

# 清理变量值中的特殊字符
CURRENT_INTERFACE=$(run_sudo_command "$password" "grep -m1 '^interface=' \"$TARGET_CONF\" | cut -d= -f2 | tr -d ' \n'")
AP_INTERFACE_CLEAN=$(echo "$AP_INTERFACE" | tr -d ' \n')  # 清理输入变量

if [ "$CURRENT_INTERFACE" = "$AP_INTERFACE_CLEAN" ]; then
    echo "配置中的interface已为${AP_INTERFACE_CLEAN}，无需修改。"
else
    # 删除所有旧的interface配置
    run_sudo_command "$password" "sed -i '\|^interface=|d' \"$TARGET_CONF\""

    # 在第一行插入新配置
    run_sudo_command "$password" "sed -i '1iinterface=${AP_INTERFACE_CLEAN}' \"$TARGET_CONF\""

    # 确保文件末尾有换行符
    run_sudo_command "$password" "sed -i -e '\$a\\' \"$TARGET_CONF\""

    echo "已将interface设置为${AP_INTERFACE_CLEAN}，并置于文件$TARGET_CONF首行。"
fi

P2P0_SEGMENT=$(bash "$SCRIPT_PATH/get_net_segment.sh" $AP_INTERFACE)
echo " $AP_INTERFACE 网段： ${P2P0_SEGMENT}"
run_sudo_command "$password" "iptables -t nat -A POSTROUTING -s ${P2P0_SEGMENT} -o wlan0 -j MASQUERADE"

sysctl -w net.ipv4.ip_forward=1
