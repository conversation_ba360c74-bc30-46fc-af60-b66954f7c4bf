#!/bin/bash

SCRIPT_PATH=$(dirname "$(readlink -f "$0")")

sleep 15
AP_INTERFACE="p2p0"

get_ssid() {
    local interface=$1
    local ssid=$(iw dev "$interface" info | grep "ssid" | awk '{print $2}')
    
    if [ -n "$ssid" ]; then
        echo "$ssid"
        return 0
    else
        echo "ERROR: No SSID found for interface $interface"
        return 1
    fi
}

get_interface_ip() {
    local interface=$1
    local ip_address=$(ip -4 addr show $interface | grep -oP '(?<=inet\s)\d+(\.\d+){3}')
    
    if [ -n "$ip_address" ]; then
        echo "$ip_address"
        return 0
    else
        echo "ERROR: No IP address found for interface $interface"
        return 1
    fi
}


get_interface_status() {
    local interface=$1
    local status=$(ip link show $interface | grep -oP '(?<=state )\w+')
    
    if [ "$status" = "UP" ]; then
        echo "UP"
        return 0
    else
        echo "ERROR: Interface $interface is $status"
        return 1
    fi
}


nmcli dev set $AP_INTERFACE managed no
#ip link set p2p0 up
iw dev $AP_INTERFACE set type __ap

#未配置需要配置ap
ap_status=$(get_interface_status "$AP_INTERFACE")
if [ $? -eq 1 ]; then
    bash  $SCRIPT_PATH/wifi/ap_start.sh start 5G
fi

#检测当前ap配置是否异常
# 获取当前的发射功率值
TXPOWER=$(iw dev p2p0 info | grep -oiP 'txpower\s*\K[\d.-]+' | head -n 1)

# 检查是否成功获取到了发射功率值
if [[ -z "$TXPOWER" ]]; then
    echo "未能获取发射功率值,请检查p2p0网络接口状态。"
    
fi

if (( $(echo "$TXPOWER >= 0 && $TXPOWER <= 30" | bc -l) )); then
    echo "发射功率正常：$TXPOWER dBm,p2p0 创建成功。"
else
    echo "发射功率异常：$TXPOWER dBm,将尝试重新配置 AP..."
    bash  $SCRIPT_PATH/wifi/ap_start.sh start 24G

fi

TXPOWER=$(iw dev p2p0 info | grep -oiP 'txpower\s*\K[\d.-]+' | head -n 1)

echo "p2p0 txpower:$TXPOWER"

while true
do
    ap_ssid=$(get_ssid "$AP_INTERFACE")
    if [ $? -eq 0 ]; then
        echo "ap_ssid is $ap_ssid"
        # ...
        ap_addr=$(get_interface_ip "$AP_INTERFACE")
        if [ $? -eq 1 ]; then
            bash  $SCRIPT_PATH/wifi/p2p_ip_set.sh
        else
            echo "ap_ip is $ap_addr"
            break
        fi
    else
        echo "ap ssid is not set"
    fi
    echo "循环执行中..."
    sleep 5
done
