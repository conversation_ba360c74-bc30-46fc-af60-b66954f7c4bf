#!/bin/bash

SCRIPT_PATH=$(dirname "$(readlink -f "$0")")

LOCAL=0

if [ -n "$1" ]; then
    LOCAL=$1
fi

network_status=wifi

INTERFACE="p2p0"
MAX_ATTEMPTS=5

bash $SCRIPT_PATH/5G/5G_ctl.sh enable

sleep 7

nmcli dev set $INTERFACE managed no
#ip link set p2p0 up
iw dev p2p0 set type __ap
bash  $SCRIPT_PATH/wifi/ap_start.sh start 5G
bash $SCRIPT_PATH/route.sh wifi

while :
do
SSID=$(iw dev $INTERFACE info | grep "ssid" | awk '{print $2}')
if [ -n "$SSID" ]; then
	echo "ssid is $SSID"
        break
fi
sleep 4
bash  $SCRIPT_PATH/wifi/ap_start.sh start 5G
done


bash  $SCRIPT_PATH/wifi/p2p_ip_set.sh
bash  $SCRIPT_PATH/wifi/iptables_wifi.sh

ATTEMPT=0
while [ $ATTEMPT -lt $MAX_ATTEMPTS ]
do
	IPV4=$(ip -4 addr show $INTERFACE | grep -oP '(?<=inet\s)\d+(\.\d+){3}')
	if [ -n "$IPV4" ]; then
    		break
	fi
	sleep 2
	bash  $SCRIPT_PATH/wifi/p2p_ip_set.sh
    ATTEMPT=$((ATTEMPT + 1))
done

ATTEMPT=0
while [ $ATTEMPT -lt $MAX_ATTEMPTS ]; do
    SSID=$(iw dev $INTERFACE info | grep "ssid" | awk '{print $2}')
    if [ -z "$SSID" ]; then
        echo "SSID not found, restarting AP (Attempt: $((ATTEMPT + 1)))"
        bash  $SCRIPT_PATH/wifi/ap_start.sh start 5G
    else
        echo "SSID found: $SSID"
        break
    fi
    sleep 2
    ATTEMPT=$((ATTEMPT + 1))
done


INTERFACE="wlan0"
TARGET="www.baidu.com"

file_path=$SCRIPT_PATH/network.conf

read_json() {
    local key="$1"
    jq -r ".${key}" "$file_path"
}

modify_json() {
	local key=$1
	local value=$2
    jq --arg val "$value" ".$key = \$val" $file_path > tmp.$$.json && mv tmp.$$.json $file_path
}

check_ip() {
    IP_ADDRESS=$(ip -4 addr show $INTERFACE | grep -oP '(?<=inet\s)\d+(\.\d+){3}')
    if [ -n "$IP_ADDRESS" ]; then
        echo "网卡 $INTERFACE地址 check 已获取到 IP 地址: $IP_ADDRESS"
        ping -c 1 $TARGET -4 > /dev/null 2>&1

        if [ $? -eq 0 ]; then
            echo "WiFi网络连接正常"

            network_status=wifi
            modify_json "wifiState" "on"
            modify_json "mobileDataState" "off"
            modify_json "isWifiConnect" true
            wlan0_ssid=$(iw dev wlan0 info | grep "ssid" | awk '{print $2}')
            modify_json "wifiName" "$wlan0_ssid"

            if [ $LOCAL = 1 ]; then
                sleep 30
                #echo "配置5G香港模式"
                $SCRIPT_PATH/5G/LiotAT_HongKong
            fi
            
            
        else
            echo "WiFi网络异常,切换到5G模式"

            bash $SCRIPT_PATH/5G/iptables_5g.sh
            if [ $LOCAL = 1 ]; then
                #echo "配置5G香港模式"
                $SCRIPT_PATH/5G/LiotAT_HongKong
            fi
            bash $SCRIPT_PATH/route.sh 5g
            network_status=5g
            modify_json "wifiState" "off"
            modify_json "mobileDataState" "on"
            modify_json "isWifiConnect" false
        fi
       return 0
    else
        echo "网卡 $INTERFACE 未获取到 IP 地址"
        return 1
    fi
}


update_network_status() {
    local store_status="none"
    wifiState=$(read_json "wifiState")
    mobileDataState=$(read_json "mobileDataState")
    if [ "$wifiState" = "on" ]; then
        store_status=wifi
    elif [ "$mobileDataState" = "on" ]; then
        store_status=5g
    else
        store_status=none
    fi
    if [ "$store_status" != "$network_status" ]; then
        echo "两个参数不一致"
        if [ "$store_status" == "wifi" ]; then
            bash $SCRIPT_PATH/route.sh wifi
            bash  $SCRIPT_PATH/wifi/iptables_wifi.sh
            modify_json "isWifiConnect" true
            wlan0_ssid=$(iw dev wlan0 info | grep "ssid" | awk '{print $2}')
            modify_json "wifiName" "$wlan0_ssid"
        elif [ "$store_status" == "5g" ]; then
            bash $SCRIPT_PATH/route.sh 5g
            bash $SCRIPT_PATH/5G/iptables_5g.sh
            modify_json "isWifiConnect" false
        fi
        network_status=$store_status
    else
        echo "两个参数一致"
    fi
    bash $SCRIPT_PATH/route.sh $store_status 

}

ATTEMPT=0
while [ $ATTEMPT -le $MAX_ATTEMPTS ]; do
    echo "尝试 $ATTEMPT/$MAX_ATTEMPTS 获取 IP 地址..."
    bash $SCRIPT_PATH/route.sh wifi
    if check_ip; then
	while :
	do
		sleep 30
		echo "循环检测中1"
        update_network_status
	done
    fi
    sleep 2
    ATTEMPT=$((ATTEMPT + 1))
done

echo "经过 $MAX_ATTEMPTS 次尝试，网卡 $INTERFACE 仍未获取到 IP 地址"
echo "WiFi网络异常,切换到5G模式"
bash $SCRIPT_PATH/5G/5g_start.sh
if [ $LOCAL = 1 ]; then
#echo "配置5G香港模式"
    $SCRIPT_PATH/5G/LiotAT_HongKong
fi
bash $SCRIPT_PATH/route.sh 5g
bash $SCRIPT_PATH/5G/iptables_5g.sh
network_status=5g
modify_json "wifiState" "off"
modify_json "mobileDataState" "on"
modify_json "isWifiConnect" false

while :
do
	echo "循环检测中2"
    update_network_status
	sleep 30
done
