#!/bin/bash

sleep 30 

LOG_DIR="/home/<USER>/log"
LOG_FILE="$LOG_DIR/dbus_monitor.log"
mkdir -p "$LOG_DIR"
touch "$LOG_FILE"
chown ysc:ysc "$LOG_DIR" "$LOG_FILE"
chmod 644 "$LOG_FILE"

exec >> "$LOG_FILE" 2>&1

log() {
    local timestamp
    timestamp=$(date "+%Y-%m-%d %H:%M:%S")
    echo "[$timestamp] $1"
}

log "=====dbus_monitor 脚本启动 ====="

SCRIPT_PATH=$(dirname "$(readlink -f "$0")")

file_path=$SCRIPT_PATH/network.conf
LOCKFILE="/tmp/network_conf.lock"

acquire_lock() {
    local retries=5
    local delay=2
    for ((i=1; i<=retries; i++)); do
        exec 200>"$LOCKFILE"
        if flock -n 200; then
            return 0
        else
            log "尝试 $i/$retries 获取锁失败，等待 $delay 秒后重试。"
            sleep $delay
        fi
    done
    log "无法获取锁，已达到最大重试次数。"
    return 1
}

release_lock() {
    flock -u 200
    exec 200>&-
}

LOCAL=0
if [ -n "$1" ]; then
    LOCAL=$1
fi


DEFAULT_JSON='{
  "wifiState": "on",
  "mobileDataState": "off",
  "wifiName": "",
  "isWifiConnect": "true",
  "isInternetConnect":"true",
  "dbusMonitorWifi":"false",
  "dbusMonitor5g":"false"
}'

# 检查 network.conf 文件是否存在、非空且可读
if [ ! -s "$file_path" ] || [ ! -r "$file_path" ]; then
    log "Warning: $file_path is missing, empty or not readable. Recreating with default settings."
    
    # 使用锁确保原子性写入
    acquire_lock
    echo "$DEFAULT_JSON" > "$file_path"
    release_lock
    log "New $file_path created and written with default settings."
else
    log "$file_path exists and is readable."
fi

read_json() {
    local key="$1"
    acquire_lock
    value=$(jq -r ".$key" "$file_path")
    release_lock
    echo "$value"
}

modify_json() {
    local key=$1
    local value=$2
    acquire_lock
    jq --arg val "$value" ".$key = \$val" "$file_path" > tmp.$$.json && mv tmp.$$.json "$file_path"
    release_lock
}


dbusMonitorWifi=$(read_json "dbusMonitorWifi")
dbusMonitor5g=$(read_json "dbusMonitor5g")

log " 读取 dbusMonitorWifi 和 dbusMonitor5g 的初始值,dbusMonitorWifi:$dbusMonitorWifi ,dbusMonitor5g:$dbusMonitor5g"
# dbusMonitorWifi="true"
# dbusMonitor5g="false"
previousState_wifi=""
previousState_5g=""

# 初始化WiFi和eth2的设备路径为空
wlan0_path=""
eth2_path=""


# 获取所有设备对象路径
device_paths=$(gdbus call --system --dest org.freedesktop.NetworkManager --object-path /org/freedesktop/NetworkManager --method org.freedesktop.NetworkManager.GetDevices | grep -oP "/org/freedesktop/NetworkManager/Devices/\d+")

# 遍历每个设备对象路径，寻找wlan0和eth2的设备路径
for device in $device_paths; do
    # 获取接口名称
    interface_name=$(gdbus call --system --dest org.freedesktop.NetworkManager --object-path $device --method org.freedesktop.DBus.Properties.Get org.freedesktop.NetworkManager.Device Interface | grep -oP "'.*?'")
    
    # 去除单引号
    interface_name=${interface_name//\'/}

    # 检查是否为wlan0或eth2，并记录其设备路径
    if [[ "$interface_name" == "wlan0" ]]; then
        wlan0_path=$device
    elif [[ "$interface_name" == "eth2" ]]; then
        eth2_path=$device
    fi
done

# 输出找到的设备路径
if [[ -n "$wlan0_path" ]]; then
    log "wlan0 设备路径: $wlan0_path"
else
    log "未找到 wlan0 的设备路径"
fi

if [[ -n "$eth2_path" ]]; then
    log "eth2 设备路径: $eth2_path"
else
    log "未找到 eth2 的设备路径"
fi

# 检查是否成功获取设备路径
if [[ -z "$wlan0_path" || -z "$eth2_path" ]]; then
    log "无法监控网络状态，因为未能找到所有必要的设备路径。"
    exit 1
fi

# 使用gdbus监控网络状态变化
gdbus monitor --system --dest org.freedesktop.NetworkManager | grep --line-buffered -i 'StateChanged' | while read -r line; do
    # 检查是否是WLAN0设备或5G模组的状态变化
    if [[ $line == *"$wlan0_path"* ]]; then
        # 提取状态信息
        newState=$(echo "$line" | grep -oP '\(uint32 \K\d+(?=,)')
        flag=$dbusMonitorWifi
        device="WiFi"

        # 根据不同的状态进行处理
        case $newState in
            100)
                if [ "$flag" = "false" ]; then
                    log "$device连接成功"
                    dbusMonitorWifi="true"
                    modify_json "dbusMonitorWifi" "true"
                fi
                ;;
            30)
                if [ "$flag" = "true" ]; then
                    log "$device网络断开"
                    dbusMonitorWifi="false"
                    modify_json "dbusMonitorWifi" "false"
                fi
                ;;
            *)
                # 对于其他状态，如果是从30变过来的且最终变为100，则表示连接过程中的状态
                if [ "$flag" = "false" ] && ([[ "$previousState_wifi" == "70" ]] || [[ "$previousState_wifi" == "80" ]] || [[ "$previousState_wifi" == "90" ]]); then
                    log "$device正在尝试重新连接..."
                fi
                ;;
        esac

        # 打印WiFi标志位的当前状态
         log "dbusMonitorWifi is now: $dbusMonitorWifi"
        # 更新previousState变量用于后续的状态判断
        previousState_wifi=$newState

    elif [[ $line == *"$eth2_path"* ]]; then
        # 提取状态信息
        newState=$(echo "$line" | grep -oP '\(uint32 \K\d+(?=,)')
        flag=$dbusMonitor5g
        device="5G模组"

        # 根据不同的状态进行处理
        case $newState in
            100)
                if [ "$flag" = "false" ]; then
                    log "$device连接成功"
                    dbusMonitor5g="true"
                    modify_json "dbusMonitor5g" "true"
                fi
                ;;
            20)
                if [ "$flag" = "true" ]; then
                    log "$device网络断开"
                    dbusMonitor5g="false"
                    modify_json "dbusMonitor5g" "false"
                fi
                ;;
            *)
                # 对于其他状态，如果是从20变过来的且最终变为100，则表示连接过程中的状态
                if [ "$flag" = "false" ] && ([[ "$previousState_5g" == "70" ]] || [[ "$previousState_5g" == "80" ]] || [[ "$previousState_5g" == "90" ]]); then
                    log "$device正在尝试重新连接..."
                fi
                ;;
        esac

        # 打印5g标志位的当前状态
         log "dbusMonitor5g is now: $dbusMonitor5g"
        # 更新previousState变量用于后续的状态判断
        previousState_5g=$newState
    fi
done
