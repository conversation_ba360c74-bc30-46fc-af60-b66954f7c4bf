#!/bin/bash
 
#需要安装sshpass工具 sudo apt-get install sshpass -y

# 配置参数
YS_IP="*************"
YS_USER="ysc"
YS_PASS="'"
YS_SOURCE="/home/<USER>/log"

NV_IP="*************"
NV_USER="nvidia"
NV_PASS="nvidia"
NV_SOURCE="/home/<USER>/log"

CAT_IP="*************"
CAT_USER="cat"
CAT_PASS="temppwd"
CAT_TARGET="/home/<USER>/log"

# 生成时间戳
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
YS_TAR="ysc_log_${TIMESTAMP}.tar.gz"
NV_TAR="nvidia_log_${TIMESTAMP}.tar.gz"

# 临时目录
TMP_DIR="/tmp/log_transfer_${TIMESTAMP}"
mkdir -p "$TMP_DIR"

# 1. 准备cat设备目录
sshpass -p "$CAT_PASS" ssh "${CAT_USER}@${CAT_IP}" \
  "sudo mkdir -p '$CAT_TARGET' && sudo chmod 777 '$CAT_TARGET'"

# 2. 打包ysc日志
sshpass -p "$YS_PASS" ssh "${YS_USER}@${YS_IP}" \
  "tar -czf - -C $(dirname "$YS_SOURCE") $(basename "$YS_SOURCE")" > "${TMP_DIR}/${YS_TAR}"

# 3. 打包nvidia日志
sshpass -p "$NV_PASS" ssh "${NV_USER}@${NV_IP}" \
  "tar -czf - -C $(dirname "$NV_SOURCE") $(basename "$NV_SOURCE")" > "${TMP_DIR}/${NV_TAR}"

# 4. 传输到cat设备
sshpass -p "$CAT_PASS" rsync -avz \
  --rsync-path="sudo rsync" \
  --chmod=D755,F644 \
  "${TMP_DIR}/"* \
  "${CAT_USER}@${CAT_IP}:${CAT_TARGET}/"

# 5. 恢复目录权限（生产环境需调整）
sshpass -p "$CAT_PASS" ssh "${CAT_USER}@${CAT_IP}" \
  "sudo chmod 755 '$CAT_TARGET' && sudo chown -R cat:cat '$CAT_TARGET'"

# 清理
rm -rf "$TMP_DIR"
echo "传输完成，文件已保存至：${CAT_TARGET}/"
