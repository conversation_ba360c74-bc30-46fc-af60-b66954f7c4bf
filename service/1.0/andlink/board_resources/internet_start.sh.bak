#!/bin/bash

SCRIPT_PATH=$(dirname "$(readlink -f "$0")")


file_path=$SCRIPT_PATH/network.conf

LOCAL=0

if [ -n "$1" ]; then
    LOCAL=$1
fi

network_status=none
AP_INTERFACE="p2p0"
WiFI_INTERFACE="wlan0"
MOBILE_INTERFACE="eth2"


MAX_ATTEMPTS=5

DEFAULT_JSON='{
  "wifiState": "on",
  "mobileDataState": "off",
  "wifiName": "",
  "isWifiConnect": "true",
  "isInternetConnect":"true"
}'

# 检查 network.conf 文件是否存在、非空且可读
if [ ! -s "$file_path" ] || [ ! -r "$file_path" ]; then
    echo "Warning: $file_path is missing, empty or not readable. Recreating with default settings."
    
    if [ -e "$file_path" ]; then
        rm -f "$file_path"
        echo "Old $file_path has been deleted."
    fi
    
    touch "$file_path"
    chmod 644 "$file_path"  
    
    echo "$DEFAULT_JSON" > "$file_path"
    echo "New $file_path created and written with default settings."
else
    echo "$file_path exists and is readable."
fi

# bash $SCRIPT_PATH/route.sh wifi

read_json() {
    local key="$1"
    jq -r ".${key}" "$file_path"
}

modify_json() {
	local key=$1
	local value=$2
    jq --arg val "$value" ".$key = \$val" $file_path > tmp.$$.json && mv tmp.$$.json $file_path
}

get_ssid() {
    local interface=$1
    local ssid=$(iw dev "$interface" info | grep "ssid" | awk '{print $2}')
    
    if [ -n "$ssid" ]; then
        echo "$ssid"
        return 0
    else
        echo "ERROR: No SSID found for interface $interface"
        return 1
    fi
}

get_interface_ip() {
    local interface=$1
    local ip_address=$(ip -4 addr show $interface | grep -oP '(?<=inet\s)\d+(\.\d+){3}')
    
    if [ -n "$ip_address" ]; then
        echo "$ip_address"
        return 0
    else
        echo "ERROR: No IP address found for interface $interface"
        return 1
    fi
}


get_interface_status() {
    local interface=$1
    local status=$(ip link show $interface | grep -oP '(?<=state )\w+')
    
    if [ "$status" = "UP" ]; then
        # echo "UP"
        return 0
    else
        echo "ERROR: Interface $interface is $status"
        return 1
    fi
}

check_ping() {
    local interface=$1
    if ping -I "$interface" -c 4 www.baidu.com > /dev/null 2>&1; then
        return 0 
    else
        return 1 
    fi
}

function run_sudo_command()
 {
    local password="$1"
    local command="$2"
    echo "$password" | sudo -S sh -c "$command"
    if [ $? -ne 0 ]; then
        error_message=$( { echo "$password"; echo "$command"; } | sudo -S sh -c "$command" 2>&1 )
        echo "An error occurred: $error_message"
    else
        echo "Command executed successfully."
    fi
}

set_network_status() {
    # 首先检查$MOBILE_INTERFACE是否存在且状态为UP
    if get_interface_status "$MOBILE_INTERFACE"; then
        echo "$MOBILE_INTERFACE UP"
        mobile_addr=$(get_interface_ip "$MOBILE_INTERFACE")
        if [ $? -eq 0 ]; then
            echo "mobile_addr is $mobile_addr"
            
            # 检查是否可以ping通www.baidu.com
            if check_ping "$MOBILE_INTERFACE"; then
                echo "Ping to www.baidu.com via $MOBILE_INTERFACE successful."
            else
                echo "Ping to www.baidu.com via $MOBILE_INTERFACE failed. switching to wifi."
                switch_to_wifi
            fi
        else
            echo "Failed to get IP address for $MOBILE_INTERFACE. switching to wifi."
            switch_to_wifi
        fi
    else
        echo "$MOBILE_INTERFACE is not UP or does not exist. switching to wifi."
        switch_to_wifi
    fi
}

switch_to_wifi() {
    wifi_addr=$(get_interface_ip "$WiFI_INTERFACE")
    echo "$WiFI_INTERFACE ip is :$wifi_addr"
    if [ $? -ne 0 ]; then #没有获取到wlan0的ip
        if [ "$store_status" == "wifi" ]; then
            store_status=5g
            modify_json "wifiState" off
            modify_json "mobileDataState" on
        fi
    else
        echo "Switched to wifi_addr: $wifi_addr"
        store_status=wifi
        modify_json "wifiState" on
        modify_json "mobileDataState" off
    fi
}

sysctl -w net.ipv4.ip_forward=1

# LOCKFILE="/tmp/async_wifi_scan_and_connect.lock"

# cleanup_lockfile() {
#     if [ -f "$LOCKFILE" ]; then
#         rm -f "$LOCKFILE"
#         echo "Lockfile removed."
#     fi
# }

# ensure_single_instance() {
#     if [ -f "$LOCKFILE" ]; then
#         echo "Another instance is running or lockfile exists. Exiting..."
#         exit 1
#     else
#         touch "$LOCKFILE"
#         echo "Lockfile created."
#     fi
# }

# cleanup() {
#     cleanup_lockfile
# }

# # 确保锁文件在脚本正常退出或遇到中断时被清理
# trap 'cleanup; exit' INT TERM EXIT

LOG_FILE="/var/log/async_wifi_scan_and_connect.log"

async_wifi_scan_and_connect() {
    while true; do       
        # ensure_single_instance  

        sleep 30
        if [ "$wifiState" = "on" ]; then 
            if ! get_interface_status "$WiFI_INTERFACE"; then
                continue
            fi
            
            current_ssid=$(get_ssid "$WiFI_INTERFACE")
            echo "current wifi ssid :$current_ssid" >> "$LOG_FILE"
            
            if check_ping "$WiFI_INTERFACE"; then
                echo "wifi connection is working." >> "$LOG_FILE"
            else
                echo "No wifi connectivity detected. Attempting to reconnect..." >> "$LOG_FILE"
                
                password="'"
                command1=" nmcli device disconnect "$WiFI_INTERFACE" "
                run_sudo_command "$password" "$command1"
                sleep 5
                command2=" nmcli device connect "$WiFI_INTERFACE" "
                run_sudo_command "$password" "$command2"
                sleep 10
                
                if check_ping "$WiFI_INTERFACE"; then
                    echo "Successfully reconnected using NetworkManager." >> "$LOG_FILE"
                else
                    echo "Automatic reconnect failed, attempting manual recovery..." >> "$LOG_FILE"
                    
                    config_file="/etc/andlink/andlinkSdk.conf"
                    
                    if [ ! -f "$config_file" ]; then
                        echo "Config file not found, switching to 5G network." >> "$LOG_FILE"
                        store_status=5g
                        modify_json "wifiState" off
                        modify_json "mobileDataState" on
                    else
                        ssid=$(grep '^ssid=' "$config_file" | cut -d '=' -f2)
                        password=$(grep '^password=' "$config_file" | cut -d '=' -f2)
                        #检测WiFi ssid是否存在
                        attempt=0
                        while true
                        do
                            command3="nmcli dev wifi list --rescan yes"
                            run_sudo_command "$password" "$command3"
                            command4="nmcli -t -f ssid dev wifi"
                            output=$(run_sudo_command "$password" "$command4")
                            # 检查输出中是否存在指定的SSID
                            if echo "$output" | grep -Fxq "$ssid"; then
                                echo "找到SSID: $ssid" >> "$LOG_FILE"
                                break
                            else
                                echo "未找到SSID: $ssid, 5秒后重试..." >> "$LOG_FILE"
                                sleep 5
                                ((attempt++))
                                if [ $attempt -ge $max_attempts ]; then
                                    echo "达到最大尝试次数，退出循环" >> "$LOG_FILE"
                                    break
                                fi
                            fi
                        done

                        if [ -n "$ssid" ] && [ -n "$password" ]; then
                            echo "Connecting to SSID from config file: $ssid" >> "$LOG_FILE"
                            
                            command5="killall hostapd"
                            run_sudo_command "$password" "$command5"
                            sleep 5
                            if nmcli dev wifi connect "$ssid" password "$password"; then
                                echo "Successfully connected to $ssid." >> "$LOG_FILE"
                            else
                                echo "Failed to connect to $ssid." >> "$LOG_FILE"
                                store_status=5g
                                modify_json "wifiState" off
                                modify_json "mobileDataState" on
                            fi
                            command6="hostapd /etc/hostapd/hostapd.conf -B"
                            run_sudo_command "$password" "$command6"
                            sleep 5
                        else
                            echo "SSID or password not provided in the config file, switching to 5G network." >> "$LOG_FILE"
                            store_status=5g
                            modify_json "wifiState" off
                            modify_json "mobileDataState" on
                        fi
                    fi
                fi
            fi
        fi  
    done 
  
}


async_wifi_scan_and_connect >> "$LOG_FILE" 2>&1 &


while true
do
    wifiState=$(read_json "wifiState")
    mobileDataState=$(read_json "mobileDataState")
    if [ "$wifiState" = "on" ]; then
        store_status=wifi
    elif [ "$mobileDataState" = "on" ]; then
        store_status=5g
    else
        store_status=none
    fi
    
    if check_ping "$MOBILE_INTERFACE" || check_ping "$WiFI_INTERFACE" ; then
        modify_json "isInternetConnect" true

    else
        modify_json "isInternetConnect" flase
    fi

    set_network_status

    # mobile_addr=$(get_interface_ip "$MOBILE_INTERFACE")
    # if [ $? -eq 0 ]; then
    #     echo "mobile_addr is $mobile_addr"
    #     wifi_addr=$(get_interface_ip "$WiFI_INTERFACE")
    #     if [ $? -eq 1 ]; then
    #         if [ "$store_status" == "wifi" ]; then
    #             #5g获取IP后wifi没有获取到IP且当前模式是wifi模式
    #             store_status=5g
    #             modify_json "wifiState" off
    #             modify_json "mobileDataState" on
    #         fi
    #     else
    #         echo "wifi_addr is $wifi_addr"
    #     fi
    #     # ...
    # fi

    if [ "$store_status" == "wifi" ]; then
	wlan0_ssid=$(iw dev wlan0 info | grep "ssid" | awk '{print $2}')
        modify_json "wifiName" "$wlan0_ssid"
        modify_json "isWifiConnect" true

        if [ "$store_status" != "$network_status" ]; then
            bash  $SCRIPT_PATH/wifi/iptables_wifi.sh
            # modify_json "isWifiConnect" true
        fi
        bash $SCRIPT_PATH/route.sh wifi
    elif [ "$store_status" == "5g" ]; then
            modify_json "isWifiConnect" false
        if [ "$store_status" != "$network_status" ]; then
            bash $SCRIPT_PATH/5G/iptables_5g.sh
            # modify_json "isWifiConnect" false
        fi
        bash $SCRIPT_PATH/route.sh 5g
    fi
    network_status=$store_status


    echo "循环执行中..."
    sleep 5
done



# while :
# do
# SSID=$(iw dev $INTERFACE info | grep "ssid" | awk '{print $2}')
# if [ -n "$SSID" ]; then
# 	echo "ssid is $SSID"
#         break
# fi
# sleep 4
# bash  $SCRIPT_PATH/wifi/ap_start.sh start 5G
# done


# bash  $SCRIPT_PATH/wifi/p2p_ip_set.sh
# bash  $SCRIPT_PATH/wifi/iptables_wifi.sh

# ATTEMPT=0
# while [ $ATTEMPT -lt $MAX_ATTEMPTS ]
# do
# 	IPV4=$(ip -4 addr show $INTERFACE | grep -oP '(?<=inet\s)\d+(\.\d+){3}')
# 	if [ -n "$IPV4" ]; then
#     		break
# 	fi
# 	sleep 2
# 	bash  $SCRIPT_PATH/wifi/p2p_ip_set.sh
#     ATTEMPT=$((ATTEMPT + 1))
# done

# ATTEMPT=0
# while [ $ATTEMPT -lt $MAX_ATTEMPTS ]; do
#     SSID=$(iw dev $INTERFACE info | grep "ssid" | awk '{print $2}')
#     if [ -z "$SSID" ]; then
#         echo "SSID not found, restarting AP (Attempt: $((ATTEMPT + 1)))"
#         bash  $SCRIPT_PATH/wifi/ap_start.sh start 5G
#     else
#         echo "SSID found: $SSID"
#         break
#     fi
#     sleep 2
#     ATTEMPT=$((ATTEMPT + 1))
# done


# INTERFACE="wlan0"
# TARGET="www.baidu.com"


# check_ip() {
#     IP_ADDRESS=$(ip -4 addr show $INTERFACE | grep -oP '(?<=inet\s)\d+(\.\d+){3}')
#     if [ -n "$IP_ADDRESS" ]; then
#         echo "网卡 $INTERFACE地址 check 已获取到 IP 地址: $IP_ADDRESS"
#         ping -c 1 $TARGET -4 > /dev/null 2>&1

#         if [ $? -eq 0 ]; then
#             echo "WiFi网络连接正常"

#             network_status=wifi
#             modify_json "wifiState" "on"
#             modify_json "mobileDataState" "off"
#             modify_json "isWifiConnect" true
#             wlan0_ssid=$(iw dev wlan0 info | grep "ssid" | awk '{print $2}')
#             modify_json "wifiName" "$wlan0_ssid"

#             if [ $LOCAL = 1 ]; then
#                 sleep 30
#                 #echo "配置5G香港模式"
#                 $SCRIPT_PATH/5G/LiotAT_HongKong
#             fi
            
            
#         else
#             echo "WiFi网络异常,切换到5G模式"

#             bash $SCRIPT_PATH/5G/iptables_5g.sh
#             if [ $LOCAL = 1 ]; then
#                 #echo "配置5G香港模式"
#                 $SCRIPT_PATH/5G/LiotAT_HongKong
#             fi
#             bash $SCRIPT_PATH/route.sh 5g
#             network_status=5g
#             modify_json "wifiState" "off"
#             modify_json "mobileDataState" "on"
#             modify_json "isWifiConnect" false
#         fi
#        return 0
#     else
#         echo "网卡 $INTERFACE 未获取到 IP 地址"
#         return 1
#     fi
# }


# update_network_status() {
#     local store_status="none"
#     wifiState=$(read_json "wifiState")
#     mobileDataState=$(read_json "mobileDataState")
#     if [ "$wifiState" = "on" ]; then
#         store_status=wifi
#     elif [ "$mobileDataState" = "on" ]; then
#         store_status=5g
#     else
#         store_status=none
#     fi
#     if [ "$store_status" != "$network_status" ]; then
#         echo "两个参数不一致"
#         if [ "$store_status" == "wifi" ]; then
#             bash $SCRIPT_PATH/route.sh wifi
#             bash  $SCRIPT_PATH/wifi/iptables_wifi.sh
#             modify_json "isWifiConnect" true
#             wlan0_ssid=$(iw dev wlan0 info | grep "ssid" | awk '{print $2}')
#             modify_json "wifiName" "$wlan0_ssid"
#         elif [ "$store_status" == "5g" ]; then
#             bash $SCRIPT_PATH/route.sh 5g
#             bash $SCRIPT_PATH/5G/iptables_5g.sh
#             modify_json "isWifiConnect" false
#         fi
#         network_status=$store_status
#     else
#         echo "两个参数一致"
#     fi
#     bash $SCRIPT_PATH/route.sh $store_status 

# }

# ATTEMPT=0
# while [ $ATTEMPT -le $MAX_ATTEMPTS ]; do
#     echo "尝试 $ATTEMPT/$MAX_ATTEMPTS 获取 IP 地址..."
#     bash $SCRIPT_PATH/route.sh wifi
#     if check_ip; then
# 	while :
# 	do
# 		sleep 30
# 		echo "循环检测中1"
#         update_network_status
# 	done
#     fi
#     sleep 2
#     ATTEMPT=$((ATTEMPT + 1))
# done

# echo "经过 $MAX_ATTEMPTS 次尝试，网卡 $INTERFACE 仍未获取到 IP 地址"
# echo "WiFi网络异常,切换到5G模式"
# # bash $SCRIPT_PATH/5G/5g_start.sh
# if [ $LOCAL = 1 ]; then
# #echo "配置5G香港模式"
#     $SCRIPT_PATH/5G/LiotAT_HongKong
# fi
# bash $SCRIPT_PATH/route.sh 5g
# bash $SCRIPT_PATH/5G/iptables_5g.sh
# network_status=5g
# modify_json "wifiState" "off"
# modify_json "mobileDataState" "on"
# modify_json "isWifiConnect" false

# while :
# do
# 	echo "循环检测中2"
#     update_network_status
# 	sleep 30
# done

# get_ssid() {
#     local interface=$1
#     local ssid=$(iw dev "$interface" info | grep "ssid" | awk '{print $2}')
    
#     if [ -n "$ssid" ]; then
#         echo "$ssid"
#         return 0
#     else
#         echo "ERROR: No SSID found for interface $interface"
#         return 1
#     fi
# }

# # 使用示例:
# # ssid=$(get_ssid "wlan0")
# # if [ $? -eq 0 ]; then
# #     echo "Found SSID: $ssid"
# # else
# #     echo "Failed to get SSID: $ssid"
# # fi

# get_interface_ip() {
#     local interface=$1
#     local ip_address=$(ip -4 addr show $interface | grep -oP '(?<=inet\s)\d+(\.\d+){3}')
    
#     if [ -n "$ip_address" ]; then
#         echo "$ip_address"
#         return 0
#     else
#         echo "ERROR: No IP address found for interface $interface"
#         return 1
#     fi
# }

# # 使用示例:
# # ip_addr=$(get_interface_ip "wlan0")
# # if [ $? -eq 0 ]; then
# #     echo "Found IP: $ip_addr"
# # else
# #     echo "$ip_addr"  # 打印错误信息
# # fi

# get_interface_status() {
#     local interface=$1
#     local status=$(ip link show $interface | grep -oP '(?<=state )\w+')
    
#     if [ "$status" = "UP" ]; then
#         echo "UP"
#         return 0
#     else
#         echo "ERROR: Interface $interface is $status"
#         return 1
#     fi
# }

# 使用示例:
# status=$(get_interface_status "wlan0")
# if [ $? -eq 0 ]; then
#     echo "Interface is UP"
# else
#     echo "$status"  # 打印错误信息
# fi
