[Unit]
# CMCC_ROBOT_SERVICE_VER=0.0.0
Description=andlink
After=network.target
Before=

[Service]
Type=simple
User=root
Group=root
WorkingDirectory=/usr/bin/cmcc_robot/install
KillMode=control-group
Restart=always
ExecStart=/usr/bin/cmcc_robot/install/env/service/andlink/run_andlink_ros2.sh
ExecStop=/usr/bin/cmcc_robot/install/env/service/andlink/stop_andlink_ros2.sh


[Install]
WantedBy=multi-user.target