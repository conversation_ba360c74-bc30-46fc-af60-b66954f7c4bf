#!/bin/bash

g_root_of_script=$(dirname $(readlink -f "$0"))
g_install_path=/usr/bin/cmcc_robot/install
g_target_user=""
g_target_pwd=""
g_target_is_test=""
g_target_is_pro=""

function func_run_sudo_command()
{
    local arg_command="$1"
    # echo "[$LINENO][NOTE ]pwd: ${g_target_pwd}, cmd: ${arg_command}"
    echo "${g_target_pwd}" | sudo -S bash -c "${arg_command}"
    if [ $? -ne 0 ]; then
        echo "[$LINENO][ERROR][${arg_command}]failed"
        g_cmd_exe_status="func_run_sudo_command"
        return
    fi
}

function func_tshark_for_dev()
{
    echo "[$LINENO][NOTE ]----- start tshark"
    local arg_dev_cfg_path=/etc/cmcc_robot/cmcc_dev.ini
    local arg_result=""
    if [ ! -f ${arg_dev_cfg_path} ]; then
        echo "[$LINENO][ERROR][${arg_dev_cfg_path}]lost"
        return
    fi
    arg_result=$(grep "devNO=" ${arg_dev_cfg_path})
    if [ "${arg_result}" == "" -o "${arg_result}" == " " ]; then
        echo "[$LINENO][ERROR][devNO]lost"
        return
    fi
    arg_result=${arg_result#*=}
    if [ "${arg_result}" == "" -o "${arg_result}" == " " ]; then
        echo "[$LINENO][ERROR][devNO]invalid"
        return
    fi
    if [ "${arg_result}" == "01" ]; then
        echo "[$LINENO][NOTE ]start tshark ..."
        bash ${g_root_of_script}/tshark.sh
        echo "[$LINENO][NOTE ]start tshark over"
    fi
}
func_tshark_for_dev

# 设置默认播放设备,当设备插拔时，或者程序启动时调用
# 优先级: [USB Audio Device]  
# 指定USB设备名
usb_device_name="USB_Audio_Device"

# 通过USB设备名获取声卡ID
#soundcard_id=$(aplay -l | grep "${usb_device_name}" -A 2 | grep "card" | awk '{print $2}' | tr -d ':' | head -n 1)
#name: <alsa_output.usb-C-Media_Electronics_Inc._USB_Audio_Device-00.analog-stereo>
padevname=$(pacmd list-sinks | grep -e name: | grep -e ${usb_device_name} | head -n 1 | sed 's/.*<\(.*\)>.*/\1/')

if [ "${padevname}" = "" ]; then
echo "no playback card named: \"${usb_device_name}\"!"
else 
# 通过pulseaudio 设置默认声卡
pacmd set-default-sink ${padevname}
echo "set default sink \"${padevname}\""
fi

if [ -d ${g_install_path}/env/ObuVoice ]; then
    echo "[$LINENO][NOTE ]start ifly ..."
    bash ${g_install_path}/env/ObuVoice/pkt/ifly-init.sh &
    echo "[$LINENO][NOTE ]start ifly over"
fi

source /opt/ros/foxy/setup.bash
source ${g_install_path}/setup.bash
export RMW_IMPLEMENTATION=rmw_cyclonedds_cpp

if [ -d ${g_install_path}/env/lib ]; then
    echo "[$LINENO][NOTE ]export lib"
    export LD_LIBRARY_PATH=${g_install_path}/env/lib:$LD_LIBRARY_PATH
    if [ "${g_is_test_dev}" == "y" ]; then
        if [ -d ${g_install_path}/env/lib/dev ]; then
            echo "[$LINENO][NOTE ]export lib dev"
            export LD_LIBRARY_PATH=${g_install_path}/env/lib/dev:$LD_LIBRARY_PATH
        fi
    else
        if [ -d ${g_install_path}/env/lib/pro ]; then
            echo "[$LINENO][NOTE ]export lib pro"
            export LD_LIBRARY_PATH=${g_install_path}/env/lib/pro:$LD_LIBRARY_PATH
        fi
    fi
fi
if [ "${g_target_is_pro}" == "y" ]; then
    echo "start ros2 launch launch_package speech_ctrl_robdog_pro.py"
    ros2 launch launch_package speech_ctrl_robdog_pro.py
else
    echo "start ros2 launch launch_package speech_ctrl_robdog.py"
    ros2 launch launch_package speech_ctrl_robdog.py
fi