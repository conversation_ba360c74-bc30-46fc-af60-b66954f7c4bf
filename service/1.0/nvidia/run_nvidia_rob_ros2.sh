#!/bin/bash

g_install_path=/usr/bin/cmcc_robot/install
g_target_is_test=""
g_target_is_pro=""

source /opt/ros/foxy/setup.bash
source ${g_install_path}/setup.bash
export RMW_IMPLEMENTATION=rmw_cyclonedds_cpp
if [ -d ${g_install_path}/env/lib ]; then
    echo "[$LINENO][NOTE ]export lib"
    export LD_LIBRARY_PATH=${g_install_path}/env/lib:$LD_LIBRARY_PATH
    if [ "${g_is_test_dev}" == "y" ]; then
        if [ -d ${g_install_path}/env/lib/dev ]; then
            echo "[$LINENO][NOTE ]export lib dev"
            export LD_LIBRARY_PATH=${g_install_path}/env/lib/dev:$LD_LIBRARY_PATH
        fi
    else
        if [ -d ${g_install_path}/env/lib/pro ]; then
            echo "[$LINENO][NOTE ]export lib pro"
            export LD_LIBRARY_PATH=${g_install_path}/env/lib/pro:$LD_LIBRARY_PATH
        fi
    fi
fi
if [ "${g_target_is_pro}" == "y" ]; then
    echo "start ros2 launch launch_package nvidia_ctrl_robdog_pro.py"
    ros2 launch launch_package nvidia_ctrl_robdog_pro.py
else
    echo "start ros2 launch launch_package nvidia_ctrl_robdog.py"
    ros2 launch launch_package nvidia_ctrl_robdog.py
fi
