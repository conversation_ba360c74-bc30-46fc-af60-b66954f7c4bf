[Unit]
# CMCC_ROBOT_SERVICE_VER=0.0.0
Description=handle_network
After=network.target
Before=

[Service]
Type=simple
User=root
Group=root
WorkingDirectory=/usr/bin/cmcc_robot/install
KillMode=control-group
Restart=always
ExecStart=bash /usr/bin/cmcc_robot/install/env/service/andlink/run_network_ros2.sh
ExecStop=bash /usr/bin/cmcc_robot/install/env/service/andlink/stop_network_ros2.sh
[Install]
WantedBy=multi-user.target