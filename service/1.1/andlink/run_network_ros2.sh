#!/bin/bash

g_root_of_script=$(dirname $(readlink -f "$0"))
g_install_path=/usr/bin/cmcc_robot/install

# 检查是否由systemd启动
if [ -n "$INVOCATION_ID" ] || [ -n "$JOURNAL_STREAM" ]; then
    echo "脚本是由systemd运行的。"
    sleep 30
else
    echo "脚本是手动运行的。"
fi

SCRIPT_DIR=$(dirname "$0")
# 将目录名转换为绝对路径
SCRIPT_DIR=$(cd "$SCRIPT_DIR" && pwd)
echo "$SCRIPT_DIR"

PARENT_DIR=$(dirname "$SCRIPT_DIR")
GRANDPARENT_DIR=$(dirname "$PARENT_DIR")

echo "当前脚本所在目录: $SCRIPT_DIR"
echo "上上级文件夹: $GRANDPARENT_DIR"

# ROS1_SERVICE_NAME="andlink"
# ROS2_SERVICE_NAME="run_network_ros2"

# STATUS=$(systemctl is-active $ROS1_SERVICE_NAME)

# if [ "$STATUS" == "active" ]; then
#     echo "服务 $ROS1_SERVICE_NAME 正在运行，正在停止..."
#     systemctl stop $ROS1_SERVICE_NAME
#     systemctl disable $ROS1_SERVICE_NAME
#     echo "服务 $ROS1_SERVICE_NAME 已被停止。"
#     sudo cp $SCRIPT_DIR/run_network_ros2.service /etc/systemd/system/
#     sudo systemctl daemon-reload
#     sudo systemctl enable $ROS2_SERVICE_NAME
# else
#     echo "服务 $ROS1_SERVICE_NAME 没有运行。"
# fi

source /opt/ros/foxy/setup.bash
source ${g_install_path}/setup.bash
export RMW_IMPLEMENTATION=rmw_cyclonedds_cpp
echo "start ros2 launch launch_package andlink_handlernetwork.py"
ros2 launch launch_package network.py 







