#!/bin/bash

g_root_of_script=$(dirname $(readlink -f "$0"))
g_install_path=/usr/bin/cmcc_robot/install

# SCRIPT_DIR=$(dirname "$0")
# SCRIPT_DIR=$(cd "$SCRIPT_DIR" && pwd)
# echo "当前脚本目录:$SCRIPT_DIR"

HOST_SYSTEM_PROCESSOR=$(uname -m)

# 获取Ubuntu版本
UBUNTU_VERSION=$(lsb_release -r | awk '{print $2}')

# 根据Ubuntu版本确定ROS 2版本
case $UBUNTU_VERSION in
20.04)
  ROS2_VERSION="foxy"
  ;;
22.04)
  ROS2_VERSION="humble"
  ;;
24.04)
  ROS2_VERSION="jazzy"
  ;;
*)
  echo "Unsupported Ubuntu version: $UBUNTU_VERSION"
  exit 1
  ;;
esac

echo "Ubuntu版本: $UBUNTU_VERSION"
echo "对应的ROS 2版本: $ROS2_VERSION"

source /opt/ros/$ROS2_VERSION/setup.bash
source $g_install_path/setup.bash
export RMW_IMPLEMENTATION=rmw_cyclonedds_cpp

#export RCUTILS_CONSOLE_OUTPUT_FORMAT="[{severity} {time}] [{name}] {function_name}() {file_name}:{line_number}: {message} "
export RCUTILS_COLORIZED_OUTPUT=1
# export RCUTILS_LOGGING_BUFFERED_STREAM=1

if [ "$HOST_SYSTEM_PROCESSOR" = "x86_64" ]; then
  echo "当前主机架构是 x86_64"
  # ros2 launch launch_package andlink_handlernetwork.py
  ros2 launch launch_package andlink.py
elif [ "$HOST_SYSTEM_PROCESSOR" = "aarch64" ]; then
  echo "当前主机架构是 aarch64"
  #ros2 launch launch_package andlink_handlernetwork.py
  ros2 launch launch_package andlink.py
fi
