#!/bin/bash

g_root_of_script=$(dirname $(readlink -f "$0"))
g_install_path=/usr/bin/cmcc_robot/install

STATE_FILE="/run/andlink_service_first_run.flag"

# 如果是开机后第一次启动（没有标记文件）
if [ ! -f "$STATE_FILE" ]; then
    echo "==== This is the first start after a reboot. ===="
    # 第一次启动时才 sleep 30
    sleep 30
    # 创建标记文件，防止后续重启再次执行“第一次启动”的逻辑
    touch "$STATE_FILE"
else
    echo "==== This is not the first start (restart). ===="
    # 后续重启时可以不等待，或者根据需要 sleep 0
    sleep 0
fi

# ----------------------
# 以下是你原先 andlink_start.sh 的主要逻辑
# ----------------------

# IP=*************
# export ROS_IP=*************
# 
# ping -c 1 "$IP" > /dev/null 2>&1
# if [ $? -eq 0 ]; then
#     echo "Ping to $IP was successful."
#     export ROS_MASTER_URI=http://*************:11311
# else
#     echo "Failed to ping $IP."
#     export ROS_MASTER_URI=http://*************:11311
# fi
# 
# SCRIPT_DIR=$(dirname "$0")
# SCRIPT_DIR=$(cd "$SCRIPT_DIR" && pwd)
# echo "$SCRIPT_DIR"
# 
# source "${SCRIPT_DIR}/../../devel/setup.bash"
# bash "${SCRIPT_DIR}/configsync.sh"

process1_name=andlinkdaemon
process2_name=andlinkscript


pidCount1=`ps -ef|grep "$process1_name" |grep -v "grep"|grep -v " Z " | wc -l`
pidCount2=`ps -ef|grep "$process2_name" |grep -v "grep"|grep -v " Z " | wc -l`

#如果进程存在，就杀掉
if [ "$pidCount1" = "0" ]; then
    echo "$process1_name 进程未运行"
else
    ps -ef | grep "$process1_name" |grep -v grep | awk '{print $2}' |xargs kill -9
    echo "$process1_name 进程已杀掉"
fi
sleep 1

if [ "$pidCount2" = "0" ]; then
    echo "$process2_name 进程未运行"
else
    ps -ef | grep "$process2_name" |grep -v grep | awk '{print $2}' |xargs kill -9
    echo "$process2_name 进程已杀掉"
fi
sleep 1

source /opt/ros/foxy/setup.bash
source ${g_install_path}/setup.bash
export RMW_IMPLEMENTATION=rmw_cyclonedds_cpp

while true; do
    pidCount1=`ps -ef|grep "$process1_name" |grep -v "grep"|grep -v " Z " |wc -l`
    pidCount2=`ps -ef|grep "$process2_name" |grep -v "grep"|grep -v " Z " |wc -l`

    # 如果任一进程未运行，就重启
    if [ "$pidCount1" = "0" ] || [ "$pidCount2" = "0" ]; then
        echo "重启 andlink 进程"
        bash ${g_root_of_script}/stop_andlink_ros2.sh
        ros2 run andlink "$process2_name" &
    fi
    sleep 30
done
