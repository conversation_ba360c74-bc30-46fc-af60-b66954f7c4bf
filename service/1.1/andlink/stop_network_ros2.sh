#!/bin/bash


process_names=("handlernetwork")
ros2_launch_package="roslaunch launch_package andlink_handlernetwork.py"


# 杀掉指定名称的进程
kill_process() {
    local process_name=$1
    local pidCount=$(ps -ef | grep "$process_name" | grep -v "grep" | wc -l)
    if [ "$pidCount" -eq 0 ]; then
        echo "$process_name 进程未运行"
    else
        ps -ef | grep "$process_name" | grep -v "grep" | awk '{print $2}' | xargs kill -9
        echo "$process_name 进程已杀掉"
    fi
}

# 杀掉 roslaunch 及其所有子进程
kill_roslaunch() {
    local roslaunch_pid=$(pgrep -f $ros2_launch_package)
    if [ -z "$roslaunch_pid" ]; then
        echo "roslaunch 进程未运行"
    else
        echo "找到 roslaunch 进程，PID: $roslaunch_pid"
        pkill -P $roslaunch_pid
        kill -9 $roslaunch_pid
        echo "roslaunch 及其所有子进程已杀掉"
    fi
}

for process_name in "${process_names[@]}"; do
    kill_process "$process_name"
    sleep 1
done

# kill_roslaunch

