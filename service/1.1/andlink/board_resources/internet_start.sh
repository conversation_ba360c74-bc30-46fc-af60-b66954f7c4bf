#!/bin/bash


# 定义日志路径
LOG_DIR="/home/<USER>/log"
MAIN_LOG="$LOG_DIR/network_main.log"
SCAN_LOG="$LOG_DIR/network_scan.log"

# 创建日志目录并设置权限
mkdir -p "$LOG_DIR"
chown ysc:ysc "$LOG_DIR"
chmod 755 "$LOG_DIR"
touch "$MAIN_LOG" "$SCAN_LOG"
chmod 644 "$MAIN_LOG" "$SCAN_LOG"

# 主进程日志函数
log_main() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') -- $1" >> "$MAIN_LOG"
}

# 异步进程日志函数
log_scan() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') -- $1" >> "$SCAN_LOG"
}

# 主进程输出重定向到 MAIN_LOG
exec >> "$MAIN_LOG" 2>&1

SCRIPT_PATH=$(dirname "$(readlink -f "$0")")

file_path="$SCRIPT_PATH/network.conf"
LOCKFILE="/tmp/network_conf.lock"
# acquire_lock() {
#     local retries=5
#     local delay=2
#     for ((i=1; i<=retries; i++)); do
#         exec 200>"$LOCKFILE"
#         if flock -n 200; then
#             return 0
#         else
#             echo "尝试 $i/$retries 获取锁失败，等待 $delay 秒后重试。" >&2
#             sleep $delay
#         fi
#     done
#     echo "无法获取锁，已达到最大重试次数。" >&2
#     return 1
# }

acquire_lock() {
    local log_file="$1"  # 新增参数：日志文件路径
    local retries=5
    local delay=2
    for ((i=1; i<=retries; i++)); do
        exec 200>"$LOCKFILE"
        if flock -n 200; then
            return 0
        else
            echo "$(date '+%Y-%m-%d %H:%M:%S') -- 尝试 $i/$retries 获取锁失败，等待 $delay 秒后重试。" >> "$log_file"
            sleep "$delay"
        fi
    done
    echo "$(date '+%Y-%m-%d %H:%M:%S') -- 无法获取锁，已达到最大重试次数。" >> "$log_file"
    return 1
}

release_lock() {
    flock -u 200
    exec 200>&-
}

LOCAL=0

if [ -n "$1" ]; then
    LOCAL=$1
fi

network_status="none"
AP_INTERFACE="p2p0"
WiFI_INTERFACE="wlan0"
MOBILE_INTERFACE="eth2"


MAX_ATTEMPTS=5

DEFAULT_JSON='{
  "wifiState": "on",
  "mobileDataState": "off",
  "wifiName": "",
  "isWifiConnect": "true",
  "isInternetConnect":"true",
  "dbusMonitorWifi":"false",
  "dbusMonitor5g":"false"
}'

# 检查 network.conf 文件是否存在、非空且可读
if [ ! -s "$file_path" ] || [ ! -r "$file_path" ]; then
    echo "Warning: $file_path is missing, empty or not readable. Recreating with default settings."
    
    
    # 使用锁确保原子性写入
    acquire_lock "$MAIN_LOG"
    echo "$DEFAULT_JSON" > "$file_path"
    release_lock

    log_main "New $file_path created and written with default settings."
else
    log_main "$file_path exists and is readable."
fi

# 修改 read_json 和 modify_json 函数以使用锁
# read_json() {
#     local key="$1"
#     acquire_lock
#     value=$(jq -r ".$key" "$file_path")
#     release_lock
#     echo "$value"
# }

# modify_json() {
#     local key=$1
#     local value=$2
#     acquire_lock
#     jq --arg val "$value" ".$key = \$val" "$file_path" > tmp.$$.json && mv tmp.$$.json "$file_path"
#     release_lock
# }

read_json() {
    local key="$1"
    local log_file="$2"  # 新增参数：日志文件路径
    acquire_lock "$log_file"
    value=$(jq -r ".$key" "$file_path")
    release_lock
    echo "$value"
}

modify_json() {
    local key="$1"
    local value="$2"
    local log_file="$3"  # 新增参数：日志文件路径
    acquire_lock "$log_file"
    jq --arg val "$value" ".$key = \$val" "$file_path" > tmp.$$.json && mv tmp.$$.json "$file_path"
    release_lock
}

get_ssid() {
    local interface=$1
    local ssid=$(iw dev "$interface" info | grep "ssid" | awk '{print $2}')
    
    if [ -n "$ssid" ]; then
        echo "$ssid"
        return 0
    else
        echo "ERROR: No SSID found for interface $interface"
        return 1
    fi
}

get_interface_ip() {
    local interface=$1
    local ip_address=$(ip -4 addr show $interface | grep -oP '(?<=inet\s)\d+(\.\d+){3}')
    
    if [ -n "$ip_address" ]; then
        echo "$ip_address"
        return 0
    else
        echo "ERROR: No IP address found for interface $interface"
        return 1
    fi
}


get_interface_status() {
    local interface=$1
    local status=$(ip link show $interface | grep -oP '(?<=state )\w+')
    
    if [ "$status" = "UP" ]; then
        # echo "UP"
        return 0
    else
        echo "ERROR: Interface $interface is $status"
        return 1
    fi
}

check_ping() {
    local interface=$1
    if ping -I "$interface" -c 4 www.baidu.com > /dev/null 2>&1; then
        return 0 
    else
        return 1 
    fi
}

function run_sudo_command()
 {
    local password="$1"
    local command="$2"
    echo "$password" | sudo -S sh -c "$command"
    if [ $? -ne 0 ]; then
        error_message=$( { echo "$password"; echo "$command"; } | sudo -S sh -c "$command" 2>&1 )
        log_scan "An error occurred: $error_message"
    else
        log_scan "Command executed successfully."
    fi
}

set_network_status() {
    # 首先检查$MOBILE_INTERFACE是否存在且状态为UP
    if get_interface_status "$MOBILE_INTERFACE"; then
        log_main "$MOBILE_INTERFACE UP"
        mobile_addr=$(get_interface_ip "$MOBILE_INTERFACE")
        if [ $? -eq 0 ]; then
            log_main "mobile_addr is $mobile_addr"
            
            # 检查是否可以ping通www.baidu.com
            if check_ping "$MOBILE_INTERFACE"; then
                log_main "Ping to www.baidu.com via $MOBILE_INTERFACE successful."
                if check_ping "$WiFI_INTERFACE"; then
                    log_main "wifi and 5G both exist,prioritize wifi !!!"
                    store_status="wifi"
                    modify_json "wifiState" "on" "$MAIN_LOG"
                    modify_json "mobileDataState" "off" "$MAIN_LOG"
                else
                    log_main "wifi mode is not used,switch 5G !!!"
                    store_status="5g"
                    modify_json "wifiState" "off" "$MAIN_LOG"
                    modify_json "mobileDataState" "on" "$MAIN_LOG"
                fi
            else
                log_main "Ping to www.baidu.com via $MOBILE_INTERFACE failed. switching to wifi."
                switch_to_wifi
            fi
        else
            log_main "Failed to get IP address for $MOBILE_INTERFACE. switching to wifi."
            switch_to_wifi
        fi
    else
        log_main "$MOBILE_INTERFACE is not UP or does not exist. switching to wifi."
        switch_to_wifi
    fi
}

switch_to_wifi() {
    wifi_addr=$(get_interface_ip "$WiFI_INTERFACE")
    log_main "$WiFI_INTERFACE ip is :$wifi_addr"
    if [ $? -ne 0 ]; then #没有获取到wlan0的ip
        if [ "$store_status" == "wifi" ]; then
            store_status="5g"
            modify_json "wifiState" "off" "$MAIN_LOG"
            modify_json "mobileDataState" "on" "$MAIN_LOG"
        fi
    else
        log_main "Switched to wifi_addr: $wifi_addr"
        store_status="wifi"
        modify_json "wifiState" "on" "$MAIN_LOG"
        modify_json "mobileDataState" "off" "$MAIN_LOG"
    fi
}

sysctl -w net.ipv4.ip_forward=1

log_main "打开网络三层转发:net.ipv4.ip_forward = 1"


# LOG_FILE="/var/log/async_wifi_scan_and_connect.log"


# log_message() {
#     local message="$1"
#     echo "$(date '+%Y-%m-%d %H:%M:%S') -- $message" >> "$LOG_FILE"
# }

async_wifi_scan_and_connect() {
    while true; do       
 
            sleep 30            
            current_ssid=$(get_ssid "$WiFI_INTERFACE")
            log_scan  "current wifi ssid :$current_ssid"         
            config_file="/etc/andlink/andlinkSdk.conf"

            if [ ! -f "$config_file" ]; then
                log_scan  "Config file not found, switching to 5G network."
                # store_status=5g
                # modify_json "wifiState" off
                # modify_json "mobileDataState" on
            else
                ssid=$(grep '^ssid=' "$config_file" | cut -d '=' -f2)
                password=$(grep '^password=' "$config_file" | cut -d '=' -f2)
                if [ -n "$ssid" ] && [ -n "$password" ]; then
                    log_scan  " SSID from config file: $ssid"
                    if [ "$current_ssid" != "$ssid" ] ; then
                    #rescan 狗子周围wifi的ssid并且判断配置文件/etc/andlink/andlinkSdk.conf的ssid是否在扫描wifi中
                        attempt=0
                        password1="'"
                        while true
                            do
                                command3="nmcli dev wifi list --rescan yes"
                                run_sudo_command "$password1" "$command3"
                                command4="nmcli -t -f ssid dev wifi"
                                output=$(run_sudo_command "$password1" "$command4")
                                # 检查输出中是否存在指定的SSID
                                if echo "$output" | grep -Fxq "$ssid"; then
                                    log_scan "rescan SSID: $ssid"
                                    if nmcli dev wifi connect "$ssid" password "$password"; then
                                        log_scan  "Successfully connected to $ssid."
                                        sleep 2 #等待dbusMonitorWifi状态更新#
                                        dbusMonitorWifi=$(read_json "dbusMonitorWifi" "$SCAN_LOG")
                                          if [ "$dbusMonitorWifi" = "true" ]; then
                                                store_status="wifi"
                                                modify_json "wifiState" "on"  "$SCAN_LOG"
                                                modify_json "mobileDataState" "off"  "$SCAN_LOG"
                                          fi
                                    else
                                        log_scan  "Failed to connect to $ssid."                                         
                                        # command5="systemctl restart NetworkManager.service"
                                        # run_sudo_command "$password1" "$command5" 
                                        sleep 5 #rescan等待时间#
                                       command6="nmcli dev wifi connect \"$ssid\" password \"$password\""
                                        run_sudo_command "$password1" "$command6"
                                        sleep 2 #等待dbusMonitorWifi状态更新#
                                        dbusMonitorWifi=$(read_json "dbusMonitorWifi" "$SCAN_LOG")
                                        if [ "$dbusMonitorWifi" = "true" ]; then
                                                store_status="wifi"
                                                modify_json "wifiState" "on"  "$SCAN_LOG"
                                                modify_json "mobileDataState" "off"  "$SCAN_LOG"
                                               
                                        else
                                            store_status="5g"
                                            modify_json "wifiState" "off"  "$SCAN_LOG"
                                            modify_json "mobileDataState" "on"  "$SCAN_LOG"
                                        fi                                       
                                    fi
                                    break
                                else
                                    log_scan "未找到SSID: $ssid, 5秒后重试..."
                                    sleep 5
                                    ((attempt++))
                                    if [ $attempt -ge $MAX_ATTEMPTS ]; then
                                        log_scan "达到最大尝试次数，退出循环"
                                        store_status="5g"
                                        modify_json "wifiState" "off"  "$SCAN_LOG"
                                        modify_json "mobileDataState" "on"  "$SCAN_LOG"
                                    fi
                                    break
                                fi
                        done
                 
                    else
                        log_scan "current wifi is the same with andlinSdk.conf's ssid !"
                        store_status="wifi"
                        modify_json "wifiState" "on"  "$SCAN_LOG"
                        modify_json "mobileDataState" "off"  "$SCAN_LOG"
                    fi
                
                else
                    log_scan "SSID or password not provided in the config file." 

                fi
            fi        
 
    done 
  
}


async_wifi_scan_and_connect >> "$SCAN_LOG" 2>&1 &


while true
do
    wifiState=$(read_json "wifiState" "$MAIN_LOG")
    mobileDataState=$(read_json "mobileDataState" "$MAIN_LOG")
    if [ "$wifiState" = "on" ]; then
        store_status="wifi"
    elif [ "$mobileDataState" = "on" ]; then
        store_status="5g"
    else
        store_status="none"
    fi
    
    if check_ping "$MOBILE_INTERFACE" || check_ping "$WiFI_INTERFACE" ; then
        modify_json "isInternetConnect" "true" "$MAIN_LOG"

    else
        modify_json "isInternetConnect" "false" "$MAIN_LOG"
        modify_json "isWifiConnect" "false" "$MAIN_LOG"
        modify_json "wifiName" "" "$MAIN_LOG"
        
    fi

    set_network_status

    if [ "$store_status" == "wifi" ]; then
	    wlan0_ssid=$(iw dev wlan0 info | grep "ssid" | awk '{print $2}')
        modify_json "wifiName" "$wlan0_ssid" "$MAIN_LOG"
        modify_json "isWifiConnect" "true" "$MAIN_LOG"

        if [ "$store_status" != "$network_status" ]; then
            bash  $SCRIPT_PATH/wifi/iptables_wifi.sh

        fi
        bash $SCRIPT_PATH/route.sh wifi
    elif [ "$store_status" == "5g" ]; then
            modify_json "isWifiConnect" "false" "$MAIN_LOG"
            modify_json "wifiName" "" "$MAIN_LOG"
        if [ "$store_status" != "$network_status" ]; then
            bash $SCRIPT_PATH/5G/iptables_5g.sh

        fi
        bash $SCRIPT_PATH/route.sh 5g
    fi
    network_status="$store_status"


    log_main  "循环执行中..."
    sleep 5
done



# while :
# do
# SSID=$(iw dev $INTERFACE info | grep "ssid" | awk '{print $2}')
# if [ -n "$SSID" ]; then
# 	echo "ssid is $SSID"
#         break
# fi
# sleep 4
# bash  $SCRIPT_PATH/wifi/ap_start.sh start 5G
# done


# bash  $SCRIPT_PATH/wifi/p2p_ip_set.sh
# bash  $SCRIPT_PATH/wifi/iptables_wifi.sh

# ATTEMPT=0
# while [ $ATTEMPT -lt $MAX_ATTEMPTS ]
# do
# 	IPV4=$(ip -4 addr show $INTERFACE | grep -oP '(?<=inet\s)\d+(\.\d+){3}')
# 	if [ -n "$IPV4" ]; then
#     		break
# 	fi
# 	sleep 2
# 	bash  $SCRIPT_PATH/wifi/p2p_ip_set.sh
#     ATTEMPT=$((ATTEMPT + 1))
# done

# ATTEMPT=0
# while [ $ATTEMPT -lt $MAX_ATTEMPTS ]; do
#     SSID=$(iw dev $INTERFACE info | grep "ssid" | awk '{print $2}')
#     if [ -z "$SSID" ]; then
#         echo "SSID not found, restarting AP (Attempt: $((ATTEMPT + 1)))"
#         bash  $SCRIPT_PATH/wifi/ap_start.sh start 5G
#     else
#         echo "SSID found: $SSID"
#         break
#     fi
#     sleep 2
#     ATTEMPT=$((ATTEMPT + 1))
# done


# INTERFACE="wlan0"
# TARGET="www.baidu.com"


# check_ip() {
#     IP_ADDRESS=$(ip -4 addr show $INTERFACE | grep -oP '(?<=inet\s)\d+(\.\d+){3}')
#     if [ -n "$IP_ADDRESS" ]; then
#         echo "网卡 $INTERFACE地址 check 已获取到 IP 地址: $IP_ADDRESS"
#         ping -c 1 $TARGET -4 > /dev/null 2>&1

#         if [ $? -eq 0 ]; then
#             echo "WiFi网络连接正常"

#             network_status=wifi
#             modify_json "wifiState" "on"
#             modify_json "mobileDataState" "off"
#             modify_json "isWifiConnect" true
#             wlan0_ssid=$(iw dev wlan0 info | grep "ssid" | awk '{print $2}')
#             modify_json "wifiName" "$wlan0_ssid"

#             if [ $LOCAL = 1 ]; then
#                 sleep 30
#                 #echo "配置5G香港模式"
#                 $SCRIPT_PATH/5G/LiotAT_HongKong
#             fi
            
            
#         else
#             echo "WiFi网络异常,切换到5G模式"

#             bash $SCRIPT_PATH/5G/iptables_5g.sh
#             if [ $LOCAL = 1 ]; then
#                 #echo "配置5G香港模式"
#                 $SCRIPT_PATH/5G/LiotAT_HongKong
#             fi
#             bash $SCRIPT_PATH/route.sh 5g
#             network_status=5g
#             modify_json "wifiState" "off"
#             modify_json "mobileDataState" "on"
#             modify_json "isWifiConnect" false
#         fi
#        return 0
#     else
#         echo "网卡 $INTERFACE 未获取到 IP 地址"
#         return 1
#     fi
# }


# update_network_status() {
#     local store_status="none"
#     wifiState=$(read_json "wifiState")
#     mobileDataState=$(read_json "mobileDataState")
#     if [ "$wifiState" = "on" ]; then
#         store_status=wifi
#     elif [ "$mobileDataState" = "on" ]; then
#         store_status=5g
#     else
#         store_status=none
#     fi
#     if [ "$store_status" != "$network_status" ]; then
#         echo "两个参数不一致"
#         if [ "$store_status" == "wifi" ]; then
#             bash $SCRIPT_PATH/route.sh wifi
#             bash  $SCRIPT_PATH/wifi/iptables_wifi.sh
#             modify_json "isWifiConnect" true
#             wlan0_ssid=$(iw dev wlan0 info | grep "ssid" | awk '{print $2}')
#             modify_json "wifiName" "$wlan0_ssid"
#         elif [ "$store_status" == "5g" ]; then
#             bash $SCRIPT_PATH/route.sh 5g
#             bash $SCRIPT_PATH/5G/iptables_5g.sh
#             modify_json "isWifiConnect" false
#         fi
#         network_status=$store_status
#     else
#         echo "两个参数一致"
#     fi
#     bash $SCRIPT_PATH/route.sh $store_status 

# }

# ATTEMPT=0
# while [ $ATTEMPT -le $MAX_ATTEMPTS ]; do
#     echo "尝试 $ATTEMPT/$MAX_ATTEMPTS 获取 IP 地址..."
#     bash $SCRIPT_PATH/route.sh wifi
#     if check_ip; then
# 	while :
# 	do
# 		sleep 30
# 		echo "循环检测中1"
#         update_network_status
# 	done
#     fi
#     sleep 2
#     ATTEMPT=$((ATTEMPT + 1))
# done

# echo "经过 $MAX_ATTEMPTS 次尝试，网卡 $INTERFACE 仍未获取到 IP 地址"
# echo "WiFi网络异常,切换到5G模式"
# # bash $SCRIPT_PATH/5G/5g_start.sh
# if [ $LOCAL = 1 ]; then
# #echo "配置5G香港模式"
#     $SCRIPT_PATH/5G/LiotAT_HongKong
# fi
# bash $SCRIPT_PATH/route.sh 5g
# bash $SCRIPT_PATH/5G/iptables_5g.sh
# network_status=5g
# modify_json "wifiState" "off"
# modify_json "mobileDataState" "on"
# modify_json "isWifiConnect" false

# while :
# do
# 	echo "循环检测中2"
#     update_network_status
# 	sleep 30
# done

# get_ssid() {
#     local interface=$1
#     local ssid=$(iw dev "$interface" info | grep "ssid" | awk '{print $2}')
    
#     if [ -n "$ssid" ]; then
#         echo "$ssid"
#         return 0
#     else
#         echo "ERROR: No SSID found for interface $interface"
#         return 1
#     fi
# }

# # 使用示例:
# # ssid=$(get_ssid "wlan0")
# # if [ $? -eq 0 ]; then
# #     echo "Found SSID: $ssid"
# # else
# #     echo "Failed to get SSID: $ssid"
# # fi

# get_interface_ip() {
#     local interface=$1
#     local ip_address=$(ip -4 addr show $interface | grep -oP '(?<=inet\s)\d+(\.\d+){3}')
    
#     if [ -n "$ip_address" ]; then
#         echo "$ip_address"
#         return 0
#     else
#         echo "ERROR: No IP address found for interface $interface"
#         return 1
#     fi
# }

# # 使用示例:
# # ip_addr=$(get_interface_ip "wlan0")
# # if [ $? -eq 0 ]; then
# #     echo "Found IP: $ip_addr"
# # else
# #     echo "$ip_addr"  # 打印错误信息
# # fi

# get_interface_status() {
#     local interface=$1
#     local status=$(ip link show $interface | grep -oP '(?<=state )\w+')
    
#     if [ "$status" = "UP" ]; then
#         echo "UP"
#         return 0
#     else
#         echo "ERROR: Interface $interface is $status"
#         return 1
#     fi
# }

# 使用示例:
# status=$(get_interface_status "wlan0")
# if [ $? -eq 0 ]; then
#     echo "Interface is UP"
# else
#     echo "$status"  # 打印错误信息
# fi

