#!/bin/bash

password="'"
command_open="ifconfig p2p0 up"
command_close="ifconfig p2p0 down"

status=$1

run_sudo_command() {
    local password="$1"
    local command="$2"
    
    # 执行命令并捕获输出
    if ! echo "$password" | sudo -S sh -c "$command" 2>/dev/null; then
        # 失败时显示详细错误
        error_message=$( { echo "$password"; echo "$command"; } | sudo -S sh -c "$command" 2>&1 )
        echo "执行失败: $error_message"
        return 1
    else
        echo "命令执行成功: $command"
        return 0
    fi
}

#开关ap模式
if [ "$status" == "up" ];then
    run_sudo_command "$password" "$command_open"
elif [ "$status" == "down" ];then
    run_sudo_command "$password" "$command_close"
fi

