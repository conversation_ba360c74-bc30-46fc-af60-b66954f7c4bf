#!/bin/bash

# 参数校验
if [ $# -ne 1 ]; then
    echo "用法：$0 <网络接口名称>" >&2
    echo "示例：$0 wlan0" >&2
    exit 1
fi

interface="$1"

# 检查接口是否存在
if ! ip link show dev "$interface" >/dev/null 2>&1; then
    echo "错误：网络接口 '$interface' 不存在" >&2
    exit 2
fi

# 获取CIDR格式的IP信息
cidr_info=$(ip -4 -o addr show dev "$interface" 2>/dev/null | awk '/inet / {print $4}')

# 验证IP分配状态
if [ -z "$cidr_info" ]; then
    echo "错误：接口 '$interface' 未分配IPv4地址" >&2
    exit 3
fi

# 拆分IP和CIDR位数
ip_address="${cidr_info%/*}"
cidr="${cidr_info#*/}"

# 计算网络地址
IFS=. read -r a b c d <<< "$ip_address"
mask=$((0xffffffff << (32 - cidr)))
network=$(
    printf "%d.%d.%d.%d/%d" \
    $((a & (mask >> 24) & 0xff)) \
    $((b & (mask >> 16) & 0xff)) \
    $((c & (mask >> 8) & 0xff)) \
    $((d & mask & 0xff)) \
    $cidr
)

echo "$network"