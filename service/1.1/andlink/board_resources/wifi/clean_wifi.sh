#!/bin/bash

echo "正在查找保存的 WiFi 网络配置..."

# 列出所有连接并筛选出 WiFi 连接
wifi_connections=$(nmcli connection show | grep "wifi" || echo "")

# 检查是否有任何 WiFi 连接
if [ -z "$wifi_connections" ]; then
    echo "没有找到保存的 WiFi 网络配置。"
else
    echo "找到以下保存的 WiFi 网络配置:"
    echo "$wifi_connections"

    # 逐个删除找到的 WiFi 连接
    echo "$wifi_connections" | while read -r line; do
        uuid=$(echo "$line" | awk '{print $2}')
        nmcli con delete uuid "$uuid"
        echo "删除了 UUID 为 $uuid 的 WiFi 网络"
    done

    echo "所有保存的 WiFi 网络配置已被删除。"
fi

