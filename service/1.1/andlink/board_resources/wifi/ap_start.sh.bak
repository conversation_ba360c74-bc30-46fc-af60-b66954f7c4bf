#!/bin/bash  

g_install_path=/usr/bin/cmcc_robot/install
g_install_dst_srv_path=${g_install_path}/env/service/andlink

# con-name 
CC_NAME_24G=myap24G
CC_NAME_5G=myap50G

dhcpip=***********/24
SSID_PSK=12345678
# 5G channel 36 40 44 48 52~64 100~104  149~165
channel=36
apdevice=""

if [ -d /sys/class/net/wlan0 ];then
    apdevice=wlan0
fi

if [ -d /sys/class/net/p2p0 ];then
    apdevice=p2p0
fi
if [ $apdevice == "" ];then
    echo "no wifi device"
    exit 0
fi
host_name=$(head -c 8 "${g_install_dst_srv_path}/board_resources/wifi/host_name")
if [ $apdevice == "" ];then
    echo "no wifi device"
    exit 0
fi

SSID_50G=X3000000_5G
SSID_24G=X3000000_24G
if [ -z "$host_name" ]; then
	echo "host_name is empty using default"
else
	SSID_24G="$host_name"_24G
	SSID_50G="$host_name"_5G
fi

CONFIG_FILE="/etc/hostapd/hostapd.conf"
BACKUP_FILE="${CONFIG_FILE}.bak"

if [ ! -f "$CONFIG_FILE" ]; then
    echo "Error: Configuration file $CONFIG_FILE does not exist." 
fi
hw_mode=$(grep "^hw_mode=" $CONFIG_FILE | cut -d '=' -f2 | tr -d '[:space:]')
channel=$(grep "^channel=" $CONFIG_FILE | cut -d '=' -f2 | tr -d '[:space:]')

if [ -z "$hw_mode" ] || [ -z "$channel" ]; then
    echo "Error: Could not find hw_mode or channel in $CONFIG_FILE."
fi

function run_sudo_command()
 {
    local password="$1"
    local command="$2"    
    echo "$password" | sudo -S sh -c "$command"
    if [ $? -ne 0 ]; then
        error_message=$( { echo "$password"; echo "$command"; } | sudo -S sh -c "$command" 2>&1 )
        echo "An error occurred: $error_message"
    else
        echo "set AP Command executed successfully."
    fi
}

function create_ap_5G()
{
    if [ "$hw_mode" = "g" ] && [ "$channel" -le 11 ]; then
        # 条件满足，恢复备份文件
        password="'"
        command="mv  $BACKUP_FILE $CONFIG_FILE"
        run_sudo_command "$password" "$command"
        if [ $? -eq 0 ]; then
            echo "Backup file $BACKUP_FILE has been restored to $CONFIG_FILE."

        else
            echo "Error: Failed to restore backup file $BACKUP_FILE to $CONFIG_FILE."
        fi
    else
        echo "No action needed. Current configuration: hw_mode=$hw_mode, channel=$channel."
    fi
    hostapd /etc/hostapd/hostapd.conf -B
    sleep 2
}

function create_ap_24G()
{
    # nmcli radio wifi on
    # iw reg set CN
    # nmcli c add type wifi ifname $apdevice con-name $CC_NAME_24G autoconnect no ssid $SSID_24G
    # nmcli connection modify $CC_NAME_24G 802-11-wireless.mode ap 802-11-wireless.band bg ipv4.method shared
    # nmcli connection modify $CC_NAME_24G wifi-sec.key-mgmt wpa-psk
    # nmcli connection modify $CC_NAME_24G wifi-sec.psk $SSID_PSK
    # nmcli con modify  $CC_NAME_24G ipv4.addresses $dhcpip
    # nmcli connection up $CC_NAME_24G
    password="'"

    command1="cp $CONFIG_FILE $BACKUP_FILE"
    run_sudo_command "$password" "$command1"
    if [ ! -f "$BACKUP_FILE" ]; then
        echo "Error: Backup file $BACKUP_FILE does not exist." 
    fi
    command2="sed -i 's/^hw_mode=a/hw_mode=g/' $CONFIG_FILE"
    run_sudo_command "$password" "$command2"
    # command3="sed -i '/^ieee80211ac=1/d' /etc/hostapd/hostapd.conf"
    # run_sudo_command "$password" "$command3"
    command4="sed -i 's/^channel=[0-9]\+/channel=6/'  $CONFIG_FILE"
    run_sudo_command "$password" "$command4"

    hostapd /etc/hostapd/hostapd.conf -B
    sleep 2

}

function close_ap()
{
    echo "try to close ap " 
    ret=$(nmcli c show | grep myap | awk 'NR=='1' {print$1}')
    if [ -z "$ret" ];then
        echo "no ap "
        return 0
    fi
    echo "current ap connection is $ret"
    if [ "$CC_NAME_24G" == "$ret" ] || [ "$CC_NAME_5G" == "$ret" ];then
        nmcli c delete $ret
        echo "delete ap success"
    fi
}

usage()
{
	echo "USAGE: "
	echo "./ap_start.sh start 5G    Create a 5G ap use default SSID and password"
    echo "./ap_start.sh start 24G   Create a 2.4G ap use default SSID and password"
    echo "./ap_start.sh stop        Close the created ap "
    exit 1
}

case $1 in 
    start)
        echo "start $2 ap"
        if [ -z $2 ];then 
	    close_ap
            usage
        fi
        if [ $2 == "5G" ];then
            create_ap_5G
        fi
        if [ $2 == "24G" ];then
            close_ap
	    create_ap_24G
        fi
        ;;
    stop)
        close_ap
        ;;
    *)
        usage
        ;;
esac
