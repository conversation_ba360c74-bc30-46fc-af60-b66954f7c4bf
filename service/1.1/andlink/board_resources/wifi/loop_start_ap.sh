#!/bin/bash

g_install_path=/usr/bin/cmcc_robot/install
g_install_dst_srv_path=${g_install_path}/env/service/andlink

echo "loop_ap_start" >> ${g_install_dst_srv_path}/board_resources/wifi/ap.log
# 定义接口名称
INTERFACE="wlan0"

# 无限循环，直到接口状态为“up”为止
while true; do
  # 使用 ip 命令检查接口状态
  status=$(ip link show "$INTERFACE" | grep "state UP")
  
  if [ -n "$status" ]; then
    echo "$INTERFACE is up" >> ${g_install_dst_srv_path}/board_resources/wifi/ap.log
    sleep 2
  else
    if [ -d "/sys/class/net/wlan0" ]; then
        echo "wlan0 启动成功 "
    else
            ${g_install_dst_srv_path}/board_resources/wifi/wifi_ctl.sh disable
            sleep 1
            echo "wlan0 重启"
            ${g_install_dst_srv_path}/board_resources/wifi/wifi_ctl.sh enable
    fi
    echo "$INTERFACE is down, checking again in 1 second..." >> ${g_install_dst_srv_path}/board_resources/wifi/ap.log
    ${g_install_dst_srv_path}/board_resources/wifi/ap_start.sh stop
    sleep 1
    ${g_install_dst_srv_path}/board_resources/wifi/ap_start.sh start 5G
    sleep 1
  fi
done

