#!/bin/bash

g_install_path=/usr/bin/cmcc_robot/install
g_install_dst_srv_path=${g_install_path}/env/service/andlink
#${g_install_dst_srv_path}/board_resources/wifi/wifi_start.sh
INTERFACE="p2p0"
sleep 7
nmcli dev set $INTERFACE managed no
iw dev p2p0 set type __ap
${g_install_dst_srv_path}/board_resources/wifi/ap_start.sh start 5G

while :
do
SSID=$(iw dev $INTERFACE info | grep "ssid" | awk '{print $2}')
if [ -n "$SSID" ]; then
        break
fi
sleep 4
${g_install_dst_srv_path}/board_resources/wifi/ap_start.sh start 5G
done

${g_install_dst_srv_path}/board_resources/wifi/ip_set.sh
${g_install_dst_srv_path}/board_resources/wifi/iptables_ysc.sh

while :
do
	IPV4=$(ip -4 addr show $INTERFACE | grep -oP '(?<=inet\s)\d+(\.\d+){3}')
	if [ -n "$IPV4" ]; then
    		break
	fi
	sleep 2
	${g_install_dst_srv_path}/board_resources/wifi/ip_set.sh
done
sleep 2
while :
do
	SSID=$(iw dev $INTERFACE info | grep "ssid" | awk '{print $2}')
	if [ -z "$SSID" ]; then
	${g_install_dst_srv_path}/board_resources/wifi/ap_start.sh start 5G
	break
	fi
	sleep 2
done


${g_install_dst_srv_path}/board_resources/wifi/iptables_ysc.sh
