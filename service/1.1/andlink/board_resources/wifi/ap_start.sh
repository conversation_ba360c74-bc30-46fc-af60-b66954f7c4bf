#!/bin/bash

g_install_path=/usr/bin/cmcc_robot/install
g_install_dst_srv_path=${g_install_path}/env/service/andlink

# 参数验证
if [ "$1" = "start" ] && [ $# -lt 2 ]; then
    echo "错误：必须指定频段类型"
    echo "用法：$0 start <5G|24G> [INTERFACE]"
    exit 1
fi

# 默认配置
DEFAULT_INTERFACE="p2p0"
CONFIG_FILE="/etc/hostapd/hostapd.conf"
BACKUP_FILE="${CONFIG_FILE}.bak"
password="'"  # 新增密码变量

# 新增 sudo 执行函数 
run_sudo_command() {
    local password="$1"
    local command="$2"
    
    # 执行命令并捕获输出
    if ! echo "$password" | sudo -S sh -c "$command" 2>/dev/null; then
        # 失败时显示详细错误
        error_message=$( { echo "$password"; echo "$command"; } | sudo -S sh -c "$command" 2>&1 )
        echo "执行失败: $error_message"
        return 1
    else
        echo "命令执行成功: $command"
        return 0
    fi
}

#  核心函数：动态修改接口配置 
configure_interface() {
    local interface="$1"
    
    # 验证接口存在性
    if ! [ -d "/sys/class/net/$interface" ]; then
        echo "错误：网络接口 $interface 不存在"
        return 1
    fi

    # 修改配置文件（兼容interface字段是否存在）
    run_sudo_command "$password" "sed -i '/^#*interface=.*/d' \"$CONFIG_FILE\""  # 删除旧配置
    run_sudo_command "$password" "sed -i '1iinterface=$interface' \"$CONFIG_FILE\""
    # 确保文件末尾有换行符
    run_sudo_command "$password" "sed -i -e '\$a\\' \"$CONFIG_FILE\""

    # 二次验证
    if ! run_sudo_command "$password" "grep -q '^interface=$interface' \"$CONFIG_FILE\""; then
        echo "错误：无法写入接口配置"
        return 1
    fi
    return 0
}

#  智能接口检测 
auto_detect_interface() {
    # 优先检测p2p0，其次wlan0
    for iface in "$DEFAULT_INTERFACE" "wlan0"; do
        if [ -d "/sys/class/net/$iface" ]; then
            echo "检测到可用接口：$iface"
            configure_interface "$iface" && return 0
        fi
    done
    echo "错误：未找到可用无线接口"
    return 1
}

#  配置预处理 
prepare_config() {
    local band="$1"
    local interface="${2:-}"
    
    # 自动检测接口（当未指定时）
    if [ -z "$interface" ]; then
        if ! auto_detect_interface; then
            exit 1
        fi
        interface="$DEFAULT_INTERFACE"
    else
        if ! configure_interface "$interface"; then
            exit 1
        fi
    fi

    # 不同频段配置
    case "$band" in
        5G)
            run_sudo_command "$password" "sed -i 's/^hw_mode=.*/hw_mode=a/' \"$CONFIG_FILE\""
            run_sudo_command "$password" "sed -i 's/^channel=.*/channel=36/' \"$CONFIG_FILE\""
            ;;
        24G)
            run_sudo_command "$password" "cp -f \"$CONFIG_FILE\" \"$BACKUP_FILE\""
            run_sudo_command "$password" "sed -i 's/^hw_mode=.*/hw_mode=g/' \"$CONFIG_FILE\""
            run_sudo_command "$password" "sed -i 's/^channel=.*/channel=6/' \"$CONFIG_FILE\""
            ;;
        *)
            echo "错误：未知频段类型 $band"
            return 1
            ;;
    esac
}

#  启动AP 
start_ap() {
    local band="$1"
    local interface="${2:-}"
    
    # 配置检查
    if ! [ -f "$CONFIG_FILE" ]; then
        echo "错误：配置文件 $CONFIG_FILE 不存在"
        exit 1
    fi

    prepare_config "$band" "$interface" || exit 1

    # 启动服务
    echo "正在启动 $band AP（接口：$(grep '^interface=' $CONFIG_FILE | cut -d= -f2)）..."
    run_sudo_command "$password" "pkill hostapd"
    run_sudo_command "$password" "hostapd -B \"$CONFIG_FILE\""
    sleep 2
    # echo "状态检查: $(systemctl is-active hostapd)"
}

#  关闭AP 
stop_ap() {
    echo "正在关闭AP..."
    run_sudo_command "$password" "pkill hostapd"
    [ -f "$BACKUP_FILE" ] && run_sudo_command "$password" "mv -f \"$BACKUP_FILE\" \"$CONFIG_FILE\""
    echo "已恢复原始配置"
}

#  使用说明 
usage() {
    cat <<EOF
智能AP管理脚本（v3.0）
用法：
  启动: $0 start <5G|24G> [接口名]
  停止: $0 stop

示例：
  $0 start 5G          # 使用默认p2p0接口
  $0 start 24G wlan0   # 指定wlan0接口
  $0 stop              # 关闭AP并恢复配置

特性：
  ✔ 自动检测p2p0/wlan0接口
  ✔ 动态修改hostapd配置
  ✔ 配置备份/恢复功能
EOF
}

#  主逻辑 
case "$1" in
    start)
        shift
        case "$1" in
            5G|24G)
                start_ap "$1" "$2"
                ;;
            *)
                usage
                exit 1
                ;;
        esac
        ;;
    stop)
        stop_ap
        ;;
    *)
        usage
        exit 1
        ;;
esac