#include <stdio.h>
#include <dirent.h>
#include <ctype.h>
#include <string.h>
#include <fcntl.h>
#include <errno.h>
#include <sys/ioctl.h>
#include <stdint.h>
#include <linux/usbdevice_fs.h>
#include <unistd.h>
#include <stdlib.h>
#include <linux/usb/ch9.h>

#define USB_DEV_DIR "/dev/bus/usb"

struct usb_device_id {
    uint16_t matchFlags;

    uint16_t idVendor;
    uint16_t idProduct;

    uint8_t bInterfaceNumber;

    uint8_t bInterfaceClass;
    uint8_t bInterfaceSubClass;
    uint8_t bInterfaceProtocol;
};

#define USB_DEVICE_ID_MATCH_VENDOR          0x0001
#define USB_DEVICE_ID_MATCH_PRODUCT         0x0002
#define USB_DEVICE_ID_MATCH_INT_NUMBER      0x0004
#define USB_DEVICE_ID_MATCH_INT_CLASS       0x0008
#define USB_DEVICE_ID_MATCH_INT_SUBCLASS    0x0010
#define USB_DEVICE_ID_MATCH_INT_PROTOCOL    0x0020

#define USB_DEVICE_INTERFACE_NUMBER(vend, prod, num) \
    .matchFlags = USB_DEVICE_ID_MATCH_VENDOR | \
            USB_DEVICE_ID_MATCH_PRODUCT | \
            USB_DEVICE_ID_MATCH_INT_NUMBER, \
    .idVendor = (vend), \
    .idProduct = (prod), \
    .bInterfaceNumber = (num)

#define USB_VENDOR_AND_INTERFACE_INFO(vend, cl, sc, pr) \
    .matchFlags = (USB_DEVICE_ID_MATCH_VENDOR | \
            USB_DEVICE_ID_MATCH_INT_CLASS | \
            USB_DEVICE_ID_MATCH_INT_SUBCLASS | \
            USB_DEVICE_ID_MATCH_INT_PROTOCOL), \
    .idVendor = (vend), \
    .bInterfaceClass = (cl), \
    .bInterfaceSubClass = (sc), \
    .bInterfaceProtocol = (pr)


static const struct usb_device_id liotIds[] = 
{
    {USB_DEVICE_INTERFACE_NUMBER(0x3505, 0x1001, 4)},
    {USB_DEVICE_INTERFACE_NUMBER(0x3505, 0x1002, 4)},
    {USB_VENDOR_AND_INTERFACE_INFO(0x3505, 0xff, 0x03, 0x12)},
};

static char g_devPath[128] = {0};
static int g_epIn;
static int g_epOut;
static int g_epLen;
static int g_interface;

int MatchId(struct usb_device_id *deviceId)
{
    int i;
    for(i=0; i < (sizeof(liotIds)/sizeof(struct usb_device_id)); i++)
    {
        if(liotIds[i].matchFlags & USB_DEVICE_ID_MATCH_VENDOR)
        {
            if(deviceId->idVendor != liotIds[i].idVendor)
                continue;
        }
        if(liotIds[i].matchFlags & USB_DEVICE_ID_MATCH_PRODUCT)
        {
            if(deviceId->idProduct != liotIds[i].idProduct)
                continue;
        }
        if(liotIds[i].matchFlags & USB_DEVICE_ID_MATCH_INT_NUMBER)
        {
            if(deviceId->bInterfaceNumber != liotIds[i].bInterfaceNumber)
                continue;
        }
        if(liotIds[i].matchFlags & USB_DEVICE_ID_MATCH_INT_CLASS)
        {
            if(deviceId->bInterfaceClass != liotIds[i].bInterfaceClass)
                continue;
        }
        if(liotIds[i].matchFlags & USB_DEVICE_ID_MATCH_INT_SUBCLASS)
        {
            if(deviceId->bInterfaceSubClass != liotIds[i].bInterfaceSubClass)
                continue;
        }
        if(liotIds[i].matchFlags & USB_DEVICE_ID_MATCH_INT_PROTOCOL)
        {
            if(deviceId->bInterfaceProtocol != liotIds[i].bInterfaceProtocol)
                continue;
        }
        return 1;
    }
    return 0;
}

int Liot_GetDesc(char *devPath)
{
    int fd = 0;
    unsigned char *desc;
    int descLen, len = 0;
    struct usb_device_id deviceId;

    printf("Liot_GetDesc enter\n");
    fd = open(devPath, O_RDWR | O_NOCTTY);
    if(fd < 0)
    {
        printf("%s open failed\n", devPath);
        return -1;
    }

    desc = (uint8_t *)malloc(2048);
    if(!desc)
    {
        printf("malloc failed\n");
        close(fd);
        return -1;
    }
    memset(desc, '0', 2048);

    descLen = read(fd, desc, 2048);
    while(len < descLen)
    {
        struct usb_descriptor_header *descHeader = (struct usb_descriptor_header *)(desc + len);
        if(descHeader->bLength == 0)
            break;
        if((descHeader->bLength == USB_DT_DEVICE_SIZE) && (descHeader->bDescriptorType == USB_DT_DEVICE))
        {
            struct usb_device_descriptor *DevDesc = (struct usb_interface_descriptor *)descHeader;
            deviceId.idVendor = DevDesc->idVendor;
            deviceId.idProduct = DevDesc->idProduct;
        }
        else if((descHeader->bLength == USB_DT_INTERFACE_SIZE) && (descHeader->bDescriptorType == USB_DT_INTERFACE))
        {
            struct usb_interface_descriptor *interfaceDesc = (struct usb_interface_descriptor *)descHeader;
            deviceId.bInterfaceNumber = interfaceDesc->bInterfaceNumber;
            deviceId.bInterfaceClass = interfaceDesc->bInterfaceClass;
            deviceId.bInterfaceSubClass = interfaceDesc->bInterfaceSubClass;
            deviceId.bInterfaceProtocol = interfaceDesc->bInterfaceProtocol;
        }
        else if((descHeader->bLength == USB_DT_ENDPOINT_SIZE) && (descHeader->bDescriptorType == USB_DT_ENDPOINT))
        {
            struct usb_endpoint_descriptor *epDesc = (struct usb_endpoint_descriptor *)descHeader;
            if(MatchId(&deviceId))
            {
                //get ep info
                printf("Match!!!\n");
                printf("idVendor:0x%04x\tidProduct:0x%04x\tbInterfaceNumber:%d\tbInterfaceClass:0x%02x\tbInterfaceSubClass:0x%02x\tbInterfaceProtocol:0x%02x\n",
                            deviceId.idVendor, deviceId.idProduct, deviceId.bInterfaceNumber,
                            deviceId.bInterfaceClass, deviceId.bInterfaceSubClass, deviceId.bInterfaceProtocol);
                if((epDesc->bmAttributes & USB_ENDPOINT_XFERTYPE_MASK) == USB_ENDPOINT_XFER_BULK)
                {
                    if (epDesc->bEndpointAddress & USB_ENDPOINT_DIR_MASK)
                    {
                        g_epIn = epDesc->bEndpointAddress;
                    }
                    else
                    {
                        g_epOut = epDesc->bEndpointAddress;
                        g_epLen = epDesc->wMaxPacketSize;
                        g_interface = deviceId.bInterfaceNumber;
                        printf("epIn:%d, epOut:%d, epLen:%d, interfaceNum:%d\n", g_epIn, g_epOut, g_epLen, g_interface);
                        //epIn first
                        free(desc);
                        close(fd);
                        return 0;
                    }
                }
            }
        }
        len += descHeader->bLength;
    }
    free(desc);
    close(fd);
    return -1;
}

int Liot_FindDevice(const char *dirPath)
{
    DIR *dir = NULL; //dev/bus/usb
    struct dirent *entry; ///dev/bus/usb/001
    char pathTemp[256] = {0};
    char devPath[128] = {0};

    dir = opendir(dirPath);
    if(dir == NULL)
    {
        printf("%s open failed!!\n", dirPath);
        return -1;
    }
    while((entry = readdir(dir)) != NULL)
    {
        if(!isdigit(entry->d_name[0]))
            continue;
        // printf("dev:%s\n", entry->d_name);
        if(entry->d_type == DT_DIR)
        {
            snprintf(pathTemp, sizeof(pathTemp), "%s/%s", dirPath, entry->d_name);
            // printf("path:%s\n", path);
            if(!Liot_FindDevice(pathTemp))
                return 0;
        }

        //读取设备描述符，判断是否为可用模组
        snprintf(devPath, sizeof(devPath), "%s/%s", dirPath, entry->d_name);
        if(!Liot_GetDesc(devPath))
        {
            strcpy(g_devPath, devPath);
            printf("dev name:%s\n", g_devPath);
            return 0;
        }
    }
    closedir(dir);
    return -1;
}

void ControlSetup(int fd)
{
    struct usbdevfs_ctrltransfer control;

    // control message get by wireshark
    control.bRequestType = 0x21;
    control.bRequest = 0x22;
    control.wValue = 0x0003;
    control.wIndex = 2;
    control.wLength = 0;
    control.data = NULL;

    ioctl(fd, USBDEVFS_CONTROL, &control);
}

int Liot_OpenDev(int *fd)
{
    int ret;
    struct usbdevfs_getdriver usbDev;
    usbDev.interface = g_interface;

    *fd = open(g_devPath, O_RDWR | O_NOCTTY);
    if(*fd < 0)
    {
        printf("usb dev open fail, fd:%d", *fd);
        return -1;
    }

    ret = ioctl(*fd, USBDEVFS_GETDRIVER, &usbDev);
    if(ret && errno != ENODATA)
    {
        printf("USBDEVFS_GETDRIVER ret=%d errno: %d (%s)\n", ret, errno, strerror(errno));
        close(*fd);
        return -1;
    }
    else if(ret == 0)
    {
        struct usbdevfs_ioctl operate;

        operate.data = NULL;
        operate.ifno = g_interface;
        operate.ioctl_code = USBDEVFS_DISCONNECT;

        ret = ioctl(*fd, USBDEVFS_IOCTL, &operate);
        if (ret)
        {
            printf("USBDEVFS_DISCONNECT ret=%d errno: %d (%s)\n", ret, errno, strerror(errno));
            close(*fd);
            return -1;
        }
    }

    ret =ioctl(*fd, USBDEVFS_CLAIMINTERFACE, &g_interface);
    if (ret) {
        printf("USBDEVFS_CLAIMINTERFACE ret=%d, errno: %d (%s)\n", ret, errno, strerror(errno));
        close(*fd);
        return -1;
    }

    ControlSetup(*fd);
    return 0;
}

int Liot_CloseDev(int *fd)
{
    close(*fd);
}

int BulkOut(int fd, const uint8_t *data, int len)
{
    int ret, zlp = 0;
    struct usbdevfs_bulktransfer bulk;
    uint8_t lastData;

    bulk.ep = g_epOut;
    bulk.len = len;
    bulk.data = (void *)data;
    bulk.timeout = 15000;

    if((len%g_epLen) == 0)
    {
        printf("zlp");
        zlp = 1;
        lastData = data[len];
        len--;
    }

    ret = ioctl(fd, USBDEVFS_BULK, &bulk);
    if(ret != len)
    {
        printf("bulk out err : %d\n", ret);
        return -1;
    }
    if(zlp)
    {
        bulk.len = 1;
        bulk.data = &lastData;
        ioctl(fd, USBDEVFS_BULK, &bulk);
    }
    return 0;
}

int BulkIn(int fd, uint8_t *data, int len, int timeout)
{
    int ret;
    struct usbdevfs_bulktransfer bulk;

    bulk.ep = g_epIn | 0x80;
    bulk.len = len;
    bulk.data = data;
    bulk.timeout = timeout;

    ret = ioctl(fd, USBDEVFS_BULK, &bulk);
    if(ret == -1)
    {
        // printf("bulk in err : %d\n", ret);
        return -1;
    }

    return 0;
}

int SendAtCmd(int fd, char *atCmd, int timeout)
{
    int ret;
    uint8_t data[1024] = {0};

    if(atCmd == NULL)
    {
        printf("AT CMD ERROR\n");
        return -1;
    }

    BulkOut(fd, (uint8_t *)atCmd, strlen(atCmd));
    while(1)
    {
        memset(data, '\0', sizeof(data));
        ret = BulkIn(fd, data, 1024, timeout);
        if(ret == 0)
        {
            printf("%s\n", data);
            if(NULL != strstr((char *)data, "OK"))
            {
                return 0;
            }
        }
        else
        {
            return -1;
        }
    }
    return 0;
}

int main(void)
{
    int fd;
    char at[128] = {0};

    if(Liot_FindDevice(USB_DEV_DIR))
    {
        printf("Not find dev!!!\n");
        return -1;
    }
    printf("find dev!!!\n");

    if(Liot_OpenDev(&fd))
    {
        printf("Open dev failed!!\n");
        return -1;
    }
    SendAtCmd(fd, "AT+LSWVER\r", 1000);
    // while(1)
    {
        memset(at, '\0', sizeof(at));
        sprintf(at,"%s","at+cfun=4");
        at[strlen(at)] = '\r';
        SendAtCmd(fd, at, 1000);

        memset(at, '\0', sizeof(at));
        sprintf(at,"%s","at+cgdcont=1,\"IP\",\"cmhk\"");
        at[strlen(at)] = '\r';
        SendAtCmd(fd, at, 1000);

        memset(at, '\0', sizeof(at));
        sprintf(at,"%s","at+cfun=1");
        at[strlen(at)] = '\r';
        SendAtCmd(fd, at, 1000);

        memset(at, '\0', sizeof(at));
        sprintf(at,"%s","at+cops?");
        at[strlen(at)] = '\r';
        SendAtCmd(fd, at, 1000);
        
        memset(at, '\0', sizeof(at));
        sprintf(at,"%s","at+lwandial=1,3,1");
        at[strlen(at)] = '\r';
        SendAtCmd(fd, at, 1000);
    }

    Liot_CloseDev(&fd);

    return 0;
}
