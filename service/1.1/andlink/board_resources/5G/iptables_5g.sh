#!/bin/bash

delete_rule() {
  local RULE=$1
    while iptables -t nat -C POSTROUTING $RULE &> /dev/null; do
        iptables -t nat -D POSTROUTING $RULE
    done
}

add_rule() {
  local RULE=$1
  iptables -t nat -A POSTROUTING $RULE
}

delete_rule "-s ***********/24 ! -d ***********/24 -j MASQUERADE"
delete_rule "-s ***********/24 -o wlan0 -j MASQUERADE"
delete_rule "-s ***********/24 -o wlan0 -j MASQUERADE"
delete_rule "-s ***********/24 -o eth0 -j MASQUERADE"
delete_rule "-s ***********/24 -o eth2 -j MASQUERADE"
delete_rule "-s ***********/24 -o eth2 -j MASQUERADE"
delete_rule "-s ***********/24 -o p2p0 -j MASQUERADE"



iptables -t nat -A POSTROUTING -s ***********/24 ! -d ***********/24 -j MASQUERADE
iptables -t nat -A POSTROUTING -s ***********/24 -o eth2 -j MASQUERADE
iptables -t nat -A POSTROUTING -s ***********/24 -o eth2 -j MASQUERADE
# iptables -t nat -A POSTROUTING -s ***********/24 -o eth0 -j MASQUERADE
# iptables -t nat -A POSTROUTING -s ***********/24 -o p2p0 -j MASQUERADE


# sed -i '3i\nameserver *******' /etc/resolv.conf





