#!/bin/bash

function run_sudo_command()
{
    local password="$1"
    local command="$2"
    # 执行命令并捕获输出和错误信息
    output=$(echo "$password" | sudo -S sh -c "$command" 2>&1)
    exit_status=$?

    if [ $exit_status -ne 0 ]; then
        echo "执行命令时出错: $output"
    else
        echo "命令执行成功。"
    fi
}

password1="temppwd"
# 使用单引号包裹整个命令字符串，避免双引号解析问题
command1='tshark -i eth0 -ta -f "tcp and port 10000" -Y "websocket.opcode == 1" -T fields -e frame.time -e ip.src -e ip.dst -e text -l >> /home/<USER>/mytest.log 2>&1 &'

run_sudo_command "$password1" "$command1"
