Invoking command in '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/build/expression': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/foxy /usr/bin/cmake /mine/worktrees/unitree-debug/xiaoli_application_ros2/src/expression -DCMAKE_CUSTOM_PRODUCT_NO=1_0 -DCMAKE_CUSTOM_LIB_INSTALL=/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/env/lib --no-warn-unused-cli -DCMAKE_INSTALL_PREFIX=/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install/expression
Invoked command in '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/build/expression' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/foxy /usr/bin/cmake /mine/worktrees/unitree-debug/xiaoli_application_ros2/src/expression -DCMAKE_CUSTOM_PRODUCT_NO=1_0 -DCMAKE_CUSTOM_LIB_INSTALL=/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/env/lib --no-warn-unused-cli -DCMAKE_INSTALL_PREFIX=/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install/expression
Invoking command in '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/build/expression': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/foxy /usr/bin/cmake --build /mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/build/expression -- -j8 -l8
Invoked command in '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/build/expression' returned '-2': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/foxy /usr/bin/cmake --build /mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/build/expression -- -j8 -l8
