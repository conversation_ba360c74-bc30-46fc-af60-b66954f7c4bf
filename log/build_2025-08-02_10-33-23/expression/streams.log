[0.131s] Invoking command in '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/build/expression': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/foxy /usr/bin/cmake /mine/worktrees/unitree-debug/xiaoli_application_ros2/src/expression -DCMAKE_CUSTOM_PRODUCT_NO=1_0 -DCMAKE_CUSTOM_LIB_INSTALL=/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/env/lib --no-warn-unused-cli -DCMAKE_INSTALL_PREFIX=/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install/expression
[0.157s] Not searching for unused variables given on the command line.
[0.554s] -- The C compiler identification is GNU 9.4.0
[0.973s] -- The CXX compiler identification is GNU 9.4.0
[1.040s] -- Check for working C compiler: /usr/bin/cc
[1.899s] -- Check for working C compiler: /usr/bin/cc -- works
[1.907s] -- Detecting C compiler ABI info
[2.756s] -- Detecting C compiler ABI info - done
[2.818s] -- Detecting C compile features
[2.818s] -- Detecting C compile features - done
[2.875s] -- Check for working CXX compiler: /usr/bin/c++
[3.733s] -- Check for working CXX compiler: /usr/bin/c++ -- works
[3.744s] -- Detecting CXX compiler ABI info
[4.542s] -- Detecting CXX compiler ABI info - done
[4.629s] -- Detecting CXX compile features
[4.629s] -- Detecting CXX compile features - done
[4.692s] -- Found ament_cmake: 0.9.12 (/opt/ros/foxy/share/ament_cmake/cmake)
[4.768s] -- Found PythonInterp: /usr/bin/python3 (found suitable version "3.8.10", minimum required is "3") 
[4.771s] -- Using PYTHON_EXECUTABLE: /usr/bin/python3
[5.312s] -- Found ament_index_cpp: 1.1.0 (/opt/ros/foxy/share/ament_index_cpp/cmake)
[5.328s] -- Found rclcpp: 2.4.3 (/opt/ros/foxy/share/rclcpp/cmake)
[5.393s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[5.404s] -- Found rosidl_adapter: 1.3.1 (/opt/ros/foxy/share/rosidl_adapter/cmake)
[5.660s] -- Found OpenSSL: /usr/lib/aarch64-linux-gnu/libcrypto.so (found version "1.1.1f")  
[5.713s] -- Found FastRTPS: /opt/ros/foxy/include  
[5.839s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[5.921s] -- Found rmw_implementation_cmake: 1.0.4 (/opt/ros/foxy/share/rmw_implementation_cmake/cmake)
[5.925s] -- Using RMW implementation 'rmw_cyclonedds_cpp' as default
[5.964s] -- Looking for pthread.h
[6.736s] -- Looking for pthread.h - found
[6.754s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD
[7.501s] -- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Failed
[7.511s] -- Looking for pthread_create in pthreads
[8.251s] -- Looking for pthread_create in pthreads - not found
[8.262s] -- Looking for pthread_create in pthread
[9.007s] -- Looking for pthread_create in pthread - found
[9.020s] -- Found Threads: TRUE  
[9.304s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.71.0/BoostConfig.cmake (found version "1.71.0") found components: filesystem 
[9.315s] -- Found ament_lint_auto: 0.9.8 (/opt/ros/foxy/share/ament_lint_auto/cmake)
[9.832s] -- Added test 'copyright' to check source files copyright and LICENSE
[9.955s] -- Added test 'cppcheck' to perform static code analysis on C / C++ code
[9.956s] -- Configured cppcheck include dirs: /mine/worktrees/unitree-debug/xiaoli_application_ros2/src/expression/include
[9.956s] -- Configured cppcheck exclude dirs and/or files: 
[10.064s] -- Added test 'cpplint' to check C / C++ code against the Google style
[10.065s] -- Configured cpplint exclude dirs and/or files: 
[10.127s] -- Added test 'lint_cmake' to check CMake code style
[10.259s] -- Added test 'uncrustify' to check C / C++ code style
[10.259s] -- Configured uncrustify additional arguments: 
[10.289s] -- Added test 'xmllint' to check XML markup files
[10.470s] -- Configuring done
[11.020s] -- Generating done
[11.290s] -- Build files have been written to: /mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/build/expression
[11.309s] Invoked command in '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/build/expression' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/foxy /usr/bin/cmake /mine/worktrees/unitree-debug/xiaoli_application_ros2/src/expression -DCMAKE_CUSTOM_PRODUCT_NO=1_0 -DCMAKE_CUSTOM_LIB_INSTALL=/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/env/lib --no-warn-unused-cli -DCMAKE_INSTALL_PREFIX=/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install/expression
[11.325s] Invoking command in '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/build/expression': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/foxy /usr/bin/cmake --build /mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/build/expression -- -j8 -l8
[11.620s] [35m[1mScanning dependencies of target expression_node[0m
[11.836s] [ 50%] [32mBuilding CXX object CMakeFiles/expression_node.dir/src/expression_node.cpp.o[0m
[11.916s] make[2]: *** [CMakeFiles/expression_node.dir/build.make:63: CMakeFiles/expression_node.dir/src/expression_node.cpp.o] Interrupt
[11.917s] make[1]: *** [CMakeFiles/Makefile2:78: CMakeFiles/expression_node.dir/all] Interrupt
[11.917s] make: *** [Makefile:141: all] Interrupt
[11.922s] Invoked command in '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/build/expression' returned '-2': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/foxy /usr/bin/cmake --build /mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/build/expression -- -j8 -l8
