[0.341s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build', '--packages-skip', 'mock', 'robdog_qt_tools', 'rtsp_image_capture', 'video_gst_neck', '--allow-overriding', 'rcutils', '--executor', 'sequential', '--parallel-worker', '1', '--build-base', '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/build', '--install-base', '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install', '--cmake-args', '-DCMAKE_CUSTOM_PRODUCT_NO=1_0', '-DCMAKE_CUSTOM_LIB_INSTALL=/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/env/lib', '--no-warn-unused-cli']
[0.341s] DEBUG:colcon:Parsed command line arguments: Namespace(allow_overriding=['rcutils'], ament_cmake_args=None, base_paths=['.'], build_base='/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/build', catkin_cmake_args=None, catkin_skip_building_tests=False, cmake_args=['-DCMAKE_CUSTOM_PRODUCT_NO=1_0', '-DCMAKE_CUSTOM_LIB_INSTALL=/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/env/lib', '--no-warn-unused-cli'], cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, cmake_target=None, cmake_target_skip_unavailable=False, continue_on_error=False, event_handlers=None, executor='sequential', ignore_user_meta=False, install_base='/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install', log_base=None, log_level=None, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x7f93342c10>>, merge_install=False, metas=['./colcon.meta'], mixin=None, mixin_files=None, mixin_verb=('build',), packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_end=None, packages_ignore=None, packages_ignore_regex=None, packages_select=None, packages_select_build_failed=False, packages_select_by_dep=None, packages_select_regex=None, packages_select_test_failures=False, packages_skip=['mock', 'robdog_qt_tools', 'rtsp_image_capture', 'video_gst_neck'], packages_skip_build_finished=False, packages_skip_by_dep=None, packages_skip_regex=None, packages_skip_test_passed=False, packages_skip_up_to=None, packages_start=None, packages_up_to=None, packages_up_to_regex=None, parallel_workers=1, paths=None, symlink_install=False, test_result_base=None, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x7f93342c10>, verb_name='build', verb_parser=<colcon_mixin.mixin.mixin_argument.MixinArgumentDecorator object at 0x7f93353130>)
[0.612s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.614s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.616s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.616s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.616s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.616s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.616s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/mine/worktrees/unitree-debug/xiaoli_application_ros2'
[0.620s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.621s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.622s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.626s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.627s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.629s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.630s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.630s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.630s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.678s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.678s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.678s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.680s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.681s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.687s] Level 1:colcon.colcon_core.package_identification:_identify(build_1.0) by extensions ['ignore', 'ignore_ament_install']
[0.688s] Level 1:colcon.colcon_core.package_identification:_identify(build_1.0) by extension 'ignore'
[0.690s] Level 1:colcon.colcon_core.package_identification:_identify(build_1.0) by extension 'ignore_ament_install'
[0.695s] Level 1:colcon.colcon_core.package_identification:_identify(build_1.0) by extensions ['colcon_pkg']
[0.697s] Level 1:colcon.colcon_core.package_identification:_identify(build_1.0) by extension 'colcon_pkg'
[0.699s] Level 1:colcon.colcon_core.package_identification:_identify(build_1.0) by extensions ['colcon_meta']
[0.702s] Level 1:colcon.colcon_core.package_identification:_identify(build_1.0) by extension 'colcon_meta'
[0.702s] Level 1:colcon.colcon_core.package_identification:_identify(build_1.0) by extensions ['ros']
[0.702s] Level 1:colcon.colcon_core.package_identification:_identify(build_1.0) by extension 'ros'
[0.710s] Level 1:colcon.colcon_core.package_identification:_identify(build_1.0) by extensions ['cmake', 'python']
[0.710s] Level 1:colcon.colcon_core.package_identification:_identify(build_1.0) by extension 'cmake'
[0.712s] Level 1:colcon.colcon_core.package_identification:_identify(build_1.0) by extension 'python'
[0.715s] Level 1:colcon.colcon_core.package_identification:_identify(build_1.0) by extensions ['python_setup_py']
[0.718s] Level 1:colcon.colcon_core.package_identification:_identify(build_1.0) by extension 'python_setup_py'
[0.723s] Level 1:colcon.colcon_core.package_identification:_identify(build_1.0/debug) by extensions ['ignore', 'ignore_ament_install']
[0.724s] Level 1:colcon.colcon_core.package_identification:_identify(build_1.0/debug) by extension 'ignore'
[0.726s] Level 1:colcon.colcon_core.package_identification:_identify(build_1.0/debug) by extension 'ignore_ament_install'
[0.742s] Level 1:colcon.colcon_core.package_identification:_identify(build_1.0/debug) by extensions ['colcon_pkg']
[0.755s] Level 1:colcon.colcon_core.package_identification:_identify(build_1.0/debug) by extension 'colcon_pkg'
[0.758s] Level 1:colcon.colcon_core.package_identification:_identify(build_1.0/debug) by extensions ['colcon_meta']
[0.766s] Level 1:colcon.colcon_core.package_identification:_identify(build_1.0/debug) by extension 'colcon_meta'
[0.766s] Level 1:colcon.colcon_core.package_identification:_identify(build_1.0/debug) by extensions ['ros']
[0.766s] Level 1:colcon.colcon_core.package_identification:_identify(build_1.0/debug) by extension 'ros'
[0.774s] Level 1:colcon.colcon_core.package_identification:_identify(build_1.0/debug) by extensions ['cmake', 'python']
[0.775s] Level 1:colcon.colcon_core.package_identification:_identify(build_1.0/debug) by extension 'cmake'
[0.777s] Level 1:colcon.colcon_core.package_identification:_identify(build_1.0/debug) by extension 'python'
[0.780s] Level 1:colcon.colcon_core.package_identification:_identify(build_1.0/debug) by extensions ['python_setup_py']
[0.780s] Level 1:colcon.colcon_core.package_identification:_identify(build_1.0/debug) by extension 'python_setup_py'
[0.786s] Level 1:colcon.colcon_core.package_identification:_identify(build_1.0/debug/build) by extensions ['ignore', 'ignore_ament_install']
[0.787s] Level 1:colcon.colcon_core.package_identification:_identify(build_1.0/debug/build) by extension 'ignore'
[0.790s] Level 1:colcon.colcon_core.package_identification:_identify(build_1.0/debug/build) ignored
[0.796s] Level 1:colcon.colcon_core.package_identification:_identify(build_1.0/debug/env) by extensions ['ignore', 'ignore_ament_install']
[0.797s] Level 1:colcon.colcon_core.package_identification:_identify(build_1.0/debug/env) by extension 'ignore'
[0.813s] Level 1:colcon.colcon_core.package_identification:_identify(build_1.0/debug/env) by extension 'ignore_ament_install'
[0.830s] Level 1:colcon.colcon_core.package_identification:_identify(build_1.0/debug/env) by extensions ['colcon_pkg']
[0.832s] Level 1:colcon.colcon_core.package_identification:_identify(build_1.0/debug/env) by extension 'colcon_pkg'
[0.836s] Level 1:colcon.colcon_core.package_identification:_identify(build_1.0/debug/env) by extensions ['colcon_meta']
[0.836s] Level 1:colcon.colcon_core.package_identification:_identify(build_1.0/debug/env) by extension 'colcon_meta'
[0.836s] Level 1:colcon.colcon_core.package_identification:_identify(build_1.0/debug/env) by extensions ['ros']
[0.839s] Level 1:colcon.colcon_core.package_identification:_identify(build_1.0/debug/env) by extension 'ros'
[0.846s] Level 1:colcon.colcon_core.package_identification:_identify(build_1.0/debug/env) by extensions ['cmake', 'python']
[0.846s] Level 1:colcon.colcon_core.package_identification:_identify(build_1.0/debug/env) by extension 'cmake'
[0.848s] Level 1:colcon.colcon_core.package_identification:_identify(build_1.0/debug/env) by extension 'python'
[0.852s] Level 1:colcon.colcon_core.package_identification:_identify(build_1.0/debug/env) by extensions ['python_setup_py']
[0.855s] Level 1:colcon.colcon_core.package_identification:_identify(build_1.0/debug/env) by extension 'python_setup_py'
[0.860s] Level 1:colcon.colcon_core.package_identification:_identify(build_1.0/debug/env/lib) by extensions ['ignore', 'ignore_ament_install']
[0.861s] Level 1:colcon.colcon_core.package_identification:_identify(build_1.0/debug/env/lib) by extension 'ignore'
[0.862s] Level 1:colcon.colcon_core.package_identification:_identify(build_1.0/debug/env/lib) by extension 'ignore_ament_install'
[0.869s] Level 1:colcon.colcon_core.package_identification:_identify(build_1.0/debug/env/lib) by extensions ['colcon_pkg']
[0.870s] Level 1:colcon.colcon_core.package_identification:_identify(build_1.0/debug/env/lib) by extension 'colcon_pkg'
[0.871s] Level 1:colcon.colcon_core.package_identification:_identify(build_1.0/debug/env/lib) by extensions ['colcon_meta']
[0.872s] Level 1:colcon.colcon_core.package_identification:_identify(build_1.0/debug/env/lib) by extension 'colcon_meta'
[0.872s] Level 1:colcon.colcon_core.package_identification:_identify(build_1.0/debug/env/lib) by extensions ['ros']
[0.874s] Level 1:colcon.colcon_core.package_identification:_identify(build_1.0/debug/env/lib) by extension 'ros'
[0.881s] Level 1:colcon.colcon_core.package_identification:_identify(build_1.0/debug/env/lib) by extensions ['cmake', 'python']
[0.882s] Level 1:colcon.colcon_core.package_identification:_identify(build_1.0/debug/env/lib) by extension 'cmake'
[0.883s] Level 1:colcon.colcon_core.package_identification:_identify(build_1.0/debug/env/lib) by extension 'python'
[0.887s] Level 1:colcon.colcon_core.package_identification:_identify(build_1.0/debug/env/lib) by extensions ['python_setup_py']
[0.887s] Level 1:colcon.colcon_core.package_identification:_identify(build_1.0/debug/env/lib) by extension 'python_setup_py'
[0.892s] Level 1:colcon.colcon_core.package_identification:_identify(build_1.0/debug/install) by extensions ['ignore', 'ignore_ament_install']
[0.892s] Level 1:colcon.colcon_core.package_identification:_identify(build_1.0/debug/install) by extension 'ignore'
[0.892s] Level 1:colcon.colcon_core.package_identification:_identify(build_1.0/debug/install) ignored
[0.895s] Level 1:colcon.colcon_core.package_identification:_identify(include) by extensions ['ignore', 'ignore_ament_install']
[0.896s] Level 1:colcon.colcon_core.package_identification:_identify(include) by extension 'ignore'
[0.896s] Level 1:colcon.colcon_core.package_identification:_identify(include) by extension 'ignore_ament_install'
[0.896s] Level 1:colcon.colcon_core.package_identification:_identify(include) by extensions ['colcon_pkg']
[0.896s] Level 1:colcon.colcon_core.package_identification:_identify(include) by extension 'colcon_pkg'
[0.897s] Level 1:colcon.colcon_core.package_identification:_identify(include) by extensions ['colcon_meta']
[0.897s] Level 1:colcon.colcon_core.package_identification:_identify(include) by extension 'colcon_meta'
[0.897s] Level 1:colcon.colcon_core.package_identification:_identify(include) by extensions ['ros']
[0.897s] Level 1:colcon.colcon_core.package_identification:_identify(include) by extension 'ros'
[0.897s] Level 1:colcon.colcon_core.package_identification:_identify(include) by extensions ['cmake', 'python']
[0.898s] Level 1:colcon.colcon_core.package_identification:_identify(include) by extension 'cmake'
[0.898s] Level 1:colcon.colcon_core.package_identification:_identify(include) by extension 'python'
[0.898s] Level 1:colcon.colcon_core.package_identification:_identify(include) by extensions ['python_setup_py']
[0.898s] Level 1:colcon.colcon_core.package_identification:_identify(include) by extension 'python_setup_py'
[0.900s] Level 1:colcon.colcon_core.package_identification:_identify(include/homi_com) by extensions ['ignore', 'ignore_ament_install']
[0.902s] Level 1:colcon.colcon_core.package_identification:_identify(include/homi_com) by extension 'ignore'
[0.902s] Level 1:colcon.colcon_core.package_identification:_identify(include/homi_com) by extension 'ignore_ament_install'
[0.902s] Level 1:colcon.colcon_core.package_identification:_identify(include/homi_com) by extensions ['colcon_pkg']
[0.902s] Level 1:colcon.colcon_core.package_identification:_identify(include/homi_com) by extension 'colcon_pkg'
[0.903s] Level 1:colcon.colcon_core.package_identification:_identify(include/homi_com) by extensions ['colcon_meta']
[0.903s] Level 1:colcon.colcon_core.package_identification:_identify(include/homi_com) by extension 'colcon_meta'
[0.903s] Level 1:colcon.colcon_core.package_identification:_identify(include/homi_com) by extensions ['ros']
[0.903s] Level 1:colcon.colcon_core.package_identification:_identify(include/homi_com) by extension 'ros'
[0.903s] Level 1:colcon.colcon_core.package_identification:_identify(include/homi_com) by extensions ['cmake', 'python']
[0.903s] Level 1:colcon.colcon_core.package_identification:_identify(include/homi_com) by extension 'cmake'
[0.904s] Level 1:colcon.colcon_core.package_identification:_identify(include/homi_com) by extension 'python'
[0.904s] Level 1:colcon.colcon_core.package_identification:_identify(include/homi_com) by extensions ['python_setup_py']
[0.907s] Level 1:colcon.colcon_core.package_identification:_identify(include/homi_com) by extension 'python_setup_py'
[0.908s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree) by extensions ['ignore', 'ignore_ament_install']
[0.909s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree) by extension 'ignore'
[0.911s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree) by extension 'ignore_ament_install'
[0.911s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree) by extensions ['colcon_pkg']
[0.911s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree) by extension 'colcon_pkg'
[0.911s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree) by extensions ['colcon_meta']
[0.911s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree) by extension 'colcon_meta'
[0.911s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree) by extensions ['ros']
[0.912s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree) by extension 'ros'
[0.912s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree) by extensions ['cmake', 'python']
[0.912s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree) by extension 'cmake'
[0.912s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree) by extension 'python'
[0.912s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree) by extensions ['python_setup_py']
[0.912s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree) by extension 'python_setup_py'
[0.913s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds) by extensions ['ignore', 'ignore_ament_install']
[0.913s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds) by extension 'ignore'
[0.913s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds) by extension 'ignore_ament_install'
[0.914s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds) by extensions ['colcon_pkg']
[0.914s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds) by extension 'colcon_pkg'
[0.916s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds) by extensions ['colcon_meta']
[0.916s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds) by extension 'colcon_meta'
[0.916s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds) by extensions ['ros']
[0.916s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds) by extension 'ros'
[0.917s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds) by extensions ['cmake', 'python']
[0.917s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds) by extension 'cmake'
[0.917s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds) by extension 'python'
[0.917s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds) by extensions ['python_setup_py']
[0.918s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds) by extension 'python_setup_py'
[0.920s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsc) by extensions ['ignore', 'ignore_ament_install']
[0.921s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsc) by extension 'ignore'
[0.921s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsc) by extension 'ignore_ament_install'
[0.921s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsc) by extensions ['colcon_pkg']
[0.921s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsc) by extension 'colcon_pkg'
[0.922s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsc) by extensions ['colcon_meta']
[0.922s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsc) by extension 'colcon_meta'
[0.922s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsc) by extensions ['ros']
[0.922s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsc) by extension 'ros'
[0.923s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsc) by extensions ['cmake', 'python']
[0.923s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsc) by extension 'cmake'
[0.928s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsc) by extension 'python'
[0.928s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsc) by extensions ['python_setup_py']
[0.928s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsc) by extension 'python_setup_py'
[0.930s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsi) by extensions ['ignore', 'ignore_ament_install']
[0.932s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsi) by extension 'ignore'
[0.932s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsi) by extension 'ignore_ament_install'
[0.932s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsi) by extensions ['colcon_pkg']
[0.932s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsi) by extension 'colcon_pkg'
[0.932s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsi) by extensions ['colcon_meta']
[0.932s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsi) by extension 'colcon_meta'
[0.933s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsi) by extensions ['ros']
[0.933s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsi) by extension 'ros'
[0.933s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsi) by extensions ['cmake', 'python']
[0.933s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsi) by extension 'cmake'
[0.933s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsi) by extension 'python'
[0.933s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsi) by extensions ['python_setup_py']
[0.933s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsi) by extension 'python_setup_py'
[0.934s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt) by extensions ['ignore', 'ignore_ament_install']
[0.938s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt) by extension 'ignore'
[0.938s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt) by extension 'ignore_ament_install'
[0.938s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt) by extensions ['colcon_pkg']
[0.938s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt) by extension 'colcon_pkg'
[0.938s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt) by extensions ['colcon_meta']
[0.938s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt) by extension 'colcon_meta'
[0.938s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt) by extensions ['ros']
[0.938s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt) by extension 'ros'
[0.939s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt) by extensions ['cmake', 'python']
[0.939s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt) by extension 'cmake'
[0.939s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt) by extension 'python'
[0.939s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt) by extensions ['python_setup_py']
[0.939s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt) by extension 'python_setup_py'
[0.940s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/atomics) by extensions ['ignore', 'ignore_ament_install']
[0.944s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/atomics) by extension 'ignore'
[0.944s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/atomics) by extension 'ignore_ament_install'
[0.944s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/atomics) by extensions ['colcon_pkg']
[0.944s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/atomics) by extension 'colcon_pkg'
[0.944s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/atomics) by extensions ['colcon_meta']
[0.944s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/atomics) by extension 'colcon_meta'
[0.944s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/atomics) by extensions ['ros']
[0.944s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/atomics) by extension 'ros'
[0.945s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/atomics) by extensions ['cmake', 'python']
[0.945s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/atomics) by extension 'cmake'
[0.945s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/atomics) by extension 'python'
[0.945s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/atomics) by extensions ['python_setup_py']
[0.945s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/atomics) by extension 'python_setup_py'
[0.947s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/filesystem) by extensions ['ignore', 'ignore_ament_install']
[0.949s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/filesystem) by extension 'ignore'
[0.949s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/filesystem) by extension 'ignore_ament_install'
[0.949s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/filesystem) by extensions ['colcon_pkg']
[0.949s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/filesystem) by extension 'colcon_pkg'
[0.950s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/filesystem) by extensions ['colcon_meta']
[0.950s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/filesystem) by extension 'colcon_meta'
[0.950s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/filesystem) by extensions ['ros']
[0.950s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/filesystem) by extension 'ros'
[0.951s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/filesystem) by extensions ['cmake', 'python']
[0.954s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/filesystem) by extension 'cmake'
[0.954s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/filesystem) by extension 'python'
[0.955s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/filesystem) by extensions ['python_setup_py']
[0.955s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/filesystem) by extension 'python_setup_py'
[0.957s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/sockets) by extensions ['ignore', 'ignore_ament_install']
[0.958s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/sockets) by extension 'ignore'
[0.958s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/sockets) by extension 'ignore_ament_install'
[0.959s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/sockets) by extensions ['colcon_pkg']
[0.959s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/sockets) by extension 'colcon_pkg'
[0.959s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/sockets) by extensions ['colcon_meta']
[0.959s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/sockets) by extension 'colcon_meta'
[0.960s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/sockets) by extensions ['ros']
[0.960s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/sockets) by extension 'ros'
[0.960s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/sockets) by extensions ['cmake', 'python']
[0.960s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/sockets) by extension 'cmake'
[0.963s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/sockets) by extension 'python'
[0.964s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/sockets) by extensions ['python_setup_py']
[0.964s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/sockets) by extension 'python_setup_py'
[0.966s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/sync) by extensions ['ignore', 'ignore_ament_install']
[0.970s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/sync) by extension 'ignore'
[0.971s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/sync) by extension 'ignore_ament_install'
[0.971s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/sync) by extensions ['colcon_pkg']
[0.971s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/sync) by extension 'colcon_pkg'
[0.971s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/sync) by extensions ['colcon_meta']
[0.972s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/sync) by extension 'colcon_meta'
[0.972s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/sync) by extensions ['ros']
[0.972s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/sync) by extension 'ros'
[0.975s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/sync) by extensions ['cmake', 'python']
[0.975s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/sync) by extension 'cmake'
[0.975s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/sync) by extension 'python'
[0.975s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/sync) by extensions ['python_setup_py']
[0.976s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/sync) by extension 'python_setup_py'
[0.978s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/threads) by extensions ['ignore', 'ignore_ament_install']
[0.979s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/threads) by extension 'ignore'
[0.979s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/threads) by extension 'ignore_ament_install'
[0.979s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/threads) by extensions ['colcon_pkg']
[0.979s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/threads) by extension 'colcon_pkg'
[0.979s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/threads) by extensions ['colcon_meta']
[0.981s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/threads) by extension 'colcon_meta'
[0.981s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/threads) by extensions ['ros']
[0.981s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/threads) by extension 'ros'
[0.981s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/threads) by extensions ['cmake', 'python']
[0.983s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/threads) by extension 'cmake'
[0.984s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/threads) by extension 'python'
[0.984s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/threads) by extensions ['python_setup_py']
[0.984s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/threads) by extension 'python_setup_py'
[0.986s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/time) by extensions ['ignore', 'ignore_ament_install']
[0.988s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/time) by extension 'ignore'
[0.988s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/time) by extension 'ignore_ament_install'
[0.988s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/time) by extensions ['colcon_pkg']
[0.988s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/time) by extension 'colcon_pkg'
[0.989s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/time) by extensions ['colcon_meta']
[0.989s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/time) by extension 'colcon_meta'
[0.989s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/time) by extensions ['ros']
[0.989s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/time) by extension 'ros'
[0.989s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/time) by extensions ['cmake', 'python']
[0.989s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/time) by extension 'cmake'
[0.989s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/time) by extension 'python'
[0.989s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/time) by extensions ['python_setup_py']
[0.989s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/time) by extension 'python_setup_py'
[0.990s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/types) by extensions ['ignore', 'ignore_ament_install']
[0.990s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/types) by extension 'ignore'
[0.990s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/types) by extension 'ignore_ament_install'
[0.990s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/types) by extensions ['colcon_pkg']
[0.990s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/types) by extension 'colcon_pkg'
[0.993s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/types) by extensions ['colcon_meta']
[0.993s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/types) by extension 'colcon_meta'
[0.993s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/types) by extensions ['ros']
[0.993s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/types) by extension 'ros'
[0.993s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/types) by extensions ['cmake', 'python']
[0.993s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/types) by extension 'cmake'
[0.993s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/types) by extension 'python'
[0.994s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/types) by extensions ['python_setup_py']
[0.994s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/dds/ddsrt/types) by extension 'python_setup_py'
[0.995s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddsc) by extensions ['ignore', 'ignore_ament_install']
[0.998s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddsc) by extension 'ignore'
[0.998s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddsc) by extension 'ignore_ament_install'
[0.998s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddsc) by extensions ['colcon_pkg']
[0.999s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddsc) by extension 'colcon_pkg'
[0.999s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddsc) by extensions ['colcon_meta']
[0.999s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddsc) by extension 'colcon_meta'
[1.003s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddsc) by extensions ['ros']
[1.004s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddsc) by extension 'ros'
[1.006s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddsc) by extensions ['cmake', 'python']
[1.008s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddsc) by extension 'cmake'
[1.008s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddsc) by extension 'python'
[1.008s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddsc) by extensions ['python_setup_py']
[1.008s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddsc) by extension 'python_setup_py'
[1.010s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx) by extensions ['ignore', 'ignore_ament_install']
[1.013s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx) by extension 'ignore'
[1.013s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx) by extension 'ignore_ament_install'
[1.013s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx) by extensions ['colcon_pkg']
[1.013s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx) by extension 'colcon_pkg'
[1.013s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx) by extensions ['colcon_meta']
[1.013s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx) by extension 'colcon_meta'
[1.013s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx) by extensions ['ros']
[1.013s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx) by extension 'ros'
[1.013s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx) by extensions ['cmake', 'python']
[1.013s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx) by extension 'cmake'
[1.013s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx) by extension 'python'
[1.013s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx) by extensions ['python_setup_py']
[1.013s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx) by extension 'python_setup_py'
[1.015s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds) by extensions ['ignore', 'ignore_ament_install']
[1.018s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds) by extension 'ignore'
[1.018s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds) by extension 'ignore_ament_install'
[1.018s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds) by extensions ['colcon_pkg']
[1.018s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds) by extension 'colcon_pkg'
[1.018s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds) by extensions ['colcon_meta']
[1.018s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds) by extension 'colcon_meta'
[1.018s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds) by extensions ['ros']
[1.018s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds) by extension 'ros'
[1.018s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds) by extensions ['cmake', 'python']
[1.018s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds) by extension 'cmake'
[1.018s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds) by extension 'python'
[1.019s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds) by extensions ['python_setup_py']
[1.019s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds) by extension 'python_setup_py'
[1.020s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core) by extensions ['ignore', 'ignore_ament_install']
[1.024s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core) by extension 'ignore'
[1.024s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core) by extension 'ignore_ament_install'
[1.024s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core) by extensions ['colcon_pkg']
[1.024s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core) by extension 'colcon_pkg'
[1.024s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core) by extensions ['colcon_meta']
[1.024s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core) by extension 'colcon_meta'
[1.024s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core) by extensions ['ros']
[1.024s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core) by extension 'ros'
[1.024s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core) by extensions ['cmake', 'python']
[1.025s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core) by extension 'cmake'
[1.025s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core) by extension 'python'
[1.025s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core) by extensions ['python_setup_py']
[1.025s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core) by extension 'python_setup_py'
[1.027s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/cond) by extensions ['ignore', 'ignore_ament_install']
[1.028s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/cond) by extension 'ignore'
[1.028s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/cond) by extension 'ignore_ament_install'
[1.028s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/cond) by extensions ['colcon_pkg']
[1.028s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/cond) by extension 'colcon_pkg'
[1.028s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/cond) by extensions ['colcon_meta']
[1.028s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/cond) by extension 'colcon_meta'
[1.028s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/cond) by extensions ['ros']
[1.028s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/cond) by extension 'ros'
[1.028s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/cond) by extensions ['cmake', 'python']
[1.029s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/cond) by extension 'cmake'
[1.029s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/cond) by extension 'python'
[1.029s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/cond) by extensions ['python_setup_py']
[1.029s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/cond) by extension 'python_setup_py'
[1.030s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/cond/detail) by extensions ['ignore', 'ignore_ament_install']
[1.034s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/cond/detail) by extension 'ignore'
[1.034s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/cond/detail) by extension 'ignore_ament_install'
[1.034s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/cond/detail) by extensions ['colcon_pkg']
[1.034s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/cond/detail) by extension 'colcon_pkg'
[1.034s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/cond/detail) by extensions ['colcon_meta']
[1.034s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/cond/detail) by extension 'colcon_meta'
[1.034s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/cond/detail) by extensions ['ros']
[1.034s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/cond/detail) by extension 'ros'
[1.034s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/cond/detail) by extensions ['cmake', 'python']
[1.034s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/cond/detail) by extension 'cmake'
[1.034s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/cond/detail) by extension 'python'
[1.034s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/cond/detail) by extensions ['python_setup_py']
[1.034s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/cond/detail) by extension 'python_setup_py'
[1.037s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/detail) by extensions ['ignore', 'ignore_ament_install']
[1.037s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/detail) by extension 'ignore'
[1.038s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/detail) by extension 'ignore_ament_install'
[1.038s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/detail) by extensions ['colcon_pkg']
[1.038s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/detail) by extension 'colcon_pkg'
[1.038s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/detail) by extensions ['colcon_meta']
[1.038s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/detail) by extension 'colcon_meta'
[1.038s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/detail) by extensions ['ros']
[1.038s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/detail) by extension 'ros'
[1.038s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/detail) by extensions ['cmake', 'python']
[1.038s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/detail) by extension 'cmake'
[1.038s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/detail) by extension 'python'
[1.038s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/detail) by extensions ['python_setup_py']
[1.039s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/detail) by extension 'python_setup_py'
[1.040s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/policy) by extensions ['ignore', 'ignore_ament_install']
[1.042s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/policy) by extension 'ignore'
[1.042s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/policy) by extension 'ignore_ament_install'
[1.043s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/policy) by extensions ['colcon_pkg']
[1.043s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/policy) by extension 'colcon_pkg'
[1.043s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/policy) by extensions ['colcon_meta']
[1.043s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/policy) by extension 'colcon_meta'
[1.044s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/policy) by extensions ['ros']
[1.047s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/policy) by extension 'ros'
[1.047s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/policy) by extensions ['cmake', 'python']
[1.047s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/policy) by extension 'cmake'
[1.047s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/policy) by extension 'python'
[1.048s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/policy) by extensions ['python_setup_py']
[1.048s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/policy) by extension 'python_setup_py'
[1.050s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/policy/detail) by extensions ['ignore', 'ignore_ament_install']
[1.051s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/policy/detail) by extension 'ignore'
[1.052s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/policy/detail) by extension 'ignore_ament_install'
[1.052s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/policy/detail) by extensions ['colcon_pkg']
[1.052s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/policy/detail) by extension 'colcon_pkg'
[1.052s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/policy/detail) by extensions ['colcon_meta']
[1.053s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/policy/detail) by extension 'colcon_meta'
[1.053s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/policy/detail) by extensions ['ros']
[1.054s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/policy/detail) by extension 'ros'
[1.057s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/policy/detail) by extensions ['cmake', 'python']
[1.057s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/policy/detail) by extension 'cmake'
[1.057s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/policy/detail) by extension 'python'
[1.057s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/policy/detail) by extensions ['python_setup_py']
[1.057s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/policy/detail) by extension 'python_setup_py'
[1.059s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/status) by extensions ['ignore', 'ignore_ament_install']
[1.063s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/status) by extension 'ignore'
[1.063s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/status) by extension 'ignore_ament_install'
[1.063s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/status) by extensions ['colcon_pkg']
[1.063s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/status) by extension 'colcon_pkg'
[1.063s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/status) by extensions ['colcon_meta']
[1.063s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/status) by extension 'colcon_meta'
[1.064s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/status) by extensions ['ros']
[1.064s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/status) by extension 'ros'
[1.064s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/status) by extensions ['cmake', 'python']
[1.064s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/status) by extension 'cmake'
[1.064s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/status) by extension 'python'
[1.064s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/status) by extensions ['python_setup_py']
[1.064s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/status) by extension 'python_setup_py'
[1.066s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/status/detail) by extensions ['ignore', 'ignore_ament_install']
[1.066s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/status/detail) by extension 'ignore'
[1.067s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/status/detail) by extension 'ignore_ament_install'
[1.067s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/status/detail) by extensions ['colcon_pkg']
[1.067s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/status/detail) by extension 'colcon_pkg'
[1.067s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/status/detail) by extensions ['colcon_meta']
[1.067s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/status/detail) by extension 'colcon_meta'
[1.067s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/status/detail) by extensions ['ros']
[1.067s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/status/detail) by extension 'ros'
[1.067s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/status/detail) by extensions ['cmake', 'python']
[1.073s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/status/detail) by extension 'cmake'
[1.073s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/status/detail) by extension 'python'
[1.073s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/status/detail) by extensions ['python_setup_py']
[1.073s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/status/detail) by extension 'python_setup_py'
[1.075s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/xtypes) by extensions ['ignore', 'ignore_ament_install']
[1.075s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/xtypes) by extension 'ignore'
[1.075s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/xtypes) by extension 'ignore_ament_install'
[1.075s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/xtypes) by extensions ['colcon_pkg']
[1.075s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/xtypes) by extension 'colcon_pkg'
[1.075s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/xtypes) by extensions ['colcon_meta']
[1.078s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/xtypes) by extension 'colcon_meta'
[1.079s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/xtypes) by extensions ['ros']
[1.079s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/xtypes) by extension 'ros'
[1.079s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/xtypes) by extensions ['cmake', 'python']
[1.079s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/xtypes) by extension 'cmake'
[1.079s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/xtypes) by extension 'python'
[1.079s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/xtypes) by extensions ['python_setup_py']
[1.079s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/xtypes) by extension 'python_setup_py'
[1.081s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/xtypes/detail) by extensions ['ignore', 'ignore_ament_install']
[1.084s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/xtypes/detail) by extension 'ignore'
[1.084s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/xtypes/detail) by extension 'ignore_ament_install'
[1.084s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/xtypes/detail) by extensions ['colcon_pkg']
[1.085s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/xtypes/detail) by extension 'colcon_pkg'
[1.085s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/xtypes/detail) by extensions ['colcon_meta']
[1.085s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/xtypes/detail) by extension 'colcon_meta'
[1.086s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/xtypes/detail) by extensions ['ros']
[1.088s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/xtypes/detail) by extension 'ros'
[1.089s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/xtypes/detail) by extensions ['cmake', 'python']
[1.089s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/xtypes/detail) by extension 'cmake'
[1.089s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/xtypes/detail) by extension 'python'
[1.089s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/xtypes/detail) by extensions ['python_setup_py']
[1.089s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/core/xtypes/detail) by extension 'python_setup_py'
[1.090s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/domain) by extensions ['ignore', 'ignore_ament_install']
[1.093s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/domain) by extension 'ignore'
[1.093s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/domain) by extension 'ignore_ament_install'
[1.094s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/domain) by extensions ['colcon_pkg']
[1.094s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/domain) by extension 'colcon_pkg'
[1.094s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/domain) by extensions ['colcon_meta']
[1.094s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/domain) by extension 'colcon_meta'
[1.094s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/domain) by extensions ['ros']
[1.094s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/domain) by extension 'ros'
[1.094s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/domain) by extensions ['cmake', 'python']
[1.094s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/domain) by extension 'cmake'
[1.094s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/domain) by extension 'python'
[1.094s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/domain) by extensions ['python_setup_py']
[1.094s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/domain) by extension 'python_setup_py'
[1.096s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/domain/detail) by extensions ['ignore', 'ignore_ament_install']
[1.097s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/domain/detail) by extension 'ignore'
[1.098s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/domain/detail) by extension 'ignore_ament_install'
[1.098s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/domain/detail) by extensions ['colcon_pkg']
[1.098s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/domain/detail) by extension 'colcon_pkg'
[1.098s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/domain/detail) by extensions ['colcon_meta']
[1.098s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/domain/detail) by extension 'colcon_meta'
[1.098s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/domain/detail) by extensions ['ros']
[1.098s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/domain/detail) by extension 'ros'
[1.098s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/domain/detail) by extensions ['cmake', 'python']
[1.098s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/domain/detail) by extension 'cmake'
[1.099s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/domain/detail) by extension 'python'
[1.099s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/domain/detail) by extensions ['python_setup_py']
[1.099s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/domain/detail) by extension 'python_setup_py'
[1.101s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/domain/qos) by extensions ['ignore', 'ignore_ament_install']
[1.103s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/domain/qos) by extension 'ignore'
[1.103s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/domain/qos) by extension 'ignore_ament_install'
[1.103s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/domain/qos) by extensions ['colcon_pkg']
[1.103s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/domain/qos) by extension 'colcon_pkg'
[1.103s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/domain/qos) by extensions ['colcon_meta']
[1.103s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/domain/qos) by extension 'colcon_meta'
[1.103s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/domain/qos) by extensions ['ros']
[1.103s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/domain/qos) by extension 'ros'
[1.103s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/domain/qos) by extensions ['cmake', 'python']
[1.103s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/domain/qos) by extension 'cmake'
[1.103s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/domain/qos) by extension 'python'
[1.103s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/domain/qos) by extensions ['python_setup_py']
[1.103s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/domain/qos) by extension 'python_setup_py'
[1.104s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/domain/qos/detail) by extensions ['ignore', 'ignore_ament_install']
[1.108s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/domain/qos/detail) by extension 'ignore'
[1.108s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/domain/qos/detail) by extension 'ignore_ament_install'
[1.108s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/domain/qos/detail) by extensions ['colcon_pkg']
[1.108s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/domain/qos/detail) by extension 'colcon_pkg'
[1.108s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/domain/qos/detail) by extensions ['colcon_meta']
[1.108s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/domain/qos/detail) by extension 'colcon_meta'
[1.108s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/domain/qos/detail) by extensions ['ros']
[1.108s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/domain/qos/detail) by extension 'ros'
[1.109s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/domain/qos/detail) by extensions ['cmake', 'python']
[1.109s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/domain/qos/detail) by extension 'cmake'
[1.109s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/domain/qos/detail) by extension 'python'
[1.109s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/domain/qos/detail) by extensions ['python_setup_py']
[1.109s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/domain/qos/detail) by extension 'python_setup_py'
[1.111s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/pub) by extensions ['ignore', 'ignore_ament_install']
[1.112s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/pub) by extension 'ignore'
[1.112s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/pub) by extension 'ignore_ament_install'
[1.113s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/pub) by extensions ['colcon_pkg']
[1.113s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/pub) by extension 'colcon_pkg'
[1.113s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/pub) by extensions ['colcon_meta']
[1.113s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/pub) by extension 'colcon_meta'
[1.113s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/pub) by extensions ['ros']
[1.113s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/pub) by extension 'ros'
[1.113s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/pub) by extensions ['cmake', 'python']
[1.113s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/pub) by extension 'cmake'
[1.113s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/pub) by extension 'python'
[1.113s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/pub) by extensions ['python_setup_py']
[1.113s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/pub) by extension 'python_setup_py'
[1.116s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/pub/detail) by extensions ['ignore', 'ignore_ament_install']
[1.117s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/pub/detail) by extension 'ignore'
[1.117s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/pub/detail) by extension 'ignore_ament_install'
[1.117s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/pub/detail) by extensions ['colcon_pkg']
[1.117s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/pub/detail) by extension 'colcon_pkg'
[1.117s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/pub/detail) by extensions ['colcon_meta']
[1.118s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/pub/detail) by extension 'colcon_meta'
[1.118s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/pub/detail) by extensions ['ros']
[1.118s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/pub/detail) by extension 'ros'
[1.118s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/pub/detail) by extensions ['cmake', 'python']
[1.118s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/pub/detail) by extension 'cmake'
[1.118s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/pub/detail) by extension 'python'
[1.118s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/pub/detail) by extensions ['python_setup_py']
[1.118s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/pub/detail) by extension 'python_setup_py'
[1.120s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/pub/qos) by extensions ['ignore', 'ignore_ament_install']
[1.122s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/pub/qos) by extension 'ignore'
[1.122s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/pub/qos) by extension 'ignore_ament_install'
[1.122s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/pub/qos) by extensions ['colcon_pkg']
[1.122s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/pub/qos) by extension 'colcon_pkg'
[1.123s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/pub/qos) by extensions ['colcon_meta']
[1.123s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/pub/qos) by extension 'colcon_meta'
[1.123s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/pub/qos) by extensions ['ros']
[1.123s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/pub/qos) by extension 'ros'
[1.124s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/pub/qos) by extensions ['cmake', 'python']
[1.128s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/pub/qos) by extension 'cmake'
[1.128s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/pub/qos) by extension 'python'
[1.128s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/pub/qos) by extensions ['python_setup_py']
[1.128s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/pub/qos) by extension 'python_setup_py'
[1.130s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/pub/qos/detail) by extensions ['ignore', 'ignore_ament_install']
[1.133s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/pub/qos/detail) by extension 'ignore'
[1.133s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/pub/qos/detail) by extension 'ignore_ament_install'
[1.133s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/pub/qos/detail) by extensions ['colcon_pkg']
[1.134s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/pub/qos/detail) by extension 'colcon_pkg'
[1.134s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/pub/qos/detail) by extensions ['colcon_meta']
[1.134s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/pub/qos/detail) by extension 'colcon_meta'
[1.134s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/pub/qos/detail) by extensions ['ros']
[1.134s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/pub/qos/detail) by extension 'ros'
[1.135s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/pub/qos/detail) by extensions ['cmake', 'python']
[1.137s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/pub/qos/detail) by extension 'cmake'
[1.138s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/pub/qos/detail) by extension 'python'
[1.138s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/pub/qos/detail) by extensions ['python_setup_py']
[1.138s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/pub/qos/detail) by extension 'python_setup_py'
[1.140s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub) by extensions ['ignore', 'ignore_ament_install']
[1.142s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub) by extension 'ignore'
[1.143s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub) by extension 'ignore_ament_install'
[1.143s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub) by extensions ['colcon_pkg']
[1.143s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub) by extension 'colcon_pkg'
[1.143s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub) by extensions ['colcon_meta']
[1.143s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub) by extension 'colcon_meta'
[1.143s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub) by extensions ['ros']
[1.143s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub) by extension 'ros'
[1.143s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub) by extensions ['cmake', 'python']
[1.143s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub) by extension 'cmake'
[1.144s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub) by extension 'python'
[1.144s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub) by extensions ['python_setup_py']
[1.144s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub) by extension 'python_setup_py'
[1.146s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/cond) by extensions ['ignore', 'ignore_ament_install']
[1.147s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/cond) by extension 'ignore'
[1.148s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/cond) by extension 'ignore_ament_install'
[1.148s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/cond) by extensions ['colcon_pkg']
[1.148s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/cond) by extension 'colcon_pkg'
[1.149s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/cond) by extensions ['colcon_meta']
[1.149s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/cond) by extension 'colcon_meta'
[1.149s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/cond) by extensions ['ros']
[1.153s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/cond) by extension 'ros'
[1.154s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/cond) by extensions ['cmake', 'python']
[1.154s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/cond) by extension 'cmake'
[1.154s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/cond) by extension 'python'
[1.154s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/cond) by extensions ['python_setup_py']
[1.154s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/cond) by extension 'python_setup_py'
[1.157s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/cond/detail) by extensions ['ignore', 'ignore_ament_install']
[1.158s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/cond/detail) by extension 'ignore'
[1.158s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/cond/detail) by extension 'ignore_ament_install'
[1.158s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/cond/detail) by extensions ['colcon_pkg']
[1.158s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/cond/detail) by extension 'colcon_pkg'
[1.158s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/cond/detail) by extensions ['colcon_meta']
[1.158s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/cond/detail) by extension 'colcon_meta'
[1.158s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/cond/detail) by extensions ['ros']
[1.159s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/cond/detail) by extension 'ros'
[1.159s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/cond/detail) by extensions ['cmake', 'python']
[1.159s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/cond/detail) by extension 'cmake'
[1.159s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/cond/detail) by extension 'python'
[1.159s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/cond/detail) by extensions ['python_setup_py']
[1.159s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/cond/detail) by extension 'python_setup_py'
[1.161s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/detail) by extensions ['ignore', 'ignore_ament_install']
[1.163s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/detail) by extension 'ignore'
[1.163s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/detail) by extension 'ignore_ament_install'
[1.163s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/detail) by extensions ['colcon_pkg']
[1.163s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/detail) by extension 'colcon_pkg'
[1.163s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/detail) by extensions ['colcon_meta']
[1.163s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/detail) by extension 'colcon_meta'
[1.163s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/detail) by extensions ['ros']
[1.163s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/detail) by extension 'ros'
[1.163s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/detail) by extensions ['cmake', 'python']
[1.163s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/detail) by extension 'cmake'
[1.164s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/detail) by extension 'python'
[1.164s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/detail) by extensions ['python_setup_py']
[1.164s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/detail) by extension 'python_setup_py'
[1.166s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/qos) by extensions ['ignore', 'ignore_ament_install']
[1.167s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/qos) by extension 'ignore'
[1.167s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/qos) by extension 'ignore_ament_install'
[1.167s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/qos) by extensions ['colcon_pkg']
[1.168s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/qos) by extension 'colcon_pkg'
[1.168s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/qos) by extensions ['colcon_meta']
[1.168s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/qos) by extension 'colcon_meta'
[1.168s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/qos) by extensions ['ros']
[1.168s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/qos) by extension 'ros'
[1.168s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/qos) by extensions ['cmake', 'python']
[1.168s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/qos) by extension 'cmake'
[1.168s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/qos) by extension 'python'
[1.168s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/qos) by extensions ['python_setup_py']
[1.169s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/qos) by extension 'python_setup_py'
[1.171s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/qos/detail) by extensions ['ignore', 'ignore_ament_install']
[1.172s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/qos/detail) by extension 'ignore'
[1.172s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/qos/detail) by extension 'ignore_ament_install'
[1.172s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/qos/detail) by extensions ['colcon_pkg']
[1.173s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/qos/detail) by extension 'colcon_pkg'
[1.173s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/qos/detail) by extensions ['colcon_meta']
[1.173s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/qos/detail) by extension 'colcon_meta'
[1.173s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/qos/detail) by extensions ['ros']
[1.173s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/qos/detail) by extension 'ros'
[1.173s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/qos/detail) by extensions ['cmake', 'python']
[1.173s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/qos/detail) by extension 'cmake'
[1.173s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/qos/detail) by extension 'python'
[1.173s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/qos/detail) by extensions ['python_setup_py']
[1.173s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/qos/detail) by extension 'python_setup_py'
[1.176s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/status) by extensions ['ignore', 'ignore_ament_install']
[1.177s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/status) by extension 'ignore'
[1.177s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/status) by extension 'ignore_ament_install'
[1.177s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/status) by extensions ['colcon_pkg']
[1.177s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/status) by extension 'colcon_pkg'
[1.178s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/status) by extensions ['colcon_meta']
[1.178s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/status) by extension 'colcon_meta'
[1.178s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/status) by extensions ['ros']
[1.178s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/status) by extension 'ros'
[1.178s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/status) by extensions ['cmake', 'python']
[1.178s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/status) by extension 'cmake'
[1.178s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/status) by extension 'python'
[1.179s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/status) by extensions ['python_setup_py']
[1.179s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/status) by extension 'python_setup_py'
[1.181s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/status/detail) by extensions ['ignore', 'ignore_ament_install']
[1.182s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/status/detail) by extension 'ignore'
[1.182s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/status/detail) by extension 'ignore_ament_install'
[1.182s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/status/detail) by extensions ['colcon_pkg']
[1.182s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/status/detail) by extension 'colcon_pkg'
[1.182s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/status/detail) by extensions ['colcon_meta']
[1.182s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/status/detail) by extension 'colcon_meta'
[1.182s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/status/detail) by extensions ['ros']
[1.183s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/status/detail) by extension 'ros'
[1.183s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/status/detail) by extensions ['cmake', 'python']
[1.183s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/status/detail) by extension 'cmake'
[1.183s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/status/detail) by extension 'python'
[1.183s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/status/detail) by extensions ['python_setup_py']
[1.183s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/sub/status/detail) by extension 'python_setup_py'
[1.185s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/topic) by extensions ['ignore', 'ignore_ament_install']
[1.187s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/topic) by extension 'ignore'
[1.187s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/topic) by extension 'ignore_ament_install'
[1.187s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/topic) by extensions ['colcon_pkg']
[1.187s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/topic) by extension 'colcon_pkg'
[1.187s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/topic) by extensions ['colcon_meta']
[1.187s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/topic) by extension 'colcon_meta'
[1.187s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/topic) by extensions ['ros']
[1.187s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/topic) by extension 'ros'
[1.188s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/topic) by extensions ['cmake', 'python']
[1.188s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/topic) by extension 'cmake'
[1.188s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/topic) by extension 'python'
[1.188s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/topic) by extensions ['python_setup_py']
[1.188s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/topic) by extension 'python_setup_py'
[1.190s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/topic/detail) by extensions ['ignore', 'ignore_ament_install']
[1.192s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/topic/detail) by extension 'ignore'
[1.192s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/topic/detail) by extension 'ignore_ament_install'
[1.192s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/topic/detail) by extensions ['colcon_pkg']
[1.193s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/topic/detail) by extension 'colcon_pkg'
[1.193s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/topic/detail) by extensions ['colcon_meta']
[1.193s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/topic/detail) by extension 'colcon_meta'
[1.193s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/topic/detail) by extensions ['ros']
[1.193s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/topic/detail) by extension 'ros'
[1.193s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/topic/detail) by extensions ['cmake', 'python']
[1.193s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/topic/detail) by extension 'cmake'
[1.193s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/topic/detail) by extension 'python'
[1.193s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/topic/detail) by extensions ['python_setup_py']
[1.193s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/topic/detail) by extension 'python_setup_py'
[1.195s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/topic/qos) by extensions ['ignore', 'ignore_ament_install']
[1.196s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/topic/qos) by extension 'ignore'
[1.196s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/topic/qos) by extension 'ignore_ament_install'
[1.197s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/topic/qos) by extensions ['colcon_pkg']
[1.197s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/topic/qos) by extension 'colcon_pkg'
[1.197s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/topic/qos) by extensions ['colcon_meta']
[1.197s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/topic/qos) by extension 'colcon_meta'
[1.197s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/topic/qos) by extensions ['ros']
[1.197s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/topic/qos) by extension 'ros'
[1.197s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/topic/qos) by extensions ['cmake', 'python']
[1.198s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/topic/qos) by extension 'cmake'
[1.198s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/topic/qos) by extension 'python'
[1.198s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/topic/qos) by extensions ['python_setup_py']
[1.198s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/topic/qos) by extension 'python_setup_py'
[1.200s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/topic/qos/detail) by extensions ['ignore', 'ignore_ament_install']
[1.202s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/topic/qos/detail) by extension 'ignore'
[1.203s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/topic/qos/detail) by extension 'ignore_ament_install'
[1.203s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/topic/qos/detail) by extensions ['colcon_pkg']
[1.203s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/topic/qos/detail) by extension 'colcon_pkg'
[1.204s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/topic/qos/detail) by extensions ['colcon_meta']
[1.204s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/topic/qos/detail) by extension 'colcon_meta'
[1.205s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/topic/qos/detail) by extensions ['ros']
[1.208s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/topic/qos/detail) by extension 'ros'
[1.209s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/topic/qos/detail) by extensions ['cmake', 'python']
[1.209s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/topic/qos/detail) by extension 'cmake'
[1.209s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/topic/qos/detail) by extension 'python'
[1.209s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/topic/qos/detail) by extensions ['python_setup_py']
[1.210s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/dds/topic/qos/detail) by extension 'python_setup_py'
[1.212s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org) by extensions ['ignore', 'ignore_ament_install']
[1.213s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org) by extension 'ignore'
[1.214s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org) by extension 'ignore_ament_install'
[1.214s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org) by extensions ['colcon_pkg']
[1.214s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org) by extension 'colcon_pkg'
[1.214s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org) by extensions ['colcon_meta']
[1.215s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org) by extension 'colcon_meta'
[1.215s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org) by extensions ['ros']
[1.215s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org) by extension 'ros'
[1.220s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org) by extensions ['cmake', 'python']
[1.220s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org) by extension 'cmake'
[1.220s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org) by extension 'python'
[1.221s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org) by extensions ['python_setup_py']
[1.221s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org) by extension 'python_setup_py'
[1.227s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse) by extensions ['ignore', 'ignore_ament_install']
[1.229s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse) by extension 'ignore'
[1.229s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse) by extension 'ignore_ament_install'
[1.229s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse) by extensions ['colcon_pkg']
[1.229s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse) by extension 'colcon_pkg'
[1.229s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse) by extensions ['colcon_meta']
[1.229s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse) by extension 'colcon_meta'
[1.229s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse) by extensions ['ros']
[1.229s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse) by extension 'ros'
[1.230s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse) by extensions ['cmake', 'python']
[1.230s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse) by extension 'cmake'
[1.230s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse) by extension 'python'
[1.230s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse) by extensions ['python_setup_py']
[1.230s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse) by extension 'python_setup_py'
[1.231s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds) by extensions ['ignore', 'ignore_ament_install']
[1.231s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds) by extension 'ignore'
[1.234s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds) by extension 'ignore_ament_install'
[1.234s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds) by extensions ['colcon_pkg']
[1.234s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds) by extension 'colcon_pkg'
[1.234s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds) by extensions ['colcon_meta']
[1.234s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds) by extension 'colcon_meta'
[1.234s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds) by extensions ['ros']
[1.234s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds) by extension 'ros'
[1.234s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds) by extensions ['cmake', 'python']
[1.234s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds) by extension 'cmake'
[1.234s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds) by extension 'python'
[1.234s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds) by extensions ['python_setup_py']
[1.234s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds) by extension 'python_setup_py'
[1.236s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/core) by extensions ['ignore', 'ignore_ament_install']
[1.236s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/core) by extension 'ignore'
[1.236s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/core) by extension 'ignore_ament_install'
[1.239s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/core) by extensions ['colcon_pkg']
[1.239s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/core) by extension 'colcon_pkg'
[1.239s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/core) by extensions ['colcon_meta']
[1.239s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/core) by extension 'colcon_meta'
[1.240s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/core) by extensions ['ros']
[1.240s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/core) by extension 'ros'
[1.240s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/core) by extensions ['cmake', 'python']
[1.240s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/core) by extension 'cmake'
[1.241s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/core) by extension 'python'
[1.241s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/core) by extensions ['python_setup_py']
[1.241s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/core) by extension 'python_setup_py'
[1.243s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/core/cdr) by extensions ['ignore', 'ignore_ament_install']
[1.243s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/core/cdr) by extension 'ignore'
[1.244s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/core/cdr) by extension 'ignore_ament_install'
[1.244s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/core/cdr) by extensions ['colcon_pkg']
[1.244s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/core/cdr) by extension 'colcon_pkg'
[1.244s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/core/cdr) by extensions ['colcon_meta']
[1.245s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/core/cdr) by extension 'colcon_meta'
[1.245s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/core/cdr) by extensions ['ros']
[1.245s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/core/cdr) by extension 'ros'
[1.246s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/core/cdr) by extensions ['cmake', 'python']
[1.246s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/core/cdr) by extension 'cmake'
[1.249s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/core/cdr) by extension 'python'
[1.249s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/core/cdr) by extensions ['python_setup_py']
[1.250s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/core/cdr) by extension 'python_setup_py'
[1.251s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/core/cond) by extensions ['ignore', 'ignore_ament_install']
[1.254s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/core/cond) by extension 'ignore'
[1.254s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/core/cond) by extension 'ignore_ament_install'
[1.255s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/core/cond) by extensions ['colcon_pkg']
[1.255s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/core/cond) by extension 'colcon_pkg'
[1.256s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/core/cond) by extensions ['colcon_meta']
[1.256s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/core/cond) by extension 'colcon_meta'
[1.260s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/core/cond) by extensions ['ros']
[1.260s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/core/cond) by extension 'ros'
[1.261s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/core/cond) by extensions ['cmake', 'python']
[1.261s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/core/cond) by extension 'cmake'
[1.261s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/core/cond) by extension 'python'
[1.261s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/core/cond) by extensions ['python_setup_py']
[1.262s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/core/cond) by extension 'python_setup_py'
[1.265s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/core/policy) by extensions ['ignore', 'ignore_ament_install']
[1.265s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/core/policy) by extension 'ignore'
[1.266s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/core/policy) by extension 'ignore_ament_install'
[1.266s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/core/policy) by extensions ['colcon_pkg']
[1.266s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/core/policy) by extension 'colcon_pkg'
[1.266s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/core/policy) by extensions ['colcon_meta']
[1.266s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/core/policy) by extension 'colcon_meta'
[1.267s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/core/policy) by extensions ['ros']
[1.270s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/core/policy) by extension 'ros'
[1.270s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/core/policy) by extensions ['cmake', 'python']
[1.270s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/core/policy) by extension 'cmake'
[1.271s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/core/policy) by extension 'python'
[1.271s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/core/policy) by extensions ['python_setup_py']
[1.271s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/core/policy) by extension 'python_setup_py'
[1.273s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/core/status) by extensions ['ignore', 'ignore_ament_install']
[1.274s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/core/status) by extension 'ignore'
[1.275s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/core/status) by extension 'ignore_ament_install'
[1.275s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/core/status) by extensions ['colcon_pkg']
[1.275s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/core/status) by extension 'colcon_pkg'
[1.275s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/core/status) by extensions ['colcon_meta']
[1.275s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/core/status) by extension 'colcon_meta'
[1.276s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/core/status) by extensions ['ros']
[1.276s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/core/status) by extension 'ros'
[1.281s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/core/status) by extensions ['cmake', 'python']
[1.281s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/core/status) by extension 'cmake'
[1.281s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/core/status) by extension 'python'
[1.281s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/core/status) by extensions ['python_setup_py']
[1.281s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/core/status) by extension 'python_setup_py'
[1.283s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/domain) by extensions ['ignore', 'ignore_ament_install']
[1.283s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/domain) by extension 'ignore'
[1.286s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/domain) by extension 'ignore_ament_install'
[1.286s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/domain) by extensions ['colcon_pkg']
[1.286s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/domain) by extension 'colcon_pkg'
[1.286s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/domain) by extensions ['colcon_meta']
[1.286s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/domain) by extension 'colcon_meta'
[1.286s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/domain) by extensions ['ros']
[1.286s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/domain) by extension 'ros'
[1.286s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/domain) by extensions ['cmake', 'python']
[1.286s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/domain) by extension 'cmake'
[1.286s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/domain) by extension 'python'
[1.286s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/domain) by extensions ['python_setup_py']
[1.287s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/domain) by extension 'python_setup_py'
[1.288s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/domain/qos) by extensions ['ignore', 'ignore_ament_install']
[1.288s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/domain/qos) by extension 'ignore'
[1.288s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/domain/qos) by extension 'ignore_ament_install'
[1.291s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/domain/qos) by extensions ['colcon_pkg']
[1.291s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/domain/qos) by extension 'colcon_pkg'
[1.291s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/domain/qos) by extensions ['colcon_meta']
[1.291s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/domain/qos) by extension 'colcon_meta'
[1.292s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/domain/qos) by extensions ['ros']
[1.292s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/domain/qos) by extension 'ros'
[1.292s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/domain/qos) by extensions ['cmake', 'python']
[1.292s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/domain/qos) by extension 'cmake'
[1.293s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/domain/qos) by extension 'python'
[1.293s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/domain/qos) by extensions ['python_setup_py']
[1.296s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/domain/qos) by extension 'python_setup_py'
[1.299s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/pub) by extensions ['ignore', 'ignore_ament_install']
[1.302s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/pub) by extension 'ignore'
[1.302s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/pub) by extension 'ignore_ament_install'
[1.302s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/pub) by extensions ['colcon_pkg']
[1.303s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/pub) by extension 'colcon_pkg'
[1.303s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/pub) by extensions ['colcon_meta']
[1.303s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/pub) by extension 'colcon_meta'
[1.304s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/pub) by extensions ['ros']
[1.307s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/pub) by extension 'ros'
[1.308s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/pub) by extensions ['cmake', 'python']
[1.308s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/pub) by extension 'cmake'
[1.309s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/pub) by extension 'python'
[1.309s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/pub) by extensions ['python_setup_py']
[1.312s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/pub) by extension 'python_setup_py'
[1.314s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/pub/qos) by extensions ['ignore', 'ignore_ament_install']
[1.317s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/pub/qos) by extension 'ignore'
[1.318s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/pub/qos) by extension 'ignore_ament_install'
[1.318s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/pub/qos) by extensions ['colcon_pkg']
[1.318s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/pub/qos) by extension 'colcon_pkg'
[1.318s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/pub/qos) by extensions ['colcon_meta']
[1.319s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/pub/qos) by extension 'colcon_meta'
[1.319s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/pub/qos) by extensions ['ros']
[1.319s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/pub/qos) by extension 'ros'
[1.323s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/pub/qos) by extensions ['cmake', 'python']
[1.323s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/pub/qos) by extension 'cmake'
[1.323s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/pub/qos) by extension 'python'
[1.323s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/pub/qos) by extensions ['python_setup_py']
[1.323s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/pub/qos) by extension 'python_setup_py'
[1.325s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/sub) by extensions ['ignore', 'ignore_ament_install']
[1.325s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/sub) by extension 'ignore'
[1.325s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/sub) by extension 'ignore_ament_install'
[1.325s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/sub) by extensions ['colcon_pkg']
[1.325s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/sub) by extension 'colcon_pkg'
[1.325s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/sub) by extensions ['colcon_meta']
[1.325s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/sub) by extension 'colcon_meta'
[1.325s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/sub) by extensions ['ros']
[1.325s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/sub) by extension 'ros'
[1.326s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/sub) by extensions ['cmake', 'python']
[1.326s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/sub) by extension 'cmake'
[1.326s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/sub) by extension 'python'
[1.326s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/sub) by extensions ['python_setup_py']
[1.326s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/sub) by extension 'python_setup_py'
[1.328s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/sub/cond) by extensions ['ignore', 'ignore_ament_install']
[1.329s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/sub/cond) by extension 'ignore'
[1.329s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/sub/cond) by extension 'ignore_ament_install'
[1.329s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/sub/cond) by extensions ['colcon_pkg']
[1.329s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/sub/cond) by extension 'colcon_pkg'
[1.331s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/sub/cond) by extensions ['colcon_meta']
[1.334s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/sub/cond) by extension 'colcon_meta'
[1.335s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/sub/cond) by extensions ['ros']
[1.335s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/sub/cond) by extension 'ros'
[1.336s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/sub/cond) by extensions ['cmake', 'python']
[1.336s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/sub/cond) by extension 'cmake'
[1.337s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/sub/cond) by extension 'python'
[1.339s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/sub/cond) by extensions ['python_setup_py']
[1.340s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/sub/cond) by extension 'python_setup_py'
[1.341s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/sub/qos) by extensions ['ignore', 'ignore_ament_install']
[1.344s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/sub/qos) by extension 'ignore'
[1.345s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/sub/qos) by extension 'ignore_ament_install'
[1.345s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/sub/qos) by extensions ['colcon_pkg']
[1.345s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/sub/qos) by extension 'colcon_pkg'
[1.346s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/sub/qos) by extensions ['colcon_meta']
[1.346s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/sub/qos) by extension 'colcon_meta'
[1.346s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/sub/qos) by extensions ['ros']
[1.349s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/sub/qos) by extension 'ros'
[1.349s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/sub/qos) by extensions ['cmake', 'python']
[1.350s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/sub/qos) by extension 'cmake'
[1.350s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/sub/qos) by extension 'python'
[1.350s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/sub/qos) by extensions ['python_setup_py']
[1.350s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/sub/qos) by extension 'python_setup_py'
[1.353s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/topic) by extensions ['ignore', 'ignore_ament_install']
[1.354s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/topic) by extension 'ignore'
[1.354s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/topic) by extension 'ignore_ament_install'
[1.354s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/topic) by extensions ['colcon_pkg']
[1.355s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/topic) by extension 'colcon_pkg'
[1.355s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/topic) by extensions ['colcon_meta']
[1.355s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/topic) by extension 'colcon_meta'
[1.355s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/topic) by extensions ['ros']
[1.356s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/topic) by extension 'ros'
[1.356s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/topic) by extensions ['cmake', 'python']
[1.356s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/topic) by extension 'cmake'
[1.360s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/topic) by extension 'python'
[1.361s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/topic) by extensions ['python_setup_py']
[1.361s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/topic) by extension 'python_setup_py'
[1.363s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/topic/qos) by extensions ['ignore', 'ignore_ament_install']
[1.366s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/topic/qos) by extension 'ignore'
[1.367s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/topic/qos) by extension 'ignore_ament_install'
[1.367s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/topic/qos) by extensions ['colcon_pkg']
[1.367s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/topic/qos) by extension 'colcon_pkg'
[1.367s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/topic/qos) by extensions ['colcon_meta']
[1.368s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/topic/qos) by extension 'colcon_meta'
[1.368s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/topic/qos) by extensions ['ros']
[1.368s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/topic/qos) by extension 'ros'
[1.369s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/topic/qos) by extensions ['cmake', 'python']
[1.372s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/topic/qos) by extension 'cmake'
[1.373s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/topic/qos) by extension 'python'
[1.373s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/topic/qos) by extensions ['python_setup_py']
[1.373s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/ddscxx/org/eclipse/cyclonedds/topic/qos) by extension 'python_setup_py'
[1.376s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree) by extensions ['ignore', 'ignore_ament_install']
[1.380s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree) by extension 'ignore'
[1.380s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree) by extension 'ignore_ament_install'
[1.381s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree) by extensions ['colcon_pkg']
[1.381s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree) by extension 'colcon_pkg'
[1.382s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree) by extensions ['colcon_meta']
[1.382s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree) by extension 'colcon_meta'
[1.386s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree) by extensions ['ros']
[1.386s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree) by extension 'ros'
[1.387s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree) by extensions ['cmake', 'python']
[1.387s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree) by extension 'cmake'
[1.387s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree) by extension 'python'
[1.387s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree) by extensions ['python_setup_py']
[1.387s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree) by extension 'python_setup_py'
[1.390s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common) by extensions ['ignore', 'ignore_ament_install']
[1.392s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common) by extension 'ignore'
[1.393s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common) by extension 'ignore_ament_install'
[1.393s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common) by extensions ['colcon_pkg']
[1.393s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common) by extension 'colcon_pkg'
[1.394s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common) by extensions ['colcon_meta']
[1.394s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common) by extension 'colcon_meta'
[1.394s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common) by extensions ['ros']
[1.397s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common) by extension 'ros'
[1.398s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common) by extensions ['cmake', 'python']
[1.398s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common) by extension 'cmake'
[1.398s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common) by extension 'python'
[1.399s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common) by extensions ['python_setup_py']
[1.399s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common) by extension 'python_setup_py'
[1.401s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/dds) by extensions ['ignore', 'ignore_ament_install']
[1.403s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/dds) by extension 'ignore'
[1.403s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/dds) by extension 'ignore_ament_install'
[1.404s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/dds) by extensions ['colcon_pkg']
[1.404s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/dds) by extension 'colcon_pkg'
[1.404s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/dds) by extensions ['colcon_meta']
[1.404s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/dds) by extension 'colcon_meta'
[1.405s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/dds) by extensions ['ros']
[1.408s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/dds) by extension 'ros'
[1.408s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/dds) by extensions ['cmake', 'python']
[1.408s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/dds) by extension 'cmake'
[1.409s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/dds) by extension 'python'
[1.409s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/dds) by extensions ['python_setup_py']
[1.409s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/dds) by extension 'python_setup_py'
[1.412s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/filesystem) by extensions ['ignore', 'ignore_ament_install']
[1.413s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/filesystem) by extension 'ignore'
[1.413s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/filesystem) by extension 'ignore_ament_install'
[1.413s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/filesystem) by extensions ['colcon_pkg']
[1.413s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/filesystem) by extension 'colcon_pkg'
[1.414s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/filesystem) by extensions ['colcon_meta']
[1.414s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/filesystem) by extension 'colcon_meta'
[1.414s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/filesystem) by extensions ['ros']
[1.414s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/filesystem) by extension 'ros'
[1.415s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/filesystem) by extensions ['cmake', 'python']
[1.415s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/filesystem) by extension 'cmake'
[1.418s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/filesystem) by extension 'python'
[1.419s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/filesystem) by extensions ['python_setup_py']
[1.419s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/filesystem) by extension 'python_setup_py'
[1.421s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/json) by extensions ['ignore', 'ignore_ament_install']
[1.424s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/json) by extension 'ignore'
[1.424s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/json) by extension 'ignore_ament_install'
[1.424s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/json) by extensions ['colcon_pkg']
[1.424s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/json) by extension 'colcon_pkg'
[1.424s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/json) by extensions ['colcon_meta']
[1.424s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/json) by extension 'colcon_meta'
[1.424s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/json) by extensions ['ros']
[1.424s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/json) by extension 'ros'
[1.424s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/json) by extensions ['cmake', 'python']
[1.424s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/json) by extension 'cmake'
[1.425s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/json) by extension 'python'
[1.425s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/json) by extensions ['python_setup_py']
[1.425s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/json) by extension 'python_setup_py'
[1.427s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/lock) by extensions ['ignore', 'ignore_ament_install']
[1.429s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/lock) by extension 'ignore'
[1.429s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/lock) by extension 'ignore_ament_install'
[1.429s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/lock) by extensions ['colcon_pkg']
[1.429s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/lock) by extension 'colcon_pkg'
[1.429s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/lock) by extensions ['colcon_meta']
[1.429s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/lock) by extension 'colcon_meta'
[1.429s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/lock) by extensions ['ros']
[1.429s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/lock) by extension 'ros'
[1.429s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/lock) by extensions ['cmake', 'python']
[1.429s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/lock) by extension 'cmake'
[1.429s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/lock) by extension 'python'
[1.430s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/lock) by extensions ['python_setup_py']
[1.430s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/lock) by extension 'python_setup_py'
[1.431s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/log) by extensions ['ignore', 'ignore_ament_install']
[1.431s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/log) by extension 'ignore'
[1.434s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/log) by extension 'ignore_ament_install'
[1.434s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/log) by extensions ['colcon_pkg']
[1.434s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/log) by extension 'colcon_pkg'
[1.435s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/log) by extensions ['colcon_meta']
[1.435s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/log) by extension 'colcon_meta'
[1.435s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/log) by extensions ['ros']
[1.436s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/log) by extension 'ros'
[1.436s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/log) by extensions ['cmake', 'python']
[1.436s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/log) by extension 'cmake'
[1.437s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/log) by extension 'python'
[1.437s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/log) by extensions ['python_setup_py']
[1.437s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/log) by extension 'python_setup_py'
[1.439s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/service) by extensions ['ignore', 'ignore_ament_install']
[1.439s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/service) by extension 'ignore'
[1.439s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/service) by extension 'ignore_ament_install'
[1.439s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/service) by extensions ['colcon_pkg']
[1.440s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/service) by extension 'colcon_pkg'
[1.440s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/service) by extensions ['colcon_meta']
[1.440s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/service) by extension 'colcon_meta'
[1.440s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/service) by extensions ['ros']
[1.441s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/service) by extension 'ros'
[1.441s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/service) by extensions ['cmake', 'python']
[1.441s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/service) by extension 'cmake'
[1.441s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/service) by extension 'python'
[1.441s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/service) by extensions ['python_setup_py']
[1.442s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/service) by extension 'python_setup_py'
[1.444s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/service/base) by extensions ['ignore', 'ignore_ament_install']
[1.446s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/service/base) by extension 'ignore'
[1.446s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/service/base) by extension 'ignore_ament_install'
[1.446s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/service/base) by extensions ['colcon_pkg']
[1.446s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/service/base) by extension 'colcon_pkg'
[1.447s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/service/base) by extensions ['colcon_meta']
[1.447s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/service/base) by extension 'colcon_meta'
[1.447s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/service/base) by extensions ['ros']
[1.447s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/service/base) by extension 'ros'
[1.448s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/service/base) by extensions ['cmake', 'python']
[1.451s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/service/base) by extension 'cmake'
[1.452s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/service/base) by extension 'python'
[1.452s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/service/base) by extensions ['python_setup_py']
[1.452s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/service/base) by extension 'python_setup_py'
[1.454s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/thread) by extensions ['ignore', 'ignore_ament_install']
[1.457s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/thread) by extension 'ignore'
[1.457s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/thread) by extension 'ignore_ament_install'
[1.458s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/thread) by extensions ['colcon_pkg']
[1.458s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/thread) by extension 'colcon_pkg'
[1.458s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/thread) by extensions ['colcon_meta']
[1.458s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/thread) by extension 'colcon_meta'
[1.458s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/thread) by extensions ['ros']
[1.458s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/thread) by extension 'ros'
[1.459s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/thread) by extensions ['cmake', 'python']
[1.459s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/thread) by extension 'cmake'
[1.459s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/thread) by extension 'python'
[1.459s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/thread) by extensions ['python_setup_py']
[1.463s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/thread) by extension 'python_setup_py'
[1.465s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/time) by extensions ['ignore', 'ignore_ament_install']
[1.465s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/time) by extension 'ignore'
[1.469s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/time) by extension 'ignore_ament_install'
[1.469s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/time) by extensions ['colcon_pkg']
[1.469s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/time) by extension 'colcon_pkg'
[1.470s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/time) by extensions ['colcon_meta']
[1.470s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/time) by extension 'colcon_meta'
[1.470s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/time) by extensions ['ros']
[1.470s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/time) by extension 'ros'
[1.471s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/time) by extensions ['cmake', 'python']
[1.471s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/time) by extension 'cmake'
[1.471s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/time) by extension 'python'
[1.471s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/time) by extensions ['python_setup_py']
[1.472s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/common/time) by extension 'python_setup_py'
[1.474s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/idl) by extensions ['ignore', 'ignore_ament_install']
[1.475s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/idl) by extension 'ignore'
[1.475s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/idl) by extension 'ignore_ament_install'
[1.477s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/idl) by extensions ['colcon_pkg']
[1.477s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/idl) by extension 'colcon_pkg'
[1.477s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/idl) by extensions ['colcon_meta']
[1.477s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/idl) by extension 'colcon_meta'
[1.477s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/idl) by extensions ['ros']
[1.477s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/idl) by extension 'ros'
[1.478s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/idl) by extensions ['cmake', 'python']
[1.478s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/idl) by extension 'cmake'
[1.478s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/idl) by extension 'python'
[1.478s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/idl) by extensions ['python_setup_py']
[1.478s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/idl) by extension 'python_setup_py'
[1.480s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/idl/go2) by extensions ['ignore', 'ignore_ament_install']
[1.482s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/idl/go2) by extension 'ignore'
[1.482s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/idl/go2) by extension 'ignore_ament_install'
[1.483s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/idl/go2) by extensions ['colcon_pkg']
[1.483s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/idl/go2) by extension 'colcon_pkg'
[1.483s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/idl/go2) by extensions ['colcon_meta']
[1.483s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/idl/go2) by extension 'colcon_meta'
[1.483s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/idl/go2) by extensions ['ros']
[1.483s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/idl/go2) by extension 'ros'
[1.483s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/idl/go2) by extensions ['cmake', 'python']
[1.483s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/idl/go2) by extension 'cmake'
[1.483s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/idl/go2) by extension 'python'
[1.483s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/idl/go2) by extensions ['python_setup_py']
[1.483s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/idl/go2) by extension 'python_setup_py'
[1.485s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/idl/hg) by extensions ['ignore', 'ignore_ament_install']
[1.488s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/idl/hg) by extension 'ignore'
[1.488s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/idl/hg) by extension 'ignore_ament_install'
[1.488s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/idl/hg) by extensions ['colcon_pkg']
[1.488s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/idl/hg) by extension 'colcon_pkg'
[1.488s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/idl/hg) by extensions ['colcon_meta']
[1.489s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/idl/hg) by extension 'colcon_meta'
[1.489s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/idl/hg) by extensions ['ros']
[1.492s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/idl/hg) by extension 'ros'
[1.493s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/idl/hg) by extensions ['cmake', 'python']
[1.493s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/idl/hg) by extension 'cmake'
[1.493s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/idl/hg) by extension 'python'
[1.493s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/idl/hg) by extensions ['python_setup_py']
[1.493s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/idl/hg) by extension 'python_setup_py'
[1.496s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/idl/hg_doubleimu) by extensions ['ignore', 'ignore_ament_install']
[1.498s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/idl/hg_doubleimu) by extension 'ignore'
[1.498s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/idl/hg_doubleimu) by extension 'ignore_ament_install'
[1.498s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/idl/hg_doubleimu) by extensions ['colcon_pkg']
[1.498s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/idl/hg_doubleimu) by extension 'colcon_pkg'
[1.499s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/idl/hg_doubleimu) by extensions ['colcon_meta']
[1.499s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/idl/hg_doubleimu) by extension 'colcon_meta'
[1.499s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/idl/hg_doubleimu) by extensions ['ros']
[1.499s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/idl/hg_doubleimu) by extension 'ros'
[1.499s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/idl/hg_doubleimu) by extensions ['cmake', 'python']
[1.503s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/idl/hg_doubleimu) by extension 'cmake'
[1.503s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/idl/hg_doubleimu) by extension 'python'
[1.503s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/idl/hg_doubleimu) by extensions ['python_setup_py']
[1.503s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/idl/hg_doubleimu) by extension 'python_setup_py'
[1.506s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/idl/ros2) by extensions ['ignore', 'ignore_ament_install']
[1.509s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/idl/ros2) by extension 'ignore'
[1.509s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/idl/ros2) by extension 'ignore_ament_install'
[1.509s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/idl/ros2) by extensions ['colcon_pkg']
[1.510s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/idl/ros2) by extension 'colcon_pkg'
[1.510s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/idl/ros2) by extensions ['colcon_meta']
[1.510s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/idl/ros2) by extension 'colcon_meta'
[1.510s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/idl/ros2) by extensions ['ros']
[1.510s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/idl/ros2) by extension 'ros'
[1.511s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/idl/ros2) by extensions ['cmake', 'python']
[1.511s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/idl/ros2) by extension 'cmake'
[1.514s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/idl/ros2) by extension 'python'
[1.514s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/idl/ros2) by extensions ['python_setup_py']
[1.515s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/idl/ros2) by extension 'python_setup_py'
[1.516s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot) by extensions ['ignore', 'ignore_ament_install']
[1.520s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot) by extension 'ignore'
[1.521s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot) by extension 'ignore_ament_install'
[1.521s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot) by extensions ['colcon_pkg']
[1.521s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot) by extension 'colcon_pkg'
[1.521s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot) by extensions ['colcon_meta']
[1.522s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot) by extension 'colcon_meta'
[1.522s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot) by extensions ['ros']
[1.522s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot) by extension 'ros'
[1.523s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot) by extensions ['cmake', 'python']
[1.526s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot) by extension 'cmake'
[1.526s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot) by extension 'python'
[1.527s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot) by extensions ['python_setup_py']
[1.527s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot) by extension 'python_setup_py'
[1.529s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2) by extensions ['ignore', 'ignore_ament_install']
[1.532s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2) by extension 'ignore'
[1.532s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2) by extension 'ignore_ament_install'
[1.533s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2) by extensions ['colcon_pkg']
[1.533s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2) by extension 'colcon_pkg'
[1.533s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2) by extensions ['colcon_meta']
[1.533s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2) by extension 'colcon_meta'
[1.533s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2) by extensions ['ros']
[1.537s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2) by extension 'ros'
[1.538s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2) by extensions ['cmake', 'python']
[1.538s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2) by extension 'cmake'
[1.538s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2) by extension 'python'
[1.538s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2) by extensions ['python_setup_py']
[1.538s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2) by extension 'python_setup_py'
[1.541s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/back_video) by extensions ['ignore', 'ignore_ament_install']
[1.542s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/back_video) by extension 'ignore'
[1.542s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/back_video) by extension 'ignore_ament_install'
[1.542s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/back_video) by extensions ['colcon_pkg']
[1.542s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/back_video) by extension 'colcon_pkg'
[1.543s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/back_video) by extensions ['colcon_meta']
[1.543s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/back_video) by extension 'colcon_meta'
[1.543s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/back_video) by extensions ['ros']
[1.543s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/back_video) by extension 'ros'
[1.544s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/back_video) by extensions ['cmake', 'python']
[1.544s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/back_video) by extension 'cmake'
[1.544s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/back_video) by extension 'python'
[1.547s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/back_video) by extensions ['python_setup_py']
[1.548s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/back_video) by extension 'python_setup_py'
[1.550s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/config) by extensions ['ignore', 'ignore_ament_install']
[1.552s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/config) by extension 'ignore'
[1.553s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/config) by extension 'ignore_ament_install'
[1.553s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/config) by extensions ['colcon_pkg']
[1.553s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/config) by extension 'colcon_pkg'
[1.553s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/config) by extensions ['colcon_meta']
[1.554s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/config) by extension 'colcon_meta'
[1.554s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/config) by extensions ['ros']
[1.554s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/config) by extension 'ros'
[1.555s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/config) by extensions ['cmake', 'python']
[1.558s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/config) by extension 'cmake'
[1.558s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/config) by extension 'python'
[1.558s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/config) by extensions ['python_setup_py']
[1.558s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/config) by extension 'python_setup_py'
[1.561s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/front_video) by extensions ['ignore', 'ignore_ament_install']
[1.564s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/front_video) by extension 'ignore'
[1.564s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/front_video) by extension 'ignore_ament_install'
[1.564s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/front_video) by extensions ['colcon_pkg']
[1.564s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/front_video) by extension 'colcon_pkg'
[1.564s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/front_video) by extensions ['colcon_meta']
[1.564s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/front_video) by extension 'colcon_meta'
[1.564s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/front_video) by extensions ['ros']
[1.564s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/front_video) by extension 'ros'
[1.565s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/front_video) by extensions ['cmake', 'python']
[1.565s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/front_video) by extension 'cmake'
[1.565s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/front_video) by extension 'python'
[1.565s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/front_video) by extensions ['python_setup_py']
[1.565s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/front_video) by extension 'python_setup_py'
[1.568s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/motion_switcher) by extensions ['ignore', 'ignore_ament_install']
[1.569s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/motion_switcher) by extension 'ignore'
[1.570s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/motion_switcher) by extension 'ignore_ament_install'
[1.570s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/motion_switcher) by extensions ['colcon_pkg']
[1.570s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/motion_switcher) by extension 'colcon_pkg'
[1.570s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/motion_switcher) by extensions ['colcon_meta']
[1.570s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/motion_switcher) by extension 'colcon_meta'
[1.571s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/motion_switcher) by extensions ['ros']
[1.571s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/motion_switcher) by extension 'ros'
[1.571s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/motion_switcher) by extensions ['cmake', 'python']
[1.575s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/motion_switcher) by extension 'cmake'
[1.575s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/motion_switcher) by extension 'python'
[1.575s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/motion_switcher) by extensions ['python_setup_py']
[1.576s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/motion_switcher) by extension 'python_setup_py'
[1.577s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/robot_state) by extensions ['ignore', 'ignore_ament_install']
[1.578s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/robot_state) by extension 'ignore'
[1.578s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/robot_state) by extension 'ignore_ament_install'
[1.580s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/robot_state) by extensions ['colcon_pkg']
[1.580s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/robot_state) by extension 'colcon_pkg'
[1.580s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/robot_state) by extensions ['colcon_meta']
[1.580s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/robot_state) by extension 'colcon_meta'
[1.580s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/robot_state) by extensions ['ros']
[1.580s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/robot_state) by extension 'ros'
[1.581s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/robot_state) by extensions ['cmake', 'python']
[1.581s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/robot_state) by extension 'cmake'
[1.581s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/robot_state) by extension 'python'
[1.581s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/robot_state) by extensions ['python_setup_py']
[1.581s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/robot_state) by extension 'python_setup_py'
[1.582s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/sport) by extensions ['ignore', 'ignore_ament_install']
[1.586s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/sport) by extension 'ignore'
[1.586s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/sport) by extension 'ignore_ament_install'
[1.587s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/sport) by extensions ['colcon_pkg']
[1.587s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/sport) by extension 'colcon_pkg'
[1.587s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/sport) by extensions ['colcon_meta']
[1.587s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/sport) by extension 'colcon_meta'
[1.587s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/sport) by extensions ['ros']
[1.587s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/sport) by extension 'ros'
[1.587s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/sport) by extensions ['cmake', 'python']
[1.587s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/sport) by extension 'cmake'
[1.588s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/sport) by extension 'python'
[1.588s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/sport) by extensions ['python_setup_py']
[1.588s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/b2/sport) by extension 'python_setup_py'
[1.590s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/channel) by extensions ['ignore', 'ignore_ament_install']
[1.592s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/channel) by extension 'ignore'
[1.592s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/channel) by extension 'ignore_ament_install'
[1.592s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/channel) by extensions ['colcon_pkg']
[1.592s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/channel) by extension 'colcon_pkg'
[1.593s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/channel) by extensions ['colcon_meta']
[1.593s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/channel) by extension 'colcon_meta'
[1.593s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/channel) by extensions ['ros']
[1.593s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/channel) by extension 'ros'
[1.594s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/channel) by extensions ['cmake', 'python']
[1.594s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/channel) by extension 'cmake'
[1.596s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/channel) by extension 'python'
[1.597s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/channel) by extensions ['python_setup_py']
[1.597s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/channel) by extension 'python_setup_py'
[1.598s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/client) by extensions ['ignore', 'ignore_ament_install']
[1.601s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/client) by extension 'ignore'
[1.601s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/client) by extension 'ignore_ament_install'
[1.601s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/client) by extensions ['colcon_pkg']
[1.601s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/client) by extension 'colcon_pkg'
[1.601s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/client) by extensions ['colcon_meta']
[1.601s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/client) by extension 'colcon_meta'
[1.601s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/client) by extensions ['ros']
[1.602s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/client) by extension 'ros'
[1.602s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/client) by extensions ['cmake', 'python']
[1.602s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/client) by extension 'cmake'
[1.602s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/client) by extension 'python'
[1.602s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/client) by extensions ['python_setup_py']
[1.602s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/client) by extension 'python_setup_py'
[1.604s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/future) by extensions ['ignore', 'ignore_ament_install']
[1.605s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/future) by extension 'ignore'
[1.605s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/future) by extension 'ignore_ament_install'
[1.605s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/future) by extensions ['colcon_pkg']
[1.605s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/future) by extension 'colcon_pkg'
[1.605s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/future) by extensions ['colcon_meta']
[1.605s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/future) by extension 'colcon_meta'
[1.606s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/future) by extensions ['ros']
[1.606s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/future) by extension 'ros'
[1.606s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/future) by extensions ['cmake', 'python']
[1.606s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/future) by extension 'cmake'
[1.606s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/future) by extension 'python'
[1.606s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/future) by extensions ['python_setup_py']
[1.606s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/future) by extension 'python_setup_py'
[1.608s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/g1) by extensions ['ignore', 'ignore_ament_install']
[1.611s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/g1) by extension 'ignore'
[1.611s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/g1) by extension 'ignore_ament_install'
[1.611s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/g1) by extensions ['colcon_pkg']
[1.611s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/g1) by extension 'colcon_pkg'
[1.611s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/g1) by extensions ['colcon_meta']
[1.611s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/g1) by extension 'colcon_meta'
[1.611s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/g1) by extensions ['ros']
[1.611s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/g1) by extension 'ros'
[1.611s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/g1) by extensions ['cmake', 'python']
[1.611s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/g1) by extension 'cmake'
[1.611s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/g1) by extension 'python'
[1.611s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/g1) by extensions ['python_setup_py']
[1.611s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/g1) by extension 'python_setup_py'
[1.613s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/g1/arm) by extensions ['ignore', 'ignore_ament_install']
[1.616s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/g1/arm) by extension 'ignore'
[1.616s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/g1/arm) by extension 'ignore_ament_install'
[1.616s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/g1/arm) by extensions ['colcon_pkg']
[1.616s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/g1/arm) by extension 'colcon_pkg'
[1.616s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/g1/arm) by extensions ['colcon_meta']
[1.616s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/g1/arm) by extension 'colcon_meta'
[1.616s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/g1/arm) by extensions ['ros']
[1.616s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/g1/arm) by extension 'ros'
[1.616s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/g1/arm) by extensions ['cmake', 'python']
[1.616s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/g1/arm) by extension 'cmake'
[1.616s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/g1/arm) by extension 'python'
[1.616s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/g1/arm) by extensions ['python_setup_py']
[1.616s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/g1/arm) by extension 'python_setup_py'
[1.617s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/g1/audio) by extensions ['ignore', 'ignore_ament_install']
[1.618s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/g1/audio) by extension 'ignore'
[1.620s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/g1/audio) by extension 'ignore_ament_install'
[1.621s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/g1/audio) by extensions ['colcon_pkg']
[1.621s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/g1/audio) by extension 'colcon_pkg'
[1.621s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/g1/audio) by extensions ['colcon_meta']
[1.621s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/g1/audio) by extension 'colcon_meta'
[1.621s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/g1/audio) by extensions ['ros']
[1.621s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/g1/audio) by extension 'ros'
[1.622s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/g1/audio) by extensions ['cmake', 'python']
[1.622s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/g1/audio) by extension 'cmake'
[1.622s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/g1/audio) by extension 'python'
[1.622s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/g1/audio) by extensions ['python_setup_py']
[1.622s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/g1/audio) by extension 'python_setup_py'
[1.625s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/g1/loco) by extensions ['ignore', 'ignore_ament_install']
[1.626s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/g1/loco) by extension 'ignore'
[1.626s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/g1/loco) by extension 'ignore_ament_install'
[1.626s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/g1/loco) by extensions ['colcon_pkg']
[1.627s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/g1/loco) by extension 'colcon_pkg'
[1.627s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/g1/loco) by extensions ['colcon_meta']
[1.627s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/g1/loco) by extension 'colcon_meta'
[1.627s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/g1/loco) by extensions ['ros']
[1.627s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/g1/loco) by extension 'ros'
[1.628s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/g1/loco) by extensions ['cmake', 'python']
[1.628s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/g1/loco) by extension 'cmake'
[1.628s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/g1/loco) by extension 'python'
[1.628s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/g1/loco) by extensions ['python_setup_py']
[1.628s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/g1/loco) by extension 'python_setup_py'
[1.631s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2) by extensions ['ignore', 'ignore_ament_install']
[1.633s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2) by extension 'ignore'
[1.633s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2) by extension 'ignore_ament_install'
[1.633s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2) by extensions ['colcon_pkg']
[1.633s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2) by extension 'colcon_pkg'
[1.633s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2) by extensions ['colcon_meta']
[1.634s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2) by extension 'colcon_meta'
[1.634s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2) by extensions ['ros']
[1.634s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2) by extension 'ros'
[1.634s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2) by extensions ['cmake', 'python']
[1.638s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2) by extension 'cmake'
[1.638s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2) by extension 'python'
[1.638s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2) by extensions ['python_setup_py']
[1.639s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2) by extension 'python_setup_py'
[1.640s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/calibration) by extensions ['ignore', 'ignore_ament_install']
[1.643s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/calibration) by extension 'ignore'
[1.643s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/calibration) by extension 'ignore_ament_install'
[1.643s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/calibration) by extensions ['colcon_pkg']
[1.644s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/calibration) by extension 'colcon_pkg'
[1.644s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/calibration) by extensions ['colcon_meta']
[1.644s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/calibration) by extension 'colcon_meta'
[1.644s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/calibration) by extensions ['ros']
[1.644s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/calibration) by extension 'ros'
[1.644s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/calibration) by extensions ['cmake', 'python']
[1.644s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/calibration) by extension 'cmake'
[1.644s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/calibration) by extension 'python'
[1.645s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/calibration) by extensions ['python_setup_py']
[1.645s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/calibration) by extension 'python_setup_py'
[1.647s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/config) by extensions ['ignore', 'ignore_ament_install']
[1.650s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/config) by extension 'ignore'
[1.650s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/config) by extension 'ignore_ament_install'
[1.650s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/config) by extensions ['colcon_pkg']
[1.650s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/config) by extension 'colcon_pkg'
[1.650s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/config) by extensions ['colcon_meta']
[1.650s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/config) by extension 'colcon_meta'
[1.651s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/config) by extensions ['ros']
[1.651s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/config) by extension 'ros'
[1.651s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/config) by extensions ['cmake', 'python']
[1.651s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/config) by extension 'cmake'
[1.651s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/config) by extension 'python'
[1.651s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/config) by extensions ['python_setup_py']
[1.652s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/config) by extension 'python_setup_py'
[1.654s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/obstacles_avoid) by extensions ['ignore', 'ignore_ament_install']
[1.656s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/obstacles_avoid) by extension 'ignore'
[1.656s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/obstacles_avoid) by extension 'ignore_ament_install'
[1.656s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/obstacles_avoid) by extensions ['colcon_pkg']
[1.656s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/obstacles_avoid) by extension 'colcon_pkg'
[1.656s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/obstacles_avoid) by extensions ['colcon_meta']
[1.656s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/obstacles_avoid) by extension 'colcon_meta'
[1.657s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/obstacles_avoid) by extensions ['ros']
[1.657s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/obstacles_avoid) by extension 'ros'
[1.657s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/obstacles_avoid) by extensions ['cmake', 'python']
[1.657s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/obstacles_avoid) by extension 'cmake'
[1.657s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/obstacles_avoid) by extension 'python'
[1.657s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/obstacles_avoid) by extensions ['python_setup_py']
[1.657s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/obstacles_avoid) by extension 'python_setup_py'
[1.660s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/public) by extensions ['ignore', 'ignore_ament_install']
[1.662s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/public) by extension 'ignore'
[1.662s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/public) by extension 'ignore_ament_install'
[1.662s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/public) by extensions ['colcon_pkg']
[1.662s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/public) by extension 'colcon_pkg'
[1.662s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/public) by extensions ['colcon_meta']
[1.662s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/public) by extension 'colcon_meta'
[1.662s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/public) by extensions ['ros']
[1.662s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/public) by extension 'ros'
[1.662s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/public) by extensions ['cmake', 'python']
[1.663s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/public) by extension 'cmake'
[1.663s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/public) by extension 'python'
[1.663s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/public) by extensions ['python_setup_py']
[1.663s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/public) by extension 'python_setup_py'
[1.665s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/robot_state) by extensions ['ignore', 'ignore_ament_install']
[1.665s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/robot_state) by extension 'ignore'
[1.665s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/robot_state) by extension 'ignore_ament_install'
[1.666s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/robot_state) by extensions ['colcon_pkg']
[1.666s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/robot_state) by extension 'colcon_pkg'
[1.666s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/robot_state) by extensions ['colcon_meta']
[1.666s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/robot_state) by extension 'colcon_meta'
[1.666s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/robot_state) by extensions ['ros']
[1.666s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/robot_state) by extension 'ros'
[1.666s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/robot_state) by extensions ['cmake', 'python']
[1.666s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/robot_state) by extension 'cmake'
[1.666s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/robot_state) by extension 'python'
[1.666s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/robot_state) by extensions ['python_setup_py']
[1.666s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/robot_state) by extension 'python_setup_py'
[1.668s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/sport) by extensions ['ignore', 'ignore_ament_install']
[1.670s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/sport) by extension 'ignore'
[1.670s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/sport) by extension 'ignore_ament_install'
[1.670s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/sport) by extensions ['colcon_pkg']
[1.670s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/sport) by extension 'colcon_pkg'
[1.670s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/sport) by extensions ['colcon_meta']
[1.670s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/sport) by extension 'colcon_meta'
[1.670s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/sport) by extensions ['ros']
[1.670s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/sport) by extension 'ros'
[1.670s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/sport) by extensions ['cmake', 'python']
[1.670s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/sport) by extension 'cmake'
[1.670s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/sport) by extension 'python'
[1.670s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/sport) by extensions ['python_setup_py']
[1.670s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/sport) by extension 'python_setup_py'
[1.673s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/utrack) by extensions ['ignore', 'ignore_ament_install']
[1.675s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/utrack) by extension 'ignore'
[1.675s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/utrack) by extension 'ignore_ament_install'
[1.676s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/utrack) by extensions ['colcon_pkg']
[1.676s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/utrack) by extension 'colcon_pkg'
[1.676s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/utrack) by extensions ['colcon_meta']
[1.676s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/utrack) by extension 'colcon_meta'
[1.676s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/utrack) by extensions ['ros']
[1.676s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/utrack) by extension 'ros'
[1.677s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/utrack) by extensions ['cmake', 'python']
[1.677s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/utrack) by extension 'cmake'
[1.680s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/utrack) by extension 'python'
[1.680s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/utrack) by extensions ['python_setup_py']
[1.680s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/utrack) by extension 'python_setup_py'
[1.682s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/video) by extensions ['ignore', 'ignore_ament_install']
[1.685s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/video) by extension 'ignore'
[1.685s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/video) by extension 'ignore_ament_install'
[1.686s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/video) by extensions ['colcon_pkg']
[1.686s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/video) by extension 'colcon_pkg'
[1.686s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/video) by extensions ['colcon_meta']
[1.686s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/video) by extension 'colcon_meta'
[1.686s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/video) by extensions ['ros']
[1.687s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/video) by extension 'ros'
[1.687s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/video) by extensions ['cmake', 'python']
[1.690s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/video) by extension 'cmake'
[1.690s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/video) by extension 'python'
[1.690s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/video) by extensions ['python_setup_py']
[1.690s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/video) by extension 'python_setup_py'
[1.692s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/vui) by extensions ['ignore', 'ignore_ament_install']
[1.696s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/vui) by extension 'ignore'
[1.696s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/vui) by extension 'ignore_ament_install'
[1.697s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/vui) by extensions ['colcon_pkg']
[1.697s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/vui) by extension 'colcon_pkg'
[1.697s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/vui) by extensions ['colcon_meta']
[1.697s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/vui) by extension 'colcon_meta'
[1.697s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/vui) by extensions ['ros']
[1.698s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/vui) by extension 'ros'
[1.698s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/vui) by extensions ['cmake', 'python']
[1.698s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/vui) by extension 'cmake'
[1.698s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/vui) by extension 'python'
[1.698s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/vui) by extensions ['python_setup_py']
[1.698s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/go2/vui) by extension 'python_setup_py'
[1.701s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/h1) by extensions ['ignore', 'ignore_ament_install']
[1.703s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/h1) by extension 'ignore'
[1.703s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/h1) by extension 'ignore_ament_install'
[1.703s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/h1) by extensions ['colcon_pkg']
[1.703s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/h1) by extension 'colcon_pkg'
[1.704s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/h1) by extensions ['colcon_meta']
[1.704s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/h1) by extension 'colcon_meta'
[1.704s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/h1) by extensions ['ros']
[1.704s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/h1) by extension 'ros'
[1.704s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/h1) by extensions ['cmake', 'python']
[1.704s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/h1) by extension 'cmake'
[1.705s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/h1) by extension 'python'
[1.705s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/h1) by extensions ['python_setup_py']
[1.705s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/h1) by extension 'python_setup_py'
[1.707s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/h1/loco) by extensions ['ignore', 'ignore_ament_install']
[1.708s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/h1/loco) by extension 'ignore'
[1.708s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/h1/loco) by extension 'ignore_ament_install'
[1.709s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/h1/loco) by extensions ['colcon_pkg']
[1.709s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/h1/loco) by extension 'colcon_pkg'
[1.709s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/h1/loco) by extensions ['colcon_meta']
[1.709s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/h1/loco) by extension 'colcon_meta'
[1.709s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/h1/loco) by extensions ['ros']
[1.709s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/h1/loco) by extension 'ros'
[1.709s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/h1/loco) by extensions ['cmake', 'python']
[1.709s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/h1/loco) by extension 'cmake'
[1.709s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/h1/loco) by extension 'python'
[1.709s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/h1/loco) by extensions ['python_setup_py']
[1.709s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/h1/loco) by extension 'python_setup_py'
[1.711s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/internal) by extensions ['ignore', 'ignore_ament_install']
[1.715s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/internal) by extension 'ignore'
[1.715s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/internal) by extension 'ignore_ament_install'
[1.715s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/internal) by extensions ['colcon_pkg']
[1.715s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/internal) by extension 'colcon_pkg'
[1.715s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/internal) by extensions ['colcon_meta']
[1.715s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/internal) by extension 'colcon_meta'
[1.715s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/internal) by extensions ['ros']
[1.715s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/internal) by extension 'ros'
[1.716s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/internal) by extensions ['cmake', 'python']
[1.716s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/internal) by extension 'cmake'
[1.716s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/internal) by extension 'python'
[1.716s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/internal) by extensions ['python_setup_py']
[1.716s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/internal) by extension 'python_setup_py'
[1.718s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/internal/internal_idl_decl) by extensions ['ignore', 'ignore_ament_install']
[1.720s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/internal/internal_idl_decl) by extension 'ignore'
[1.720s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/internal/internal_idl_decl) by extension 'ignore_ament_install'
[1.720s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/internal/internal_idl_decl) by extensions ['colcon_pkg']
[1.720s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/internal/internal_idl_decl) by extension 'colcon_pkg'
[1.720s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/internal/internal_idl_decl) by extensions ['colcon_meta']
[1.721s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/internal/internal_idl_decl) by extension 'colcon_meta'
[1.721s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/internal/internal_idl_decl) by extensions ['ros']
[1.721s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/internal/internal_idl_decl) by extension 'ros'
[1.721s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/internal/internal_idl_decl) by extensions ['cmake', 'python']
[1.721s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/internal/internal_idl_decl) by extension 'cmake'
[1.721s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/internal/internal_idl_decl) by extension 'python'
[1.721s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/internal/internal_idl_decl) by extensions ['python_setup_py']
[1.721s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/internal/internal_idl_decl) by extension 'python_setup_py'
[1.723s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/serialize) by extensions ['ignore', 'ignore_ament_install']
[1.725s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/serialize) by extension 'ignore'
[1.726s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/serialize) by extension 'ignore_ament_install'
[1.726s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/serialize) by extensions ['colcon_pkg']
[1.726s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/serialize) by extension 'colcon_pkg'
[1.726s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/serialize) by extensions ['colcon_meta']
[1.727s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/serialize) by extension 'colcon_meta'
[1.727s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/serialize) by extensions ['ros']
[1.727s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/serialize) by extension 'ros'
[1.727s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/serialize) by extensions ['cmake', 'python']
[1.728s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/serialize) by extension 'cmake'
[1.728s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/serialize) by extension 'python'
[1.728s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/serialize) by extensions ['python_setup_py']
[1.729s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/serialize) by extension 'python_setup_py'
[1.742s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/server) by extensions ['ignore', 'ignore_ament_install']
[1.757s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/server) by extension 'ignore'
[1.757s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/server) by extension 'ignore_ament_install'
[1.757s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/server) by extensions ['colcon_pkg']
[1.757s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/server) by extension 'colcon_pkg'
[1.757s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/server) by extensions ['colcon_meta']
[1.757s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/server) by extension 'colcon_meta'
[1.757s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/server) by extensions ['ros']
[1.757s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/server) by extension 'ros'
[1.757s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/server) by extensions ['cmake', 'python']
[1.758s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/server) by extension 'cmake'
[1.758s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/server) by extension 'python'
[1.758s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/server) by extensions ['python_setup_py']
[1.758s] Level 1:colcon.colcon_core.package_identification:_identify(include/unitree/unitree/robot/server) by extension 'python_setup_py'
[1.762s] Level 1:colcon.colcon_core.package_identification:_identify(include/xiaoli_com) by extensions ['ignore', 'ignore_ament_install']
[1.769s] Level 1:colcon.colcon_core.package_identification:_identify(include/xiaoli_com) by extension 'ignore'
[1.769s] Level 1:colcon.colcon_core.package_identification:_identify(include/xiaoli_com) by extension 'ignore_ament_install'
[1.769s] Level 1:colcon.colcon_core.package_identification:_identify(include/xiaoli_com) by extensions ['colcon_pkg']
[1.770s] Level 1:colcon.colcon_core.package_identification:_identify(include/xiaoli_com) by extension 'colcon_pkg'
[1.770s] Level 1:colcon.colcon_core.package_identification:_identify(include/xiaoli_com) by extensions ['colcon_meta']
[1.770s] Level 1:colcon.colcon_core.package_identification:_identify(include/xiaoli_com) by extension 'colcon_meta'
[1.770s] Level 1:colcon.colcon_core.package_identification:_identify(include/xiaoli_com) by extensions ['ros']
[1.770s] Level 1:colcon.colcon_core.package_identification:_identify(include/xiaoli_com) by extension 'ros'
[1.770s] Level 1:colcon.colcon_core.package_identification:_identify(include/xiaoli_com) by extensions ['cmake', 'python']
[1.770s] Level 1:colcon.colcon_core.package_identification:_identify(include/xiaoli_com) by extension 'cmake'
[1.770s] Level 1:colcon.colcon_core.package_identification:_identify(include/xiaoli_com) by extension 'python'
[1.770s] Level 1:colcon.colcon_core.package_identification:_identify(include/xiaoli_com) by extensions ['python_setup_py']
[1.770s] Level 1:colcon.colcon_core.package_identification:_identify(include/xiaoli_com) by extension 'python_setup_py'
[1.773s] Level 1:colcon.colcon_core.package_identification:_identify(lib) by extensions ['ignore', 'ignore_ament_install']
[1.774s] Level 1:colcon.colcon_core.package_identification:_identify(lib) by extension 'ignore'
[1.779s] Level 1:colcon.colcon_core.package_identification:_identify(lib) by extension 'ignore_ament_install'
[1.779s] Level 1:colcon.colcon_core.package_identification:_identify(lib) by extensions ['colcon_pkg']
[1.779s] Level 1:colcon.colcon_core.package_identification:_identify(lib) by extension 'colcon_pkg'
[1.780s] Level 1:colcon.colcon_core.package_identification:_identify(lib) by extensions ['colcon_meta']
[1.780s] Level 1:colcon.colcon_core.package_identification:_identify(lib) by extension 'colcon_meta'
[1.780s] Level 1:colcon.colcon_core.package_identification:_identify(lib) by extensions ['ros']
[1.785s] Level 1:colcon.colcon_core.package_identification:_identify(lib) by extension 'ros'
[1.786s] Level 1:colcon.colcon_core.package_identification:_identify(lib) by extensions ['cmake', 'python']
[1.786s] Level 1:colcon.colcon_core.package_identification:_identify(lib) by extension 'cmake'
[1.786s] Level 1:colcon.colcon_core.package_identification:_identify(lib) by extension 'python'
[1.786s] Level 1:colcon.colcon_core.package_identification:_identify(lib) by extensions ['python_setup_py']
[1.786s] Level 1:colcon.colcon_core.package_identification:_identify(lib) by extension 'python_setup_py'
[1.796s] Level 1:colcon.colcon_core.package_identification:_identify(lib/aarch64) by extensions ['ignore', 'ignore_ament_install']
[1.797s] Level 1:colcon.colcon_core.package_identification:_identify(lib/aarch64) by extension 'ignore'
[1.797s] Level 1:colcon.colcon_core.package_identification:_identify(lib/aarch64) by extension 'ignore_ament_install'
[1.803s] Level 1:colcon.colcon_core.package_identification:_identify(lib/aarch64) by extensions ['colcon_pkg']
[1.803s] Level 1:colcon.colcon_core.package_identification:_identify(lib/aarch64) by extension 'colcon_pkg'
[1.803s] Level 1:colcon.colcon_core.package_identification:_identify(lib/aarch64) by extensions ['colcon_meta']
[1.803s] Level 1:colcon.colcon_core.package_identification:_identify(lib/aarch64) by extension 'colcon_meta'
[1.804s] Level 1:colcon.colcon_core.package_identification:_identify(lib/aarch64) by extensions ['ros']
[1.804s] Level 1:colcon.colcon_core.package_identification:_identify(lib/aarch64) by extension 'ros'
[1.804s] Level 1:colcon.colcon_core.package_identification:_identify(lib/aarch64) by extensions ['cmake', 'python']
[1.804s] Level 1:colcon.colcon_core.package_identification:_identify(lib/aarch64) by extension 'cmake'
[1.805s] Level 1:colcon.colcon_core.package_identification:_identify(lib/aarch64) by extension 'python'
[1.830s] Level 1:colcon.colcon_core.package_identification:_identify(lib/aarch64) by extensions ['python_setup_py']
[1.830s] Level 1:colcon.colcon_core.package_identification:_identify(lib/aarch64) by extension 'python_setup_py'
[1.844s] Level 1:colcon.colcon_core.package_identification:_identify(lib/x86_64) by extensions ['ignore', 'ignore_ament_install']
[1.845s] Level 1:colcon.colcon_core.package_identification:_identify(lib/x86_64) by extension 'ignore'
[1.849s] Level 1:colcon.colcon_core.package_identification:_identify(lib/x86_64) by extension 'ignore_ament_install'
[1.849s] Level 1:colcon.colcon_core.package_identification:_identify(lib/x86_64) by extensions ['colcon_pkg']
[1.849s] Level 1:colcon.colcon_core.package_identification:_identify(lib/x86_64) by extension 'colcon_pkg'
[1.849s] Level 1:colcon.colcon_core.package_identification:_identify(lib/x86_64) by extensions ['colcon_meta']
[1.849s] Level 1:colcon.colcon_core.package_identification:_identify(lib/x86_64) by extension 'colcon_meta'
[1.850s] Level 1:colcon.colcon_core.package_identification:_identify(lib/x86_64) by extensions ['ros']
[1.850s] Level 1:colcon.colcon_core.package_identification:_identify(lib/x86_64) by extension 'ros'
[1.850s] Level 1:colcon.colcon_core.package_identification:_identify(lib/x86_64) by extensions ['cmake', 'python']
[1.850s] Level 1:colcon.colcon_core.package_identification:_identify(lib/x86_64) by extension 'cmake'
[1.850s] Level 1:colcon.colcon_core.package_identification:_identify(lib/x86_64) by extension 'python'
[1.850s] Level 1:colcon.colcon_core.package_identification:_identify(lib/x86_64) by extensions ['python_setup_py']
[1.850s] Level 1:colcon.colcon_core.package_identification:_identify(lib/x86_64) by extension 'python_setup_py'
[1.855s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[1.856s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[1.859s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[1.864s] Level 1:colcon.colcon_core.package_identification:_identify(scripts) by extensions ['ignore', 'ignore_ament_install']
[1.864s] Level 1:colcon.colcon_core.package_identification:_identify(scripts) by extension 'ignore'
[1.866s] Level 1:colcon.colcon_core.package_identification:_identify(scripts) by extension 'ignore_ament_install'
[1.870s] Level 1:colcon.colcon_core.package_identification:_identify(scripts) by extensions ['colcon_pkg']
[1.870s] Level 1:colcon.colcon_core.package_identification:_identify(scripts) by extension 'colcon_pkg'
[1.871s] Level 1:colcon.colcon_core.package_identification:_identify(scripts) by extensions ['colcon_meta']
[1.871s] Level 1:colcon.colcon_core.package_identification:_identify(scripts) by extension 'colcon_meta'
[1.872s] Level 1:colcon.colcon_core.package_identification:_identify(scripts) by extensions ['ros']
[1.874s] Level 1:colcon.colcon_core.package_identification:_identify(scripts) by extension 'ros'
[1.881s] Level 1:colcon.colcon_core.package_identification:_identify(scripts) by extensions ['cmake', 'python']
[1.881s] Level 1:colcon.colcon_core.package_identification:_identify(scripts) by extension 'cmake'
[1.881s] Level 1:colcon.colcon_core.package_identification:_identify(scripts) by extension 'python'
[1.883s] Level 1:colcon.colcon_core.package_identification:_identify(scripts) by extensions ['python_setup_py']
[1.886s] Level 1:colcon.colcon_core.package_identification:_identify(scripts) by extension 'python_setup_py'
[1.888s] Level 1:colcon.colcon_core.package_identification:_identify(scripts/alg) by extensions ['ignore', 'ignore_ament_install']
[1.890s] Level 1:colcon.colcon_core.package_identification:_identify(scripts/alg) by extension 'ignore'
[1.892s] Level 1:colcon.colcon_core.package_identification:_identify(scripts/alg) by extension 'ignore_ament_install'
[1.901s] Level 1:colcon.colcon_core.package_identification:_identify(scripts/alg) by extensions ['colcon_pkg']
[1.902s] Level 1:colcon.colcon_core.package_identification:_identify(scripts/alg) by extension 'colcon_pkg'
[1.903s] Level 1:colcon.colcon_core.package_identification:_identify(scripts/alg) by extensions ['colcon_meta']
[1.903s] Level 1:colcon.colcon_core.package_identification:_identify(scripts/alg) by extension 'colcon_meta'
[1.904s] Level 1:colcon.colcon_core.package_identification:_identify(scripts/alg) by extensions ['ros']
[1.904s] Level 1:colcon.colcon_core.package_identification:_identify(scripts/alg) by extension 'ros'
[1.911s] Level 1:colcon.colcon_core.package_identification:_identify(scripts/alg) by extensions ['cmake', 'python']
[1.912s] Level 1:colcon.colcon_core.package_identification:_identify(scripts/alg) by extension 'cmake'
[1.914s] Level 1:colcon.colcon_core.package_identification:_identify(scripts/alg) by extension 'python'
[1.917s] Level 1:colcon.colcon_core.package_identification:_identify(scripts/alg) by extensions ['python_setup_py']
[1.917s] Level 1:colcon.colcon_core.package_identification:_identify(scripts/alg) by extension 'python_setup_py'
[1.920s] Level 1:colcon.colcon_core.package_identification:_identify(scripts/pipeline) by extensions ['ignore', 'ignore_ament_install']
[1.921s] Level 1:colcon.colcon_core.package_identification:_identify(scripts/pipeline) by extension 'ignore'
[1.923s] Level 1:colcon.colcon_core.package_identification:_identify(scripts/pipeline) by extension 'ignore_ament_install'
[1.929s] Level 1:colcon.colcon_core.package_identification:_identify(scripts/pipeline) by extensions ['colcon_pkg']
[1.930s] Level 1:colcon.colcon_core.package_identification:_identify(scripts/pipeline) by extension 'colcon_pkg'
[1.932s] Level 1:colcon.colcon_core.package_identification:_identify(scripts/pipeline) by extensions ['colcon_meta']
[1.932s] Level 1:colcon.colcon_core.package_identification:_identify(scripts/pipeline) by extension 'colcon_meta'
[1.932s] Level 1:colcon.colcon_core.package_identification:_identify(scripts/pipeline) by extensions ['ros']
[1.932s] Level 1:colcon.colcon_core.package_identification:_identify(scripts/pipeline) by extension 'ros'
[1.939s] Level 1:colcon.colcon_core.package_identification:_identify(scripts/pipeline) by extensions ['cmake', 'python']
[1.939s] Level 1:colcon.colcon_core.package_identification:_identify(scripts/pipeline) by extension 'cmake'
[1.941s] Level 1:colcon.colcon_core.package_identification:_identify(scripts/pipeline) by extension 'python'
[1.944s] Level 1:colcon.colcon_core.package_identification:_identify(scripts/pipeline) by extensions ['python_setup_py']
[1.946s] Level 1:colcon.colcon_core.package_identification:_identify(scripts/pipeline) by extension 'python_setup_py'
[1.949s] Level 1:colcon.colcon_core.package_identification:_identify(service) by extensions ['ignore', 'ignore_ament_install']
[1.952s] Level 1:colcon.colcon_core.package_identification:_identify(service) by extension 'ignore'
[1.954s] Level 1:colcon.colcon_core.package_identification:_identify(service) by extension 'ignore_ament_install'
[1.960s] Level 1:colcon.colcon_core.package_identification:_identify(service) by extensions ['colcon_pkg']
[1.962s] Level 1:colcon.colcon_core.package_identification:_identify(service) by extension 'colcon_pkg'
[1.964s] Level 1:colcon.colcon_core.package_identification:_identify(service) by extensions ['colcon_meta']
[1.964s] Level 1:colcon.colcon_core.package_identification:_identify(service) by extension 'colcon_meta'
[1.964s] Level 1:colcon.colcon_core.package_identification:_identify(service) by extensions ['ros']
[1.964s] Level 1:colcon.colcon_core.package_identification:_identify(service) by extension 'ros'
[1.974s] Level 1:colcon.colcon_core.package_identification:_identify(service) by extensions ['cmake', 'python']
[1.975s] Level 1:colcon.colcon_core.package_identification:_identify(service) by extension 'cmake'
[1.976s] Level 1:colcon.colcon_core.package_identification:_identify(service) by extension 'python'
[1.983s] Level 1:colcon.colcon_core.package_identification:_identify(service) by extensions ['python_setup_py']
[1.984s] Level 1:colcon.colcon_core.package_identification:_identify(service) by extension 'python_setup_py'
[1.987s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0) by extensions ['ignore', 'ignore_ament_install']
[1.988s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0) by extension 'ignore'
[1.992s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0) by extension 'ignore_ament_install'
[1.996s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0) by extensions ['colcon_pkg']
[1.996s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0) by extension 'colcon_pkg'
[1.998s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0) by extensions ['colcon_meta']
[2.001s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0) by extension 'colcon_meta'
[2.001s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0) by extensions ['ros']
[2.002s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0) by extension 'ros'
[2.009s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0) by extensions ['cmake', 'python']
[2.009s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0) by extension 'cmake'
[2.012s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0) by extension 'python'
[2.014s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0) by extensions ['python_setup_py']
[2.014s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0) by extension 'python_setup_py'
[2.019s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/andlink) by extensions ['ignore', 'ignore_ament_install']
[2.022s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/andlink) by extension 'ignore'
[2.023s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/andlink) by extension 'ignore_ament_install'
[2.030s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/andlink) by extensions ['colcon_pkg']
[2.032s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/andlink) by extension 'colcon_pkg'
[2.035s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/andlink) by extensions ['colcon_meta']
[2.038s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/andlink) by extension 'colcon_meta'
[2.038s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/andlink) by extensions ['ros']
[2.038s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/andlink) by extension 'ros'
[2.046s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/andlink) by extensions ['cmake', 'python']
[2.046s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/andlink) by extension 'cmake'
[2.047s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/andlink) by extension 'python'
[2.051s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/andlink) by extensions ['python_setup_py']
[2.051s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/andlink) by extension 'python_setup_py'
[2.053s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/andlink/board_resources) by extensions ['ignore', 'ignore_ament_install']
[2.054s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/andlink/board_resources) by extension 'ignore'
[2.056s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/andlink/board_resources) by extension 'ignore_ament_install'
[2.059s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/andlink/board_resources) by extensions ['colcon_pkg']
[2.061s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/andlink/board_resources) by extension 'colcon_pkg'
[2.062s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/andlink/board_resources) by extensions ['colcon_meta']
[2.062s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/andlink/board_resources) by extension 'colcon_meta'
[2.063s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/andlink/board_resources) by extensions ['ros']
[2.066s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/andlink/board_resources) by extension 'ros'
[2.074s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/andlink/board_resources) by extensions ['cmake', 'python']
[2.074s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/andlink/board_resources) by extension 'cmake'
[2.076s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/andlink/board_resources) by extension 'python'
[2.079s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/andlink/board_resources) by extensions ['python_setup_py']
[2.079s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/andlink/board_resources) by extension 'python_setup_py'
[2.081s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/andlink/board_resources/5G) by extensions ['ignore', 'ignore_ament_install']
[2.082s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/andlink/board_resources/5G) by extension 'ignore'
[2.083s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/andlink/board_resources/5G) by extension 'ignore_ament_install'
[2.088s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/andlink/board_resources/5G) by extensions ['colcon_pkg']
[2.088s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/andlink/board_resources/5G) by extension 'colcon_pkg'
[2.089s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/andlink/board_resources/5G) by extensions ['colcon_meta']
[2.090s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/andlink/board_resources/5G) by extension 'colcon_meta'
[2.090s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/andlink/board_resources/5G) by extensions ['ros']
[2.090s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/andlink/board_resources/5G) by extension 'ros'
[2.099s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/andlink/board_resources/5G) by extensions ['cmake', 'python']
[2.099s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/andlink/board_resources/5G) by extension 'cmake'
[2.100s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/andlink/board_resources/5G) by extension 'python'
[2.104s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/andlink/board_resources/5G) by extensions ['python_setup_py']
[2.107s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/andlink/board_resources/5G) by extension 'python_setup_py'
[2.110s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/andlink/board_resources/wifi) by extensions ['ignore', 'ignore_ament_install']
[2.113s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/andlink/board_resources/wifi) by extension 'ignore'
[2.114s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/andlink/board_resources/wifi) by extension 'ignore_ament_install'
[2.122s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/andlink/board_resources/wifi) by extensions ['colcon_pkg']
[2.123s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/andlink/board_resources/wifi) by extension 'colcon_pkg'
[2.125s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/andlink/board_resources/wifi) by extensions ['colcon_meta']
[2.125s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/andlink/board_resources/wifi) by extension 'colcon_meta'
[2.129s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/andlink/board_resources/wifi) by extensions ['ros']
[2.129s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/andlink/board_resources/wifi) by extension 'ros'
[2.136s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/andlink/board_resources/wifi) by extensions ['cmake', 'python']
[2.136s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/andlink/board_resources/wifi) by extension 'cmake'
[2.138s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/andlink/board_resources/wifi) by extension 'python'
[2.142s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/andlink/board_resources/wifi) by extensions ['python_setup_py']
[2.145s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/andlink/board_resources/wifi) by extension 'python_setup_py'
[2.147s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/cat) by extensions ['ignore', 'ignore_ament_install']
[2.150s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/cat) by extension 'ignore'
[2.152s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/cat) by extension 'ignore_ament_install'
[2.159s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/cat) by extensions ['colcon_pkg']
[2.160s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/cat) by extension 'colcon_pkg'
[2.162s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/cat) by extensions ['colcon_meta']
[2.162s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/cat) by extension 'colcon_meta'
[2.164s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/cat) by extensions ['ros']
[2.165s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/cat) by extension 'ros'
[2.173s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/cat) by extensions ['cmake', 'python']
[2.173s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/cat) by extension 'cmake'
[2.176s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/cat) by extension 'python'
[2.179s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/cat) by extensions ['python_setup_py']
[2.181s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/cat) by extension 'python_setup_py'
[2.183s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/nvidia) by extensions ['ignore', 'ignore_ament_install']
[2.187s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/nvidia) by extension 'ignore'
[2.190s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/nvidia) by extension 'ignore_ament_install'
[2.196s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/nvidia) by extensions ['colcon_pkg']
[2.197s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/nvidia) by extension 'colcon_pkg'
[2.198s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/nvidia) by extensions ['colcon_meta']
[2.199s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/nvidia) by extension 'colcon_meta'
[2.199s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/nvidia) by extensions ['ros']
[2.199s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/nvidia) by extension 'ros'
[2.210s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/nvidia) by extensions ['cmake', 'python']
[2.210s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/nvidia) by extension 'cmake'
[2.212s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/nvidia) by extension 'python'
[2.215s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/nvidia) by extensions ['python_setup_py']
[2.215s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.0/nvidia) by extension 'python_setup_py'
[2.217s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1) by extensions ['ignore', 'ignore_ament_install']
[2.219s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1) by extension 'ignore'
[2.221s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1) by extension 'ignore_ament_install'
[2.224s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1) by extensions ['colcon_pkg']
[2.224s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1) by extension 'colcon_pkg'
[2.226s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1) by extensions ['colcon_meta']
[2.227s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1) by extension 'colcon_meta'
[2.227s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1) by extensions ['ros']
[2.227s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1) by extension 'ros'
[2.235s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1) by extensions ['cmake', 'python']
[2.235s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1) by extension 'cmake'
[2.237s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1) by extension 'python'
[2.240s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1) by extensions ['python_setup_py']
[2.240s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1) by extension 'python_setup_py'
[2.243s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/andlink) by extensions ['ignore', 'ignore_ament_install']
[2.244s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/andlink) by extension 'ignore'
[2.249s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/andlink) by extension 'ignore_ament_install'
[2.253s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/andlink) by extensions ['colcon_pkg']
[2.253s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/andlink) by extension 'colcon_pkg'
[2.254s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/andlink) by extensions ['colcon_meta']
[2.255s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/andlink) by extension 'colcon_meta'
[2.255s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/andlink) by extensions ['ros']
[2.255s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/andlink) by extension 'ros'
[2.262s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/andlink) by extensions ['cmake', 'python']
[2.262s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/andlink) by extension 'cmake'
[2.264s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/andlink) by extension 'python'
[2.268s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/andlink) by extensions ['python_setup_py']
[2.268s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/andlink) by extension 'python_setup_py'
[2.273s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/andlink/board_resources) by extensions ['ignore', 'ignore_ament_install']
[2.276s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/andlink/board_resources) by extension 'ignore'
[2.278s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/andlink/board_resources) by extension 'ignore_ament_install'
[2.286s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/andlink/board_resources) by extensions ['colcon_pkg']
[2.287s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/andlink/board_resources) by extension 'colcon_pkg'
[2.289s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/andlink/board_resources) by extensions ['colcon_meta']
[2.289s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/andlink/board_resources) by extension 'colcon_meta'
[2.289s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/andlink/board_resources) by extensions ['ros']
[2.291s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/andlink/board_resources) by extension 'ros'
[2.301s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/andlink/board_resources) by extensions ['cmake', 'python']
[2.301s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/andlink/board_resources) by extension 'cmake'
[2.303s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/andlink/board_resources) by extension 'python'
[2.308s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/andlink/board_resources) by extensions ['python_setup_py']
[2.308s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/andlink/board_resources) by extension 'python_setup_py'
[2.310s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/andlink/board_resources/5G) by extensions ['ignore', 'ignore_ament_install']
[2.312s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/andlink/board_resources/5G) by extension 'ignore'
[2.314s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/andlink/board_resources/5G) by extension 'ignore_ament_install'
[2.318s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/andlink/board_resources/5G) by extensions ['colcon_pkg']
[2.319s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/andlink/board_resources/5G) by extension 'colcon_pkg'
[2.321s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/andlink/board_resources/5G) by extensions ['colcon_meta']
[2.323s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/andlink/board_resources/5G) by extension 'colcon_meta'
[2.323s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/andlink/board_resources/5G) by extensions ['ros']
[2.323s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/andlink/board_resources/5G) by extension 'ros'
[2.331s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/andlink/board_resources/5G) by extensions ['cmake', 'python']
[2.332s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/andlink/board_resources/5G) by extension 'cmake'
[2.333s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/andlink/board_resources/5G) by extension 'python'
[2.338s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/andlink/board_resources/5G) by extensions ['python_setup_py']
[2.342s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/andlink/board_resources/5G) by extension 'python_setup_py'
[2.344s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/andlink/board_resources/wifi) by extensions ['ignore', 'ignore_ament_install']
[2.346s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/andlink/board_resources/wifi) by extension 'ignore'
[2.349s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/andlink/board_resources/wifi) by extension 'ignore_ament_install'
[2.356s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/andlink/board_resources/wifi) by extensions ['colcon_pkg']
[2.357s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/andlink/board_resources/wifi) by extension 'colcon_pkg'
[2.360s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/andlink/board_resources/wifi) by extensions ['colcon_meta']
[2.360s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/andlink/board_resources/wifi) by extension 'colcon_meta'
[2.363s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/andlink/board_resources/wifi) by extensions ['ros']
[2.363s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/andlink/board_resources/wifi) by extension 'ros'
[2.370s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/andlink/board_resources/wifi) by extensions ['cmake', 'python']
[2.370s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/andlink/board_resources/wifi) by extension 'cmake'
[2.372s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/andlink/board_resources/wifi) by extension 'python'
[2.377s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/andlink/board_resources/wifi) by extensions ['python_setup_py']
[2.379s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/andlink/board_resources/wifi) by extension 'python_setup_py'
[2.382s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/cat) by extensions ['ignore', 'ignore_ament_install']
[2.385s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/cat) by extension 'ignore'
[2.388s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/cat) by extension 'ignore_ament_install'
[2.394s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/cat) by extensions ['colcon_pkg']
[2.394s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/cat) by extension 'colcon_pkg'
[2.397s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/cat) by extensions ['colcon_meta']
[2.399s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/cat) by extension 'colcon_meta'
[2.399s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/cat) by extensions ['ros']
[2.400s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/cat) by extension 'ros'
[2.407s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/cat) by extensions ['cmake', 'python']
[2.407s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/cat) by extension 'cmake'
[2.408s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/cat) by extension 'python'
[2.412s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/cat) by extensions ['python_setup_py']
[2.415s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/cat) by extension 'python_setup_py'
[2.417s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/nvidia) by extensions ['ignore', 'ignore_ament_install']
[2.420s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/nvidia) by extension 'ignore'
[2.422s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/nvidia) by extension 'ignore_ament_install'
[2.427s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/nvidia) by extensions ['colcon_pkg']
[2.427s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/nvidia) by extension 'colcon_pkg'
[2.429s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/nvidia) by extensions ['colcon_meta']
[2.431s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/nvidia) by extension 'colcon_meta'
[2.431s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/nvidia) by extensions ['ros']
[2.431s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/nvidia) by extension 'ros'
[2.438s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/nvidia) by extensions ['cmake', 'python']
[2.438s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/nvidia) by extension 'cmake'
[2.440s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/nvidia) by extension 'python'
[2.443s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/nvidia) by extensions ['python_setup_py']
[2.444s] Level 1:colcon.colcon_core.package_identification:_identify(service/1.1/nvidia) by extension 'python_setup_py'
[2.449s] Level 1:colcon.colcon_core.package_identification:_identify(service/2.0) by extensions ['ignore', 'ignore_ament_install']
[2.453s] Level 1:colcon.colcon_core.package_identification:_identify(service/2.0) by extension 'ignore'
[2.455s] Level 1:colcon.colcon_core.package_identification:_identify(service/2.0) by extension 'ignore_ament_install'
[2.463s] Level 1:colcon.colcon_core.package_identification:_identify(service/2.0) by extensions ['colcon_pkg']
[2.464s] Level 1:colcon.colcon_core.package_identification:_identify(service/2.0) by extension 'colcon_pkg'
[2.466s] Level 1:colcon.colcon_core.package_identification:_identify(service/2.0) by extensions ['colcon_meta']
[2.466s] Level 1:colcon.colcon_core.package_identification:_identify(service/2.0) by extension 'colcon_meta'
[2.466s] Level 1:colcon.colcon_core.package_identification:_identify(service/2.0) by extensions ['ros']
[2.466s] Level 1:colcon.colcon_core.package_identification:_identify(service/2.0) by extension 'ros'
[2.476s] Level 1:colcon.colcon_core.package_identification:_identify(service/2.0) by extensions ['cmake', 'python']
[2.476s] Level 1:colcon.colcon_core.package_identification:_identify(service/2.0) by extension 'cmake'
[2.478s] Level 1:colcon.colcon_core.package_identification:_identify(service/2.0) by extension 'python'
[2.481s] Level 1:colcon.colcon_core.package_identification:_identify(service/2.0) by extensions ['python_setup_py']
[2.484s] Level 1:colcon.colcon_core.package_identification:_identify(service/2.0) by extension 'python_setup_py'
[2.486s] Level 1:colcon.colcon_core.package_identification:_identify(service/2.0/app) by extensions ['ignore', 'ignore_ament_install']
[2.489s] Level 1:colcon.colcon_core.package_identification:_identify(service/2.0/app) by extension 'ignore'
[2.490s] Level 1:colcon.colcon_core.package_identification:_identify(service/2.0/app) by extension 'ignore_ament_install'
[2.498s] Level 1:colcon.colcon_core.package_identification:_identify(service/2.0/app) by extensions ['colcon_pkg']
[2.499s] Level 1:colcon.colcon_core.package_identification:_identify(service/2.0/app) by extension 'colcon_pkg'
[2.501s] Level 1:colcon.colcon_core.package_identification:_identify(service/2.0/app) by extensions ['colcon_meta']
[2.505s] Level 1:colcon.colcon_core.package_identification:_identify(service/2.0/app) by extension 'colcon_meta'
[2.505s] Level 1:colcon.colcon_core.package_identification:_identify(service/2.0/app) by extensions ['ros']
[2.505s] Level 1:colcon.colcon_core.package_identification:_identify(service/2.0/app) by extension 'ros'
[2.513s] Level 1:colcon.colcon_core.package_identification:_identify(service/2.0/app) by extensions ['cmake', 'python']
[2.513s] Level 1:colcon.colcon_core.package_identification:_identify(service/2.0/app) by extension 'cmake'
[2.515s] Level 1:colcon.colcon_core.package_identification:_identify(service/2.0/app) by extension 'python'
[2.518s] Level 1:colcon.colcon_core.package_identification:_identify(service/2.0/app) by extensions ['python_setup_py']
[2.518s] Level 1:colcon.colcon_core.package_identification:_identify(service/2.0/app) by extension 'python_setup_py'
[2.521s] Level 1:colcon.colcon_core.package_identification:_identify(service/2.0/unitree) by extensions ['ignore', 'ignore_ament_install']
[2.522s] Level 1:colcon.colcon_core.package_identification:_identify(service/2.0/unitree) by extension 'ignore'
[2.525s] Level 1:colcon.colcon_core.package_identification:_identify(service/2.0/unitree) by extension 'ignore_ament_install'
[2.530s] Level 1:colcon.colcon_core.package_identification:_identify(service/2.0/unitree) by extensions ['colcon_pkg']
[2.531s] Level 1:colcon.colcon_core.package_identification:_identify(service/2.0/unitree) by extension 'colcon_pkg'
[2.533s] Level 1:colcon.colcon_core.package_identification:_identify(service/2.0/unitree) by extensions ['colcon_meta']
[2.533s] Level 1:colcon.colcon_core.package_identification:_identify(service/2.0/unitree) by extension 'colcon_meta'
[2.533s] Level 1:colcon.colcon_core.package_identification:_identify(service/2.0/unitree) by extensions ['ros']
[2.537s] Level 1:colcon.colcon_core.package_identification:_identify(service/2.0/unitree) by extension 'ros'
[2.544s] Level 1:colcon.colcon_core.package_identification:_identify(service/2.0/unitree) by extensions ['cmake', 'python']
[2.545s] Level 1:colcon.colcon_core.package_identification:_identify(service/2.0/unitree) by extension 'cmake'
[2.546s] Level 1:colcon.colcon_core.package_identification:_identify(service/2.0/unitree) by extension 'python'
[2.549s] Level 1:colcon.colcon_core.package_identification:_identify(service/2.0/unitree) by extensions ['python_setup_py']
[2.549s] Level 1:colcon.colcon_core.package_identification:_identify(service/2.0/unitree) by extension 'python_setup_py'
[2.551s] Level 1:colcon.colcon_core.package_identification:_identify(service/2.0/unitree/network) by extensions ['ignore', 'ignore_ament_install']
[2.553s] Level 1:colcon.colcon_core.package_identification:_identify(service/2.0/unitree/network) by extension 'ignore'
[2.555s] Level 1:colcon.colcon_core.package_identification:_identify(service/2.0/unitree/network) by extension 'ignore_ament_install'
[2.560s] Level 1:colcon.colcon_core.package_identification:_identify(service/2.0/unitree/network) by extensions ['colcon_pkg']
[2.560s] Level 1:colcon.colcon_core.package_identification:_identify(service/2.0/unitree/network) by extension 'colcon_pkg'
[2.562s] Level 1:colcon.colcon_core.package_identification:_identify(service/2.0/unitree/network) by extensions ['colcon_meta']
[2.564s] Level 1:colcon.colcon_core.package_identification:_identify(service/2.0/unitree/network) by extension 'colcon_meta'
[2.565s] Level 1:colcon.colcon_core.package_identification:_identify(service/2.0/unitree/network) by extensions ['ros']
[2.565s] Level 1:colcon.colcon_core.package_identification:_identify(service/2.0/unitree/network) by extension 'ros'
[2.573s] Level 1:colcon.colcon_core.package_identification:_identify(service/2.0/unitree/network) by extensions ['cmake', 'python']
[2.573s] Level 1:colcon.colcon_core.package_identification:_identify(service/2.0/unitree/network) by extension 'cmake'
[2.576s] Level 1:colcon.colcon_core.package_identification:_identify(service/2.0/unitree/network) by extension 'python'
[2.579s] Level 1:colcon.colcon_core.package_identification:_identify(service/2.0/unitree/network) by extensions ['python_setup_py']
[2.582s] Level 1:colcon.colcon_core.package_identification:_identify(service/2.0/unitree/network) by extension 'python_setup_py'
[2.584s] Level 1:colcon.colcon_core.package_identification:_identify(service/2.0/unitree/network/5G) by extensions ['ignore', 'ignore_ament_install']
[2.587s] Level 1:colcon.colcon_core.package_identification:_identify(service/2.0/unitree/network/5G) by extension 'ignore'
[2.589s] Level 1:colcon.colcon_core.package_identification:_identify(service/2.0/unitree/network/5G) by extension 'ignore_ament_install'
[2.596s] Level 1:colcon.colcon_core.package_identification:_identify(service/2.0/unitree/network/5G) by extensions ['colcon_pkg']
[2.596s] Level 1:colcon.colcon_core.package_identification:_identify(service/2.0/unitree/network/5G) by extension 'colcon_pkg'
[2.598s] Level 1:colcon.colcon_core.package_identification:_identify(service/2.0/unitree/network/5G) by extensions ['colcon_meta']
[2.599s] Level 1:colcon.colcon_core.package_identification:_identify(service/2.0/unitree/network/5G) by extension 'colcon_meta'
[2.599s] Level 1:colcon.colcon_core.package_identification:_identify(service/2.0/unitree/network/5G) by extensions ['ros']
[2.599s] Level 1:colcon.colcon_core.package_identification:_identify(service/2.0/unitree/network/5G) by extension 'ros'
[2.606s] Level 1:colcon.colcon_core.package_identification:_identify(service/2.0/unitree/network/5G) by extensions ['cmake', 'python']
[2.606s] Level 1:colcon.colcon_core.package_identification:_identify(service/2.0/unitree/network/5G) by extension 'cmake'
[2.608s] Level 1:colcon.colcon_core.package_identification:_identify(service/2.0/unitree/network/5G) by extension 'python'
[2.612s] Level 1:colcon.colcon_core.package_identification:_identify(service/2.0/unitree/network/5G) by extensions ['python_setup_py']
[2.616s] Level 1:colcon.colcon_core.package_identification:_identify(service/2.0/unitree/network/5G) by extension 'python_setup_py'
[2.619s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[2.622s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[2.624s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[2.627s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[2.628s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[2.629s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[2.629s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[2.630s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[2.632s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[2.639s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[2.639s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[2.640s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[2.642s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[2.643s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[2.646s] Level 1:colcon.colcon_core.package_identification:_identify(src/a2dp) by extensions ['ignore', 'ignore_ament_install']
[2.646s] Level 1:colcon.colcon_core.package_identification:_identify(src/a2dp) by extension 'ignore'
[2.651s] Level 1:colcon.colcon_core.package_identification:_identify(src/a2dp) by extension 'ignore_ament_install'
[2.657s] Level 1:colcon.colcon_core.package_identification:_identify(src/a2dp) by extensions ['colcon_pkg']
[2.658s] Level 1:colcon.colcon_core.package_identification:_identify(src/a2dp) by extension 'colcon_pkg'
[2.661s] Level 1:colcon.colcon_core.package_identification:_identify(src/a2dp) by extensions ['colcon_meta']
[2.663s] Level 1:colcon.colcon_core.package_identification:_identify(src/a2dp) by extension 'colcon_meta'
[2.664s] Level 1:colcon.colcon_core.package_identification:_identify(src/a2dp) by extensions ['ros']
[2.664s] Level 1:colcon.colcon_core.package_identification:_identify(src/a2dp) by extension 'ros'
[2.700s] DEBUG:colcon.colcon_core.package_identification:Package 'src/a2dp' with type 'ros.ament_python' and name 'a2dp'
[2.703s] Level 1:colcon.colcon_core.package_identification:_identify(src/andlink) by extensions ['ignore', 'ignore_ament_install']
[2.704s] Level 1:colcon.colcon_core.package_identification:_identify(src/andlink) by extension 'ignore'
[2.706s] Level 1:colcon.colcon_core.package_identification:_identify(src/andlink) by extension 'ignore_ament_install'
[2.706s] Level 1:colcon.colcon_core.package_identification:_identify(src/andlink) by extensions ['colcon_pkg']
[2.707s] Level 1:colcon.colcon_core.package_identification:_identify(src/andlink) by extension 'colcon_pkg'
[2.707s] Level 1:colcon.colcon_core.package_identification:_identify(src/andlink) by extensions ['colcon_meta']
[2.710s] Level 1:colcon.colcon_core.package_identification:_identify(src/andlink) by extension 'colcon_meta'
[2.710s] Level 1:colcon.colcon_core.package_identification:_identify(src/andlink) by extensions ['ros']
[2.711s] Level 1:colcon.colcon_core.package_identification:_identify(src/andlink) by extension 'ros'
[2.718s] DEBUG:colcon.colcon_core.package_identification:Package 'src/andlink' with type 'ros.ament_cmake' and name 'andlink'
[2.721s] Level 1:colcon.colcon_core.package_identification:_identify(src/audio_player) by extensions ['ignore', 'ignore_ament_install']
[2.722s] Level 1:colcon.colcon_core.package_identification:_identify(src/audio_player) by extension 'ignore'
[2.724s] Level 1:colcon.colcon_core.package_identification:_identify(src/audio_player) by extension 'ignore_ament_install'
[2.737s] Level 1:colcon.colcon_core.package_identification:_identify(src/audio_player) by extensions ['colcon_pkg']
[2.738s] Level 1:colcon.colcon_core.package_identification:_identify(src/audio_player) by extension 'colcon_pkg'
[2.747s] Level 1:colcon.colcon_core.package_identification:_identify(src/audio_player) by extensions ['colcon_meta']
[2.756s] Level 1:colcon.colcon_core.package_identification:_identify(src/audio_player) by extension 'colcon_meta'
[2.756s] Level 1:colcon.colcon_core.package_identification:_identify(src/audio_player) by extensions ['ros']
[2.757s] Level 1:colcon.colcon_core.package_identification:_identify(src/audio_player) by extension 'ros'
[2.773s] DEBUG:colcon.colcon_core.package_identification:Package 'src/audio_player' with type 'ros.ament_cmake' and name 'audio_player'
[2.776s] Level 1:colcon.colcon_core.package_identification:_identify(src/audio_recorder) by extensions ['ignore', 'ignore_ament_install']
[2.778s] Level 1:colcon.colcon_core.package_identification:_identify(src/audio_recorder) by extension 'ignore'
[2.780s] Level 1:colcon.colcon_core.package_identification:_identify(src/audio_recorder) by extension 'ignore_ament_install'
[2.784s] Level 1:colcon.colcon_core.package_identification:_identify(src/audio_recorder) by extensions ['colcon_pkg']
[2.784s] Level 1:colcon.colcon_core.package_identification:_identify(src/audio_recorder) by extension 'colcon_pkg'
[2.786s] Level 1:colcon.colcon_core.package_identification:_identify(src/audio_recorder) by extensions ['colcon_meta']
[2.788s] Level 1:colcon.colcon_core.package_identification:_identify(src/audio_recorder) by extension 'colcon_meta'
[2.788s] Level 1:colcon.colcon_core.package_identification:_identify(src/audio_recorder) by extensions ['ros']
[2.788s] Level 1:colcon.colcon_core.package_identification:_identify(src/audio_recorder) by extension 'ros'
[2.798s] DEBUG:colcon.colcon_core.package_identification:Package 'src/audio_recorder' with type 'ros.ament_cmake' and name 'audio_recorder'
[2.811s] Level 1:colcon.colcon_core.package_identification:_identify(src/ble) by extensions ['ignore', 'ignore_ament_install']
[2.824s] Level 1:colcon.colcon_core.package_identification:_identify(src/ble) by extension 'ignore'
[2.830s] Level 1:colcon.colcon_core.package_identification:_identify(src/ble) by extension 'ignore_ament_install'
[2.837s] Level 1:colcon.colcon_core.package_identification:_identify(src/ble) by extensions ['colcon_pkg']
[2.839s] Level 1:colcon.colcon_core.package_identification:_identify(src/ble) by extension 'colcon_pkg'
[2.841s] Level 1:colcon.colcon_core.package_identification:_identify(src/ble) by extensions ['colcon_meta']
[2.842s] Level 1:colcon.colcon_core.package_identification:_identify(src/ble) by extension 'colcon_meta'
[2.842s] Level 1:colcon.colcon_core.package_identification:_identify(src/ble) by extensions ['ros']
[2.842s] Level 1:colcon.colcon_core.package_identification:_identify(src/ble) by extension 'ros'
[2.880s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ble' with type 'ros.ament_python' and name 'ble'
[2.883s] Level 1:colcon.colcon_core.package_identification:_identify(src/cmcc_rtc) by extensions ['ignore', 'ignore_ament_install']
[2.884s] Level 1:colcon.colcon_core.package_identification:_identify(src/cmcc_rtc) by extension 'ignore'
[2.886s] Level 1:colcon.colcon_core.package_identification:_identify(src/cmcc_rtc) by extension 'ignore_ament_install'
[2.895s] Level 1:colcon.colcon_core.package_identification:_identify(src/cmcc_rtc) by extensions ['colcon_pkg']
[2.896s] Level 1:colcon.colcon_core.package_identification:_identify(src/cmcc_rtc) by extension 'colcon_pkg'
[2.898s] Level 1:colcon.colcon_core.package_identification:_identify(src/cmcc_rtc) by extensions ['colcon_meta']
[2.898s] Level 1:colcon.colcon_core.package_identification:_identify(src/cmcc_rtc) by extension 'colcon_meta'
[2.898s] Level 1:colcon.colcon_core.package_identification:_identify(src/cmcc_rtc) by extensions ['ros']
[2.898s] Level 1:colcon.colcon_core.package_identification:_identify(src/cmcc_rtc) by extension 'ros'
[2.911s] DEBUG:colcon.colcon_core.package_identification:Package 'src/cmcc_rtc' with type 'ros.ament_cmake' and name 'cmcc_rtc'
[2.914s] Level 1:colcon.colcon_core.package_identification:_identify(src/expression) by extensions ['ignore', 'ignore_ament_install']
[2.915s] Level 1:colcon.colcon_core.package_identification:_identify(src/expression) by extension 'ignore'
[2.917s] Level 1:colcon.colcon_core.package_identification:_identify(src/expression) by extension 'ignore_ament_install'
[2.923s] Level 1:colcon.colcon_core.package_identification:_identify(src/expression) by extensions ['colcon_pkg']
[2.925s] Level 1:colcon.colcon_core.package_identification:_identify(src/expression) by extension 'colcon_pkg'
[2.927s] Level 1:colcon.colcon_core.package_identification:_identify(src/expression) by extensions ['colcon_meta']
[2.927s] Level 1:colcon.colcon_core.package_identification:_identify(src/expression) by extension 'colcon_meta'
[2.930s] Level 1:colcon.colcon_core.package_identification:_identify(src/expression) by extensions ['ros']
[2.930s] Level 1:colcon.colcon_core.package_identification:_identify(src/expression) by extension 'ros'
[2.942s] DEBUG:colcon.colcon_core.package_identification:Package 'src/expression' with type 'ros.ament_cmake' and name 'expression'
[2.945s] Level 1:colcon.colcon_core.package_identification:_identify(src/follow_me) by extensions ['ignore', 'ignore_ament_install']
[2.946s] Level 1:colcon.colcon_core.package_identification:_identify(src/follow_me) by extension 'ignore'
[2.948s] Level 1:colcon.colcon_core.package_identification:_identify(src/follow_me) by extension 'ignore_ament_install'
[2.956s] Level 1:colcon.colcon_core.package_identification:_identify(src/follow_me) by extensions ['colcon_pkg']
[2.957s] Level 1:colcon.colcon_core.package_identification:_identify(src/follow_me) by extension 'colcon_pkg'
[2.960s] Level 1:colcon.colcon_core.package_identification:_identify(src/follow_me) by extensions ['colcon_meta']
[2.960s] Level 1:colcon.colcon_core.package_identification:_identify(src/follow_me) by extension 'colcon_meta'
[2.960s] Level 1:colcon.colcon_core.package_identification:_identify(src/follow_me) by extensions ['ros']
[2.960s] Level 1:colcon.colcon_core.package_identification:_identify(src/follow_me) by extension 'ros'
[2.967s] Level 1:colcon.colcon_core.package_identification:_identify(src/follow_me) by extensions ['cmake', 'python']
[2.967s] Level 1:colcon.colcon_core.package_identification:_identify(src/follow_me) by extension 'cmake'
[2.970s] Level 1:colcon.colcon_core.package_identification:_identify(src/follow_me) by extension 'python'
[2.973s] Level 1:colcon.colcon_core.package_identification:_identify(src/follow_me) by extensions ['python_setup_py']
[2.973s] Level 1:colcon.colcon_core.package_identification:_identify(src/follow_me) by extension 'python_setup_py'
[2.979s] Level 1:colcon.colcon_core.package_identification:_identify(src/follow_me/src) by extensions ['ignore', 'ignore_ament_install']
[2.981s] Level 1:colcon.colcon_core.package_identification:_identify(src/follow_me/src) by extension 'ignore'
[2.983s] Level 1:colcon.colcon_core.package_identification:_identify(src/follow_me/src) by extension 'ignore_ament_install'
[2.990s] Level 1:colcon.colcon_core.package_identification:_identify(src/follow_me/src) by extensions ['colcon_pkg']
[2.991s] Level 1:colcon.colcon_core.package_identification:_identify(src/follow_me/src) by extension 'colcon_pkg'
[2.994s] Level 1:colcon.colcon_core.package_identification:_identify(src/follow_me/src) by extensions ['colcon_meta']
[2.994s] Level 1:colcon.colcon_core.package_identification:_identify(src/follow_me/src) by extension 'colcon_meta'
[2.994s] Level 1:colcon.colcon_core.package_identification:_identify(src/follow_me/src) by extensions ['ros']
[2.998s] Level 1:colcon.colcon_core.package_identification:_identify(src/follow_me/src) by extension 'ros'
[3.006s] Level 1:colcon.colcon_core.package_identification:_identify(src/follow_me/src) by extensions ['cmake', 'python']
[3.006s] Level 1:colcon.colcon_core.package_identification:_identify(src/follow_me/src) by extension 'cmake'
[3.011s] Level 1:colcon.colcon_core.package_identification:_identify(src/follow_me/src) by extension 'python'
[3.016s] Level 1:colcon.colcon_core.package_identification:_identify(src/follow_me/src) by extensions ['python_setup_py']
[3.016s] Level 1:colcon.colcon_core.package_identification:_identify(src/follow_me/src) by extension 'python_setup_py'
[3.018s] Level 1:colcon.colcon_core.package_identification:_identify(src/follow_me/src/follow_me_bringup) by extensions ['ignore', 'ignore_ament_install']
[3.020s] Level 1:colcon.colcon_core.package_identification:_identify(src/follow_me/src/follow_me_bringup) by extension 'ignore'
[3.021s] Level 1:colcon.colcon_core.package_identification:_identify(src/follow_me/src/follow_me_bringup) by extension 'ignore_ament_install'
[3.025s] Level 1:colcon.colcon_core.package_identification:_identify(src/follow_me/src/follow_me_bringup) by extensions ['colcon_pkg']
[3.025s] Level 1:colcon.colcon_core.package_identification:_identify(src/follow_me/src/follow_me_bringup) by extension 'colcon_pkg'
[3.027s] Level 1:colcon.colcon_core.package_identification:_identify(src/follow_me/src/follow_me_bringup) by extensions ['colcon_meta']
[3.029s] Level 1:colcon.colcon_core.package_identification:_identify(src/follow_me/src/follow_me_bringup) by extension 'colcon_meta'
[3.030s] Level 1:colcon.colcon_core.package_identification:_identify(src/follow_me/src/follow_me_bringup) by extensions ['ros']
[3.030s] Level 1:colcon.colcon_core.package_identification:_identify(src/follow_me/src/follow_me_bringup) by extension 'ros'
[3.043s] DEBUG:colcon.colcon_core.package_identification:Package 'src/follow_me/src/follow_me_bringup' with type 'ros.ament_cmake' and name 'follow_me_bringup'
[3.045s] Level 1:colcon.colcon_core.package_identification:_identify(src/follow_me/src/follow_msgs) by extensions ['ignore', 'ignore_ament_install']
[3.046s] Level 1:colcon.colcon_core.package_identification:_identify(src/follow_me/src/follow_msgs) by extension 'ignore'
[3.048s] Level 1:colcon.colcon_core.package_identification:_identify(src/follow_me/src/follow_msgs) by extension 'ignore_ament_install'
[3.054s] Level 1:colcon.colcon_core.package_identification:_identify(src/follow_me/src/follow_msgs) by extensions ['colcon_pkg']
[3.055s] Level 1:colcon.colcon_core.package_identification:_identify(src/follow_me/src/follow_msgs) by extension 'colcon_pkg'
[3.057s] Level 1:colcon.colcon_core.package_identification:_identify(src/follow_me/src/follow_msgs) by extensions ['colcon_meta']
[3.057s] Level 1:colcon.colcon_core.package_identification:_identify(src/follow_me/src/follow_msgs) by extension 'colcon_meta'
[3.057s] Level 1:colcon.colcon_core.package_identification:_identify(src/follow_me/src/follow_msgs) by extensions ['ros']
[3.057s] Level 1:colcon.colcon_core.package_identification:_identify(src/follow_me/src/follow_msgs) by extension 'ros'
[3.068s] DEBUG:colcon.colcon_core.package_identification:Package 'src/follow_me/src/follow_msgs' with type 'ros.ament_cmake' and name 'follow_msgs'
[3.071s] Level 1:colcon.colcon_core.package_identification:_identify(src/follow_me/src/follow_rcs) by extensions ['ignore', 'ignore_ament_install']
[3.073s] Level 1:colcon.colcon_core.package_identification:_identify(src/follow_me/src/follow_rcs) by extension 'ignore'
[3.074s] Level 1:colcon.colcon_core.package_identification:_identify(src/follow_me/src/follow_rcs) by extension 'ignore_ament_install'
[3.079s] Level 1:colcon.colcon_core.package_identification:_identify(src/follow_me/src/follow_rcs) by extensions ['colcon_pkg']
[3.079s] Level 1:colcon.colcon_core.package_identification:_identify(src/follow_me/src/follow_rcs) by extension 'colcon_pkg'
[3.081s] Level 1:colcon.colcon_core.package_identification:_identify(src/follow_me/src/follow_rcs) by extensions ['colcon_meta']
[3.082s] Level 1:colcon.colcon_core.package_identification:_identify(src/follow_me/src/follow_rcs) by extension 'colcon_meta'
[3.083s] Level 1:colcon.colcon_core.package_identification:_identify(src/follow_me/src/follow_rcs) by extensions ['ros']
[3.083s] Level 1:colcon.colcon_core.package_identification:_identify(src/follow_me/src/follow_rcs) by extension 'ros'
[3.096s] DEBUG:colcon.colcon_core.package_identification:Package 'src/follow_me/src/follow_rcs' with type 'ros.ament_cmake' and name 'follow_rcs'
[3.098s] Level 1:colcon.colcon_core.package_identification:_identify(src/follow_me/src/follow_strategy) by extensions ['ignore', 'ignore_ament_install']
[3.099s] Level 1:colcon.colcon_core.package_identification:_identify(src/follow_me/src/follow_strategy) by extension 'ignore'
[3.101s] Level 1:colcon.colcon_core.package_identification:_identify(src/follow_me/src/follow_strategy) by extension 'ignore_ament_install'
[3.107s] Level 1:colcon.colcon_core.package_identification:_identify(src/follow_me/src/follow_strategy) by extensions ['colcon_pkg']
[3.109s] Level 1:colcon.colcon_core.package_identification:_identify(src/follow_me/src/follow_strategy) by extension 'colcon_pkg'
[3.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/follow_me/src/follow_strategy) by extensions ['colcon_meta']
[3.111s] Level 1:colcon.colcon_core.package_identification:_identify(src/follow_me/src/follow_strategy) by extension 'colcon_meta'
[3.111s] Level 1:colcon.colcon_core.package_identification:_identify(src/follow_me/src/follow_strategy) by extensions ['ros']
[3.111s] Level 1:colcon.colcon_core.package_identification:_identify(src/follow_me/src/follow_strategy) by extension 'ros'
[3.121s] DEBUG:colcon.colcon_core.package_identification:Package 'src/follow_me/src/follow_strategy' with type 'ros.ament_python' and name 'follow_strategy'
[3.124s] Level 1:colcon.colcon_core.package_identification:_identify(src/follow_me/src/pixel2world_pose) by extensions ['ignore', 'ignore_ament_install']
[3.125s] Level 1:colcon.colcon_core.package_identification:_identify(src/follow_me/src/pixel2world_pose) by extension 'ignore'
[3.127s] Level 1:colcon.colcon_core.package_identification:_identify(src/follow_me/src/pixel2world_pose) by extension 'ignore_ament_install'
[3.133s] Level 1:colcon.colcon_core.package_identification:_identify(src/follow_me/src/pixel2world_pose) by extensions ['colcon_pkg']
[3.134s] Level 1:colcon.colcon_core.package_identification:_identify(src/follow_me/src/pixel2world_pose) by extension 'colcon_pkg'
[3.136s] Level 1:colcon.colcon_core.package_identification:_identify(src/follow_me/src/pixel2world_pose) by extensions ['colcon_meta']
[3.136s] Level 1:colcon.colcon_core.package_identification:_identify(src/follow_me/src/pixel2world_pose) by extension 'colcon_meta'
[3.137s] Level 1:colcon.colcon_core.package_identification:_identify(src/follow_me/src/pixel2world_pose) by extensions ['ros']
[3.139s] Level 1:colcon.colcon_core.package_identification:_identify(src/follow_me/src/pixel2world_pose) by extension 'ros'
[3.153s] DEBUG:colcon.colcon_core.package_identification:Package 'src/follow_me/src/pixel2world_pose' with type 'ros.ament_python' and name 'pixel2world_pose'
[3.154s] Level 1:colcon.colcon_core.package_identification:_identify(src/homi_audio_player) by extensions ['ignore', 'ignore_ament_install']
[3.156s] Level 1:colcon.colcon_core.package_identification:_identify(src/homi_audio_player) by extension 'ignore'
[3.158s] Level 1:colcon.colcon_core.package_identification:_identify(src/homi_audio_player) by extension 'ignore_ament_install'
[3.162s] Level 1:colcon.colcon_core.package_identification:_identify(src/homi_audio_player) by extensions ['colcon_pkg']
[3.162s] Level 1:colcon.colcon_core.package_identification:_identify(src/homi_audio_player) by extension 'colcon_pkg'
[3.163s] Level 1:colcon.colcon_core.package_identification:_identify(src/homi_audio_player) by extensions ['colcon_meta']
[3.163s] Level 1:colcon.colcon_core.package_identification:_identify(src/homi_audio_player) by extension 'colcon_meta'
[3.163s] Level 1:colcon.colcon_core.package_identification:_identify(src/homi_audio_player) by extensions ['ros']
[3.164s] Level 1:colcon.colcon_core.package_identification:_identify(src/homi_audio_player) by extension 'ros'
[3.176s] DEBUG:colcon.colcon_core.package_identification:Package 'src/homi_audio_player' with type 'ros.ament_cmake' and name 'homi_audio_player'
[3.178s] Level 1:colcon.colcon_core.package_identification:_identify(src/homi_speech) by extensions ['ignore', 'ignore_ament_install']
[3.179s] Level 1:colcon.colcon_core.package_identification:_identify(src/homi_speech) by extension 'ignore'
[3.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/homi_speech) by extension 'ignore_ament_install'
[3.180s] Level 1:colcon.colcon_core.package_identification:_identify(src/homi_speech) by extensions ['colcon_pkg']
[3.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/homi_speech) by extension 'colcon_pkg'
[3.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/homi_speech) by extensions ['colcon_meta']
[3.181s] Level 1:colcon.colcon_core.package_identification:_identify(src/homi_speech) by extension 'colcon_meta'
[3.182s] Level 1:colcon.colcon_core.package_identification:_identify(src/homi_speech) by extensions ['ros']
[3.185s] Level 1:colcon.colcon_core.package_identification:_identify(src/homi_speech) by extension 'ros'
[3.194s] DEBUG:colcon.colcon_core.package_identification:Package 'src/homi_speech' with type 'ros.ament_cmake' and name 'homi_speech'
[3.197s] Level 1:colcon.colcon_core.package_identification:_identify(src/homi_speech_interface) by extensions ['ignore', 'ignore_ament_install']
[3.198s] Level 1:colcon.colcon_core.package_identification:_identify(src/homi_speech_interface) by extension 'ignore'
[3.198s] Level 1:colcon.colcon_core.package_identification:_identify(src/homi_speech_interface) by extension 'ignore_ament_install'
[3.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/homi_speech_interface) by extensions ['colcon_pkg']
[3.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/homi_speech_interface) by extension 'colcon_pkg'
[3.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/homi_speech_interface) by extensions ['colcon_meta']
[3.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/homi_speech_interface) by extension 'colcon_meta'
[3.199s] Level 1:colcon.colcon_core.package_identification:_identify(src/homi_speech_interface) by extensions ['ros']
[3.200s] Level 1:colcon.colcon_core.package_identification:_identify(src/homi_speech_interface) by extension 'ros'
[3.211s] DEBUG:colcon.colcon_core.package_identification:Package 'src/homi_speech_interface' with type 'ros.ament_cmake' and name 'homi_speech_interface'
[3.213s] Level 1:colcon.colcon_core.package_identification:_identify(src/launch_package) by extensions ['ignore', 'ignore_ament_install']
[3.215s] Level 1:colcon.colcon_core.package_identification:_identify(src/launch_package) by extension 'ignore'
[3.217s] Level 1:colcon.colcon_core.package_identification:_identify(src/launch_package) by extension 'ignore_ament_install'
[3.223s] Level 1:colcon.colcon_core.package_identification:_identify(src/launch_package) by extensions ['colcon_pkg']
[3.225s] Level 1:colcon.colcon_core.package_identification:_identify(src/launch_package) by extension 'colcon_pkg'
[3.227s] Level 1:colcon.colcon_core.package_identification:_identify(src/launch_package) by extensions ['colcon_meta']
[3.230s] Level 1:colcon.colcon_core.package_identification:_identify(src/launch_package) by extension 'colcon_meta'
[3.230s] Level 1:colcon.colcon_core.package_identification:_identify(src/launch_package) by extensions ['ros']
[3.230s] Level 1:colcon.colcon_core.package_identification:_identify(src/launch_package) by extension 'ros'
[3.240s] DEBUG:colcon.colcon_core.package_identification:Package 'src/launch_package' with type 'ros.ament_cmake' and name 'launch_package'
[3.242s] Level 1:colcon.colcon_core.package_identification:_identify(src/live_stream) by extensions ['ignore', 'ignore_ament_install']
[3.243s] Level 1:colcon.colcon_core.package_identification:_identify(src/live_stream) by extension 'ignore'
[3.245s] Level 1:colcon.colcon_core.package_identification:_identify(src/live_stream) by extension 'ignore_ament_install'
[3.251s] Level 1:colcon.colcon_core.package_identification:_identify(src/live_stream) by extensions ['colcon_pkg']
[3.254s] Level 1:colcon.colcon_core.package_identification:_identify(src/live_stream) by extension 'colcon_pkg'
[3.255s] Level 1:colcon.colcon_core.package_identification:_identify(src/live_stream) by extensions ['colcon_meta']
[3.255s] Level 1:colcon.colcon_core.package_identification:_identify(src/live_stream) by extension 'colcon_meta'
[3.256s] Level 1:colcon.colcon_core.package_identification:_identify(src/live_stream) by extensions ['ros']
[3.256s] Level 1:colcon.colcon_core.package_identification:_identify(src/live_stream) by extension 'ros'
[3.270s] DEBUG:colcon.colcon_core.package_identification:Package 'src/live_stream' with type 'ros.ament_cmake' and name 'live_stream'
[3.273s] Level 1:colcon.colcon_core.package_identification:_identify(src/map_server) by extensions ['ignore', 'ignore_ament_install']
[3.275s] Level 1:colcon.colcon_core.package_identification:_identify(src/map_server) by extension 'ignore'
[3.278s] Level 1:colcon.colcon_core.package_identification:_identify(src/map_server) by extension 'ignore_ament_install'
[3.283s] Level 1:colcon.colcon_core.package_identification:_identify(src/map_server) by extensions ['colcon_pkg']
[3.284s] Level 1:colcon.colcon_core.package_identification:_identify(src/map_server) by extension 'colcon_pkg'
[3.286s] Level 1:colcon.colcon_core.package_identification:_identify(src/map_server) by extensions ['colcon_meta']
[3.289s] Level 1:colcon.colcon_core.package_identification:_identify(src/map_server) by extension 'colcon_meta'
[3.289s] Level 1:colcon.colcon_core.package_identification:_identify(src/map_server) by extensions ['ros']
[3.289s] Level 1:colcon.colcon_core.package_identification:_identify(src/map_server) by extension 'ros'
[3.305s] DEBUG:colcon.colcon_core.package_identification:Package 'src/map_server' with type 'ros.ament_cmake' and name 'map_server'
[3.308s] Level 1:colcon.colcon_core.package_identification:_identify(src/mock) by extensions ['ignore', 'ignore_ament_install']
[3.309s] Level 1:colcon.colcon_core.package_identification:_identify(src/mock) by extension 'ignore'
[3.311s] Level 1:colcon.colcon_core.package_identification:_identify(src/mock) by extension 'ignore_ament_install'
[3.316s] Level 1:colcon.colcon_core.package_identification:_identify(src/mock) by extensions ['colcon_pkg']
[3.316s] Level 1:colcon.colcon_core.package_identification:_identify(src/mock) by extension 'colcon_pkg'
[3.321s] Level 1:colcon.colcon_core.package_identification:_identify(src/mock) by extensions ['colcon_meta']
[3.321s] Level 1:colcon.colcon_core.package_identification:_identify(src/mock) by extension 'colcon_meta'
[3.321s] Level 1:colcon.colcon_core.package_identification:_identify(src/mock) by extensions ['ros']
[3.321s] Level 1:colcon.colcon_core.package_identification:_identify(src/mock) by extension 'ros'
[3.333s] DEBUG:colcon.colcon_core.package_identification:Package 'src/mock' with type 'ros.ament_python' and name 'mock'
[3.336s] Level 1:colcon.colcon_core.package_identification:_identify(src/network) by extensions ['ignore', 'ignore_ament_install']
[3.337s] Level 1:colcon.colcon_core.package_identification:_identify(src/network) by extension 'ignore'
[3.337s] Level 1:colcon.colcon_core.package_identification:_identify(src/network) by extension 'ignore_ament_install'
[3.337s] Level 1:colcon.colcon_core.package_identification:_identify(src/network) by extensions ['colcon_pkg']
[3.337s] Level 1:colcon.colcon_core.package_identification:_identify(src/network) by extension 'colcon_pkg'
[3.337s] Level 1:colcon.colcon_core.package_identification:_identify(src/network) by extensions ['colcon_meta']
[3.337s] Level 1:colcon.colcon_core.package_identification:_identify(src/network) by extension 'colcon_meta'
[3.337s] Level 1:colcon.colcon_core.package_identification:_identify(src/network) by extensions ['ros']
[3.337s] Level 1:colcon.colcon_core.package_identification:_identify(src/network) by extension 'ros'
[3.344s] DEBUG:colcon.colcon_core.package_identification:Package 'src/network' with type 'ros.ament_python' and name 'network'
[3.346s] Level 1:colcon.colcon_core.package_identification:_identify(src/nvidia_control) by extensions ['ignore', 'ignore_ament_install']
[3.347s] Level 1:colcon.colcon_core.package_identification:_identify(src/nvidia_control) by extension 'ignore'
[3.349s] Level 1:colcon.colcon_core.package_identification:_identify(src/nvidia_control) by extension 'ignore_ament_install'
[3.353s] Level 1:colcon.colcon_core.package_identification:_identify(src/nvidia_control) by extensions ['colcon_pkg']
[3.354s] Level 1:colcon.colcon_core.package_identification:_identify(src/nvidia_control) by extension 'colcon_pkg'
[3.355s] Level 1:colcon.colcon_core.package_identification:_identify(src/nvidia_control) by extensions ['colcon_meta']
[3.356s] Level 1:colcon.colcon_core.package_identification:_identify(src/nvidia_control) by extension 'colcon_meta'
[3.356s] Level 1:colcon.colcon_core.package_identification:_identify(src/nvidia_control) by extensions ['ros']
[3.356s] Level 1:colcon.colcon_core.package_identification:_identify(src/nvidia_control) by extension 'ros'
[3.367s] DEBUG:colcon.colcon_core.package_identification:Package 'src/nvidia_control' with type 'ros.ament_cmake' and name 'nvidia_control'
[3.370s] Level 1:colcon.colcon_core.package_identification:_identify(src/peripherals) by extensions ['ignore', 'ignore_ament_install']
[3.371s] Level 1:colcon.colcon_core.package_identification:_identify(src/peripherals) by extension 'ignore'
[3.372s] Level 1:colcon.colcon_core.package_identification:_identify(src/peripherals) by extension 'ignore_ament_install'
[3.376s] Level 1:colcon.colcon_core.package_identification:_identify(src/peripherals) by extensions ['colcon_pkg']
[3.376s] Level 1:colcon.colcon_core.package_identification:_identify(src/peripherals) by extension 'colcon_pkg'
[3.378s] Level 1:colcon.colcon_core.package_identification:_identify(src/peripherals) by extensions ['colcon_meta']
[3.378s] Level 1:colcon.colcon_core.package_identification:_identify(src/peripherals) by extension 'colcon_meta'
[3.379s] Level 1:colcon.colcon_core.package_identification:_identify(src/peripherals) by extensions ['ros']
[3.379s] Level 1:colcon.colcon_core.package_identification:_identify(src/peripherals) by extension 'ros'
[3.391s] DEBUG:colcon.colcon_core.package_identification:Package 'src/peripherals' with type 'ros.ament_cmake' and name 'peripherals'
[3.394s] Level 1:colcon.colcon_core.package_identification:_identify(src/point_process) by extensions ['ignore', 'ignore_ament_install']
[3.395s] Level 1:colcon.colcon_core.package_identification:_identify(src/point_process) by extension 'ignore'
[3.397s] Level 1:colcon.colcon_core.package_identification:_identify(src/point_process) by extension 'ignore_ament_install'
[3.401s] Level 1:colcon.colcon_core.package_identification:_identify(src/point_process) by extensions ['colcon_pkg']
[3.402s] Level 1:colcon.colcon_core.package_identification:_identify(src/point_process) by extension 'colcon_pkg'
[3.403s] Level 1:colcon.colcon_core.package_identification:_identify(src/point_process) by extensions ['colcon_meta']
[3.405s] Level 1:colcon.colcon_core.package_identification:_identify(src/point_process) by extension 'colcon_meta'
[3.406s] Level 1:colcon.colcon_core.package_identification:_identify(src/point_process) by extensions ['ros']
[3.406s] Level 1:colcon.colcon_core.package_identification:_identify(src/point_process) by extension 'ros'
[3.420s] DEBUG:colcon.colcon_core.package_identification:Package 'src/point_process' with type 'ros.ament_python' and name 'point_process'
[3.424s] Level 1:colcon.colcon_core.package_identification:_identify(src/pwm_touch) by extensions ['ignore', 'ignore_ament_install']
[3.425s] Level 1:colcon.colcon_core.package_identification:_identify(src/pwm_touch) by extension 'ignore'
[3.428s] Level 1:colcon.colcon_core.package_identification:_identify(src/pwm_touch) by extension 'ignore_ament_install'
[3.434s] Level 1:colcon.colcon_core.package_identification:_identify(src/pwm_touch) by extensions ['colcon_pkg']
[3.436s] Level 1:colcon.colcon_core.package_identification:_identify(src/pwm_touch) by extension 'colcon_pkg'
[3.438s] Level 1:colcon.colcon_core.package_identification:_identify(src/pwm_touch) by extensions ['colcon_meta']
[3.441s] Level 1:colcon.colcon_core.package_identification:_identify(src/pwm_touch) by extension 'colcon_meta'
[3.441s] Level 1:colcon.colcon_core.package_identification:_identify(src/pwm_touch) by extensions ['ros']
[3.441s] Level 1:colcon.colcon_core.package_identification:_identify(src/pwm_touch) by extension 'ros'
[3.453s] DEBUG:colcon.colcon_core.package_identification:Package 'src/pwm_touch' with type 'ros.ament_python' and name 'pwm_touch'
[3.456s] Level 1:colcon.colcon_core.package_identification:_identify(src/rcutils) by extensions ['ignore', 'ignore_ament_install']
[3.457s] Level 1:colcon.colcon_core.package_identification:_identify(src/rcutils) by extension 'ignore'
[3.460s] Level 1:colcon.colcon_core.package_identification:_identify(src/rcutils) by extension 'ignore_ament_install'
[3.466s] Level 1:colcon.colcon_core.package_identification:_identify(src/rcutils) by extensions ['colcon_pkg']
[3.468s] Level 1:colcon.colcon_core.package_identification:_identify(src/rcutils) by extension 'colcon_pkg'
[3.470s] Level 1:colcon.colcon_core.package_identification:_identify(src/rcutils) by extensions ['colcon_meta']
[3.470s] Level 1:colcon.colcon_core.package_identification:_identify(src/rcutils) by extension 'colcon_meta'
[3.471s] Level 1:colcon.colcon_core.package_identification:_identify(src/rcutils) by extensions ['ros']
[3.473s] Level 1:colcon.colcon_core.package_identification:_identify(src/rcutils) by extension 'ros'
[3.483s] DEBUG:colcon.colcon_core.package_identification:Package 'src/rcutils' with type 'ros.ament_cmake' and name 'rcutils'
[3.485s] Level 1:colcon.colcon_core.package_identification:_identify(src/robdog_control) by extensions ['ignore', 'ignore_ament_install']
[3.487s] Level 1:colcon.colcon_core.package_identification:_identify(src/robdog_control) by extension 'ignore'
[3.487s] Level 1:colcon.colcon_core.package_identification:_identify(src/robdog_control) by extension 'ignore_ament_install'
[3.488s] Level 1:colcon.colcon_core.package_identification:_identify(src/robdog_control) by extensions ['colcon_pkg']
[3.488s] Level 1:colcon.colcon_core.package_identification:_identify(src/robdog_control) by extension 'colcon_pkg'
[3.488s] Level 1:colcon.colcon_core.package_identification:_identify(src/robdog_control) by extensions ['colcon_meta']
[3.488s] Level 1:colcon.colcon_core.package_identification:_identify(src/robdog_control) by extension 'colcon_meta'
[3.488s] Level 1:colcon.colcon_core.package_identification:_identify(src/robdog_control) by extensions ['ros']
[3.488s] Level 1:colcon.colcon_core.package_identification:_identify(src/robdog_control) by extension 'ros'
[3.498s] DEBUG:colcon.colcon_core.package_identification:Package 'src/robdog_control' with type 'ros.ament_cmake' and name 'robdog_control'
[3.501s] Level 1:colcon.colcon_core.package_identification:_identify(src/robdog_qt_tools) by extensions ['ignore', 'ignore_ament_install']
[3.502s] Level 1:colcon.colcon_core.package_identification:_identify(src/robdog_qt_tools) by extension 'ignore'
[3.505s] Level 1:colcon.colcon_core.package_identification:_identify(src/robdog_qt_tools) by extension 'ignore_ament_install'
[3.512s] Level 1:colcon.colcon_core.package_identification:_identify(src/robdog_qt_tools) by extensions ['colcon_pkg']
[3.513s] Level 1:colcon.colcon_core.package_identification:_identify(src/robdog_qt_tools) by extension 'colcon_pkg'
[3.515s] Level 1:colcon.colcon_core.package_identification:_identify(src/robdog_qt_tools) by extensions ['colcon_meta']
[3.515s] Level 1:colcon.colcon_core.package_identification:_identify(src/robdog_qt_tools) by extension 'colcon_meta'
[3.516s] Level 1:colcon.colcon_core.package_identification:_identify(src/robdog_qt_tools) by extensions ['ros']
[3.519s] Level 1:colcon.colcon_core.package_identification:_identify(src/robdog_qt_tools) by extension 'ros'
[3.530s] DEBUG:colcon.colcon_core.package_identification:Package 'src/robdog_qt_tools' with type 'ros.ament_cmake' and name 'robdog_qt_tools'
[3.532s] Level 1:colcon.colcon_core.package_identification:_identify(src/rtsp_image_capture) by extensions ['ignore', 'ignore_ament_install']
[3.533s] Level 1:colcon.colcon_core.package_identification:_identify(src/rtsp_image_capture) by extension 'ignore'
[3.536s] Level 1:colcon.colcon_core.package_identification:_identify(src/rtsp_image_capture) by extension 'ignore_ament_install'
[3.542s] Level 1:colcon.colcon_core.package_identification:_identify(src/rtsp_image_capture) by extensions ['colcon_pkg']
[3.544s] Level 1:colcon.colcon_core.package_identification:_identify(src/rtsp_image_capture) by extension 'colcon_pkg'
[3.546s] Level 1:colcon.colcon_core.package_identification:_identify(src/rtsp_image_capture) by extensions ['colcon_meta']
[3.546s] Level 1:colcon.colcon_core.package_identification:_identify(src/rtsp_image_capture) by extension 'colcon_meta'
[3.546s] Level 1:colcon.colcon_core.package_identification:_identify(src/rtsp_image_capture) by extensions ['ros']
[3.546s] Level 1:colcon.colcon_core.package_identification:_identify(src/rtsp_image_capture) by extension 'ros'
[3.561s] DEBUG:colcon.colcon_core.package_identification:Package 'src/rtsp_image_capture' with type 'ros.ament_cmake' and name 'rtsp_image_capture'
[3.563s] Level 1:colcon.colcon_core.package_identification:_identify(src/ultrasonic) by extensions ['ignore', 'ignore_ament_install']
[3.564s] Level 1:colcon.colcon_core.package_identification:_identify(src/ultrasonic) by extension 'ignore'
[3.566s] Level 1:colcon.colcon_core.package_identification:_identify(src/ultrasonic) by extension 'ignore_ament_install'
[3.570s] Level 1:colcon.colcon_core.package_identification:_identify(src/ultrasonic) by extensions ['colcon_pkg']
[3.570s] Level 1:colcon.colcon_core.package_identification:_identify(src/ultrasonic) by extension 'colcon_pkg'
[3.571s] Level 1:colcon.colcon_core.package_identification:_identify(src/ultrasonic) by extensions ['colcon_meta']
[3.571s] Level 1:colcon.colcon_core.package_identification:_identify(src/ultrasonic) by extension 'colcon_meta'
[3.572s] Level 1:colcon.colcon_core.package_identification:_identify(src/ultrasonic) by extensions ['ros']
[3.572s] Level 1:colcon.colcon_core.package_identification:_identify(src/ultrasonic) by extension 'ros'
[3.584s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ultrasonic' with type 'ros.ament_cmake' and name 'ultrasonic'
[3.588s] Level 1:colcon.colcon_core.package_identification:_identify(src/uploadlog) by extensions ['ignore', 'ignore_ament_install']
[3.589s] Level 1:colcon.colcon_core.package_identification:_identify(src/uploadlog) by extension 'ignore'
[3.590s] Level 1:colcon.colcon_core.package_identification:_identify(src/uploadlog) by extension 'ignore_ament_install'
[3.595s] Level 1:colcon.colcon_core.package_identification:_identify(src/uploadlog) by extensions ['colcon_pkg']
[3.595s] Level 1:colcon.colcon_core.package_identification:_identify(src/uploadlog) by extension 'colcon_pkg'
[3.596s] Level 1:colcon.colcon_core.package_identification:_identify(src/uploadlog) by extensions ['colcon_meta']
[3.597s] Level 1:colcon.colcon_core.package_identification:_identify(src/uploadlog) by extension 'colcon_meta'
[3.597s] Level 1:colcon.colcon_core.package_identification:_identify(src/uploadlog) by extensions ['ros']
[3.597s] Level 1:colcon.colcon_core.package_identification:_identify(src/uploadlog) by extension 'ros'
[3.609s] DEBUG:colcon.colcon_core.package_identification:Package 'src/uploadlog' with type 'ros.ament_python' and name 'uploadlog'
[3.612s] Level 1:colcon.colcon_core.package_identification:_identify(src/uwb_package) by extensions ['ignore', 'ignore_ament_install']
[3.614s] Level 1:colcon.colcon_core.package_identification:_identify(src/uwb_package) by extension 'ignore'
[3.616s] Level 1:colcon.colcon_core.package_identification:_identify(src/uwb_package) by extension 'ignore_ament_install'
[3.622s] Level 1:colcon.colcon_core.package_identification:_identify(src/uwb_package) by extensions ['colcon_pkg']
[3.624s] Level 1:colcon.colcon_core.package_identification:_identify(src/uwb_package) by extension 'colcon_pkg'
[3.626s] Level 1:colcon.colcon_core.package_identification:_identify(src/uwb_package) by extensions ['colcon_meta']
[3.626s] Level 1:colcon.colcon_core.package_identification:_identify(src/uwb_package) by extension 'colcon_meta'
[3.626s] Level 1:colcon.colcon_core.package_identification:_identify(src/uwb_package) by extensions ['ros']
[3.626s] Level 1:colcon.colcon_core.package_identification:_identify(src/uwb_package) by extension 'ros'
[3.641s] DEBUG:colcon.colcon_core.package_identification:Package 'src/uwb_package' with type 'ros.ament_cmake' and name 'uwb_package'
[3.644s] Level 1:colcon.colcon_core.package_identification:_identify(src/video_gst) by extensions ['ignore', 'ignore_ament_install']
[3.646s] Level 1:colcon.colcon_core.package_identification:_identify(src/video_gst) by extension 'ignore'
[3.648s] Level 1:colcon.colcon_core.package_identification:_identify(src/video_gst) by extension 'ignore_ament_install'
[3.655s] Level 1:colcon.colcon_core.package_identification:_identify(src/video_gst) by extensions ['colcon_pkg']
[3.657s] Level 1:colcon.colcon_core.package_identification:_identify(src/video_gst) by extension 'colcon_pkg'
[3.659s] Level 1:colcon.colcon_core.package_identification:_identify(src/video_gst) by extensions ['colcon_meta']
[3.662s] Level 1:colcon.colcon_core.package_identification:_identify(src/video_gst) by extension 'colcon_meta'
[3.663s] Level 1:colcon.colcon_core.package_identification:_identify(src/video_gst) by extensions ['ros']
[3.663s] Level 1:colcon.colcon_core.package_identification:_identify(src/video_gst) by extension 'ros'
[3.675s] DEBUG:colcon.colcon_core.package_identification:Package 'src/video_gst' with type 'ros.ament_cmake' and name 'video_gst'
[3.677s] Level 1:colcon.colcon_core.package_identification:_identify(src/video_gst_neck) by extensions ['ignore', 'ignore_ament_install']
[3.678s] Level 1:colcon.colcon_core.package_identification:_identify(src/video_gst_neck) by extension 'ignore'
[3.680s] Level 1:colcon.colcon_core.package_identification:_identify(src/video_gst_neck) by extension 'ignore_ament_install'
[3.686s] Level 1:colcon.colcon_core.package_identification:_identify(src/video_gst_neck) by extensions ['colcon_pkg']
[3.688s] Level 1:colcon.colcon_core.package_identification:_identify(src/video_gst_neck) by extension 'colcon_pkg'
[3.690s] Level 1:colcon.colcon_core.package_identification:_identify(src/video_gst_neck) by extensions ['colcon_meta']
[3.690s] Level 1:colcon.colcon_core.package_identification:_identify(src/video_gst_neck) by extension 'colcon_meta'
[3.690s] Level 1:colcon.colcon_core.package_identification:_identify(src/video_gst_neck) by extensions ['ros']
[3.692s] Level 1:colcon.colcon_core.package_identification:_identify(src/video_gst_neck) by extension 'ros'
[3.705s] DEBUG:colcon.colcon_core.package_identification:Package 'src/video_gst_neck' with type 'ros.ament_cmake' and name 'video_gst_neck'
[3.707s] Level 1:colcon.colcon_core.package_identification:_identify(src/video_gst_nv) by extensions ['ignore', 'ignore_ament_install']
[3.709s] Level 1:colcon.colcon_core.package_identification:_identify(src/video_gst_nv) by extension 'ignore'
[3.711s] Level 1:colcon.colcon_core.package_identification:_identify(src/video_gst_nv) by extension 'ignore_ament_install'
[3.717s] Level 1:colcon.colcon_core.package_identification:_identify(src/video_gst_nv) by extensions ['colcon_pkg']
[3.719s] Level 1:colcon.colcon_core.package_identification:_identify(src/video_gst_nv) by extension 'colcon_pkg'
[3.721s] Level 1:colcon.colcon_core.package_identification:_identify(src/video_gst_nv) by extensions ['colcon_meta']
[3.724s] Level 1:colcon.colcon_core.package_identification:_identify(src/video_gst_nv) by extension 'colcon_meta'
[3.724s] Level 1:colcon.colcon_core.package_identification:_identify(src/video_gst_nv) by extensions ['ros']
[3.725s] Level 1:colcon.colcon_core.package_identification:_identify(src/video_gst_nv) by extension 'ros'
[3.756s] DEBUG:colcon.colcon_core.package_identification:Package 'src/video_gst_nv' with type 'ros.ament_cmake' and name 'video_gst_nv'
[3.756s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[3.756s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[3.756s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[3.756s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[3.756s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[3.915s] INFO:colcon.colcon_core.package_selection:Skipping package 'mock' in 'src/mock'
[3.916s] INFO:colcon.colcon_core.package_selection:Skipping package 'robdog_qt_tools' in 'src/robdog_qt_tools'
[3.916s] INFO:colcon.colcon_core.package_selection:Skipping package 'rtsp_image_capture' in 'src/rtsp_image_capture'
[3.916s] INFO:colcon.colcon_core.package_selection:Skipping package 'video_gst_neck' in 'src/video_gst_neck'
[3.919s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[3.920s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[3.929s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 5 installed packages in /opt/ros/unitree_ros2/cyclonedds_ws/install
[3.936s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 278 installed packages in /opt/ros/foxy
[3.946s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[4.049s] Level 5:colcon.colcon_core.verb:set package 'expression' build argument 'cmake_args' from command line to '['-DCMAKE_CUSTOM_PRODUCT_NO=1_0', '-DCMAKE_CUSTOM_LIB_INSTALL=/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/env/lib', '--no-warn-unused-cli']'
[4.050s] Level 5:colcon.colcon_core.verb:set package 'expression' build argument 'cmake_target' from command line to 'None'
[4.050s] Level 5:colcon.colcon_core.verb:set package 'expression' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[4.050s] Level 5:colcon.colcon_core.verb:set package 'expression' build argument 'cmake_clean_cache' from command line to 'False'
[4.050s] Level 5:colcon.colcon_core.verb:set package 'expression' build argument 'cmake_clean_first' from command line to 'False'
[4.050s] Level 5:colcon.colcon_core.verb:set package 'expression' build argument 'cmake_force_configure' from command line to 'False'
[4.050s] Level 5:colcon.colcon_core.verb:set package 'expression' build argument 'ament_cmake_args' from command line to 'None'
[4.054s] Level 5:colcon.colcon_core.verb:set package 'expression' build argument 'catkin_cmake_args' from command line to 'None'
[4.054s] Level 5:colcon.colcon_core.verb:set package 'expression' build argument 'catkin_skip_building_tests' from command line to 'False'
[4.054s] DEBUG:colcon.colcon_core.verb:Building package 'expression' with the following arguments: {'ament_cmake_args': None, 'build_base': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/build/expression', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_CUSTOM_PRODUCT_NO=1_0', '-DCMAKE_CUSTOM_LIB_INSTALL=/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/env/lib', '--no-warn-unused-cli'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install/expression', 'merge_install': False, 'path': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/src/expression', 'symlink_install': False, 'test_result_base': None}
[4.055s] Level 5:colcon.colcon_core.verb:set package 'follow_me_bringup' build argument 'cmake_args' from command line to '['-DCMAKE_CUSTOM_PRODUCT_NO=1_0', '-DCMAKE_CUSTOM_LIB_INSTALL=/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/env/lib', '--no-warn-unused-cli']'
[4.055s] Level 5:colcon.colcon_core.verb:set package 'follow_me_bringup' build argument 'cmake_target' from command line to 'None'
[4.055s] Level 5:colcon.colcon_core.verb:set package 'follow_me_bringup' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[4.055s] Level 5:colcon.colcon_core.verb:set package 'follow_me_bringup' build argument 'cmake_clean_cache' from command line to 'False'
[4.055s] Level 5:colcon.colcon_core.verb:set package 'follow_me_bringup' build argument 'cmake_clean_first' from command line to 'False'
[4.055s] Level 5:colcon.colcon_core.verb:set package 'follow_me_bringup' build argument 'cmake_force_configure' from command line to 'False'
[4.055s] Level 5:colcon.colcon_core.verb:set package 'follow_me_bringup' build argument 'ament_cmake_args' from command line to 'None'
[4.055s] Level 5:colcon.colcon_core.verb:set package 'follow_me_bringup' build argument 'catkin_cmake_args' from command line to 'None'
[4.055s] Level 5:colcon.colcon_core.verb:set package 'follow_me_bringup' build argument 'catkin_skip_building_tests' from command line to 'False'
[4.055s] DEBUG:colcon.colcon_core.verb:Building package 'follow_me_bringup' with the following arguments: {'ament_cmake_args': None, 'build_base': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/build/follow_me_bringup', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_CUSTOM_PRODUCT_NO=1_0', '-DCMAKE_CUSTOM_LIB_INSTALL=/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/env/lib', '--no-warn-unused-cli'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install/follow_me_bringup', 'merge_install': False, 'path': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/src/follow_me/src/follow_me_bringup', 'symlink_install': False, 'test_result_base': None}
[4.056s] Level 5:colcon.colcon_core.verb:set package 'follow_msgs' build argument 'cmake_args' from command line to '['-DCMAKE_CUSTOM_PRODUCT_NO=1_0', '-DCMAKE_CUSTOM_LIB_INSTALL=/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/env/lib', '--no-warn-unused-cli']'
[4.056s] Level 5:colcon.colcon_core.verb:set package 'follow_msgs' build argument 'cmake_target' from command line to 'None'
[4.056s] Level 5:colcon.colcon_core.verb:set package 'follow_msgs' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[4.056s] Level 5:colcon.colcon_core.verb:set package 'follow_msgs' build argument 'cmake_clean_cache' from command line to 'False'
[4.056s] Level 5:colcon.colcon_core.verb:set package 'follow_msgs' build argument 'cmake_clean_first' from command line to 'False'
[4.056s] Level 5:colcon.colcon_core.verb:set package 'follow_msgs' build argument 'cmake_force_configure' from command line to 'False'
[4.056s] Level 5:colcon.colcon_core.verb:set package 'follow_msgs' build argument 'ament_cmake_args' from command line to 'None'
[4.056s] Level 5:colcon.colcon_core.verb:set package 'follow_msgs' build argument 'catkin_cmake_args' from command line to 'None'
[4.056s] Level 5:colcon.colcon_core.verb:set package 'follow_msgs' build argument 'catkin_skip_building_tests' from command line to 'False'
[4.056s] DEBUG:colcon.colcon_core.verb:Building package 'follow_msgs' with the following arguments: {'ament_cmake_args': None, 'build_base': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/build/follow_msgs', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_CUSTOM_PRODUCT_NO=1_0', '-DCMAKE_CUSTOM_LIB_INSTALL=/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/env/lib', '--no-warn-unused-cli'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install/follow_msgs', 'merge_install': False, 'path': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/src/follow_me/src/follow_msgs', 'symlink_install': False, 'test_result_base': None}
[4.060s] Level 5:colcon.colcon_core.verb:set package 'follow_strategy' build argument 'cmake_args' from command line to '['-DCMAKE_CUSTOM_PRODUCT_NO=1_0', '-DCMAKE_CUSTOM_LIB_INSTALL=/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/env/lib', '--no-warn-unused-cli']'
[4.060s] Level 5:colcon.colcon_core.verb:set package 'follow_strategy' build argument 'cmake_target' from command line to 'None'
[4.060s] Level 5:colcon.colcon_core.verb:set package 'follow_strategy' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[4.061s] Level 5:colcon.colcon_core.verb:set package 'follow_strategy' build argument 'cmake_clean_cache' from command line to 'False'
[4.061s] Level 5:colcon.colcon_core.verb:set package 'follow_strategy' build argument 'cmake_clean_first' from command line to 'False'
[4.061s] Level 5:colcon.colcon_core.verb:set package 'follow_strategy' build argument 'cmake_force_configure' from command line to 'False'
[4.061s] Level 5:colcon.colcon_core.verb:set package 'follow_strategy' build argument 'ament_cmake_args' from command line to 'None'
[4.061s] Level 5:colcon.colcon_core.verb:set package 'follow_strategy' build argument 'catkin_cmake_args' from command line to 'None'
[4.061s] Level 5:colcon.colcon_core.verb:set package 'follow_strategy' build argument 'catkin_skip_building_tests' from command line to 'False'
[4.061s] DEBUG:colcon.colcon_core.verb:Building package 'follow_strategy' with the following arguments: {'ament_cmake_args': None, 'build_base': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/build/follow_strategy', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_CUSTOM_PRODUCT_NO=1_0', '-DCMAKE_CUSTOM_LIB_INSTALL=/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/env/lib', '--no-warn-unused-cli'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install/follow_strategy', 'merge_install': False, 'path': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/src/follow_me/src/follow_strategy', 'symlink_install': False, 'test_result_base': None}
[4.061s] Level 5:colcon.colcon_core.verb:set package 'homi_speech_interface' build argument 'cmake_args' from command line to '['-DCMAKE_CUSTOM_PRODUCT_NO=1_0', '-DCMAKE_CUSTOM_LIB_INSTALL=/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/env/lib', '--no-warn-unused-cli']'
[4.065s] Level 5:colcon.colcon_core.verb:set package 'homi_speech_interface' build argument 'cmake_target' from command line to 'None'
[4.065s] Level 5:colcon.colcon_core.verb:set package 'homi_speech_interface' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[4.065s] Level 5:colcon.colcon_core.verb:set package 'homi_speech_interface' build argument 'cmake_clean_cache' from command line to 'False'
[4.065s] Level 5:colcon.colcon_core.verb:set package 'homi_speech_interface' build argument 'cmake_clean_first' from command line to 'False'
[4.065s] Level 5:colcon.colcon_core.verb:set package 'homi_speech_interface' build argument 'cmake_force_configure' from command line to 'False'
[4.065s] Level 5:colcon.colcon_core.verb:set package 'homi_speech_interface' build argument 'ament_cmake_args' from command line to 'None'
[4.065s] Level 5:colcon.colcon_core.verb:set package 'homi_speech_interface' build argument 'catkin_cmake_args' from command line to 'None'
[4.065s] Level 5:colcon.colcon_core.verb:set package 'homi_speech_interface' build argument 'catkin_skip_building_tests' from command line to 'False'
[4.065s] DEBUG:colcon.colcon_core.verb:Building package 'homi_speech_interface' with the following arguments: {'ament_cmake_args': None, 'build_base': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/build/homi_speech_interface', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_CUSTOM_PRODUCT_NO=1_0', '-DCMAKE_CUSTOM_LIB_INSTALL=/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/env/lib', '--no-warn-unused-cli'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install/homi_speech_interface', 'merge_install': False, 'path': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/src/homi_speech_interface', 'symlink_install': False, 'test_result_base': None}
[4.066s] Level 5:colcon.colcon_core.verb:set package 'launch_package' build argument 'cmake_args' from command line to '['-DCMAKE_CUSTOM_PRODUCT_NO=1_0', '-DCMAKE_CUSTOM_LIB_INSTALL=/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/env/lib', '--no-warn-unused-cli']'
[4.066s] Level 5:colcon.colcon_core.verb:set package 'launch_package' build argument 'cmake_target' from command line to 'None'
[4.066s] Level 5:colcon.colcon_core.verb:set package 'launch_package' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[4.066s] Level 5:colcon.colcon_core.verb:set package 'launch_package' build argument 'cmake_clean_cache' from command line to 'False'
[4.066s] Level 5:colcon.colcon_core.verb:set package 'launch_package' build argument 'cmake_clean_first' from command line to 'False'
[4.066s] Level 5:colcon.colcon_core.verb:set package 'launch_package' build argument 'cmake_force_configure' from command line to 'False'
[4.066s] Level 5:colcon.colcon_core.verb:set package 'launch_package' build argument 'ament_cmake_args' from command line to 'None'
[4.066s] Level 5:colcon.colcon_core.verb:set package 'launch_package' build argument 'catkin_cmake_args' from command line to 'None'
[4.066s] Level 5:colcon.colcon_core.verb:set package 'launch_package' build argument 'catkin_skip_building_tests' from command line to 'False'
[4.066s] DEBUG:colcon.colcon_core.verb:Building package 'launch_package' with the following arguments: {'ament_cmake_args': None, 'build_base': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/build/launch_package', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_CUSTOM_PRODUCT_NO=1_0', '-DCMAKE_CUSTOM_LIB_INSTALL=/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/env/lib', '--no-warn-unused-cli'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install/launch_package', 'merge_install': False, 'path': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/src/launch_package', 'symlink_install': False, 'test_result_base': None}
[4.067s] Level 5:colcon.colcon_core.verb:set package 'map_server' build argument 'cmake_args' from command line to '['-DCMAKE_CUSTOM_PRODUCT_NO=1_0', '-DCMAKE_CUSTOM_LIB_INSTALL=/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/env/lib', '--no-warn-unused-cli']'
[4.067s] Level 5:colcon.colcon_core.verb:set package 'map_server' build argument 'cmake_target' from command line to 'None'
[4.067s] Level 5:colcon.colcon_core.verb:set package 'map_server' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[4.067s] Level 5:colcon.colcon_core.verb:set package 'map_server' build argument 'cmake_clean_cache' from command line to 'False'
[4.067s] Level 5:colcon.colcon_core.verb:set package 'map_server' build argument 'cmake_clean_first' from command line to 'False'
[4.067s] Level 5:colcon.colcon_core.verb:set package 'map_server' build argument 'cmake_force_configure' from command line to 'False'
[4.071s] Level 5:colcon.colcon_core.verb:set package 'map_server' build argument 'ament_cmake_args' from command line to 'None'
[4.071s] Level 5:colcon.colcon_core.verb:set package 'map_server' build argument 'catkin_cmake_args' from command line to 'None'
[4.071s] Level 5:colcon.colcon_core.verb:set package 'map_server' build argument 'catkin_skip_building_tests' from command line to 'False'
[4.071s] DEBUG:colcon.colcon_core.verb:Building package 'map_server' with the following arguments: {'ament_cmake_args': None, 'build_base': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/build/map_server', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_CUSTOM_PRODUCT_NO=1_0', '-DCMAKE_CUSTOM_LIB_INSTALL=/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/env/lib', '--no-warn-unused-cli'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install/map_server', 'merge_install': False, 'path': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/src/map_server', 'symlink_install': False, 'test_result_base': None}
[4.072s] Level 5:colcon.colcon_core.verb:set package 'nvidia_control' build argument 'cmake_args' from command line to '['-DCMAKE_CUSTOM_PRODUCT_NO=1_0', '-DCMAKE_CUSTOM_LIB_INSTALL=/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/env/lib', '--no-warn-unused-cli']'
[4.072s] Level 5:colcon.colcon_core.verb:set package 'nvidia_control' build argument 'cmake_target' from command line to 'None'
[4.072s] Level 5:colcon.colcon_core.verb:set package 'nvidia_control' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[4.072s] Level 5:colcon.colcon_core.verb:set package 'nvidia_control' build argument 'cmake_clean_cache' from command line to 'False'
[4.072s] Level 5:colcon.colcon_core.verb:set package 'nvidia_control' build argument 'cmake_clean_first' from command line to 'False'
[4.072s] Level 5:colcon.colcon_core.verb:set package 'nvidia_control' build argument 'cmake_force_configure' from command line to 'False'
[4.072s] Level 5:colcon.colcon_core.verb:set package 'nvidia_control' build argument 'ament_cmake_args' from command line to 'None'
[4.072s] Level 5:colcon.colcon_core.verb:set package 'nvidia_control' build argument 'catkin_cmake_args' from command line to 'None'
[4.072s] Level 5:colcon.colcon_core.verb:set package 'nvidia_control' build argument 'catkin_skip_building_tests' from command line to 'False'
[4.072s] DEBUG:colcon.colcon_core.verb:Building package 'nvidia_control' with the following arguments: {'ament_cmake_args': None, 'build_base': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/build/nvidia_control', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_CUSTOM_PRODUCT_NO=1_0', '-DCMAKE_CUSTOM_LIB_INSTALL=/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/env/lib', '--no-warn-unused-cli'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install/nvidia_control', 'merge_install': False, 'path': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/src/nvidia_control', 'symlink_install': False, 'test_result_base': None}
[4.073s] Level 5:colcon.colcon_core.verb:set package 'pixel2world_pose' build argument 'cmake_args' from command line to '['-DCMAKE_CUSTOM_PRODUCT_NO=1_0', '-DCMAKE_CUSTOM_LIB_INSTALL=/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/env/lib', '--no-warn-unused-cli']'
[4.073s] Level 5:colcon.colcon_core.verb:set package 'pixel2world_pose' build argument 'cmake_target' from command line to 'None'
[4.073s] Level 5:colcon.colcon_core.verb:set package 'pixel2world_pose' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[4.073s] Level 5:colcon.colcon_core.verb:set package 'pixel2world_pose' build argument 'cmake_clean_cache' from command line to 'False'
[4.073s] Level 5:colcon.colcon_core.verb:set package 'pixel2world_pose' build argument 'cmake_clean_first' from command line to 'False'
[4.073s] Level 5:colcon.colcon_core.verb:set package 'pixel2world_pose' build argument 'cmake_force_configure' from command line to 'False'
[4.073s] Level 5:colcon.colcon_core.verb:set package 'pixel2world_pose' build argument 'ament_cmake_args' from command line to 'None'
[4.073s] Level 5:colcon.colcon_core.verb:set package 'pixel2world_pose' build argument 'catkin_cmake_args' from command line to 'None'
[4.073s] Level 5:colcon.colcon_core.verb:set package 'pixel2world_pose' build argument 'catkin_skip_building_tests' from command line to 'False'
[4.073s] DEBUG:colcon.colcon_core.verb:Building package 'pixel2world_pose' with the following arguments: {'ament_cmake_args': None, 'build_base': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/build/pixel2world_pose', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_CUSTOM_PRODUCT_NO=1_0', '-DCMAKE_CUSTOM_LIB_INSTALL=/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/env/lib', '--no-warn-unused-cli'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install/pixel2world_pose', 'merge_install': False, 'path': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/src/follow_me/src/pixel2world_pose', 'symlink_install': False, 'test_result_base': None}
[4.074s] Level 5:colcon.colcon_core.verb:set package 'point_process' build argument 'cmake_args' from command line to '['-DCMAKE_CUSTOM_PRODUCT_NO=1_0', '-DCMAKE_CUSTOM_LIB_INSTALL=/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/env/lib', '--no-warn-unused-cli']'
[4.077s] Level 5:colcon.colcon_core.verb:set package 'point_process' build argument 'cmake_target' from command line to 'None'
[4.077s] Level 5:colcon.colcon_core.verb:set package 'point_process' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[4.077s] Level 5:colcon.colcon_core.verb:set package 'point_process' build argument 'cmake_clean_cache' from command line to 'False'
[4.077s] Level 5:colcon.colcon_core.verb:set package 'point_process' build argument 'cmake_clean_first' from command line to 'False'
[4.077s] Level 5:colcon.colcon_core.verb:set package 'point_process' build argument 'cmake_force_configure' from command line to 'False'
[4.077s] Level 5:colcon.colcon_core.verb:set package 'point_process' build argument 'ament_cmake_args' from command line to 'None'
[4.077s] Level 5:colcon.colcon_core.verb:set package 'point_process' build argument 'catkin_cmake_args' from command line to 'None'
[4.077s] Level 5:colcon.colcon_core.verb:set package 'point_process' build argument 'catkin_skip_building_tests' from command line to 'False'
[4.077s] DEBUG:colcon.colcon_core.verb:Building package 'point_process' with the following arguments: {'ament_cmake_args': None, 'build_base': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/build/point_process', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_CUSTOM_PRODUCT_NO=1_0', '-DCMAKE_CUSTOM_LIB_INSTALL=/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/env/lib', '--no-warn-unused-cli'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install/point_process', 'merge_install': False, 'path': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/src/point_process', 'symlink_install': False, 'test_result_base': None}
[4.078s] Level 5:colcon.colcon_core.verb:set package 'pwm_touch' build argument 'cmake_args' from command line to '['-DCMAKE_CUSTOM_PRODUCT_NO=1_0', '-DCMAKE_CUSTOM_LIB_INSTALL=/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/env/lib', '--no-warn-unused-cli']'
[4.078s] Level 5:colcon.colcon_core.verb:set package 'pwm_touch' build argument 'cmake_target' from command line to 'None'
[4.078s] Level 5:colcon.colcon_core.verb:set package 'pwm_touch' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[4.078s] Level 5:colcon.colcon_core.verb:set package 'pwm_touch' build argument 'cmake_clean_cache' from command line to 'False'
[4.078s] Level 5:colcon.colcon_core.verb:set package 'pwm_touch' build argument 'cmake_clean_first' from command line to 'False'
[4.078s] Level 5:colcon.colcon_core.verb:set package 'pwm_touch' build argument 'cmake_force_configure' from command line to 'False'
[4.078s] Level 5:colcon.colcon_core.verb:set package 'pwm_touch' build argument 'ament_cmake_args' from command line to 'None'
[4.078s] Level 5:colcon.colcon_core.verb:set package 'pwm_touch' build argument 'catkin_cmake_args' from command line to 'None'
[4.078s] Level 5:colcon.colcon_core.verb:set package 'pwm_touch' build argument 'catkin_skip_building_tests' from command line to 'False'
[4.078s] DEBUG:colcon.colcon_core.verb:Building package 'pwm_touch' with the following arguments: {'ament_cmake_args': None, 'build_base': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/build/pwm_touch', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_CUSTOM_PRODUCT_NO=1_0', '-DCMAKE_CUSTOM_LIB_INSTALL=/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/env/lib', '--no-warn-unused-cli'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install/pwm_touch', 'merge_install': False, 'path': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/src/pwm_touch', 'symlink_install': False, 'test_result_base': None}
[4.079s] Level 5:colcon.colcon_core.verb:set package 'rcutils' build argument 'cmake_args' from command line to '['-DCMAKE_CUSTOM_PRODUCT_NO=1_0', '-DCMAKE_CUSTOM_LIB_INSTALL=/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/env/lib', '--no-warn-unused-cli']'
[4.082s] Level 5:colcon.colcon_core.verb:set package 'rcutils' build argument 'cmake_target' from command line to 'None'
[4.082s] Level 5:colcon.colcon_core.verb:set package 'rcutils' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[4.082s] Level 5:colcon.colcon_core.verb:set package 'rcutils' build argument 'cmake_clean_cache' from command line to 'False'
[4.082s] Level 5:colcon.colcon_core.verb:set package 'rcutils' build argument 'cmake_clean_first' from command line to 'False'
[4.082s] Level 5:colcon.colcon_core.verb:set package 'rcutils' build argument 'cmake_force_configure' from command line to 'False'
[4.082s] Level 5:colcon.colcon_core.verb:set package 'rcutils' build argument 'ament_cmake_args' from command line to 'None'
[4.082s] Level 5:colcon.colcon_core.verb:set package 'rcutils' build argument 'catkin_cmake_args' from command line to 'None'
[4.082s] Level 5:colcon.colcon_core.verb:set package 'rcutils' build argument 'catkin_skip_building_tests' from command line to 'False'
[4.082s] DEBUG:colcon.colcon_core.verb:Building package 'rcutils' with the following arguments: {'ament_cmake_args': None, 'build_base': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/build/rcutils', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_CUSTOM_PRODUCT_NO=1_0', '-DCMAKE_CUSTOM_LIB_INSTALL=/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/env/lib', '--no-warn-unused-cli'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install/rcutils', 'merge_install': False, 'path': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/src/rcutils', 'symlink_install': False, 'test_result_base': None}
[4.083s] Level 5:colcon.colcon_core.verb:set package 'uploadlog' build argument 'cmake_args' from command line to '['-DCMAKE_CUSTOM_PRODUCT_NO=1_0', '-DCMAKE_CUSTOM_LIB_INSTALL=/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/env/lib', '--no-warn-unused-cli']'
[4.083s] Level 5:colcon.colcon_core.verb:set package 'uploadlog' build argument 'cmake_target' from command line to 'None'
[4.083s] Level 5:colcon.colcon_core.verb:set package 'uploadlog' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[4.083s] Level 5:colcon.colcon_core.verb:set package 'uploadlog' build argument 'cmake_clean_cache' from command line to 'False'
[4.083s] Level 5:colcon.colcon_core.verb:set package 'uploadlog' build argument 'cmake_clean_first' from command line to 'False'
[4.083s] Level 5:colcon.colcon_core.verb:set package 'uploadlog' build argument 'cmake_force_configure' from command line to 'False'
[4.083s] Level 5:colcon.colcon_core.verb:set package 'uploadlog' build argument 'ament_cmake_args' from command line to 'None'
[4.083s] Level 5:colcon.colcon_core.verb:set package 'uploadlog' build argument 'catkin_cmake_args' from command line to 'None'
[4.083s] Level 5:colcon.colcon_core.verb:set package 'uploadlog' build argument 'catkin_skip_building_tests' from command line to 'False'
[4.083s] DEBUG:colcon.colcon_core.verb:Building package 'uploadlog' with the following arguments: {'ament_cmake_args': None, 'build_base': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/build/uploadlog', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_CUSTOM_PRODUCT_NO=1_0', '-DCMAKE_CUSTOM_LIB_INSTALL=/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/env/lib', '--no-warn-unused-cli'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install/uploadlog', 'merge_install': False, 'path': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/src/uploadlog', 'symlink_install': False, 'test_result_base': None}
[4.084s] Level 5:colcon.colcon_core.verb:set package 'uwb_package' build argument 'cmake_args' from command line to '['-DCMAKE_CUSTOM_PRODUCT_NO=1_0', '-DCMAKE_CUSTOM_LIB_INSTALL=/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/env/lib', '--no-warn-unused-cli']'
[4.087s] Level 5:colcon.colcon_core.verb:set package 'uwb_package' build argument 'cmake_target' from command line to 'None'
[4.087s] Level 5:colcon.colcon_core.verb:set package 'uwb_package' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[4.087s] Level 5:colcon.colcon_core.verb:set package 'uwb_package' build argument 'cmake_clean_cache' from command line to 'False'
[4.087s] Level 5:colcon.colcon_core.verb:set package 'uwb_package' build argument 'cmake_clean_first' from command line to 'False'
[4.087s] Level 5:colcon.colcon_core.verb:set package 'uwb_package' build argument 'cmake_force_configure' from command line to 'False'
[4.087s] Level 5:colcon.colcon_core.verb:set package 'uwb_package' build argument 'ament_cmake_args' from command line to 'None'
[4.087s] Level 5:colcon.colcon_core.verb:set package 'uwb_package' build argument 'catkin_cmake_args' from command line to 'None'
[4.087s] Level 5:colcon.colcon_core.verb:set package 'uwb_package' build argument 'catkin_skip_building_tests' from command line to 'False'
[4.087s] DEBUG:colcon.colcon_core.verb:Building package 'uwb_package' with the following arguments: {'ament_cmake_args': None, 'build_base': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/build/uwb_package', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_CUSTOM_PRODUCT_NO=1_0', '-DCMAKE_CUSTOM_LIB_INSTALL=/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/env/lib', '--no-warn-unused-cli'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install/uwb_package', 'merge_install': False, 'path': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/src/uwb_package', 'symlink_install': False, 'test_result_base': None}
[4.088s] Level 5:colcon.colcon_core.verb:set package 'a2dp' build argument 'cmake_args' from command line to '['-DCMAKE_CUSTOM_PRODUCT_NO=1_0', '-DCMAKE_CUSTOM_LIB_INSTALL=/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/env/lib', '--no-warn-unused-cli']'
[4.088s] Level 5:colcon.colcon_core.verb:set package 'a2dp' build argument 'cmake_target' from command line to 'None'
[4.088s] Level 5:colcon.colcon_core.verb:set package 'a2dp' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[4.088s] Level 5:colcon.colcon_core.verb:set package 'a2dp' build argument 'cmake_clean_cache' from command line to 'False'
[4.088s] Level 5:colcon.colcon_core.verb:set package 'a2dp' build argument 'cmake_clean_first' from command line to 'False'
[4.088s] Level 5:colcon.colcon_core.verb:set package 'a2dp' build argument 'cmake_force_configure' from command line to 'False'
[4.088s] Level 5:colcon.colcon_core.verb:set package 'a2dp' build argument 'ament_cmake_args' from command line to 'None'
[4.088s] Level 5:colcon.colcon_core.verb:set package 'a2dp' build argument 'catkin_cmake_args' from command line to 'None'
[4.088s] Level 5:colcon.colcon_core.verb:set package 'a2dp' build argument 'catkin_skip_building_tests' from command line to 'False'
[4.088s] DEBUG:colcon.colcon_core.verb:Building package 'a2dp' with the following arguments: {'ament_cmake_args': None, 'build_base': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/build/a2dp', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_CUSTOM_PRODUCT_NO=1_0', '-DCMAKE_CUSTOM_LIB_INSTALL=/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/env/lib', '--no-warn-unused-cli'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install/a2dp', 'merge_install': False, 'path': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/src/a2dp', 'symlink_install': False, 'test_result_base': None}
[4.089s] Level 5:colcon.colcon_core.verb:set package 'andlink' build argument 'cmake_args' from command line to '['-DCMAKE_CUSTOM_PRODUCT_NO=1_0', '-DCMAKE_CUSTOM_LIB_INSTALL=/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/env/lib', '--no-warn-unused-cli']'
[4.089s] Level 5:colcon.colcon_core.verb:set package 'andlink' build argument 'cmake_target' from command line to 'None'
[4.089s] Level 5:colcon.colcon_core.verb:set package 'andlink' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[4.089s] Level 5:colcon.colcon_core.verb:set package 'andlink' build argument 'cmake_clean_cache' from command line to 'False'
[4.089s] Level 5:colcon.colcon_core.verb:set package 'andlink' build argument 'cmake_clean_first' from command line to 'False'
[4.089s] Level 5:colcon.colcon_core.verb:set package 'andlink' build argument 'cmake_force_configure' from command line to 'False'
[4.089s] Level 5:colcon.colcon_core.verb:set package 'andlink' build argument 'ament_cmake_args' from command line to 'None'
[4.089s] Level 5:colcon.colcon_core.verb:set package 'andlink' build argument 'catkin_cmake_args' from command line to 'None'
[4.089s] Level 5:colcon.colcon_core.verb:set package 'andlink' build argument 'catkin_skip_building_tests' from command line to 'False'
[4.089s] DEBUG:colcon.colcon_core.verb:Building package 'andlink' with the following arguments: {'ament_cmake_args': None, 'build_base': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/build/andlink', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_CUSTOM_PRODUCT_NO=1_0', '-DCMAKE_CUSTOM_LIB_INSTALL=/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/env/lib', '--no-warn-unused-cli'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install/andlink', 'merge_install': False, 'path': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/src/andlink', 'symlink_install': False, 'test_result_base': None}
[4.090s] Level 5:colcon.colcon_core.verb:set package 'audio_player' build argument 'cmake_args' from command line to '['-DCMAKE_CUSTOM_PRODUCT_NO=1_0', '-DCMAKE_CUSTOM_LIB_INSTALL=/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/env/lib', '--no-warn-unused-cli']'
[4.091s] Level 5:colcon.colcon_core.verb:set package 'audio_player' build argument 'cmake_target' from command line to 'None'
[4.092s] Level 5:colcon.colcon_core.verb:set package 'audio_player' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[4.092s] Level 5:colcon.colcon_core.verb:set package 'audio_player' build argument 'cmake_clean_cache' from command line to 'False'
[4.092s] Level 5:colcon.colcon_core.verb:set package 'audio_player' build argument 'cmake_clean_first' from command line to 'False'
[4.092s] Level 5:colcon.colcon_core.verb:set package 'audio_player' build argument 'cmake_force_configure' from command line to 'False'
[4.092s] Level 5:colcon.colcon_core.verb:set package 'audio_player' build argument 'ament_cmake_args' from command line to 'None'
[4.092s] Level 5:colcon.colcon_core.verb:set package 'audio_player' build argument 'catkin_cmake_args' from command line to 'None'
[4.092s] Level 5:colcon.colcon_core.verb:set package 'audio_player' build argument 'catkin_skip_building_tests' from command line to 'False'
[4.092s] DEBUG:colcon.colcon_core.verb:Building package 'audio_player' with the following arguments: {'ament_cmake_args': None, 'build_base': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/build/audio_player', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_CUSTOM_PRODUCT_NO=1_0', '-DCMAKE_CUSTOM_LIB_INSTALL=/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/env/lib', '--no-warn-unused-cli'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install/audio_player', 'merge_install': False, 'path': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/src/audio_player', 'symlink_install': False, 'test_result_base': None}
[4.093s] Level 5:colcon.colcon_core.verb:set package 'audio_recorder' build argument 'cmake_args' from command line to '['-DCMAKE_CUSTOM_PRODUCT_NO=1_0', '-DCMAKE_CUSTOM_LIB_INSTALL=/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/env/lib', '--no-warn-unused-cli']'
[4.093s] Level 5:colcon.colcon_core.verb:set package 'audio_recorder' build argument 'cmake_target' from command line to 'None'
[4.093s] Level 5:colcon.colcon_core.verb:set package 'audio_recorder' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[4.093s] Level 5:colcon.colcon_core.verb:set package 'audio_recorder' build argument 'cmake_clean_cache' from command line to 'False'
[4.093s] Level 5:colcon.colcon_core.verb:set package 'audio_recorder' build argument 'cmake_clean_first' from command line to 'False'
[4.093s] Level 5:colcon.colcon_core.verb:set package 'audio_recorder' build argument 'cmake_force_configure' from command line to 'False'
[4.093s] Level 5:colcon.colcon_core.verb:set package 'audio_recorder' build argument 'ament_cmake_args' from command line to 'None'
[4.093s] Level 5:colcon.colcon_core.verb:set package 'audio_recorder' build argument 'catkin_cmake_args' from command line to 'None'
[4.093s] Level 5:colcon.colcon_core.verb:set package 'audio_recorder' build argument 'catkin_skip_building_tests' from command line to 'False'
[4.093s] DEBUG:colcon.colcon_core.verb:Building package 'audio_recorder' with the following arguments: {'ament_cmake_args': None, 'build_base': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/build/audio_recorder', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_CUSTOM_PRODUCT_NO=1_0', '-DCMAKE_CUSTOM_LIB_INSTALL=/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/env/lib', '--no-warn-unused-cli'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install/audio_recorder', 'merge_install': False, 'path': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/src/audio_recorder', 'symlink_install': False, 'test_result_base': None}
[4.094s] Level 5:colcon.colcon_core.verb:set package 'ble' build argument 'cmake_args' from command line to '['-DCMAKE_CUSTOM_PRODUCT_NO=1_0', '-DCMAKE_CUSTOM_LIB_INSTALL=/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/env/lib', '--no-warn-unused-cli']'
[4.094s] Level 5:colcon.colcon_core.verb:set package 'ble' build argument 'cmake_target' from command line to 'None'
[4.094s] Level 5:colcon.colcon_core.verb:set package 'ble' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[4.094s] Level 5:colcon.colcon_core.verb:set package 'ble' build argument 'cmake_clean_cache' from command line to 'False'
[4.094s] Level 5:colcon.colcon_core.verb:set package 'ble' build argument 'cmake_clean_first' from command line to 'False'
[4.094s] Level 5:colcon.colcon_core.verb:set package 'ble' build argument 'cmake_force_configure' from command line to 'False'
[4.094s] Level 5:colcon.colcon_core.verb:set package 'ble' build argument 'ament_cmake_args' from command line to 'None'
[4.094s] Level 5:colcon.colcon_core.verb:set package 'ble' build argument 'catkin_cmake_args' from command line to 'None'
[4.094s] Level 5:colcon.colcon_core.verb:set package 'ble' build argument 'catkin_skip_building_tests' from command line to 'False'
[4.094s] DEBUG:colcon.colcon_core.verb:Building package 'ble' with the following arguments: {'ament_cmake_args': None, 'build_base': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/build/ble', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_CUSTOM_PRODUCT_NO=1_0', '-DCMAKE_CUSTOM_LIB_INSTALL=/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/env/lib', '--no-warn-unused-cli'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install/ble', 'merge_install': False, 'path': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/src/ble', 'symlink_install': False, 'test_result_base': None}
[4.095s] Level 5:colcon.colcon_core.verb:set package 'cmcc_rtc' build argument 'cmake_args' from command line to '['-DCMAKE_CUSTOM_PRODUCT_NO=1_0', '-DCMAKE_CUSTOM_LIB_INSTALL=/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/env/lib', '--no-warn-unused-cli']'
[4.098s] Level 5:colcon.colcon_core.verb:set package 'cmcc_rtc' build argument 'cmake_target' from command line to 'None'
[4.098s] Level 5:colcon.colcon_core.verb:set package 'cmcc_rtc' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[4.098s] Level 5:colcon.colcon_core.verb:set package 'cmcc_rtc' build argument 'cmake_clean_cache' from command line to 'False'
[4.098s] Level 5:colcon.colcon_core.verb:set package 'cmcc_rtc' build argument 'cmake_clean_first' from command line to 'False'
[4.098s] Level 5:colcon.colcon_core.verb:set package 'cmcc_rtc' build argument 'cmake_force_configure' from command line to 'False'
[4.098s] Level 5:colcon.colcon_core.verb:set package 'cmcc_rtc' build argument 'ament_cmake_args' from command line to 'None'
[4.098s] Level 5:colcon.colcon_core.verb:set package 'cmcc_rtc' build argument 'catkin_cmake_args' from command line to 'None'
[4.098s] Level 5:colcon.colcon_core.verb:set package 'cmcc_rtc' build argument 'catkin_skip_building_tests' from command line to 'False'
[4.098s] DEBUG:colcon.colcon_core.verb:Building package 'cmcc_rtc' with the following arguments: {'ament_cmake_args': None, 'build_base': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/build/cmcc_rtc', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_CUSTOM_PRODUCT_NO=1_0', '-DCMAKE_CUSTOM_LIB_INSTALL=/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/env/lib', '--no-warn-unused-cli'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install/cmcc_rtc', 'merge_install': False, 'path': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/src/cmcc_rtc', 'symlink_install': False, 'test_result_base': None}
[4.100s] Level 5:colcon.colcon_core.verb:set package 'follow_rcs' build argument 'cmake_args' from command line to '['-DCMAKE_CUSTOM_PRODUCT_NO=1_0', '-DCMAKE_CUSTOM_LIB_INSTALL=/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/env/lib', '--no-warn-unused-cli']'
[4.103s] Level 5:colcon.colcon_core.verb:set package 'follow_rcs' build argument 'cmake_target' from command line to 'None'
[4.104s] Level 5:colcon.colcon_core.verb:set package 'follow_rcs' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[4.104s] Level 5:colcon.colcon_core.verb:set package 'follow_rcs' build argument 'cmake_clean_cache' from command line to 'False'
[4.104s] Level 5:colcon.colcon_core.verb:set package 'follow_rcs' build argument 'cmake_clean_first' from command line to 'False'
[4.104s] Level 5:colcon.colcon_core.verb:set package 'follow_rcs' build argument 'cmake_force_configure' from command line to 'False'
[4.104s] Level 5:colcon.colcon_core.verb:set package 'follow_rcs' build argument 'ament_cmake_args' from command line to 'None'
[4.104s] Level 5:colcon.colcon_core.verb:set package 'follow_rcs' build argument 'catkin_cmake_args' from command line to 'None'
[4.104s] Level 5:colcon.colcon_core.verb:set package 'follow_rcs' build argument 'catkin_skip_building_tests' from command line to 'False'
[4.104s] DEBUG:colcon.colcon_core.verb:Building package 'follow_rcs' with the following arguments: {'ament_cmake_args': None, 'build_base': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/build/follow_rcs', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_CUSTOM_PRODUCT_NO=1_0', '-DCMAKE_CUSTOM_LIB_INSTALL=/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/env/lib', '--no-warn-unused-cli'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install/follow_rcs', 'merge_install': False, 'path': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/src/follow_me/src/follow_rcs', 'symlink_install': False, 'test_result_base': None}
[4.105s] Level 5:colcon.colcon_core.verb:set package 'homi_audio_player' build argument 'cmake_args' from command line to '['-DCMAKE_CUSTOM_PRODUCT_NO=1_0', '-DCMAKE_CUSTOM_LIB_INSTALL=/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/env/lib', '--no-warn-unused-cli']'
[4.110s] Level 5:colcon.colcon_core.verb:set package 'homi_audio_player' build argument 'cmake_target' from command line to 'None'
[4.110s] Level 5:colcon.colcon_core.verb:set package 'homi_audio_player' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[4.111s] Level 5:colcon.colcon_core.verb:set package 'homi_audio_player' build argument 'cmake_clean_cache' from command line to 'False'
[4.111s] Level 5:colcon.colcon_core.verb:set package 'homi_audio_player' build argument 'cmake_clean_first' from command line to 'False'
[4.111s] Level 5:colcon.colcon_core.verb:set package 'homi_audio_player' build argument 'cmake_force_configure' from command line to 'False'
[4.111s] Level 5:colcon.colcon_core.verb:set package 'homi_audio_player' build argument 'ament_cmake_args' from command line to 'None'
[4.111s] Level 5:colcon.colcon_core.verb:set package 'homi_audio_player' build argument 'catkin_cmake_args' from command line to 'None'
[4.111s] Level 5:colcon.colcon_core.verb:set package 'homi_audio_player' build argument 'catkin_skip_building_tests' from command line to 'False'
[4.111s] DEBUG:colcon.colcon_core.verb:Building package 'homi_audio_player' with the following arguments: {'ament_cmake_args': None, 'build_base': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/build/homi_audio_player', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_CUSTOM_PRODUCT_NO=1_0', '-DCMAKE_CUSTOM_LIB_INSTALL=/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/env/lib', '--no-warn-unused-cli'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install/homi_audio_player', 'merge_install': False, 'path': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/src/homi_audio_player', 'symlink_install': False, 'test_result_base': None}
[4.112s] Level 5:colcon.colcon_core.verb:set package 'homi_speech' build argument 'cmake_args' from command line to '['-DCMAKE_CUSTOM_PRODUCT_NO=1_0', '-DCMAKE_CUSTOM_LIB_INSTALL=/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/env/lib', '--no-warn-unused-cli']'
[4.112s] Level 5:colcon.colcon_core.verb:set package 'homi_speech' build argument 'cmake_target' from command line to 'None'
[4.112s] Level 5:colcon.colcon_core.verb:set package 'homi_speech' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[4.112s] Level 5:colcon.colcon_core.verb:set package 'homi_speech' build argument 'cmake_clean_cache' from command line to 'False'
[4.112s] Level 5:colcon.colcon_core.verb:set package 'homi_speech' build argument 'cmake_clean_first' from command line to 'False'
[4.113s] Level 5:colcon.colcon_core.verb:set package 'homi_speech' build argument 'cmake_force_configure' from command line to 'False'
[4.113s] Level 5:colcon.colcon_core.verb:set package 'homi_speech' build argument 'ament_cmake_args' from command line to 'None'
[4.113s] Level 5:colcon.colcon_core.verb:set package 'homi_speech' build argument 'catkin_cmake_args' from command line to 'None'
[4.113s] Level 5:colcon.colcon_core.verb:set package 'homi_speech' build argument 'catkin_skip_building_tests' from command line to 'False'
[4.113s] DEBUG:colcon.colcon_core.verb:Building package 'homi_speech' with the following arguments: {'ament_cmake_args': None, 'build_base': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/build/homi_speech', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_CUSTOM_PRODUCT_NO=1_0', '-DCMAKE_CUSTOM_LIB_INSTALL=/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/env/lib', '--no-warn-unused-cli'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install/homi_speech', 'merge_install': False, 'path': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/src/homi_speech', 'symlink_install': False, 'test_result_base': None}
[4.116s] Level 5:colcon.colcon_core.verb:set package 'live_stream' build argument 'cmake_args' from command line to '['-DCMAKE_CUSTOM_PRODUCT_NO=1_0', '-DCMAKE_CUSTOM_LIB_INSTALL=/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/env/lib', '--no-warn-unused-cli']'
[4.116s] Level 5:colcon.colcon_core.verb:set package 'live_stream' build argument 'cmake_target' from command line to 'None'
[4.116s] Level 5:colcon.colcon_core.verb:set package 'live_stream' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[4.116s] Level 5:colcon.colcon_core.verb:set package 'live_stream' build argument 'cmake_clean_cache' from command line to 'False'
[4.116s] Level 5:colcon.colcon_core.verb:set package 'live_stream' build argument 'cmake_clean_first' from command line to 'False'
[4.117s] Level 5:colcon.colcon_core.verb:set package 'live_stream' build argument 'cmake_force_configure' from command line to 'False'
[4.117s] Level 5:colcon.colcon_core.verb:set package 'live_stream' build argument 'ament_cmake_args' from command line to 'None'
[4.117s] Level 5:colcon.colcon_core.verb:set package 'live_stream' build argument 'catkin_cmake_args' from command line to 'None'
[4.117s] Level 5:colcon.colcon_core.verb:set package 'live_stream' build argument 'catkin_skip_building_tests' from command line to 'False'
[4.117s] DEBUG:colcon.colcon_core.verb:Building package 'live_stream' with the following arguments: {'ament_cmake_args': None, 'build_base': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/build/live_stream', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_CUSTOM_PRODUCT_NO=1_0', '-DCMAKE_CUSTOM_LIB_INSTALL=/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/env/lib', '--no-warn-unused-cli'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install/live_stream', 'merge_install': False, 'path': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/src/live_stream', 'symlink_install': False, 'test_result_base': None}
[4.117s] Level 5:colcon.colcon_core.verb:set package 'network' build argument 'cmake_args' from command line to '['-DCMAKE_CUSTOM_PRODUCT_NO=1_0', '-DCMAKE_CUSTOM_LIB_INSTALL=/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/env/lib', '--no-warn-unused-cli']'
[4.117s] Level 5:colcon.colcon_core.verb:set package 'network' build argument 'cmake_target' from command line to 'None'
[4.117s] Level 5:colcon.colcon_core.verb:set package 'network' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[4.117s] Level 5:colcon.colcon_core.verb:set package 'network' build argument 'cmake_clean_cache' from command line to 'False'
[4.117s] Level 5:colcon.colcon_core.verb:set package 'network' build argument 'cmake_clean_first' from command line to 'False'
[4.117s] Level 5:colcon.colcon_core.verb:set package 'network' build argument 'cmake_force_configure' from command line to 'False'
[4.117s] Level 5:colcon.colcon_core.verb:set package 'network' build argument 'ament_cmake_args' from command line to 'None'
[4.117s] Level 5:colcon.colcon_core.verb:set package 'network' build argument 'catkin_cmake_args' from command line to 'None'
[4.117s] Level 5:colcon.colcon_core.verb:set package 'network' build argument 'catkin_skip_building_tests' from command line to 'False'
[4.117s] DEBUG:colcon.colcon_core.verb:Building package 'network' with the following arguments: {'ament_cmake_args': None, 'build_base': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/build/network', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_CUSTOM_PRODUCT_NO=1_0', '-DCMAKE_CUSTOM_LIB_INSTALL=/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/env/lib', '--no-warn-unused-cli'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install/network', 'merge_install': False, 'path': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/src/network', 'symlink_install': False, 'test_result_base': None}
[4.118s] Level 5:colcon.colcon_core.verb:set package 'peripherals' build argument 'cmake_args' from command line to '['-DCMAKE_CUSTOM_PRODUCT_NO=1_0', '-DCMAKE_CUSTOM_LIB_INSTALL=/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/env/lib', '--no-warn-unused-cli']'
[4.118s] Level 5:colcon.colcon_core.verb:set package 'peripherals' build argument 'cmake_target' from command line to 'None'
[4.118s] Level 5:colcon.colcon_core.verb:set package 'peripherals' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[4.118s] Level 5:colcon.colcon_core.verb:set package 'peripherals' build argument 'cmake_clean_cache' from command line to 'False'
[4.118s] Level 5:colcon.colcon_core.verb:set package 'peripherals' build argument 'cmake_clean_first' from command line to 'False'
[4.118s] Level 5:colcon.colcon_core.verb:set package 'peripherals' build argument 'cmake_force_configure' from command line to 'False'
[4.118s] Level 5:colcon.colcon_core.verb:set package 'peripherals' build argument 'ament_cmake_args' from command line to 'None'
[4.118s] Level 5:colcon.colcon_core.verb:set package 'peripherals' build argument 'catkin_cmake_args' from command line to 'None'
[4.118s] Level 5:colcon.colcon_core.verb:set package 'peripherals' build argument 'catkin_skip_building_tests' from command line to 'False'
[4.118s] DEBUG:colcon.colcon_core.verb:Building package 'peripherals' with the following arguments: {'ament_cmake_args': None, 'build_base': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/build/peripherals', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_CUSTOM_PRODUCT_NO=1_0', '-DCMAKE_CUSTOM_LIB_INSTALL=/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/env/lib', '--no-warn-unused-cli'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install/peripherals', 'merge_install': False, 'path': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/src/peripherals', 'symlink_install': False, 'test_result_base': None}
[4.118s] Level 5:colcon.colcon_core.verb:set package 'robdog_control' build argument 'cmake_args' from command line to '['-DCMAKE_CUSTOM_PRODUCT_NO=1_0', '-DCMAKE_CUSTOM_LIB_INSTALL=/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/env/lib', '--no-warn-unused-cli']'
[4.122s] Level 5:colcon.colcon_core.verb:set package 'robdog_control' build argument 'cmake_target' from command line to 'None'
[4.122s] Level 5:colcon.colcon_core.verb:set package 'robdog_control' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[4.123s] Level 5:colcon.colcon_core.verb:set package 'robdog_control' build argument 'cmake_clean_cache' from command line to 'False'
[4.123s] Level 5:colcon.colcon_core.verb:set package 'robdog_control' build argument 'cmake_clean_first' from command line to 'False'
[4.123s] Level 5:colcon.colcon_core.verb:set package 'robdog_control' build argument 'cmake_force_configure' from command line to 'False'
[4.123s] Level 5:colcon.colcon_core.verb:set package 'robdog_control' build argument 'ament_cmake_args' from command line to 'None'
[4.123s] Level 5:colcon.colcon_core.verb:set package 'robdog_control' build argument 'catkin_cmake_args' from command line to 'None'
[4.123s] Level 5:colcon.colcon_core.verb:set package 'robdog_control' build argument 'catkin_skip_building_tests' from command line to 'False'
[4.124s] DEBUG:colcon.colcon_core.verb:Building package 'robdog_control' with the following arguments: {'ament_cmake_args': None, 'build_base': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/build/robdog_control', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_CUSTOM_PRODUCT_NO=1_0', '-DCMAKE_CUSTOM_LIB_INSTALL=/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/env/lib', '--no-warn-unused-cli'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install/robdog_control', 'merge_install': False, 'path': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/src/robdog_control', 'symlink_install': False, 'test_result_base': None}
[4.126s] Level 5:colcon.colcon_core.verb:set package 'ultrasonic' build argument 'cmake_args' from command line to '['-DCMAKE_CUSTOM_PRODUCT_NO=1_0', '-DCMAKE_CUSTOM_LIB_INSTALL=/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/env/lib', '--no-warn-unused-cli']'
[4.128s] Level 5:colcon.colcon_core.verb:set package 'ultrasonic' build argument 'cmake_target' from command line to 'None'
[4.129s] Level 5:colcon.colcon_core.verb:set package 'ultrasonic' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[4.129s] Level 5:colcon.colcon_core.verb:set package 'ultrasonic' build argument 'cmake_clean_cache' from command line to 'False'
[4.129s] Level 5:colcon.colcon_core.verb:set package 'ultrasonic' build argument 'cmake_clean_first' from command line to 'False'
[4.129s] Level 5:colcon.colcon_core.verb:set package 'ultrasonic' build argument 'cmake_force_configure' from command line to 'False'
[4.129s] Level 5:colcon.colcon_core.verb:set package 'ultrasonic' build argument 'ament_cmake_args' from command line to 'None'
[4.129s] Level 5:colcon.colcon_core.verb:set package 'ultrasonic' build argument 'catkin_cmake_args' from command line to 'None'
[4.129s] Level 5:colcon.colcon_core.verb:set package 'ultrasonic' build argument 'catkin_skip_building_tests' from command line to 'False'
[4.129s] DEBUG:colcon.colcon_core.verb:Building package 'ultrasonic' with the following arguments: {'ament_cmake_args': None, 'build_base': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/build/ultrasonic', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_CUSTOM_PRODUCT_NO=1_0', '-DCMAKE_CUSTOM_LIB_INSTALL=/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/env/lib', '--no-warn-unused-cli'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install/ultrasonic', 'merge_install': False, 'path': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/src/ultrasonic', 'symlink_install': False, 'test_result_base': None}
[4.131s] Level 5:colcon.colcon_core.verb:set package 'video_gst' build argument 'cmake_args' from command line to '['-DCMAKE_CUSTOM_PRODUCT_NO=1_0', '-DCMAKE_CUSTOM_LIB_INSTALL=/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/env/lib', '--no-warn-unused-cli']'
[4.134s] Level 5:colcon.colcon_core.verb:set package 'video_gst' build argument 'cmake_target' from command line to 'None'
[4.134s] Level 5:colcon.colcon_core.verb:set package 'video_gst' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[4.134s] Level 5:colcon.colcon_core.verb:set package 'video_gst' build argument 'cmake_clean_cache' from command line to 'False'
[4.134s] Level 5:colcon.colcon_core.verb:set package 'video_gst' build argument 'cmake_clean_first' from command line to 'False'
[4.134s] Level 5:colcon.colcon_core.verb:set package 'video_gst' build argument 'cmake_force_configure' from command line to 'False'
[4.134s] Level 5:colcon.colcon_core.verb:set package 'video_gst' build argument 'ament_cmake_args' from command line to 'None'
[4.134s] Level 5:colcon.colcon_core.verb:set package 'video_gst' build argument 'catkin_cmake_args' from command line to 'None'
[4.134s] Level 5:colcon.colcon_core.verb:set package 'video_gst' build argument 'catkin_skip_building_tests' from command line to 'False'
[4.134s] DEBUG:colcon.colcon_core.verb:Building package 'video_gst' with the following arguments: {'ament_cmake_args': None, 'build_base': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/build/video_gst', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_CUSTOM_PRODUCT_NO=1_0', '-DCMAKE_CUSTOM_LIB_INSTALL=/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/env/lib', '--no-warn-unused-cli'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install/video_gst', 'merge_install': False, 'path': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/src/video_gst', 'symlink_install': False, 'test_result_base': None}
[4.135s] Level 5:colcon.colcon_core.verb:set package 'video_gst_nv' build argument 'cmake_args' from command line to '['-DCMAKE_CUSTOM_PRODUCT_NO=1_0', '-DCMAKE_CUSTOM_LIB_INSTALL=/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/env/lib', '--no-warn-unused-cli']'
[4.135s] Level 5:colcon.colcon_core.verb:set package 'video_gst_nv' build argument 'cmake_target' from command line to 'None'
[4.135s] Level 5:colcon.colcon_core.verb:set package 'video_gst_nv' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[4.135s] Level 5:colcon.colcon_core.verb:set package 'video_gst_nv' build argument 'cmake_clean_cache' from command line to 'False'
[4.135s] Level 5:colcon.colcon_core.verb:set package 'video_gst_nv' build argument 'cmake_clean_first' from command line to 'False'
[4.135s] Level 5:colcon.colcon_core.verb:set package 'video_gst_nv' build argument 'cmake_force_configure' from command line to 'False'
[4.135s] Level 5:colcon.colcon_core.verb:set package 'video_gst_nv' build argument 'ament_cmake_args' from command line to 'None'
[4.135s] Level 5:colcon.colcon_core.verb:set package 'video_gst_nv' build argument 'catkin_cmake_args' from command line to 'None'
[4.135s] Level 5:colcon.colcon_core.verb:set package 'video_gst_nv' build argument 'catkin_skip_building_tests' from command line to 'False'
[4.135s] DEBUG:colcon.colcon_core.verb:Building package 'video_gst_nv' with the following arguments: {'ament_cmake_args': None, 'build_base': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/build/video_gst_nv', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': ['-DCMAKE_CUSTOM_PRODUCT_NO=1_0', '-DCMAKE_CUSTOM_LIB_INSTALL=/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/env/lib', '--no-warn-unused-cli'], 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install/video_gst_nv', 'merge_install': False, 'path': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/src/video_gst_nv', 'symlink_install': False, 'test_result_base': None}
[4.135s] INFO:colcon.colcon_core.executor:Executing jobs using 'sequential' executor
[4.137s] DEBUG:colcon.colcon_core.executor.sequential:run_until_complete 'expression'
[4.140s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/mine/worktrees/unitree-debug/xiaoli_application_ros2/src/expression' with build type 'ament_cmake'
[4.140s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/mine/worktrees/unitree-debug/xiaoli_application_ros2/src/expression'
[4.146s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[4.146s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[4.146s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[4.273s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/build/expression': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/foxy /usr/bin/cmake /mine/worktrees/unitree-debug/xiaoli_application_ros2/src/expression -DCMAKE_CUSTOM_PRODUCT_NO=1_0 -DCMAKE_CUSTOM_LIB_INSTALL=/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/env/lib --no-warn-unused-cli -DCMAKE_INSTALL_PREFIX=/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install/expression
[15.454s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/build/expression' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/foxy /usr/bin/cmake /mine/worktrees/unitree-debug/xiaoli_application_ros2/src/expression -DCMAKE_CUSTOM_PRODUCT_NO=1_0 -DCMAKE_CUSTOM_LIB_INSTALL=/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/env/lib --no-warn-unused-cli -DCMAKE_INSTALL_PREFIX=/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install/expression
[15.467s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/build/expression': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/foxy /usr/bin/cmake --build /mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/build/expression -- -j8 -l8
[16.056s] DEBUG:colcon.colcon_core.executor.sequential:run_until_complete 'expression' was interrupted
[16.057s] DEBUG:colcon.colcon_core.executor.sequential:run_until_complete 'expression' again
[16.064s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/build/expression' returned '-2': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/opt/ros/foxy /usr/bin/cmake --build /mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/build/expression -- -j8 -l8
[16.079s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(expression)
[16.090s] Level 1:colcon.colcon_core.environment:checking '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install/expression' for CMake module files
[16.092s] Level 1:colcon.colcon_core.environment:checking '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install/expression' for CMake config files
[16.093s] Level 1:colcon.colcon_core.environment:checking '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install/expression/bin'
[16.093s] Level 1:colcon.colcon_core.environment:checking '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install/expression/lib/pkgconfig/expression.pc'
[16.094s] Level 1:colcon.colcon_core.environment:checking '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install/expression/lib/python3.8/site-packages'
[16.095s] Level 1:colcon.colcon_core.environment:checking '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install/expression/bin'
[16.096s] INFO:colcon.colcon_core.shell:Creating package script '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install/expression/share/expression/package.ps1'
[16.125s] INFO:colcon.colcon_core.shell:Creating package descriptor '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install/expression/share/expression/package.dsv'
[16.142s] INFO:colcon.colcon_core.shell:Creating package script '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install/expression/share/expression/package.sh'
[16.160s] INFO:colcon.colcon_core.shell:Creating package script '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install/expression/share/expression/package.bash'
[16.176s] INFO:colcon.colcon_core.shell:Creating package script '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install/expression/share/expression/package.zsh'
[16.191s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install/expression/share/colcon-core/packages/expression)
[16.212s] DEBUG:colcon.colcon_core.executor.sequential:run_until_complete 'expression' finished
[16.212s] DEBUG:colcon.colcon_core.executor.sequential:closing loop
[16.213s] DEBUG:colcon.colcon_core.executor.sequential:loop closed
[16.217s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[16.314s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.notify_send': Could not find 'notify-send'
[16.314s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[16.314s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[16.315s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[16.317s] DEBUG:colcon.colcon_notification.desktop_notification.notify2:Failed to initialize notify2: org.freedesktop.DBus.Error.ServiceUnknown: The name org.freedesktop.Notifications was not provided by any .service files
[16.319s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[16.320s] INFO:colcon.colcon_core.shell:Creating prefix script '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install/local_setup.ps1'
[16.339s] INFO:colcon.colcon_core.shell:Creating prefix util module '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install/_local_setup_util_ps1.py'
[16.362s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install/setup.ps1'
[16.376s] INFO:colcon.colcon_core.shell:Creating prefix script '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install/local_setup.sh'
[16.393s] INFO:colcon.colcon_core.shell:Creating prefix util module '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install/_local_setup_util_sh.py'
[16.409s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install/setup.sh'
[16.422s] INFO:colcon.colcon_core.shell:Creating prefix script '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install/local_setup.bash'
[16.433s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install/setup.bash'
[16.448s] INFO:colcon.colcon_core.shell:Creating prefix script '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install/local_setup.zsh'
[16.463s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install/setup.zsh'
