[0.000000] (-) TimerEvent: {}
[0.000288] (-) JobUnselected: {'identifier': 'mock'}
[0.000336] (-) JobUnselected: {'identifier': 'robdog_qt_tools'}
[0.000360] (-) JobUnselected: {'identifier': 'rtsp_image_capture'}
[0.000387] (-) JobUnselected: {'identifier': 'video_gst_neck'}
[0.000418] (expression) JobQueued: {'identifier': 'expression', 'dependencies': OrderedDict()}
[0.000446] (follow_me_bringup) JobQueued: {'identifier': 'follow_me_bringup', 'dependencies': OrderedDict()}
[0.000469] (follow_msgs) JobQueued: {'identifier': 'follow_msgs', 'dependencies': OrderedDict()}
[0.000491] (follow_strategy) JobQueued: {'identifier': 'follow_strategy', 'dependencies': OrderedDict()}
[0.000512] (homi_speech_interface) JobQueued: {'identifier': 'homi_speech_interface', 'dependencies': OrderedDict()}
[0.000534] (launch_package) JobQueued: {'identifier': 'launch_package', 'dependencies': OrderedDict()}
[0.000555] (map_server) JobQueued: {'identifier': 'map_server', 'dependencies': OrderedDict()}
[0.000576] (nvidia_control) JobQueued: {'identifier': 'nvidia_control', 'dependencies': OrderedDict()}
[0.000596] (pixel2world_pose) JobQueued: {'identifier': 'pixel2world_pose', 'dependencies': OrderedDict()}
[0.000617] (point_process) JobQueued: {'identifier': 'point_process', 'dependencies': OrderedDict()}
[0.000790] (pwm_touch) JobQueued: {'identifier': 'pwm_touch', 'dependencies': OrderedDict()}
[0.000997] (rcutils) JobQueued: {'identifier': 'rcutils', 'dependencies': OrderedDict()}
[0.001184] (uploadlog) JobQueued: {'identifier': 'uploadlog', 'dependencies': OrderedDict()}
[0.001471] (uwb_package) JobQueued: {'identifier': 'uwb_package', 'dependencies': OrderedDict()}
[0.001532] (a2dp) JobQueued: {'identifier': 'a2dp', 'dependencies': OrderedDict([('homi_speech_interface', '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install/homi_speech_interface')])}
[0.001590] (andlink) JobQueued: {'identifier': 'andlink', 'dependencies': OrderedDict([('homi_speech_interface', '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install/homi_speech_interface')])}
[0.001637] (audio_player) JobQueued: {'identifier': 'audio_player', 'dependencies': OrderedDict([('homi_speech_interface', '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install/homi_speech_interface')])}
[0.001681] (audio_recorder) JobQueued: {'identifier': 'audio_recorder', 'dependencies': OrderedDict([('homi_speech_interface', '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install/homi_speech_interface')])}
[0.001727] (ble) JobQueued: {'identifier': 'ble', 'dependencies': OrderedDict([('homi_speech_interface', '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install/homi_speech_interface')])}
[0.001788] (cmcc_rtc) JobQueued: {'identifier': 'cmcc_rtc', 'dependencies': OrderedDict([('homi_speech_interface', '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install/homi_speech_interface')])}
[0.001831] (follow_rcs) JobQueued: {'identifier': 'follow_rcs', 'dependencies': OrderedDict([('follow_msgs', '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install/follow_msgs'), ('homi_speech_interface', '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install/homi_speech_interface')])}
[0.001878] (homi_audio_player) JobQueued: {'identifier': 'homi_audio_player', 'dependencies': OrderedDict([('homi_speech_interface', '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install/homi_speech_interface')])}
[0.001947] (homi_speech) JobQueued: {'identifier': 'homi_speech', 'dependencies': OrderedDict([('homi_speech_interface', '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install/homi_speech_interface')])}
[0.001975] (live_stream) JobQueued: {'identifier': 'live_stream', 'dependencies': OrderedDict([('homi_speech_interface', '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install/homi_speech_interface')])}
[0.002001] (network) JobQueued: {'identifier': 'network', 'dependencies': OrderedDict([('homi_speech_interface', '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install/homi_speech_interface')])}
[0.002066] (peripherals) JobQueued: {'identifier': 'peripherals', 'dependencies': OrderedDict([('homi_speech_interface', '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install/homi_speech_interface')])}
[0.002093] (robdog_control) JobQueued: {'identifier': 'robdog_control', 'dependencies': OrderedDict([('homi_speech_interface', '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install/homi_speech_interface')])}
[0.002118] (ultrasonic) JobQueued: {'identifier': 'ultrasonic', 'dependencies': OrderedDict([('homi_speech_interface', '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install/homi_speech_interface')])}
[0.002158] (video_gst) JobQueued: {'identifier': 'video_gst', 'dependencies': OrderedDict([('homi_speech_interface', '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install/homi_speech_interface')])}
[0.002193] (video_gst_nv) JobQueued: {'identifier': 'video_gst_nv', 'dependencies': OrderedDict([('homi_speech_interface', '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install/homi_speech_interface')])}
[0.002329] (expression) JobStarted: {'identifier': 'expression'}
[0.057489] (expression) JobProgress: {'identifier': 'expression', 'progress': 'cmake'}
[0.097455] (-) TimerEvent: {}
[0.105966] (expression) Command: {'cmd': ['/usr/bin/cmake', '/mine/worktrees/unitree-debug/xiaoli_application_ros2/src/expression', '-DCMAKE_CUSTOM_PRODUCT_NO=1_0', '-DCMAKE_CUSTOM_LIB_INSTALL=/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/env/lib', '--no-warn-unused-cli', '-DCMAKE_INSTALL_PREFIX=/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/install/expression'], 'cwd': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/build/expression', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('HTTPS_PROXY', 'http://127.0.0.1:7890'), ('no_proxy', 'localhost,127.0.0.1,::1'), ('USER', 'root'), ('SSH_CLIENT', '************** 56501 22'), ('all_proxy', 'socks5h://127.0.0.1:7890'), ('XDG_SESSION_TYPE', 'tty'), ('GIT_ASKPASS', '/root/.cursor-server/cli/servers/Stable-68b8fe7396ea37d8acdaaaa08ba316ba359a4160/server/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/opt/ros/unitree_ros2/cyclonedds_ws/install/unitree_hg/lib:/opt/ros/unitree_ros2/cyclonedds_ws/install/unitree_go/lib:/opt/ros/unitree_ros2/cyclonedds_ws/install/unitree_api/lib:/opt/ros/unitree_ros2/cyclonedds_ws/install/rmw_cyclonedds_cpp/lib:/opt/ros/unitree_ros2/cyclonedds_ws/install/cyclonedds/lib:/opt/ros/foxy/opt/yaml_cpp_vendor/lib:/opt/ros/foxy/opt/rviz_ogre_vendor/lib:/opt/ros/foxy/lib/aarch64-linux-gnu:/opt/ros/foxy/lib'), ('BROWSER', '/root/.cursor-server/cli/servers/Stable-68b8fe7396ea37d8acdaaaa08ba316ba359a4160/server/bin/helpers/browser.sh'), ('LESS', '-FX'), ('MOTD_SHOWN', 'pam'), ('HOME', '/root'), ('CYCLONEDDS_URI', '<CycloneDDS><Domain><General><Interfaces>\n                            <NetworkInterface name="eth0" priority="default" multicast="default" />\n                        </Interfaces><AllowMulticast>spdp</AllowMulticast></General></Domain></CycloneDDS>'), ('OLDPWD', '/mine/worktrees'), ('TERM_PROGRAM_VERSION', '1.3.6'), ('NO_PROXY', 'localhost,127.0.0.1,::1'), ('VSCODE_IPC_HOOK_CLI', '/run/user/0/vscode-ipc-e8e913eb-41ab-4f24-a16b-d193027fa16b.sock'), ('ROS_PYTHON_VERSION', '3'), ('PAGER', 'cat'), ('VSCODE_GIT_ASKPASS_MAIN', '/root/.cursor-server/cli/servers/Stable-68b8fe7396ea37d8acdaaaa08ba316ba359a4160/server/extensions/git/dist/askpass-main.js'), ('VSCODE_GIT_ASKPASS_NODE', '/root/.cursor-server/cli/servers/Stable-68b8fe7396ea37d8acdaaaa08ba316ba359a4160/server/node'), ('SSL_CERT_FILE', '/usr/lib/ssl/certs/ca-certificates.crt'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('BUNDLED_DEBUGPY_PATH', '/root/.cursor-server/extensions/ms-python.debugpy-2025.6.0-linux-arm64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/0/bus'), ('COLORTERM', 'truecolor'), ('https_proxy', 'http://127.0.0.1:7890'), ('COLCON_PREFIX_PATH', '/opt/ros/unitree_ros2/cyclonedds_ws/install'), ('LOGNAME', 'root'), ('ALL_PROXY', 'socks5h://127.0.0.1:7890'), ('http_proxy', 'http://127.0.0.1:7890'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/opt/ros/unitree_ros2/cyclonedds_ws/install/cyclonedds/lib/aarch64-linux-gnu/pkgconfig:/opt/ros/unitree_ros2/cyclonedds_ws/install/cyclonedds/lib/pkgconfig'), ('CLAUDE_CODE_SSE_PORT', '22023'), ('XDG_SESSION_CLASS', 'user'), ('ANTHROPIC_BASE_URL', 'https://anyrouter.top'), ('TERM', 'xterm-256color'), ('XDG_SESSION_ID', '2'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/unitree_ros2/cyclonedds_ws/install/cyclonedds/bin:/opt/ros/foxy/bin:/root/.cursor-server/cli/servers/Stable-68b8fe7396ea37d8acdaaaa08ba316ba359a4160/server/bin/remote-cli:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/root/.cursor-server/extensions/ms-python.debugpy-2025.6.0-linux-arm64/bundled/scripts/noConfigScripts'), ('XDG_RUNTIME_DIR', '/run/user/0'), ('SSL_CERT_DIR', '/usr/lib/ssl/certs'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/root/.cursor-server/extensions/ms-python.debugpy-2025.6.0-linux-arm64/.noConfigDebugAdapterEndpoints/endpoint-0f447d4f645154f6.txt'), ('LANG', 'en_US.UTF-8'), ('RMW_IMPLEMENTATION', 'rmw_cyclonedds_cpp'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/0/vscode-git-e7e8394b8f.sock'), ('TERM_PROGRAM', 'vscode'), ('AMENT_PREFIX_PATH', '/opt/ros/unitree_ros2/cyclonedds_ws/install/unitree_hg:/opt/ros/unitree_ros2/cyclonedds_ws/install/unitree_go:/opt/ros/unitree_ros2/cyclonedds_ws/install/unitree_api:/opt/ros/unitree_ros2/cyclonedds_ws/install/rmw_cyclonedds_cpp:/opt/ros/foxy'), ('SHELL', '/usr/bin/bash'), ('ANTHROPIC_AUTH_TOKEN', 'sk-bIrPf7D0AFcc66DlrzOF2T4Rop4TbuQC1WUry1mczY3Iz3YL'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('GIT_PAGER', 'cat'), ('PWD', '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/build/expression'), ('ENABLE_IDE_INTEGRATION', 'true'), ('LC_ALL', 'en_US.UTF-8'), ('SSH_CONNECTION', '************** 56501 ************** 22'), ('PYTHONPATH', '/opt/ros/unitree_ros2/cyclonedds_ws/install/unitree_hg/lib/python3.8/site-packages:/opt/ros/unitree_ros2/cyclonedds_ws/install/unitree_go/lib/python3.8/site-packages:/opt/ros/unitree_ros2/cyclonedds_ws/install/unitree_api/lib/python3.8/site-packages:/opt/ros/foxy/lib/python3.8/site-packages'), ('HTTP_PROXY', 'http://127.0.0.1:7890'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/opt/ros/unitree_ros2/cyclonedds_ws/install/unitree_hg:/opt/ros/unitree_ros2/cyclonedds_ws/install/unitree_go:/opt/ros/unitree_ros2/cyclonedds_ws/install/unitree_api:/opt/ros/unitree_ros2/cyclonedds_ws/install/rmw_cyclonedds_cpp:/opt/ros/unitree_ros2/cyclonedds_ws/install/cyclonedds:/opt/ros/foxy'), ('AMENT_CURRENT_PREFIX', '/opt/ros/foxy')]), 'shell': False}
[0.158607] (expression) StdoutLine: {'line': b'Not searching for unused variables given on the command line.\n'}
[0.197606] (-) TimerEvent: {}
[0.298081] (-) TimerEvent: {}
[0.398531] (-) TimerEvent: {}
[0.499258] (-) TimerEvent: {}
[0.555857] (expression) StdoutLine: {'line': b'-- The C compiler identification is GNU 9.4.0\n'}
[0.599439] (-) TimerEvent: {}
[0.700083] (-) TimerEvent: {}
[0.800930] (-) TimerEvent: {}
[0.901918] (-) TimerEvent: {}
[0.975370] (expression) StdoutLine: {'line': b'-- The CXX compiler identification is GNU 9.4.0\n'}
[1.002033] (-) TimerEvent: {}
[1.042397] (expression) StdoutLine: {'line': b'-- Check for working C compiler: /usr/bin/cc\n'}
[1.102325] (-) TimerEvent: {}
[1.204266] (-) TimerEvent: {}
[1.304919] (-) TimerEvent: {}
[1.405566] (-) TimerEvent: {}
[1.506111] (-) TimerEvent: {}
[1.606896] (-) TimerEvent: {}
[1.707664] (-) TimerEvent: {}
[1.808145] (-) TimerEvent: {}
[1.901363] (expression) StdoutLine: {'line': b'-- Check for working C compiler: /usr/bin/cc -- works\n'}
[1.908900] (-) TimerEvent: {}
[1.909447] (expression) StdoutLine: {'line': b'-- Detecting C compiler ABI info\n'}
[2.009064] (-) TimerEvent: {}
[2.109733] (-) TimerEvent: {}
[2.210535] (-) TimerEvent: {}
[2.311287] (-) TimerEvent: {}
[2.411922] (-) TimerEvent: {}
[2.512409] (-) TimerEvent: {}
[2.612832] (-) TimerEvent: {}
[2.713654] (-) TimerEvent: {}
[2.758615] (expression) StdoutLine: {'line': b'-- Detecting C compiler ABI info - done\n'}
[2.813745] (-) TimerEvent: {}
[2.817878] (expression) StdoutLine: {'line': b'-- Detecting C compile features\n'}
[2.820115] (expression) StdoutLine: {'line': b'-- Detecting C compile features - done\n'}
[2.876967] (expression) StdoutLine: {'line': b'-- Check for working CXX compiler: /usr/bin/c++\n'}
[2.913920] (-) TimerEvent: {}
[3.014543] (-) TimerEvent: {}
[3.115222] (-) TimerEvent: {}
[3.216125] (-) TimerEvent: {}
[3.317125] (-) TimerEvent: {}
[3.417931] (-) TimerEvent: {}
[3.520065] (-) TimerEvent: {}
[3.620755] (-) TimerEvent: {}
[3.721271] (-) TimerEvent: {}
[3.735195] (expression) StdoutLine: {'line': b'-- Check for working CXX compiler: /usr/bin/c++ -- works\n'}
[3.746073] (expression) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info\n'}
[3.821421] (-) TimerEvent: {}
[3.922007] (-) TimerEvent: {}
[4.022600] (-) TimerEvent: {}
[4.123325] (-) TimerEvent: {}
[4.223972] (-) TimerEvent: {}
[4.324391] (-) TimerEvent: {}
[4.424979] (-) TimerEvent: {}
[4.525847] (-) TimerEvent: {}
[4.543818] (expression) StdoutLine: {'line': b'-- Detecting CXX compiler ABI info - done\n'}
[4.626047] (-) TimerEvent: {}
[4.629691] (expression) StdoutLine: {'line': b'-- Detecting CXX compile features\n'}
[4.631559] (expression) StdoutLine: {'line': b'-- Detecting CXX compile features - done\n'}
[4.694636] (expression) StdoutLine: {'line': b'-- Found ament_cmake: 0.9.12 (/opt/ros/foxy/share/ament_cmake/cmake)\n'}
[4.726160] (-) TimerEvent: {}
[4.769655] (expression) StdoutLine: {'line': b'-- Found PythonInterp: /usr/bin/python3 (found suitable version "3.8.10", minimum required is "3") \n'}
[4.770463] (expression) StdoutLine: {'line': b'-- Using PYTHON_EXECUTABLE: /usr/bin/python3\n'}
[4.826356] (-) TimerEvent: {}
[4.927035] (-) TimerEvent: {}
[5.027655] (-) TimerEvent: {}
[5.128014] (-) TimerEvent: {}
[5.228520] (-) TimerEvent: {}
[5.314337] (expression) StdoutLine: {'line': b'-- Found ament_index_cpp: 1.1.0 (/opt/ros/foxy/share/ament_index_cpp/cmake)\n'}
[5.328644] (-) TimerEvent: {}
[5.329931] (expression) StdoutLine: {'line': b'-- Found rclcpp: 2.4.3 (/opt/ros/foxy/share/rclcpp/cmake)\n'}
[5.394896] (expression) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[5.405586] (expression) StdoutLine: {'line': b'-- Found rosidl_adapter: 1.3.1 (/opt/ros/foxy/share/rosidl_adapter/cmake)\n'}
[5.428778] (-) TimerEvent: {}
[5.529165] (-) TimerEvent: {}
[5.629671] (-) TimerEvent: {}
[5.661824] (expression) StdoutLine: {'line': b'-- Found OpenSSL: /usr/lib/aarch64-linux-gnu/libcrypto.so (found version "1.1.1f")  \n'}
[5.714941] (expression) StdoutLine: {'line': b'-- Found FastRTPS: /opt/ros/foxy/include  \n'}
[5.729841] (-) TimerEvent: {}
[5.830517] (-) TimerEvent: {}
[5.841201] (expression) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[5.922979] (expression) StdoutLine: {'line': b'-- Found rmw_implementation_cmake: 1.0.4 (/opt/ros/foxy/share/rmw_implementation_cmake/cmake)\n'}
[5.925157] (expression) StdoutLine: {'line': b"-- Using RMW implementation 'rmw_cyclonedds_cpp' as default\n"}
[5.930636] (-) TimerEvent: {}
[5.966554] (expression) StdoutLine: {'line': b'-- Looking for pthread.h\n'}
[6.030774] (-) TimerEvent: {}
[6.131188] (-) TimerEvent: {}
[6.231846] (-) TimerEvent: {}
[6.332797] (-) TimerEvent: {}
[6.433497] (-) TimerEvent: {}
[6.534175] (-) TimerEvent: {}
[6.634546] (-) TimerEvent: {}
[6.734922] (-) TimerEvent: {}
[6.737603] (expression) StdoutLine: {'line': b'-- Looking for pthread.h - found\n'}
[6.756464] (expression) StdoutLine: {'line': b'-- Performing Test CMAKE_HAVE_LIBC_PTHREAD\n'}
[6.835124] (-) TimerEvent: {}
[6.936023] (-) TimerEvent: {}
[7.036685] (-) TimerEvent: {}
[7.137275] (-) TimerEvent: {}
[7.237840] (-) TimerEvent: {}
[7.338264] (-) TimerEvent: {}
[7.438758] (-) TimerEvent: {}
[7.502805] (expression) StdoutLine: {'line': b'-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Failed\n'}
[7.513056] (expression) StdoutLine: {'line': b'-- Looking for pthread_create in pthreads\n'}
[7.538983] (-) TimerEvent: {}
[7.639582] (-) TimerEvent: {}
[7.739940] (-) TimerEvent: {}
[7.840365] (-) TimerEvent: {}
[7.941210] (-) TimerEvent: {}
[8.041582] (-) TimerEvent: {}
[8.142005] (-) TimerEvent: {}
[8.242948] (-) TimerEvent: {}
[8.253184] (expression) StdoutLine: {'line': b'-- Looking for pthread_create in pthreads - not found\n'}
[8.261491] (expression) StdoutLine: {'line': b'-- Looking for pthread_create in pthread\n'}
[8.344358] (-) TimerEvent: {}
[8.445109] (-) TimerEvent: {}
[8.545883] (-) TimerEvent: {}
[8.646591] (-) TimerEvent: {}
[8.747181] (-) TimerEvent: {}
[8.847901] (-) TimerEvent: {}
[8.948406] (-) TimerEvent: {}
[9.009597] (expression) StdoutLine: {'line': b'-- Looking for pthread_create in pthread - found\n'}
[9.021654] (expression) StdoutLine: {'line': b'-- Found Threads: TRUE  \n'}
[9.048655] (-) TimerEvent: {}
[9.149596] (-) TimerEvent: {}
[9.250052] (-) TimerEvent: {}
[9.306591] (expression) StdoutLine: {'line': b'-- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.71.0/BoostConfig.cmake (found version "1.71.0") found components: filesystem \n'}
[9.313738] (expression) StdoutLine: {'line': b'-- Found ament_lint_auto: 0.9.8 (/opt/ros/foxy/share/ament_lint_auto/cmake)\n'}
[9.350177] (-) TimerEvent: {}
[9.450532] (-) TimerEvent: {}
[9.550926] (-) TimerEvent: {}
[9.651380] (-) TimerEvent: {}
[9.752314] (-) TimerEvent: {}
[9.833719] (expression) StdoutLine: {'line': b"-- Added test 'copyright' to check source files copyright and LICENSE\n"}
[9.852596] (-) TimerEvent: {}
[9.953573] (-) TimerEvent: {}
[9.955718] (expression) StdoutLine: {'line': b"-- Added test 'cppcheck' to perform static code analysis on C / C++ code\n"}
[9.958028] (expression) StdoutLine: {'line': b'-- Configured cppcheck include dirs: /mine/worktrees/unitree-debug/xiaoli_application_ros2/src/expression/include\n'}
[9.958492] (expression) StdoutLine: {'line': b'-- Configured cppcheck exclude dirs and/or files: \n'}
[10.053807] (-) TimerEvent: {}
[10.066202] (expression) StdoutLine: {'line': b"-- Added test 'cpplint' to check C / C++ code against the Google style\n"}
[10.066834] (expression) StdoutLine: {'line': b'-- Configured cpplint exclude dirs and/or files: \n'}
[10.128477] (expression) StdoutLine: {'line': b"-- Added test 'lint_cmake' to check CMake code style\n"}
[10.154010] (-) TimerEvent: {}
[10.254755] (-) TimerEvent: {}
[10.260935] (expression) StdoutLine: {'line': b"-- Added test 'uncrustify' to check C / C++ code style\n"}
[10.261206] (expression) StdoutLine: {'line': b'-- Configured uncrustify additional arguments: \n'}
[10.290597] (expression) StdoutLine: {'line': b"-- Added test 'xmllint' to check XML markup files\n"}
[10.355014] (-) TimerEvent: {}
[10.455903] (-) TimerEvent: {}
[10.471731] (expression) StdoutLine: {'line': b'-- Configuring done\n'}
[10.556045] (-) TimerEvent: {}
[10.656632] (-) TimerEvent: {}
[10.757118] (-) TimerEvent: {}
[10.858890] (-) TimerEvent: {}
[10.959378] (-) TimerEvent: {}
[11.022389] (expression) StdoutLine: {'line': b'-- Generating done\n'}
[11.059480] (-) TimerEvent: {}
[11.159916] (-) TimerEvent: {}
[11.260711] (-) TimerEvent: {}
[11.291827] (expression) StdoutLine: {'line': b'-- Build files have been written to: /mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/build/expression\n'}
[11.310412] (expression) CommandEnded: {'returncode': 0}
[11.319119] (expression) JobProgress: {'identifier': 'expression', 'progress': 'build'}
[11.319913] (expression) Command: {'cmd': ['/usr/bin/cmake', '--build', '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/build/expression', '--', '-j8', '-l8'], 'cwd': '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/build/expression', 'env': OrderedDict([('LESSOPEN', '| /usr/bin/lesspipe %s'), ('HTTPS_PROXY', 'http://127.0.0.1:7890'), ('no_proxy', 'localhost,127.0.0.1,::1'), ('USER', 'root'), ('SSH_CLIENT', '************** 56501 22'), ('all_proxy', 'socks5h://127.0.0.1:7890'), ('XDG_SESSION_TYPE', 'tty'), ('GIT_ASKPASS', '/root/.cursor-server/cli/servers/Stable-68b8fe7396ea37d8acdaaaa08ba316ba359a4160/server/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/opt/ros/unitree_ros2/cyclonedds_ws/install/unitree_hg/lib:/opt/ros/unitree_ros2/cyclonedds_ws/install/unitree_go/lib:/opt/ros/unitree_ros2/cyclonedds_ws/install/unitree_api/lib:/opt/ros/unitree_ros2/cyclonedds_ws/install/rmw_cyclonedds_cpp/lib:/opt/ros/unitree_ros2/cyclonedds_ws/install/cyclonedds/lib:/opt/ros/foxy/opt/yaml_cpp_vendor/lib:/opt/ros/foxy/opt/rviz_ogre_vendor/lib:/opt/ros/foxy/lib/aarch64-linux-gnu:/opt/ros/foxy/lib'), ('BROWSER', '/root/.cursor-server/cli/servers/Stable-68b8fe7396ea37d8acdaaaa08ba316ba359a4160/server/bin/helpers/browser.sh'), ('LESS', '-FX'), ('MOTD_SHOWN', 'pam'), ('HOME', '/root'), ('CYCLONEDDS_URI', '<CycloneDDS><Domain><General><Interfaces>\n                            <NetworkInterface name="eth0" priority="default" multicast="default" />\n                        </Interfaces><AllowMulticast>spdp</AllowMulticast></General></Domain></CycloneDDS>'), ('OLDPWD', '/mine/worktrees'), ('TERM_PROGRAM_VERSION', '1.3.6'), ('NO_PROXY', 'localhost,127.0.0.1,::1'), ('VSCODE_IPC_HOOK_CLI', '/run/user/0/vscode-ipc-e8e913eb-41ab-4f24-a16b-d193027fa16b.sock'), ('ROS_PYTHON_VERSION', '3'), ('PAGER', 'cat'), ('VSCODE_GIT_ASKPASS_MAIN', '/root/.cursor-server/cli/servers/Stable-68b8fe7396ea37d8acdaaaa08ba316ba359a4160/server/extensions/git/dist/askpass-main.js'), ('VSCODE_GIT_ASKPASS_NODE', '/root/.cursor-server/cli/servers/Stable-68b8fe7396ea37d8acdaaaa08ba316ba359a4160/server/node'), ('SSL_CERT_FILE', '/usr/lib/ssl/certs/ca-certificates.crt'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('BUNDLED_DEBUGPY_PATH', '/root/.cursor-server/extensions/ms-python.debugpy-2025.6.0-linux-arm64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/0/bus'), ('COLORTERM', 'truecolor'), ('https_proxy', 'http://127.0.0.1:7890'), ('COLCON_PREFIX_PATH', '/opt/ros/unitree_ros2/cyclonedds_ws/install'), ('LOGNAME', 'root'), ('ALL_PROXY', 'socks5h://127.0.0.1:7890'), ('http_proxy', 'http://127.0.0.1:7890'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/opt/ros/unitree_ros2/cyclonedds_ws/install/cyclonedds/lib/aarch64-linux-gnu/pkgconfig:/opt/ros/unitree_ros2/cyclonedds_ws/install/cyclonedds/lib/pkgconfig'), ('CLAUDE_CODE_SSE_PORT', '22023'), ('XDG_SESSION_CLASS', 'user'), ('ANTHROPIC_BASE_URL', 'https://anyrouter.top'), ('TERM', 'xterm-256color'), ('XDG_SESSION_ID', '2'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/opt/ros/unitree_ros2/cyclonedds_ws/install/cyclonedds/bin:/opt/ros/foxy/bin:/root/.cursor-server/cli/servers/Stable-68b8fe7396ea37d8acdaaaa08ba316ba359a4160/server/bin/remote-cli:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/root/.cursor-server/extensions/ms-python.debugpy-2025.6.0-linux-arm64/bundled/scripts/noConfigScripts'), ('XDG_RUNTIME_DIR', '/run/user/0'), ('SSL_CERT_DIR', '/usr/lib/ssl/certs'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/root/.cursor-server/extensions/ms-python.debugpy-2025.6.0-linux-arm64/.noConfigDebugAdapterEndpoints/endpoint-0f447d4f645154f6.txt'), ('LANG', 'en_US.UTF-8'), ('RMW_IMPLEMENTATION', 'rmw_cyclonedds_cpp'), ('LS_COLORS', 'rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/0/vscode-git-e7e8394b8f.sock'), ('TERM_PROGRAM', 'vscode'), ('AMENT_PREFIX_PATH', '/opt/ros/unitree_ros2/cyclonedds_ws/install/unitree_hg:/opt/ros/unitree_ros2/cyclonedds_ws/install/unitree_go:/opt/ros/unitree_ros2/cyclonedds_ws/install/unitree_api:/opt/ros/unitree_ros2/cyclonedds_ws/install/rmw_cyclonedds_cpp:/opt/ros/foxy'), ('SHELL', '/usr/bin/bash'), ('ANTHROPIC_AUTH_TOKEN', 'sk-bIrPf7D0AFcc66DlrzOF2T4Rop4TbuQC1WUry1mczY3Iz3YL'), ('LESSCLOSE', '/usr/bin/lesspipe %s %s'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('GIT_PAGER', 'cat'), ('PWD', '/mine/worktrees/unitree-debug/xiaoli_application_ros2/build_1.0/debug/build/expression'), ('ENABLE_IDE_INTEGRATION', 'true'), ('LC_ALL', 'en_US.UTF-8'), ('SSH_CONNECTION', '************** 56501 ************** 22'), ('PYTHONPATH', '/opt/ros/unitree_ros2/cyclonedds_ws/install/unitree_hg/lib/python3.8/site-packages:/opt/ros/unitree_ros2/cyclonedds_ws/install/unitree_go/lib/python3.8/site-packages:/opt/ros/unitree_ros2/cyclonedds_ws/install/unitree_api/lib/python3.8/site-packages:/opt/ros/foxy/lib/python3.8/site-packages'), ('HTTP_PROXY', 'http://127.0.0.1:7890'), ('COLCON', '1'), ('CMAKE_PREFIX_PATH', '/opt/ros/unitree_ros2/cyclonedds_ws/install/unitree_hg:/opt/ros/unitree_ros2/cyclonedds_ws/install/unitree_go:/opt/ros/unitree_ros2/cyclonedds_ws/install/unitree_api:/opt/ros/unitree_ros2/cyclonedds_ws/install/rmw_cyclonedds_cpp:/opt/ros/unitree_ros2/cyclonedds_ws/install/cyclonedds:/opt/ros/foxy'), ('AMENT_CURRENT_PREFIX', '/opt/ros/foxy')]), 'shell': False}
[11.360941] (-) TimerEvent: {}
[11.461937] (-) TimerEvent: {}
[11.562731] (-) TimerEvent: {}
[11.622367] (expression) StdoutLine: {'line': b'\x1b[35m\x1b[1mScanning dependencies of target expression_node\x1b[0m\n'}
[11.662962] (-) TimerEvent: {}
[11.763946] (-) TimerEvent: {}
[11.837580] (expression) StdoutLine: {'line': b'[ 50%] \x1b[32mBuilding CXX object CMakeFiles/expression_node.dir/src/expression_node.cpp.o\x1b[0m\n'}
[11.864211] (-) TimerEvent: {}
[11.917972] (expression) StderrLine: {'line': b'make[2]: *** [CMakeFiles/expression_node.dir/build.make:63: CMakeFiles/expression_node.dir/src/expression_node.cpp.o] Interrupt\n'}
[11.918726] (expression) StderrLine: {'line': b'make[1]: *** [CMakeFiles/Makefile2:78: CMakeFiles/expression_node.dir/all] Interrupt\n'}
[11.919438] (expression) StderrLine: {'line': b'make: *** [Makefile:141: all] Interrupt\n'}
[11.923528] (expression) CommandEnded: {'returncode': -2}
[11.964337] (-) TimerEvent: {}
[12.064908] (-) TimerEvent: {}
[12.072983] (expression) JobEnded: {'identifier': 'expression', 'rc': 'SIGINT'}
[12.092048] (follow_me_bringup) JobSkipped: {'identifier': 'follow_me_bringup'}
[12.092278] (follow_msgs) JobSkipped: {'identifier': 'follow_msgs'}
[12.092359] (follow_strategy) JobSkipped: {'identifier': 'follow_strategy'}
[12.092421] (homi_speech_interface) JobSkipped: {'identifier': 'homi_speech_interface'}
[12.092479] (launch_package) JobSkipped: {'identifier': 'launch_package'}
[12.092535] (map_server) JobSkipped: {'identifier': 'map_server'}
[12.094909] (nvidia_control) JobSkipped: {'identifier': 'nvidia_control'}
[12.094990] (pixel2world_pose) JobSkipped: {'identifier': 'pixel2world_pose'}
[12.095048] (point_process) JobSkipped: {'identifier': 'point_process'}
[12.095105] (pwm_touch) JobSkipped: {'identifier': 'pwm_touch'}
[12.095160] (rcutils) JobSkipped: {'identifier': 'rcutils'}
[12.095216] (uploadlog) JobSkipped: {'identifier': 'uploadlog'}
[12.095270] (uwb_package) JobSkipped: {'identifier': 'uwb_package'}
[12.095325] (a2dp) JobSkipped: {'identifier': 'a2dp'}
[12.095380] (andlink) JobSkipped: {'identifier': 'andlink'}
[12.095435] (audio_player) JobSkipped: {'identifier': 'audio_player'}
[12.095508] (audio_recorder) JobSkipped: {'identifier': 'audio_recorder'}
[12.095566] (ble) JobSkipped: {'identifier': 'ble'}
[12.095621] (cmcc_rtc) JobSkipped: {'identifier': 'cmcc_rtc'}
[12.095675] (follow_rcs) JobSkipped: {'identifier': 'follow_rcs'}
[12.095729] (homi_audio_player) JobSkipped: {'identifier': 'homi_audio_player'}
[12.095784] (homi_speech) JobSkipped: {'identifier': 'homi_speech'}
[12.095838] (live_stream) JobSkipped: {'identifier': 'live_stream'}
[12.095893] (network) JobSkipped: {'identifier': 'network'}
[12.095947] (peripherals) JobSkipped: {'identifier': 'peripherals'}
[12.096000] (robdog_control) JobSkipped: {'identifier': 'robdog_control'}
[12.096054] (ultrasonic) JobSkipped: {'identifier': 'ultrasonic'}
[12.096108] (video_gst) JobSkipped: {'identifier': 'video_gst'}
[12.096162] (video_gst_nv) JobSkipped: {'identifier': 'video_gst_nv'}
[12.096217] (-) EventReactorShutdown: {}
