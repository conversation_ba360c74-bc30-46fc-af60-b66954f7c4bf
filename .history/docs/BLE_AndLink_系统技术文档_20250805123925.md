# BLE和AndLink系统技术文档

## 1. 系统概述

### 1.1 系统简介
本系统基于ROS2框架，实现了机器人设备的蓝牙低功耗(BLE)配网和AndLink云平台连接功能。主要用于机器人设备的初始化配置、网络连接和远程管理。

### 1.2 核心功能
- **BLE配网**：通过蓝牙低功耗技术实现设备WiFi配置
- **AndLink集成**：与移动云平台进行设备注册和管理
- **异常处理**：完善的异常监控和恢复机制
- **ROS2通信**：基于ROS2的模块间通信

## 2. 系统架构

### 2.1 整体架构
```
移动端APP ←→ BLE模块 ←→ AndLink模块 ←→ 移动云平台
                ↓           ↓
            ROS2通信 ←→ 网络模块
```

### 2.2 核心模块

#### 2.2.1 BLE模块 (src/ble/)
- **ble_node.py**: BLE节点主程序，负责ROS2通信和整体协调
- **ble_manager.py**: BLE连接管理，处理蓝牙广播、连接和断开
- **ble_data_process.py**: BLE数据处理，解析和处理来自APP的数据
- **ble_exception_handler.py**: BLE异常处理器
- **exception_manager.py**: 通用异常管理器

#### 2.2.2 AndLink模块 (src/andlink/)
- **andlink_node.cpp**: AndLink节点主程序
- **andlink_adapt.c**: AndLink SDK适配层
- **AndLink SDK**: 移动云平台SDK

#### 2.2.3 网络模块 (src/network/)
- **network_node**: 网络连接管理
- **WiFi服务**: 提供WiFi连接服务

## 3. 技术方案

### 3.1 BLE配网方案

#### 3.1.1 技术选型
- **协议**: 蓝牙低功耗(BLE) 4.0+
- **框架**: Python + D-Bus + BlueZ
- **通信**: GATT协议

#### 3.1.2 实现特点
- 自定义GATT服务和特征值
- 支持设备广播和发现
- 异常监控和自动恢复
- 多线程处理保证响应性

#### 3.1.3 配网流程
1. 系统启动，初始化BLE管理器
2. 开启蓝牙广播，等待APP连接
3. APP扫描并连接BLE设备
4. APP发送WiFi配置信息
5. 设备连接WiFi网络
6. 向AndLink平台注册
7. 完成用户绑定
8. 停止BLE广播

### 3.2 AndLink集成方案

#### 3.2.1 技术选型
- **语言**: C/C++
- **SDK**: AndLink官方SDK
- **平台**: 移动云平台

#### 3.2.2 核心功能
- 设备注册和认证
- 用户绑定管理
- 远程控制接口
- 设备状态上报

#### 3.2.3 配置信息
```c
#define DEVICE_TYPE        "2320647"           // 产品类型ID
#define PRODUCTION_TOKEN   "PXclNa5N6PmeVzZV" // 产品验证令牌
#define ANDLINK_TOKEN      "reF7fz1mT585oHY5" // 平台验证令牌
#define DEVICE_VENDOR_NAME "test"             // 厂商名称
```

### 3.3 ROS2通信方案

#### 3.3.1 Topic通信
- **ble_byte_stream**: BLE原始数据流
- **andlink_cmd**: AndLink命令
- **andlink_network**: 网络状态
- **sigc_event_topic_APP**: APP事件

#### 3.3.2 Service通信
- **/homi_speech/network_service**: 网络连接服务

## 4. 关键流程

### 4.1 设备初始化流程
1. 读取设备配置信息
2. 初始化BLE管理器
3. 启动AndLink SDK
4. 检查绑定状态
5. 开始相应的广播模式

### 4.2 WiFi配网流程
1. APP通过BLE发送WiFi信息
2. BLE模块解析配置数据
3. 调用网络服务连接WiFi
4. 返回连接结果给APP
5. 更新设备网络状态

### 4.3 用户绑定流程
1. 设备向AndLink平台注册
2. 获取设备Token
3. APP发送绑定请求
4. 执行用户绑定操作
5. 更新绑定状态

## 5. 异常处理机制

### 5.1 异常类型
- **BLE_SEND_ERROR**: BLE发送错误
- **BLE_RECEIVE_ERROR**: BLE接收错误
- **BLE_CONNECTION_ERROR**: BLE连接错误
- **BLE_ADVERTISEMENT_ERROR**: BLE广播错误
- **WIFI_CONNECTION_ERROR**: WiFi连接错误

### 5.2 处理策略
- **监控机制**: 定期健康检查
- **重试机制**: 自动重连和重试
- **恢复策略**: 分级恢复处理
- **日志记录**: 详细的异常日志

### 5.3 恢复流程
1. 检测异常情况
2. 记录异常信息
3. 执行恢复策略
4. 验证恢复结果
5. 更新系统状态

## 6. 配置文件

### 6.1 设备信息配置 (robot_info.yaml)
```yaml
robot_info:
  dev_x86:
    sn: "1830004229212345670000024"
    mac: "08:00:27:d4:fa:4A"
    cmei: "1830004229212345670000024"
```

### 6.2 网络配置 (handle_network.yaml)
```yaml
handlernetwork_node: 
  ros__parameters: 
    config_file_path: /usr/bin/cmcc_robot/install/env/service/andlink/board_resources/network.conf
    handle_internet_server_name: "/homi_speech/network_service"
```

## 7. 启动配置

### 7.1 Launch文件
- **andlink.py**: 启动AndLink和BLE节点
- **ap.py**: AP模式启动配置
- **network.py**: 网络节点启动

### 7.2 节点依赖关系
```
andlink_node ← ble_node ← network_node
     ↓            ↓           ↓
  AndLink SDK  BLE管理    WiFi服务
```

## 8. 开发和调试

### 8.1 日志系统
- 使用ROS2标准日志系统
- 分级日志记录(INFO/WARN/ERROR)
- 异常详细追踪

### 8.2 调试工具
- ROS2 topic监控
- BLE连接状态检查
- AndLink平台状态查询

### 8.3 常见问题
1. **BLE连接失败**: 检查蓝牙服务状态
2. **WiFi配网失败**: 检查网络服务配置
3. **AndLink注册失败**: 检查设备证书和网络连接

## 9. 性能优化

### 9.1 BLE优化
- 合理的广播间隔设置
- 连接参数优化
- 数据传输效率提升

### 9.2 内存管理
- 及时释放资源
- 避免内存泄漏
- 合理的缓存策略

### 9.3 网络优化
- 连接超时设置
- 重试机制优化
- 并发连接控制

## 10. 安全考虑

### 10.1 数据安全
- BLE通信加密
- 敏感信息保护
- 安全的密钥管理

### 10.2 设备安全
- 设备认证机制
- 防止未授权访问
- 安全的固件更新

---

*文档版本: v1.0*  
*最后更新: 2025-01-08*
