#ifndef __UT_ROBOT_GO2_CALIBRATION_CLIENT_HPP__
#define __UT_ROBOT_GO2_CALIBRATION_CLIENT_HPP__

#include <unitree/robot/client/client.hpp>
#include "calibration_api.hpp"
// #include "calibration_client.hpp"

namespace unitree {

namespace robot {

namespace go2 {

class CalibrationClient : public Client {
  public:
    CalibrationClient() :
        Client(ROBOT_CALIBRATION_SERVICE_NAME, false) {}

    ~CalibrationClient() {}

    void Init() {
        /*set api version*/
        SetApiVersion(ROBOT_CALIBRATION_API_VERSION);

        /*register Api*/
        UT_ROBOT_CLIENT_REG_API_NO_PROI(BASIC_DEMA_START_IMUDEMA_IN_ZERO_TORQUE);
        UT_ROBOT_CLIENT_REG_API_NO_PROI(BASIC_DEMA_START_IMUDEMA);
        UT_ROBOT_CLIENT_REG_API_NO_PROI(BASIC_DEMA_START_JOINTDEMA_IN_ZERO_TORQUE);
        UT_ROBOT_CLIENT_REG_API_NO_PROI(BASIC_DEMA_START_JOINTDEMA_ALL_JOINT_0);
        UT_ROBOT_CLIENT_REG_API_NO_PROI(BASIC_DEMA_START_JOINTDEMA_FRONT_JOINT_12);
        UT_ROBOT_CLIENT_REG_API_NO_PROI(BASIC_DEMA_START_JOINTDEMA_REAR_JOINT_12);
        UT_ROBOT_CLIENT_REG_API_NO_PROI(BASIC_DEMA_START_JOINTDEMA_SAVE_DATA);
        /*Other initialized*/
    }

    /*API ID 1001*/
    int32_t DoBasicDemaStartImuDemaInZeroTorque() {
        std::string parameter, data;
        int32_t ret = Call(BASIC_DEMA_START_IMUDEMA_IN_ZERO_TORQUE, parameter, data);

        return ret;
    }

    /*API ID 1002*/
    int32_t DoBasicDemaStartImuDema() {
        std::string parameter, data;
        int32_t ret = Call(BASIC_DEMA_START_IMUDEMA, parameter, data);

        return ret;
    }

    /*API ID 1003*/
    int32_t DoBasicDemaStartJointDemaInZeroTorque() {
        std::string parameter, data;
        int32_t ret = Call(BASIC_DEMA_START_JOINTDEMA_IN_ZERO_TORQUE, parameter, data);

        return ret;
    }

    /*API ID 1004*/
    int32_t DoBasicDemaStartJointDemaAllJoint0() {
        std::string parameter, data;
        int32_t ret = Call(BASIC_DEMA_START_JOINTDEMA_ALL_JOINT_0, parameter, data);

        return ret;
    }

    /*API ID 1005*/
    int32_t DoBasicDemaStartJointDemaFrontJoint12() {
        std::string parameter, data;
        int32_t ret = Call(BASIC_DEMA_START_JOINTDEMA_FRONT_JOINT_12, parameter, data);

        return ret;
    }

    /*API ID 1006*/
    int32_t DoBasicDemaStartJointDemaRearJoint12() {
        std::string parameter, data;
        int32_t ret = Call(BASIC_DEMA_START_JOINTDEMA_REAR_JOINT_12, parameter, data);

        return ret;
    }

    /*API ID 1007*/
    int32_t DoBasicDemaStartJointDemaSaveData() {
        std::string parameter, data;
        int32_t ret = Call(BASIC_DEMA_START_JOINTDEMA_SAVE_DATA, parameter, data);

        return ret;
    }

};

} // namespace go2
} // namespace robot
} // namespace unitree

#endif//__UT_ROBOT_GO2_CALIBRATION_CLIENT_HPP__
