#ifndef __UT_ROBOT_GO2_CALIBRATION_API_HPP__
#define __UT_ROBOT_GO2_CALIBRATION_API_HPP__

#include <unitree/common/json/jsonize.hpp>

namespace unitree
{
namespace robot
{
namespace go2
{
/*service name*/
const std::string ROBOT_CALIBRATION_SERVICE_NAME = "calibration";

/*api version*/
const std::string ROBOT_CALIBRATION_API_VERSION = "1.0.0.1";

/*
 * api id
 */
const int32_t BASIC_DEMA_START_IMUDEMA_IN_ZERO_TORQUE       = 1001;
const int32_t BASIC_DEMA_START_IMUDEMA                      = 1002;
const int32_t BASIC_DEMA_START_JOINTDEMA_IN_ZERO_TORQUE     = 1003;
const int32_t BASIC_DEMA_START_JOINTDEMA_ALL_JOINT_0        = 1004;
const int32_t BASIC_DEMA_START_JOINTDEMA_FRONT_JOINT_12     = 1005;
const int32_t BASIC_DEMA_START_JOINTDEMA_REAR_JOINT_12      = 1006;
const int32_t BASIC_DEMA_START_JOINTDEMA_SAVE_DATA          = 1007;

}
}
}

#endif //__UT_ROBOT_GO2_CALIBRATION_API_HPP__
